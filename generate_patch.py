import json
import os
import subprocess
from typing import List, Set
from datetime import datetime

def print_files(files: List[str]):
    """Print changed files in a clean format."""
    if files:
        print("\nDetected files changed:")
        for file in files:
            print(file)
        print()

def get_latest_tag() -> str:
    """Get the latest tag from git."""
    try:
        command = "git ls-remote --tags origin | grep -v '{}$' | sort -t '/' -k 3 -V | tail -n 1"
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )

        if not result.stdout.strip():
            raise Exception("No valid tags found in remote repository")

        latest_tag = result.stdout.strip().split('/')[-1]
        return latest_tag
    except Exception as e:
        raise Exception(f"Failed to get latest tag: {str(e)}")

def get_changed_sql_files(base_branch: str = "origin/master") -> Set[str]:
    """Get all SQL files changed since last tag, including those in master."""
    latest_tag = get_latest_tag()

    try:
        # Make sure we have the latest changes
        run_git_command("git fetch origin")

        # Get changes in current branch since last tag
        feature_cmd = f"git diff --name-only {latest_tag}"
        feature_changes = set(run_git_command(feature_cmd).split('\n'))

        # Get changes in master since last tag
        main_cmd = f"git diff --name-only {latest_tag} {base_branch}"
        main_changes = set(run_git_command(main_cmd).split('\n'))

        # Debug output
        print(f"\nLatest tag: {latest_tag}")
        print("\nFeature branch changes:")
        print('\n'.join(sorted(feature_changes)))
        print(f"\n{base_branch} changes:")
        print('\n'.join(sorted(main_changes)))

        # Combine both sets of changes
        all_changes = feature_changes.union(main_changes)

        # Filter for SQL files in database/schema
        sql_changes = {
            file for file in all_changes
            if file and file.startswith('database/schema/') and file.endswith('.sql')
        }

        print("\nAll SQL changes detected:")
        print('\n'.join(sorted(sql_changes)))

        return sql_changes
    except subprocess.CalledProcessError as e:
        raise

def get_latest_patch_version() -> float:
    """Get the latest patch version from git tags."""
    try:
        # Run the command in parts using a shell to handle the pipeline
        command = "git ls-remote --tags origin | grep -v '{}$' | sort -t '/' -k 3 -V | tail -n 1"
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )

        if not result.stdout.strip():
            raise Exception("No valid tags found in remote repository")

        # Extract version number from the output
        latest_tag = result.stdout.strip().split('/')[-1]
        # Remove 'v' prefix and convert to float
        latest_version = float(latest_tag.lstrip('v'))
        return latest_version
    except subprocess.CalledProcessError as e:
        raise Exception(f"Git command failed: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to get latest tag version: {str(e)}")

def run_git_command(command: str) -> str:
    """Run git command and return output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Git command failed: {command}")
        print(f"Error: {e.stderr}")
        raise

def merge_scripts(base_scripts, feature_scripts):
    """Merge scripts from both branches, maintaining order and uniqueness."""
    # Create a dictionary of scripts by id
    merged = {}

    # Add all scripts from both sources
    for script in base_scripts + feature_scripts:
        script_id = script["id"]
        if script_id not in merged or script_id == "recompile_packages":
            merged[script_id] = script

    # Sort scripts by type category
    types_scripts = []
    table_changes_scripts = []
    packages_scripts = []
    other_scripts = []

    for script_id, script in merged.items():
        if script_id == "recompile_packages":
            continue
        elif "source" in script and "/types/" in script["source"]:
            types_scripts.append(script)
        elif "source" in script and "/table_changes/" in script["source"]:
            table_changes_scripts.append(script)
        elif "source" in script and "/packages/" in script["source"]:
            packages_scripts.append(script)
        else:
            other_scripts.append(script)

    # Sort each category by id
    types_scripts.sort(key=lambda x: x["id"])
    table_changes_scripts.sort(key=lambda x: x["id"])
    packages_scripts.sort(key=lambda x: x["id"])
    other_scripts.sort(key=lambda x: x["id"])

    # Combine all scripts in the CORRECT required order:
    # 1. Types, 2. Table changes, 3. Packages, 4. Others
    regular_scripts = types_scripts + table_changes_scripts + packages_scripts + other_scripts

    # Reassign rollbackIndex values
    for idx, script in enumerate(regular_scripts, start=1):
        script["rollbackIndex"] = idx

    # Add recompile_packages at the end if it exists
    if "recompile_packages" in merged:
        recompile = merged["recompile_packages"]
        recompile["rollbackIndex"] = 9999
        regular_scripts.append(recompile)

    return regular_scripts

def update_patch_file():
    """Update patch-latest.json with merged changes from both branches."""
    patch_file_path = "database/patches/patch-latest.json"

    try:
        # Get all changed SQL files
        changed_files = get_changed_sql_files()

        # Get our local version for RFC and modifier info
        with open(patch_file_path, "r") as f:
            current_data = json.load(f)

        # Separate scripts into different categories
        types_scripts = []
        table_changes_scripts = []
        packages_scripts = {}  # Dictionary to group packages by base name
        other_scripts = []

        for script in current_data.get("scripts", []):
            if script["id"] == "recompile_packages":
                continue

            # Categorize by path
            if "source" in script and "/types/" in script["source"]:
                types_scripts.append(script)
            elif "source" in script and "/table_changes/" in script["source"]:
                table_changes_scripts.append(script)
            elif "source" in script and "/packages/" in script["source"]:
                # Extract base name (without .pks.sql or .pkb.sql)
                base_name = script["id"].split('.')[0]
                if base_name not in packages_scripts:
                    packages_scripts[base_name] = {"pks": None, "pkb": None}
                
                if script["id"].endswith(".pks.sql"):
                    packages_scripts[base_name]["pks"] = script
                else:
                    packages_scripts[base_name]["pkb"] = script
            else:
                other_scripts.append(script)

        # Add any new changed files to appropriate category
        for file_path in sorted(changed_files):
            filename = os.path.basename(file_path)

            # Skip if this file is already in any list
            all_scripts_ids = [s["id"] for s in types_scripts + table_changes_scripts + other_scripts]
            for pkg in packages_scripts.values():
                if pkg["pks"] and pkg["pks"]["id"] == filename:
                    all_scripts_ids.append(filename)
                if pkg["pkb"] and pkg["pkb"]["id"] == filename:
                    all_scripts_ids.append(filename)
            
            if filename in all_scripts_ids:
                continue

            source_path = file_path.replace('database/', '', 1)

            # Check if this is a new file by checking master branch
            is_new_file = True
            try:
                # Check if file exists in master branch
                cmd = f"git ls-tree origin/master {file_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                is_new_file = not bool(result.stdout.strip())
            except:
                # If git command fails, assume it's a new file
                is_new_file = True

            script = {
                "id": filename,
                "source": source_path,
                "runAs": "bi_ods",
            }

            # Only add rollbackRepoTag and remoteFileLocation for existing packages/types
            if any(ext in filename for ext in ['.pkb', '.pks', '.typ', 'sql']) and not is_new_file:
                script.update({
                    "rollbackRepoTag": f"v{get_latest_patch_version():.1f}",
                    "remoteFileLocation": file_path
                })

            # Remove None values
            script = {k: v for k, v in script.items() if v is not None}

            # Add to appropriate category
            if "/types/" in source_path:
                types_scripts.append(script)
            elif "/table_changes/" in source_path:
                table_changes_scripts.append(script)
            elif "/packages/" in source_path:
                # Extract base name (without .pks.sql or .pkb.sql)
                base_name = filename.split('.')[0]
                if base_name not in packages_scripts:
                    packages_scripts[base_name] = {"pks": None, "pkb": None}
                
                if filename.endswith(".pks.sql"):
                    packages_scripts[base_name]["pks"] = script
                else:
                    packages_scripts[base_name]["pkb"] = script
            else:
                other_scripts.append(script)

        # Sort each category by id
        types_scripts.sort(key=lambda x: x["id"])
        table_changes_scripts.sort(key=lambda x: x["id"])
        # For packages, we'll sort by base name when we flatten the dictionary
        other_scripts.sort(key=lambda x: x["id"])

        # Combine scripts in correct order with updated rollbackIndexes
        all_scripts = []
        current_index = 1
        
        # 1. Types
        for script in types_scripts:
            script["rollbackIndex"] = current_index
            all_scripts.append(script)
            current_index += 1
            
        # 2. Table changes
        for script in table_changes_scripts:
            script["rollbackIndex"] = current_index
            all_scripts.append(script)
            current_index += 1
            
        # 3. Packages - grouped by name, with .pks before .pkb
        # Sort package names alphabetically
        for base_name in sorted(packages_scripts.keys()):
            pkg = packages_scripts[base_name]
            # First add .pks if it exists
            if pkg["pks"]:
                pkg["pks"]["rollbackIndex"] = current_index
                all_scripts.append(pkg["pks"])
                current_index += 1
            
            # Then add .pkb if it exists
            if pkg["pkb"]:
                pkg["pkb"]["rollbackIndex"] = current_index
                all_scripts.append(pkg["pkb"])
                current_index += 1
            
        # 4. Others
        for script in other_scripts:
            script["rollbackIndex"] = current_index
            all_scripts.append(script)
            current_index += 1

        # Add recompile_packages at the end
        recompile_script = {
            "id": "recompile_packages",
            "rollbackRepoTag": f"v{get_latest_patch_version():.1f}",
            "remoteFileLocation": "database/schema/bi_ods/verify/recompile_packages.sql",
            "source": "schema/bi_ods/verify/recompile_packages.sql",
            "runAs": "bi_ods",
            "rollbackIndex": 9999
        }
        all_scripts.append(recompile_script)

        # Create final patch data
        patch_data = {
            "rfcId": current_data.get("rfcId", ""),
            "patchId": f"{get_latest_patch_version() + 1.0:.1f}",
            "modificationDate": datetime.now().strftime("%d/%m/%Y"),
            "modifiedBy": current_data.get("modifiedBy", ""),
            "typeOfRelease": "ods",
            "unlockUserRequired": "false",
            "specifyRollbackOrder": "true",
            "scripts": all_scripts
        }

        # Write merged patch file
        with open(patch_file_path, "w") as f:
            json.dump(patch_data, f, indent=2)
            print("\nUpdated patch-latest.json successfully")

    except Exception as e:
        raise Exception(f"Failed to update patch file: {str(e)}")

def generate_patch(base_branch: str = "origin/master"):
    """Generate patch-latest.json file."""
    try:
        # Get changed files from both branches
        base_changes = get_changed_sql_files(base_branch)
        feature_changes = get_changed_sql_files("HEAD")
        all_changes = sorted(base_changes.union(feature_changes))

        if not all_changes:
            print("No SQL files changed")
            return

        print_files(all_changes)

        # Update the patch file using the new approach
        update_patch_file()

    except Exception as e:
        print(f"\nError: {str(e)}")
        raise

if __name__ == "__main__":
    print("\nLatest tag:", f"v{get_latest_patch_version():.1f}")

    # Show changes in both branches
    print("\nFeature branch changes:")
    try:
        feature_changes = run_git_command("git diff --name-only HEAD").split('\n')
        print('\n'.join(feature_changes))
    except:
        print("Failed to get feature branch changes")

    print("\nmaster changes:")
    try:
        main_changes = run_git_command("git diff --name-only origin/master").split('\n')
        print('\n'.join(main_changes))
    except:
        print("Failed to get main branch changes")

    print("\nAll SQL changes detected:")
    all_sql_changes = get_changed_sql_files()
    print('\n'.join(all_sql_changes))

    generate_patch()
