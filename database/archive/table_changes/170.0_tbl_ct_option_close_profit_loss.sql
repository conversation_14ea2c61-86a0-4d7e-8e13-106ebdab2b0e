CREATE TABLE bi_ods.ct_option_close_profit_loss (
    cash_transaction_seq             number,
    logical_load_timestamp           timestamp(6),
    created_by                       varchar2(50),
    create_timestamp                 timestamp(6),
    updated_by                       varchar2(50),
    update_timestamp                 timestamp(6),
    effective_start_timestamp        timestamp(6),
    business_date                    date,
    reporting_date                   date,
    opening_price_in_inst_ccy  number,
    closing_price_in_inst_ccy   number,
    total_amount_in_inst_ccy    number,
    fx_rate_bid                         number,
    fx_rate_ask                         number,
    settlement_price_in_inst_ccy  number,
    is_settlement                         varchar2(3)
)
    partition by range (reporting_date)
(partition P0 values less than (TO_DATE(' 01/12/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
partition P1 values less than (TO_DATE(' 01/01/2024 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
partition P2 values less than (TO_DATE(' 01/02/2024 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
partition P3 values less than (TO_DATE(' 01/03/2024 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
);
//

alter table ct_option_close_profit_loss set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));
//

alter table ct_option_close_profit_loss
    add constraint ct_option_close_profit_loss_pk primary key ( cash_transaction_seq )
    using index tablespace ODS_IND;
//

CREATE TABLE bi_ods.ct_option_close_profit_loss_h (
    cash_transaction_seq             number,
    logical_load_timestamp           timestamp(6),
    created_by                       varchar2(50),
    create_timestamp                 timestamp(6),
    updated_by                       varchar2(50),
    update_timestamp                 timestamp(6),
    effective_start_timestamp        timestamp(6),
    effective_end_timestamp     timestamp(6),
    action                      varchar2(1),
    action_timestamp            timestamp(6),
    business_date                    date,
    reporting_date                   date,
    opening_price_in_inst_ccy  number,
    closing_price_in_inst_ccy   number,
    total_amount_in_inst_ccy    number,
    fx_rate_bid                         number,
    fx_rate_ask                         number,
    settlement_price_in_inst_ccy  number,
    is_settlement                         varchar2(3)
)
    partition by range (reporting_date)
(partition P0 values less than (TO_DATE(' 01/12/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
partition P1 values less than (TO_DATE(' 01/01/2024 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
partition P2 values less than (TO_DATE(' 01/02/2024 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
partition P3 values less than (TO_DATE(' 01/03/2024 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
);
//

alter table ct_option_close_profit_loss_h set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));
//

alter table ct_option_close_profit_loss_h
    add constraint ct_option_close_profit_loss_h_pk primary key ( cash_transaction_seq, effective_start_timestamp )
    using index tablespace ODS_IND;
//

--//@UNDO

DROP TABLE bi_ods.ct_option_close_profit_loss
    //

DROP TABLE bi_ods.ct_option_close_profit_loss_h
    //
