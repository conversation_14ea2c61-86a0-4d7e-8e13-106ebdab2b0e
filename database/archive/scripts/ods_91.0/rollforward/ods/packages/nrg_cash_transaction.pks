CREATE OR REPLACE PACKAGE bi_ods.nrg_cash_transaction
AS
    -- ===================================================================================
    -- NRG_CASH_TRANSACTION
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the cash transactions model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     05/09/2011   Sanket Mittal     1.0     Creation (stub only)
    --     08/12/2011   Mark Gornicki     1.1     Added the functionality to process cash bookings
    --     19/12/2011   Mark Gornicki     1.2     Incorporate all changes to include Platform attribute
    --     23/12/2011   Mark Gornicki     1.3     Incorporate all changes to include Reporting Date attribute
    --     09/02/2012   Mark Gornicki     2.2     Changed the create_cash_account_stub call to include platform parameter
    --     14/02/2012   Mark Gornicki     2.3     Added additional XML Ref parameter for periodic settlement adjustments
    --                                            Required for historical data load, but still valid within XSD even if no longer used
    --     15/02/2012   Mark Gornicki     2.4     Added lookup for Trading Account Id for MarketMaker
    --     28/02/2012   Mark Gornicki     2.8     Added missing column AMOUNT_FX_REVAL_RATE for CT_CHARGES. (Story 1318)
    --                                            Added cardinality hints to the CAST'd tables for performance improvements (Story 1263)
    --                                            On put_cash_transaction added array parameter p_ct_charge_rebates and other dependant code (Story 1320)
    --     13/03/2012   Mark Gornicki     2.9     Temporarily commenting out the MM Trading Account Lookup and "CUSTOMER" to "CUSTOMER" validation check, due to rejects currently occurring
    --                                            where CMS FIRM_ACCOUNTS have also been created as TRADING_ACCOUNTS during migration.
    --                                            The correct resolution is to remove the trading_accounts and load the FIRM_ACCOUNTS but this will take too long to resolve.
    --                                            As a consequence we will remove the check and when the account situation is ready, we will have to issue
    --                                            a datafix to reparent all cash related data to the FIRM_ACCOUNT records and remove the trading account from the
    --                                            ODS tables. Story 1355 raised for this
    --     28/03/2012   Mark Gornicki     3.0     Added additional parameters for MM late deals and MM Position Reference (BackofficeRef)
    --                                            to put_cash_transaction and all associated processing
    --                                            NRG will also start populating REFCODE and REFCODIFIER with MM details such as the TradeId for the linkedorderid
    --                                            and the backoffice/position reference for the MM Financing
    --     31/07/2012   Mark Gornicki     3.3     Added new parameter p_order_id to put_cash_transaction, and new internal procedures to maintain the link table to orders
    --     23/01/2013   Sanket Mittal     3.4     Added the methods rectify business date and get stub id's
    --     23/04/2013   Sanket Mittal     3.7     Added the new fields for P2. Removed the business date and reporting date from the parameters for put_cash_transactions
    --     20/06/2013   Prachi Shah       3.8     Added the new fields Payment_code, Payment_codifier for P2.
    --     17/09/2013   Ravi Shankar      3.9     Modified get_bi_metric_type procedure to include logic to fetch MM(Non NG) bi_metric_type as well
    --     21/10/2013   Adam Krasnicki    4.1     Modified put_cash_transaction procedure.
    --     01/11/2013   Adam Krasnicki    4.2     Added get_hedge_cash_transactions function and put_hedge_cash_transactions procedure which calls put_cash_transactions
    --     19/03/2014   Adam Krasnicki    4.5     CT_PAYMENTS CHANGED (5 new attributes added to ct_payment_obj)
    --     24/03/2014   Adam Krasnicki    4.5     CT_CHARGES changed (customer_classification, person_id added to table and  ct_charge_obj)
    --     24/03/2014   Adam Krasnicki    4.5     CT_DIVIDENTS changed (new object/table, new logic for total_amounts% CT_DIVIDEND_OPEN_TRADES[_H])
    --     26/03/2014   Adam Krasnicki    4.6     CT_PARTNER_TRADE_SPREAD_CD
    --                                            ,CT_PARTNER_CMSN_CHARGE_CD
    --                                            ,CT_PARTNER_CRNG_COSTS_CD
    --                                            ,CT_PARTNER_CHARGES_CD
    --                                            ,CT_PAYMENT_TRADING_REBATE_CD have been added and are fed as part of put_cash_transaction
    --     12/09/2014   Adam Krasnicki    4.6     EOD_POSITIONS renamed to EOD_OPEN_TRADES
    --     30/06/2015   Adam Krasnicki    5.2     get_bi_metric_type corrected for  NG, RESULT_CACHE added
    --     28/12/2015   Sanket Mittal     5.3     New attribute to cash transactions for instrument_relations
    --     22/06/2016   Sanket Mittal     5.5     BER-2705-Integrate updated cash transactions custom data changes - Knockouts
    --     22/08/2016   Sanket Mittal     5.7     BER-2837 Integrate updated payments custom data changes
    --     19/12/2016   Sanket Mittal     5.9     BER-3222 ODS - Integrate updated commission charge custom data
    --     17/05/2018   Patrick Dinwiddy  6.4     BER-4626 Decommission ng_cash_balances
    --     03/05/2019   Patrick Dinwiddy  6.9     JCS-10568 new German Tax objects
    --     03/02/2020   Patrick Dinwiddy  7.2     JCS-12162 and JCS-12140 new rebate ai and partner cc abs entities
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

    -- ===================================================================================
    -- create_cash_transaction_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a cash_transaction stub in case the cash_transaction is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CASH_TRANSACTIONS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_booking_number                 Booking Number
    --     p_plaform                        Platform e.g. NG
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_cash_transaction_stub (p_user                       IN cash_transactions.created_by%TYPE,
                                            p_logical_load_timestamp     IN cash_transactions.logical_load_timestamp%TYPE,
                                            p_effective_start_timestamp  IN cash_transactions.effective_start_timestamp%TYPE,
                                            p_booking_number             IN cash_transactions.booking_number%TYPE,
                                            p_platform                   IN cash_transactions.platform%TYPE,
                                            p_record_source              IN cash_transactions.record_source%TYPE,
                                            p_cash_transaction_seq       OUT cash_transactions.cash_transaction_seq%TYPE);

  -- ===================================================================================
  -- put_cash_transaction
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a cash transaction (booking)
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     CASH_TRANSACTIONS
  --     CASH_TRANSACTIONS_H
  --     CASH_TRANSACTION_ENTRIES
  --     CASH_TRANSACTION_ENTRIES_H
  --     other minor link tables
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                          Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                             User
  --     p_effective_start_timestamp        Effective Start Timestamp
  --     p_booking_number                   Booking Number
  --     p_plaform                          Platform e.g. NG
  --     p_business_date                    Business Date
  --     p_reporting_date                   Reporting Date
  --     p_transaction_time                 Transaction Time
  --     p_account_number_credit            Account Number Credit
  --     p_account_number_debit             Account Number Debit
  --     p_session_key                      Session Key
  --     p_version_number                   Version Number
  --     p_creation_time                    Creation Time
  --     p_update_time                      Update Time
  --     p_creation_identity_token          Creation Identity Token
  --     p_crtn_on_bhlf_of_idntty_tkn       Crtn On Bhlf Of Idntty Tkn
  --     p_update_identity_token            Update Identity Token
  --     p_updt_on_bhlf_of_idntty_tkn       Updt On Bhlf Of Idntty Tkn
  --     p_amount                           Amount
  --     p_amount_ccy                       Amount Ccy
  --     p_intl_clrd_dbt_acctbal            Intl Clrd Debit Acct Bal
  --     p_intl_clrd_dbt_acctbal_ccy        Intl Clrd Debit Acct Bal Ccy
  --     p_intl_unclrd_dbt_acctbal          Intl Unclrd Debit Acct Bal
  --     p_intl_unclrd_dbt_acctbal_ccy      Intl Unclrd Debit Acct Bal Ccy
  --     p_intl_clrd_crdt_acctbal           Intl Clrd Credit Acct Bal
  --     p_intl_clrd_crdt_acctbal_ccy       Intl Clrd Credit Acct Bal Ccy
  --     p_intl_unclrd_crdt_acctbal         Intl Unclrd Crdt Acct Bal
  --     p_intl_unclrd_crdt_acctbal_ccy     Intl Unclrd Crdt Acct Bal Ccy
  --     p_channel_id                       Channel Id
  --     p_request_id                       Request Id
  --     p_request_function                 Request Function
  --     p_is_conditional                   Is Conditional
  --     p_is_cancellation                  Is Cancellation
  --     p_cancelled_booking_number         Cancelled Booking Number
  --     p_reservation_number               Reservation Number
  --     p_booking_type                     Booking Type
  --     p_booking_comment                  Booking Comment
  --     p_refcodifier                      Refcodifier
  --     p_refcode                          Refcode
  --     p_is_deleted                       Is Deleted
  --     p_trading_account_id               Trading Account Id that triggered the cash transaction
  --     p_trading_account_type              Trading Account Type
  --     p_trade_id                         Optional Trade Id that is associated with the cash transaction
  --     p_dividend                         XML Ref Data (Dividend): Is a Object od CT_Divident
  --     p_ct_periodic_settlements          XML Ref Data (Settlement): Is A Table Of Periodic Settlement Orders
  --     p_ct_carrying_costs_offset         XML part of  Ref Data (Settlement): Is A Table Of Periodic Settlement Orders
  --     p_ct_payments                      XML Ref Data (Pay): Is A Table Of Payments (FMS)
  --     p_ct_charges                       XML Ref Data (Chrg): Is A Table Of Charges
  --     p_ct_periodic_settlement_adjs      XML Ref Data (Settlement): Is A Table Of Periodic Settlement Adjustments
  --     p_ct_charge_rebates                XML Ref Data (Chrg): Is A Table Of Charge Rebates
  --     p_ct_commission                    XML Ref Data (Commission)
  --     p_ct_partner_trade_spread_cd       XML Ref Data (Partner Trade Spread Custom Data): OBJECT
  --     p_is_late_deal                     MM Late Deal indicator which can cause adjustments to the business and reporting dates
  --     p_backoffice_ref                   MM Backoffice Reference which is used to establish a link to EOD_Positions
  --     p_record_source                    Source for the data. This is set to platform for all the transactions. For the others like Cash Chits it is set to Cash Chits
  --     p_is_executed                      This is the flag to check if the cash order has been executed
  --     p_is_out_of_sequence               This is to check if the cash booking has come in out of sequence
  --     p_order_id                         Optional Order Id that is associated with the cash transaction
  --     p_ct_payment_trd_rbt_cd            DEFAULT NULL removed, now it is required
  --     p_instrument_relations             Ref data from hedge bookings for instrument relations
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Transaction Deleted Before Update and After Insert
  --     -20004    Default Exception
  --     -20005    Missing Account Number
  --     -20006    Both MM Credit and Debit Accounts have CUSTOMER trading account types
  --
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_cash_transaction (
                                    p_user                               IN cash_transactions.created_by%TYPE,
                                    p_effective_start_timestamp          IN cash_transactions.effective_start_timestamp%TYPE,
                                    p_booking_number                     IN cash_transactions.booking_number%TYPE,
                                    p_platform                           IN cash_transactions.platform%TYPE,
                                    --p_business_date                     IN cash_transactions.business_date%TYPE,
                                    --p_reporting_date                     IN cash_transactions.reporting_date%TYPE,
                                    p_transaction_time                   IN cash_transactions.transaction_time%TYPE,
                                    p_account_number_credit              IN cash_transactions.account_number_credit%TYPE,
                                    p_account_number_debit               IN cash_transactions.account_number_debit%TYPE,
                                    p_session_key                        IN cash_transactions.session_key%TYPE,
                                    p_version_number                     IN cash_transactions.version_number%TYPE,
                                    p_creation_time                      IN cash_transactions.creation_time%TYPE,
                                    p_update_time                        IN cash_transactions.update_time%TYPE,
                                    p_creation_identity_token            IN cash_transactions.creation_identity_token%TYPE,
                                    p_crtn_on_bhlf_of_idntty_tkn         IN cash_transactions.crtn_on_bhlf_of_idntty_tkn%TYPE,
                                    p_update_identity_token              IN cash_transactions.update_identity_token%TYPE,
                                    p_updt_on_bhlf_of_idntty_tkn         IN cash_transactions.updt_on_bhlf_of_idntty_tkn%TYPE,
                                    p_amount                             IN cash_transactions.amount%TYPE,
                                    p_amount_ccy                         IN cash_transactions.amount_ccy%TYPE,
                                    p_intl_clrd_dbt_acctbal              IN cash_transactions.intl_clrd_debit_acct_bal%TYPE,
                                    p_intl_clrd_dbt_acctbal_ccy          IN cash_transactions.intl_clrd_debit_acct_bal_ccy%TYPE,
                                    p_intl_unclrd_dbt_acctbal            IN cash_transactions.intl_unclrd_debit_acct_bal%TYPE,
                                    p_intl_unclrd_dbt_acctbal_ccy        IN cash_transactions.intl_unclrd_debit_acct_bal_ccy%TYPE,
                                    p_intl_clrd_crdt_acctbal             IN cash_transactions.intl_clrd_credit_acct_bal%TYPE,
                                    p_intl_clrd_crdt_acctbal_ccy         IN cash_transactions.intl_clrd_credit_acct_bal_ccy%TYPE,
                                    p_intl_unclrd_crdt_acctbal           IN cash_transactions.intl_unclrd_crdt_acct_bal%TYPE,
                                    p_intl_unclrd_crdt_acctbal_ccy       IN cash_transactions.intl_unclrd_crdt_acct_bal_ccy%TYPE,
                                    p_channel_id                         IN cash_transactions.channel_id%TYPE,
                                    p_request_id                         IN cash_transactions.request_id%TYPE,
                                    p_request_function                   IN cash_transactions.request_function%TYPE,
                                    p_is_conditional                     IN cash_transactions.is_conditional%TYPE,
                                    p_is_cancellation                    IN cash_transactions.is_cancellation%TYPE,
                                    p_cancelled_booking_number           IN cash_transactions.cancelled_booking_number%TYPE,
                                    p_reservation_number                 IN cash_transactions.reservation_number%TYPE,
                                    p_booking_type                       IN cash_transactions.booking_type%TYPE,
                                    p_booking_comment                    IN cash_transactions.booking_comment%TYPE,
                                    p_refcodifier                        IN cash_transactions.refcodifier%TYPE,
                                    p_refcode                            IN cash_transactions.refcode%TYPE,
                                    p_is_deleted                         IN cash_transactions.is_deleted%TYPE,
                                    p_trading_account_id                 IN trdng_accnts_csh_trns_link.trading_account_id%TYPE,
                                    p_trading_account_type               IN trdng_accnts_csh_trns_link.trading_account_type%TYPE,
                                    p_trade_id                           IN  trds_csh_trns_link.trade_id%TYPE,
                                    p_dividend                           IN ct_dividend_obj,
                                    p_ct_periodic_settlements            IN ct_periodic_settlement_tab,
                                    p_ct_carrying_costs_offset           IN ct_carrying_costs_offset_obj,   --- NEW
                                    p_ct_payments                        IN ct_payment_tab,
                                    p_ct_charges                         IN ct_charge_tab,
                                    p_ct_periodic_settlement_adjs        IN ct_periodic_settlement_adj_tab,
                                    p_ct_charge_rebates                  IN ct_charge_rebate_tab,
                                    p_ct_commission                      IN ct_commission_obj,
                                    p_ct_partner_trade_spread_cd         IN ct_partner_trade_spread_cd_obj,
                                    p_ct_partner_cmsn_charge_cd          IN ct_partner_cmsn_charge_cd_obj,
                                    p_ct_partner_crng_costs_cd           IN ct_partner_crng_costs_cd_obj,
                                    p_ct_partner_charges_cd              IN ct_partner_charges_cd_tab,       --->NEW
                                    p_ct_payment_trd_rbt_cd              IN ct_payment_trd_rbt_cd_tab,      --->NEW
                                    p_is_late_deal                       IN cash_transactions.is_late_deal%TYPE,
                                    p_backoffice_ref                     IN eod_open_trades.mm_backoffice_ref%TYPE,
                                    p_record_source                      IN cash_transactions.record_source%TYPE,
                                    p_is_executed                        IN cash_transactions.is_executed%TYPE,
                                    p_is_out_of_sequence                 IN cash_transactions.is_out_of_sequence%TYPE,
                                    p_order_id                           IN ordrs_csh_trns_link.order_id%TYPE,
                                    p_to_be_cleared                      IN cash_transactions.to_be_cleared%TYPE,
                                    p_is_cancelled                       IN cash_transactions.is_cancelled%TYPE,
                                    p_cancelling_booking_number          IN cash_transactions.cancelling_booking_number%TYPE,
                                    p_is_postable                        IN cash_transactions.is_postable%TYPE,
                                    p_payment_code                       IN cash_transactions.payment_code%TYPE,
                                    p_payment_codifier                   IN cash_transactions.payment_codifier%TYPE,
                                    p_ct_payment_trading_adjust          IN ct_payment_trading_adjust_tab,
                                    p_instrument_relations               IN ct_instrument_relations_tab,
                                    p_ct_trade                           IN ct_trade_obj,
                                    p_ct_hedge_transactions              IN ct_hedge_transactions_tab,
                                    p_parent_booking_number              IN cash_transactions.parent_booking_number%TYPE,
                                    p_ct_forex_conversion                IN ct_forex_conversion_tab,
                                    p_ct_payment_info                    IN ct_payment_info_tab,
                                    p_ct_german_tax                      IN ct_german_tax_tab,
                                    p_ct_partner_cc_abs                  IN ct_prtnr_cc_abs_obj,
                                    p_ct_payment_trd_rbt_ai              IN ct_payment_trd_rbt_ai_tab
                                    );

  -- ===================================================================================
  -- put_cash_chit_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a cash transaction (booking)
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     CASH_TRANSACTIONS
  --     CASH_TRANSACTIONS_H
  --     CASH_TRANSACTION_ENTRIES
  --     CASH_TRANSACTION_ENTRIES_H
  --     other minor link tables
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                          Description
  --     ------------------------------     ----------------------------------------------
  --     p_user                             User
  --     p_effective_start_timestamp        Effective Start Timestamp
  --     p_business_date                    Business_date
  --     p_reporting_date                   Reporting_date
  --     p_record_source                    Source for the record
  --     p_cash_chits                       Set of cash chits as cash transactions
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Transaction Deleted Before Update and After Insert
  --     -20004    Default Exception
  --     -20005    Missing Account Number
  --     -20006    Both MM Credit and Debit Accounts have CUSTOMER trading account types
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_cash_chit_set(p_user                               IN cash_transactions.created_by%TYPE,
                              p_effective_start_timestamp          IN cash_transactions.effective_start_timestamp%TYPE,
                              --p_business_date                      IN cash_transactions.business_date%TYPE,
                              --p_reporting_date                     IN cash_transactions.reporting_date%TYPE,
                              p_record_source                      IN cash_transactions.record_source%TYPE,
                              p_cash_chits                         IN cash_transaction_tab);

    -- ===================================================================================
    -- get_cash_transactions
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of booking_number attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_creation_time_from             Creation time from - Time the cash transaction was created at source
    --     p_creation_time_to               Creation time to - Time the cash transaction was created at source
    --     p_plaform                        Platform e.g. NG
    -- -----------------------------------------------------------------------------------
    FUNCTION get_cash_transactions(p_creation_time_from  IN cash_transactions.creation_time%TYPE,
                                   p_creation_time_to    IN cash_transactions.creation_time%TYPE,
                                   p_platform            IN cash_transactions.platform%TYPE
                                  ) RETURN SYS_REFCURSOR;

    -- ===================================================================================
    -- get_last_booking
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return the booking that was last written to the database
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_booking_number                 This is the out parameter which stores gives the last booking that was written to the database
    --     p_creation_time                  This is the creation time for the last booking
    -- -----------------------------------------------------------------------------------

    PROCEDURE get_last_booking (p_booking_number  OUT cash_transactions.booking_number%TYPE,
                                p_creation_time   OUT cash_transactions.creation_time%TYPE);

  -- ===================================================================================
  -- get_bi_metric_type
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to Calculate the metric type
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --      p_account_type                    Account Type
  --      p_counterparty_account_type       Counterparty Account Type
  --      p_channel_id                      channel_id
  --      p_booking_type                    Booking Type
  --      p_amoaunt                         case when p_amount >= 0 then 1 else -1 end
  --      p_platform                         Cash Account Platform
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the booking type
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION get_bi_metric_type(p_account_type               cash_accounts.account_type%TYPE,--Credit - Account (AccountType)
                              p_counterparty_account_type  cash_accounts.account_type%TYPE,--Counterparty - debit(AccountType)
                              p_channel_id                 cash_transactions.Channel_Id%TYPE,
                              p_booking_type               cash_transactions.booking_type%TYPE,
                              p_platform                   cash_transactions.platform%TYPE,
                              p_amount                     cash_transactions.amount%TYPE,
                              p_ta_account_type            trading_accounts.account_type%TYPE,
                              p_refcode                    cash_transactions.refcode%TYPE) RETURN VARCHAR2;

    -- ===================================================================================
    -- rectify_business_date_eod
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This procedure is used to set the business date for the out of sequence cash transactions
    --
    --     This procedure will be called as part of EOD Cash Balances for NG ( populate_eod_ng_balance)
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_business_date                 Starting Date For the balance
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE rectify_business_date_eod(p_business_date DATE);

    -- ===================================================================================
    -- get_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of stubbed cash account id's
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
    --     p_platform                       Cash Account Platform
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
      FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER,
                               p_platform     IN VARCHAR2) RETURN SYS_REFCURSOR;

    -- ===================================================================================
    -- sync_latest_ng_cash_balance
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to update the latest cash balance for ng cash accounts
    --
    --     If the cash account number is passed in the parameter then the cash balance is updated only for that account
    --     Else the cash balances are updated for all the NG accounts
    --
    --     This sub routine can be run at the end of day to sync all the latest cash balances
    --
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_account_number                 Cash Account Number
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------

    --PROCEDURE sync_latest_ng_cash_balance(p_account_number IN NUMBER DEFAULT -1);

  -- ===================================================================================
  -- get_hedge_cash_transactions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of booking_number and version_number attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_creation_time_from             Creation time from - Time the cash transaction was created at source
  --     p_creation_time_to               Creation time to - Time the cash transaction was created at source
  --     p_plaform                        Platform e.g. NG
  -- -----------------------------------------------------------------------------------

  FUNCTION get_hedge_cash_transactions(p_creation_time_from IN cash_transactions.creation_time%TYPE,
                                       p_creation_time_to   IN cash_transactions.creation_time%TYPE,
                                       p_platform           IN cash_transactions.platform%TYPE) RETURN SYS_REFCURSOR;

  -- ===================================================================================
  -- put_hedge_cash_transactions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a cash transaction (booking)
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     CASH_TRANSACTIONS
  --     CASH_TRANSACTIONS_H
  --     CASH_TRANSACTION_ENTRIES
  --     CASH_TRANSACTION_ENTRIES_H
  --     other minor link tables
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                          Description
  --     ------------------------------     ----------------------------------------------
  --     p_user                             User
  --     p_effective_start_timestamp        Effective Start Timestamp
  --     p_business_date                    Business_date
  --     p_reporting_date                   Reporting_date
  --     p_record_source                    Source for the record
  --     p_cash_hedge                       Table of hedge cash transactions
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Transaction Deleted Before Update and After Insert
  --     -20004    Default Exception
  --     -20005    Missing Account Number
  --     -20006    Both MM Credit and Debit Accounts have CUSTOMER trading account types
  --     -20007    Empty Hedge Transaction
  -- -----------------------------------------------------------------------------------


  PROCEDURE put_hedge_cash_transactions(p_user                        IN cash_transactions.created_by%TYPE
                                       ,p_effective_start_timestamp   IN cash_transactions.effective_start_timestamp%TYPE
                                       ,p_record_source               IN cash_transactions.record_source%TYPE
                                       ,p_cash_hedge                  IN cash_transaction_tab);

END nrg_cash_transaction;
/