DROP TYPE bi_ods.price_option_contracts_tab;

CREATE OR REPLACE TYPE bi_ods.price_option_contracts_obj IS OBJECT (
is_incr_long_pos_allowed	varchar2(100),
is_incr_short_pos_allowed	varchar2(100),
symbol	                    varchar2(100),
instrument_code	            varchar2(100),
asset_type              	varchar2(100),
currency	                varchar2(100),
quote_id	                varchar2(100),
time	                    timestamp(6),
book_time	                timestamp(6),
status	                    varchar2(100),
good_for	                varchar2(100),
underlying_stream_code   	varchar2(100),
option_code	                varchar2(100),
subs_expiry_time	        timestamp(6),
price_exponent	            number,
strike_exponent	            number,
maturity_date	            date,
underlying_bid_price	    varchar2(100),
underlying_ask_price	    varchar2(100),
bid_sizes_order_book	    varchar2(500),
ask_sizes_order_book	    varchar2(500),
bid_prices_order_book	    varchar2(500),
ask_prices_order_book	    varchar2(500),
option_trading_class	    varchar2(100),
option_type    	            varchar2(100),
strike_price	            number,
reason	                    varchar2(100),
full_symbol	                varchar2(100),
size_exponent	            number
);