DROP TYPE valued_open_trade_tab FORCE;
DROP TYPE valued_open_trade_obj FORCE;

CREATE OR REPLACE TYPE bi_ods.valued_open_trade_obj IS OBJECT (
order_id                            VARCHAR2(50),
customer_upnl_in_prmry_ccy          NUMBER,
customer_upnl_evluatn_price         NUMBER,
customer_upnl_evluatn_reval         NUMBER,
decision_maker                      NUMBER,
customer_id                         NUMBER,
accnt_mon_upnl_in_prmry_ccy         NUMBER,
accnt_mon_upnl_evluatn_price        NUMBER,
accnt_mon_upnl_evluatn_reval        NUMBER,
cmc_upnl_in_prmry_ccy               NUMBER,
cmc_upnl_evluatn_price              NUMBER,
cmc_upnl_evluatn_reval              NUMBER,
prime_margin                        NUMBER,
accnt_mon_opn_trd_amt_inst_ccy      NUMBER,
accnt_mon_opn_trd_amt_prm_ccy       NUMBER,
accnt_mon_opn_trd_amnt_usd          NUMBER,
accnt_mon_opn_trd_amnt_gbp          NUMBER,
accnt_mon_opn_trd_amnt_eur          NUMBER,
unrlzd_cptl_gns_in_prmry_ccy        NUMBER,
gslo_absolute_limit_price           NUMBER,
open_trade_quantity                 NUMBER,
direction                           VARCHAR2(10),
intl_cust_opn_trd_amnt              NUMBER,
intl_cust_opn_trd_amnt_ccy          VARCHAR2(3),
intl_cust_opn_trd_amnt_fxr          NUMBER,
intl_cust_opn_trd_amnt_prm_ccy      NUMBER,
opening_trade_id                    VARCHAR2(50),
opening_trade_price                 NUMBER,
opn_accrd_trnvr_in_accnt_ccy        NUMBER,
customer_upnl_in_inst_ccy           NUMBER,
customer_opn_trd_amnt_inst_ccy      NUMBER,
customer_opn_trd_amnt_prm_ccy       NUMBER
);
/