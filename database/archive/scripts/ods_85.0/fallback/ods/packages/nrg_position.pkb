CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_position AS
  -- ===================================================================================
  -- NRG_POSITION
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of Positions
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     09/12/2011   Sanket Mittal      1.0    Creation
  --     25/01/2012   Manoj Kumar        1.1    Modified as per the latest data lineage for eod positions
  --     13/01/2012   Sanket Mittal      1.2    Modified the EOD Positions write and the Latest Positions
  --     29/02/2012   Manoj Kumar        1.3    Modified EOD Positions to include price_symbol,feed_symbol,eod_price fields.
  --     12/03/2012   Sanket Mittal      1.4    Updated the change to avoid deadlocks
  --     18/07/2012   Sanket Mittal      1.5    Added put_eod_cash_position_set
  --     18/07/2012   Sanket Mittal      1.6    Added is_deleted flag to eod_positions which can help us soft delete data for MM
  --     02/08/2012   Patrick Dinwiddy   1.7    Added put_eod_position_snapshot which helps loading of the EDW position summaries
  --     12/09/2012   Sanket Mittal      1.8    Updated the logic for EOD Positions for MM Backoffice re run
  --     18/09/2012   Sanket Mittal      1.9    Removed the fields feed_symbol and price_symbol
  --     19/09/2012   Sanket Mittal      2.0    Updated the business rules for the normalised fields normalised_open_trade_price, normalised_open_trade_quantity_in_trading_ccy, normalised_open_value_in_trading_ccy and normalised_trading_ccy
  --     04/10/2012   Sanket Mittal      2.1    Updated the business rules to remove calcs for margin, margin secondary and normalised margin requirement
  --     21/11/2012   Shanavaz Malayodu  2.2    Commented out the If Clause for Insert
  --     04/01/2013   Sanket Mittal      2.3    Added the counterparty id to eod positions and latest positions. Also updated the deletion for latest position at eod for NG
  --     29/01/2013   Sanket Mittal      2.4    Updated the query for MM positions on latest positions for performance tuning
  --     13/03/2013   Sanket Mittal      2.5    Updated the code to handle replayed snapshots for NG
  --     19/03/2013   Sanket Mittal      2.6    Updated the code for open trade price to handle 0 quantity
  --     29/04/2013   Sanket Mittal      2.7    Updated the EOD positions load bot to sync the latest position for NG with eod snapshot and added a new proc for this sync
  --     08/07/2013   Prachi Shah        2.8    As part of P2 change, margin will not be provided by Upstream, it needs to be calculated by ODS. Adding a procedure for same.
  --     10/07/2013   Prachi Shah        2.9    Updated the procedure upd_eod_positions_margin to update conversion rate for fx
  --     01/08/2013   Prachi Shah        3.0    Updated the margin logic in upd_eod_positions_margin procedure
  --     26/07/2013   Prachi Shah        3.1   Added the procedure cleanup_latest_positions.
  --     29/07/2013   Prachi Shah        3.1   Updated the procedure upd_eod_positions to add and update new columns for eod_positions table
  --     31/07/2013   Prachi Shah        3.1   Updated the reporting date calculation for eod_positions, eod_cash_positions
  --     05/08/2013   Prachi Shah        3.1   Updated the calculation for normalised_margin_rqrmnt, eod_price for NG
  --     22/08/2013   Prachi Shah        3.2   Updated the insert logic for latest positions for NG
  --     27/08/2013   Prachi Shah        3.2   Upddated upd_eod_positions_margin as per the latest sql.
  --     30/08/2013   Prachi Shah        3.2   Update the logic for margin calculations in upd_eod_positions
  --     25/09/2013   Prachi Shah        3.3   Included the logic for MM Unrealised pnl calculations
  --     10/10/2013   Ravi Shankar Gopal 3.4   Modified the logic for fetching eod_prices data as per BER-641
  --     17/10/2013   Prachi Shah        3.4   Modified the logic for MM Unrealised PNL
  --     18/10/2013   Prachi Shah        3.4   Modified the logic for fetching eod_prices data as per BER - 674
  --     18/10/2013   Prachi Shah        3.4   Modified the logic for margin_trdn_accnt_ccy as per BER - 673
  --     23/10/2013   Prachi Shah        3.4   Modified the logic in eod_prices to eliminate the duplicates.
  --     24/10/2013   Prachi Shah        3.4   Modified the logic for instrument prices in eod_prices in unrealised pnl sql
  --     19/11/2013   Adam Krasnicki     3.5   Modified the logic for latest_position insert (merge instead of insert)
  --     21/11/2013   Ravi Shankar Gopal 3.5   Modified the logic for MM Unrealised PNL for Sprint33
  --     28/11/2013   Prachi Shah        3.5   Modified the logic for MM Unrealsed PNL removed the converion to GBP
  --     10/12/2013   Prachi Shah   3.6   Modified the logic for NG UNrealsed PNL added a clause business_DAte desc for eod_prices
  --     24/12/2013   Ravi Shankar Gopal 3.7   BER - 781: Added New logic to handle Integration with Hedge Positions. Details as follows:
  --                                           (a). Updated the put_eod_position_set procedure to handle the next gen hedge position
  --                                           (b). Updated the put_eod_position_snapshots to handle the new Record Source "NG-HEDGE"
  --                                           (c). Updated put_position,resync_latest_position,cleanup_latest_position Procedures to
  --                                                exclude NG Hedge
  --     03/01/2014  Adam Krasnicki      3.8   Correction of NG Hedge logic
  --     14/01/2014  Adam Krasnicki      3.8   New procedure put_anytime_positions_set added
  --     21/01/2014  Prachi Shah         3.8   Updated the logic for MM Unrealised PNL upd_eod_positions_margin(only for MM) as shared by by Matt today
  --     29/01/2014  Adam Krasnicki      3.9   BER-845  p_platform NOT LIKE 'NG-HEDGE%' in put_positions replaced by
  --                                           p_record_source NOT LIKE 'NG-HEDGE%'
  --     10/02/2014  Prachi Shah         3.9   Updated the logic for MM Unrealised PNL upd_eod_positions_margin(only for MM) for value date filter
  --     10/02/2014  Adam Krasnicki      3.9   Story BER-763, updated delete statement (delete from cash_transaction[_entries] - casting applied to avoid full table scan)
  --     19/02/2014  Prachi Shah         3.9   Removed the logic for calculation of unrealised pnl for MM from upd_eod_positions_margin.
  --                                           Added the logic for same into put_eod_position_set procedure.
  --     19/02/2014  Adam Krasnicki      3.9   Added else to case statement for the calc. of unrealised_pnl measure
  --     06/03/2014  Adam Krasnicki      4.0   Added new function cleanup_latest_positions_2
  --     18/07/2014  Adam Krasnicki      4.0   p_load_latest_positions added to put_position procedure
  --     19/08/2014  Adam Krasnicki      4.0   Changes in select stmt in upd_eod_positions_margin procedure
  --     12/09/2014  Adam Krasnicki      4.1   eod_positions[_h] table renamed to eod_open_trades[_h]  plus other long/short changes
  --     30/10/2014  Adam Krasnicki      4.1   Business date determined in anytime_positions changed
  --     30/10/2014  Adam Krasnicki      4.2   Eod_open_trades/Eod_open_positions logic for MM changed
  --     12/11/2014  Adam Krasnicki      4.2   Put_position procedure changed for MM
  --     14/11/2014  Adam Krasnicki      4.2   NG-HEDGE-FX added to put_eod_position
  --     26/11/2014  Adam Krasnicki      4.2   NG-HEDGE-FX hedge_profitloss set to amount
  --     05/12/2014  Adam Krasnicki      4.3   NG-HEDGE-FX chnage in upd_eod_open_trades procedure (additional if for NG-HEDGE-FX only)
  --                                           plus change of the with statment  eod_banded_prices in upd_eod_open_trades proc
  --                                          (possible request for historical days)
  --     15/01/2015  Adam Krasnicki      4.4   merge added to update eod_cash_position_snapshots after load MM eod_cash_positions (proc put_eod_cash_position_set)
  --     16/01/2015  Adam Krasnicki      4.4   unrealised_pnl changed in eod_positions_unrpnl_stg (UPD_EOD_OPEN_TRADES procedure)
  --     25/02/2015  Adam Krasnicki      4.5   4 new columns added to eod_open_positions (set to NULL)
  --     13/03/2015  Adam Krasnicki      4.5   2 new cols added to eod_open_positions (logic changed in insert statement to EOD_OPEN_POSITIONS)
  --     16/03/2015  Adam Krasnicki      4.5   Group by statement changed in insert eod_open_positions  (sum changed to max etc.)
  --     07/04/2015  Adam Krasnicki      4.5   Change in logic in eod_open_position sql for NG
  --     08/05/2015  Adam Krasnicki      4.5   execution_type parameter added to eod_open_trades
  --     27/05/2015  Adam Krasnicki      4.6   put_position: procedure adjusted to consume SPEEDBET data (only one order_id allowed, it is updated later)
  --     28/05/2015  Adam Krasnicki      4.7   UnrPnL changed for speedbets
  --     16/06/2015  Adam Krasnicki      4.7   put_position: p_direction_multiplier parameter added
  --     26/06/2015  Adam Krasnicki      4.7   eod_open_trades, new column cd_state
  --     01/07/2015  Adam Krasnicki      4.7   put_eod_position_set: EOD_OPEN_TRADES, changes in normalised_open_price and normalised_open_qty_trad_ccy measures for MM
  --     01/07/2015  Adam Krasnicki      4.7   put_position: BI_ODS.LATEST_OPEN_TRADES.MARGIN, MARGIN_SECONDARY, p_margin AS NULL, p_margin_secondary AS NULL
  --     05/10/2015  Sanket Mittal       4.8   put_position additional attributes trading scope and managed order information tab
  --     07/10/2015  Sanket Mittal       4.9   updatd put_position to add trading_scope Story BER-2081
  --     07/10/2015  Sanket Mittal       5.0   Updated EOD_OPEN_POSITIONS and EOD_OPEN_TRADES for p_record_source = 'SPEEDBET' BER-2083
  --     19/10/2015  Adam Krasnicki      5.1   put_eod_position_set: ltab_latest_positions tab populated only for MMCFD data
  --     30/01/2016  Sanket Mittal       5.2   put_eod_position_set: New columns for binary added
  --     03/03/2016  Sanket Mittal       5.3   Updated for BER-2369 eod_open_trades changes
  --     26/04/2016   Sanket Mittal      5.4   Updated for eod binary prices logic BER-2504 and 2508
  --     24/06/2016  Sanket Mittal       5.5   Updated for ProductPointMultiplier logic BER-2712
  --     16/08/2016  Sanket Mittal       5.6   BER-2818 Contingent Order Snapshot should only be called by the NG Record Source position
  --     22/08/2016  Sanket Mittal       5.7   BER-2713 ODS - EOD_Open_Trades and EOD_Open_Positions needs to handle missing prices
  --     01/09/2016  Sanket Mittal       5.8   BER-2872 Knockouts Normalisation - Add the STRIKE_PRICE
  --     20/09/2016  Sanket Mittal       5.9   BER-2095 ODS - Remove Contingent_Orders_Snapshot call from nrg_position, and set up DBMS Scheduler Job.
  --     18/10/2016  Sanket Mittal       6.0   BER-2969 BI_ODS.EOD_OPEN_TRADES - add new columns
  --     18/10/2016  Sanket Mittal       6.0   BER-2986 Correction of NRG_POSITION package 'SPPEEDBET'
  --     15/11/2016  Patrick Dinwiddy    6.1   BER-3088  Refactor Hedge Execution Commission
  --     14/02/2017  Karolina Krasnicka  6.2   BER-3389  upd_eod_open_trades_hedge_fx: add materlialize hints + query in EOD_HEDGE_FX using business_date
  --     12/06/2017  Sanket Mittal       6.3   BER-3717 ODS - Add REPORTING_DATE where statement to the end of day process
  --     05/07/2017  Sanket Mittal       6.4   BER-3800 ODS - NRG_Position.upd_eod_open_trades - Resolve JOIN issue
  --     27/07/2017  Sanket Mittal       6.5   BER-3870 ODS - Anytime Positions Update
  --     15/01/2018  Sakina Kinkhabwala  6.6   BER-4128 ODS - put_eod_position_set - add new attributes
  --     21/04/2018  Sakina Kinkhabwala  6.7   BER-4479 ODS - insert_eod_open_positions to address prime margin positions for CMC-PL closeout schema code.
  --     10/05/2018  Patrick Dinwiddy    6.8   BER-3731 enable batching of put_eod_position_set and BER-3770 amend upd_eod_open_trades to use staging tables
  --     27/08/2019  Sei Shibahara       6.9   BER-5190 FXR Banding and BER-5235 uPnL for Hedge FXFWD
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '6.9';
  gc_default_timestamp  CONSTANT TIMESTAMP   := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');




  --
  --
  -- ===================================================================================
  -- PRIVATE MODULES
  -- ===================================================================================
  --
  --
  --
  -- ===================================================================================
  -- PUBLIC MODULES
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

   FUNCTION VERSION
        RETURN VARCHAR2 deterministic IS

  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END VERSION;

  -- ===================================================================================
  -- reset_eod_positions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure deletes the EOD positions prior to the business date
  --     passed in the parameter
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     LATEST_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION reset_eod_positions(p_reporting_date DATE,
                               p_platform VARCHAR2) RETURN NUMBER IS
    lv_reporting_date DATE;
    lv_platform latest_open_trades.platform%TYPE;

  BEGIN
    --
    --Delete the previous EOD positions
    --

    DELETE latest_open_trades
    WHERE nrg_common.get_reporting_date(last_modified_time) <= lv_reporting_date AND
          platform = lv_platform;

    RETURN 1;

  END reset_eod_positions;

  FUNCTION get_hdg_exec_commission(p_trading_account_id      IN Eod_Open_Trades.Trading_Account_Id%TYPE,
                                   p_reporting_date          IN Eod_Open_Trades.reporting_date%TYPE,
                                   p_product_instrument_code IN Eod_Open_Trades.product_instrument_code%TYPE,
                                   p_hedge_risk_bucket       IN Eod_Open_Trades.hedge_risk_bucket%TYPE,
                                   p_mm_value_date           IN Eod_Open_Trades.mm_value_date%TYPE)
  RETURN NUMBER IS

    lv_hdg_execution_commission NUMBER;

  BEGIN
    logger.logger.set_module('NRG_COMMON.GET_HDG_EXEC_COMMISSION');

    SELECT nvl(sum(t.hdg_execution_commission),0)
    INTO lv_hdg_execution_commission
    FROM bi_ods.trades t
    WHERE t.trading_account_id = p_trading_account_id AND
          t.reporting_date = p_reporting_date AND
          t.product_instrument_code = p_product_instrument_code AND
          t.hdg_risk_bucket = p_hedge_risk_bucket AND
          t.mm_value_date = p_mm_value_date AND
          t.trading_account_type = 'HEDGE' AND
          t.record_source = 'NG-HEDGE' AND
          t.hdg_asset_class = 'FOREX';

    RETURN lv_hdg_execution_commission;

    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  PROCEDURE put_eod_opn_trds_mngd_info(p_effective_start_timestamp eod_open_trades_managed_info.effective_start_timestamp%TYPE,
                                       p_user                      eod_open_trades_managed_info.created_by%TYPE,
                                       p_platform                  eod_open_trades_managed_info.platform%TYPE,
                                       p_order_id                  eod_open_trades_managed_info.order_id%TYPE,
                                       p_managed_order_info        eod_open_trds_managed_info_tab,
                                       p_business_date             eod_open_trades_managed_info.business_date%TYPE,
                                       p_reporting_date            eod_open_trades_managed_info.reporting_date%TYPE,
                                       p_logical_load_timestamp    eod_open_trades_managed_info.logical_load_timestamp%TYPE) IS

  BEGIN

    --
    --Create Stubs
    --

    FOR lv_cnt IN 1..p_managed_order_info.COUNT LOOP
      nrg_trading_account.create_trading_account_stub(p_trading_account_id => p_managed_order_info(lv_cnt).trading_account_id,
                                                      p_trading_account_type => p_managed_order_info(lv_cnt).trading_account_type,
                                                      p_user => p_user,
                                                      p_effective_start_timestamp => p_effective_start_timestamp,
                                                      p_logical_load_timestamp => p_logical_load_timestamp);

      nrg_order.create_order_stub(p_user => p_user,
                                  p_platform => p_platform,
                                  p_order_id => p_managed_order_info(lv_cnt).managed_order_id,
                                  p_effective_start_timestamp => p_effective_start_timestamp,
                                  p_logical_load_timestamp => p_logical_load_timestamp);

    END LOOP;


    INSERT INTO eod_open_trades_managed_info(platform,
                                             order_id,
                                             managed_order_id,
                                             snapshot_time,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             business_date,
                                             reporting_date,
                                             trading_account_id,
                                             trading_account_type,
                                             open_trade_quantity,
                                             allocation_transaction_id,
                                             managed_transaction_id,
                                             opening_trade_price)
                                      SELECT p_platform,
                                             p_order_id,
                                             managed_order_id,
                                             p_effective_start_timestamp snapshot_time,
                                             SYSTIMESTAMP logical_load_timestamp,
                                             p_user created_by,
                                             SYSTIMESTAMP create_timestamp,
                                             p_user updated_by,
                                             SYSTIMESTAMP update_timestamp,
                                             p_effective_start_timestamp,
                                             p_business_date,
                                             p_reporting_date,
                                             trading_account_id,
                                             trading_account_type,
                                             open_trade_quantity,
                                             allocation_transaction_id,
                                             managed_transaction_id,
                                             opening_trade_price
                                      FROM  TABLE(CAST(p_managed_order_info AS eod_open_trds_managed_info_tab));
  END;

   -- ===================================================================================
  -- upd_mm_eod_open_trades
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will update value in ODS and update it
  --     in bi_ods.eod_open_trades table for MM
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_mm_eod_open_trades (p_business_date    IN DATE
                                    ,p_reporting_date   IN DATE
                                    ,p_platform         IN VARCHAR2)
 IS
 BEGIN

 MERGE INTO Eod_Open_Trades old_values
 USING (with eod_core_prices AS
     (SELECT * FROM (SELECT business_date,
                              quote_time,
                              price_symbol,
                              instrument_code,
                              bid_price,
                              ask_price,
                              (bid_price + ask_price)/2 AS mid_price,

                              CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 3)
                                   ELSE NULL
                               END AS fxr_from_ccy,

                              CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 4, 3)
                                   ELSE NULL
                               END AS fxr_to_ccy,
                               'CMC-STANDARD' AS price_band,
                               ROW_NUMBER () OVER (PARTITION BY (CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 6) ELSE instrument_code END ) ORDER BY business_date desc, quote_time DESC)rnk
                       FROM bi_ods.prices
                       WHERE platform = 'NG'
                         AND instrument_code NOT LIKE '%/CAR'
                         AND business_date = p_business_date) WHERE rnk = 1)
    select eot.platform,
           eot.order_id,
           eot.snapshot_time,
           eot.business_date,
           eot.reporting_date,
           eot.amount * case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval1.ask_price end AS AMNT_IN_TRDNG_ACCNT_PRMRY_CCY,
           eot.amount AS EOD_VALUE,
           eot.amount_currency AS EOD_VALUE_CCY,
           eot.amount * case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval1.ask_price end AS EOD_VALUE_IN_ACCNT_CCY
      from bi_ods.eod_open_trades eot
   join bi_ods.instruments i on i.instrument_code = eot.product_instrument_code
   left join eod_core_prices reval1 on reval1.fxr_from_ccy = eot.amount_currency
                           and reval1.fxr_to_ccy = eot.trading_accnt_primary_ccy
                           and reval1.business_date = eot.business_date
   where eot.business_date = p_business_date
   AND eot.Reporting_Date= p_reporting_date
   AND eot.Platform = p_platform) new_values
   ON (old_values.Platform=new_values.platform AND
       old_values.Order_Id=new_values.order_id AND
       old_values.Snapshot_Time=new_values.snapshot_time)
   WHEN MATCHED THEN
     UPDATE SET
       old_values.amnt_in_trdng_accnt_prmry_ccy=new_values.amnt_in_trdng_accnt_prmry_ccy
      ,old_values.eod_value=new_values.eod_value
      ,old_values.eod_value_ccy=new_values.eod_value_ccy
      ,old_values.eod_value_in_accnt_ccy=new_values.eod_value_in_accnt_ccy
      WHERE old_values.Reporting_Date=p_reporting_date
        AND old_values.platform = p_platform;


 END upd_mm_eod_open_trades;

  -- ===================================================================================
  -- upd_eod_open_trades_hedge_fx
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_eod_open_trades_hedge_fx (p_business_date    IN DATE,
                                       p_reporting_date   IN DATE
                                       /***** Begin Modification for V3.3 - BER606 *****/
                                       ,p_platform VARCHAR2
                                       /***** End Modification for V3.3 *****/
                                       ,p_record_source VARCHAR2
                                       )

  IS
  BEGIN
    EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_positions_hedge_fx_stg';

     INSERT INTO bi_ods.eod_positions_hedge_fx_stg
      (PLATFORM,
       ORDER_ID,
       SNAPSHOT_TIME,
       BUSINESS_DATE,
       REPORTING_DATE,
       TRADING_ACCOUNT_ID,
       PRODUCT_INSTRUMENT_CODE,
       MARGIN,
       MARGIN_CURRENCY,
       MRGN_IN_TRDNG_ACCNT_PRMRY_CCY,
       EOD_VALUE,
       EOD_VALUE_CCY,
       EOD_VALUE_IN_ACCNT_CCY,
       UNREALISED_PNL,
       UNREALISED_PNL_CCY,
       UNREALISED_PNL_IN_ACCTN_CCY,
       CNVRSN_RATE_TO_ACCNT_CCY,
       open_trade_margin_fx_rate,
       normalised_margin_rqrmnt,
       eod_price,
       HEDGE_EXECUTION_COMMISSION
      )

      SELECT
        platform,
        order_id,
        snapshot_time,
        business_date,
        reporting_date,
        trading_account_id,
        product_instrument_code,
        margin,
        margin_currency,
        mrgn_in_trdng_accnt_prmry_ccy,
        eod_value,
        eod_value_ccy,
        eod_value_in_accnt_ccy,
        unrealised_pnl,
        unrealised_pnl_ccy,
        unrealised_pnl_in_acctn_ccy,
        cnvrsn_rate_to_accnt_ccy,
        open_trade_margin_fx_rate,
        normalised_margin_rqrmnt,
        eod_price,
        HEDGE_EXECUTION_COMMISSION

      FROM
      (


                   WITH eod_core_prices AS
                   (SELECT /*+materialize*/ * FROM (SELECT business_date,
                                            quote_time,
                                            price_symbol,
                                            instrument_code,
                                            bid_price,
                                            ask_price,
                                            (bid_price + ask_price)/2 AS mid_price,
                                            'CMC-STANDARD' AS price_band,
                                            CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 3)
                                                 ELSE NULL
                                             END AS fxr_from_ccy,

                                            CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 4, 3)
                                                 ELSE NULL
                                             END AS fxr_to_ccy,
                                          ROW_NUMBER () OVER (PARTITION BY (CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 6) ELSE instrument_code END ) ORDER BY business_date desc, quote_time DESC)rnk
                                     FROM bi_ods.prices
                                     WHERE platform = 'NG'
                                       AND instrument_code NOT LIKE '%/CAR'
                                       AND business_date = p_business_date) WHERE rnk = 1),

                  EOD_HEDGE_FX AS
                   (SELECT /*+materialize*/
                           PLATFORM,
                           ORDER_ID,
                           SNAPSHOT_TIME,
                           TRADING_ACCOUNT_ID,
                           REPORTING_DATE,
                           PRODUCT_INSTRUMENT_CODE,
                           HEDGE_RISK_BUCKET,
                           MM_VALUE_DATE
                      FROM BI_ODS.EOD_OPEN_TRADES
                     WHERE business_date = p_business_date
                       AND RECORD_SOURCE = 'NG-HEDGE-FX'),

                  HEDGE_FX_TRADES AS
                   (SELECT /*+materialize*/
                           TRADING_ACCOUNT_ID,
                           REPORTING_DATE,
                           PRODUCT_INSTRUMENT_CODE,
                           HDG_RISK_BUCKET,
                           MM_VALUE_DATE,
                           HDG_EXECUTION_COMMISSION
                      FROM BI_ODS.TRADES
                     WHERE REPORTING_DATE = p_reporting_date
                       AND TRADING_ACCOUNT_TYPE = 'HEDGE'
                       AND RECORD_SOURCE = 'NG-HEDGE'
                       AND HDG_ASSET_CLASS = 'FOREX'),

                  HDG_EXEC_COMMISSION AS
                   (SELECT EOT.PLATFORM,
                           EOT.ORDER_ID,
                           EOT.SNAPSHOT_TIME,
                           T.TRADING_ACCOUNT_ID,
                           T.REPORTING_DATE,
                           T.PRODUCT_INSTRUMENT_CODE,
                           T.HDG_RISK_BUCKET,
                           T.MM_VALUE_DATE,
                           NVL(SUM(T.HDG_EXECUTION_COMMISSION), 0) AS HEDGE_EXECUTION_COMMISSION
                      FROM EOD_HEDGE_FX EOT
                      LEFT JOIN HEDGE_FX_TRADES T
                        ON T.TRADING_ACCOUNT_ID = EOT.TRADING_ACCOUNT_ID
                       AND T.REPORTING_DATE = EOT.REPORTING_DATE
                       AND T.PRODUCT_INSTRUMENT_CODE = EOT.PRODUCT_INSTRUMENT_CODE
                       AND T.HDG_RISK_BUCKET = EOT.HEDGE_RISK_BUCKET
                       AND T.MM_VALUE_DATE = EOT.MM_VALUE_DATE
                     GROUP BY EOT.PLATFORM,
                              EOT.ORDER_ID,
                              EOT.SNAPSHOT_TIME,
                              T.TRADING_ACCOUNT_ID,
                              T.REPORTING_DATE,
                              T.PRODUCT_INSTRUMENT_CODE,
                              T.HDG_RISK_BUCKET,
                              T.MM_VALUE_DATE)


    SELECT eod.platform,
           eod.order_id,
           eod.snapshot_time,
           eod.business_date,
           eod.reporting_date,
           eod.trading_account_id,
           eod.product_instrument_code,
           NULL AS margin,
           NULL AS margin_currency,
           NULL AS mrgn_in_trdng_accnt_prmry_ccy,
           NULL AS cnvrsn_rate_to_accnt_ccy,

           ROUND(CASE WHEN i.currency = 'USD' THEN pr.mid_price ELSE 1/pr.mid_price END * eod.quantity * -1,8)
            AS eod_value,

           eod.amount_currency
            AS eod_value_ccy,

           NULL AS eod_value_in_accnt_ccy,

           CASE WHEN eod.mm_value_date = eod.business_date THEN 0
                ELSE ROUND((((CASE WHEN i.currency = 'USD' THEN pr.mid_price ELSE 1/pr.mid_price END) * eod.quantity     ) - (eod.amount*(-1))),8)
            END AS unrealised_pnl,

           eod.amount_currency   AS unrealised_pnl_ccy,
           NULL                  AS unrealised_pnl_in_acctn_ccy,
           NULL                  AS open_trade_margin_fx_rate,
           NULL                  AS normalised_margin_rqrmnt,

           ROUND(CASE WHEN i.currency = 'USD' THEN 1/pr.mid_price ELSE pr.mid_price END,8)
            AS eod_price,

           eod.record_source,
           HEC.HEDGE_EXECUTION_COMMISSION

    FROM bi_ods.eod_open_trades eod
    JOIN eod_core_prices pr ON pr.instrument_code = eod.product_instrument_code
    JOIN bi_ods.instruments i ON i.instrument_code = eod.product_instrument_code
    JOIN HDG_EXEC_COMMISSION HEC ON
        (eod.platform = HEC.platform AND
         eod.order_id = HEC.order_id AND
         eod.snapshot_time = HEC.snapshot_time)
    WHERE eod.platform = 'NG'
    AND eod.record_source = 'NG-HEDGE-FX'
    AND eod.business_date = p_business_date
    );


     MERGE INTO eod_open_trades po
        USING
        (
          SELECT
                platform,
                order_id,
                snapshot_time,
                business_date,
                reporting_date,
                trading_account_id,
               product_instrument_code,
                margin,
                margin_currency,
                mrgn_in_trdng_accnt_prmry_ccy,
                cnvrsn_rate_to_accnt_ccy,
                eod_value,
                eod_value_ccy,
                eod_value_in_accnt_ccy,
                unrealised_pnl,
                unrealised_pnl_ccy,
                unrealised_pnl_in_acctn_ccy,
                open_trade_margin_fx_rate,
                normalised_margin_rqrmnt,
                eod_price,
                HEDGE_EXECUTION_COMMISSION
          FROM
               BI_ODS.eod_positions_hedge_fx_stg
        ) mq
        ON
          (
          po.platform = mq.platform AND
          po.order_id = mq.order_id AND
          po.snapshot_time = mq.snapshot_time)
        WHEN MATCHED THEN
        UPDATE SET
               po.margin = mq.margin,
               po.margin_currency = mq.margin_currency,
               po.mrgn_in_trdng_accnt_prmry_ccy = mq.mrgn_in_trdng_accnt_prmry_ccy,
               po.open_trade_margin_fx_rate = mq.open_trade_margin_fx_rate,
               po.eod_value=mq.eod_value,
               po.eod_value_ccy=mq.eod_value_ccy,
               po.eod_value_in_accnt_ccy=mq.eod_value_in_accnt_ccy,
               po.unrealised_pnl=mq.unrealised_pnl,
               po.unrealised_pnl_ccy=mq.unrealised_pnl_ccy,
               po.unrealised_pnl_in_accnt_ccy=mq.unrealised_pnl_in_acctn_ccy,
               po.cnvrsn_rate_to_accnt_ccy=mq.cnvrsn_rate_to_accnt_ccy,
               po.normalised_margin_rqrmnt = CASE WHEN platform='NG' THEN mq.normalised_margin_rqrmnt
                                             ELSE
                                             po.normalised_margin_rqrmnt
                                             END,
               po.eod_price = CASE WHEN platform='NG' THEN mq.eod_price
                                            ELSE
                                             po.eod_price
                                             END,
               po.HEDGE_EXECUTION_COMMISSION = mq.HEDGE_EXECUTION_COMMISSION;

  END;

  -- ===================================================================================
  -- upd_eod_open_trades_hedge_cr
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the upnl for Hedge Crypto
  --     in bi_ods.eod_open_trades table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_OPEN_TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_eod_open_trades_hedge_cr (p_business_date    IN DATE,
                                           p_reporting_date   IN DATE,
                                           p_platform VARCHAR2,
                                           p_record_source VARCHAR2
                                          )

  IS
  BEGIN

     MERGE INTO eod_open_trades po
        USING
        (

         WITH
         eod_core_prices AS
         (SELECT /*+materialize*/
                 *
            FROM (SELECT business_date,
                         quote_time,
                         price_symbol,
                         instrument_code,
                         bid_price,
                         ask_price,
                         (bid_price + ask_price)/2 AS mid_price,
                         'CMC-STANDARD' AS price_band,
                         CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 3)
                              ELSE NULL
                          END AS fxr_from_ccy,

                         CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 4, 3)
                              ELSE NULL
                          END AS fxr_to_ccy,
                         ROW_NUMBER () OVER (PARTITION BY (CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 6) ELSE instrument_code END ) ORDER BY business_date desc, quote_time DESC)rnk
                    FROM bi_ods.prices
                   WHERE platform = 'NG'
                     AND instrument_code NOT LIKE '%/CAR'
                     AND reporting_date = p_reporting_date
                     AND business_date = p_business_date
                 )
           WHERE rnk = 1)

           SELECT eod.platform,
                  eod.order_id,
                  eod.snapshot_time,
                  eod.business_date,
                  eod.reporting_date,
                  eod.trading_account_id,
                  eod.product_instrument_code,

                  CASE WHEN eod.record_source = 'NG-HEDGE-CR' AND eod.trading_account_id = 0
                         THEN ROUND(eod.quantity * pr.mid_price,8)
                       ELSE 0
                  END AS eod_value,

                  eod.amount_currency  AS eod_value_ccy,

                  CASE WHEN eod.record_source = 'NG-HEDGE-CR' AND eod.trading_account_id = 0
                         THEN round((eod.normalised_direction_mulplr * eod.quantity * pr.mid_price) + (eod.normalised_direction_mulplr * ABS(eod.amount) * -1),8)
                       ELSE 0
                  END AS unrealised_pnl,

                  eod.amount_currency   AS unrealised_pnl_ccy,

                  CASE WHEN eod.record_source = 'NG-HEDGE-CR' AND eod.trading_account_id = 0
                         THEN pr.mid_price
                       ELSE 0
                  END AS eod_price,

                  eod.record_source

           FROM bi_ods.eod_open_trades eod
           JOIN eod_core_prices pr
             ON pr.instrument_code = eod.product_instrument_code
            AND pr.business_date = eod.business_date
           JOIN bi_ods.instruments i
             ON i.instrument_code = eod.product_instrument_code
          WHERE eod.platform = 'NG'
            AND eod.record_source = 'NG-HEDGE-CR'
            AND eod.reporting_date = p_reporting_date
            AND eod.business_date = p_business_date
        ) cr
        ON
          (
          po.platform = cr.platform AND
          po.order_id = cr.order_id AND
          po.snapshot_time = cr.snapshot_time)
        WHEN MATCHED THEN
        UPDATE SET
               po.eod_value=cr.eod_value,
               po.eod_value_ccy=cr.eod_value_ccy,
               po.unrealised_pnl=cr.unrealised_pnl,
               po.unrealised_pnl_ccy=cr.unrealised_pnl_ccy,
               po.eod_price = cr.eod_price;

  END;

  -- ===================================================================================
  -- upd_eod_open_trades_hedge_fxfd
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the upnl for Hedge Forex Forwards
  --     in bi_ods.eod_open_trades table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_OPEN_TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_eod_open_trades_hedge_fxfd (p_business_date    IN DATE,
                                             p_reporting_date   IN DATE,
                                             p_platform         IN VARCHAR2,
                                             p_record_source    IN VARCHAR2
                                            )
  IS
  BEGIN
     MERGE INTO eod_open_trades po
          USING (SELECT eod.platform,
                        eod.order_id,
                        eod.snapshot_time,
                        eod.business_date,
                        eod.reporting_date,
                        eod.trading_account_id,
                        eod.product_instrument_code,
                        NVL(ROUND(eod.quantity * eod.eod_price,8),0) AS eod_value,
                        eod.amount_currency  AS eod_value_ccy,
                        NVL(ROUND((eod.normalised_direction_mulplr * eod.quantity * eod.eod_price) + (eod.normalised_direction_mulplr * ABS(eod.amount) * -1),8),0) AS unrealised_pnl,
                        eod.amount_currency   AS unrealised_pnl_ccy,
                        eod.eod_price,
                        eod.record_source
                   FROM bi_ods.eod_open_trades eod
                  WHERE eod.platform = 'NG'
                    AND eod.record_source = 'NG-HEDGE-FXFWD'
                    AND eod.reporting_date = p_reporting_date
                    AND eod.business_date = p_business_date
                ) upnl
             ON (po.platform      = upnl.platform AND
                 po.order_id      = upnl.order_id AND
                 po.snapshot_time = upnl.snapshot_time)
        WHEN MATCHED THEN
        UPDATE SET
               po.eod_value          = upnl.eod_value,
               po.eod_value_ccy      = upnl.eod_value_ccy,
               po.unrealised_pnl     = upnl.unrealised_pnl,
               po.unrealised_pnl_ccy = upnl.unrealised_pnl_ccy;
  END;
  
  -- ===================================================================================
  -- upd_eod_open_trades
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE upd_eod_open_trades (p_business_date    IN DATE,
                                 p_reporting_date   IN DATE,
                                 p_platform         VARCHAR2,
                                 p_record_source    VARCHAR2)

  IS
  PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN

  -- eod_positions_unrpnl_stg is a temporary staging table created to calculate values for margin, unrpnl, eod_value, etc.
  EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_positions_unrpnl_stg';
  EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_pos_pricebanding_stg';
  EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_pos_core_prices_stg';
  EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_pos_banded_prices_stg';
  EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_pos_banded_revals_stg';

   INSERT INTO bi_ods.eod_pos_pricebanding_stg
       (SELECT i.instrument_code,
               ps.price_schema_name,
               ps.default_band,
               nvl(nvl(psi1.price_band, psi2.price_band),psi3.price_band) price_band
          FROM bi_ods.instruments i
          JOIN bi_ods.price_schemas ps
            ON ps.is_deleted = 'NO'
     LEFT JOIN bi_ods.price_schema_items psi1
            ON ps.price_schema_name   = psi1.price_schema_name
           AND psi1.override_type     = 'INSTRUMENT'
           AND CASE WHEN i.financial_instrument_type = 'CMCForwardExpiry'
                         THEN i.contract_code
                    ELSE i.instrument_code
               END = psi1.code
     LEFT JOIN bi_ods.price_schema_items psi2
            ON ps.price_schema_name   = psi2.price_schema_name
           AND psi2.override_type     = 'MARKET'
           AND i.market_code          = psi2.code
     LEFT JOIN bi_ods.price_schema_items psi3
            ON ps.price_schema_name   = psi3.price_schema_name
           AND psi3.override_type     = 'ASSET_CLASS'
           AND i.instrument_type_code = psi3.code
         WHERE i.instrument_code IN (SELECT DISTINCT o.product_instrument_code
                                       FROM bi_ods.eod_open_trades o
                                      WHERE o.reporting_date = p_reporting_date
                                        AND o.business_date  = p_business_date));

   COMMIT;

   INSERT INTO bi_ods.eod_pos_core_prices_stg
       (SELECT *
          FROM
            (SELECT business_date,
                    quote_time,
                    price_symbol,
                    instrument_code,
                    bid_price,
                    ask_price,
                    (bid_price + ask_price)/2 AS mid_price,
                    'CMC-STANDARD'            AS price_band,
                    CASE
                      WHEN substr(instrument_code, -4, 4 ) = '/FXR'
                      THEN substr(price_symbol, 1, 3)
                      ELSE NULL
                    END AS fxr_from_ccy,
                    CASE
                      WHEN substr(instrument_code, -4, 4 ) = '/FXR'
                      THEN substr(price_symbol, 4, 3)
                      ELSE NULL
                    END AS fxr_to_ccy,
                    row_number () OVER (PARTITION BY (
                    CASE
                      WHEN substr(instrument_code, -4, 4 ) = '/FXR'
                      THEN substr(price_symbol, 1, 6)
                      ELSE instrument_code
                    END ) ORDER BY business_date DESC, quote_time DESC)rnk
            FROM bi_ods.prices ph
            WHERE platform = 'NG'
            AND instrument_code NOT LIKE '%/CAR'
            AND business_date = p_business_date
            AND reporting_date = p_reporting_date
            )
          WHERE rnk = 1
          );

   COMMIT;

   INSERT INTO bi_ods.eod_pos_banded_prices_stg
   (SELECT * FROM (WITH eod_banded_price_offets AS
          (SELECT pc.business_date,
                pc.instrument_code,
                'BAND'
                ||to_char(pa.bucket)                 AS price_band,
                (pc.bid_price + pb.offset_bid_price) AS bid_price,
                (pc.ask_price + pa.offset_ask_price) AS ask_price,
                NULL mid_price,
                NULL AS fxr_from_ccy,
                NULL AS fxr_to_ccy
              FROM bi_ods.eod_pos_core_prices_stg pc
              JOIN bi_ods.price_ask_offsets pa
              ON pa.instrument_code = pc.instrument_code
              AND pa.business_date  = p_business_date
              AND pa.reporting_date = p_reporting_date
              AND pa.platform       = 'NG'
              AND pa.price_symbol = pc.price_symbol
              LEFT JOIN bi_ods.price_bid_offsets pb
              ON pb.instrument_code = pc.instrument_code
              AND pb.business_date  = p_business_date
              AND pb.reporting_date = p_reporting_date
              AND pb.platform       = 'NG'
              AND pb.bucket       = pa.bucket
              AND pb.price_symbol = pc.price_symbol
              )
          SELECT business_date,
                 instrument_code,
                 price_band,
                 bid_price,
                 ask_price,
                 mid_price,
                 fxr_from_ccy,
                 fxr_to_ccy
            FROM eod_banded_price_offets ebp
           WHERE ebp.instrument_code NOT LIKE '%/FXR'
             AND ebp.instrument_code NOT LIKE '%/CAR'
          UNION ALL
          SELECT business_date,
                 instrument_code,
                 price_band,
                 bid_price,
                 ask_price,
                 mid_price,
                 fxr_from_ccy,
                 fxr_to_ccy
            FROM eod_pos_core_prices_stg ecp
          ));

  COMMIT;

   INSERT INTO bi_ods.eod_pos_banded_revals_stg
       (SELECT pr.business_date, 
               NVL(i.pair_currency,SUBSTR(pr.price_symbol,1,3)) AS from_ccy, 
               NVL(i.currency,SUBSTR(pr.price_symbol,4,3)) AS to_ccy,                         
               CASE WHEN bands.band = 'CMC-Standard' THEN bands.band ELSE 'Band'||bands.band END AS band, 
               NVL(pr.bid_price + pbo.offset_bid_price,pr.bid_price) AS bid_price,
               ((NVL(pr.bid_price + pbo.offset_bid_price,pr.bid_price) + NVL(pr.ask_price + pao.offset_ask_price,pr.ask_price)) / 2) AS mid_price,
               NVL(pr.ask_price + pao.offset_ask_price,pr.ask_price) AS ask_price
          FROM bi_ods.prices pr
          JOIN (  SELECT to_char(ROWNUM) AS band, ROWNUM AS band_sort_order FROM dual CONNECT BY ROWNUM <= 10
                   UNION ALL
                  SELECT 'CMC-Standard'  AS band, 0 AS band_sort_order FROM dual
               ) bands 
            ON 1=1
     LEFT JOIN bi_ods.instruments i
            ON SUBSTR(pr.instrument_code,1,INSTR(pr.instrument_code,'/')-1) = i.instrument_code
     LEFT JOIN bi_ods.price_bid_offsets pbo
            ON pr.reporting_date = pbo.reporting_date
           AND pr.business_date = pbo.business_date
           AND pr.instrument_code = pbo.instrument_code
           AND bands.band = to_char(pbo.bucket)
     LEFT JOIN bi_ods.price_ask_offsets pao
            ON pr.reporting_date = pao.reporting_date
           AND pr.business_date = pao.business_date
           AND pr.instrument_code = pao.instrument_code     
           AND bands.band = to_char(pao.bucket)       
         WHERE pr.instrument_code LIKE '%/FXR'
           AND pr.reporting_date = p_reporting_date 
           AND pr.business_date = p_business_date);

   COMMIT;
   
    INSERT INTO bi_ods.eod_positions_unrpnl_stg
      (platform,
       order_id,
       snapshot_time,
       business_date,
       reporting_date,
       trading_account_id,
       product_instrument_code,
       margin,
       margin_currency,
       mrgn_in_trdng_accnt_prmry_ccy,
       eod_value,
       eod_value_ccy,
       eod_value_in_accnt_ccy,
       unrealised_pnl,
       unrealised_pnl_ccy,
       unrealised_pnl_in_acctn_ccy,
       cnvrsn_rate_to_accnt_ccy,
       open_trade_margin_fx_rate,
       normalised_margin_rqrmnt,
       eod_price)
      SELECT
        platform,
        order_id,
        snapshot_time,
        business_date,
        reporting_date,
        trading_account_id,
        product_instrument_code,
        margin,
        margin_currency,
        mrgn_in_trdng_accnt_prmry_ccy,
        eod_value,
        eod_value_ccy,
        eod_value_in_accnt_ccy,
        unrealised_pnl,
        unrealised_pnl_ccy,
        unrealised_pnl_in_acctn_ccy,
        cnvrsn_rate_to_accnt_ccy,
        open_trade_margin_fx_rate,
        normalised_margin_rqrmnt,
        eod_price
      FROM
    (WITH
    prices_binaries_eod AS (
    SELECT *
      FROM (SELECT pb.*,
                   RANK() OVER (PARTITION BY price_symbol, is_eod, business_date, reporting_date, instrument_code, type_name, tenor_name ORDER BY TIME DESC) AS rnk
              FROM bi_ods.prices_binaries pb
             WHERE pb.reporting_date = p_reporting_date
               AND pb.business_date = p_business_date
               AND is_eod = 'YES')
     WHERE rnk = 1
    ),
    pb_strike_prices_eod AS (
    SELECT pbsp.*
      FROM bi_ods.prices_binary_strike_prices pbsp
           JOIN prices_binaries_eod pb
             ON pb.subject = pbsp.subject
                AND pb.is_eod = pbsp.is_eod
                AND pb.effective_start_timestamp = pbsp.effective_start_timestamp
                AND pb.price_symbol = pbsp.price_symbol
                AND pb.instrument_code = pbsp.instrument_code
                AND pb.reporting_date = pbsp.reporting_date
                AND pb.business_date = pbsp.business_date
    ),
    eod_binary_prices AS (
    SELECT eod.business_date,
           eod.product_instrument_code AS instrument_code,
           eod.binary_type             AS type_name,
           eod.tenor                   AS tenor_name,
           eod.strike_price,
           eod.range_spread_factor/100 AS range_spread_factor,
           CASE
                WHEN eod.strike_price = p1.strike_price
                     THEN p1.option_bid_price
                WHEN eod.strike_price < eod.min_strike_price
                     THEN p2.option_bid_price
                WHEN eod.strike_price > eod.max_strike_price
                     THEN p3.option_bid_price
                ELSE NULL
           END
           AS option_bid_price,
           CASE
                WHEN eod.strike_price = p1.strike_price
                     THEN p1.option_ask_price
                WHEN eod.strike_price < eod.min_strike_price
                     THEN p2.option_ask_price
                WHEN eod.strike_price > eod.max_strike_price
                     THEN p3.option_ask_price
                ELSE NULL
           END
           AS option_ask_price
      FROM (
              SELECT eod.business_date,
                     eod.platform,
                     eod.product_instrument_code,
                     eod.strike_price,
                     eod.binary_type,
                     eod.tenor,
                     pb.range_spread_factor,
                     MIN(p.strike_price) AS min_strike_price,
                     MAX(p.strike_price) AS max_strike_price
                FROM (
                        SELECT e1.business_date,
                               e1.platform,
                               e1.product_instrument_code,
                               e1.strike_price,
                               e1.binary_type,
                               e1.tenor
                          FROM bi_ods.eod_open_trades e1
                         WHERE e1.product_wrapper_code IN ('X-MNRM','X-MNRN')
                               AND e1.strike_price IS NOT NULL
                               AND e1.business_date = p_business_date
                               AND e1.reporting_date = p_reporting_date
                         UNION
                        SELECT e2.business_date,
                               e2.platform,
                               e2.product_instrument_code,
                               e2.strike_price_additional AS strike_price,
                               e2.binary_type,
                               e2.tenor
                          FROM bi_ods.eod_open_trades e2
                         WHERE e2.product_wrapper_code  IN ('X-MNRM','X-MNRN')
                               AND e2.strike_price_additional IS NOT NULL
                               AND e2.business_date = p_business_date
                               AND e2.reporting_date = p_reporting_date
                     ) eod
                LEFT JOIN prices_binaries_eod pb
                       ON pb.instrument_code = eod.product_instrument_code
                          AND pb.type_name = (
                                               CASE
                                                    WHEN eod.binary_type LIKE 'ONETOUCH%'
                                                         THEN 'ONETOUCH'
                                                    WHEN eod.binary_type = 'RANGE'
                                                         THEN 'LADDER'
                                                    ELSE eod.binary_type
                                               END
                                             )
                          AND pb.tenor_name = eod.tenor
                LEFT JOIN pb_strike_prices_eod p
                           ON p.business_date    = p_business_date
                          AND p.reporting_date   = p_reporting_date
                          AND p.instrument_code = eod.product_instrument_code
                          AND p.type_name = (
                                              CASE
                                                   WHEN eod.binary_type LIKE 'ONETOUCH%'
                                                        THEN 'ONETOUCH'
                                                   WHEN eod.binary_type = 'RANGE'
                                                        THEN 'LADDER'
                                                   ELSE eod.binary_type
                                              END
                                            )
                          AND p.tenor_name = eod.tenor
                 GROUP BY eod.business_date,
                          eod.platform,
                          eod.product_instrument_code,
                          eod.strike_price,
                          eod.binary_type,
                          eod.tenor,
                          pb.range_spread_factor
           ) eod
      LEFT JOIN pb_strike_prices_eod p1
                 ON p1.business_date    = p_business_date
                AND p1.reporting_date  = p_reporting_date
                AND p1.instrument_code = eod.product_instrument_code
                AND p1.type_name = (
                                     CASE
                                          WHEN eod.binary_type LIKE 'ONETOUCH%'
                                               THEN 'ONETOUCH'
                                          WHEN eod.binary_type = 'RANGE'
                                               THEN 'LADDER'
                                          ELSE eod.binary_type
                                     END
                                   )
                AND p1.tenor_name = eod.tenor
                AND p1.strike_price = eod.strike_price
      LEFT JOIN pb_strike_prices_eod p2
                           ON p2.business_date    = p_business_date
                          AND p2.reporting_date  = p_reporting_date
                          AND p2.instrument_code = eod.product_instrument_code
                AND p2.type_name = (
                                     CASE
                                          WHEN eod.binary_type LIKE 'ONETOUCH%'
                                               THEN 'ONETOUCH'
                                          WHEN eod.binary_type = 'RANGE'
                                               THEN 'LADDER'
                                          ELSE eod.binary_type
                                     END
                                   )
                AND p2.tenor_name = eod.tenor
                AND p2.strike_price != eod.strike_price
                AND p2.strike_price  = eod.min_strike_price
      LEFT JOIN pb_strike_prices_eod p3
                           ON p3.business_date    = p_business_date
                          AND p3.reporting_date  = p_reporting_date
                          AND p3.instrument_code = eod.product_instrument_code
                AND p3.type_name = (
                                     CASE
                                          WHEN eod.binary_type LIKE 'ONETOUCH%'
                                               THEN 'ONETOUCH'
                                          WHEN eod.binary_type = 'RANGE'
                                               THEN 'LADDER'
                                          ELSE eod.binary_type
                                     END
                                   )
                AND p3.tenor_name = eod.tenor
                AND p3.strike_price != eod.strike_price
                AND p3.strike_price  = eod.max_strike_price
        )
          SELECT po.platform,
            po.order_id,
            po.snapshot_time,
            po.business_date,
            po.reporting_date,
            po.trading_account_id,
            po.product_instrument_code,
            --1.1 Margin
            NULL AS margin,
            --1.2 Margin Currency
            NULL margin_currency,
            --1.3 Margin in Trading Account Primary CCY
            NULL mrgn_in_trdng_accnt_prmry_ccy,
            --1.4 Margin FX Reval Rate
            NULL open_trade_margin_fx_rate,
            --1.5 Average Open Trade Margin
            NULL normalised_margin_rqrmnt,
            --2.1 EOD Value
            nvl(CASE
              WHEN po.product_wrapper_code IN ('X-MNRM','X-MNRN')
              THEN
                -- EOD Value for Binaries
                po.amount +
                CASE
                    -- Binary Options uPnL
                  WHEN upper(po.binary_type) IN ('LADDER','UPDOWN','ONETOUCH','ONETOUCHUP','ONETOUCHDOWN')
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round((nbp.option_bid_price * po.quantity) - (po.open_trade_price * po.quantity),8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round((po.open_trade_price * po.quantity) - (nbp.option_ask_price * po.quantity) ,8)
                      ELSE NULL -- Binary 'LADDER','UPDOWN','ONETOUCHUP','ONETOUCHDOWN' direction NOT RECOGNIZED
                    END
                    -- End
                  WHEN upper(po.binary_type) = 'RANGE'
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round( ( round(
                        -- Binary Range BidPrice
                        CASE
                          WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                          THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                          ELSE
                            CASE
                              WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                              THEN 0
                              ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                            END
                        END ,1) * po.quantity ) - (po.open_trade_price * po.quantity) ,8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round( (po.open_trade_price * po.quantity) - ( round(
                        -- Binary Range AskPrice
                        CASE
                          WHEN (rbp_down.option_ask_price - rbp_up.option_bid_price) = 100
                          THEN 100
                          ELSE round(
                            -- Binary Range BidPrice
                            CASE
                              WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                              THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                              ELSE
                                CASE
                                  WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                                  THEN 0
                                  ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                                END
                            END ,1) + ( (rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price) ) * rbp_down.range_spread_factor
                        END ,1)     * po.quantity ) ,8)
                      ELSE NULL -- Binary RANGE type direction NOT RECOGNIZED
                    END
                  ELSE NULL -- type_name is NOT RECOGNIZED
                END
              WHEN po.product_wrapper_code = 'X-QOQH' THEN --BER-2850
                round ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price - po.strike_price --pr.bid_price
                  WHEN po.direction = 'SELL'
                  THEN po.strike_price - pr.ask_price--pr.ask_price
                  ELSE NULL
                END ) ,8)

              ELSE
                round ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price
                  WHEN po.direction = 'SELL'
                  THEN pr.ask_price
                  ELSE NULL
                END ) ,8)
            END, po.NORMALISED_OPEN_VAL_TRAD_CCY) AS eod_value,--Changed For BER2713
            --2.2 EOD Value CCY
            CASE
              WHEN po.product_wrapper_code = 'X-A'
              THEN po.product_currency
              WHEN po.product_wrapper_code = 'A-EOVH'
              THEN po.trading_accnt_primary_ccy
              WHEN po.product_wrapper_code IN ('X-MNRM','X-MNRN')
              THEN po.trading_accnt_primary_ccy
              WHEN po.product_wrapper_code = 'X-QOQH'--BER-2850
              THEN po.trading_accnt_primary_ccy
              ELSE NULL
            END AS eod_value_ccy,
            --2.3 EOD Value in Account CCY
            nvl(CASE
              WHEN po.product_wrapper_code IN ('X-MNRM','X-MNRN')
              THEN
                -- EOD Value for Binaries
                po.amount +
                CASE
                    -- Binary Options uPnL
                  WHEN upper(po.binary_type) IN ('LADDER','UPDOWN','ONETOUCH','ONETOUCHUP','ONETOUCHDOWN')
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round((nbp.option_bid_price * po.quantity) - (po.open_trade_price * po.quantity),8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round((po.open_trade_price * po.quantity) - (nbp.option_ask_price * po.quantity) ,8)
                      ELSE NULL -- Binary 'LADDER','UPDOWN','ONETOUCHUP','ONETOUCHDOWN' direction NOT RECOGNIZED
                    END
                    -- End
                  WHEN upper(po.binary_type) = 'RANGE'
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round( ( round(
                        -- Binary Range BidPrice
                        CASE
                          WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                          THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                          ELSE
                            CASE
                              WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                              THEN 0
                              ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                            END
                        END ,1) * po.quantity ) - (po.open_trade_price * po.quantity) ,8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round( (po.open_trade_price * po.quantity) - ( round(
                        -- Binary Range AskPrice
                        CASE
                          WHEN (rbp_down.option_ask_price - rbp_up.option_bid_price) = 100
                          THEN 100
                          ELSE round(
                            -- Binary Range BidPrice
                            CASE
                              WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                              THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                              ELSE
                                CASE
                                  WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                                  THEN 0
                                  ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                                END
                            END ,1) + ( (rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price) ) * rbp_down.range_spread_factor
                        END ,1)     * po.quantity ) ,8)
                      ELSE NULL -- Binary RANGE type direction NOT RECOGNIZED
                    END
                  ELSE NULL -- type_name is NOT RECOGNIZED
                END
              WHEN po.product_wrapper_code = 'X-QOQH' THEN--BER-2850
                round ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price - po.strike_price
                  WHEN po.direction = 'SELL'
                  THEN po.strike_price - pr.ask_price
                  ELSE NULL
                END ),8)
              ELSE
                round ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price
                  WHEN po.direction = 'SELL'
                  THEN pr.ask_price
                  ELSE NULL
                END ) * (--Reval into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
                CASE
                  WHEN po.product_wrapper_code = 'X-A'
                  AND (po.product_currency    != po.trading_accnt_primary_ccy)
                  THEN reval.ask_price
                  ELSE 1
                END)
                ,8)
            END,NORMALISED_OPEN_VAL_TRAD_CCY) AS eod_value_in_accnt_ccy,--Changed For BER2713
            --2.4 Unrealised PNL
            nvl(CASE
              WHEN nvl(po.binary_type,'NONE')<>'NONE'
              THEN
                CASE
                    -- Binary Options uPnL
                  WHEN upper(po.binary_type) IN ('LADDER','UPDOWN','ONETOUCH','ONETOUCHUP','ONETOUCHDOWN')
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round((nbp.option_bid_price * po.quantity) - (po.open_trade_price * po.quantity),8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round((po.open_trade_price * po.quantity) - (nbp.option_ask_price * po.quantity) ,8)
                      ELSE NULL -- Binary 'LADDER','UPDOWN','ONETOUCHUP','ONETOUCHDOWN' direction NOT RECOGNIZED
                    END
                    -- End
                  WHEN upper(po.binary_type) = 'RANGE'
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round( ( round(
                        -- Binary Range BidPrice
                        CASE
                          WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                          THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                          ELSE
                            CASE
                              WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                              THEN 0
                              ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                            END
                        END ,1) * po.quantity ) - (po.open_trade_price * po.quantity) ,8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round( (po.open_trade_price * po.quantity) - ( round(
                        -- Binary Range AskPrice
                        CASE
                          WHEN (rbp_down.option_ask_price - rbp_up.option_bid_price) = 100
                          THEN 100
                          ELSE round(
                            -- Binary Range BidPrice
                            CASE
                              WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                              THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                              ELSE
                                CASE
                                  WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                                  THEN 0
                                  ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                                END
                            END ,1) + ( (rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price) ) * rbp_down.range_spread_factor
                        END ,1)     * po.quantity ) ,8)
                      ELSE NULL -- Binary RANGE type direction NOT RECOGNIZED
                    END
                  ELSE NULL -- type_name is NOT RECOGNIZED
                END
              WHEN po.product_wrapper_code = 'X-QOQH' THEN--BER-2850
                -- normal uPnl
                round ( ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price - po.strike_price
                  WHEN po.direction = 'SELL'
                  THEN po.strike_price - pr.ask_price
                  ELSE NULL
                END ) ) + (po.normalised_open_val_trad_ccy * (-1)) ,8)
              ELSE
                round ( ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price
                  WHEN po.direction = 'SELL'
                  THEN pr.ask_price
                  ELSE NULL
                END ) * po.normalised_direction_mulplr ) + ( po.normalised_open_val_trad_ccy* po.normalised_direction_mulplr * (-1) ) ,8)
            END,0) AS unrealised_pnl,--Changed For BER2713
            --2.5 Unrealised PNL CCY
            CASE
              WHEN po.product_wrapper_code = 'X-A'
              THEN po.product_currency -- CFD
              WHEN po.product_wrapper_code IN ('X-MNRM','X-MNRN')
              THEN po.trading_accnt_primary_ccy -- Binary CFD,Binary SB
              WHEN po.product_wrapper_code = 'A-EOVH'
              THEN po.trading_accnt_primary_ccy -- SB
              WHEN po.product_wrapper_code = 'X-QOQH'--BER-2850
              THEN po.trading_accnt_primary_ccy --KNOCKOUTS
            END AS unrealised_pnl_ccy,
            --2.6 Unrealised PNL In Account CCY
            nvl(CASE
              WHEN nvl(po.binary_type,'NONE')<>'NONE'
              THEN
                CASE
                    -- Binary Options uPnL
                  WHEN upper(po.binary_type) IN ('LADDER','UPDOWN','ONETOUCH','ONETOUCHUP','ONETOUCHDOWN')
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round((nbp.option_bid_price * po.quantity)-(po.open_trade_price * po.quantity),8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round((po.open_trade_price * po.quantity) - (nbp.option_ask_price * po.quantity),8)
                      ELSE NULL -- Binary 'LADDER','UPDOWN','ONETOUCHUP','ONETOUCHDOWN' direction NOT RECOGNIZED
                    END
                    -- End
                  WHEN upper(po.binary_type) = 'RANGE'
                  THEN
                    -- BUY formula
                    CASE
                      WHEN po.direction = 'BUY'
                      THEN round( ( round(
                        -- Binary Range BidPrice
                        CASE
                          WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                          THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                          ELSE
                            CASE
                              WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                              THEN 0
                              ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                            END
                        END ,1) * po.quantity ) - (po.open_trade_price * po.quantity) ,8)
                        -- SELL formula
                      WHEN po.direction = 'SELL'
                      THEN round( (po.open_trade_price * po.quantity) - ( round(
                        -- Binary Range AskPrice
                        CASE
                          WHEN (rbp_down.option_ask_price - rbp_up.option_bid_price) = 100
                          THEN 100
                          ELSE round(
                            -- Binary Range BidPrice
                            CASE
                              WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                              THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                              ELSE
                                CASE
                                  WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                                  THEN 0
                                  ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                                END
                            END ,1) + ( (rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price) ) * rbp_down.range_spread_factor
                        END ,1)     * po.quantity ) ,8)
                      ELSE NULL -- Binary RANGE type direction NOT RECOGNIZED
                    END
                  ELSE NULL -- type_name is NOT RECOGNIZED
                END
              WHEN po.product_wrapper_code = 'X-QOQH' THEN--BER-2850
                 round ( ( po.normalised_open_qty_trad_ccy * (
                 CASE
                 WHEN po.direction = 'BUY'
                 THEN pr.bid_price - po.strike_price
                 WHEN po.direction = 'SELL'
                 THEN po.strike_price - pr.ask_price
                 ELSE NULL
                 END )) + ( po.normalised_open_val_trad_ccy * (-1) ) ,8)
              ELSE
                ROUND(( ( ( po.normalised_open_qty_trad_ccy *
                  (
                    CASE
                    WHEN po.direction = 'BUY' THEN
                      pr.bid_price
                    WHEN po.direction = 'SELL' THEN
                      pr.ask_price
                    ELSE
                      NULL
                    END ) * po.normalised_direction_mulplr ) + ( po.normalised_open_val_trad_ccy* po.normalised_direction_mulplr * (-1) ) ) *
                  (
                    CASE
                    WHEN ( ( po.normalised_open_qty_trad_ccy *
                          (
                            CASE
                            WHEN po.direction = 'BUY' THEN
                              pr.bid_price
                            WHEN po.direction = 'SELL' THEN
                              pr.ask_price
                            ELSE
                              NULL
                            END ) * po.normalised_direction_mulplr ) + ( po.normalised_open_val_trad_ccy* po.normalised_direction_mulplr * (-1) ) )> 0 THEN
                      (--Reval the into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
                        CASE
                        WHEN po.product_wrapper_code = 'X-A' AND (po.product_currency != po.trading_accnt_primary_ccy) THEN
                          reval.bid_price --Reval back to account currency using BID price if Unrealised PNL is a PROFIT.
                        ELSE
                          1
                        END )
                    ELSE
                      (--Reval the into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
                        CASE
                        WHEN po.product_wrapper_code = 'X-A' AND (po.product_currency != po.trading_accnt_primary_ccy) THEN
                          reval.ask_price --Reval back to account currency using ASK price if Unrealised PNL is a LOSS.
                        ELSE
                          1
                        END )
                    END )),8)
            END,0) AS unrealised_pnl_in_acctn_ccy,--Changed For BER2713
            --7. Reval Rate to convert into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
            CASE
              WHEN ( ( po.normalised_open_qty_trad_ccy * (
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price
                  WHEN po.direction = 'SELL'
                  THEN pr.ask_price
                  ELSE NULL
                END ) * po.normalised_direction_mulplr ) + ( po.normalised_open_val_trad_ccy* po.normalised_direction_mulplr * (-1) ) )> 0
              THEN (--Reval the into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
                CASE
                  WHEN po.product_wrapper_code = 'X-A'
                  AND (po.product_currency    != po.trading_accnt_primary_ccy)
                  THEN reval.bid_price --Reval back to account currency using BID price if Unrealised PNL is a PROFIT.
                  ELSE 1
                END )
              ELSE (--Reval the into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
                CASE
                  WHEN po.product_wrapper_code = 'X-A'
                  AND (po.product_currency    != po.trading_accnt_primary_ccy)
                  THEN reval.ask_price --Reval back to account currency using ASK price if Unrealised PNL is a LOSS.
                  ELSE 1
                END )
            END AS cnvrsn_rate_to_accnt_ccy,
            CASE
              WHEN po.product_wrapper_code IN ('X-MNRM','X-MNRN')
              AND upper(po.binary_type)    IN ('LADDER','UPDOWN','ONETOUCH','ONETOUCHUP','ONETOUCHDOWN')
              THEN
                CASE
                  WHEN po.direction = 'BUY'
                  THEN nbp.option_bid_price
                  WHEN po.direction = 'SELL'
                  THEN nbp.option_ask_price
                  ELSE NULL
                END
              WHEN po.product_wrapper_code IN ('X-MNRM','X-MNRN')
              AND upper(po.binary_type)    IN ('RANGE')
              THEN
                CASE
                  WHEN po.direction = 'BUY'
                  THEN round(
                    -- Binary Range BidPrice
                    CASE
                      WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                      THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                      ELSE
                        CASE
                          WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                          THEN 0
                          ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                        END
                    END ,1)
                  WHEN po.direction = 'SELL'
                  THEN round(
                    -- Binary Range AskPrice
                    CASE
                      WHEN (rbp_down.option_ask_price - rbp_up.option_bid_price) = 100
                      THEN 100
                      ELSE round(
                        -- Binary Range BidPrice
                        CASE
                          WHEN (rbp_down.option_ask_price      - rbp_up.option_bid_price) = 100
                          THEN ( (( (rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) + ((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price)) * rbp_down.range_spread_factor ) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))* rbp_down.range_spread_factor)
                          ELSE
                            CASE
                              WHEN round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1) < 0
                              THEN 0
                              ELSE round( (((rbp_down.option_bid_price - rbp_up.option_ask_price) + (rbp_down.option_ask_price - rbp_up.option_bid_price))/2) - (((rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price))/2 * rbp_down.range_spread_factor) ,1)
                            END
                        END ,1) + ( (rbp_down.option_ask_price - rbp_up.option_bid_price) - (rbp_down.option_bid_price - rbp_up.option_ask_price) ) * rbp_down.range_spread_factor
                    END ,1)
                  ELSE NULL
                END
              WHEN po.product_wrapper_code = 'X-QOQH' THEN--BER-2850
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price - po.strike_price
                  WHEN po.direction = 'SELL'
                  THEN po.strike_price - pr.ask_price
                  ELSE NULL
                END
              ELSE
                CASE
                  WHEN po.direction = 'BUY'
                  THEN pr.bid_price
                  WHEN po.direction = 'SELL'
                  THEN pr.ask_price
                  ELSE NULL
                END
            END AS eod_price
          FROM bi_ods.eod_open_trades po
          JOIN bi_ods.instruments i
          ON i.instrument_code = po.product_instrument_code
          JOIN bi_ods.trading_accounts ad
          ON ad.trading_account_id    = po.trading_account_id
          AND ad.trading_account_type = po.trading_account_type
          AND ad.source_platform IN ('NG','SmartTrade','IRESS')
          AND ad.trading_account_type = 'CUSTOMER'
          LEFT OUTER JOIN eod_pos_pricebanding_stg pb
          ON pb.price_schema_name = price_schema_code
          AND pb.instrument_code  = i.instrument_code
          LEFT JOIN eod_pos_banded_prices_stg pr
          ON pr.instrument_code = po.product_instrument_code
          AND pr.price_band     = nvl(upper(pb.price_band),upper(pb.default_band))
          LEFT JOIN eod_binary_prices nbp
          ON nbp.instrument_code = po.product_instrument_code
          AND nbp.type_name      = po.binary_type
          AND nbp.tenor_name     = po.tenor
          AND nbp.strike_price   = po.strike_price
          LEFT JOIN eod_binary_prices rbp_up
          ON rbp_up.instrument_code = po.product_instrument_code
          AND rbp_up.type_name      = po.binary_type
          AND rbp_up.tenor_name     = po.tenor
          AND rbp_up.strike_price   = po.strike_price_additional
          LEFT JOIN eod_binary_prices rbp_down
          ON rbp_down.instrument_code = po.product_instrument_code
          AND rbp_down.type_name      = po.binary_type
          AND rbp_down.tenor_name     = po.tenor
          AND rbp_down.strike_price   = po.strike_price
          LEFT JOIN eod_pos_banded_revals_stg reval
          ON reval.from_ccy            = po.product_currency
          AND reval.to_ccy             = po.trading_accnt_primary_ccy
          AND reval.band               = NVL(ad.fxr_schema_code,'CMC-Standard')
          WHERE po.platform            = 'NG'
          AND po.business_date         = p_business_date
          AND po.reporting_date = p_reporting_date
          AND po.record_source         = 'NG'
          ORDER BY 1,2,3 );

    COMMIT;

        -- Merge data into the eod_positions table

        MERGE INTO eod_open_trades po
        USING
        (SELECT platform,
                order_id,
                snapshot_time,
                business_date,
                reporting_date,
                trading_account_id,
                product_instrument_code,
                margin,
                margin_currency,
                mrgn_in_trdng_accnt_prmry_ccy,
                cnvrsn_rate_to_accnt_ccy,
                eod_value,
                eod_value_ccy,
                eod_value_in_accnt_ccy,
                unrealised_pnl,
                unrealised_pnl_ccy,
                unrealised_pnl_in_acctn_ccy,
                open_trade_margin_fx_rate,
                normalised_margin_rqrmnt,
                eod_price
          FROM BI_ODS.eod_positions_unrpnl_stg) mq
        ON (po.platform = mq.platform AND
            po.order_id = mq.order_id AND
            po.snapshot_time = mq.snapshot_time)
        WHEN MATCHED THEN
        UPDATE SET
               po.margin = mq.margin,
               po.margin_currency = mq.margin_currency,
               po.mrgn_in_trdng_accnt_prmry_ccy = mq.mrgn_in_trdng_accnt_prmry_ccy,
               po.open_trade_margin_fx_rate = mq.open_trade_margin_fx_rate,
               po.eod_value=mq.eod_value,
               po.eod_value_ccy=mq.eod_value_ccy,
               po.eod_value_in_accnt_ccy=mq.eod_value_in_accnt_ccy,
               po.unrealised_pnl=mq.unrealised_pnl,
               po.unrealised_pnl_ccy=mq.unrealised_pnl_ccy,
               po.unrealised_pnl_in_accnt_ccy=mq.unrealised_pnl_in_acctn_ccy,
               po.cnvrsn_rate_to_accnt_ccy=mq.cnvrsn_rate_to_accnt_ccy,
               po.normalised_margin_rqrmnt = CASE WHEN platform='NG' THEN mq.normalised_margin_rqrmnt
                                             ELSE
                                             po.normalised_margin_rqrmnt
                                             END,
               po.eod_price = CASE WHEN platform='NG' THEN mq.eod_price
                                             ELSE
                                             po.eod_price
                                             END;

    COMMIT;

  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END upd_eod_open_trades;

  -- ===================================================================================
  -- upd_eod_open_trades_metatrader
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the upnl values for MT4 accounts
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_OPEN_TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE upd_eod_open_trades_metatrader (p_business_date    IN DATE,
                                            p_reporting_date   IN DATE,
                                            p_platform         VARCHAR2,
                                            p_record_source    VARCHAR2
                                            ) IS

  lv_logical_load_timestamp   TIMESTAMP(6):= systimestamp;

  BEGIN

    EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_open_trades_mt_stg';

  INSERT INTO bi_ods.eod_open_trades_mt_stg (
    SELECT * FROM (
    WITH
    mt_accounts AS
    (
      SELECT trading_account_id, trading_account_type, currency AS trading_account_currency, instrument_schema_code, closeout_schema_code, price_schema_code, fxr_schema_code
        FROM bi_ods.trading_accounts
       WHERE source_platform = 'MT4'
         AND account_type = 'MetaTrader'
         AND closeout_schema_code IN (SELECT closeout_schema_code
                                        FROM bi_ods.accnt_mon_region_notif_levls
                                       WHERE standard_margin_calc_strategy IN ('LARGERSIDEWITHOPENINGPRICE')
                                         AND unrealized_pnl_calc_strategy  IN ('METATRADER'))
    ),
    accnt_mon_lvls AS
    (
          SELECT closeout_schema_code,
                 prime_margin_buffer,
                 min_prime_margin_type,
                 min_prime_margin_value,
                 standard_margin_calc_strategy,
                 unrealized_pnl_calc_strategy
            FROM bi_ods.accnt_mon_region_notif_levls
    ),
    psmd_defaults AS
    (
          SELECT *
            FROM bi_ods.product_settings_margin_defs psmd
           WHERE psmd.instrument_schema_code = 'Default'
    ),
    psmd_non_defaults AS
    (
          SELECT *
            FROM bi_ods.product_settings_margin_defs psmd
           WHERE psmd.instrument_schema_code != 'Default'
    ),
    eot_open_trade_upnl AS
    (
          SELECT eot.order_id,
                 eot.platform,
                 eot.snapshot_time,
                 eot.trading_account_id,
                 eot.trading_account_type,
                 eot.product_instrument_code,
                 eot.product_wrapper_code,
                 eot.direction,
                 eot.quantity,
                 eot.is_inst_ccy_in_fracnal_parts,
                 eot.fractional_part_ratio,
                 eot.normalised_direction_mulplr,
                 eot.open_trade_price,

                 --OPEN_TRADE_AMOUNT_PRD_CCY:
                 eot.quantity
                 * eot.open_trade_price
                 * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                              THEN eot.fractional_part_ratio
                         ELSE 1
                    END)
                 * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                              THEN eot.product_point_multiplier
                         ELSE 1
                    END)
                 * eot.normalised_direction_mulplr
                 AS open_trade_amount_prd_ccy,

                 --EOD_PRICE:
                 (CASE WHEN eot.direction = 'BUY'
                             THEN pr.bid_price
                         WHEN eot.direction = 'SELL'
                             THEN pr.ask_price
                        ELSE NULL
                  END)
                 AS eod_price,

                 --EOD_VALUE_PRD_CCY:
                 eot.quantity
                 * (CASE WHEN eot.direction = 'BUY'
                             THEN pr.bid_price
                         WHEN eot.direction = 'SELL'
                             THEN pr.ask_price
                        ELSE NULL
                    END)
                 * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                              THEN eot.fractional_part_ratio
                         ELSE 1
                    END)
                 * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                              THEN eot.product_point_multiplier
                         ELSE 1
                    END)
                 AS
                 eod_value_prd_ccy,

                 --EOD_VALUE_IN_ACCNT_CCY:
                 eot.quantity
                 * (CASE WHEN eot.direction = 'BUY'
                             THEN pr.bid_price
                         WHEN eot.direction = 'SELL'
                             THEN pr.ask_price
                        ELSE NULL
                    END)
                 * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                              THEN eot.fractional_part_ratio
                         ELSE 1
                    END)
                 * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                              THEN eot.product_point_multiplier
                         ELSE 1
                    END)
                 /*BER-4832*/
                 --* NVL(reval.ask_price,1)
                 * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.product_currency != mta.trading_account_currency
                              THEN reval.ask_price
                         ELSE 1
                    END)
                 /*BER-4832*/
                 AS eod_value_in_accnt_ccy,

                 --EOD_UPNL_PRD_CCY:
                 (
                   (eot.quantity
                    * (CASE WHEN eot.direction = 'BUY'
                                THEN pr.bid_price
                           WHEN eot.direction = 'SELL'
                                THEN pr.ask_price
                           ELSE NULL
                       END)
                    * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                                 THEN eot.fractional_part_ratio
                            ELSE 1
                       END)
                    * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                                 THEN eot.product_point_multiplier
                            ELSE 1
                       END)
                    * eot.normalised_direction_mulplr
                   )
                   - (eot.quantity
                      * eot.open_trade_price
                      * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                                   THEN eot.fractional_part_ratio
                              ELSE 1
                         END)
                      * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                                   THEN eot.product_point_multiplier
                              ELSE 1
                         END)
                      * eot.normalised_direction_mulplr
                     )
                 )
                 AS eod_upnl_prd_ccy,

                 --EOD_UPNL_TA_CCY:
                 (
                   (eot.quantity
                    * (CASE WHEN eot.direction = 'BUY'
                                THEN pr.bid_price
                           WHEN eot.direction = 'SELL'
                                THEN pr.ask_price
                           ELSE NULL
                       END)
                    * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                                 THEN eot.fractional_part_ratio
                            ELSE 1
                       END)
                    * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                                 THEN eot.product_point_multiplier
                            ELSE 1
                       END)
                    * eot.normalised_direction_mulplr
                   )
                   - (eot.quantity
                      * eot.open_trade_price
                      * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                                   THEN eot.fractional_part_ratio
                              ELSE 1
                         END)
                      * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                                   THEN eot.product_point_multiplier
                              ELSE 1
                         END)
                      * eot.normalised_direction_mulplr
                     )
                 )
                 /*BER-4832*/
                 --* NVL(reval.ask_price,1)
                 * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.product_currency != mta.trading_account_currency
                              THEN reval.ask_price
                         ELSE 1
                    END)
                 /*BER-4832*/
                 AS
                 eod_upnl_ta_ccy,

                 --CNVRSN_RATE_TO_ACCNT_CCY:
                 /*BER-4832*/
                 --NVL(reval.ask_price,1)
                 CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.product_currency != mta.trading_account_currency
                           THEN reval.ask_price
                      ELSE 1
                 END
                 AS cnvrsn_rate_to_accnt_ccy,
                 /*BER-4832*/

                 mta.trading_account_currency,
                 mta.closeout_schema_code,
                 mta.instrument_schema_code,
                 mta.price_schema_code,
                 aml.standard_margin_calc_strategy

            FROM bi_ods.eod_open_trades eot
            JOIN mt_accounts mta
              ON eot.trading_account_id = mta.trading_account_id
             AND eot.trading_account_type = mta.trading_account_type
       LEFT JOIN accnt_mon_lvls aml
              ON mta.closeout_schema_code = aml.closeout_schema_code
       LEFT JOIN eod_pos_pricebanding_stg pb
              ON pb.price_schema_name    = mta.price_schema_code
             AND pb.instrument_code      = eot.product_instrument_code
       LEFT JOIN eod_pos_banded_prices_stg pr
              ON pr.instrument_code      = eot.product_instrument_code
             AND pr.price_band           = NVL(UPPER(pb.price_band),UPPER(pb.default_band))
       LEFT JOIN eod_pos_banded_revals_stg reval
              ON reval.from_ccy          = eot.product_currency
             AND reval.to_ccy            = mta.trading_account_currency
             AND reval.band              = NVL(mta.fxr_schema_code,'CMC-Standard')
           WHERE eot.reporting_date      = p_reporting_date
             AND eot.business_date       = p_business_date
    )
    SELECT eotu.order_id,
           eotu.platform,
           eotu.snapshot_time,
           eotu.trading_account_currency,
           eotu.eod_value_prd_ccy,
           eotu.eod_value_in_accnt_ccy,
           eotu.eod_upnl_prd_ccy,
           eotu.eod_upnl_ta_ccy,
           eotu.eod_price,
           eotu.cnvrsn_rate_to_accnt_ccy
      FROM eot_open_trade_upnl eotu
           ));

     MERGE INTO eod_open_trades po USING
          (SELECT
           order_id,
           platform,
           snapshot_time,
           trading_account_currency,
           eod_value_prd_ccy,
           eod_value_in_accnt_ccy,
           eod_upnl_prd_ccy,
           eod_upnl_ta_ccy,
           eod_price,
           cnvrsn_rate_to_accnt_ccy
      FROM eod_open_trades_mt_stg) mq
       ON (po.platform      = mq.platform
       AND po.order_id      = mq.order_id
       AND po.snapshot_time = mq.snapshot_time)
      WHEN MATCHED THEN UPDATE SET
           po.trading_accnt_primary_ccy        = mq.trading_account_currency,
           po.eod_value                        = mq.eod_value_prd_ccy,
           po.eod_value_ccy                    = po.product_currency,
           po.eod_value_in_accnt_ccy           = mq.eod_value_in_accnt_ccy,
           po.unrealised_pnl                   = mq.eod_upnl_prd_ccy,
           po.unrealised_pnl_ccy               = po.product_currency,
           po.unrealised_pnl_in_accnt_ccy      = mq.eod_upnl_ta_ccy,
           po.eod_price                        = mq.eod_price,
           po.cnvrsn_rate_to_accnt_ccy         = mq.cnvrsn_rate_to_accnt_ccy,
           po.margin                           = NULL,
           po.mrgn_in_trdng_accnt_prmry_ccy    = NULL
           ;
  END;

  -- ===================================================================================
  -- insert_eod_open_positions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     eod_open_positions[_h]
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  insert_spdbet_eod_open_trades (p_business_date             IN DATE,
                                            p_reporting_date            IN DATE,
                                            p_effective_start_timestamp IN TIMESTAMP,
                                            p_snapshot_time             IN TIMESTAMP,
                                            p_platform                  IN VARCHAR2,
                                            p_user                      IN VARCHAR2
                                            )

  IS
  lv_logical_load_timestamp   TIMESTAMP(6):= systimestamp;
  BEGIN

  INSERT INTO eod_open_trades
       (platform,
        order_id,
        snapshot_time,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        business_date,
        reporting_date,
        mm_value_date,
        trading_account_id,
        trading_account_type,
        trading_account_function,
        trading_account_codifier,
        mm_account_id,
        product_instrument_code,
        product_wrapper_code,
        mm_instrument_id,
        product_generation,
        product_point_multiplier,
        product_currency,
        product_financing_ratio_max,
        product_schema_code,
        custom_info_virt_portcode,
        direction,
        financing_ratio,
        quantity,
        quantity_currency,
        margin,
        margin_currency,
        amount,
        amount_currency,
        margin_secondary,
        margin_secondary_ccy,
        mrgn_in_trdng_accnt_prmry_ccy,
        amnt_in_trdng_accnt_prmry_ccy,
        open_trade_id,
        open_trade_price,
        open_trade_margin_fx_rate,
        open_time,
        last_modified_time,
        quantity_designator,
        trading_accnt_primary_ccy,
        open_trade_quantity_fx_rate,
        price_designator,
        is_inst_ccy_in_fracnal_parts,
        fractional_part_ratio,
        normalised_order_type,
        normalised_open_price,
        normalised_direction_mulplr,
        normalised_open_qty_trad_ccy,
        normalised_open_val_trad_ccy,
        normalised_trading_ccy,
        normalised_margin_type,
        normalised_margin_rqrmnt,
        mm_backoffice_ref,
        is_primary,
        eod_price,
        counterparty_id,
        opening_trade_amount_fx_rate,
        opening_trade_app_to_units,
        is_automatically_rolled,
        rolled_open_trade_id,
        eod_value,
        eod_value_ccy,
        eod_value_in_accnt_ccy,
        unrealised_pnl,
        unrealised_pnl_ccy,
        unrealised_pnl_in_accnt_ccy,
        cnvrsn_rate_to_accnt_ccy,
        is_mm_old_style,
        record_source,
        hedge_asset_class,
        hedge_instrument_code_external,
        hedge_expiry_month_code,
        hedge_risk_bucket,
        hedge_profitloss,
        hedge_commission,
        execution_type,
        cd_state,
        trading_scope,
        hedge_execution_commission,
        price_band
        )

       -- eod_ope_trades


              WITH accounts AS
                   (
                   SELECT /*+ MATERIALIZE */
                          trading_account_id,
                          trading_account_type,
                          account_function            as trading_account_function,
                          currency                    AS trading_account_ccy,
                          --UPPER(product_schm_cd)      AS product_schema_code,
                          --UPPER(trading_risk_schm_cd) AS trading_risk_schema_code,
                          instrument_schema_code,
                          UPPER(price_schema_code)    AS default_price_schema_code,
                          independent_margin_amount
                   FROM bi_ods.trading_accounts
                   WHERE source_platform      = 'NG'
                     AND trading_account_type = 'CUSTOMER'
                   ),
              ord AS (
              SELECT
                    p_platform                  AS platform,
                    po.order_id                 AS order_id,
                    p_snapshot_time             AS snapshot_time,
                    lv_logical_load_timestamp   AS logical_load_timestamp,
                    p_user                      AS created_by,
                    lv_logical_load_timestamp   AS create_timestamp,
                    p_user                      AS updated_by,
                    lv_logical_load_timestamp   AS update_timestamp,
                    p_effective_start_timestamp AS effective_start_timestamp,
                    p_business_date             AS business_date,
                    p_reporting_date            AS reporting_date,
                    NULL                        AS mm_value_date,
                    po.trading_account_id       AS trading_account_id,
                    po.trading_account_type     AS trading_account_type,
                    po.trading_account_function AS trading_account_function,
                    po.trading_account_codifier AS trading_account_codifier,
                    NULL                        AS mm_account_id,
                    po.product_instrument_code  AS product_instrument_code,
                    po.product_wrapper_code     AS product_wrapper_code,
                    NULL                        AS mm_instrument_id,
                    po.product_generation       AS product_generation,
                    po.product_point_multiplier AS product_point_multiplier,
                    po.product_currency         AS product_currency,
                    NULL                        AS product_financing_ratio_max,
                    po.product_schema_code      AS product_schema_code,
                    NULL                        AS custom_info_virt_portcode,
                    po.direction                AS direction,
                    po.financing_ratio          AS financing_ratio,
                    po.trade_quantity           AS quantity,
                    po.trade_quantity_currency  AS quantity_currency,
                    po.trade_amount             AS margin,
                    po.trading_account_currency AS margin_currency,
                    po.trade_amount             AS amount,
                    po.trade_amount_currency    AS amount_currency,
                    NULL                        AS margin_secondary,
                    NULL                        AS margin_secondary_ccy,
                    po.trade_amount             AS mrgn_in_trdng_accnt_prmry_ccy,
                    po.trade_amount             AS amnt_in_trdng_accnt_prmry_ccy,
                    po.trade_id                 AS open_trade_id,
                    po.trade_price              AS open_trade_price,
                    NULL                        AS open_trade_margin_fx_rate,
                    po.trade_time               AS open_time,
                    NULL                        AS last_modified_time,
                    po.quantity_designator      AS quantity_designator,
                    po.trading_account_currency AS trading_accnt_primary_ccy,
                    NULL                        AS open_trade_quantity_fx_rate,
                    po.price_designator         AS price_designator,
                    NULL                        AS is_inst_ccy_in_fracnal_parts,
                    NULL                        AS fractional_part_ratio,
                    po.order_type               AS normalised_order_type,
                    po.trade_price              AS normalised_open_price,
                    po.direction_multiplier     AS normalised_direction_mulplr,
                    po.trade_quantity           AS normalised_open_qty_trad_ccy,
                    po.trade_amount             AS normalised_open_val_trad_ccy,
                    po.trading_account_currency AS normalised_trading_ccy,
                    NULL                        AS normalised_margin_type,
                    NULL                        AS normalised_margin_rqrmnt,
                    NULL                        AS mm_backoffice_ref,
                    po.is_primary               AS is_primary,
                    reval.mid_price             AS eod_price,--pr.mid_price
                    NULL                        AS counterparty_id,
                    NULL                        AS opening_trade_amount_fx_rate,
                    NULL                        AS opening_trade_app_to_units,
                    NULL                        AS is_automatically_rolled,
                    NULL                        AS rolled_open_trade_id,
                    po.trade_amount             AS eod_value,
                    po.trading_account_currency AS eod_value_ccy,
                    po.trade_amount             AS eod_value_in_accnt_ccy,
                    NULL                        AS UNREALISED_PNL, -- field updated on 07.07.2015
                    NULL                        AS unrealised_pnl_ccy,
                    NULL                        AS UNREALISED_PNL_IN_ACCNT_CCY, -- field updated on 07.07.2015
                    NULL                        AS cnvrsn_rate_to_accnt_ccy,
                    NULL                        AS is_mm_old_style,
                    po.Record_Source            AS record_source,
                    NULL                        AS hedge_asset_class,
                    NULL                        AS hedge_instrument_code_external,
                    NULL                        AS hedge_expiry_month_code,
                    NULL                        AS hedge_risk_bucket,
                    NULL                        AS hedge_profitloss,
                    NULL                        AS hedge_commission,
                    o.Execution_Type            AS execution_type,
                    po.cd_state,
                    o.trading_scope,
                    RANK() OVER (PARTITION BY o.order_id ORDER BY o.logical_load_timestamp desc) rnk

              FROM bi_ods.trades po

              JOIN bi_ods.orders o ON po.order_id = o.order_id
                                  AND po.platform = o.platform
                                  --AND po.reporting_date = p_reporting_date

              JOIN bi_ods.instruments i ON i.instrument_code = po.product_instrument_code

              JOIN accounts ad          ON  ad.trading_account_id                     = po.trading_account_id
                                       AND  ad.trading_account_type                   = po.trading_account_type

              --LEFT
              --JOIN bi_ods.accnt_prc_ast_schm_cds acp ON acp.trading_account_id             = ad.trading_account_id
              --                                      AND UPPER(acp.trading_account_type)    = UPPER(ad.trading_account_type)
              --                                      AND UPPER(acp.asset_class)             = UPPER(i.instrument_type)

              --LEFT
              --JOIN eod_banded_prices pr      ON pr.instrument_code           = po.product_instrument_code
              --                              AND pr.price_band                = NVL(UPPER(acp.prc_ast_cls_schema_code),UPPER(ad.default_price_schema_code))
              LEFT
              JOIN bi_ods.eod_pos_core_prices_stg reval     ON  reval.fxr_from_ccy          =  po.product_currency
                                            AND  reval.fxr_to_ccy            =  po.trading_account_currency

              WHERE po.platform = 'NG'
                --AND po.business_date = p_business_date
                AND po.Record_Source = 'SPEEDBET'
                AND o.Record_Source = 'SPEEDBET'
                AND o.state IN ('OPEN','SETTLEMENTPENDING')
                AND po.effective_start_timestamp < p_effective_start_timestamp

----------------------------------------------------------- HISTORY
                UNION ALL

              SELECT
                    p_platform                  AS platform,
                    po.order_id                 AS order_id,
                    p_snapshot_time             AS snapshot_time,
                    lv_logical_load_timestamp   AS logical_load_timestamp,
                    p_user                      AS created_by,
                    lv_logical_load_timestamp   AS create_timestamp,
                    p_user                      AS updated_by,
                    lv_logical_load_timestamp   AS update_timestamp,
                    p_effective_start_timestamp AS effective_start_timestamp,
                    p_business_date             AS business_date,
                    p_reporting_date            AS reporting_date,
                    NULL                        AS mm_value_date,
                    po.trading_account_id       AS trading_account_id,
                    po.trading_account_type     AS trading_account_type,
                    po.trading_account_function AS trading_account_function,
                    po.trading_account_codifier AS trading_account_codifier,
                    NULL                        AS mm_account_id,
                    po.product_instrument_code  AS product_instrument_code,
                    po.product_wrapper_code     AS product_wrapper_code,
                    NULL                        AS mm_instrument_id,
                    po.product_generation       AS product_generation,
                    po.product_point_multiplier AS product_point_multiplier,
                    po.product_currency         AS product_currency,
                    NULL                        AS product_financing_ratio_max,
                    po.product_schema_code      AS product_schema_code,
                    NULL                        AS custom_info_virt_portcode,
                    po.direction                AS direction,
                    po.financing_ratio          AS financing_ratio,
                    po.trade_quantity           AS quantity,
                    po.trade_quantity_currency  AS quantity_currency,
                    po.trade_amount             AS margin,
                    po.trading_account_currency AS margin_currency,
                    po.trade_amount             AS amount,
                    po.trade_amount_currency    AS amount_currency,
                    NULL                        AS margin_secondary,
                    NULL                        AS margin_secondary_ccy,
                    po.trade_amount             AS mrgn_in_trdng_accnt_prmry_ccy,
                    po.trade_amount             AS amnt_in_trdng_accnt_prmry_ccy,
                    po.trade_id                 AS open_trade_id,
                    po.trade_price              AS open_trade_price,
                    NULL                        AS open_trade_margin_fx_rate,
                    po.trade_time               AS open_time,
                    NULL                        AS last_modified_time,
                    po.quantity_designator      AS quantity_designator,
                    po.trading_account_currency AS trading_accnt_primary_ccy,
                    NULL                        AS open_trade_quantity_fx_rate,
                    po.price_designator         AS price_designator,
                    NULL                        AS is_inst_ccy_in_fracnal_parts,
                    NULL                        AS fractional_part_ratio,
                    po.order_type               AS normalised_order_type,
                    po.trade_price              AS normalised_open_price,
                    po.direction_multiplier     AS normalised_direction_mulplr,
                    po.trade_quantity           AS normalised_open_qty_trad_ccy,
                    po.trade_amount             AS normalised_open_val_trad_ccy,
                    po.trading_account_currency AS normalised_trading_ccy,
                    NULL                        AS normalised_margin_type,
                    NULL                        AS normalised_margin_rqrmnt,
                    NULL                        AS mm_backoffice_ref,
                    po.is_primary               AS is_primary,
                    reval.mid_price             AS eod_price,--pr.mid_price
                    NULL                        AS counterparty_id,
                    NULL                        AS opening_trade_amount_fx_rate,
                    NULL                        AS opening_trade_app_to_units,
                    NULL                        AS is_automatically_rolled,
                    NULL                        AS rolled_open_trade_id,
                    po.trade_amount             AS eod_value,
                    po.trading_account_currency AS eod_value_ccy,
                    po.trade_amount             AS eod_value_in_accnt_ccy,
                    NULL                        AS UNREALISED_PNL, -- field updated on 07.07.2015
                    NULL                        AS unrealised_pnl_ccy,
                    NULL                        AS UNREALISED_PNL_IN_ACCNT_CCY, -- field updated on 07.07.2015
                    NULL                        AS cnvrsn_rate_to_accnt_ccy,
                    NULL                        AS is_mm_old_style,
                    po.Record_Source            AS record_source,
                    NULL                        AS hedge_asset_class,
                    NULL                        AS hedge_instrument_code_external,
                    NULL                        AS hedge_expiry_month_code,
                    NULL                        AS hedge_risk_bucket,
                    NULL                        AS hedge_profitloss,
                    NULL                        AS hedge_commission,
                    o.Execution_Type            AS execution_type,
                    po.cd_state,
                    o.trading_scope,
                    RANK() OVER (PARTITION BY o.order_id ORDER BY o.logical_load_timestamp desc) rnk

              FROM bi_ods.trades_h po

              JOIN bi_ods.orders o ON po.order_id = o.order_id
                                  AND po.platform = o.platform
                                  --AND po.reporting_date = p_reporting_date


              JOIN bi_ods.instruments i ON i.instrument_code = po.product_instrument_code

              JOIN accounts ad          ON  ad.trading_account_id                     = po.trading_account_id
                                       AND  ad.trading_account_type                   = po.trading_account_type

              --LEFT
              --JOIN bi_ods.accnt_prc_ast_schm_cds acp ON acp.trading_account_id             = ad.trading_account_id
              --                                      AND UPPER(acp.trading_account_type)    = UPPER(ad.trading_account_type)
              --                                      AND UPPER(acp.asset_class)             = UPPER(i.instrument_type)

              --LEFT
              --JOIN eod_banded_prices pr      ON pr.instrument_code           = po.product_instrument_code
              --                              AND pr.price_band                = NVL(UPPER(acp.prc_ast_cls_schema_code),UPPER(ad.default_price_schema_code))
              LEFT
              JOIN bi_ods.eod_pos_core_prices_stg reval     ON  reval.fxr_from_ccy          =  po.product_currency
                                            AND  reval.fxr_to_ccy            =  po.trading_account_currency

              WHERE po.platform = 'NG'
               --AND po.business_date = p_business_date
                AND po.Record_Source = 'SPEEDBET'
                AND o.Record_Source = 'SPEEDBET'
                AND po.cd_state IN ('OPEN','SETTLEMENTPENDING')
                AND po.effective_start_timestamp <  p_effective_start_timestamp
                AND po.effective_end_timestamp   >= p_effective_start_timestamp)

                SELECT platform,
                    order_id,
                    snapshot_time,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    effective_start_timestamp,
                    business_date,
                    reporting_date,
                    mm_value_date,
                    trading_account_id,
                    trading_account_type,
                    trading_account_function,
                    trading_account_codifier,
                    mm_account_id,
                    product_instrument_code,
                    product_wrapper_code,
                    mm_instrument_id,
                    product_generation,
                    product_point_multiplier,
                    product_currency,
                    product_financing_ratio_max,
                    product_schema_code,
                    custom_info_virt_portcode,
                    direction,
                    financing_ratio,
                    quantity,
                    quantity_currency,
                    margin,
                    margin_currency,
                    amount,
                    amount_currency,
                    margin_secondary,
                    margin_secondary_ccy,
                    mrgn_in_trdng_accnt_prmry_ccy,
                    amnt_in_trdng_accnt_prmry_ccy,
                    open_trade_id,
                    open_trade_price,
                    open_trade_margin_fx_rate,
                    open_time,
                    last_modified_time,
                    quantity_designator,
                    trading_accnt_primary_ccy,
                    open_trade_quantity_fx_rate,
                    price_designator,
                    is_inst_ccy_in_fracnal_parts,
                    fractional_part_ratio,
                    normalised_order_type,
                    normalised_open_price,
                    normalised_direction_mulplr,
                    normalised_open_qty_trad_ccy,
                    normalised_open_val_trad_ccy,
                    normalised_trading_ccy,
                    normalised_margin_type,
                    normalised_margin_rqrmnt,
                    mm_backoffice_ref,
                    is_primary,
                    eod_price,
                    counterparty_id,
                    opening_trade_amount_fx_rate,
                    opening_trade_app_to_units,
                    is_automatically_rolled,
                    rolled_open_trade_id,
                    eod_value,
                    eod_value_ccy,
                    eod_value_in_accnt_ccy,
                    nvl(UNREALISED_PNL,0),--Updated FOR BER-2713
                    nvl(unrealised_pnl_ccy,normalised_trading_ccy),--Updated FOR BER-2713
                    nvl(UNREALISED_PNL_IN_ACCNT_CCY,0), --Updated FOR BER-2713
                    cnvrsn_rate_to_accnt_ccy,
                    is_mm_old_style,
                    record_source,
                    hedge_asset_class,
                    hedge_instrument_code_external,
                    hedge_expiry_month_code,
                    hedge_risk_bucket,
                    hedge_profitloss,
                    hedge_commission,
                    execution_type,
                    cd_state,
                    trading_scope,
                    /*get_hdg_exec_commission(p_trading_account_id      => trading_account_id,
                                                       p_reporting_date          => reporting_date,
                                                       p_product_instrument_code => product_instrument_code,
                                                       p_hedge_risk_bucket       => hedge_risk_bucket,
                                                       p_mm_value_date           => mm_value_date)*/
                    NULL AS hedge_execution_commission,
                    nrg_common.get_price_band(p_trading_account_id      => trading_account_id,
                                              p_instrument_code         => product_instrument_code)price_band
                    FROM ord
                    WHERE rnk = 1;

  END;
  -- ===================================================================================
  -- put_anytime_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     ANYTIME_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_anytime_position                    Anytime Position Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_anytime_position_set(p_user                      IN VARCHAR2,
                                     p_effective_start_timestamp IN TIMESTAMP,
                                     p_platform                  IN VARCHAR2,
                                     p_anytime_positions         IN anytime_positions_tab)
  IS

    lv_business_date anytime_positions.Business_Date%TYPE;
    lv_reporting_date anytime_positions.Reporting_Date%TYPE;

    TYPE lvtab_anytime_pos IS TABLE OF anytime_positions%rowtype;
    ltab_anytime_pos lvtab_anytime_pos;

  BEGIN

    logger.logger.set_module('nrg_position.put_anytime_position_set');

    --
    --Calculate the business date
    --

    lv_business_date := Nrg_Common.get_business_date(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

       /*
    --
      This is to select the positions into a structure in which they will be stored
      Once the data is ready it will be bulk inserted into the table
    --
    */

    SELECT  p_platform,
            SYSTIMESTAMP ,
            p_user,
            SYSTIMESTAMP,
            P_USER,
            SYSTIMESTAMP,
            p_effective_start_timestamp,
            lv_business_date,
            lv_reporting_date,
            p_effective_start_timestamp,
            pos_tab.order_id,
            pos_tab.direction,
            pos_tab.quantity,
            pos_tab.amount,
            pos_tab.amount_currency,
            pos_tab.amnt_in_trdng_accnt_prmry_ccy,
            pos_tab.open_trade_id,
            pos_tab.open_trade_price,
            pos_tab.opening_trade_amount_fx_rate,
            pos_tab.opening_trade_app_to_units,
            pos_tab.open_time,
            pos_tab.quantity_designator,
            pos_tab.product_instrument_code,
            pos_tab.product_wrapper_code,
            pos_tab.product_point_multiplier,
            pos_tab.product_currency,
            pos_tab.trading_account_id,
            pos_tab.trading_account_type,
            pos_tab.trading_account_codifier,
            pos_tab.trading_account_function,
            pos_tab.trading_accnt_primary_ccy,
            pos_tab.is_automatically_rolled,
            pos_tab.rolled_open_trade_id,
            pos_tab.execution_type,
            pos_tab.trading_scope,
            pos_tab.binary_type,
            pos_tab.settle_time,
            pos_tab.strike_price,
            pos_tab.strike_price_additional,
            pos_tab.tenor,
            pos_tab.tenor_start_time,
            pos_tab.opening_trade_instrument_price,
            pos_tab.forced_margin_fx_rate
             BULK COLLECT INTO ltab_anytime_pos
            FROM TABLE(CAST(p_anytime_positions AS anytime_positions_tab)) pos_tab;



    /*
    --
      This is to delete the data from the history incase the same snapshot is replayed more than 2 times
      Generally this is a very rare chance that a snapshot is re played more than twice

      This has been done because the effective start timestamp for a snapshot is the time for which it is requested
      For market maker there can only be one snapshot a day but for Next Gen there can be many more snapshots(24 a day if required)
    --
    */
    DELETE anytime_positions_h
    WHERE platform = p_platform AND
          effective_start_timestamp = p_effective_start_timestamp;

   INSERT INTO anytime_positions_h
           (platform
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,effective_end_timestamp
           ,action
           ,action_timestamp
           ,business_date
           ,reporting_date
           ,requested_snapshot_time
           ,order_id
           ,direction
           ,quantity
           ,amount
           ,amount_currency
           ,amnt_in_trdng_accnt_prmry_ccy
           ,open_trade_id
           ,open_trade_price
           ,opening_trade_amount_fx_rate
           ,opening_trade_app_to_units
           ,open_time
           ,quantity_designator
           ,product_instrument_code
           ,product_wrapper_code
           ,product_point_multiplier
           ,product_currency
           ,trading_account_id
           ,trading_account_type
           ,trading_account_codifier
           ,trading_account_function
           ,trading_accnt_primary_ccy
           ,is_automatically_rolled
           ,rolled_open_trade_id
           ,execution_type
           ,trading_scope
           ,binary_type
           ,settle_time
           ,strike_price
           ,strike_price_additional
           ,tenor
           ,tenor_start_time
           ,opening_trade_instrument_price
           ,forced_margin_fx_rate)
     SELECT platform
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,p_effective_start_timestamp effective_end_timestamp
           ,'U'
           ,SYSTIMESTAMP action_timestamp
           ,business_date
           ,reporting_date
           ,requested_snapshot_time
           ,order_id
           ,direction
           ,quantity
           ,amount
           ,amount_currency
           ,amnt_in_trdng_accnt_prmry_ccy
           ,open_trade_id
           ,open_trade_price
           ,opening_trade_amount_fx_rate
           ,opening_trade_app_to_units
           ,open_time
           ,quantity_designator
           ,product_instrument_code
           ,product_wrapper_code
           ,product_point_multiplier
           ,product_currency
           ,trading_account_id
           ,trading_account_type
           ,trading_account_codifier
           ,trading_account_function
           ,trading_accnt_primary_ccy
           ,is_automatically_rolled
           ,rolled_open_trade_id
           ,execution_type
           ,trading_scope
           ,binary_type
           ,settle_time
           ,strike_price
           ,strike_price_additional
           ,tenor
           ,tenor_start_time
           ,opening_trade_instrument_price
           ,forced_margin_fx_rate
     FROM   anytime_positions
     WHERE  platform = p_platform AND
            effective_start_timestamp = p_effective_start_timestamp AND
            business_date = lv_business_date;


    DELETE anytime_positions
    WHERE platform = p_platform AND
          effective_start_timestamp = p_effective_start_timestamp AND
          business_date = lv_business_Date;


      FORALL i IN ltab_anytime_pos.FIRST..ltab_anytime_pos.LAST
      INSERT INTO /*+ APPEND_VALUES */ anytime_positions
      VALUES
      (ltab_anytime_pos(i).platform
      ,ltab_anytime_pos(i).logical_load_timestamp
      ,ltab_anytime_pos(i).created_by
      ,ltab_anytime_pos(i).create_timestamp
      ,ltab_anytime_pos(i).updated_by
      ,ltab_anytime_pos(i).update_timestamp
      ,ltab_anytime_pos(i).effective_start_timestamp
      ,ltab_anytime_pos(i).business_date
      ,ltab_anytime_pos(i).reporting_date
      ,ltab_anytime_pos(i).requested_snapshot_time
      ,ltab_anytime_pos(i).order_id
      ,ltab_anytime_pos(i).direction
      ,ltab_anytime_pos(i).quantity
      ,ltab_anytime_pos(i).amount
      ,ltab_anytime_pos(i).amount_currency
      ,ltab_anytime_pos(i).amnt_in_trdng_accnt_prmry_ccy
      ,ltab_anytime_pos(i).open_trade_id
      ,ltab_anytime_pos(i).open_trade_price
      ,ltab_anytime_pos(i).opening_trade_amount_fx_rate
      ,ltab_anytime_pos(i).opening_trade_app_to_units
      ,ltab_anytime_pos(i).open_time
      ,ltab_anytime_pos(i).quantity_designator
      ,ltab_anytime_pos(i).product_instrument_code
      ,ltab_anytime_pos(i).product_wrapper_code
      ,ltab_anytime_pos(i).product_point_multiplier
      ,ltab_anytime_pos(i).product_currency
      ,ltab_anytime_pos(i).trading_account_id
      ,ltab_anytime_pos(i).trading_account_type
      ,ltab_anytime_pos(i).trading_account_codifier
      ,ltab_anytime_pos(i).trading_account_function
      ,ltab_anytime_pos(i).trading_accnt_primary_ccy
      ,ltab_anytime_pos(i).is_automatically_rolled
      ,ltab_anytime_pos(i).rolled_open_trade_id
      ,ltab_anytime_pos(i).execution_type
      ,ltab_anytime_pos(i).trading_scope
      ,ltab_anytime_pos(i).binary_type
      ,ltab_anytime_pos(i).settle_time
      ,ltab_anytime_pos(i).strike_price
      ,ltab_anytime_pos(i).strike_price_additional
      ,ltab_anytime_pos(i).tenor
      ,ltab_anytime_pos(i).tenor_start_time
      ,ltab_anytime_pos(i).opening_trade_instrument_price
      ,ltab_anytime_pos(i).forced_margin_fx_rate);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_anytime_position_set;


  -- ===================================================================================
  -- insert_eod_open_positions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     eod_open_positions[_h]
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------


  PROCEDURE  insert_eod_open_positions (p_business_date             IN eod_open_positions.business_date%TYPE,
                                        p_reporting_date            IN eod_open_positions.reporting_date%TYPE,
                                        p_effective_start_timestamp IN eod_open_positions.effective_start_timestamp%TYPE,
                                        p_logical_load_timestamp    IN eod_open_positions.logical_load_timestamp%TYPE,
                                        p_user                      IN eod_open_positions.created_by%TYPE,
                                        p_record_source             IN eod_open_positions.record_source%TYPE,
                                        p_platform                  IN eod_open_positions.platform%TYPE
                                       )

  IS
  BEGIN

/* BER-3770 Replace eod_core_prices WITH statement with the bi_ods.eod_pos_core_prices_stg staging table created in upd_eod_open_trades */

 -- check if exists data for the same business_date
 BEGIN
   INSERT INTO eod_open_positions_h
    SELECT trading_account_id
          ,trading_account_type
          ,record_source
          ,business_date
          ,product_instrument_code
          ,logical_load_timestamp
          ,created_by
          ,create_timestamp
          ,updated_by
          ,update_timestamp
          ,effective_start_timestamp
          ,p_effective_start_timestamp
          ,'U'
          ,systimestamp
          ,platform
          ,reporting_date
          ,snapshot_time
          ,trading_account_function
          ,trading_accnt_primary_ccy
          ,product_wrapper_code
          ,product_point_multiplier
          ,product_currency
          ,is_inst_ccy_in_fracnal_parts
          ,fractional_part_ratio
          ,direction
          ,financing_ratio
          ,quantity
          ,quantity_currency
          ,margin
          ,margin_currency
          ,amount
          ,amount_currency
          ,mrgn_in_trdng_accnt_prmry_ccy
          ,amnt_in_trdng_accnt_prmry_ccy
          ,open_trade_price_avg
          ,opening_trade_amnt_fx_rate_avg
          ,open_trade_margin_fx_rate
          ,quantity_designator
          ,open_trade_qty_fx_rate_avg
          ,normalised_open_price_avg
          ,normalised_direction_mulplr
          ,normalised_open_qty_trad_ccy
          ,normalised_open_val_trad_ccy
          ,normalised_trading_ccy
          ,normalised_margin_type
          ,normalised_margin_rqrmnt
          ,eod_price_avg
          ,eod_value
          ,eod_value_ccy
          ,eod_value_in_accnt_ccy
          ,unrealised_pnl
          ,unrealised_pnl_ccy
          ,unrealised_pnl_in_accnt_ccy
          ,cnvrsn_rate_to_accnt_ccy_avg
          ,mm_account_id
          ,mm_instrument_id
          ,counterparty_id
          ,position_margin
          ,prime_margin
          ,prime_margin_in_tapc
          ,position_margin_in_tapc
          ,quantity_standard
          ,quantity_gslo
          ,trading_scope
    FROM eod_open_positions t
     WHERE  t.business_date = p_business_date AND
            t.reporting_date = p_reporting_date AND
            t.platform = p_record_source;


     DELETE FROM eod_open_positions t
      WHERE t.business_date = p_business_date
        AND t.Reporting_Date = p_reporting_date
        AND t.platform = p_record_source;

   EXCEPTION
   WHEN DUP_VAL_ON_INDEX THEN
       NULL;
   END;

  BEGIN

    IF (p_platform='NG' AND p_record_source != 'SPEEDBET') THEN


    INSERT INTO eod_open_positions
    WITH accounts AS
         (
         SELECT /*+ MATERIALIZE */
                trading_account_id,
                trading_account_type,
                account_function            as trading_account_function,
                currency                    AS trading_account_ccy,
                --UPPER(product_schm_cd)      AS product_schema_code,
                --UPPER(trading_risk_schm_cd) AS trading_risk_schema_code,
                /*BER-4836*/
                --UPPER(price_schema_code)    AS default_price_schema_code,
                price_schema_code,
                /*BER-4836*/
                UPPER(closeout_schema_code) AS closeout_schema_code,
                independent_margin_amount,
                instrument_schema_code,
                fxr_schema_code
         FROM bi_ods.trading_accounts
         WHERE --source_platform      = 'NG'
               source_platform IN ('NG','SmartTrade','IRESS')
           AND trading_account_type = 'CUSTOMER'
         ),
         gslo_child_orders AS
         (
           SELECT  platform, trading_account_id, trading_account_type, order_id, order_type, related_parent_order_id, limit_price
            FROM bi_ods.contingent_orders_snapshot
            WHERE business_date = p_business_date AND
                  is_eod = 'YES' AND
                  platform = 'NG' AND
                  state = 'PENDING' AND
                  UPPER(order_type) = 'STOPLOSS (GUARANTEED)'

         ),
         position_margin AS
         (
          SELECT /*+ MATERIALIZE */
                business_date,
                trading_account_id,
                trading_account_type,
                product_instrument_code,
                product_wrapper_code,
                total_position_quantity,
                abs_total_position_quantity,
                open_trade_count,
                instrument_mid_price,
                open_trade_margin_fx_rate,
                margin,
                margin_in_ac_ccy,
                margin_currency
         FROM
         (
         SELECT
                posn.business_date,
                posn.trading_account_id,
                posn.trading_account_type,
                NULL AS trading_scope,
                --Get Price Band for the Account's Asset Class Price Bands, if NULL then use the default Account Price Band
                --UPPER(ad.default_price_schema_code) AS price_band,--NVL(UPPER(acp.prc_ast_cls_schema_code),UPPER(ad.default_price_schema_code))

                posn.product_instrument_code,
                posn.product_wrapper_code,
                ABS(posn.total_position_quantity) AS total_position_quantity,
                posn.abs_total_position_quantity,
                posn.open_trade_count,
                --posn.normalised_direction_mulplr, -- new filed - positions
                (pr.bid_price+pr.ask_price)/2 instrument_mid_price,

                --Margin is calculated at a Positon grain, therefore include Margin calculation in this Position Statement.

                --Reval the Margin into Account CCY, this is only required for CFDs that are not in the same currency as the accont currency.
                CASE WHEN posn.product_wrapper_code = 'X-A' AND (posn.product_currency != posn.trading_accnt_primary_ccy) THEN (reval.ask_price + reval.bid_price)/2
                     ELSE 1
                 END AS open_trade_margin_fx_rate,

                --Margin Tier 1 Allocation
                md.Tier1_Boundary,
                md.Tier1_Rate,
                CASE WHEN ABS(posn.total_position_quantity) > 0
                    THEN (CASE WHEN ABS(posn.total_position_quantity) >= md.Tier1_Boundary THEN md.Tier1_Boundary
                               ELSE ABS(posn.total_position_quantity)
                           END)
                    ELSE 0
                END AS Tier1_Boundary_Used,

                --Margin Tier 2 Allocation
                md.Tier2_Boundary,
                md.Tier2_Rate,
                CASE WHEN ABS(posn.total_position_quantity) - md.Tier1_Boundary > 0
                     THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier2_Boundary THEN md.Tier2_Boundary - md.Tier1_Boundary
                                ELSE ABS(posn.total_position_quantity) - md.Tier1_Boundary
                            END)
                     ELSE 0
                 END AS Tier2_Boundary_Used,

                --Margin Tier 3 Allocation
                md.Tier3_Boundary,
                md.Tier3_Rate,
                CASE WHEN ABS(posn.total_position_quantity) - md.Tier2_Boundary > 0
                     THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier3_Boundary THEN md.Tier3_Boundary - md.Tier2_Boundary
                                ELSE ABS(posn.total_position_quantity) - md.Tier2_Boundary
                            END)
                     ELSE 0
                 END AS Tier3_Boundary_Used,

                --Margin Tier 4 Allocation
                md.Tier4_Boundary,
                md.Tier4_Rate,
                CASE WHEN ABS(posn.total_position_quantity) - md.Tier3_Boundary > 0
                     THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier4_Boundary THEN md.Tier4_Boundary - md.Tier3_Boundary
                                ELSE ABS(posn.total_position_quantity) - md.Tier3_Boundary
                            END)
                     ELSE 0
                 END AS Tier4_Boundary_Used,

                --Margin Tier 5 Allocation
                md.Tier5_Boundary,
                md.Tier5_Rate,

                 CASE
                       WHEN ABS(posn.total_position_quantity) - md.Tier4_Boundary > 0
                       THEN ABS(posn.total_position_quantity) - md.Tier4_Boundary
                       ELSE 0
                  END

                  AS Tier5_Boundary_Used,

               -- START OF MARGIN VALUE CALCULATION
               /*BER-4838*/--TRUNC(
                       (
                         --Tier 1 Rate multiplied by the number of units for Tier 1
                         (NVL(md.Tier1_Rate,0) *
                         CASE WHEN ABS(posn.total_position_quantity) > 0
                              THEN (CASE WHEN ABS(posn.total_position_quantity) >= md.Tier1_Boundary THEN md.Tier1_Boundary
                                         ELSE ABS(posn.total_position_quantity)
                                     END)
                              ELSE 0
                          END)
                        +
                         --Tier 2 Rate multiplied by the number of units for Tier 2
                         (NVL(md.Tier2_Rate,0) *
                         CASE WHEN ABS(posn.total_position_quantity) - md.Tier1_Boundary > 0
                              THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier2_Boundary THEN md.Tier2_Boundary - md.Tier1_Boundary
                                         ELSE ABS(posn.total_position_quantity) - md.Tier1_Boundary
                                     END)
                              ELSE 0
                          END)
                        +
                         --Tier 3 Rate multiplied by the number of units for Tier 3
                         (NVL(md.Tier3_Rate,0) *
                         CASE WHEN ABS(posn.total_position_quantity) - md.Tier2_Boundary > 0
                              THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier3_Boundary THEN md.Tier3_Boundary - md.Tier2_Boundary
                                         ELSE ABS(posn.total_position_quantity) - md.Tier2_Boundary
                                     END)
                              ELSE 0
                          END)
                        +
                         --Tier 4 Rate multiplied by the number of units for Tier 4
                         (NVL(md.Tier4_Rate,0) *
                         CASE WHEN ABS(posn.total_position_quantity) - md.Tier3_Boundary > 0
                              THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier4_Boundary THEN md.Tier4_Boundary - md.Tier3_Boundary
                                         ELSE ABS(posn.total_position_quantity) - md.Tier3_Boundary
                                     END)
                              ELSE 0
                          END)
                        +
                        --Tier 5 Rate multiplied by the number of units for Tier 5
                         (NVL(md.Tier5_Rate,0) *
                           CASE
                             WHEN ABS(posn.total_position_quantity) - md.Tier4_Boundary > 0
                             THEN ABS(posn.total_position_quantity) - md.Tier4_Boundary
                             ELSE 0
                           END
                             )
                       )

                      * ((pr.bid_price+pr.ask_price)/2) --Calculated instrument MID price

                      * (CASE WHEN posn.product_wrapper_code = 'X-A' AND posn.is_inst_ccy_in_fracnal_parts = 'YES'
                              THEN posn.fractional_part_ratio
                              ELSE 1 END)

                      * (CASE WHEN posn.product_wrapper_code = 'A-EOVH'
                              THEN posn.product_point_multiplier
                              ELSE 1 END)

                    /*BER-4838*/--,2) --The Frontend truncates to 2dp
                AS Margin,
               -- END OF MARGIN VALUE CALCULATION

               -- START OF MARGIN VALUE CALCULATION IN ACCOUNT CCY
               /*BER-4838*/--TRUNC(
                   (
                     --Tier 1 Rate multiplied by the number of units for Tier 1
                     (NVL(md.Tier1_Rate,0) *
                     CASE WHEN ABS(posn.total_position_quantity) > 0
                          THEN (CASE WHEN ABS(posn.total_position_quantity) >= md.Tier1_Boundary THEN md.Tier1_Boundary
                                     ELSE ABS(posn.total_position_quantity)
                                 END)
                          ELSE 0
                      END)
                    +
                     --Tier 2 Rate multiplied by the number of units for Tier 2
                     (NVL(md.Tier2_Rate,0) *
                     CASE WHEN ABS(posn.total_position_quantity) - md.Tier1_Boundary > 0
                          THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier2_Boundary THEN md.Tier2_Boundary - md.Tier1_Boundary
                                     ELSE ABS(posn.total_position_quantity) - md.Tier1_Boundary
                                 END)
                          ELSE 0
                      END)
                    +
                     --Tier 3 Rate multiplied by the number of units for Tier 3
                     (NVL(md.Tier3_Rate,0) *
                     CASE WHEN ABS(posn.total_position_quantity) - md.Tier2_Boundary > 0
                          THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier3_Boundary THEN md.Tier3_Boundary - md.Tier2_Boundary
                                     ELSE ABS(posn.total_position_quantity) - md.Tier2_Boundary
                                 END)
                          ELSE 0
                      END)
                    +
                     --Tier 4 Rate multiplied by the number of units for Tier 4
                     (NVL(md.Tier4_Rate,0) *
                     CASE WHEN ABS(posn.total_position_quantity) - md.Tier3_Boundary > 0
                          THEN (CASE WHEN ABS(posn.total_position_quantity) > md.Tier4_Boundary THEN md.Tier4_Boundary - md.Tier3_Boundary
                                     ELSE ABS(posn.total_position_quantity) - md.Tier3_Boundary
                                 END)
                          ELSE 0
                      END)
                    +
                    --Tier 5 Rate multiplied by the number of units for Tier 5
                     (NVL(md.Tier5_Rate,0) *
                           CASE
                             WHEN ABS(posn.total_position_quantity) - md.Tier4_Boundary > 0
                             THEN ABS(posn.total_position_quantity) - md.Tier4_Boundary
                             ELSE 0
                           END)
                   )

                  * ((pr.bid_price+pr.ask_price)/2) --Calculated instrument MID price

                  --Reval the Margin into Account CCY, this is only required for CFDs that are not in the same currency as the account currency.
                  * (CASE WHEN posn.product_wrapper_code = 'X-A' AND (posn.product_currency != posn.trading_accnt_primary_ccy) THEN (reval.ask_price + reval.bid_price)/2
                          ELSE 1 END)

                  * (CASE WHEN posn.product_wrapper_code = 'X-A' AND posn.is_inst_ccy_in_fracnal_parts = 'YES'
                          THEN posn.fractional_part_ratio
                          ELSE 1 END)

                  * (CASE WHEN posn.product_wrapper_code = 'A-EOVH'
                          THEN posn.product_point_multiplier
                          ELSE 1 END)
                /*BER-4838*/--,2) --The Frontend truncates to 2dp

            AS  Margin_IN_AC_CCY,
            -- END OF MARGIN VALUE CALCULATION IN ACCOUNT CCY

            CASE WHEN posn.product_wrapper_code = 'X-A'    THEN  posn.product_currency
                 WHEN posn.product_wrapper_code = 'A-EOVH' THEN  posn.trading_accnt_primary_ccy
             END  AS Margin_currency,

            --Rank to ensure only 1 Margin Row per Trading Account and Instrument Code is returned, by order of Margin Definitions: Named instrument schema codes take priority, Default takes 2nd priotity
            ROW_NUMBER () OVER (PARTITION BY posn.trading_account_id, posn.trading_account_type,posn.product_instrument_code, posn.product_wrapper_code ORDER BY (CASE WHEN md.instrument_schema_code = 'Default' THEN 2 ELSE 1 END) ASC, md.instrument_schema_code
                               ) AS position_rank

           FROM
                 (
                 SELECT eod.business_date,
                        eod.trading_account_id,
                        eod.trading_account_type,
                        eod.trading_accnt_primary_ccy,
                        eod.product_instrument_code,
                        eod.product_wrapper_code,
                        eod.product_currency,
                        product_setting_code,
                        --eod.product_point_multiplier,
                        --BER-2712
                        CASE WHEN eod.product_wrapper_code = 'X-A' THEN prd.point_multiplier ELSE eod.product_point_multiplier END AS product_point_multiplier,
                        i.instrument_type,
                        eod.is_inst_ccy_in_fracnal_parts,
                        eod.fractional_part_ratio,
                       NULL AS trading_scope,
                        SUM (eod.quantity*eod.normalised_direction_mulplr) AS total_position_quantity,
                        SUM (eod.quantity) AS abs_total_position_quantity,
                        COUNT(eod.order_id) AS open_trade_count
                 FROM bi_ods.eod_open_trades eod
                 --BER-2712
                 JOIN bi_ods.products prd ON (eod.platform = prd.platform AND eod.product_wrapper_code = prd.wrapper_code AND eod.product_instrument_code = prd.instrument_code)
                 JOIN bi_ods.instruments i ON i.instrument_code = eod.product_instrument_code

                 WHERE eod.platform = 'NG'
                   AND eod.business_date = p_business_date
                   AND eod.record_source = 'NG'
           AND eod.product_wrapper_code NOT IN ('X-MNRM','X-MNRN', 'X-QOQH') -- X-MNRM - NG CFD BinaryOption,  X-MNRN - NG SB BinaryOption -- Binary Option change

                   AND NOT EXISTS (--Consder Open Trades that are NOT related to a GSLO child order
                                   SELECT gslo_child_orders.order_id
                                     FROM gslo_child_orders
                                    WHERE gslo_child_orders.related_parent_order_id = eod.order_id
                                      AND gslo_child_orders.platform = eod.platform)
                 GROUP BY eod.business_date,
                          eod.trading_account_id,
                          eod.trading_account_type,
                          eod.trading_accnt_primary_ccy,
                          eod.product_instrument_code,
                          eod.product_wrapper_code,
                          eod.product_currency,
                          product_setting_code,
                          --eod.product_point_multiplier,
                          --BER-2712
                          CASE WHEN eod.product_wrapper_code = 'X-A' THEN prd.point_multiplier ELSE eod.product_point_multiplier END,
                          eod.is_inst_ccy_in_fracnal_parts,
                          eod.fractional_part_ratio,
                          i.instrument_type
                         -- eod.trading_scope
                 ) posn

           LEFT
           JOIN accounts ad               ON  ad.trading_account_id                     =  posn.trading_account_id

           --LEFT
           --JOIN bi_ods.accnt_prc_ast_schm_cds acp ON acp.trading_account_id             = ad.trading_account_id
           --                                      AND UPPER(acp.trading_account_type)    = UPPER(ad.trading_account_type)
           --                                      AND UPPER(acp.asset_class)             = UPPER(posn.instrument_type)

           LEFT
           JOIN bi_ods.product_settings_margin_defs md   ON
                                                md.product_setting_code = posn.product_setting_code
                                                --md.instrument_code                 =  posn.product_instrument_code
                                                --AND  md.wrapper_code                    =  posn.product_wrapper_code
                                                AND DECODE(md.trading_account_ccy,'NA',ad.trading_account_ccy,md.trading_account_ccy) =  ad.trading_account_ccy
                                                AND DECODE(md.instrument_schema_code,'Default', ad.instrument_schema_code, md.instrument_schema_code) = ad.instrument_schema_code
                                                --AND  UPPER(md.product_schema_code)      =  UPPER(ad.product_schema_code)
                                                --AND  UPPER(md.trading_risk_schema_code) =  UPPER(ad.trading_risk_schema_code)

            /*BER-4836*/
            --LEFT
            --JOIN bi_ods.eod_pos_core_prices_stg pr              ON  pr.instrument_code                 =  posn.product_instrument_code
            --                                  --AND  pr.business_date                   =  posn.business_Date

            LEFT OUTER
            JOIN bi_ods.eod_pos_pricebanding_stg pb
              ON pb.price_schema_name = ad.price_schema_code
             AND pb.instrument_code  = posn.product_instrument_code

            LEFT
            JOIN bi_ods.eod_pos_banded_prices_stg pr
              ON pr.instrument_code = posn.product_instrument_code
             AND pr.price_band     = nvl(upper(pb.price_band),upper(pb.default_band))

            /*BER-4836*/


       LEFT JOIN bi_ods.eod_pos_banded_revals_stg reval           
              ON reval.from_ccy = posn.product_currency
             AND reval.to_ccy   = posn.trading_accnt_primary_ccy
             AND reval.band     = NVL(ad.fxr_schema_code,'CMC-Standard')
                                              
         )WHERE position_rank = 1),

         prime_margin AS
         (
         SELECT /*+ MATERIALIZE */
                eod.business_date,
                eod.trading_account_id,
                eod.trading_account_type,
                --NVL(UPPER(pb.prc_ast_cls_schema_code),UPPER(ta.default_price_schema_code)) AS price_band,
                eod.product_instrument_code,
                eod.product_wrapper_code,

                ABS(SUM (eod.quantity*eod.normalised_direction_mulplr)) AS total_position_quantity,
                SUM (eod.quantity) AS abs_total_position_quantity,
                COUNT(eod.order_id) AS open_trade_count,

                (pr.bid_price+pr.ask_price)/2 instrument_mid_price,

                CASE WHEN eod.product_wrapper_code = 'X-A' AND (eod.product_currency != eod.trading_accnt_primary_ccy) THEN (reval.ask_price + reval.bid_price)/2
                     ELSE 1
                 END AS open_trade_margin_fx_rate,
                CASE
                  WHEN eod.product_wrapper_code = 'X-QOQH' THEN
                    0
              WHEN am.min_prime_margin_type = 'ABSOLUTE' --UPPER(ta.closeout_schema_code) = 'CMC-PL'
                   THEN /*BER-4838*/--TRUNC(
                               SUM(
                                    GREATEST(
                                              --STANDARD GSLO CALCULATION
                                              eod.normalised_open_qty_trad_ccy *
                                              eod.normalised_direction_mulplr *
                                              (((pr.bid_price + pr.ask_price)/2) - gslo.limit_price),
                                              --MARGIN USING MIN PRIME MARGIN VALUE (Currently 1%)
                                              NVL(am.min_prime_margin_value,0) *
                                              eod.normalised_open_qty_trad_ccy *
                                              ((pr.bid_price + pr.ask_price)/2)
                                            )
                                  )
                             /*BER-4838*/--,2)
              WHEN am.min_prime_margin_type = 'TIER1' --UPPER(ta.closeout_schema_code) = 'CMC-CA'
                   THEN /*BER-4838*/--TRUNC(
                               SUM(
                                    GREATEST(
                                              --STANDARD GSLO CALCULATION
                                              eod.normalised_open_qty_trad_ccy *
                                              eod.normalised_direction_mulplr *
                                              (((pr.bid_price + pr.ask_price)/2) - gslo.limit_price),
                                              --MARGIN USING MIN PRIME MARGIN TYPE (Currently TIER1)
                                              eod.normalised_open_qty_trad_ccy *
                                              CASE WHEN am.min_prime_margin_type = 'TIER1'
                                                        THEN NVL(
                                                                  NVL(
                                                                       (SELECT md.tier1_rate
                                                                          FROM bi_ods.product_settings_margin_defs md
                                                                         WHERE md.product_setting_code = p.product_setting_code
                                                                           AND md.instrument_schema_code = ta.instrument_schema_code
                                                                           AND CASE WHEN p.wrapper_code = 'A-EOVH'
                                                                                         THEN ta.trading_account_ccy
                                                                                    ELSE 'NA'
                                                                               END = md.trading_account_ccy
                                                                           AND md.is_deleted = 'NO'),
                                                                       (SELECT md.tier1_rate
                                                                          FROM bi_ods.product_settings_margin_defs md
                                                                         WHERE md.product_setting_code = p.product_setting_code
                                                                           AND md.instrument_schema_code = 'Default'
                                                                           AND CASE WHEN p.wrapper_code = 'A-EOVH'
                                                                                         THEN ta.trading_account_ccy
                                                                                    ELSE 'NA'
                                                                               END = md.trading_account_ccy
                                                                           AND md.is_deleted = 'NO')
                                                                     )
                                                                ,0)
                                                   ELSE 0
                                              END *
                                              ((pr.bid_price + pr.ask_price)/2)
                                            )
                                  )
                             /*BER-4838*/--,2)
                  ELSE
                    /*BER-4838*/--TRUNC(
                         SUM(  eod.normalised_open_qty_trad_ccy
                             * eod.normalised_direction_mulplr
                             * (((pr.bid_price + pr.ask_price)/2) - gslo.limit_price)
                             * (CASE WHEN UPPER(gsko.instrument_schema_value) = 'YES'
                                     THEN (NVL(am.prime_margin_buffer,0)+1)
                                     ELSE 1
                                 END)
                            )
                         /*BER-4838*/--,2)
                END AS Margin,
                CASE
                  WHEN eod.product_wrapper_code = 'X-QOQH' THEN
                    0
              WHEN am.min_prime_margin_type = 'ABSOLUTE' --UPPER(ta.closeout_schema_code) = 'CMC-PL'
                   THEN /*BER-4838*/--TRUNC(
                               SUM(
                                    GREATEST(
                                              --STANDARD GSLO CALCULATION
                                              eod.normalised_open_qty_trad_ccy *
                                              eod.normalised_direction_mulplr *
                                              (((pr.bid_price + pr.ask_price)/2) - gslo.limit_price) *
                                              (CASE WHEN eod.product_wrapper_code = 'X-A'
                                                         AND (eod.product_currency != eod.trading_accnt_primary_ccy)
                                                         THEN (reval.ask_price + reval.bid_price)/2
                                                    ELSE 1
                                               END),
                                              --MARGIN USING MIN PRIME MARGIN VALUE (Currently 1%)
                                              NVL(am.min_prime_margin_value,0) *
                                              eod.normalised_open_qty_trad_ccy *
                                              ((pr.bid_price + pr.ask_price)/2) *
                                              (CASE WHEN eod.product_wrapper_code = 'X-A'
                                                         AND (eod.product_currency != eod.trading_accnt_primary_ccy)
                                                         THEN (reval.ask_price + reval.bid_price)/2
                                                    ELSE 1
                                               END)
                                            )
                                  )
                             /*BER-4838*/--,2)
               WHEN am.min_prime_margin_type = 'TIER1'--UPPER(ta.closeout_schema_code) = 'CMC-CA'
                    THEN /*BER-4838*/--TRUNC(
                                SUM(
                                     GREATEST(
                                               --STANDARD GSLO CALCULATION
                                               eod.normalised_open_qty_trad_ccy *
                                               eod.normalised_direction_mulplr *
                                               (((pr.bid_price + pr.ask_price)/2) - gslo.limit_price) *
                                               (CASE WHEN eod.product_wrapper_code = 'X-A'
                                                          AND (eod.product_currency != eod.trading_accnt_primary_ccy)
                                                          THEN (reval.ask_price + reval.bid_price)/2
                                                     ELSE 1
                                                END),
                                               --MARGIN USING MIN PRIME MARGIN TYPE (Currently TIER1)
                                               eod.normalised_open_qty_trad_ccy *
                                               CASE WHEN am.min_prime_margin_type = 'TIER1'
                                                         THEN NVL(
                                                                   NVL(
                                                                       (SELECT md.tier1_rate
                                                                          FROM bi_ods.product_settings_margin_defs md
                                                                         WHERE md.product_setting_code = p.product_setting_code
                                                                           AND md.instrument_schema_code = ta.instrument_schema_code
                                                                           AND CASE WHEN p.wrapper_code = 'A-EOVH'
                                                                                         THEN ta.trading_account_ccy
                                                                                    ELSE 'NA'
                                                                               END = md.trading_account_ccy
                                                                           AND md.is_deleted = 'NO'),
                                                                       (SELECT md.tier1_rate
                                                                          FROM bi_ods.product_settings_margin_defs md
                                                                         WHERE md.product_setting_code = p.product_setting_code
                                                                           AND md.instrument_schema_code = 'Default'
                                                                           AND CASE WHEN p.wrapper_code = 'A-EOVH'
                                                                                         THEN ta.trading_account_ccy
                                                                                    ELSE 'NA'
                                                                               END = md.trading_account_ccy
                                                                           AND md.is_deleted = 'NO')
                                                                      )
                                                                 ,0)
                                                    ELSE 0
                                               END *
                                               ((pr.bid_price + pr.ask_price)/2) *
                                               (CASE WHEN eod.product_wrapper_code = 'X-A'
                                                          AND (eod.product_currency != eod.trading_accnt_primary_ccy)
                                                          THEN (reval.ask_price + reval.bid_price)/2
                                                     ELSE 1
                                                END)
                                             )
                                   )
                              /*BER-4838*/--,2)
                  ELSE
                    /*BER-4838*/--TRUNC(
                         SUM(  eod.normalised_open_qty_trad_ccy
                             * eod.normalised_direction_mulplr
                             * (((pr.bid_price + pr.ask_price)/2) - gslo.limit_price)
                             * (CASE WHEN UPPER(gsko.instrument_schema_value) = 'YES'
                                     THEN (NVL(am.prime_margin_buffer,0)+1)
                                     ELSE 1
                                 END)
                             * (CASE WHEN eod.product_wrapper_code = 'X-A' AND (eod.product_currency != eod.trading_accnt_primary_ccy) THEN (reval.ask_price + reval.bid_price)/2
                                     ELSE 1 END)
                            )
                         /*BER-4838*/--,2)
                END AS Margin_IN_AC_CCY,

                CASE WHEN eod.product_wrapper_code = 'X-A'    THEN  eod.product_currency
                     WHEN eod.product_wrapper_code = 'A-EOVH' THEN  eod.trading_accnt_primary_ccy
                     WHEN eod.product_wrapper_code = 'X-QOQH' THEN eod.trading_accnt_primary_ccy
                 END  AS Margin_currency

         FROM bi_ods.eod_open_trades eod

         JOIN accounts ta ON  ta.trading_account_id  = eod.trading_account_id

         JOIN bi_ods.accnt_mon_region_notif_levls am ON UPPER(am.closeout_schema_code) = UPPER(ta.closeout_schema_code)

         JOIN gslo_child_orders gslo  ON gslo.related_parent_order_id = eod.order_id
                                     AND gslo.trading_account_id      = eod.trading_account_id
                                     AND gslo.trading_account_type    = eod.trading_account_type
                                     AND gslo.platform                = eod.platform

         JOIN bi_ods.instruments i ON i.instrument_code = eod.product_instrument_code

         JOIN bi_ods.products p ON eod.product_instrument_code = p.instrument_code
                               AND eod.product_wrapper_code = p.wrapper_code
                               AND eod.platform = p.platform

         LEFT JOIN bi_ods.products_gsko gsko ON gsko.product_setting_code = p.product_setting_code
                                            AND gsko.instrument_schema_code = ta.instrument_schema_code --CHECK THIS LINE
                                            AND UPPER(gsko.product_setting_property) = 'APPLYPRIMEMARGINBUFFER'
                                            AND gsko.is_deleted = 'NO'

         --LEFT JOIN bi_ods.accnt_prc_ast_schm_cds pb ON pb.trading_account_id = eod.trading_account_id
         --                                          AND pb.trading_account_type = eod.trading_account_type
         --                                          AND UPPER(pb.asset_class) = UPPER(i.instrument_type)


         /*BER-4836*/
         --LEFT JOIN bi_ods.eod_pos_core_prices_stg pr              ON  pr.instrument_code                 =  eod.product_instrument_code
         --                                  --AND  pr.business_date                   =  eod.business_Date

         LEFT OUTER
         JOIN bi_ods.eod_pos_pricebanding_stg pb
           ON pb.price_schema_name = ta.price_schema_code
          AND pb.instrument_code  = eod.product_instrument_code

         LEFT
         JOIN bi_ods.eod_pos_banded_prices_stg pr
           ON pr.instrument_code = eod.product_instrument_code
          AND pr.price_band     = nvl(upper(pb.price_band),upper(pb.default_band))

         /*BER-4836*/

    LEFT JOIN bi_ods.eod_pos_banded_revals_stg reval  
           ON reval.from_ccy  = eod.product_currency
          AND reval.to_ccy    = eod.trading_accnt_primary_ccy
          AND reval.band      = NVL(ta.fxr_schema_code,'CMC-Standard')          
          
         WHERE eod.platform = 'NG'
           AND eod.business_date = p_business_date
           AND eod.record_source = 'NG'
       AND eod.product_wrapper_code NOT IN ('X-MNRM','X-MNRN')--'X-QOQH' BER-2850 -- X-MNRM - NG CFD BinaryOption,  X-MNRN - NG SB BinaryOption -- Binary Option change

         GROUP BY
                  eod.business_date,
                  eod.trading_account_id,
                  eod.trading_account_type,
                  --NVL(UPPER(pb.prc_ast_cls_schema_code),UPPER(ta.default_price_schema_code)),
                  eod.product_instrument_code,
                  eod.product_wrapper_code,
                  (pr.bid_price+pr.ask_price)/2,

                  CASE WHEN eod.product_wrapper_code = 'X-A' AND (eod.product_currency != eod.trading_accnt_primary_ccy) THEN (reval.ask_price + reval.bid_price)/2
                       ELSE 1
                   END,
                   am.min_prime_margin_type,

                  CASE WHEN eod.product_wrapper_code = 'X-A'    THEN  eod.product_currency
                       WHEN eod.product_wrapper_code = 'A-EOVH' THEN  eod.trading_accnt_primary_ccy
                       WHEN eod.product_wrapper_code = 'X-QOQH' THEN eod.trading_accnt_primary_ccy
                   END
         )

    SELECT

    po.trading_account_id,
    po.trading_account_type,
    po.record_source,
    po.business_date,
    po.product_instrument_code,
    p_logical_load_timestamp  LOGICAL_LOAD_TIMESTAMP,
    p_user CREATED_BY,
    systimestamp CREATE_TIMESTAMP,
    p_user UPDATED_BY,
    systimestamp UPDATE_TIMESTAMP,
    p_effective_start_timestamp EFFECTIVE_START_TIMESTAMP,
    po.platform,
    po.reporting_date,
    po.snapshot_time,
    po.trading_account_function,
    po.trading_accnt_primary_ccy,
    po.product_wrapper_code,
    --po.product_point_multiplier,
    --BER-2712
    CASE WHEN po.product_wrapper_code = 'X-A' THEN prd.point_multiplier ELSE po.product_point_multiplier END AS product_point_multiplier,
    po.product_currency,
    po.is_inst_ccy_in_fracnal_parts,
    po.fractional_part_ratio,


    CASE
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 'FLAT'
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) > 0 THEN 'BUY'
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) < 0 THEN 'SELL'
        ELSE NULL
     END AS direction,

    po.financing_ratio,

    ABS(SUM(po.quantity*po.normalised_direction_mulplr)) AS quantity,
    po.quantity_currency,

    /*BER-4838*/
    --MAX(NVL(psn.margin,0) + NVL(prm.margin,0)) AS margin,
    TRUNC(MAX(NVL(psn.margin,0) + NVL(prm.margin,0)),2) AS margin,
    /*BER-4838*/
    NVL(psn.margin_currency,prm.margin_currency) AS margin_currency,

    ABS(SUM(po.amount*po.normalised_direction_mulplr)) AS amount,
    po.amount_currency,


    /*BER-4838*/
    --MAX(NVL(psn.Margin_IN_AC_CCY,0) + NVL(prm.Margin_IN_AC_CCY,0)) AS mrgn_in_trdng_accnt_prmry_ccy,
    TRUNC(MAX(NVL(psn.Margin_IN_AC_CCY,0) + NVL(prm.Margin_IN_AC_CCY,0)),2) AS mrgn_in_trdng_accnt_prmry_ccy,
    /*BER-4838*/

    ABS(SUM(po.amnt_in_trdng_accnt_prmry_ccy*po.normalised_direction_mulplr)) AS amnt_in_trdng_accnt_prmry_ccy,


    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.open_trade_price)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS open_trade_price_avg,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.opening_trade_amount_fx_rate)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS opening_trade_amnt_fx_rate_avg,

    NVL(psn.open_trade_margin_fx_rate,prm.open_trade_margin_fx_rate) AS open_trade_margin_fx_rate,


    po.quantity_designator,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN NULL
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.open_trade_quantity_fx_rate)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS open_trade_qty_fx_rate_avg,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.normalised_open_price)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS normalised_open_price_avg,

    CASE
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN  0
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) > 0 THEN  1
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) < 0 THEN -1
     END AS normalised_direction_mulplr,


    ABS(SUM(po.normalised_open_qty_trad_ccy*po.normalised_direction_mulplr)) AS normalised_open_qty_trad_ccy,

    ABS(SUM(po.normalised_open_val_trad_ccy*po.normalised_direction_mulplr)) AS normalised_open_val_trad_ccy,

    po.normalised_trading_ccy,
    po.normalised_margin_type,

    MAX(NVL(psn.margin,0) + NVL(prm.margin,0))/(ABS(MAX(po.eod_value*po.normalised_direction_mulplr)))
    AS normalised_margin_rqrmnt,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.eod_price)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS eod_price_avg,

    ABS(SUM(po.eod_value*po.normalised_direction_mulplr)) AS eod_value,
    po.eod_value_ccy,
    ABS(SUM(po.eod_value_in_accnt_ccy*po.normalised_direction_mulplr)) AS eod_value_in_accnt_ccy,

    SUM(po.unrealised_pnl) AS unrealised_pnl,

    po.unrealised_pnl_ccy,
    SUM(po.unrealised_pnl_in_accnt_ccy) AS unrealised_pnl_in_accnt_ccy,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.cnvrsn_rate_to_accnt_ccy)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS cnvrsn_rate_to_accnt_ccy_avg,

    NULL AS MM_ACCOUNT_ID,
    NULL AS MM_INSTRUMENT_ID,
    NULL AS COUNTERPARTY_ID,
    ---NEW
    /*BER-4838*/
    --MAX(NVL(psn.margin,0)) AS position_margin,
    TRUNC(MAX(NVL(psn.margin,0)),2) AS position_margin,
    --MAX(NVL(prm.margin,0)) AS prime_margin,
    TRUNC(MAX(NVL(prm.margin,0)),2) AS prime_margin,
    --MAX(NVL(prm.Margin_IN_AC_CCY,0)) AS prime_margin_in_tapc,
    TRUNC(MAX(NVL(prm.Margin_IN_AC_CCY,0)),2) AS prime_margin_in_tapc,
    --MAX(NVL(psn.Margin_IN_AC_CCY,0)) AS position_margin_in_tapc,
    TRUNC(MAX(NVL(psn.Margin_IN_AC_CCY,0)),2) AS position_margin_in_tapc,
    /*BER-4838*/
    MAX(NVL(psn.total_position_quantity,0)) AS QUANTITY_STANDARD,
    MAX(NVL(prm.total_position_quantity,0)) AS QUANTITY_GSLO,
   NULL AS trading_scope

    FROM bi_ods.eod_open_trades po


    LEFT JOIN position_margin psn  ON psn.trading_account_id       = po.trading_account_id
                                  AND psn.trading_account_type     = po.trading_account_type
                                  AND psn.product_instrument_code  = po.product_instrument_code
                                  AND psn.product_wrapper_code     = po.product_wrapper_code
                                  AND psn.business_date            = po.business_date
                                  --AND psn.trading_scope            = po.trading_scope

    LEFT  JOIN prime_margin prm    ON prm.trading_account_id       = po.trading_account_id
                                  AND prm.trading_account_type     = po.trading_account_type
                                  AND prm.product_instrument_code  = po.product_instrument_code
                                  AND prm.product_wrapper_code     = po.product_wrapper_code
                                  AND prm.business_date            = po.business_date
    --BER-2712
    LEFT JOIN bi_ods.products prd ON po.platform = prd.platform AND
                                     po.product_wrapper_code = prd.wrapper_code AND
                                     po.product_instrument_code = prd.instrument_code
    WHERE po.platform = 'NG'
      AND po.record_source = 'NG'
    AND po.product_wrapper_code NOT IN ('X-MNRM','X-MNRN')--'X-QOQH'--BER-2850 -- X-MNRM - NG CFD BinaryOption,  X-MNRN - NG SB BinaryOption -- Binary Option change
      AND po.business_date = p_business_date

    GROUP BY
    po.platform,
    po.snapshot_time,
    po.business_date,
    po.reporting_date,
    po.trading_account_id,
    po.trading_account_type,
    po.trading_account_function,
  --  po.trading_scope,
    po.product_instrument_code,
    po.platform,
    po.product_wrapper_code,
    --po.product_point_multiplier,
    --BER-2712
    CASE WHEN po.product_wrapper_code = 'X-A' THEN prd.point_multiplier ELSE po.product_point_multiplier END,
    po.product_currency,
    po.financing_ratio,
    po.quantity_currency,
    po.amount_currency,
    NVL(psn.margin_currency,prm.margin_currency),
    NVL(psn.open_trade_margin_fx_rate,prm.open_trade_margin_fx_rate),
    po.quantity_designator,
    po.trading_accnt_primary_ccy,
    po.is_inst_ccy_in_fracnal_parts,
    po.fractional_part_ratio,
    po.normalised_trading_ccy,
    po.normalised_margin_type,
    po.eod_value_ccy,
    po.unrealised_pnl_ccy,
    po.record_source;





    ELSIF (p_platform != 'NG') THEN

    INSERT INTO Eod_Open_Positions
    -- This is for MM only. Please implement this so that NG logic is unaffected.
    -- The summing and grouping is required for MM FX positions. At this level we want to treat different value dates of the same ccy pair positions to be aggregated into one.
    -- Note that, for FX, as Backoffice doesn't have a concept of is_primary (like we do on a trade level), it just treats LHS / prim ccy as quantity and RHS / sec ccy as amount.

    with eod_core_prices AS
         (SELECT * FROM (
         SELECT business_date,
                                  quote_time,
                                  price_symbol,
                                  instrument_code,
                                  bid_price,
                                  ask_price,
                                  (bid_price + ask_price)/2 AS mid_price,
                                  'CMC-STANDARD' AS price_band,
                                  CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 3)
                                       ELSE NULL
                                   END AS fxr_from_ccy,

                                  CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 4, 3)
                                       ELSE NULL
                                   END AS fxr_to_ccy,
                                  ROW_NUMBER () OVER (PARTITION BY (CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 6) ELSE instrument_code END ) ORDER BY business_date desc, quote_time DESC)rnk
                           FROM bi_ods.prices
                           WHERE platform = 'NG'
                             AND instrument_code NOT LIKE '%/CAR'
                             AND business_date = p_business_date) WHERE RNK = 1)

    select eot.trading_account_id,
           eot.trading_account_type,
           eot.record_source,
           eot.business_date,
           eot.product_instrument_code,
           systimestamp as logical_load_timestamp,
           'BI_ODS' as created_by,
           systimestamp as create_timestamp,
           'BI_ODS' as updated_by,
           systimestamp as update_timestamp,
           eot.effective_start_timestamp,
           eot.platform,
           eot.reporting_date,
           eot.snapshot_time,
           eot.trading_account_function,
           eot.trading_accnt_primary_ccy,
           eot.product_wrapper_code,
           eot.product_point_multiplier,
           eot.product_currency,
           eot.is_inst_ccy_in_fracnal_parts,
           eot.fractional_part_ratio,
           CASE
                WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 'FLAT'
                WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) > 0 THEN 'BUY'
                WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) < 0 THEN 'SELL'
                ELSE NULL
           END AS direction,
           eot.financing_ratio,
           ABS(SUM(eot.quantity*eot.normalised_direction_mulplr)) AS quantity,
           eot.quantity_currency,

           case when eot.platform = 'MMCFD' then abs(sum(eot.margin + nvl(eot.margin_secondary * reval.mid_price,0)))
             else null end as margin, -- NG should follow existing margin tiers logic
           eot.margin_currency,
           ABS(SUM(eot.amount*eot.normalised_direction_mulplr)) AS amount,
           eot.amount_currency,

           case when eot.platform = 'MMCFD' then abs(sum((eot.margin * case when eot.margin_currency = eot.trading_accnt_primary_ccy then 1 else reval3.mid_price end) + nvl(eot.margin_secondary * case when eot.margin_secondary_ccy = eot.trading_accnt_primary_ccy then 1 else reval2.mid_price end,0)))
                else null end as mrgn_in_trdng_accnt_prmry_ccy, -- NG should follow existing margin tiers logic

           abs(sum(eot.amnt_in_trdng_accnt_prmry_ccy * eot.normalised_direction_mulplr)) as amnt_in_trdng_accnt_prmry_ccy, -- Adding to EOT package via BER-1305. Calc should be same as NG.
          /* case when eot.platform = 'MMCFD' then ABS(SUM(eot.amount * case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval4.mid_price end *eot.normalised_direction_mulplr))
             else abs(sum(eot.amnt_in_trdng_accnt_prmry_ccy * eot.normalised_direction_mulplr)) end AS amnt_in_tapc_2, -- temp calc to show what it would be.*/

           ROUND(
                  CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                       ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.open_trade_price)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
                  END,8) AS open_trade_price_avg,

           ROUND(
                  CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                       ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.opening_trade_amount_fx_rate)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
                  END,8) AS opening_trade_amnt_fx_rate_avg,    -- eot.opening_trade_amount_fx_rate is null from source, so expect this to be null

           case when eot.margin_currency = eot.trading_accnt_primary_ccy then 1 else reval3.mid_price end as open_trade_margin_fx_rate ,

           eot.quantity_designator,

           ROUND(
                  CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN NULL
                       ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.open_trade_quantity_fx_rate)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
                  END,8) AS open_trade_qty_fx_rate_avg,   -- eot.open_trade_quantity_fx_rate is null from source, so expect this to be null

           ROUND(
                  CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                       ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.normalised_open_price)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
                  END,8) AS normalised_open_price_avg,

           CASE
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN  0
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) > 0 THEN  1
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) < 0 THEN -1
           END AS normalised_direction_mulplr,

           ABS(SUM(eot.normalised_open_qty_trad_ccy*eot.normalised_direction_mulplr)) AS normalised_open_qty_trad_ccy,

           ABS(SUM(eot.normalised_open_val_trad_ccy*eot.normalised_direction_mulplr)) AS normalised_open_val_trad_ccy,

           eot.normalised_trading_ccy,
           eot.normalised_margin_type,
           eot.normalised_margin_rqrmnt,

           ROUND(
                  CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                       ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.eod_price)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
                  END,8) AS eod_price_avg,

           ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) AS eod_value, -- null at source. should show correct values after BER-1305 implemented
           /*case when eot.platform = 'MMCFD' then abs(sum(eot.amount*eot.normalised_direction_mulplr))
                else ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) end as eod_value_new, --Adding to EOT package via BER-1305. This is only temp calc to show what it would be.*/
           eot.eod_value_ccy,-- null at source. should show correct values after BER-1305 implemented
          /* case when eot.platform = 'MMCFD' then eot.amount_currency
             else eot.eod_value_ccy end as eod_value_ccy_new,--Adding to EOT package via BER-1305. This is only temp calc to show what it would be.*/
           ABS(SUM(eot.eod_value_in_accnt_ccy*eot.normalised_direction_mulplr)) AS eod_value_in_accnt_ccy,-- null at source. should show correct values after BER-1305 implemented
         /*  case when eot.platform = 'MMCFD' then abs(sum(eot.amount * case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval4.ask_price end))
             else ABS(SUM(eot.eod_value_in_accnt_ccy*eot.normalised_direction_mulplr)) end as eod_value_tapc_new, -- Adding to EOT package via BER-1305. This is only temp calc to show what it would be..
                                                                                                                --Using ask price as this is logic used for NG EoD*/

           --Unrealised pnl = eod value - open value. Cannot use value from eod_open_trades, because M2M by definition means we do not have unrealised pnl at eod snapshot.
           ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) - ABS(SUM(eot.normalised_open_val_trad_ccy*eot.normalised_direction_mulplr)) as unrealised_pnl,

           /*(case when eot.platform = 'MMCFD' then abs(sum(eot.amount*eot.normalised_direction_mulplr))
                else ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) end ) -  ABS(SUM(eot.normalised_open_val_trad_ccy*eot.normalised_direction_mulplr)) as unrealised_pnl_2,*/

            eot.amount_currency as unrealised_pnl_ccy,

           (ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) - ABS(SUM(eot.normalised_open_val_trad_ccy*eot.normalised_direction_mulplr))) *
           (case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval4.ask_price end) as unrealised_pnl_in_accnt_ccy,

           /*((case when eot.platform = 'MMCFD' then abs(sum(eot.amount*eot.normalised_direction_mulplr))
                else ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) end ) -  ABS(SUM(eot.normalised_open_val_trad_ccy*eot.normalised_direction_mulplr))) *
           (case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval4.ask_price end) as unrealised_pnl_in_accnt_ccy_2,
           */
           case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval4.ask_price end AS cnvrsn_rate_to_accnt_ccy_avg, -- rate to convert unrealised pnl into tapc.

           --new columns
           eot.mm_account_id,
           eot.mm_instrument_id,
           eot.counterparty_id,
           ---NEW 25-02-2015
          NULL AS position_margin,
          NULL AS prime_margin,
          NULL AS prime_margin_in_tapc,
          NULL AS position_margin_in_tapc,
          NULL AS quantity_standard,
          NULL AS quantity_gslo,
          NULL AS trading_scope


    from bi_ods.eod_open_trades eot

    left join eod_core_prices reval on reval.fxr_from_ccy = eot.margin_secondary_ccy
                               and reval.fxr_to_ccy = eot.margin_currency
                               and reval.business_date = eot.business_date

    left join eod_core_prices reval2 on reval2.fxr_from_ccy = eot.margin_secondary_ccy
                               and reval2.fxr_to_ccy = eot.trading_accnt_primary_ccy
                               and reval2.business_date = eot.business_date

    left join eod_core_prices reval3 on reval3.fxr_from_ccy = eot.margin_currency
                               and reval3.fxr_to_ccy = eot.trading_accnt_primary_ccy
                               and reval3.business_date = eot.business_date

    left join eod_core_prices reval4 on reval4.fxr_from_ccy = eot.amount_currency
                               and reval4.fxr_to_ccy = eot.trading_accnt_primary_ccy
                               and reval4.business_date = eot.business_date

    where eot.platform = 'MMCFD'
    and eot.business_date = p_business_date
    AND eot.Reporting_Date= p_reporting_date
    group by eot.trading_account_id,
           eot.trading_account_type,
           eot.record_source,
           eot.business_date,
           eot.product_instrument_code,
           eot.effective_start_timestamp,
           eot.platform,
           eot.reporting_date,
           eot.snapshot_time,
           eot.trading_account_function,
           eot.trading_accnt_primary_ccy,
           eot.product_wrapper_code,
           eot.product_point_multiplier,
           eot.product_currency,
           eot.is_inst_ccy_in_fracnal_parts,
           eot.fractional_part_ratio,
           eot.financing_ratio,
           eot.quantity_currency,
           eot.margin_currency,
           eot.amount_currency,
           case when eot.margin_currency = eot.trading_accnt_primary_ccy then 1 else reval3.mid_price end,
           eot.quantity_designator,
           eot.normalised_trading_ccy,
           eot.normalised_margin_type,
           eot.normalised_margin_rqrmnt,
           eot.eod_value_ccy,
           eot.mm_account_id,
           eot.mm_instrument_id,
           eot.counterparty_id,
           eot.unrealised_pnl_ccy,
           case when eot.amount_currency = eot.trading_accnt_primary_ccy then 1 else reval4.ask_price END;

     /*

     Commented against story BER-2179 disabling Speedbets (Countdowns) load into bi_ods.eod_open_positions

     ELSIF(p_platform='NG' AND p_record_source='SPEEDBET') THEN


    INSERT INTO Eod_Open_Positions
    WITH accounts AS
         (
         SELECT + MATERIALIZE
                trading_account_id,
                trading_account_type,
                account_function            as trading_account_function,
                currency                    AS trading_account_ccy,
                UPPER(product_schm_cd)      AS product_schema_code,
                UPPER(trading_risk_schm_cd) AS trading_risk_schema_code,
                UPPER(price_schema_code)    AS default_price_schema_code,
                UPPER(closeout_schema_code) AS closeout_schema_code,
                independent_margin_amount
         FROM bi_ods.trading_accounts
         WHERE source_platform      = 'NG'
           AND trading_account_type = 'CUSTOMER'
         ),

         eod_core_prices AS
         (
         SELECT + MATERIALIZE
                business_date,
                instrument_code,
                price_band,
                bid_price,
                ask_price,
                mid_price,
                fxr_from_ccy,
                fxr_to_ccy,
                inst_rank
           FROM
                (--CORE PRICES
                SELECT business_date,
                       instrument_code,
                       'CMC-STANDARD' AS price_band,
                       bid_price,
                       ask_price,
                       mid_price,
                       fxr_from_ccy,
                       fxr_to_ccy,

                       --Rank to ensure only 1 price per business date is returned
                       ROW_NUMBER () OVER (PARTITION BY (CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 6)
                                                         ELSE instrument_code
                                                         END) ORDER BY business_date desc, quote_time DESC
                                          ) AS inst_rank
                  FROM (
                           (--CORE PRICES FROM bi_ods.prices
                           SELECT business_date,
                                  quote_time,
                                  price_symbol,
                                  instrument_code,
                                  bid_price,
                                  ask_price,
                                  (bid_price + ask_price)/2 AS mid_price,

                                  CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 3)
                                       ELSE NULL
                                   END AS fxr_from_ccy,

                                  CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 4, 3)
                                       ELSE NULL
                                   END AS fxr_to_ccy

                           FROM bi_ods.prices
                           WHERE platform = 'NG'
                             AND instrument_code NOT LIKE '%/CAR'
                             AND business_date <= p_business_date
                           )
                           UNION ALL
                           (--CORE PRICES FROM bi_ods.prices_h
                           SELECT business_date,
                                  quote_time,
                                  price_symbol,
                                  instrument_code,
                                  bid_price,
                                  ask_price,
                                  (bid_price + ask_price)/2 AS mid_price,

                                  CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 1, 3)
                                       ELSE NULL
                                   END AS fxr_from_ccy,

                                  CASE WHEN SUBSTR(instrument_code, -4, 4 ) = '/FXR' THEN SUBSTR(price_symbol, 4, 3)
                                       ELSE NULL
                                   END AS fxr_to_ccy


                           FROM bi_ods.prices_h ph
                           WHERE platform = 'NG'
                             AND instrument_code NOT LIKE '%/CAR'
                             AND business_date <= p_business_date
                           )
                       )
                ) WHERE inst_rank = 1
         )

    SELECT
    po.trading_account_id,
    po.trading_account_type,
    po.record_source,
    po.business_date,
    po.product_instrument_code,
    SYSTIMESTAMP as logical_load_timestamp,
    'BI_ODS' Created_By,
    SYSTIMESTAMP,
    'BI_ODS' Updated_By,
    SYSTIMESTAMP,
    po.Effective_Start_Timestamp,
    po.platform,
    po.reporting_date,
    po.snapshot_time,
    po.trading_account_function,
    po.trading_accnt_primary_ccy,
    po.product_wrapper_code,
    po.product_point_multiplier,
    po.product_currency,
    po.is_inst_ccy_in_fracnal_parts,
    po.fractional_part_ratio,


    CASE
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 'FLAT'
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) > 0 THEN 'BUY'
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) < 0 THEN 'SELL'
        ELSE NULL
     END AS direction,

    po.financing_ratio,
    SUM(po.quantity) AS quantity,
    po.quantity_currency,
    SUM(po.margin) AS margin,
    po.margin_currency AS margin_currency,
    SUM(po.amount) AS amount,
    po.amount_currency,
    SUM(po.mrgn_in_trdng_accnt_prmry_ccy) AS mrgn_in_trdng_accnt_prmry_ccy,

    SUM(po.amnt_in_trdng_accnt_prmry_ccy) AS amnt_in_trdng_accnt_prmry_ccy,


    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.open_trade_price)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS open_trade_price_avg,

    NULL AS opening_trade_amnt_fx_rate_avg,

    NULL AS open_trade_margin_fx_rate,


    po.quantity_designator,

    NULL AS open_trade_qty_fx_rate_avg,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.normalised_open_price)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS normalised_open_price_avg,

    CASE
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN  0
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) > 0 THEN  1
        WHEN SUM(po.quantity*po.normalised_direction_mulplr) < 0 THEN -1
     END AS normalised_direction_mulplr,


    ABS(SUM(po.normalised_open_qty_trad_ccy*po.normalised_direction_mulplr)) AS normalised_open_qty_trad_ccy,

    ABS(SUM(po.normalised_open_val_trad_ccy*po.normalised_direction_mulplr)) AS normalised_open_val_trad_ccy,

    po.normalised_trading_ccy,
    NULL AS normalised_margin_type,

    NULL AS normalised_margin_rqrmnt,

    ROUND(
    CASE WHEN SUM(po.quantity*po.normalised_direction_mulplr) = 0 THEN 0
         ELSE SUM(po.quantity*po.normalised_direction_mulplr*po.eod_price)/ SUM(po.quantity*po.normalised_direction_mulplr)
     END,8) AS eod_price_avg,

    SUM(po.eod_value) AS eod_value,
    po.eod_value_ccy,
    SUM(po.eod_value_in_accnt_ccy) AS eod_value_in_accnt_ccy,

    NULL AS unrealised_pnl,

    NULL AS unrealised_pnl_ccy,
    NULL AS unrealised_pnl_in_accnt_ccy,

    NULL AS cnvrsn_rate_to_accnt_ccy_avg,

    NULL AS MM_ACCOUNT_ID,
    NULL AS MM_INSTRUMENT_ID,
    NULL AS COUNTERPARTY_ID,
    ---NEW
    SUM(po.margin) AS position_margin,
    NULL AS prime_margin,
    NULL AS prime_margin_in_tapc,
    SUM(po.margin) AS position_margin_in_tapc,
    SUM(po.quantity) AS QUANTITY_STANDARD,
    NULL AS QUANTITY_GSLO,
    po.trading_scope

    FROM bi_ods.eod_open_trades po

    WHERE po.platform = 'NG'
      AND po.record_source = 'SPEEDBET'
      AND po.business_date = p_business_date



    GROUP BY
    po.platform,
    po.snapshot_time,
    po.business_date,
    po.reporting_date,
    po.Effective_Start_Timestamp,
    po.trading_account_id,
    po.trading_account_type,
    po.trading_account_function,
    po.trading_scope,
    po.product_instrument_code,
    po.product_wrapper_code,
    po.product_point_multiplier,
    po.product_currency,
    po.is_inst_ccy_in_fracnal_parts,
    po.fractional_part_ratio,
    po.financing_ratio,
    po.quantity_currency,
    po.margin_currency,
    po.amount_currency,
    po.quantity_designator,
    po.trading_accnt_primary_ccy,
    po.normalised_trading_ccy,
    po.eod_value_ccy,
    po.unrealised_pnl_ccy,
    po.record_source;

    */

    END IF;

 EXCEPTION
   WHEN DUP_VAL_ON_INDEX THEN
     logger.logger.SEVERE(logger.logger.error_backtrace);
     logger.logger.set_module(NULL);
     raise_application_error(-20004, logger.logger.error_backtrace);
 END;



  EXCEPTION
    WHEN OTHERS THEN
        logger.logger.SEVERE(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);



  END insert_eod_open_positions;

  -- ===================================================================================
  -- upd_eod_positions_metatrader
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the upnl values for MT4 accounts
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_OPEN_TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE upd_eod_positions_metatrader (p_business_date    IN DATE,
                                          p_reporting_date   IN DATE,
                                          p_platform         VARCHAR2,
                                          p_record_source    VARCHAR2
                                          ) IS

  lv_logical_load_timestamp   TIMESTAMP(6):= systimestamp;

  BEGIN

    EXECUTE IMMEDIATE 'TRUNCATE TABLE eod_open_positions_mt_stg';

  INSERT INTO bi_ods.eod_open_positions_mt_stg (
    SELECT * FROM (
    WITH
    mt_accounts AS
    (
      SELECT trading_account_id, trading_account_type, currency AS trading_account_currency, instrument_schema_code, closeout_schema_code, fxr_schema_code
        FROM bi_ods.trading_accounts
       WHERE source_platform = 'MT4'
         AND account_type = 'MetaTrader'
         AND closeout_schema_code IN (SELECT closeout_schema_code
                                        FROM bi_ods.accnt_mon_region_notif_levls
                                       WHERE standard_margin_calc_strategy IN ('LARGERSIDEWITHOPENINGPRICE')
                                         AND unrealized_pnl_calc_strategy  IN ('METATRADER'))
    ),
    accnt_mon_lvls AS
    (
          SELECT closeout_schema_code,
                 prime_margin_buffer,
                 min_prime_margin_type,
                 min_prime_margin_value,
                 standard_margin_calc_strategy,
                 unrealized_pnl_calc_strategy
            FROM bi_ods.accnt_mon_region_notif_levls
    ),
    psmd_defaults AS
    (
          SELECT *
            FROM bi_ods.product_settings_margin_defs psmd
           WHERE psmd.instrument_schema_code = 'Default'
    ),
    psmd_non_defaults AS
    (
          SELECT *
            FROM bi_ods.product_settings_margin_defs psmd
           WHERE psmd.instrument_schema_code != 'Default'
    ),
    eot_open_trade_margin AS
    (
          SELECT eot.reporting_date,
                 eot.business_date,
                 eot.trading_account_id,
                 eot.trading_account_type,
                 eot.product_instrument_code,
                 eot.product_wrapper_code,
                 eot.direction,
                 eot.quantity,
                 eot.normalised_direction_mulplr,
                 eot.amount,
                 eot.amnt_in_trdng_accnt_prmry_ccy,
                 eot.open_trade_price,
                 eot.opening_trade_amount_fx_rate,
                 eot.normalised_open_price,
                 eot.normalised_open_qty_trad_ccy,
                 eot.normalised_open_val_trad_ccy,
                 eot.eod_value,
                 eot.eod_value_in_accnt_ccy,
                 mta.instrument_schema_code, mta.trading_account_currency,
                 CASE WHEN psmd_nd.product_setting_code IS NOT NULL
                           THEN psmd_nd.product_setting_code
                      ELSE psmd_d.product_setting_code
                 END AS product_setting_code,
                 CASE WHEN psmd_nd.product_setting_code IS NOT NULL
                           THEN psmd_nd.trading_account_ccy
                      ELSE psmd_d.trading_account_ccy
                 END AS ps_trading_account_ccy,
                 CASE WHEN psmd_nd.product_setting_code IS NOT NULL
                           THEN psmd_nd.tier1_boundary
                      ELSE psmd_d.tier1_boundary
                 END AS tier1_boundary,
                 CASE WHEN psmd_nd.product_setting_code IS NOT NULL
                           THEN psmd_nd.tier1_rate
                      ELSE psmd_d.tier1_rate
                 END AS tier1_rate,
                 CASE WHEN psmd_nd.product_setting_code IS NOT NULL
                           THEN psmd_nd.instrument_schema_code
                      ELSE psmd_d.instrument_schema_code
                 END AS instrument_schema_code,
                 eot.quantity
                       * eot.open_trade_price
                       * (CASE WHEN psmd_nd.product_setting_code IS NOT NULL
                                    THEN psmd_nd.tier1_rate
                               ELSE psmd_d.tier1_rate
                          END)
                       * (CASE WHEN eot.product_wrapper_code = 'X-A' AND eot.is_inst_ccy_in_fracnal_parts = 'YES'
                                    THEN eot.fractional_part_ratio
                               ELSE 1
                          END)
                       * (CASE WHEN eot.product_wrapper_code = 'A-EOVH'
                                    THEN eot.product_point_multiplier
                               ELSE 1
                          END)
                 AS mt4_open_trade_margin,
                 mta.trading_account_currency,

                 /*BER-4832*/
                 --NVL(eot.forced_margin_fx_rate,NVL(reval.mid_price,1)) as tac_conversion_rate,
                 CASE WHEN eot.product_wrapper_code = 'X-A'
                           THEN NVL(eot.forced_margin_fx_rate,NVL(reval.mid_price,1))
                      ELSE NULL --currently no handling for SB since MT4 SB is yet to be considered by business
                 END
                 AS tac_conversion_rate,
                 /*BER-4832*/
                 eot.cnvrsn_rate_to_accnt_ccy,
                 eot.unrealised_pnl,
                 eot.unrealised_pnl_in_accnt_ccy,
                 eot.eod_price
            FROM bi_ods.eod_open_trades eot
            JOIN mt_accounts mta
              ON eot.trading_account_id = mta.trading_account_id
             AND eot.trading_account_type = mta.trading_account_type
       LEFT JOIN accnt_mon_lvls aml
              ON mta.closeout_schema_code = aml.closeout_schema_code
            JOIN bi_ods.products prd
              ON eot.product_instrument_code = prd.instrument_code
             AND eot.product_wrapper_code = prd.wrapper_code
             AND eot.platform = prd.platform
            JOIN psmd_defaults psmd_d
              ON prd.product_setting_code = psmd_d.product_setting_code
             AND CASE WHEN prd.wrapper_code = 'A-EOVH'
                           THEN mta.trading_account_currency
                      WHEN prd.wrapper_code = 'X-A'
                           THEN 'NA'
                 END = psmd_d.trading_account_ccy
       LEFT JOIN psmd_non_defaults psmd_nd
              ON prd.product_setting_code = psmd_nd.product_setting_code
             AND CASE WHEN prd.wrapper_code = 'A-EOVH'
                           THEN mta.trading_account_currency
                      WHEN prd.wrapper_code = 'X-A'
                           THEN 'NA'
                 END = psmd_nd.trading_account_ccy
             AND mta.instrument_schema_code = psmd_nd.instrument_schema_code
       LEFT JOIN eod_pos_banded_revals_stg reval
              ON eot.product_currency = reval.from_ccy
             AND mta.trading_account_currency = reval.to_ccy
             AND NVL(mta.fxr_schema_code,'CMC-Standard') = reval.band
           WHERE eot.reporting_date = p_reporting_date
             AND eot.business_date  = p_business_date
    )
      SELECT eot.reporting_date,
             eot.business_date,
             eot.trading_account_id,
             eot.trading_account_type,
             eot.product_instrument_code,
             eot.product_wrapper_code,
             TRUNC(GREATEST(SUM(CASE WHEN eot.direction = 'BUY'  THEN eot.mt4_open_trade_margin ELSE 0 END),
                      SUM(CASE WHEN eot.direction = 'SELL' THEN eot.mt4_open_trade_margin ELSE 0 END)),2)
                 AS lrgr_sd_opc_mrgn,
             TRUNC(GREATEST(SUM(CASE WHEN eot.direction = 'BUY'  THEN (eot.mt4_open_trade_margin * eot.tac_conversion_rate) ELSE 0 END),
                      SUM(CASE WHEN eot.direction = 'SELL' THEN (eot.mt4_open_trade_margin * eot.tac_conversion_rate) ELSE 0 END)),2)
                 AS lrgr_sd_opc_mrgn_tac,
             (NVL(TRUNC(GREATEST(SUM(CASE WHEN eot.direction = 'BUY'  THEN eot.mt4_open_trade_margin ELSE 0 END),
                      SUM(CASE WHEN eot.direction = 'SELL' THEN eot.mt4_open_trade_margin ELSE 0 END)),2) ,0))
                      /(ABS(MAX(eot.eod_value*eot.normalised_direction_mulplr))) AS normalised_margin_rqrmnt,
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 'FLAT'
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) > 0 THEN 'BUY'
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) < 0 THEN 'SELL'
                  ELSE NULL
              END AS direction,
             ABS(SUM(eot.quantity*eot.normalised_direction_mulplr)) AS quantity,
             ABS(SUM(eot.amount*eot.normalised_direction_mulplr)) AS amount,
             ABS(SUM(eot.amnt_in_trdng_accnt_prmry_ccy*eot.normalised_direction_mulplr)) AS amnt_in_trdng_accnt_prmry_ccy,
             ROUND(
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                  ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.open_trade_price)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
              END,8) AS open_trade_price_avg,
             ROUND(
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                  ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.opening_trade_amount_fx_rate)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
              END,8) AS opening_trade_amnt_fx_rate_avg,
             GREATEST(SUM(CASE WHEN eot.direction = 'BUY'  THEN (eot.mt4_open_trade_margin * eot.tac_conversion_rate) ELSE 0 END),
                      SUM(CASE WHEN eot.direction = 'SELL' THEN (eot.mt4_open_trade_margin * eot.tac_conversion_rate) ELSE 0 END))
             / GREATEST(SUM(CASE WHEN eot.direction = 'BUY'  THEN eot.mt4_open_trade_margin ELSE 0 END),
                        SUM(CASE WHEN eot.direction = 'SELL' THEN eot.mt4_open_trade_margin ELSE 0 END)) AS open_trade_margin_fx_rate,
             ROUND(
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                  ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.normalised_open_price)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
              END,8) AS normalised_open_price_avg,
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN  0
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) > 0 THEN  1
                  WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) < 0 THEN -1
              END AS normalised_direction_mulplr,
             ABS(SUM(eot.normalised_open_qty_trad_ccy*eot.normalised_direction_mulplr)) AS normalised_open_qty_trad_ccy,
             ABS(SUM(eot.normalised_open_val_trad_ccy*eot.normalised_direction_mulplr)) AS normalised_open_val_trad_ccy,
             ROUND(
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                  ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.eod_price)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
              END,8) AS eod_price_avg,
             ABS(SUM(eot.eod_value*eot.normalised_direction_mulplr)) AS eod_value,
             ABS(SUM(eot.eod_value_in_accnt_ccy*eot.normalised_direction_mulplr)) AS eod_value_in_accnt_ccy,
             SUM(eot.unrealised_pnl) AS unrealised_pnl,
             SUM(eot.unrealised_pnl_in_accnt_ccy) AS unrealised_pnl_in_accnt_ccy,
             ROUND(
             CASE WHEN SUM(eot.quantity*eot.normalised_direction_mulplr) = 0 THEN 0
                  ELSE SUM(eot.quantity*eot.normalised_direction_mulplr*eot.cnvrsn_rate_to_accnt_ccy)/ SUM(eot.quantity*eot.normalised_direction_mulplr)
              END,8) AS cnvrsn_rate_to_accnt_ccy_avg
        FROM eot_open_trade_margin eot
    GROUP BY eot.reporting_date,
             eot.business_date,
             eot.trading_account_id,
             eot.trading_account_type,
             eot.product_instrument_code,
             eot.product_wrapper_code
    ORDER BY eot.trading_account_id, eot.product_instrument_code
    ));

    MERGE INTO eod_open_positions po USING
    (SELECT DISTINCT
            reporting_date,
            business_date,
            trading_account_id,
            trading_account_type,
            product_instrument_code,
            product_wrapper_code,
            lrgr_sd_opc_mrgn,
            lrgr_sd_opc_mrgn_tac,
            normalised_margin_rqrmnt,
            direction,
            quantity,
            amount,
            amnt_in_trdng_accnt_prmry_ccy,
            open_trade_price_avg,
            opening_trade_amnt_fx_rate_avg,
            open_trade_margin_fx_rate,
            normalised_open_price_avg,
            normalised_direction_mulplr,
            normalised_open_qty_trad_ccy,
            normalised_open_val_trad_ccy,
            eod_price_avg,
            eod_value,
            eod_value_in_accnt_ccy,
            unrealised_pnl,
            unrealised_pnl_in_accnt_ccy,
            cnvrsn_rate_to_accnt_ccy_avg
       FROM eod_open_positions_mt_stg) ps
     ON (po.reporting_date                 = ps.reporting_date
     AND po.business_date                  = ps.business_date
     AND po.trading_account_id             = ps.trading_account_id
     AND po.trading_account_type           = ps.trading_account_type
     AND po.product_instrument_code        = ps.product_instrument_code
     AND po.product_wrapper_code           = ps.product_wrapper_code)
     WHEN MATCHED THEN UPDATE SET
         po.position_margin                = ps.lrgr_sd_opc_mrgn,
         po.margin                         = ps.lrgr_sd_opc_mrgn,
         po.mrgn_in_trdng_accnt_prmry_ccy  = ps.lrgr_sd_opc_mrgn_tac,
         po.position_margin_in_tapc        = ps.lrgr_sd_opc_mrgn_tac,
         po.normalised_margin_rqrmnt       = ps.normalised_margin_rqrmnt,
         po.direction                      = ps.direction,
         po.quantity                       = ps.quantity,
         po.amount                         = ps.amount,
         po.amnt_in_trdng_accnt_prmry_ccy  = ps.amnt_in_trdng_accnt_prmry_ccy,
         po.open_trade_price_avg           = ps.open_trade_price_avg,
         po.opening_trade_amnt_fx_rate_avg = ps.opening_trade_amnt_fx_rate_avg,
         po.open_trade_margin_fx_rate      = ps.open_trade_margin_fx_rate,
         po.normalised_open_price_avg      = ps.normalised_open_price_avg,
         po.normalised_direction_mulplr    = ps.normalised_direction_mulplr,
         po.normalised_open_qty_trad_ccy   = ps.normalised_open_qty_trad_ccy,
         po.normalised_open_val_trad_ccy   = ps.normalised_open_val_trad_ccy,
         po.eod_price_avg                  = ps.eod_price_avg,
         po.eod_value                      = ps.eod_value,
         po.eod_value_in_accnt_ccy         = ps.eod_value_in_accnt_ccy,
         po.unrealised_pnl                 = ps.unrealised_pnl,
         po.unrealised_pnl_in_accnt_ccy    = ps.unrealised_pnl_in_accnt_ccy,
         po.cnvrsn_rate_to_accnt_ccy_avg   = ps.cnvrsn_rate_to_accnt_ccy_avg;

  END;

  -- ===================================================================================
  -- put_eod_position_snapshots
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure maintains the helper table BI_ODS.EOD_POSITION_SNAPSHOTS
  --     which is used to aid loading of the TRADING_ACTIVITY_SUMMARY table in EDW.
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     EOD_POSITION_SNAPSHOTS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_eod_position_snapshots (p_snapshot_time        IN TIMESTAMP,
                                        p_platform             IN VARCHAR2,
                                        p_summary_type         IN VARCHAR2,
                                        p_is_processed         IN VARCHAR2,
                                        p_processed_time       IN TIMESTAMP
                                        /***** Begin Modification for V3.7(b) - BER781 *****/
                                       ,p_record_source        IN eod_open_trades.record_source%TYPE
                                        /***** End Modification for V3.7(b) *****/)
  IS

  lv_snapshot_exists NUMBER;

  BEGIN

  SELECT CASE WHEN EXISTS (SELECT 1
                           FROM bi_ods.eod_position_snapshots
                           WHERE snapshot_time = p_snapshot_time
                           AND platform = p_platform
                           /***** Begin Modification for V3.7(b) - BER781 *****/
                           AND record_source = p_record_source
                           /***** End Modification for V3.7(b) *****/)
         THEN 1 ELSE 0 END
  INTO lv_snapshot_exists FROM dual;

  IF lv_snapshot_exists = 1 THEN

    IF p_summary_type = 'TAS' THEN

    UPDATE bi_ods.eod_position_snapshots
       SET processed_time_tas = p_processed_time,
           is_processed_tas = p_is_processed
     WHERE snapshot_time = p_snapshot_time
       AND platform = p_platform
       /***** Begin Modification for V3.7(b) - BER781 *****/
       AND record_source = p_record_source
       /***** End Modification for V3.7(b) *****/;

    ELSIF p_summary_type = '1MIN' THEN

    UPDATE bi_ods.eod_position_snapshots
       SET processed_time_1min = p_processed_time,
           is_processed_1min = p_is_processed
     WHERE snapshot_time = p_snapshot_time
       AND platform = p_platform
       /***** Begin Modification for V3.7(b) - BER781 *****/
       AND record_source = p_record_source
       /***** End Modification for V3.7(b) *****/;

    ELSIF p_summary_type = 'EOD' THEN

    UPDATE bi_ods.eod_position_snapshots
       SET processed_time_tas = p_processed_time,
           is_processed_tas = p_is_processed,
           processed_time_1min = p_processed_time,
           is_processed_1min = p_is_processed
     WHERE snapshot_time = p_snapshot_time
       AND platform = p_platform
       /***** Begin Modification for V3.7(b) - BER781 *****/
       AND record_source = p_record_source
       /***** End Modification for V3.7(b) *****/;

    END IF;

  ELSE

  INSERT INTO bi_ods.eod_position_snapshots (platform,
                                             snapshot_time,
                                             is_processed_tas,
                                             is_processed_1min
                                             /***** Begin Modification for V3.7(b) - BER781 *****/
                                            ,record_source
                                             /***** End Modification for V3.7(b) *****/)
                                     VALUES (p_platform,
                                             p_snapshot_time,
                                             'N',
                                             'N'
                                             /***** Begin Modification for V3.7(b) - BER781 *****/
                                            ,p_record_source
                                             /***** End Modification for V3.7(b) *****/);

  END IF;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_eod_position_snapshots;

  -- ===================================================================================
  -- put_eod_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     LATEST_POSITIONS
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_business_date                       Business Date
  --      p_reporting_date                      Reporting Date
  --      p_position                            Position Details
  --      /***** Begin Modification for V3.7(a) - BER781 *****/
  --      p_record_source                       Record Source
  --      /***** End Modification for V3.7(a) *****/
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_eod_position_set (p_user                      IN VARCHAR2,
                                  p_effective_start_timestamp IN TIMESTAMP,
                                  p_platform                  IN VARCHAR2,
                                  p_position                  IN position_tab,
                                  p_record_source             IN eod_open_trades.record_source%TYPE,
                                  p_batch_detail              IN VARCHAR2)
  IS

    lv_reset_status             NUMBER;

    TYPE lvtab_eod_pos IS TABLE OF eod_open_trades%rowtype;
    ltab_eod_pos lvtab_eod_pos;

    TYPE lvtab_latest_positions IS TABLE OF Latest_Open_Trades%ROWTYPE;
    ltab_latest_positions lvtab_latest_positions;

    TYPE lvtab_missing_positions IS TABLE OF latest_open_trades.order_id%TYPE;
    ltab_order_id lvtab_missing_positions;

    lv_load_number        NUMBER := 0;

    CURSOR lcur_identify_duplicate IS (SELECT platform, order_id, max(effective_start_timestamp) max_effective_start_time
                                       FROM bi_ods.Latest_Open_Trades
                                       WHERE active_flag = 'YES'
                                       GROUP BY platform, order_id
                                       HAVING COUNT(*) > 1);


    lv_previous_snapshot lvtab_eod_pos := NULL;

    lv_snapshot_time            eod_open_trades.Snapshot_Time%TYPE;

    lv_has_snapshot_changed     NUMBER := 0;

    lv_business_date            eod_open_trades.business_date%TYPE;
    lv_reporting_date           eod_open_trades.reporting_date%TYPE;
    lv_loagical_load_timestamp  eod_open_trades.Logical_Load_Timestamp%TYPE := systimestamp;

    lv_trading_account_id       Eod_Open_Trades.Trading_Account_Id%TYPE;
    lv_trading_account_type     Eod_Open_Trades.Trading_Account_Type%TYPE;

    lv_current_value_seq       NUMBER;
  BEGIN


    --
    --Calculate the business date
    --

    lv_business_date := TRUNC(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;


    --Added this condition for BER-2818 Contingent Order Snapshot should only be called by the NG Record Source position
    --ODS 61/62/63
    --IF p_platform = 'NG' AND p_record_source = 'NG' THEN
      --
      --Generate Contingent Order Snapshot
      --
    --  ods_contingent_order.put_snapshot(p_snapshot_time => p_effective_start_timestamp);
    --END IF;

    IF p_platform  <> 'NG' THEN
      --
      --Clean up cash chits if they exists for that business date. This is to ensure that for MM re-run
      --we do not store the stale cash chits.
      --Assumption is that whenever we get a set of positions we will get a set of cash chits as well
      --
      DELETE eod_pstns_csh_trns_link
      WHERE platform = p_platform AND
            record_source = 'CASHCHIT' AND
            business_date = lv_business_date AND
            reporting_date = lv_reporting_date;

      DELETE cash_transaction_entries
      WHERE platform = p_platform AND
            record_source = 'CASHCHIT' AND
            business_date = cast(lv_business_date as date) AND
            reporting_date = cast(lv_reporting_date as date);

      DELETE cash_transactions
      WHERE platform = p_platform AND
            record_source = 'CASHCHIT' AND
            business_date = cast(lv_business_Date as date) AND
            reporting_date = cast(lv_reporting_date as date);

    END IF;

    /*
    --
      This is to select the positions into a structure in which they will be stored
      This is done so that all the calculations can be performed
      Once the data is ready it will be bulk inserted into the table
    --
    */
  IF p_platform='NG' THEN

    SELECT  p_platform,
            order_id,
            snapshot_time,
            SYSTIMESTAMP,
            P_USER,
            SYSTIMESTAMP,
            P_USER,
            SYSTIMESTAMP,
            p_effective_start_timestamp,
            lv_business_Date,
            lv_reporting_date,
            mm_value_date,
            trading_account_code,
            trading_account_type,
            trading_account_function,
            trading_account_codifier,
            mm_account_id,
            product_instrument_code,
            product_wrapper_code,
            mm_instrument_id,
            product_generation,
            product_point_multiplier,
            product_currency,
            product_financing_rat_max,
            product_schema_code,
            custominfo_virt_portcode,
            direction,
            financing_ratio,
            quantity,
            quantity_currency,
            margin,
            margin_currency,
            amount,
            amount_currency,
            margin_secondary,
            margin_secondary_ccy,
            margin_in_trad_acnt_prim_ccy,
            amount_in_trad_acnt_prim_ccy,
            open_trade_id,
            open_trade_price,
            open_trade_margin_fx_rate,
            open_time,
            last_modified_time,
            quantity_designator,
            trading_accnt_primary_ccy,
            opentrade_quantity_fx_rate,
            price_designator,
            is_inst_ccy_in_fracnal_parts,
            fractional_part_ratio,
            normalised_order_type,
            normalised_open_price,
            normalised_direction_mulplr,
            normalised_open_qty_trad_ccy,
            normalised_open_val_trad_ccy,
            normalised_trading_ccy,
            normalised_margin_type,
            normalised_margin_reqrment,
            mm_backoffice_ref,
            is_primary,
            eod_price,
            counterparty_id,
            opening_trade_amount_fx_rate,
            opening_trade_app_to_units,
            is_automatically_rolled,
            rolled_open_trade_id,
            eod_value,
            eod_value_ccy,
            eod_value_in_accnt_ccy,
            unrealised_pnl,
            unrealised_pnl_ccy,
            unrealised_pnl_in_accnt_ccy,
            cnvrsn_rate_to_accnt_ccy,
            is_mm_old_style,
            p_record_source,
            hedge_asset_class,
            hedge_instrument_code_external,
            hedge_expiry_month_code,
            hedge_risk_bucket,
            hedge_ProfitLoss,
            hedge_Commission,
            execution_type,
            NULL AS cd_state,
            trading_scope,
            binary_type,
            settle_time,
            tenor,
            strike_price,
            strike_price_additional,
            tenor_start_time,
            open_trade_instrument_price,
            /*get_hdg_exec_commission(p_trading_account_id      => trading_account_code,
                                               p_reporting_date          => lv_reporting_date,
                                               p_product_instrument_code => product_instrument_code,
                                               p_hedge_risk_bucket       => hedge_risk_bucket,
                                               p_mm_value_date           => mm_value_date)*/
            NULL AS hedge_execution_commission,

            CASE WHEN p_record_source LIKE 'NG-HEDGE%'
                 THEN NULL
                 ELSE nrg_common.get_price_band(p_trading_account_id      => trading_account_code,
                                                p_instrument_code         => product_instrument_code)
             END AS price_band,

            hedge_opening_reference,
            hdg_redemption_date,
            hdg_current_coupon_date,
            hdg_accrued_interest_days,
            hdg_accrued_interest_amount,
            hdg_eval_accrued_intrst_days,
            hdg_eval_accrued_intrst_amnt,
            hdg_effctv_intrst_rate,
            hdg_effctv_intrst_base_amnt,
            hdg_afs_reserve_amount,
            hdg_quantity_settlement_date,
            hdg_new_effctv_intrst_bse_amt,
            hdg_new_afs_reserve_amount,
            hdg_was_manually_corrected,
            hdg_crrnt_coupon_pymnt_date,
            hdg_redemption_payment_date,
            forced_margin_fx_rate,
            trading_accnt_primary_ccy
            BULK COLLECT INTO ltab_eod_pos
            FROM TABLE(CAST(p_position AS position_tab)) pos_tab;

   ELSE

   ---MMCFD
    SELECT /*+cardinality(pos_tab,2000)*/ p_platform,
            order_id,
            snapshot_time,
            SYSTIMESTAMP,
            P_USER,
            SYSTIMESTAMP,
            P_USER,
            SYSTIMESTAMP,
            p_effective_start_timestamp,
            lv_business_Date,
            lv_reporting_date,
            mm_value_date,
            nvl(ta.Trading_Account_Id, pos_tab.trading_account_code),
            nvl(ta.Trading_Account_Type, pos_tab.trading_account_type),
            trading_account_function,
            trading_account_codifier,
            mm_account_id,
            product_instrument_code,
            product_wrapper_code,
            mm_instrument_id,
            product_generation,
            product_point_multiplier,
            product_currency,
            product_financing_rat_max,
            product_schema_code,
            custominfo_virt_portcode,
            direction,
            financing_ratio,
            quantity,
            quantity_currency,
            margin,
            margin_currency,
            amount,
            amount_currency,
            margin_secondary,
            margin_secondary_ccy,
            margin_in_trad_acnt_prim_ccy,
            amount_in_trad_acnt_prim_ccy,
            open_trade_id,
            open_trade_price,
            open_trade_margin_fx_rate,
            open_time,
            last_modified_time,
            quantity_designator,
            trading_accnt_primary_ccy,
            opentrade_quantity_fx_rate,
            price_designator,
            is_inst_ccy_in_fracnal_parts,
            fractional_part_ratio,
            normalised_order_type,
            normalised_open_price,
            normalised_direction_mulplr,
            normalised_open_qty_trad_ccy,
            normalised_open_val_trad_ccy,
            normalised_trading_ccy,
            normalised_margin_type,
            normalised_margin_reqrment,
            mm_backoffice_ref,
            is_primary,
            eod_price,
            counterparty_id,
            opening_trade_amount_fx_rate,
            opening_trade_app_to_units,
            is_automatically_rolled,
            rolled_open_trade_id,
            eod_value,
            eod_value_ccy,
            eod_value_in_accnt_ccy,
            unrealised_pnl,
            unrealised_pnl_ccy,
            unrealised_pnl_in_accnt_ccy,
            cnvrsn_rate_to_accnt_ccy,
            is_mm_old_style,
            p_record_source,
            hedge_asset_class,
            hedge_instrument_code_external,
            hedge_expiry_month_code,
            hedge_risk_bucket,
            hedge_ProfitLoss,
            hedge_Commission,
            execution_type,
            NULL AS cd_state,
            trading_scope,
            binary_type,
            settle_time,
            tenor,
            strike_price,
            strike_price_additional,
            tenor_start_time,
            open_trade_instrument_price,
            /*get_hdg_exec_commission(p_trading_account_id      => trading_account_code,
                                               p_reporting_date          => lv_reporting_date,
                                               p_product_instrument_code => product_instrument_code,
                                               p_hedge_risk_bucket       => hedge_risk_bucket,
                                               p_mm_value_date           => mm_value_date)*/
            NULL AS hedge_execution_commission,
            nrg_common.get_price_band(p_trading_account_id      => trading_account_code,
                                      p_instrument_code         => product_instrument_code)price_band,
            hedge_opening_reference,
            hdg_redemption_date,
            hdg_current_coupon_date,
            hdg_accrued_interest_days,
            hdg_accrued_interest_amount,
            hdg_eval_accrued_intrst_days,
            hdg_eval_accrued_intrst_amnt,
            hdg_effctv_intrst_rate,
            hdg_effctv_intrst_base_amnt,
            hdg_afs_reserve_amount,
            hdg_quantity_settlement_date,
            hdg_new_effctv_intrst_bse_amt,
            hdg_new_afs_reserve_amount,
            hdg_was_manually_corrected,
            hdg_crrnt_coupon_pymnt_date,
            hdg_redemption_payment_date,
            forced_margin_fx_rate,
            trading_accnt_primary_ccy
            BULK COLLECT INTO ltab_eod_pos
            FROM TABLE(CAST(p_position AS position_tab)) pos_tab,
              (SELECT trading_account_id, trading_account_type, Source_Account_Id FROM trading_accounts WHERE Source_Platform = p_platform) ta
             WHERE pos_tab.mm_account_id=ta.Source_Account_Id(+);

   END IF;


    /*
    --
      This is to delete the data from the history incase the same snapshot is replayed more than 2 times
      Generally this is a very rare chance that a snapshot is re played more than twice

      This has been done because the effective start timestamp for a snapshot is the time for which it is requested
      For market maker there can only be one snapshot a day but for Next Gen there can be many more snapshots(24 a day if required)
    --
    */

    IF p_batch_detail = 'FIRST' THEN -- BER-3731: only delete history if it's the first batch of a request

      DELETE eod_open_trades_h
       WHERE platform = p_platform
         AND record_source = p_record_source
         AND effective_start_timestamp = p_effective_start_timestamp;

     --SPEEDBET
        IF p_record_source = 'NG' THEN

            DELETE eod_open_trades_h
            WHERE platform = p_platform AND
                  record_source = 'SPEEDBET' AND
                  effective_start_timestamp = p_effective_start_timestamp;

        END IF;



    /*
    --
      Insert the old snapshot copy into history table
    --
    */

    INSERT INTO eod_open_trades_h(platform,
                                order_id,
                                snapshot_time,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp,
                                effective_end_timestamp,
                                action,
                                action_timestamp,
                                business_date,
                                reporting_date,
                                mm_value_date,
                                trading_account_id,
                                trading_account_type,
                                trading_account_function,
                                trading_account_codifier,
                                mm_account_id,
                                product_instrument_code,
                                product_wrapper_code,
                                mm_instrument_id,
                                product_generation,
                                product_point_multiplier,
                                product_currency,
                                product_financing_ratio_max,
                                product_schema_code,
                                custom_info_virt_portcode,
                                direction,
                                financing_ratio,
                                quantity,
                                quantity_currency,
                                margin,
                                margin_currency,
                                amount,
                                amount_currency,
                                margin_secondary,
                                margin_secondary_ccy,
                                mrgn_in_trdng_accnt_prmry_ccy,
                                amnt_in_trdng_accnt_prmry_ccy,
                                open_trade_id,
                                open_trade_price,
                                open_trade_margin_fx_rate,
                                open_time,
                                last_modified_time,
                                quantity_designator,
                                trading_accnt_primary_ccy,
                                open_trade_quantity_fx_rate,
                                price_designator,
                                is_inst_ccy_in_fracnal_parts,
                                fractional_part_ratio,
                                normalised_order_type,
                                normalised_open_price,
                                normalised_direction_mulplr,
                                normalised_open_qty_trad_ccy,
                                normalised_open_val_trad_ccy,
                                normalised_trading_ccy,
                                normalised_margin_type,
                                normalised_margin_rqrmnt,
                                mm_backoffice_ref,
                                is_primary,
                                eod_price,
                                counterparty_id,
                                opening_trade_amount_fx_rate,
                                opening_trade_app_to_units,
                                is_automatically_rolled,
                                rolled_open_trade_id,
                                eod_value,
                                eod_value_ccy,
                                eod_value_in_accnt_ccy,
                                unrealised_pnl,
                                unrealised_pnl_ccy,
                                unrealised_pnl_in_accnt_ccy,
                                cnvrsn_rate_to_accnt_ccy,
                                is_mm_old_style,
                                record_source,
                                hedge_asset_class,
                                hedge_instrument_code_external,
                                hedge_expiry_month_code,
                                hedge_risk_bucket,
                                hedge_profitloss,
                                hedge_commission,
                                execution_type,
                                Cd_State,
                                trading_scope,
                                binary_type,
                                settle_time,
                                tenor,
                                strike_price,
                                strike_price_additional,
                                tenor_start_time,
                                open_trade_instrument_price,
                                hedge_execution_commission,
                                price_band,
                                hedge_opening_reference,
                                hdg_redemption_date,
                                hdg_current_coupon_date,
                                hdg_accrued_interest_days,
                                hdg_accrued_interest_amount,
                                hdg_eval_accrued_intrst_days,
                                hdg_eval_accrued_intrst_amnt,
                                hdg_effctv_intrst_rate,
                                hdg_effctv_intrst_base_amnt,
                                hdg_afs_reserve_amount,
                                hdg_quantity_settlement_date,
                                hdg_new_effctv_intrst_bse_amt,
                                hdg_new_afs_reserve_amount,
                                hdg_was_manually_corrected,
                                hdg_crrnt_coupon_pymnt_date,
                                hdg_redemption_payment_date,
                                forced_margin_fx_rate,
                                trading_account_currency
                                )
                         SELECT platform,
                                order_id,
                                snapshot_time,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp,
                                p_effective_start_timestamp effective_end_timestamp,
                                'U' action,
                                SYSTIMESTAMP action_timestamp,
                                business_date,
                                reporting_date,
                                mm_value_date,
                                trading_account_id,
                                trading_account_type,
                                trading_account_function,
                                trading_account_codifier,
                                mm_account_id,
                                product_instrument_code,
                                product_wrapper_code,
                                mm_instrument_id,
                                product_generation,
                                product_point_multiplier,
                                product_currency,
                                product_financing_ratio_max,
                                product_schema_code,
                                custom_info_virt_portcode,
                                direction,
                                financing_ratio,
                                quantity,
                                quantity_currency,
                                margin,
                                margin_currency,
                                amount,
                                amount_currency,
                                margin_secondary,
                                margin_secondary_ccy,
                                mrgn_in_trdng_accnt_prmry_ccy,
                                amnt_in_trdng_accnt_prmry_ccy,
                                open_trade_id,
                                open_trade_price,
                                open_trade_margin_fx_rate,
                                open_time,
                                last_modified_time,
                                quantity_designator,
                                trading_accnt_primary_ccy,
                                open_trade_quantity_fx_rate,
                                price_designator,
                                is_inst_ccy_in_fracnal_parts,
                                fractional_part_ratio,
                                normalised_order_type,
                                normalised_open_price,
                                normalised_direction_mulplr,
                                normalised_open_qty_trad_ccy,
                                normalised_open_val_trad_ccy,
                                normalised_trading_ccy,
                                normalised_margin_type,
                                normalised_margin_rqrmnt,
                                mm_backoffice_ref,
                                is_primary,
                                eod_price,
                                counterparty_id,
                                opening_trade_amount_fx_rate,
                                opening_trade_app_to_units,
                                is_automatically_rolled,
                                rolled_open_trade_id,
                                eod_value,
                                eod_value_ccy,
                                eod_value_in_accnt_ccy,
                                unrealised_pnl,
                                unrealised_pnl_ccy,
                                unrealised_pnl_in_accnt_ccy,
                                cnvrsn_rate_to_accnt_ccy,
                                is_mm_old_style,
                                record_source,
                                hedge_asset_class,
                                hedge_instrument_code_external,
                                hedge_expiry_month_code,
                                hedge_risk_bucket,
                                hedge_profitloss,
                                hedge_commission,
                                execution_type,
                                cd_state,
                                trading_scope,
                                binary_type,
                                settle_time,
                                tenor,
                                strike_price,
                                strike_price_additional,
                                tenor_start_time,
                                open_trade_instrument_price,
                                hedge_execution_commission,
                                price_band,
                                hedge_opening_reference,
                                hdg_redemption_date,
                                hdg_current_coupon_date,
                                hdg_accrued_interest_days,
                                hdg_accrued_interest_amount,
                                hdg_eval_accrued_intrst_days,
                                hdg_eval_accrued_intrst_amnt,
                                hdg_effctv_intrst_rate,
                                hdg_effctv_intrst_base_amnt,
                                hdg_afs_reserve_amount,
                                hdg_quantity_settlement_date,
                                hdg_new_effctv_intrst_bse_amt,
                                hdg_new_afs_reserve_amount,
                                hdg_was_manually_corrected,
                                hdg_crrnt_coupon_pymnt_date,
                                hdg_redemption_payment_date,
                                forced_margin_fx_rate,
                                trading_account_currency
                          FROM  eod_open_trades
                          WHERE platform = p_platform AND
                                record_source = p_record_source AND
                                effective_start_timestamp = p_effective_start_timestamp AND
                                business_date = lv_business_Date;

  --INSERT SPEEDBET
   IF p_record_source = 'NG' THEN

    INSERT INTO eod_open_trades_h(platform,
                                order_id,
                                snapshot_time,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp,
                                effective_end_timestamp,
                                action,
                                action_timestamp,
                                business_date,
                                reporting_date,
                                mm_value_date,
                                trading_account_id,
                                trading_account_type,
                                trading_account_function,
                                trading_account_codifier,
                                mm_account_id,
                                product_instrument_code,
                                product_wrapper_code,
                                mm_instrument_id,
                                product_generation,
                                product_point_multiplier,
                                product_currency,
                                product_financing_ratio_max,
                                product_schema_code,
                                custom_info_virt_portcode,
                                direction,
                                financing_ratio,
                                quantity,
                                quantity_currency,
                                margin,
                                margin_currency,
                                amount,
                                amount_currency,
                                margin_secondary,
                                margin_secondary_ccy,
                                mrgn_in_trdng_accnt_prmry_ccy,
                                amnt_in_trdng_accnt_prmry_ccy,
                                open_trade_id,
                                open_trade_price,
                                open_trade_margin_fx_rate,
                                open_time,
                                last_modified_time,
                                quantity_designator,
                                trading_accnt_primary_ccy,
                                open_trade_quantity_fx_rate,
                                price_designator,
                                is_inst_ccy_in_fracnal_parts,
                                fractional_part_ratio,
                                normalised_order_type,
                                normalised_open_price,
                                normalised_direction_mulplr,
                                normalised_open_qty_trad_ccy,
                                normalised_open_val_trad_ccy,
                                normalised_trading_ccy,
                                normalised_margin_type,
                                normalised_margin_rqrmnt,
                                mm_backoffice_ref,
                                is_primary,
                                eod_price,
                                counterparty_id,
                                opening_trade_amount_fx_rate,
                                opening_trade_app_to_units,
                                is_automatically_rolled,
                                rolled_open_trade_id,
                                eod_value,
                                eod_value_ccy,
                                eod_value_in_accnt_ccy,
                                unrealised_pnl,
                                unrealised_pnl_ccy,
                                unrealised_pnl_in_accnt_ccy,
                                cnvrsn_rate_to_accnt_ccy,
                                is_mm_old_style,
                                record_source,
                                hedge_asset_class,
                                hedge_instrument_code_external,
                                hedge_expiry_month_code,
                                hedge_risk_bucket,
                                hedge_profitloss,
                                hedge_commission,
                                execution_type,
                                cd_state,
                                trading_scope,
                                binary_type,
                                settle_time,
                                tenor,
                                strike_price,
                                strike_price_additional,
                                tenor_start_time,
                                open_trade_instrument_price,
                                hedge_execution_commission,
                                price_band,
                                hedge_opening_reference,
                                hdg_redemption_date,
                                hdg_current_coupon_date,
                                hdg_accrued_interest_days,
                                hdg_accrued_interest_amount,
                                hdg_eval_accrued_intrst_days,
                                hdg_eval_accrued_intrst_amnt,
                                hdg_effctv_intrst_rate,
                                hdg_effctv_intrst_base_amnt,
                                hdg_afs_reserve_amount,
                                hdg_quantity_settlement_date,
                                hdg_new_effctv_intrst_bse_amt,
                                hdg_new_afs_reserve_amount,
                                hdg_was_manually_corrected,
                                hdg_crrnt_coupon_pymnt_date,
                                hdg_redemption_payment_date,
                                forced_margin_fx_rate,
                                trading_account_currency
                                )
                         SELECT platform,
                                order_id,
                                snapshot_time,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp,
                                p_effective_start_timestamp effective_end_timestamp,
                                'U' action,
                                SYSTIMESTAMP action_timestamp,
                                business_date,
                                reporting_date,
                                mm_value_date,
                                trading_account_id,
                                trading_account_type,
                                trading_account_function,
                                trading_account_codifier,
                                mm_account_id,
                                product_instrument_code,
                                product_wrapper_code,
                                mm_instrument_id,
                                product_generation,
                                product_point_multiplier,
                                product_currency,
                                product_financing_ratio_max,
                                product_schema_code,
                                custom_info_virt_portcode,
                                direction,
                                financing_ratio,
                                quantity,
                                quantity_currency,
                                margin,
                                margin_currency,
                                amount,
                                amount_currency,
                                margin_secondary,
                                margin_secondary_ccy,
                                mrgn_in_trdng_accnt_prmry_ccy,
                                amnt_in_trdng_accnt_prmry_ccy,
                                open_trade_id,
                                open_trade_price,
                                open_trade_margin_fx_rate,
                                open_time,
                                last_modified_time,
                                quantity_designator,
                                trading_accnt_primary_ccy,
                                open_trade_quantity_fx_rate,
                                price_designator,
                                is_inst_ccy_in_fracnal_parts,
                                fractional_part_ratio,
                                normalised_order_type,
                                normalised_open_price,
                                normalised_direction_mulplr,
                                normalised_open_qty_trad_ccy,
                                normalised_open_val_trad_ccy,
                                normalised_trading_ccy,
                                normalised_margin_type,
                                normalised_margin_rqrmnt,
                                mm_backoffice_ref,
                                is_primary,
                                eod_price,
                                counterparty_id,
                                opening_trade_amount_fx_rate,
                                opening_trade_app_to_units,
                                is_automatically_rolled,
                                rolled_open_trade_id,
                                eod_value,
                                eod_value_ccy,
                                eod_value_in_accnt_ccy,
                                unrealised_pnl,
                                unrealised_pnl_ccy,
                                unrealised_pnl_in_accnt_ccy,
                                cnvrsn_rate_to_accnt_ccy,
                                is_mm_old_style,
                                record_source,
                                hedge_asset_class,
                                hedge_instrument_code_external,
                                hedge_expiry_month_code,
                                hedge_risk_bucket,
                                hedge_profitloss,
                                hedge_commission,
                                execution_type,
                                cd_state,
                                trading_scope,
                                binary_type,
                                settle_time,
                                tenor,
                                strike_price,
                                strike_price_additional,
                                tenor_start_time,
                                open_trade_instrument_price,
                                hedge_execution_commission,
                                price_band,
                                hedge_opening_reference,
                                hdg_redemption_date,
                                hdg_current_coupon_date,
                                hdg_accrued_interest_days,
                                hdg_accrued_interest_amount,
                                hdg_eval_accrued_intrst_days,
                                hdg_eval_accrued_intrst_amnt,
                                hdg_effctv_intrst_rate,
                                hdg_effctv_intrst_base_amnt,
                                hdg_afs_reserve_amount,
                                hdg_quantity_settlement_date,
                                hdg_new_effctv_intrst_bse_amt,
                                hdg_new_afs_reserve_amount,
                                hdg_was_manually_corrected,
                                hdg_crrnt_coupon_pymnt_date,
                                hdg_redemption_payment_date,
                                forced_margin_fx_rate,
                                trading_account_currency
                          FROM  eod_open_trades
                          WHERE platform = p_platform AND
                                record_source = 'SPEEDBET' AND
                                effective_start_timestamp = p_effective_start_timestamp AND
                                business_date = lv_business_Date;

    END IF;
   END IF;


    /*
    --
      Since the data is now written to the history table we can now delete the data for that snapshot at once and then
      reinsert the new data
    --
    */

    /* Collect the data which is there on the current table already */
    /* This is to find out if the data has been changed from the last time it was written */

    --IF p_platform = 'NG' THEN

      /*SELECT platform,
             order_id,
             snapshot_time,
             logical_load_timestamp,
             created_by,
             create_timestamp,
             updated_by,
             update_timestamp,
             effective_start_timestamp,
             business_date,
             reporting_date,
             mm_value_date,
             trading_account_id,
             trading_account_type,
             trading_account_function,
             trading_account_codifier,
             mm_account_id,
             product_instrument_code,
             product_wrapper_code,
             mm_instrument_id,
             product_generation,
             product_point_multiplier,
             product_currency,
             product_financing_ratio_max,
             product_schema_code,
             custom_info_virt_portcode,
             direction,
             financing_ratio,
             quantity,
             quantity_currency,
             margin,
             margin_currency,
             amount,
             amount_currency,
             margin_secondary,
             margin_secondary_ccy,
             mrgn_in_trdng_accnt_prmry_ccy,
             amnt_in_trdng_accnt_prmry_ccy,
             open_trade_id,
             open_trade_price,
             open_trade_margin_fx_rate,
             open_time,
             last_modified_time,
             quantity_designator,
             trading_accnt_primary_ccy,
             open_trade_quantity_fx_rate,
             price_designator,
             is_inst_ccy_in_fracnal_parts,
             fractional_part_ratio,
             normalised_order_type,
             normalised_open_price,
             normalised_direction_mulplr,
             normalised_open_qty_trad_ccy,
             normalised_open_val_trad_ccy,
             normalised_trading_ccy,
             normalised_margin_type,
             normalised_margin_rqrmnt,
             mm_backoffice_ref,
             is_primary,
             eod_price,
             counterparty_id,
             opening_trade_amount_fx_rate,
             opening_trade_app_to_units,
             is_automatically_rolled,
             rolled_open_trade_id,
             eod_value,
             eod_value_ccy,
             eod_value_in_accnt_ccy,
             unrealised_pnl,
             unrealised_pnl_ccy,
             unrealised_pnl_in_accnt_ccy,
             cnvrsn_rate_to_accnt_ccy,
             is_mm_old_style,
             p_record_source record_source,
             hedge_asset_class,
             hedge_instrument_code_external,
             hedge_expiry_month_code,
             hedge_risk_bucket,
             hedge_profitloss,
             hedge_commission,
             execution_type,
             cd_state,
             trading_scope,
             binary_type,
             settle_time,
             tenor,
             strike_price,
             strike_price_additional,
             tenor_start_time,
             open_trade_instrument_price,
             hedge_execution_commission,
             price_band,
             hedge_opening_reference,
             hdg_redemption_date,
             hdg_current_coupon_date,
             hdg_accrued_interest_days,
             hdg_accrued_interest_amount,
             hdg_eval_accrued_intrst_days,
             hdg_eval_accrued_intrst_amnt,
             hdg_effctv_intrst_rate,
             hdg_effctv_intrst_base_amnt,
             hdg_afs_reserve_amount,
             hdg_quantity_settlement_date,
             hdg_new_effctv_intrst_bse_amt,
             hdg_new_afs_reserve_amount,
             hdg_was_manually_corrected,
             hdg_crrnt_coupon_pymnt_date,
             hdg_redemption_payment_date,
             forced_margin_fx_rate
        BULK COLLECT INTO lv_previous_snapshot
      FROM eod_open_trades
      WHERE platform = p_platform AND
            record_source = p_record_source AND
            effective_start_timestamp = p_effective_start_timestamp AND
            business_date = lv_business_Date;*/
    --END IF;

   IF p_batch_detail = 'FIRST' THEN -- BER-3731: only delete current contents if it's the first batch of a request

      DELETE eod_open_trades
       WHERE platform = p_platform AND
             record_source = p_record_source AND
             effective_start_timestamp = p_effective_start_timestamp AND
             business_date = lv_business_Date;

--SPEEDBET
       IF p_record_source = 'NG' THEN

          DELETE eod_open_trades
           WHERE platform = p_platform AND
                 record_source = 'SPEEDBET' AND
                 effective_start_timestamp = p_effective_start_timestamp AND
                 business_date = lv_business_Date;

        END IF;
    END IF;
    /*
    --
      Perform the business calculations
    --
    */

    FOR lv_count in 1.. ltab_eod_pos.count LOOP




      CASE
        WHEN ltab_eod_pos(lv_count).platform ='NG' THEN
          ltab_eod_pos(lv_count).order_id      := CASE WHEN p_record_source='NG-HEDGE-FX' THEN 'NG-HEDGE-FX-'||lv_count ELSE ltab_eod_pos(lv_count).order_id END;
          ltab_eod_pos(lv_count).business_date := trunc(ltab_eod_pos(lv_count).snapshot_time);
          ltab_eod_pos(lv_count).quantity      := ltab_eod_pos(lv_count).quantity;
          ltab_eod_pos(lv_count).amount        := ltab_eod_pos(lv_count).amount;
          ltab_eod_pos(lv_count).hedge_profitloss := CASE WHEN p_record_source='NG-HEDGE-FX' AND ltab_eod_pos(lv_count).business_date = ltab_eod_pos(lv_count).mm_value_date
                                                     THEN ltab_eod_pos(lv_count).amount
                                                     ELSE ltab_eod_pos(lv_count).hedge_profitloss END;

        ELSE
          ltab_eod_pos(lv_count).order_id      := 'EOD-'||lv_count;
          ltab_eod_pos(lv_count).business_date := ltab_eod_pos(lv_count).business_date;
          ltab_eod_pos(lv_count).quantity      := abs(ltab_eod_pos(lv_count).quantity);
          ltab_eod_pos(lv_count).amount        := abs(ltab_eod_pos(lv_count).amount);
          ltab_eod_pos(lv_count).normalised_margin_rqrmnt := ltab_eod_pos(lv_count).normalised_margin_rqrmnt ;
      END CASE;

     /*
      --
      --Calculate the Reporting Date
      --

      ltab_eod_pos(lv_count).reporting_date := bi_ods.nrg_common.get_reporting_date(ltab_eod_pos(lv_count).snapshot_time);
    */
      --
      --Counterparty Id
      --

      ltab_eod_pos(lv_count).counterparty_id := ltab_eod_pos(lv_count).counterparty_id;

      CASE
        WHEN ltab_eod_pos(lv_count).record_source LIKE 'NG-HEDGE%' THEN
         ltab_eod_pos(lv_count).trading_account_function := 'LIVE';
        ELSE
         ltab_eod_pos(lv_count).trading_account_function := ltab_eod_pos(lv_count).trading_account_function;
      END CASE;

      --
      -- Calculate Margin
      --


      --
      -- Calculate Secondary Margin
      --

      --
      --Changed for story BER-154 EOD Positions - Margin Changes(ODS)
      --

      --
      -- Calculate Open Trade Price
      --

      CASE
         WHEN ltab_eod_pos(lv_count).platform = 'NG' THEN
          ltab_eod_pos(lv_count).open_trade_price := ltab_eod_pos(lv_count).open_trade_price;
        WHEN ltab_eod_pos(lv_count).platform = 'MMSB' THEN
          IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
            ltab_eod_pos(lv_count).open_trade_price := 0;
          ELSE
            ltab_eod_pos(lv_count).open_trade_price := abs(ltab_eod_pos(lv_count).amount / ltab_eod_pos(lv_count).quantity) / ltab_eod_pos(lv_count).product_point_multiplier;
          END IF;
        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).quantity_designator = 'UNITS' THEN
          CASE
            WHEN ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts = 'YES' THEN
              IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
                ltab_eod_pos(lv_count).open_trade_price := 0;
              ELSE
                ltab_eod_pos(lv_count).open_trade_price := abs(ltab_eod_pos(lv_count).amount / ltab_eod_pos(lv_count).quantity) / (ltab_eod_pos(lv_count).product_point_multiplier * ltab_eod_pos(lv_count).fractional_part_ratio);
              END IF;
            ELSE
              IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
                ltab_eod_pos(lv_count).open_trade_price := 0;
              ELSE
                ltab_eod_pos(lv_count).open_trade_price := abs(ltab_eod_pos(lv_count).amount / ltab_eod_pos(lv_count).quantity) / ltab_eod_pos(lv_count).product_point_multiplier;
              END IF;
          END CASE;
        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNT' THEN
          IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
            ltab_eod_pos(lv_count).open_trade_price := 0;
          ELSE
            ltab_eod_pos(lv_count).open_trade_price := abs(ltab_eod_pos(lv_count).amount / ltab_eod_pos(lv_count).quantity);
          END IF;
        ELSE
          ltab_eod_pos(lv_count).open_trade_price := NULL;
      END CASE;

      --
      -- Calculate Normalised Open Price
      --

      CASE
        WHEN ltab_eod_pos(lv_count).platform = 'NG' THEN
          IF ltab_eod_pos(lv_count).product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
            ltab_eod_pos(lv_count).normalised_open_price := NULL;
          ELSE
            ltab_eod_pos(lv_count).normalised_open_price :=  ltab_eod_pos(lv_count).open_trade_price;
          END IF;
        WHEN ltab_eod_pos(lv_count).platform = 'MMSB' THEN
          IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
            ltab_eod_pos(lv_count).open_trade_price := 0;
          ELSE
            ltab_eod_pos(lv_count).normalised_open_price :=  abs(ltab_eod_pos(lv_count).amount/ltab_eod_pos(lv_count).quantity) / ltab_eod_pos(lv_count).product_point_multiplier;
          END IF;
        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).quantity_designator = 'UNITS' THEN
          CASE
            WHEN ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts = 'YES' THEN
              IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
                ltab_eod_pos(lv_count).open_trade_price := 0;
              ELSE
                ltab_eod_pos(lv_count).normalised_open_price := abs(ltab_eod_pos(lv_count).amount/ltab_eod_pos(lv_count).quantity) / (ltab_eod_pos(lv_count).product_point_multiplier * ltab_eod_pos(lv_count).fractional_part_ratio);
              END IF;
            ELSE
              IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
                ltab_eod_pos(lv_count).open_trade_price := 0;
              ELSE
                ltab_eod_pos(lv_count).normalised_open_price := abs (ltab_eod_pos(lv_count).amount/ltab_eod_pos(lv_count).quantity) / ltab_eod_pos(lv_count).product_point_multiplier;
              END IF;
          END CASE;
        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNT'   THEN

          --
          --Updated the business rule as per data lineage update by Cheng
          --

          IF ltab_eod_pos(lv_count).quantity IS NULL OR ltab_eod_pos(lv_count).quantity = 0 THEN
            ltab_eod_pos(lv_count).open_trade_price := 0;
          ELSE
            ltab_eod_pos(lv_count).normalised_open_price := abs(ltab_eod_pos(lv_count).amount/ltab_eod_pos(lv_count).quantity);
          END IF;

        ELSE
          ltab_eod_pos(lv_count).normalised_open_price := NULL;
      END CASE;

      --
      -- Calculate Normalised Open Quantity in trading account currency
      --

      CASE
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := NULL;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).record_source LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy;

        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNTPERPOINT' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := ltab_eod_pos(lv_count).quantity * ltab_eod_pos(lv_count).product_point_multiplier;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'UNITS' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          CASE
            WHEN ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts = 'NO' THEN
              ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := ltab_eod_pos(lv_count).quantity;
            ELSE
              ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := ltab_eod_pos(lv_count).quantity * ltab_eod_pos(lv_count).fractional_part_ratio;
          END CASE;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'KOUNITS' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN--BER-2850
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := ltab_eod_pos(lv_count).quantity * ltab_eod_pos(lv_count).product_point_multiplier;
         WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNT' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := ltab_eod_pos(lv_count).amount / ltab_eod_pos(lv_count).open_trade_price;
        WHEN ltab_eod_pos(lv_count).platform = 'MMSB' THEN
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := abs(ltab_eod_pos(lv_count).quantity * (ltab_eod_pos(lv_count).product_point_multiplier));
        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).quantity_designator = 'UNITS' THEN
          CASE
            WHEN ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts = 'YES' THEN
              --01/07/2015 change
              ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := abs((ltab_eod_pos(lv_count).quantity * (ltab_eod_pos(lv_count).product_point_multiplier)) *  (ltab_eod_pos(lv_count).fractional_part_ratio));
            ELSE
              ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := abs(ltab_eod_pos(lv_count).quantity * (ltab_eod_pos(lv_count).product_point_multiplier));
          END CASE;
        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNT'   THEN

          --
          --Updated the business rule based on data lineage update by Cheng
          --

          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := abs(ltab_eod_pos(lv_count).quantity);

        ELSE
          ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy := NULL;
      END CASE;

      --
      -- Calculate Normalised Open Value in trading account currency
      --

      CASE
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := NULL;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).record_source LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).normalised_open_val_trad_ccy;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'KOUNITS' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN--BER-2850
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).amount;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNTPERPOINT' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          /*BER-4841*/
          --ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).amount;
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).quantity * ltab_eod_pos(lv_count).product_point_multiplier * ltab_eod_pos(lv_count).open_trade_price;
          /*BER-4841*/
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'UNITS' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          CASE WHEN ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts = 'NO' THEN
            ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).quantity  * ltab_eod_pos(lv_count).open_trade_price;
          ELSE
            ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).quantity * ltab_eod_pos(lv_count).fractional_part_ratio* ltab_eod_pos(lv_count).open_trade_price;
          END CASE;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNT' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := ltab_eod_pos(lv_count).amount;
        WHEN ltab_eod_pos(lv_count).platform = 'MMSB'  THEN
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy :=   abs(ltab_eod_pos(lv_count).amount);

        --
        --Updated based on data lineage update by Cheng
        --

        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' THEN

          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy :=   abs(ltab_eod_pos(lv_count).amount);

        ELSE
          ltab_eod_pos(lv_count).normalised_open_val_trad_ccy := NULL;
      END CASE;

      --
      -- Calculate Normalised trading currency
      --

      CASE
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := NULL;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).record_source LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).normalised_trading_ccy;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNTPERPOINT' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).amount_currency;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'KOUNITS' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN--BER-2850
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).amount_currency;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'UNITS' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).product_currency;
        WHEN ltab_eod_pos(lv_count).platform = 'NG' AND ltab_eod_pos(lv_count).quantity_designator = 'AMOUNT' AND ltab_eod_pos(lv_count).record_source NOT LIKE 'NG-HEDGE%' THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).amount_currency;
        WHEN ltab_eod_pos(lv_count).platform = 'MMSB' THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).amount_currency;

        --
        --Updated based on data lineage update by Cheng
        --

        WHEN ltab_eod_pos(lv_count).platform = 'MMCFD' THEN
          ltab_eod_pos(lv_count).normalised_trading_ccy := ltab_eod_pos(lv_count).amount_currency;
        ELSE
          ltab_eod_pos(lv_count).normalised_trading_ccy := NULL;
      END CASE;


      /**** Begin Modification for BER 606 on 19-Feb-2014 ****/

       CASE WHEN
          ltab_eod_pos(lv_count).platform = 'MMCFD' AND ltab_eod_pos(lv_count).IS_MM_OLD_STYLE = 'YES' then
          ltab_eod_pos(lv_count).unrealised_pnl :=
          (((ltab_eod_pos(lv_count).QUANTITY * ltab_eod_pos(lv_count).NORMALISED_DIRECTION_MULPLR * - 1) * ltab_eod_pos(lv_count).EOD_PRICE) +
          ((ltab_eod_pos(lv_count).AMOUNT * ltab_eod_pos(lv_count).NORMALISED_DIRECTION_MULPLR)));
        ELSE
          --- AK due to ORA error
           ltab_eod_pos(lv_count).unrealised_pnl := NULL ;

       END CASE;

     CASE WHEN ltab_eod_pos(lv_count).record_source LIKE 'NG-HEDGE%' THEN
         ltab_eod_pos(lv_count).eod_value_in_accnt_ccy := NULL;
         ltab_eod_pos(lv_count).unrealised_pnl_in_accnt_ccy := NULL;
          ELSE
         ltab_eod_pos(lv_count).eod_value_in_accnt_ccy := ltab_eod_pos(lv_count).eod_value_in_accnt_ccy;
         ltab_eod_pos(lv_count).unrealised_pnl_in_accnt_ccy := ltab_eod_pos(lv_count).unrealised_pnl_in_accnt_ccy;
     END CASE;

     CASE WHEN ltab_eod_pos(lv_count).platform = 'NG' AND p_record_source = 'NG' THEN
         ltab_eod_pos(lv_count).margin := 0;
         ltab_eod_pos(lv_count).mrgn_in_trdng_accnt_prmry_ccy := 0;
     ELSE
       NULL;
     END CASE;

     ltab_eod_pos(lv_count).trading_scope := ltab_eod_pos(lv_count).trading_scope;

     ltab_eod_pos(lv_count).binary_type := ltab_eod_pos(lv_count).binary_type;
     ltab_eod_pos(lv_count).settle_time := ltab_eod_pos(lv_count).settle_time;
     ltab_eod_pos(lv_count).tenor := ltab_eod_pos(lv_count).tenor;
     ltab_eod_pos(lv_count).strike_price := ltab_eod_pos(lv_count).strike_price;
     ltab_eod_pos(lv_count).strike_price_additional := ltab_eod_pos(lv_count).strike_price_additional;
     ltab_eod_pos(lv_count).tenor_start_time := ltab_eod_pos(lv_count).tenor_start_time;
     ltab_eod_pos(lv_count).open_trade_instrument_price := ltab_eod_pos(lv_count).open_trade_instrument_price;

     /*IF p_platform = 'NG' THEN
        IF lv_previous_snapshot.COUNT > 0 AND lv_has_snapshot_changed = 0 THEN
          FOR lv_cnt IN 1..lv_previous_snapshot.COUNT LOOP
            IF lv_previous_snapshot(lv_cnt).order_id = ltab_eod_pos(lv_count).order_id THEN
              IF (nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).business_date, ltab_eod_pos(lv_count).business_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).reporting_date, ltab_eod_pos(lv_count).reporting_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).mm_value_date, ltab_eod_pos(lv_count).mm_value_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).trading_account_id, ltab_eod_pos(lv_count).trading_account_id) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).trading_account_type, ltab_eod_pos(lv_count).trading_account_type) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).trading_account_function, ltab_eod_pos(lv_count).trading_account_function) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).trading_account_codifier, ltab_eod_pos(lv_count).trading_account_codifier) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).mm_account_id, ltab_eod_pos(lv_count).mm_account_id) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_instrument_code, ltab_eod_pos(lv_count).product_instrument_code) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_wrapper_code, ltab_eod_pos(lv_count).product_wrapper_code) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).mm_instrument_id, ltab_eod_pos(lv_count).mm_instrument_id) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_generation, ltab_eod_pos(lv_count).product_generation) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_point_multiplier, ltab_eod_pos(lv_count).product_point_multiplier) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_currency, ltab_eod_pos(lv_count).product_currency) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_financing_ratio_max, ltab_eod_pos(lv_count).product_financing_ratio_max) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).product_schema_code, ltab_eod_pos(lv_count).product_schema_code) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).custom_info_virt_portcode, ltab_eod_pos(lv_count).custom_info_virt_portcode) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).direction, ltab_eod_pos(lv_count).direction) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).financing_ratio, ltab_eod_pos(lv_count).financing_ratio) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).quantity, ltab_eod_pos(lv_count).quantity) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).quantity_currency, ltab_eod_pos(lv_count).quantity_currency) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).margin, ltab_eod_pos(lv_count).margin) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).margin_currency, ltab_eod_pos(lv_count).margin_currency) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).amount, ltab_eod_pos(lv_count).amount) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).amount_currency, ltab_eod_pos(lv_count).amount_currency) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).margin_secondary, ltab_eod_pos(lv_count).margin_secondary) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).margin_secondary_ccy, ltab_eod_pos(lv_count).margin_secondary_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).mrgn_in_trdng_accnt_prmry_ccy, ltab_eod_pos(lv_count).mrgn_in_trdng_accnt_prmry_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).amnt_in_trdng_accnt_prmry_ccy, ltab_eod_pos(lv_count).amnt_in_trdng_accnt_prmry_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).open_trade_id, ltab_eod_pos(lv_count).open_trade_id) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).open_trade_price, ltab_eod_pos(lv_count).open_trade_price) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).open_trade_margin_fx_rate, ltab_eod_pos(lv_count).open_trade_margin_fx_rate) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).open_time, ltab_eod_pos(lv_count).open_time) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).last_modified_time, ltab_eod_pos(lv_count).last_modified_time) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).quantity_designator, ltab_eod_pos(lv_count).quantity_designator) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).trading_accnt_primary_ccy, ltab_eod_pos(lv_count).trading_accnt_primary_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).open_trade_quantity_fx_rate, ltab_eod_pos(lv_count).open_trade_quantity_fx_rate) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).price_designator, ltab_eod_pos(lv_count).price_designator) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).is_inst_ccy_in_fracnal_parts, ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).fractional_part_ratio, ltab_eod_pos(lv_count).fractional_part_ratio) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_order_type, ltab_eod_pos(lv_count).normalised_order_type) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_open_price, ltab_eod_pos(lv_count).normalised_open_price) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_direction_mulplr, ltab_eod_pos(lv_count).normalised_direction_mulplr) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_open_qty_trad_ccy, ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_open_val_trad_ccy, ltab_eod_pos(lv_count).normalised_open_val_trad_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_trading_ccy, ltab_eod_pos(lv_count).normalised_trading_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_margin_type, ltab_eod_pos(lv_count).normalised_margin_type) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).normalised_margin_rqrmnt, ltab_eod_pos(lv_count).normalised_margin_rqrmnt) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).mm_backoffice_ref, ltab_eod_pos(lv_count).mm_backoffice_ref) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).is_primary, ltab_eod_pos(lv_count).is_primary) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).eod_price, ltab_eod_pos(lv_count).eod_price) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).counterparty_id, ltab_eod_pos(lv_count).counterparty_id) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).opening_trade_amount_fx_rate, ltab_eod_pos(lv_count).opening_trade_amount_fx_rate) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).opening_trade_app_to_units, ltab_eod_pos(lv_count).opening_trade_app_to_units) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).is_automatically_rolled, ltab_eod_pos(lv_count).is_automatically_rolled) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).rolled_open_trade_id, ltab_eod_pos(lv_count).rolled_open_trade_id) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).eod_value,ltab_eod_pos(lv_count).eod_value) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).eod_value_ccy,ltab_eod_pos(lv_count).eod_value_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).eod_value_in_accnt_ccy,ltab_eod_pos(lv_count).eod_value_in_accnt_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).unrealised_pnl,ltab_eod_pos(lv_count).unrealised_pnl) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).unrealised_pnl_ccy,ltab_eod_pos(lv_count).unrealised_pnl_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).unrealised_pnl_in_accnt_ccy,ltab_eod_pos(lv_count).unrealised_pnl_in_accnt_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).cnvrsn_rate_to_accnt_ccy,ltab_eod_pos(lv_count).cnvrsn_rate_to_accnt_ccy) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).is_mm_old_style,ltab_eod_pos(lv_count).is_mm_old_style) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_asset_class,ltab_eod_pos(lv_count).hedge_asset_class) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_instrument_code_external,ltab_eod_pos(lv_count).hedge_instrument_code_external) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_expiry_month_code,ltab_eod_pos(lv_count).hedge_expiry_month_code) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_risk_bucket,ltab_eod_pos(lv_count).hedge_risk_bucket) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_profitloss,ltab_eod_pos(lv_count).hedge_profitloss) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_commission,ltab_eod_pos(lv_count).hedge_commission) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).execution_type,ltab_eod_pos(lv_count).execution_type) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).cd_state,ltab_eod_pos(lv_count).cd_state) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).trading_scope,ltab_eod_pos(lv_count).trading_scope) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).binary_type, ltab_eod_pos(lv_count).binary_type) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).settle_time, ltab_eod_pos(lv_count).settle_time) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).tenor, ltab_eod_pos(lv_count).tenor) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).strike_price, ltab_eod_pos(lv_count).strike_price) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).strike_price_additional, ltab_eod_pos(lv_count).strike_price_additional) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).tenor_start_time, ltab_eod_pos(lv_count).tenor_start_time) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).open_trade_instrument_price, ltab_eod_pos(lv_count).open_trade_instrument_price) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hedge_opening_reference, ltab_eod_pos(lv_count).hedge_opening_reference) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_redemption_date, ltab_eod_pos(lv_count).hdg_redemption_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_current_coupon_date, ltab_eod_pos(lv_count).hdg_current_coupon_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_accrued_interest_days, ltab_eod_pos(lv_count).hdg_accrued_interest_days) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_accrued_interest_amount, ltab_eod_pos(lv_count).hdg_accrued_interest_amount) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_eval_accrued_intrst_days, ltab_eod_pos(lv_count).hdg_eval_accrued_intrst_days) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_eval_accrued_intrst_amnt, ltab_eod_pos(lv_count).hdg_eval_accrued_intrst_amnt) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_effctv_intrst_rate, ltab_eod_pos(lv_count).hdg_effctv_intrst_rate) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_effctv_intrst_base_amnt, ltab_eod_pos(lv_count).hdg_effctv_intrst_base_amnt) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_afs_reserve_amount, ltab_eod_pos(lv_count).hdg_afs_reserve_amount) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_quantity_settlement_date, ltab_eod_pos(lv_count).hdg_quantity_settlement_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_new_effctv_intrst_bse_amt, ltab_eod_pos(lv_count).hdg_new_effctv_intrst_bse_amt) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_new_afs_reserve_amount, ltab_eod_pos(lv_count).hdg_new_afs_reserve_amount) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_was_manually_corrected, ltab_eod_pos(lv_count).hdg_was_manually_corrected) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_crrnt_coupon_pymnt_date, ltab_eod_pos(lv_count).hdg_crrnt_coupon_pymnt_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).hdg_redemption_payment_date, ltab_eod_pos(lv_count).hdg_redemption_payment_date) = 1 OR
                  nrg_common.has_value_changed(lv_previous_snapshot(lv_cnt).forced_margin_fx_rate, ltab_eod_pos(lv_count).forced_margin_fx_rate) = 1)


            THEN

                lv_has_snapshot_changed := 1;
              END IF;
            END IF;
          END LOOP;
        END IF;
      ELSE
        lv_has_snapshot_changed := 1;
      END IF;*/
    END LOOP;


    /*
    --
      Now bulk insert all the values to the eod positions table
    --
    */


    FORALL lv_count IN 1..ltab_eod_pos.COUNT
      INSERT INTO eod_open_trades
                         VALUES ltab_eod_pos(lv_count);

    /*

    Process Managed Order Information

    */

    --
    --Clean up history snapshot for managed order info
    --

    IF p_platform = 'NG' AND p_record_source = 'NG-Hedge-FX' THEN

     IF p_batch_detail = 'FIRST' THEN -- BER-3731: only delete and reinsert history and clear current table contents if it's the first batch of a request

        DELETE eod_open_trades_managed_info_h
        WHERE snapshot_time = p_effective_start_timestamp;

      --
      --Insert existing data into history
      --

        INSERT INTO eod_open_trades_managed_info_h
        SELECT platform,
               order_id,
               managed_order_id,
               snapshot_time,
               logical_load_timestamp,
               created_by,
               create_timestamp,
               updated_by,
               update_timestamp,
               effective_start_timestamp,
               p_effective_start_timestamp,
               'U',
               SYSTIMESTAMP,
               business_date,
               reporting_date,
               trading_account_id,
               trading_account_type,
               open_trade_quantity,
               allocation_transaction_id,
               managed_transaction_id,
               opening_trade_price
        FROM eod_open_trades_managed_info
        WHERE snapshot_time = p_effective_start_timestamp;

      --
      --Clean the current table
      --

      DELETE eod_open_trades_managed_info
      WHERE snapshot_time = p_effective_start_timestamp;

    END IF;
      --
      --Write new data into eod_open_trades_managed_info
      --

      FOR lv_cnt IN 1..p_position.COUNT LOOP
        IF p_position(lv_cnt).managed_open_trade_info IS NOT NULL THEN
        IF p_position(lv_cnt).managed_open_trade_info.COUNT > 0 THEN
          put_eod_opn_trds_mngd_info(p_effective_start_timestamp => p_effective_start_timestamp,
                                     p_user                      => p_user,
                                     p_platform                  => p_platform,
                                     p_order_id                  => p_position(lv_cnt).order_id,
                                     p_managed_order_info        => p_position(lv_cnt).managed_open_trade_info,
                                     p_business_date             => lv_business_date,
                                     p_reporting_date            => lv_reporting_date,
                                     p_logical_load_timestamp    => lv_loagical_load_timestamp);
        END IF;
        END IF;
      END LOOP;
   END IF;

   IF p_batch_detail = 'LAST' THEN /*BER-3731: Only perform these steps after the final batch of the request has loaded.
     There will be an empty batch sent after the last one has loaded with p_batch_detail = 'LAST'.*/

    -- procedure call to update margin for eod_positions
     IF p_platform = 'NG' AND p_record_source = 'NG-HEDGE-FX' THEN

      upd_eod_open_trades_hedge_fx           (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform,
                                              p_record_source             => p_record_source);

    ELSIF p_platform = 'NG' AND p_record_source = 'NG-HEDGE-CR' THEN

      upd_eod_open_trades_hedge_cr           (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform,
                                              p_record_source             => p_record_source);

    ELSIF p_platform = 'NG' AND p_record_source = 'NG-HEDGE-FXFWD' THEN

      upd_eod_open_trades_hedge_fxfd         (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform,
                                              p_record_source             => p_record_source);
                                              
    ELSIF p_platform = 'NG' AND p_record_source = 'NG' THEN

      upd_eod_open_trades                    (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform,
                                              p_record_source             => p_record_source);

      upd_eod_open_trades_metatrader         (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform,
                                              p_record_source             => p_record_source);

    SELECT snapshot_time
      INTO lv_snapshot_time
      FROM eod_open_trades
     WHERE business_date = lv_business_date

       AND rownum = 1;

      insert_spdbet_eod_open_trades          (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_effective_start_timestamp => p_effective_start_timestamp,
                                              p_snapshot_time             => lv_snapshot_time,
                                              p_platform                  => p_platform,
                                              p_user                      => p_user);

    ELSIF p_platform <> 'NG' THEN

      upd_mm_eod_open_trades                 (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform);

    END IF;

    IF p_record_source NOT LIKE 'NG-HEDGE%' THEN

      insert_eod_open_positions              (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_effective_start_timestamp => p_effective_start_timestamp,
                                              p_logical_load_timestamp    => lv_loagical_load_timestamp,
                                              p_user                      => p_user,
                                              p_record_source             => p_record_source,
                                              p_platform                  => p_platform);

      upd_eod_positions_metatrader           (p_business_date             => lv_business_date,
                                              p_reporting_date            => lv_reporting_date,
                                              p_platform                  => p_platform,
                                              p_record_source             => p_record_source);

    END IF;

      put_eod_position_snapshots             (p_snapshot_time             => p_effective_start_timestamp,
                                              p_platform                  => p_platform,
                                              p_summary_type              => 'EOD',
                                              p_is_processed              => 'N',
                                              p_processed_time            => NULL,
                                              p_record_source             => p_record_source);

   END IF;

  IF p_platform = 'MMSB' OR p_platform = 'MMCFD' THEN
    --
    --Populate the values for latest positions
    --
    ltab_latest_positions := NEW lvtab_latest_positions();

    FOR lv_count IN 1..ltab_eod_pos.COUNT LOOP
      ltab_latest_positions.EXTEND;
      ltab_latest_positions(lv_count).amnt_in_trdng_accnt_prmry_ccy    := ltab_eod_pos(lv_count).amnt_in_trdng_accnt_prmry_ccy;
      ltab_latest_positions(lv_count).trade_amount                    := ltab_eod_pos(lv_count).amount;
      ltab_latest_positions(lv_count).trade_amount_currency           := ltab_eod_pos(lv_count).amount_currency;
      ltab_latest_positions(lv_count).cstm_info_vrtl_prtfl_cd         := ltab_eod_pos(lv_count).custom_info_virt_portcode;
      ltab_latest_positions(lv_count).requested_direction             := ltab_eod_pos(lv_count).direction;
      ltab_latest_positions(lv_count).effective_start_timestamp       := ltab_eod_pos(lv_count).effective_start_timestamp;
      ltab_latest_positions(lv_count).open_trade_financing_ratio      := ltab_eod_pos(lv_count).financing_ratio;
      ltab_latest_positions(lv_count).fractional_part_ratio           := ltab_eod_pos(lv_count).fractional_part_ratio;
      ltab_latest_positions(lv_count).is_crrncy_in_fractional_parts   := ltab_eod_pos(lv_count).is_inst_ccy_in_fracnal_parts;
      ltab_latest_positions(lv_count).is_primary                      := ltab_eod_pos(lv_count).is_primary;
      ltab_latest_positions(lv_count).logical_load_timestamp          := ltab_eod_pos(lv_count).logical_load_timestamp;
      ltab_latest_positions(lv_count).margin                          := ltab_eod_pos(lv_count).margin;
      ltab_latest_positions(lv_count).margin_currency                 := ltab_eod_pos(lv_count).margin_currency;
      ltab_latest_positions(lv_count).margin_secondary                := ltab_eod_pos(lv_count).margin_secondary;
      ltab_latest_positions(lv_count).margin_secondary_currency       := ltab_eod_pos(lv_count).margin_secondary_ccy;
      ltab_latest_positions(lv_count).mm_account_id                   := ltab_eod_pos(lv_count).mm_account_id;
      ltab_latest_positions(lv_count).mm_backoffice_ref               := ltab_eod_pos(lv_count).mm_backoffice_ref;
      ltab_latest_positions(lv_count).mm_instrument_id                := ltab_eod_pos(lv_count).mm_instrument_id;
      ltab_latest_positions(lv_count).mm_value_date                   := ltab_eod_pos(lv_count).mm_value_date;
      ltab_latest_positions(lv_count).mrgn_in_trdng_accnt_prmry_ccy   := ltab_eod_pos(lv_count).mrgn_in_trdng_accnt_prmry_ccy;
      ltab_latest_positions(lv_count).direction_multiplier            := ltab_eod_pos(lv_count).normalised_direction_mulplr;
      ltab_latest_positions(lv_count).normalised_margin_req           := ltab_eod_pos(lv_count).normalised_margin_rqrmnt;
      ltab_latest_positions(lv_count).normalised_margin_type          := ltab_eod_pos(lv_count).normalised_margin_type;
      ltab_latest_positions(lv_count).normalised_open_price           := ltab_eod_pos(lv_count).normalised_open_price;
      ltab_latest_positions(lv_count).nrmlsd_opn_qty_in_trdng_ccy     := ltab_eod_pos(lv_count).normalised_open_qty_trad_ccy;
      ltab_latest_positions(lv_count).nrmlsd_opn_value_in_trdng_ccy   := ltab_eod_pos(lv_count).normalised_open_val_trad_ccy;
      ltab_latest_positions(lv_count).trade_time                      := ltab_eod_pos(lv_count).open_time;
      ltab_latest_positions(lv_count).trade_id                        := ltab_eod_pos(lv_count).open_trade_id;
      ltab_latest_positions(lv_count).trade_price                     := ltab_eod_pos(lv_count).open_trade_price;
      ltab_latest_positions(lv_count).order_id                        := ltab_eod_pos(lv_count).order_id;
      ltab_latest_positions(lv_count).platform                        := ltab_eod_pos(lv_count).platform;
      ltab_latest_positions(lv_count).product_currency                := ltab_eod_pos(lv_count).product_currency;
      ltab_latest_positions(lv_count).product_financing_ratio_max     := ltab_eod_pos(lv_count).product_financing_ratio_max;
      ltab_latest_positions(lv_count).product_generation              := ltab_eod_pos(lv_count).product_generation;
      ltab_latest_positions(lv_count).product_instrument_code         := ltab_eod_pos(lv_count).product_instrument_code;
      ltab_latest_positions(lv_count).product_point_multiplier        := ltab_eod_pos(lv_count).product_point_multiplier;
      ltab_latest_positions(lv_count).product_wrapper_code            := ltab_eod_pos(lv_count).product_wrapper_code;
      ltab_latest_positions(lv_count).trade_quantity                  := ltab_eod_pos(lv_count).quantity;
      ltab_latest_positions(lv_count).trade_quantity_currency         := ltab_eod_pos(lv_count).quantity_currency;
      ltab_latest_positions(lv_count).quantity_designator             := ltab_eod_pos(lv_count).quantity_designator;
      ltab_latest_positions(lv_count).trading_accnt_primary_currency  := ltab_eod_pos(lv_count).trading_accnt_primary_ccy;
      ltab_latest_positions(lv_count).trading_account_codifier        := ltab_eod_pos(lv_count).trading_account_codifier;
      ltab_latest_positions(lv_count).trading_account_function        := ltab_eod_pos(lv_count).trading_account_function;
      ltab_latest_positions(lv_count).trading_account_id              := ltab_eod_pos(lv_count).trading_account_id;
      ltab_latest_positions(lv_count).trading_account_type            := ltab_eod_pos(lv_count).trading_account_type;
      ltab_latest_positions(lv_count).updated_by                      := ltab_eod_pos(lv_count).updated_by;
      ltab_latest_positions(lv_count).update_timestamp                := ltab_eod_pos(lv_count).update_timestamp;
      ltab_latest_positions(lv_count).created_by                      := ltab_eod_pos(lv_count).created_by;
      ltab_latest_positions(lv_count).create_timestamp                := ltab_eod_pos(lv_count).create_timestamp;
      ltab_latest_positions(lv_count).active_flag                     := 'YES';
      ltab_latest_positions(lv_count).margin_type                     := ltab_eod_pos(lv_count).normalised_margin_type;
      ltab_latest_positions(lv_count).normalised_trading_currency     := ltab_eod_pos(lv_count).normalised_trading_ccy;
      ltab_latest_positions(lv_count).last_modified_time              := ltab_eod_pos(lv_count).last_modified_time;
      ltab_latest_positions(lv_count).creation_time                   := ltab_eod_pos(lv_count).open_time;
      ltab_latest_positions(lv_count).counterparty_id                 := ltab_eod_pos(lv_count).counterparty_id;


      /*
        --
          Calculate the normalised fields for latest positions
        --
      */

      ltab_latest_positions(lv_count).open_trade_qntty_fx_rate := ltab_eod_pos(lv_count).open_trade_quantity_fx_rate;
      ltab_latest_positions(lv_count).margin_fx_rate := ltab_eod_pos(lv_count).open_trade_margin_fx_rate;


    END LOOP;

    --
    --For Market Maker clear the previous days positions and resync the data
    --For NG check if there is any missing position then insert the missing one
    --



      --
      --Clear the previous days position
      --

      DELETE Latest_Open_Trades
       WHERE platform = p_platform AND
             effective_start_timestamp < p_effective_start_timestamp;


      FORALL lv_count IN 1..ltab_latest_positions.COUNT
        MERGE INTO Latest_Open_Trades old_ver
          USING ( SELECT   ltab_latest_positions(lv_count).ORDER_ID
                  ,ltab_latest_positions(lv_count).PLATFORM
                  ,ltab_latest_positions(lv_count).LOGICAL_LOAD_TIMESTAMP
                  ,ltab_latest_positions(lv_count).CREATED_BY
                  ,ltab_latest_positions(lv_count).CREATE_TIMESTAMP
                  ,ltab_latest_positions(lv_count).UPDATED_BY
                  ,ltab_latest_positions(lv_count).UPDATE_TIMESTAMP
                  ,ltab_latest_positions(lv_count).EFFECTIVE_START_TIMESTAMP
                  ,ltab_latest_positions(lv_count).EFFECTIVE_END_TIMESTAMP
                  ,ltab_latest_positions(lv_count).ACTIVE_FLAG
                  ,ltab_latest_positions(lv_count).TRADING_ACCOUNT_ID
                  ,ltab_latest_positions(lv_count).TRADING_ACCOUNT_TYPE
                  ,ltab_latest_positions(lv_count).TRADING_ACCOUNT_CODIFIER
                  ,ltab_latest_positions(lv_count).TRADING_ACCOUNT_FUNCTION
                  ,ltab_latest_positions(lv_count).MM_ACCOUNT_ID
                  ,ltab_latest_positions(lv_count).PRODUCT_INSTRUMENT_CODE
                  ,ltab_latest_positions(lv_count).PRODUCT_WRAPPER_CODE
                  ,ltab_latest_positions(lv_count).PRODUCT_POINT_MULTIPLIER
                  ,ltab_latest_positions(lv_count).PRODUCT_FINANCING_RATIO_MAX
                  ,ltab_latest_positions(lv_count).PRODUCT_GENERATION
                  ,ltab_latest_positions(lv_count).IS_CRRNCY_IN_FRACTIONAL_PARTS
                  ,ltab_latest_positions(lv_count).FRACTIONAL_PART_RATIO
                  ,ltab_latest_positions(lv_count).MM_INSTRUMENT_ID
                  ,ltab_latest_positions(lv_count).REQUESTED_DIRECTION
                  ,ltab_latest_positions(lv_count).DIRECTION_MULTIPLIER
                  ,ltab_latest_positions(lv_count).CREATION_TIME
                  ,ltab_latest_positions(lv_count).TRADE_ID
                  ,ltab_latest_positions(lv_count).TRADE_TIME
                  ,ltab_latest_positions(lv_count).IS_PRIMARY
                  ,ltab_latest_positions(lv_count).TRADING_ACCNT_PRIMARY_CURRENCY
                  ,ltab_latest_positions(lv_count).PRODUCT_CURRENCY
                  ,ltab_latest_positions(lv_count).TRADE_QUANTITY
                  ,ltab_latest_positions(lv_count).TRADE_QUANTITY_CURRENCY
                  ,ltab_latest_positions(lv_count).NRMLSD_OPN_QTY_IN_TRDNG_CCY
                  ,ltab_latest_positions(lv_count).TRADE_PRICE
                  ,ltab_latest_positions(lv_count).NORMALISED_OPEN_PRICE
                  ,ltab_latest_positions(lv_count).OPEN_TRADE_FINANCING_RATIO
                  ,ltab_latest_positions(lv_count).TRADE_AMOUNT
                  ,ltab_latest_positions(lv_count).TRADE_AMOUNT_CURRENCY
                  ,ltab_latest_positions(lv_count).AMNT_IN_TRDNG_ACCNT_PRMRY_CCY
                  ,ltab_latest_positions(lv_count).NRMLSD_OPN_VALUE_IN_TRDNG_CCY
                  ,ltab_latest_positions(lv_count).MARGIN_TYPE
                  ,ltab_latest_positions(lv_count).NORMALISED_MARGIN_TYPE
                  ,ltab_latest_positions(lv_count).NORMALISED_MARGIN_REQ
                  ,ltab_latest_positions(lv_count).MARGIN
                  ,ltab_latest_positions(lv_count).MRGN_IN_TRDNG_ACCNT_PRMRY_CCY
                  ,ltab_latest_positions(lv_count).MARGIN_CURRENCY
                  ,ltab_latest_positions(lv_count).MARGIN_SECONDARY
                  ,ltab_latest_positions(lv_count).MARGIN_SECONDARY_CURRENCY
                  ,ltab_latest_positions(lv_count).MARGIN_FX_RATE
                  ,ltab_latest_positions(lv_count).OPEN_TRADE_QNTTY_FX_RATE
                  ,ltab_latest_positions(lv_count).CSTM_INFO_VRTL_PRTFL_CD
                  ,ltab_latest_positions(lv_count).QUANTITY_DESIGNATOR
                  ,ltab_latest_positions(lv_count).MM_BACKOFFICE_REF
                  ,ltab_latest_positions(lv_count).MM_VALUE_DATE
                  ,ltab_latest_positions(lv_count).NORMALISED_TRADING_CURRENCY
                  ,ltab_latest_positions(lv_count).LAST_MODIFIED_TIME
                  ,ltab_latest_positions(lv_count).COUNTERPARTY_ID
                  ,ltab_latest_positions(lv_count).trading_scope FROM DUAL)
              ON (old_ver.platform=ltab_latest_positions(lv_count).PLATFORM
                  AND old_ver.ORDER_ID=ltab_latest_positions(lv_count).ORDER_ID
                  AND old_ver.EFFECTIVE_START_TIMESTAMP=ltab_latest_positions(lv_count).EFFECTIVE_START_TIMESTAMP)
             WHEN MATCHED THEN
              UPDATE
               SET  old_ver.LOGICAL_LOAD_TIMESTAMP        = ltab_latest_positions(lv_count).LOGICAL_LOAD_TIMESTAMP
                    ,old_ver.CREATED_BY                    = ltab_latest_positions(lv_count).CREATED_BY
                    ,old_ver.CREATE_TIMESTAMP              = ltab_latest_positions(lv_count).CREATE_TIMESTAMP
                    ,old_ver.UPDATED_BY                    = ltab_latest_positions(lv_count).UPDATED_BY
                    ,old_ver.UPDATE_TIMESTAMP              = ltab_latest_positions(lv_count).UPDATE_TIMESTAMP
                    ,old_ver.EFFECTIVE_END_TIMESTAMP       = ltab_latest_positions(lv_count).EFFECTIVE_END_TIMESTAMP
                    ,old_ver.ACTIVE_FLAG                   = ltab_latest_positions(lv_count).ACTIVE_FLAG
                    ,old_ver.TRADING_ACCOUNT_ID            = ltab_latest_positions(lv_count).TRADING_ACCOUNT_ID
                    ,old_ver.TRADING_ACCOUNT_TYPE          = ltab_latest_positions(lv_count).TRADING_ACCOUNT_TYPE
                    ,old_ver.TRADING_ACCOUNT_CODIFIER      = ltab_latest_positions(lv_count).TRADING_ACCOUNT_CODIFIER
                    ,old_ver.TRADING_ACCOUNT_FUNCTION      = ltab_latest_positions(lv_count).TRADING_ACCOUNT_FUNCTION
                    ,old_ver.MM_ACCOUNT_ID                 = ltab_latest_positions(lv_count).MM_ACCOUNT_ID
                    ,old_ver.PRODUCT_INSTRUMENT_CODE       = ltab_latest_positions(lv_count).PRODUCT_INSTRUMENT_CODE
                    ,old_ver.PRODUCT_WRAPPER_CODE          = ltab_latest_positions(lv_count).PRODUCT_WRAPPER_CODE
                    ,old_ver.PRODUCT_POINT_MULTIPLIER      = ltab_latest_positions(lv_count).PRODUCT_POINT_MULTIPLIER
                    ,old_ver.PRODUCT_FINANCING_RATIO_MAX   = ltab_latest_positions(lv_count).PRODUCT_FINANCING_RATIO_MAX
                    ,old_ver.PRODUCT_GENERATION            = ltab_latest_positions(lv_count).PRODUCT_GENERATION
                    ,old_ver.IS_CRRNCY_IN_FRACTIONAL_PARTS = ltab_latest_positions(lv_count).IS_CRRNCY_IN_FRACTIONAL_PARTS
                    ,old_ver.FRACTIONAL_PART_RATIO         = ltab_latest_positions(lv_count).FRACTIONAL_PART_RATIO
                    ,old_ver.MM_INSTRUMENT_ID              = ltab_latest_positions(lv_count).MM_INSTRUMENT_ID
                    ,old_ver.REQUESTED_DIRECTION           = ltab_latest_positions(lv_count).REQUESTED_DIRECTION
                    ,old_ver.DIRECTION_MULTIPLIER          = ltab_latest_positions(lv_count).DIRECTION_MULTIPLIER
                    ,old_ver.CREATION_TIME                 = ltab_latest_positions(lv_count).CREATION_TIME
                    ,old_ver.TRADE_ID                      = ltab_latest_positions(lv_count).TRADE_ID
                    ,old_ver.TRADE_TIME                    = ltab_latest_positions(lv_count).TRADE_TIME
                    ,old_ver.IS_PRIMARY                    = ltab_latest_positions(lv_count).IS_PRIMARY
                    ,old_ver.TRADING_ACCNT_PRIMARY_CURRENCY= ltab_latest_positions(lv_count).TRADING_ACCNT_PRIMARY_CURRENCY
                    ,old_ver.PRODUCT_CURRENCY              = ltab_latest_positions(lv_count).PRODUCT_CURRENCY
                    ,old_ver.TRADE_QUANTITY                = ltab_latest_positions(lv_count).TRADE_QUANTITY
                    ,old_ver.TRADE_QUANTITY_CURRENCY       = ltab_latest_positions(lv_count).TRADE_QUANTITY_CURRENCY
                    ,old_ver.NRMLSD_OPN_QTY_IN_TRDNG_CCY   = ltab_latest_positions(lv_count).NRMLSD_OPN_QTY_IN_TRDNG_CCY
                    ,old_ver.TRADE_PRICE                   = ltab_latest_positions(lv_count).TRADE_PRICE
                    ,old_ver.NORMALISED_OPEN_PRICE         = ltab_latest_positions(lv_count).NORMALISED_OPEN_PRICE
                    ,old_ver.OPEN_TRADE_FINANCING_RATIO    = ltab_latest_positions(lv_count).OPEN_TRADE_FINANCING_RATIO
                    ,old_ver.TRADE_AMOUNT                  = ltab_latest_positions(lv_count).TRADE_AMOUNT
                    ,old_ver.TRADE_AMOUNT_CURRENCY         = ltab_latest_positions(lv_count).TRADE_AMOUNT_CURRENCY
                    ,old_ver.AMNT_IN_TRDNG_ACCNT_PRMRY_CCY = ltab_latest_positions(lv_count).AMNT_IN_TRDNG_ACCNT_PRMRY_CCY
                    ,old_ver.NRMLSD_OPN_VALUE_IN_TRDNG_CCY = ltab_latest_positions(lv_count).NRMLSD_OPN_VALUE_IN_TRDNG_CCY
                    ,old_ver.MARGIN_TYPE                   = ltab_latest_positions(lv_count).MARGIN_TYPE
                    ,old_ver.NORMALISED_MARGIN_TYPE        = ltab_latest_positions(lv_count).NORMALISED_MARGIN_TYPE
                    ,old_ver.NORMALISED_MARGIN_REQ         = ltab_latest_positions(lv_count).NORMALISED_MARGIN_REQ
                    ,old_ver.MARGIN                        = ltab_latest_positions(lv_count).MARGIN
                    ,old_ver.MRGN_IN_TRDNG_ACCNT_PRMRY_CCY = ltab_latest_positions(lv_count).MRGN_IN_TRDNG_ACCNT_PRMRY_CCY
                    ,old_ver.MARGIN_CURRENCY               = ltab_latest_positions(lv_count).MARGIN_CURRENCY
                    ,old_ver.MARGIN_SECONDARY              = ltab_latest_positions(lv_count).MARGIN_SECONDARY
                    ,old_ver.MARGIN_SECONDARY_CURRENCY     = ltab_latest_positions(lv_count).MARGIN_SECONDARY_CURRENCY
                    ,old_ver.MARGIN_FX_RATE                = ltab_latest_positions(lv_count).MARGIN_FX_RATE
                    ,old_ver.OPEN_TRADE_QNTTY_FX_RATE      = ltab_latest_positions(lv_count).OPEN_TRADE_QNTTY_FX_RATE
                    ,old_ver.CSTM_INFO_VRTL_PRTFL_CD       = ltab_latest_positions(lv_count).CSTM_INFO_VRTL_PRTFL_CD
                    ,old_ver.QUANTITY_DESIGNATOR           = ltab_latest_positions(lv_count).QUANTITY_DESIGNATOR
                    ,old_ver.MM_BACKOFFICE_REF             = ltab_latest_positions(lv_count).MM_BACKOFFICE_REF
                    ,old_ver.MM_VALUE_DATE                 = ltab_latest_positions(lv_count).MM_VALUE_DATE
                    ,old_ver.NORMALISED_TRADING_CURRENCY   = ltab_latest_positions(lv_count).NORMALISED_TRADING_CURRENCY
                    ,old_ver.LAST_MODIFIED_TIME            = ltab_latest_positions(lv_count).LAST_MODIFIED_TIME
                    ,old_ver.COUNTERPARTY_ID               = ltab_latest_positions(lv_count).COUNTERPARTY_ID
                    ,old_ver.trading_scope                 = ltab_latest_positions(lv_count).trading_scope
              WHERE  (     nrg_common.has_value_changed(old_ver.LOGICAL_LOAD_TIMESTAMP,ltab_latest_positions(lv_count).LOGICAL_LOAD_TIMESTAMP)=1
                        OR nrg_common.has_value_changed(old_ver.CREATED_BY,ltab_latest_positions(lv_count).CREATED_BY)=1
                        OR nrg_common.has_value_changed(old_ver.CREATE_TIMESTAMP,ltab_latest_positions(lv_count).CREATE_TIMESTAMP)=1
                        OR nrg_common.has_value_changed(old_ver.UPDATED_BY,ltab_latest_positions(lv_count).UPDATED_BY)=1
                        OR nrg_common.has_value_changed(old_ver.UPDATE_TIMESTAMP,ltab_latest_positions(lv_count).UPDATE_TIMESTAMP)=1
                        OR nrg_common.has_value_changed(old_ver.EFFECTIVE_END_TIMESTAMP,ltab_latest_positions(lv_count).EFFECTIVE_END_TIMESTAMP)=1
                        OR nrg_common.has_value_changed(old_ver.ACTIVE_FLAG,ltab_latest_positions(lv_count).ACTIVE_FLAG)=1
                        OR nrg_common.has_value_changed(old_ver.TRADING_ACCOUNT_ID,ltab_latest_positions(lv_count).TRADING_ACCOUNT_ID)=1
                        OR nrg_common.has_value_changed(old_ver.TRADING_ACCOUNT_TYPE,ltab_latest_positions(lv_count).TRADING_ACCOUNT_TYPE)=1
                        OR nrg_common.has_value_changed(old_ver.TRADING_ACCOUNT_CODIFIER,ltab_latest_positions(lv_count).TRADING_ACCOUNT_CODIFIER)=1
                        OR nrg_common.has_value_changed(old_ver.TRADING_ACCOUNT_FUNCTION,ltab_latest_positions(lv_count).TRADING_ACCOUNT_FUNCTION)=1
                        OR nrg_common.has_value_changed(old_ver.MM_ACCOUNT_ID,ltab_latest_positions(lv_count).MM_ACCOUNT_ID)=1
                        OR nrg_common.has_value_changed(old_ver.PRODUCT_INSTRUMENT_CODE,ltab_latest_positions(lv_count).PRODUCT_INSTRUMENT_CODE)=1
                        OR nrg_common.has_value_changed(old_ver.PRODUCT_WRAPPER_CODE,ltab_latest_positions(lv_count).PRODUCT_WRAPPER_CODE)=1
                        OR nrg_common.has_value_changed(old_ver.PRODUCT_POINT_MULTIPLIER,ltab_latest_positions(lv_count).PRODUCT_POINT_MULTIPLIER)=1
                        OR nrg_common.has_value_changed(old_ver.PRODUCT_FINANCING_RATIO_MAX,ltab_latest_positions(lv_count).PRODUCT_FINANCING_RATIO_MAX)=1
                        OR nrg_common.has_value_changed(old_ver.PRODUCT_GENERATION,ltab_latest_positions(lv_count).PRODUCT_GENERATION)=1
                        OR nrg_common.has_value_changed(old_ver.IS_CRRNCY_IN_FRACTIONAL_PARTS,ltab_latest_positions(lv_count).IS_CRRNCY_IN_FRACTIONAL_PARTS)=1
                        OR nrg_common.has_value_changed(old_ver.FRACTIONAL_PART_RATIO,ltab_latest_positions(lv_count).FRACTIONAL_PART_RATIO)=1
                        OR nrg_common.has_value_changed(old_ver.MM_INSTRUMENT_ID,ltab_latest_positions(lv_count).MM_INSTRUMENT_ID)=1
                        OR nrg_common.has_value_changed(old_ver.REQUESTED_DIRECTION,ltab_latest_positions(lv_count).REQUESTED_DIRECTION)=1
                        OR nrg_common.has_value_changed(old_ver.DIRECTION_MULTIPLIER,ltab_latest_positions(lv_count).DIRECTION_MULTIPLIER)=1
                        OR nrg_common.has_value_changed(old_ver.CREATION_TIME,ltab_latest_positions(lv_count).CREATION_TIME)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_ID,ltab_latest_positions(lv_count).TRADE_ID)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_TIME,ltab_latest_positions(lv_count).TRADE_TIME)=1
                        OR nrg_common.has_value_changed(old_ver.IS_PRIMARY,ltab_latest_positions(lv_count).IS_PRIMARY)=1
                        OR nrg_common.has_value_changed(old_ver.TRADING_ACCNT_PRIMARY_CURRENCY,ltab_latest_positions(lv_count).TRADING_ACCNT_PRIMARY_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.PRODUCT_CURRENCY,ltab_latest_positions(lv_count).PRODUCT_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_QUANTITY,ltab_latest_positions(lv_count).TRADE_QUANTITY)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_QUANTITY_CURRENCY,ltab_latest_positions(lv_count).TRADE_QUANTITY_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.NRMLSD_OPN_QTY_IN_TRDNG_CCY,ltab_latest_positions(lv_count).NRMLSD_OPN_QTY_IN_TRDNG_CCY)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_PRICE,ltab_latest_positions(lv_count).TRADE_PRICE)=1
                        OR nrg_common.has_value_changed(old_ver.NORMALISED_OPEN_PRICE,ltab_latest_positions(lv_count).NORMALISED_OPEN_PRICE)=1
                        OR nrg_common.has_value_changed(old_ver.OPEN_TRADE_FINANCING_RATIO,ltab_latest_positions(lv_count).OPEN_TRADE_FINANCING_RATIO)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_AMOUNT,ltab_latest_positions(lv_count).TRADE_AMOUNT)=1
                        OR nrg_common.has_value_changed(old_ver.TRADE_AMOUNT_CURRENCY,ltab_latest_positions(lv_count).TRADE_AMOUNT_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.AMNT_IN_TRDNG_ACCNT_PRMRY_CCY,ltab_latest_positions(lv_count).AMNT_IN_TRDNG_ACCNT_PRMRY_CCY)=1
                        OR nrg_common.has_value_changed(old_ver.NRMLSD_OPN_VALUE_IN_TRDNG_CCY,ltab_latest_positions(lv_count).NRMLSD_OPN_VALUE_IN_TRDNG_CCY)=1
                        OR nrg_common.has_value_changed(old_ver.MARGIN_TYPE,ltab_latest_positions(lv_count).MARGIN_TYPE)=1
                        OR nrg_common.has_value_changed(old_ver.NORMALISED_MARGIN_TYPE,ltab_latest_positions(lv_count).NORMALISED_MARGIN_TYPE)=1
                        OR nrg_common.has_value_changed(old_ver.NORMALISED_MARGIN_REQ,ltab_latest_positions(lv_count).NORMALISED_MARGIN_REQ)=1
                        OR nrg_common.has_value_changed(old_ver.MARGIN,ltab_latest_positions(lv_count).MARGIN)=1
                        OR nrg_common.has_value_changed(old_ver.MRGN_IN_TRDNG_ACCNT_PRMRY_CCY,ltab_latest_positions(lv_count).MRGN_IN_TRDNG_ACCNT_PRMRY_CCY)=1
                        OR nrg_common.has_value_changed(old_ver.MARGIN_CURRENCY,ltab_latest_positions(lv_count).MARGIN_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.MARGIN_SECONDARY,ltab_latest_positions(lv_count).MARGIN_SECONDARY)=1
                        OR nrg_common.has_value_changed(old_ver.MARGIN_SECONDARY_CURRENCY,ltab_latest_positions(lv_count).MARGIN_SECONDARY_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.MARGIN_FX_RATE,ltab_latest_positions(lv_count).MARGIN_FX_RATE)=1
                        OR nrg_common.has_value_changed(old_ver.OPEN_TRADE_QNTTY_FX_RATE,ltab_latest_positions(lv_count).OPEN_TRADE_QNTTY_FX_RATE)=1
                        OR nrg_common.has_value_changed(old_ver.CSTM_INFO_VRTL_PRTFL_CD,ltab_latest_positions(lv_count).CSTM_INFO_VRTL_PRTFL_CD)=1
                        OR nrg_common.has_value_changed(old_ver.QUANTITY_DESIGNATOR,ltab_latest_positions(lv_count).QUANTITY_DESIGNATOR)=1
                        OR nrg_common.has_value_changed(old_ver.MM_BACKOFFICE_REF,ltab_latest_positions(lv_count).MM_BACKOFFICE_REF)=1
                        OR nrg_common.has_value_changed(old_ver.MM_VALUE_DATE,ltab_latest_positions(lv_count).MM_VALUE_DATE)=1
                        OR nrg_common.has_value_changed(old_ver.NORMALISED_TRADING_CURRENCY,ltab_latest_positions(lv_count).NORMALISED_TRADING_CURRENCY)=1
                        OR nrg_common.has_value_changed(old_ver.LAST_MODIFIED_TIME,ltab_latest_positions(lv_count).LAST_MODIFIED_TIME)=1
                        OR nrg_common.has_value_changed(old_ver.COUNTERPARTY_ID,ltab_latest_positions(lv_count).COUNTERPARTY_ID)=1
                        OR nrg_common.has_value_changed(old_ver.trading_scope,ltab_latest_positions(lv_count).trading_scope)=1)
              WHEN NOT MATCHED THEN
            INSERT VALUES ltab_latest_positions(lv_count);

      ELSIF p_platform = 'NG' AND p_record_source NOT LIKE 'NG-HEDGE%' THEN

      --
      --Delete Old Records which have been marked as closed
      --

      DELETE Latest_Open_Trades
       WHERE active_flag = 'NO' AND
             platform = 'NG' AND
             effective_start_timestamp < p_effective_start_timestamp;

      --
      --Since the NG positions are calculated based on the position transaction, the completeness of the latest positions
      --will be based on completeness of position transactions
      --
      --Incase we face issues with the completeness of the latest positions
      --we can re-sync the positions with the eod snapshot using the procedure resync_latest_positions
      --
    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;


  -- ===================================================================================
  -- put_eod_cash_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will insert eod cash positions into
  --     bi_ods.eod_cash_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --     p_platform                       Platform
  --     p_cash_position                  Cash positions objects
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_eod_cash_position_set (p_user                            IN VARCHAR2,
                                       p_effective_start_timestamp       IN TIMESTAMP,
                                       p_platform                        IN VARCHAR2,
                                       --p_business_date                   IN DATE,
                                       --p_reporting_date                  IN DATE,
                                       p_cash_position                   IN position_tab)
  IS

    lv_last_snaopshot_time  DATE;

    lv_logical_load_timestamp TIMESTAMP(6);

    lv_business_date            eod_open_trades.business_date%TYPE;
    lv_reporting_date           eod_open_trades.reporting_date%TYPE;

  BEGIN

    --
    --Calculate the business date
    --

    lv_business_date := TRUNC(p_effective_start_timestamp);

    --
    --Reporting Date
    --

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;


    lv_logical_load_timestamp := SYSTIMESTAMP;

    DELETE eod_cash_positions
    WHERE business_date = lv_business_Date AND
          platform = p_platform AND
          snapshot_time = p_effective_start_timestamp;

    SELECT nvl(max(snapshot_time), to_date('01-Jan-1970','DD-Mon-YYYY'))
    INTO lv_last_snaopshot_time
    FROM eod_cash_positions
    WHERE platform = p_platform;

    IF lv_last_snaopshot_time <= p_effective_start_timestamp THEN
      DELETE mm_cash_movements
      WHERE  effective_start_timestamp <= p_effective_start_timestamp;

      FOR lv_cnt IN 1..p_cash_position.COUNT LOOP
        nrg_cash_account.create_cash_account_stub(p_user                       => p_user,
                                                  p_logical_load_timestamp     => lv_logical_load_timestamp,
                                                  p_effective_start_timestamp  => p_effective_start_timestamp,
                                                  p_account_number             => p_cash_position(lv_cnt).mm_account_id,
                                                  p_platform                   => p_platform);
      END LOOP;

      FOR lv_cnt IN 1..p_cash_position.COUNT LOOP
        IF p_cash_position(lv_cnt).trading_account_code IS NOT NULL THEN
          nrg_trading_account.create_trading_account_stub (p_user                       => p_user,
                                                           p_logical_load_timestamp     => lv_logical_load_timestamp,
                                                           p_effective_start_timestamp  => p_effective_start_timestamp,
                                                           p_trading_account_id         => p_cash_position(lv_cnt).trading_account_code,
                                                           p_trading_account_type       => p_cash_position(lv_cnt).trading_account_type);
        END IF;
      END LOOP;

      FOR lv_cnt IN 1..p_cash_position.COUNT LOOP
        nrg_products.create_instrument_stub(p_user                       => p_user,
                                            p_logical_load_timestamp     => lv_logical_load_timestamp,
                                            p_effective_start_timestamp  => p_effective_start_timestamp,
                                            p_instrument_code            => p_cash_position(lv_cnt).product_instrument_code,
                                            p_product_platform           => p_platform,
                                            p_product_wrapper            => p_cash_position(lv_cnt).product_wrapper_code,
                                            p_mm_instrument_id           => p_cash_position(lv_cnt).mm_instrument_id);
      END LOOP;

      FORALL lv_cnt IN 1..p_cash_position.COUNT
        INSERT INTO mm_cash_movements (account_number,
                                       platform,
                                       cash_transaction_seq,
                                       transaction_currency,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       business_date,
                                       reporting_date,
                                       amount,
                                       transaction_type)
                              VALUES  (p_cash_position(lv_cnt).mm_account_id,
                                       p_platform,
                                       -1,
                                       p_cash_position(lv_cnt).quantity_currency,
                                       lv_logical_load_timestamp,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_effective_start_timestamp,
                                       lv_business_Date,
                                       lv_reporting_date,
                                       p_cash_position(lv_cnt).quantity,
                                       'EOD_CASH_POSITIONS');
    END IF;

    FORALL lv_cnt IN 1..p_cash_position.COUNT
      INSERT INTO eod_cash_positions (platform,
                                      snapshot_time,
                                      cash_transaction_ref,
                                      logical_load_timestamp,
                                      created_by,
                                      create_timestamp,
                                      updated_by,
                                      update_timestamp,
                                      effective_start_timestamp,
                                      business_date,
                                      reporting_date,
                                      account_number,
                                      mm_instrument_id,
                                      direction,
                                      direction_multiplier,
                                      quantity,
                                      quantity_currency,
                                      amount,
                                      amount_currency,
                                      is_primary,
                                      mm_value_date)
                              VALUES  (p_platform,
                                       p_cash_position(lv_cnt).snapshot_time,
                                       p_cash_position(lv_cnt).mm_backoffice_ref,
                                       lv_logical_load_timestamp,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_effective_start_timestamp,
                                       lv_business_Date,
                                       lv_reporting_date,
                                       p_cash_position(lv_cnt).mm_account_id,
                                       p_cash_position(lv_cnt).mm_instrument_id,
                                       p_cash_position(lv_cnt).direction,
                                       DECODE(p_cash_position(lv_cnt).direction, 'CR', 1, -1),
                                       abs(p_cash_position(lv_cnt).quantity),
                                       p_cash_position(lv_cnt).quantity_currency,
                                       p_cash_position(lv_cnt).amount,
                                       p_cash_position(lv_cnt).amount_currency,
                                       p_cash_position(lv_cnt).is_primary,
                                       p_cash_position(lv_cnt).mm_value_date);

   -------add notification to eod_cash_position_snapshots that cash positions have been loaded

        MERGE INTO bi_ods.eod_cash_position_snapshots tgt
        USING (SELECT p_platform AS platform
                     ,lv_business_Date AS business_date
                     ,systimestamp AS logical_load_timestamp
                     ,'N' AS is_processed_cas
                     ,NULL AS processed_time_cas
               FROM   dual) src
        ON (src.business_date = tgt.business_date AND src.platform = tgt.platform)
        WHEN MATCHED THEN
          UPDATE
          SET    tgt.logical_load_timestamp = src.logical_load_timestamp
                ,tgt.is_processed_cas       = src.is_processed_cas
        WHEN NOT MATCHED THEN
          INSERT
            (platform
            ,business_date
            ,logical_load_timestamp
            ,is_processed_cas
            ,processed_time_cas)
          VALUES
            (src.platform
            ,src.business_date
            ,src.logical_load_timestamp
            ,src.is_processed_cas
            ,src.processed_time_cas);


  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

END nrg_position;
/