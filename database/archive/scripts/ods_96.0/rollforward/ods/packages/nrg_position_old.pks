CREATE OR REPLACE PACKAGE bi_ods.nrg_position_old
AS
  -- ===================================================================================
  -- NRG_POSITION
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of Positions
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     09/12/2011   Sanket Mittal      1.0    Creation
  --     25/01/2012   Manoj Kumar        1.1    Modified as per the latest data lineage for eod positions
  --     13/01/2012   Sanket Mittal	 1.2	Modified the EOD Positions write and the Latest Positions
  --     06/03/2012   Sanket Mittal      1.3    Modified to resolve the deadlock issues
  --     18/07/2012   Sanket Mittal      1.5    Added put_eod_cash_position_set
  --     02/08/2012   Patrick Dinwiddy   1.7    Added put_eod_position_snapshot which helps loading of the EDW position summaries
  --     29/04/2013   Sanket Mittal      2.7    Updated the EOD positions load bot to sync the latest position for NG with eod snapshot and added a new proc for this sync
  --     26/07/2013   Prachi Shah        2.10   Added the procedure cleanup_latest_positions.
  --     24/12/2013   Ravi Shankar Gopal 3.7    BER - 781: Added New logic to handle Integration with Hedge Positions. Details as follows:
  --                                            (a). Updated the put_eod_position_set procedure to handle the next gen hedge position
  --                                            (b). Updated the put_eod_position_snapshot to handle the new Record Source "NG-HEDGE"
  --                                            (c). Updated put_position,resync_latest_position,cleanup_latest_position Procedures to
  --                                                 exclude NG Hedge
  --     14/01/2014  Adam Krasnicki      3.8     put_anytime_positions_set added
  --     06/03/2014  Adam Krasnicki      3.9   Added new function cleanup_latest_positions_2
  --     18/07/2014  Adam Krasnicki      4.0   put_position (p_load_latest_positions parameter added)
  --     12/09/2014  Adam Krasnicki      4.1   eod_positions[_h] table renamed to eod_open_trades[_h] plus other long/short changes
  --     16/06/2015  Adam Krasnicki      4.1   put_position: p_direction_multiplier parameter added
  --     05/10/2015  Sanket Mittal       4.8   put_eod_position_set additional attributes trading scope and managed order information tab
  --     07/10/2015  Sanket Mittal       4.9   updatd put_position to add trading_scope Story BER-2081
  --     07/10/2015  Sanket Mittal       5.0   Updated EOD_OPEN_POSITIONS and EOD_OPEN_TRADES for p_record_source = 'SPEEDBET'
  --     30/01/2016  Sanket Mittal       5.2   put_eod_position_set: New columns for binary added
  --     01/09/2016  Sanket Mittal       5.8   BER-2872 Knockouts Normalisation - Add the STRIKE_PRICE
  --     18/10/2016  Sanket Mittal       6.0   BER-2969 BI_ODS.EOD_OPEN_TRADES - add new columns
  --     18/10/2016  Sanket Mittal       6.0   BER-2986 Correction of NRG_POSITION package 'SPPEEDBET'
  --     10/05/2018  Patrick Dinwiddy    6.7   BER-3731 enable batching of put_eod_position_set
  --     03/12/2019  Patrick Dinwiddy    7.3   JCS-11274 clean up package code, remove redundant/obsolete commented sections
  --
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION version RETURN VARCHAR2 DETERMINISTIC;


  -- ===================================================================================
  -- put_eod_position_snapshots
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure maintains the helper table BI_ODS.EOD_POSITION_SNAPSHOTS
  --     which is used to aid loading of the TRADING_ACTIVITY_SUMMARY table in EDW.
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     EOD_POSITION_SNAPSHOTS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_eod_position_snapshots (p_snapshot_time        IN TIMESTAMP,
                                        p_platform             IN VARCHAR2,
                                        p_summary_type         IN VARCHAR2,
                                        p_is_processed         IN VARCHAR2,
                                        p_processed_time       IN TIMESTAMP,
                                        p_record_source        IN eod_open_trades.record_source%TYPE);

  -- ===================================================================================
  -- put_eod_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     LATEST_POSITIONS
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_business_date                       Business Date
  --      p_reporting_date                      Reporting Date
  --      p_position                            Position Details
  --      p_record_source                       Record Source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------
  PROCEDURE put_anytime_position_set(p_user                      IN VARCHAR2,
                                     p_effective_start_timestamp IN TIMESTAMP,
                                     p_platform                  IN VARCHAR2,
                                     p_anytime_positions         IN anytime_positions_tab);

  PROCEDURE put_eod_position_set (p_user                      IN VARCHAR2,
                                  p_effective_start_timestamp IN TIMESTAMP,
                                  p_platform                  IN VARCHAR2,
                                  p_position                  IN position_tab,
                                  p_record_source             IN eod_open_trades.record_source%TYPE,
                                  p_batch_detail              IN VARCHAR2);
  -- ===================================================================================
  -- put_eod_cash_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     EOD_CASH_POSITIONS
  --     MM_CASH_MOVEMENTS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_business_date                       Business Date
  --      p_reporting_date                      Reporting Date
  --      p_position                            Position Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_eod_cash_position_set (p_user                            IN VARCHAR2,
                                       p_effective_start_timestamp       IN TIMESTAMP,
                                       p_platform                        IN VARCHAR2,
                                       p_cash_position                   IN position_tab);

  -- ===================================================================================
  -- upd_mm_eod_open_trades
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will update value in ODS and update it
  --     in bi_ods.eod_open_trades table for MM
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_mm_eod_open_trades (p_business_date    IN DATE
                                    ,p_reporting_date   IN DATE
                                    ,p_platform         IN VARCHAR2);

  -- ===================================================================================
  -- upd_eod_open_trades_hedge_fx
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_eod_open_trades_hedge_fx (p_business_date    IN DATE,
                                           p_reporting_date   IN DATE,
                                           p_platform         VARCHAR2,
                                           p_record_source    VARCHAR2);

  -- ===================================================================================
  -- upd_eod_open_trades
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  upd_eod_open_trades (p_business_date    IN DATE,
                                  p_reporting_date   IN DATE,
                                  p_platform         VARCHAR2,
                                  p_record_source    VARCHAR2);

  -- ===================================================================================
  -- upd_eod_open_trades_metatrader
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will update additional MT4 calculations
  --     in bi_ods.eod_open_trades table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     eod_open_trades
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE upd_eod_open_trades_metatrader (p_business_date    IN DATE,
                                            p_reporting_date   IN DATE,
                                            p_platform         VARCHAR2,
                                            p_record_source    VARCHAR2);
  -- ===================================================================================
  -- insert_spdbet_eod_open_trades
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     eod_open_positions[_h]
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE  insert_spdbet_eod_open_trades (p_business_date             IN DATE,
                                            p_reporting_date            IN DATE,
                                            p_effective_start_timestamp IN TIMESTAMP,
                                            p_snapshot_time             IN TIMESTAMP,
                                            p_platform                  IN VARCHAR2,
                                            p_user                      IN VARCHAR2);

  -- ===================================================================================
  -- insert_eod_open_positions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will calculate the margin value in ODS and update it
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     eod_open_positions[_h]
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------


  PROCEDURE  insert_eod_open_positions (p_business_date             IN eod_open_positions.business_date%TYPE,
                                        p_reporting_date            IN eod_open_positions.reporting_date%TYPE,
                                        p_effective_start_timestamp IN eod_open_positions.Effective_Start_Timestamp%TYPE,
                                        p_logical_load_timestamp    IN eod_open_positions.Logical_Load_Timestamp%TYPE,
                                        p_user                      IN eod_open_positions.Created_By%TYPE,
                                        p_record_source             IN eod_open_positions.record_source%TYPE,
                                        p_platform                  IN Eod_Open_Positions.Platform%TYPE);

  -- ===================================================================================
  -- upd_eod_positions_metatrader
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure will update additional MT4 calculations
  --     in bi_ods.eod_positions table
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially updated:
  --
  --     eod_open_positions[_h]
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE upd_eod_positions_metatrader (p_business_date    IN DATE,
                                          p_reporting_date   IN DATE,
                                          p_platform         VARCHAR2,
                                          p_record_source    VARCHAR2);
  -- ===================================================================================
  -- get_hdg_exec_commission
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to get price band
  --
  -- Parameters:
  -- -----------
  --
  --     p_trading_account_id         Trading Account Id
  --     p_reporting_date             Reporting Date
  --     p_product_instrument_code    Product Instrument Code
  --     p_hedge_risk_bucket          Hedge Rish Bucket
  --     p_mm_value_date              Market Maker Value Date
  --
  -- Return:
  -- -------
  --
  --     date reporting date at midnight
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION get_hdg_exec_commission(p_trading_account_id      IN Eod_Open_Trades.Trading_Account_Id%TYPE,
                                   p_reporting_date          IN Eod_Open_Trades.reporting_date%TYPE,
                                   p_product_instrument_code IN Eod_Open_Trades.product_instrument_code%TYPE,
                                   p_hedge_risk_bucket       IN Eod_Open_Trades.hedge_risk_bucket%TYPE,
                                   p_mm_value_date           IN Eod_Open_Trades.mm_value_date%TYPE)
  RETURN NUMBER;

END nrg_position_old;
/