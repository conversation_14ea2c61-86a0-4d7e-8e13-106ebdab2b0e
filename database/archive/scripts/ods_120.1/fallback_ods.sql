-- ===================================================================================
-- fallback_ods.sql
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Wrapper script to fallback BI_ODS schema
--
-- Notes:
-- ------
--
--     Oracle environments
--
-- Modifications:
-- --------------
--
--     Date         Modified By       Ref     Action
--     ----------   ---------------   -----   ----------------------------------------
--     11/05/2023   <PERSON>  1.0     Creation
--
-- -----------------------------------------------------------------------------------

DEFINE application_name=ods
DEFINE application_version=120.1
DEFINE build_number=1

DEFINE home_path=/dba/releases
DEFINE src=&&home_path/&&application_name._&&application_version/fallback
DEFINE logs=&&home_path/logs

SET LINESIZE 200
SET PAGESIZE 50

COLUMN start_time NEW_VALUE gv_start_time
COLUMN end_time   NEW_VALUE gv_end_time
COLUMN db_name    NEW_VALUE gv_db_name

SELECT TO_CHAR(SYSDATE,'ddmonyyhh24miss') AS start_time
FROM   dual;

SELECT SYS_CONTEXT('USERENV', 'DB_NAME') AS db_name
FROM   dual;

ACCEPT dba PROMPT "Please enter dba performing release: "
ACCEPT dba_password PROMPT "Please enter password for dba performing release: " HIDE

SPOOL &logs/FallBack_&build_number._&&application_name._&&application_version._&gv_db_name._&gv_start_time..log

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Get All Invalid Objects                                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

SELECT owner,
       object_type,
       SUBSTR(object_name, 1, 30) AS object_name,
       status
FROM   dba_objects
WHERE  status != 'VALID'
ORDER BY owner,
         object_type,
         object_name;

-- -----------------------------
-- START OF CUSTOMIZABLE SECTION
-- -----------------------------
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ BI_ODS actions via ORADBA                                             +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

DEFINE ods_username       = "bi_ods"

CONNECT &&dba[&&ods_username]/&&dba_password

SHOW USER

WHENEVER SQLERROR CONTINUE

ACCEPT gv_continue_y_n PROMPT "Hit enter if OK to continue or hit CTRL-D to exit script"

PROMPT *****************************
PROMPT DATAFIX INSTRUMENTS
PROMPT *****************************
PROMPT

@"&src/ods/data/upd_instruments.sql"

SHOW ERRORS
PROMPT

PROMPT
PROMPT *****************************
PROMPT CREATE PACKAGES
PROMPT *****************************


PROMPT *****************************
PROMPT CREATE PACKAGE nrg_products
PROMPT *****************************

@"&src/ods/packages/nrg_products.pks"
@"&src/ods/packages/nrg_products.pkb"

SHOW ERRORS
PROMPT

PROMPT ******************************
PROMPT RECOMPILE PACKAGES
PROMPT ******************************
PROMPT

@"&src/ods/packages/recompile_packages.sql"

SHOW ERRORS
PROMPT

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Update RELEASE_LOG table with fallback information as ORADBA          +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

ACCEPT gv_continue_y_n PROMPT "Hit enter if OK to continue or hit CTRL-D to exit script"

UPDATE release_log
SET    rollback_date = current_timestamp,
       rollback_by = '&&dba'
WHERE  application_name = '&&application_name'
AND    version_number = '&&application_version'
AND    build_number = '&&build_number'
AND    rollback_date IS NULL;

COMMIT;

-- ---------------------------
-- END OF CUSTOMIZABLE SECTION
-- ---------------------------

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Get All Invalid Objects                                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SHOW USER

WHENEVER SQLERROR EXIT

SELECT owner,
       object_type,
       SUBSTR(object_name, 1, 30) AS object_name,
       status
FROM   dba_objects
WHERE  status != 'VALID'
ORDER BY owner,
         object_type,
         object_name;

SELECT TO_CHAR(SYSDATE,'ddmonyyhh24miss') AS end_time FROM dual;

SPOOL OFF

