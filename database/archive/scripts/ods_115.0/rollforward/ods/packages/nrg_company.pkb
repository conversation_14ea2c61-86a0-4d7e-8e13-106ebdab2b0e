CREATE OR REPLACE PACKAGE BODY nrg_company
AS
  -- ===================================================================================
  -- NRG_COMPANY
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the company model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --   ----------   ---------------   -----   ----------------------------------------
  --     03/09/2012   Sanket Mittal      1.0    Creation
  --     11/10/2013   Ravi Shankar Gopal 1.1    Removed Reference to persons.remediation_date BER-664
  --     15/01/2014   Adam Krasnicki     1.2    Adjusted to changed person package
  --     03/02/2014   Adam Krasnicki     1.3    Employee_id added to company_employee_obj, put_company_employee changed as employee_id
  --                                            is Primary Key now instead of company_id and person_id
  --     19/02/2014   Adam Krasnicki     1.4    Modification in put_company_employees table (update of modified fiels is now available, delete of removed employees from tab)
  --     11/03/2014   Adam Krasnicki     1.4    Modification of put_history procedure for company_employees (it based on PK person_id,company_id, eff_start_timestamp)
  --     16/06/2014   Adam Krasnicki     1.4    put_person procedure takes p_us_citizen as the last one
  --     18/07/2014   Adam Krasnicki     1.4    put_company_address the version is removed from comparisons when company_addresses is updated
  --     26/06/2015   Adam Krasnicki     1.5    new table company_tax_registrations and new put procedure for it; delete statment for company_email_addresses
  --     22/07/2015   Adam Krasnicki     1.5    company_telephone_numbers[_h], company_addresses[_h],  is_deleted column added
  --     27/08/2015   Adam Krasnicki     1.5    person_obj changed and put_person call amended
  --     24/12/2015   Sanket Mittal      1.6    Added missing call for put_company_primary_contact on put_copmpany
  --     28/12/2015   Sanket Mittal      1.7    Added procedure for associate shareholding
  --     24/06/2016   Sanket Mittal      1.8    BER-2646-Integrate updated Company data contract - Knockouts
  --     29/11/2016   Sanket Mittal      1.9    BER-3106 ODS - Integrate updated Company data contract
  --     23/01/2017   Sanket Mittal      2.0    BER-3309 ODS - COMPANIES - Integrate updated Company data contract
  --     08/02/2017   S Kinkhabwala      2.1    BER-3309 ODS - COMPANIES - Integrate updated Company data contract
  --     18/04/2017   Sanket Mittal      2.2    BER-3529 BI_ODS.COMPANIES_H not writing IS_DELETED attribute
  --     07/06/2017   Sanket Mittal      2.3    BER-3690 ODS - Corporate Associates that do not exist need to be moved to history.
  --     07/06/2017   Sanket Mittal      2.3    BER-3630 bi_ods.companies_h logs every time a company is requested
  --     24/08/2017   Patrick Dinwiddy   2.4    BER-3917 put_person changes for mififd identification, BER-3920 prim contact history
  --     20/03/2018   D Rajurkar         2.5    BER-4351 ODS - COMPANIES - Legal Entity Identifiers
  --     25/09/2018   Patrick Dinwiddy   2.6    BER-4747 LEI details bug
  --     20/11/2018   Patrick Dinwiddy   2.7    BER-4906 New attribute - Corporate Knowledge Assessor Person Id
  --     22/10/2019   Patrick Dinwiddy   2.8    JCS-11393 new attributes from CM 164
  --     24/09/2020   Patrick Dinwiddy   2.9    JCS-13788 new attribute corp sector type and new additional countries entity
  --     05/11/2020   Patrick Dinwiddy   3.0    JCS-13899 deletes issue for source of wealth
  --     21/09/2021   Oleg Ciobanu       3.1    JCS-14840
  --
  --
  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '3.1';
  gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
  gc_true               CONSTANT PLS_INTEGER := 1;
  gc_false              CONSTANT PLS_INTEGER := 0;
  gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

  --
  --
  -- ===================================================================================
  --  PRIVATE MODULES
  -- ===================================================================================
  --
  --

  PROCEDURE put_history(p_old_record                 IN company_fnncl_dtl_srcs%ROWTYPE,
                        p_effective_end_timestamp    IN company_fnncl_dtl_srcs_h.effective_end_timestamp%TYPE,
                        p_action                     IN company_fnncl_dtl_srcs_h.action%TYPE) IS
  BEGIN
    INSERT INTO company_fnncl_dtl_srcs_h(company_id,
                                         source_id,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         source_name,
                                         source_details,
                                         is_deleted)
                                  VALUES(p_old_record.company_id,
                                         p_old_record.source_id,
                                         p_old_record.logical_load_timestamp,
                                         p_old_record.created_by,
                                         p_old_record.create_timestamp,
                                         p_old_record.updated_by,
                                         p_old_record.update_timestamp,
                                         p_old_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_record.source_name,
                                         p_old_record.source_details,
                                         p_old_record.is_deleted);

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_fnncl_dtl_srcs_h
      SET
          logical_load_timestamp = p_old_record.logical_load_timestamp,
          created_by = p_old_record.created_by,
          create_timestamp = p_old_record.create_timestamp,
          updated_by = p_old_record.updated_by,
          update_timestamp = p_old_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          source_name = p_old_record.source_name,
          source_details = p_old_record.source_details,
          is_deleted = p_old_record.is_deleted
      WHERE company_id = p_old_record.company_id AND
            source_id = p_old_record.source_id AND
            effective_start_timestamp = p_old_record.effective_start_timestamp;
  END;

--DR
PROCEDURE put_history(p_old_lei_record                 IN COMPANY_LEI_DETAILS%ROWTYPE,
                        p_effective_end_timestamp    IN COMPANY_LEI_DETAILS_H.effective_end_timestamp%TYPE,
                        p_action                     IN COMPANY_LEI_DETAILS_H.action%TYPE) IS
  BEGIN



    INSERT INTO COMPANY_LEI_DETAILS_H ( logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         legal_entity_identifier,
                                         LEGAL_NAME,
                                         LEGAL_JURISDICTION,
                                         ENTITY_CATEGORY_TYPE,
                                         ENTITY_LEGAL_FORM_CODE,
                                         ENTITY_STATUS,
                                         ENTITY_EXPIRATION_DATE,
                                         ENTITY_EXPIRATION_REASON,
                                         INITIAL_REGISTRATION_DATE,
                                         LAST_UPDATE_DATE,
                                         REGISTRATION_STATUS,
                                         NEXT_RENEWAL_DATE,
                                         MANAGING_LOU,
                                         VALIDATION_SOURCES)
                                  VALUES(
                                         p_old_lei_record.logical_load_timestamp,
                                         p_old_lei_record.created_by,
                                         p_old_lei_record.create_timestamp,
                                         p_old_lei_record.updated_by,
                                         p_old_lei_record.update_timestamp,
                                         p_old_lei_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_lei_record.LEGAL_ENTITY_IDENTIFIER,
                                         p_old_lei_record.LEGAL_NAME,
                                         p_old_lei_record.LEGAL_JURISDICTION,
                                         p_old_lei_record.ENTITY_CATEGORY_TYPE,
                                         p_old_lei_record.ENTITY_LEGAL_FORM_CODE,
                                         p_old_lei_record.ENTITY_STATUS,
                                         p_old_lei_record.ENTITY_EXPIRATION_DATE,
                                         p_old_lei_record.ENTITY_EXPIRATION_REASON,
                                         p_old_lei_record.INITIAL_REGISTRATION_DATE,
                                         p_old_lei_record.LAST_UPDATE_DATE,
                                         p_old_lei_record.REGISTRATION_STATUS,
                                         p_old_lei_record.NEXT_RENEWAL_DATE,
                                         p_old_lei_record.MANAGING_LOU,
                                         p_old_lei_record.VALIDATION_SOURCES
                                         );

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE COMPANY_LEI_DETAILS_H
      SET
          logical_load_timestamp = p_old_lei_record.logical_load_timestamp,
          created_by = p_old_lei_record.created_by,
          create_timestamp = p_old_lei_record.create_timestamp,
          updated_by = p_old_lei_record.updated_by,
          update_timestamp = p_old_lei_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          LEGAL_NAME=  p_old_lei_record.LEGAL_NAME,
          LEGAL_JURISDICTION= p_old_lei_record.LEGAL_JURISDICTION,
          ENTITY_CATEGORY_TYPE=p_old_lei_record.ENTITY_CATEGORY_TYPE,
          ENTITY_LEGAL_FORM_CODE= p_old_lei_record.ENTITY_LEGAL_FORM_CODE,
          ENTITY_STATUS= p_old_lei_record.ENTITY_STATUS,
          ENTITY_EXPIRATION_DATE= p_old_lei_record.ENTITY_EXPIRATION_DATE,
          ENTITY_EXPIRATION_REASON= p_old_lei_record.ENTITY_EXPIRATION_REASON,
          INITIAL_REGISTRATION_DATE= p_old_lei_record.INITIAL_REGISTRATION_DATE,
          LAST_UPDATE_DATE= p_old_lei_record.LAST_UPDATE_DATE,
          REGISTRATION_STATUS= p_old_lei_record.REGISTRATION_STATUS,
          NEXT_RENEWAL_DATE= p_old_lei_record.NEXT_RENEWAL_DATE,
          MANAGING_LOU= p_old_lei_record.MANAGING_LOU,
          VALIDATION_SOURCES= p_old_lei_record.VALIDATION_SOURCES
      WHERE LEGAL_ENTITY_IDENTIFIER = p_old_lei_record.LEGAL_ENTITY_IDENTIFIER AND
            effective_start_timestamp = p_old_lei_record.effective_start_timestamp;
  END;

--DR


--LEI_ADDRESS_H
--DR
PROCEDURE put_history  (p_old_addr_record            IN COMPANY_LEI_ADDRESSES%ROWTYPE,
                        p_effective_end_timestamp    IN COMPANY_LEI_ADDRESSES_H.effective_end_timestamp%TYPE,
                        p_action                     IN COMPANY_LEI_ADDRESSES_H.action%TYPE) IS
  BEGIN
    INSERT INTO COMPANY_LEI_ADDRESSES_H (LEGAL_ENTITY_IDENTIFIER,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         address_type,
                                         first_address_line,
                                         address_number,
                                         number_within_building,
                                         mail_routing,
                                         additional_address_lines,
                                         city,
                                         region,
                                         country,
                                         postal_code)
                                  VALUES(p_old_addr_record.LEGAL_ENTITY_IDENTIFIER,
                                         p_old_addr_record.logical_load_timestamp,
                                         p_old_addr_record.created_by,
                                         p_old_addr_record.create_timestamp,
                                         p_old_addr_record.updated_by,
                                         p_old_addr_record.update_timestamp,
                                         p_old_addr_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_addr_record.address_type,
                                         p_old_addr_record.first_address_line,
                                         p_old_addr_record.address_number,
                                         p_old_addr_record.number_within_building,
                                         p_old_addr_record.mail_routing,
                                         p_old_addr_record.additional_address_lines,
                                         p_old_addr_record.city,
                                         p_old_addr_record.region,
                                         p_old_addr_record.country,
                                         p_old_addr_record.postal_code
                                         );

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE COMPANY_LEI_ADDRESSES_H
      SET
          logical_load_timestamp = p_old_addr_record.logical_load_timestamp,
          created_by = p_old_addr_record.created_by,
          create_timestamp = p_old_addr_record.create_timestamp,
          updated_by = p_old_addr_record.updated_by,
          update_timestamp = p_old_addr_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          address_type=p_old_addr_record.address_type,
          first_address_line=p_old_addr_record.first_address_line,
          address_number=p_old_addr_record.address_number,
          number_within_building=p_old_addr_record.number_within_building,
          mail_routing=p_old_addr_record.mail_routing,
          additional_address_lines=p_old_addr_record.additional_address_lines,
          city=p_old_addr_record.city,
          region=p_old_addr_record.region,
          country=p_old_addr_record.country,
          postal_code=p_old_addr_record.postal_code
      WHERE LEGAL_ENTITY_IDENTIFIER = p_old_addr_record.LEGAL_ENTITY_IDENTIFIER AND
            effective_start_timestamp = p_old_addr_record.effective_start_timestamp;
  END;

--DR

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company primary contact
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_corporate_associate_record     This is the old version of the corporate associate record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_corporate_associate_record IN company_corporate_associates%ROWTYPE,
                        p_effective_end_timestamp    IN company_corporate_associates_h.effective_end_timestamp%TYPE,
                        p_action                     IN company_corporate_associates_h.action%TYPE) IS

  BEGIN
    INSERT INTO company_corporate_associates_h(corporate_associate_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               effective_end_timestamp,
                                               action,
                                               action_timestamp,
                                               company_id,
                                               person_id,
                                               company_associate_id,
                                               associate_version,
                                               associate_type,
                                               class_name,
                                               company_associate_name,
                                               communication_group_member,
                                               is_deleted,
                                               type_details)
                                       VALUES (p_corporate_associate_record.corporate_associate_id,
                                               p_corporate_associate_record.logical_load_timestamp,
                                               p_corporate_associate_record.created_by,
                                               p_corporate_associate_record.create_timestamp,
                                               p_corporate_associate_record.updated_by,
                                               p_corporate_associate_record.update_timestamp,
                                               p_corporate_associate_record.effective_start_timestamp,
                                               p_effective_end_timestamp,
                                               p_action,
                                               SYSTIMESTAMP,
                                               p_corporate_associate_record.company_id,
                                               p_corporate_associate_record.person_id,
                                               p_corporate_associate_record.company_associate_id,
                                               p_corporate_associate_record.associate_version,
                                               p_corporate_associate_record.associate_type,
                                               p_corporate_associate_record.class_name,
                                               p_corporate_associate_record.company_associate_name,
                                               p_corporate_associate_record.communication_group_member,
                                               p_corporate_associate_record.is_deleted,
                                               p_corporate_associate_record.type_details);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_corporate_associates_h
      SET logical_load_timestamp = p_corporate_associate_record.logical_load_timestamp,
          created_by = p_corporate_associate_record.created_by,
          create_timestamp = p_corporate_associate_record.create_timestamp,
          updated_by = p_corporate_associate_record.updated_by,
          update_timestamp = p_corporate_associate_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          company_id = p_corporate_associate_record.company_id,
          person_id = p_corporate_associate_record.person_id,
          company_associate_id = p_corporate_associate_record.company_associate_id,
          associate_version = p_corporate_associate_record.associate_version,
          associate_type = p_corporate_associate_record.associate_type,
          class_name = p_corporate_associate_record.class_name,
          company_associate_name = p_corporate_associate_record.company_associate_name,
          is_deleted = p_corporate_associate_record.is_deleted,
          communication_group_member = p_corporate_associate_record.communication_group_member,
          type_details = p_corporate_associate_record.type_details
      WHERE corporate_associate_id = p_corporate_associate_record.corporate_associate_id AND
            effective_start_timestamp = p_corporate_associate_record.effective_start_timestamp;
  END;
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company primary contact
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_employee_record                This is the old version of the primary contact
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_primary_contact_record        company_primary_contacts%ROWTYPE,
                         p_effective_end_timestamp       company_primary_contacts_h.effective_end_timestamp%TYPE,
                         p_action                        company_primary_contacts_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO company_primary_contacts_h  (company_id,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             primary_contact_id,
                                             person_id,
                                             primary_contact_version,
                                             job_title)
                                      VALUES(p_primary_contact_record.company_id,
                                             p_primary_contact_record.logical_load_timestamp,
                                             p_primary_contact_record.created_by,
                                             p_primary_contact_record.create_timestamp,
                                             p_primary_contact_record.updated_by,
                                             p_primary_contact_record.update_timestamp,
                                             p_primary_contact_record.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_primary_contact_record.primary_contact_id,
                                             p_primary_contact_record.person_id,
                                             p_primary_contact_record.primary_contact_version,
                                             p_primary_contact_record.job_title);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_primary_contacts_h
         SET logical_load_timestamp = p_primary_contact_record.logical_load_timestamp,
             created_by = p_primary_contact_record.created_by,
             create_timestamp = p_primary_contact_record.create_timestamp,
             updated_by = p_primary_contact_record.updated_by,
             update_timestamp = p_primary_contact_record.update_timestamp,
             effective_end_timestamp = p_effective_end_timestamp,
             action = p_action,
             action_timestamp = SYSTIMESTAMP,
             primary_contact_version = p_primary_contact_record.primary_contact_version,
             job_title = p_primary_contact_record.job_title,
             primary_contact_id = p_primary_contact_record.primary_contact_id
         WHERE company_id = p_primary_contact_record.company_id AND
               effective_start_timestamp = p_primary_contact_record.effective_start_timestamp AND
               (nrg_common.has_value_changed(person_id, p_primary_contact_record.person_id) = 1 OR
                nrg_common.has_value_changed(job_title, p_primary_contact_record.job_title) = 1 OR
                nrg_common.has_value_changed(primary_contact_id, p_primary_contact_record.primary_contact_id) = 1 OR
                nrg_common.has_value_changed(primary_contact_version, p_primary_contact_record.primary_contact_version) = 1);
  END;
  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company email address
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_employee_record                This is the old version of the employee record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_employee_record               company_employees%ROWTYPE,
                         p_effective_end_timestamp       company_employees_h.effective_end_timestamp%TYPE,
                         p_action                        company_employees_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO company_employees_h (company_id,
                                    person_id,
                                    logical_load_timestamp,
                                    created_by,
                                    create_timestamp,
                                    updated_by,
                                    update_timestamp,
                                    effective_start_timestamp,
                                    effective_end_timestamp,
                                    action,
                                    action_timestamp,
                                    employee_version,
                                    job_title,
                                    employee_id,
                                    Is_Deleted,
                                    is_communication_group_member)
                             VALUES(p_employee_record.company_id,
                                    p_employee_record.person_id,
                                    p_employee_record.logical_load_timestamp,
                                    p_employee_record.created_by,
                                    p_employee_record.create_timestamp,
                                    p_employee_record.updated_by,
                                    p_employee_record.update_timestamp,
                                    p_employee_record.effective_start_timestamp,
                                    p_effective_end_timestamp,
                                    p_action,
                                    SYSTIMESTAMP,
                                    p_employee_record.employee_version,
                                    p_employee_record.job_title,
                                    p_employee_record.employee_id,
                                    p_employee_record.Is_Deleted,
                                    p_employee_record.is_communication_group_member);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_employees_h
         SET effective_end_timestamp = p_effective_end_timestamp,
             action = p_action,
             action_timestamp = SYSTIMESTAMP,
             employee_version = p_employee_record.employee_version,
             job_title = p_employee_record.job_title,
             company_id = p_employee_record.company_id,
             person_id = p_employee_record.Person_Id,
             is_deleted = p_employee_record.Is_Deleted,
             is_communication_group_member = p_employee_record.is_communication_group_member
       WHERE company_id = p_employee_record.company_id AND
             person_id = p_employee_record.person_id AND
             effective_start_timestamp = p_employee_record.effective_start_timestamp AND
             (nrg_common.has_value_changed(employee_version, p_employee_record.employee_version) = 1 OR
              nrg_common.has_value_changed(job_title, p_employee_record.job_title) = 1 OR
              nrg_common.has_value_changed(employee_id, p_employee_record.employee_id) = 1 OR
              nrg_common.has_value_changed(Is_Deleted, p_employee_record.Is_Deleted) = 1 OR
              nrg_common.has_value_changed(is_communication_group_member, p_employee_record.is_communication_group_member) = 1);
  END;
  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company email address
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_email_address_record           This is the old version of the email address record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_email_address_record          company_email_addresses%ROWTYPE,
                         p_effective_end_timestamp       company_email_addresses_h.effective_end_timestamp%TYPE,
                         p_action                        company_email_addresses_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO company_email_addresses_h (company_id,
                                           email_address_id,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           email_version,
                                           email_type,
                                           email_address,
                                           is_primary,
                                           is_deleted)
                                    VALUES(p_email_address_record.company_id,
                                           p_email_address_record.email_address_id,
                                           p_email_address_record.logical_load_timestamp,
                                           p_email_address_record.created_by,
                                           p_email_address_record.create_timestamp,
                                           p_email_address_record.updated_by,
                                           p_email_address_record.update_timestamp,
                                           p_email_address_record.effective_start_timestamp,
                                           p_effective_end_timestamp,
                                           p_action,
                                           SYSTIMESTAMP,
                                           p_email_address_record.email_version,
                                           p_email_address_record.email_type,
                                           p_email_address_record.email_address,
                                           p_email_address_record.is_primary,
                                           p_email_address_record.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_email_addresses_h
         SET logical_load_timestamp = p_email_address_record.logical_load_timestamp,
             created_by = p_email_address_record.created_by,
             create_timestamp = p_email_address_record.create_timestamp,
             updated_by = p_email_address_record.updated_by,
             update_timestamp = p_email_address_record.update_timestamp,
             effective_end_timestamp = p_effective_end_timestamp,
             action = p_action,
             action_timestamp = SYSTIMESTAMP,
             email_version = p_email_address_record.email_version,
             email_type = p_email_address_record.email_type,
             email_address = p_email_address_record.email_address,
             is_primary = p_email_address_record.is_primary,
             is_deleted = p_email_address_record.is_deleted
       WHERE company_id = p_email_address_record.company_id AND
             email_address_id = p_email_address_record.email_address_id AND
             effective_start_timestamp = p_email_address_record.effective_start_timestamp AND
             (nrg_common.has_value_changed(email_type, p_email_address_record.email_type) = 1 OR
              nrg_common.has_value_changed(email_address, p_email_address_record.email_address) = 1 OR
              nrg_common.has_value_changed(is_primary, p_email_address_record.is_primary) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_email_address_record.is_deleted) = 1 );
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_record                 This is the old version of the company record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_company_record          companies%ROWTYPE,
                         p_effective_end_timestamp companies_h.effective_end_timestamp%TYPE,
                         p_action                  companies_h.action%TYPE) IS

  BEGIN
    INSERT INTO companies_h (company_id,
                             logical_load_timestamp,
                             created_by,
                             create_timestamp,
                             updated_by,
                             update_timestamp,
                             effective_start_timestamp,
                             effective_end_timestamp,
                             action,
                             action_timestamp,
                             partner_id,
                             company_name,
                             full_legal_name,
                             company_type,
                             company_version,
                             industry,
                             fnncl_srvcs_license_number,
                             registration_number,
                             spoken_language,
                             date_of_incorporation,
                             country_of_incorporation,
                             place_of_incorporation,
                             customer_sub_type,
                             vat_registration_number,
                             country_of_domicile,
                             fatca_entity_classification,
                             nffe_classification,
                             giin,
                             legal_entity_identifier,
                             legal_entity_identifier_expiry,
                             corporate_sector,
                             is_financial_counterparty,
                             is_delegated_report_required,
                             is_daily_report_extr_required,
                             customer_id,
                             industry_further_details,
                             industry_other_details,
                             is_politically_exposed,
                             politically_exposed_details,
                             regulatory_body,
                             net_worth_range_crrncy,
                             net_worth_range,
                             cumulative_risk_limit_crrncy,
                             cumulative_risk_limit_amount,
                             is_institutional_investor,
                             knowledge_assessor_person_id,
                             is_deleted,
                             company_form,
                             company_name_latin,
                             is_no_shrhldrs_over_25_perc,
                             corporate_sector_type)
                    VALUES  (p_company_record.company_id,
                             p_company_record.logical_load_timestamp,
                             p_company_record.created_by,
                             p_company_record.create_timestamp,
                             p_company_record.updated_by,
                             p_company_record.update_timestamp,
                             p_company_record.effective_start_timestamp,
                             p_effective_end_timestamp,
                             p_action,
                             SYSTIMESTAMP,
                             p_company_record.partner_id,
                             p_company_record.company_name,
                             p_company_record.full_legal_name,
                             p_company_record.company_type,
                             p_company_record.company_version,
                             p_company_record.industry,
                             p_company_record.fnncl_srvcs_license_number,
                             p_company_record.registration_number,
                             p_company_record.spoken_language,
                             p_company_record.date_of_incorporation,
                             p_company_record.country_of_incorporation,
                             p_company_record.place_of_incorporation,
                             p_company_record.customer_sub_type,
                             p_company_record.vat_registration_number,
                             p_company_record.country_of_domicile,
                             p_company_record.fatca_entity_classification,
                             p_company_record.nffe_classification,
                             p_company_record.giin,
                             p_company_record.legal_entity_identifier,
                             p_company_record.legal_entity_identifier_expiry,
                             p_company_record.corporate_sector,
                             p_company_record.is_financial_counterparty,
                             p_company_record.is_delegated_report_required,
                             p_company_record.is_daily_report_extr_required,
                             p_company_record.customer_id,
                             p_company_record.industry_further_details,
                             p_company_record.industry_other_details,
                             p_company_record.is_politically_exposed,
                             p_company_record.politically_exposed_details,
                             p_company_record.regulatory_body,
                             p_company_record.net_worth_range_crrncy,
                             p_company_record.net_worth_range,
                             p_company_record.cumulative_risk_limit_crrncy,
                             p_company_record.cumulative_risk_limit_amount,
                             p_company_record.is_institutional_investor,
                             p_company_record.knowledge_assessor_person_id,
                             p_company_record.is_deleted,
                             p_company_record.company_form,
                             p_company_record.company_name_latin,
                             p_company_record.is_no_shrhldrs_over_25_perc,
                             p_company_record.corporate_sector_type);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE companies_h
          SET logical_load_timestamp       = p_company_record.logical_load_timestamp,
              updated_by                   = p_company_record.updated_by,
              update_timestamp             = p_company_record.update_timestamp,
              effective_end_timestamp      = p_effective_end_timestamp,
              action                       = p_action,
              action_timestamp             = SYSTIMESTAMP,
              partner_id                   = p_company_record.partner_id,
              company_name                 = p_company_record.company_name,
              full_legal_name              = p_company_record.full_legal_name,
              company_type                 = p_company_record.company_type,
              company_version              = p_company_record.company_version,
              industry                     = p_company_record.industry,
              fnncl_srvcs_license_number   = p_company_record.fnncl_srvcs_license_number,
              registration_number          = p_company_record.registration_number,
              spoken_language              = p_company_record.spoken_language,
              date_of_incorporation        = p_company_record.date_of_incorporation,
              country_of_incorporation     = p_company_record.country_of_incorporation,
              place_of_incorporation       = p_company_record.place_of_incorporation,
              customer_sub_type            = p_company_record.customer_sub_type,
              vat_registration_number      = p_company_record.vat_registration_number,
              country_of_domicile          = p_company_record.country_of_domicile,
              fatca_entity_classification  = p_company_record.fatca_entity_classification,
              nffe_classification          = p_company_record.nffe_classification,
              giin                         = p_company_record.giin,
              legal_entity_identifier      = p_company_record.legal_entity_identifier,
              legal_entity_identifier_expiry = p_company_record.legal_entity_identifier_expiry,
              corporate_sector             = p_company_record.corporate_sector,
              is_financial_counterparty    = p_company_record.is_financial_counterparty,
              is_delegated_report_required = p_company_record.is_delegated_report_required,
              is_daily_report_extr_required= p_company_record.is_daily_report_extr_required,
              customer_id                  = p_company_record.customer_id,
              industry_further_details     = p_company_record.industry_further_details,
              industry_other_details       = p_company_record.industry_other_details,
              is_politically_exposed       = p_company_record.is_politically_exposed,
              politically_exposed_details  = p_company_record.politically_exposed_details,
              regulatory_body              = p_company_record.regulatory_body,
              net_worth_range_crrncy       = p_company_record.net_worth_range_crrncy,
              net_worth_range              = p_company_record.net_worth_range,
              cumulative_risk_limit_crrncy = p_company_record.cumulative_risk_limit_crrncy,
              cumulative_risk_limit_amount = p_company_record.cumulative_risk_limit_amount,
              is_institutional_investor    = p_company_record.is_institutional_investor,
              knowledge_assessor_person_id = p_company_record.knowledge_assessor_person_id,
              is_deleted                   = p_company_record.is_deleted,
              company_form                 = p_company_record.company_form,
              company_name_latin           = p_company_record.company_name_latin,
              is_no_shrhldrs_over_25_perc  = p_company_record.is_no_shrhldrs_over_25_perc,
              corporate_sector_type        = p_company_record.corporate_sector_type
          WHERE company_id = p_company_record.company_id AND
                effective_start_timestamp = p_company_record.effective_start_timestamp AND
                (nrg_common.has_value_changed(partner_id,p_company_record.partner_id) = 1 OR
                 nrg_common.has_value_changed(company_name,p_company_record.company_name) = 1 OR
                 nrg_common.has_value_changed(full_legal_name,p_company_record.full_legal_name) = 1 OR
                 nrg_common.has_value_changed(company_type,p_company_record.company_type) = 1 OR
                 nrg_common.has_value_changed(company_version,p_company_record.company_version) = 1 OR
                 nrg_common.has_value_changed(industry,p_company_record.industry) = 1 OR
                 nrg_common.has_value_changed(fnncl_srvcs_license_number,p_company_record.fnncl_srvcs_license_number) = 1 OR
                 nrg_common.has_value_changed(registration_number,p_company_record.registration_number) = 1 OR
                 nrg_common.has_value_changed(spoken_language,p_company_record.spoken_language) = 1 OR
                 nrg_common.has_value_changed(date_of_incorporation,p_company_record.date_of_incorporation) = 1 OR
                 nrg_common.has_value_changed(country_of_incorporation,p_company_record.country_of_incorporation) = 1 OR
                 nrg_common.has_value_changed(place_of_incorporation,p_company_record.place_of_incorporation) = 1 OR
                 nrg_common.has_value_changed(customer_sub_type, p_company_record.customer_sub_type) = 1 OR
                 nrg_common.has_value_changed(vat_registration_number, p_company_record.vat_registration_number) = 1 OR
                 nrg_common.has_value_changed(country_of_domicile, p_company_record.country_of_domicile) = 1 OR
                 nrg_common.has_value_changed(fatca_entity_classification, p_company_record.fatca_entity_classification) = 1 OR
                 nrg_common.has_value_changed(nffe_classification, p_company_record.nffe_classification) = 1 OR
                 nrg_common.has_value_changed(giin, p_company_record.giin) = 1 OR
                 nrg_common.has_value_changed(legal_entity_identifier, p_company_record.legal_entity_identifier) = 1 OR
                 nrg_common.has_value_changed(legal_entity_identifier_expiry, p_company_record.legal_entity_identifier_expiry) = 1 OR
                 nrg_common.has_value_changed(corporate_sector, p_company_record.corporate_sector) = 1 OR
                 nrg_common.has_value_changed(is_financial_counterparty, p_company_record.is_financial_counterparty) = 1 OR
                 nrg_common.has_value_changed(is_delegated_report_required, p_company_record.is_delegated_report_required) = 1 OR
                 nrg_common.has_value_changed(is_daily_report_extr_required, p_company_record.is_daily_report_extr_required) = 1 OR
                 nrg_common.has_value_changed(customer_id, p_company_record.customer_id) = 1 OR
                 nrg_common.has_value_changed(industry_further_details, p_company_record.industry_further_details) = 1 OR
                 nrg_common.has_value_changed(industry_other_details, p_company_record.industry_other_details) = 1 OR
                 nrg_common.has_value_changed(is_politically_exposed, p_company_record.is_politically_exposed) = 1 OR
                 nrg_common.has_value_changed(politically_exposed_details, p_company_record.politically_exposed_details) = 1 OR
                 nrg_common.has_value_changed(regulatory_body, p_company_record.regulatory_body) = 1 OR
                 nrg_common.has_value_changed(net_worth_range_crrncy, p_company_record.net_worth_range_crrncy) = 1 OR
                 nrg_common.has_value_changed(net_worth_range, p_company_record.net_worth_range) = 1 OR
                 nrg_common.has_value_changed(cumulative_risk_limit_crrncy, p_company_record.cumulative_risk_limit_crrncy) = 1 OR
                 nrg_common.has_value_changed(cumulative_risk_limit_amount, p_company_record.cumulative_risk_limit_amount) = 1 OR
                 nrg_common.has_value_changed(is_institutional_investor, p_company_record.is_institutional_investor) = 1 OR
                 nrg_common.has_value_changed(knowledge_assessor_person_id, p_company_record.knowledge_assessor_person_id) = 1 OR
                 nrg_common.has_value_changed(is_deleted, p_company_record.is_deleted) = 1 OR
                 nrg_common.has_value_changed(company_form, p_company_record.company_form) = 1 OR
                 nrg_common.has_value_changed(company_name_latin, p_company_record.company_name_latin) = 1 OR
                 nrg_common.has_value_changed(is_no_shrhldrs_over_25_perc, p_company_record.is_no_shrhldrs_over_25_perc) = 1 OR
                 nrg_common.has_value_changed(corporate_sector_type, p_company_record.corporate_sector_type) = 1
                 );
  END;
  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_record                 This is the old version of the company record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_address_record           company_addresses%ROWTYPE,
                         p_effective_end_timestamp  company_addresses_h.effective_end_timestamp%TYPE,
                         p_action                   company_addresses_h.action%TYPE) IS
  BEGIN
    INSERT INTO company_addresses_h (company_id,
                                     address_id,
                                     logical_load_timestamp,
                                     created_by,
                                     create_timestamp,
                                     updated_by,
                                     update_timestamp,
                                     effective_start_timestamp,
                                     effective_end_timestamp,
                                     action,
                                     action_timestamp,
                                     address_version,
                                     address_type,
                                     years_at_address,
                                     address_line_1,
                                     address_line_2,
                                     address_line_3,
                                     address_line_4,
                                     address_line_5,
                                     postal_code,
                                     country_code,
                                     is_primary,
                                     is_previous,
                                     is_deleted,
                                     related_employment_details_id)
                              VALUES(p_address_record.company_id,
                                     p_address_record.address_id,
                                     p_address_record.logical_load_timestamp,
                                     p_address_record.created_by,
                                     p_address_record.create_timestamp,
                                     p_address_record.updated_by,
                                     p_address_record.update_timestamp,
                                     p_address_record.effective_start_timestamp,
                                     p_effective_end_timestamp,
                                     p_action,
                                     SYSTIMESTAMP,
                                     p_address_record.address_version,
                                     p_address_record.address_type,
                                     p_address_record.years_at_address,
                                     p_address_record.address_line_1,
                                     p_address_record.address_line_2,
                                     p_address_record.address_line_3,
                                     p_address_record.address_line_4,
                                     p_address_record.address_line_5,
                                     p_address_record.postal_code,
                                     p_address_record.country_code,
                                     p_address_record.is_primary,
                                     p_address_record.is_previous,
                                     p_address_record.Is_Deleted,
                                     p_address_record.related_employment_details_id);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_addresses_h
         SET logical_load_timestamp = p_address_record.logical_load_timestamp,
             created_by = p_address_record.created_by,
             create_timestamp = p_address_record.create_timestamp,
             updated_by = p_address_record.updated_by,
             update_timestamp = p_address_record.update_timestamp,
             effective_end_timestamp = p_effective_end_timestamp,
             action = p_action,
             action_timestamp = SYSTIMESTAMP,
             address_version = p_address_record.address_version,
             address_type = p_address_record.address_type,
             years_at_address = p_address_record.years_at_address,
             address_line_1 = p_address_record.address_line_1,
             address_line_2 = p_address_record.address_line_2,
             address_line_3 = p_address_record.address_line_3,
             address_line_4 = p_address_record.address_line_4,
             address_line_5 = p_address_record.address_line_5,
             postal_code = p_address_record.postal_code,
             country_code = p_address_record.country_code,
             is_primary = p_address_record.is_primary,
             is_previous = p_address_record.is_previous,
             is_deleted = p_address_record.Is_Deleted,
             related_employment_details_id = p_address_record.related_employment_details_id
       WHERE company_id = p_address_record.company_id AND
             address_id = p_address_record.address_id AND
             effective_start_timestamp = p_address_record.effective_start_timestamp AND
             (nrg_common.has_value_changed(address_type, p_address_record.address_type) = 1 OR
              nrg_common.has_value_changed(years_at_address, p_address_record.years_at_address) = 1 OR
              nrg_common.has_value_changed(address_line_1, p_address_record.address_line_1) = 1 OR
              nrg_common.has_value_changed(address_line_2, p_address_record.address_line_2) = 1 OR
              nrg_common.has_value_changed(address_line_3, p_address_record.address_line_3) = 1 OR
              nrg_common.has_value_changed(address_line_4, p_address_record.address_line_4) = 1 OR
              nrg_common.has_value_changed(address_line_5, p_address_record.address_line_5) = 1 OR
              nrg_common.has_value_changed(postal_code, p_address_record.postal_code) = 1 OR
              nrg_common.has_value_changed(country_code, p_address_record.country_code) = 1 OR
              nrg_common.has_value_changed(is_primary, p_address_record.is_primary) = 1 OR
              nrg_common.has_value_changed(is_previous, p_address_record.is_previous) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_address_record.Is_Deleted) = 1 OR
              nrg_common.has_value_changed(related_employment_details_id, p_address_record.related_employment_details_id) = 1);
  END;
  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_record                 This is the old version of the company record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_telephone_record        company_telephone_numbers%ROWTYPE,
                         p_effective_end_timestamp company_telephone_numbers_h.effective_end_timestamp%TYPE,
                         p_action                  company_telephone_numbers_h.action%TYPE) IS
  BEGIN
    INSERT INTO company_telephone_numbers_h (company_id,
                                             telephone_id,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             telephone_version,
                                             telephone_type,
                                             telephone_number,
                                             is_primary,
                                             is_deleted)
                                      VALUES(p_telephone_record.company_id,
                                             p_telephone_record.telephone_id,
                                             p_telephone_record.logical_load_timestamp,
                                             p_telephone_record.created_by,
                                             p_telephone_record.create_timestamp,
                                             p_telephone_record.updated_by,
                                             p_telephone_record.update_timestamp,
                                             p_telephone_record.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_telephone_record.telephone_version,
                                             p_telephone_record.telephone_type,
                                             p_telephone_record.telephone_number,
                                             p_telephone_record.is_primary,
                                             p_telephone_record.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_telephone_numbers_h
         SET logical_load_timestamp = p_telephone_record.logical_load_timestamp,
             created_by = p_telephone_record.created_by,
             create_timestamp = p_telephone_record.create_timestamp,
             updated_by = p_telephone_record.updated_by,
             update_timestamp = p_telephone_record.update_timestamp,
             action = p_action,
             action_timestamp = SYSTIMESTAMP,
             telephone_version = p_telephone_record.telephone_version,
             telephone_type = p_telephone_record.telephone_type,
             telephone_number = p_telephone_record.telephone_number,
             is_primary = p_telephone_record.is_primary,
             effective_end_timestamp = p_effective_end_timestamp,
             is_deleted = p_telephone_record.is_deleted
       WHERE company_id = p_telephone_record.company_id AND
             telephone_id = p_telephone_record.telephone_id AND
             effective_start_timestamp = p_telephone_record.effective_start_timestamp AND
             (nrg_common.has_value_changed(telephone_version, p_telephone_record.telephone_version) = 1 OR
              nrg_common.has_value_changed(telephone_type, p_telephone_record.telephone_type) = 1 OR
              nrg_common.has_value_changed(telephone_number, p_telephone_record.telephone_number) = 1 OR
              nrg_common.has_value_changed(is_primary, p_telephone_record.is_primary) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_telephone_record.is_deleted) = 1 );
  END;

  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_tax_registration       This is the old version of the company record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_company_tax_registration company_tax_registrations%ROWTYPE,
                         p_effective_end_timestamp  company_tax_registrations_h.effective_end_timestamp%TYPE,
                         p_action                   company_tax_registrations_h.action%TYPE) IS
  BEGIN
    INSERT INTO company_tax_registrations_h (company_id,
                                             country_of_issue,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             registration_number,
                                             Is_Deleted)
                                      VALUES(p_company_tax_registration.company_id,
                                             p_company_tax_registration.country_of_issue,
                                             p_company_tax_registration.logical_load_timestamp,
                                             p_company_tax_registration.created_by,
                                             p_company_tax_registration.create_timestamp,
                                             p_company_tax_registration.updated_by,
                                             p_company_tax_registration.update_timestamp,
                                             p_company_tax_registration.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_company_tax_registration.registration_number ,
                                             p_company_tax_registration.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_tax_registrations_h
         SET logical_load_timestamp = p_company_tax_registration.logical_load_timestamp,
             created_by = p_company_tax_registration.created_by,
             create_timestamp = p_company_tax_registration.create_timestamp,
             updated_by = p_company_tax_registration.updated_by,
             update_timestamp = p_company_tax_registration.update_timestamp,
             action = p_action,
             action_timestamp = SYSTIMESTAMP,
             effective_end_timestamp = p_effective_end_timestamp,
             registration_number = p_company_tax_registration.registration_number,
             is_deleted = p_company_tax_registration.is_deleted
       WHERE company_id = p_company_tax_registration.company_id AND
             country_of_issue = p_company_tax_registration.country_of_issue AND
             effective_start_timestamp = p_company_tax_registration.effective_start_timestamp AND
             (nrg_common.has_value_changed(registration_number, p_company_tax_registration.registration_number) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_company_tax_registration.is_deleted) = 1);
  END;

  PROCEDURE put_history (p_effective_end_timestamp company_crprt_assct_shrhldng.effective_start_timestamp%TYPE,
                        p_action company_crprt_assct_shrhldng_h.action%TYPE,
                        p_old_record company_crprt_assct_shrhldng%ROWTYPE) IS

  BEGIN

    logger.logger.set_module('put_history - associate_shareholding');

    INSERT INTO company_crprt_assct_shrhldng_h(company_id,
                                               company_associate_id,
                                               shareholding_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               effective_end_timestamp,
                                               action,
                                               action_timestamp,
                                               percentage_holding,
                                               ubo_percentage,
                                               shareholding_version)
                                        VALUES(p_old_record.company_id,
                                               p_old_record.company_associate_id,
                                               p_old_record.shareholding_id,
                                               p_old_record.logical_load_timestamp,
                                               p_old_record.created_by,
                                               p_old_record.create_timestamp,
                                               p_old_record.updated_by,
                                               p_old_record.update_timestamp,
                                               p_old_record.effective_start_timestamp,
                                               p_effective_end_timestamp,
                                               p_action,
                                               SYSTIMESTAMP,
                                               p_old_record.percentage_holding,
                                               p_old_record.ubo_percentage,
                                               p_old_record.shareholding_version);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_crprt_assct_shrhldng_h
      SET logical_load_timestamp = p_old_record.logical_load_timestamp,
          created_by = p_old_record.created_by,
          create_timestamp = p_old_record.create_timestamp,
          updated_by = p_old_record.updated_by,
          update_timestamp = p_old_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          percentage_holding = p_old_record.percentage_holding,
          ubo_percentage = p_old_record.ubo_percentage,
          shareholding_version = p_old_record.shareholding_version
      WHERE company_id = p_old_record.company_id AND
            company_associate_id = p_old_record.company_associate_id AND
            shareholding_id = p_old_record.shareholding_id AND
            effective_start_timestamp = p_old_record.effective_start_timestamp;

    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_addtnl_trdng_cntrys       This is the old version of the company record
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_company_addtnl_trdng_cntrys company_addtnl_trdng_cntrys%ROWTYPE,
                         p_effective_end_timestamp     company_addtnl_trdng_cntrys_h.effective_end_timestamp%TYPE,
                         p_action                      company_addtnl_trdng_cntrys_h.action%TYPE) IS
  BEGIN

    INSERT INTO company_addtnl_trdng_cntrys_h (company_id,
                                               country_code,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               effective_end_timestamp,
                                               action,
                                               action_timestamp,
                                               is_deleted)
                                        VALUES(p_company_addtnl_trdng_cntrys.company_id,
                                               p_company_addtnl_trdng_cntrys.country_code,
                                               p_company_addtnl_trdng_cntrys.logical_load_timestamp,
                                               p_company_addtnl_trdng_cntrys.created_by,
                                               p_company_addtnl_trdng_cntrys.create_timestamp,
                                               p_company_addtnl_trdng_cntrys.updated_by,
                                               p_company_addtnl_trdng_cntrys.update_timestamp,
                                               p_company_addtnl_trdng_cntrys.effective_start_timestamp,
                                               p_effective_end_timestamp,
                                               p_action,
                                               SYSTIMESTAMP,
                                               p_company_addtnl_trdng_cntrys.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE company_addtnl_trdng_cntrys_h
         SET logical_load_timestamp          = p_company_addtnl_trdng_cntrys.logical_load_timestamp,
             created_by                      = p_company_addtnl_trdng_cntrys.created_by,
             create_timestamp                = p_company_addtnl_trdng_cntrys.create_timestamp,
             updated_by                      = p_company_addtnl_trdng_cntrys.updated_by,
             update_timestamp                = p_company_addtnl_trdng_cntrys.update_timestamp,
             action                          = p_action,
             action_timestamp                = SYSTIMESTAMP,
             effective_end_timestamp         = p_effective_end_timestamp,
             is_deleted                      = p_company_addtnl_trdng_cntrys.is_deleted
       WHERE company_id                      = p_company_addtnl_trdng_cntrys.company_id
         AND country_code                    = p_company_addtnl_trdng_cntrys.country_code
         AND effective_start_timestamp       = p_company_addtnl_trdng_cntrys.effective_start_timestamp
         AND (nrg_common.has_value_changed(is_deleted, p_company_addtnl_trdng_cntrys.is_deleted) = 1);

  END;

  PROCEDURE put_associate_shareholding(p_company_id companies.company_id%TYPE,
                                       p_associate_id company_corporate_associates.company_associate_id%TYPE,
                                       p_logical_load_timestamp company_crprt_assct_shrhldng.logical_load_timestamp%TYPE,
                                       p_user company_crprt_assct_shrhldng.created_by%TYPE,
                                       p_effective_start_timestamp company_crprt_assct_shrhldng.effective_start_timestamp%TYPE,
                                       p_old_effective_timestamp company_crprt_assct_shrhldng.effective_start_timestamp%TYPE,
                                       p_shareholding company_shareholding_obj) IS
    lv_insert NUMBER(1) := 0;

    lv_old_record company_crprt_assct_shrhldng%ROWTYPE;

    lex_missing_shareholding_id EXCEPTION;

    lv_shrhldng_exists NUMBER := 0;

    lv_action VARCHAR2(1);
  BEGIN
    logger.logger.set_module('put_associate_shareholding');

    IF p_shareholding.shareholding_id IS NULL THEN
      RAISE lex_missing_shareholding_id;
    END IF;

    BEGIN
      SELECT COUNT(*)
      INTO lv_shrhldng_exists
      FROM company_crprt_assct_shrhldng
      WHERE company_id = p_company_id AND
            company_associate_id = p_associate_id;
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        NULL;
    END;

    CASE
      WHEN lv_shrhldng_exists = 0 THEN
        INSERT INTO company_crprt_assct_shrhldng(company_id,
                                               company_associate_id,
                                               shareholding_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               shareholding_version,
                                               percentage_holding,
                                               ubo_percentage)
                                        VALUES(p_company_id,
                                               p_associate_id,
                                               p_shareholding.shareholding_id,
                                               p_logical_load_timestamp,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_effective_start_timestamp,
                                               p_shareholding.shareholding_version,
                                               p_shareholding.percentage_holding,
                                               p_shareholding.ubo_percentage);
      WHEN lv_shrhldng_exists > 0 AND p_old_effective_timestamp <= p_effective_start_timestamp THEN

        BEGIN
          SELECT *
          INTO lv_old_record
          FROM company_crprt_assct_shrhldng
          WHERE company_id = p_company_id AND
                company_associate_id = p_associate_id AND
                (nrg_common.has_value_changed(shareholding_id, p_shareholding.shareholding_id) = 1 OR
                 nrg_common.has_value_changed(percentage_holding, p_shareholding.percentage_holding) = 1 OR
                 nrg_common.has_value_changed(ubo_percentage, p_shareholding.ubo_percentage) = 1 OR
                 nrg_common.has_value_changed(shareholding_version, p_shareholding.shareholding_version) = 1)
          FOR UPDATE;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL;
        END;

        UPDATE company_crprt_assct_shrhldng
        SET
            shareholding_id = p_shareholding.shareholding_id,
            logical_load_timestamp = p_logical_load_timestamp,
            updated_by = p_user,
            update_timestamp = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            shareholding_version = p_shareholding.shareholding_version,
            percentage_holding = p_shareholding.percentage_holding,
            ubo_percentage = p_shareholding.ubo_percentage
        WHERE company_id = p_company_id AND
              company_associate_id = p_associate_id AND
              (nrg_common.has_value_changed(shareholding_id, p_shareholding.shareholding_id ) = 1 OR
               nrg_common.has_value_changed(percentage_holding, p_shareholding.percentage_holding) = 1 OR
               nrg_common.has_value_changed(ubo_percentage, p_shareholding.ubo_percentage) = 1 OR
               nrg_common.has_value_changed(shareholding_version, p_shareholding.shareholding_version) = 1);

        IF SQL%ROWCOUNT > 0 THEN
          IF lv_old_record.shareholding_id <> p_shareholding.shareholding_id THEN
            lv_action := 'D';
          ELSE
            lv_action := 'U';
          END IF;

          IF lv_old_record.shareholding_id IS NOT NULL THEN
          put_history (p_effective_end_timestamp => p_effective_start_timestamp,
                       p_action => lv_action,
                       p_old_record => lv_old_record);
          END IF;
        END IF;

      WHEN lv_shrhldng_exists > 0 AND p_old_effective_timestamp > p_effective_start_timestamp THEN
        lv_old_record.effective_start_timestamp := p_effective_start_timestamp;
        put_history (p_effective_end_timestamp => p_effective_start_timestamp,
                     p_action => 'I',
                     p_old_record => lv_old_record);
    END CASE;
  EXCEPTION
      WHEN lex_missing_shareholding_id THEN
        NULL;
        --
        --It is mandatory to have shareholder id
        --
      WHEN OTHERS THEN
        logger.logger.SEVERE(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        RAISE;
  END;
  --
  --
  -- ===================================================================================
  -- put_corporate_associate
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the primary contacts for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           This is the name of the user writing this record
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --     p_corporate_associate            This is the corporate associate details
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_corporate_associate(p_user                          company_corporate_associates.created_by%TYPE,
                                    p_effective_start_timestamp     company_corporate_associates.effective_start_timestamp%TYPE,
                                    p_logical_load_timestamp        company_corporate_associates.logical_load_timestamp%TYPE,
                                    p_corporate_associate           company_associate_obj) IS

    lv_insert               NUMBER := 1;

    lv_old_record           company_corporate_associates%ROWTYPE;
    lv_old_shareholding_record company_crprt_assct_shrhldng%ROWTYPE;

    lv_cmpny_crprt_assct_shrhldng company_crprt_assct_shrhldng%ROWTYPE;

    lex_data_after_insert   EXCEPTION;
    lex_invalid_key         EXCEPTION;

    lv_old_version          NUMBER;

    lv_version              NUMBER;

  BEGIN

    --
    --Normalise All Fields
    --

    lv_version := NVL(p_corporate_associate.associate_version, 0);

    --
    --Create Person Record For The Corporate Associate
    --

    IF p_corporate_associate.person IS NOT NULL THEN
      nrg_person.put_person(p_user                          => p_user,
                            p_effective_start_timestamp     => p_effective_start_timestamp,
                            p_person_id                     => p_corporate_associate.person.person_id,
                            p_person_version                => p_corporate_associate.person.person_version,
                            p_title                         => p_corporate_associate.person.title,
                            p_first_name                    => p_corporate_associate.person.first_name,
                            p_last_name                     => p_corporate_associate.person.last_name,
                            p_gender                        => p_corporate_associate.person.gender,
                            p_date_of_birth                 => p_corporate_associate.person.date_of_birth,
                            p_nationality                   => p_corporate_associate.person.nationality,
                            p_spoken_language               => p_corporate_associate.person.spoken_language,
                            p_exchange_data_classification  => p_corporate_associate.person.exchange_data_classification,
                            p_financial_detail_id           => p_corporate_associate.person.financial_detail_id,
                            p_origin_of_wealth              => p_corporate_associate.person.origin_of_wealth,
                            p_origin_of_wealth_detail       => p_corporate_associate.person.origin_of_wealth_detail,
                            p_annual_income_range           => p_corporate_associate.person.annual_income_range,
                            p_annual_income_currency        => p_corporate_associate.person.annual_income_currency,
                            p_val_svngs_invstmnt_range      => p_corporate_associate.person.val_svngs_invstmnt_range,
                            p_val_svngs_invstmnt_currency   => p_corporate_associate.person.val_svngs_invstmnt_currency,
                            p_sources_of_funds              => p_corporate_associate.person.sources_of_funds,
                            p_sources_of_income             => p_corporate_associate.person.sources_of_income,
                            p_employment_details            => p_corporate_associate.person.employment_details,
                            p_person_addresses              => p_corporate_associate.person.person_addresses,
                            p_person_aliases                => p_corporate_associate.person.person_aliases,
                            p_person_email_addresses        => p_corporate_associate.person.person_email_addresses,
                            p_person_national_identifiers   => p_corporate_associate.person.person_national_identifiers,
                            p_person_relationships          => p_corporate_associate.person.person_relationships,
                            p_person_telephone_numbers      => p_corporate_associate.person.person_telephone_numbers,
                            p_customer_ids                  => p_corporate_associate.person.customer_ids,
                            p_power_of_attorneys            => p_corporate_associate.person.power_of_attorneys,
                            p_is_deleted                    => p_corporate_associate.person.is_deleted,
                            p_person_trading_experience     => p_corporate_associate.person.person_trading_experience,
                            p_initial_lead_channel          => p_corporate_associate.person.initial_lead_channel,
                            p_person_trading_profiles       => p_corporate_associate.person.person_trading_profiles,
                            p_us_citizen                    => p_corporate_associate.person.us_citizen,
                            p_marital_status                => p_corporate_associate.person.marital_status,
                            p_market_counterpart_partnr_id  => p_corporate_associate.person.market_counterparty_partner_id,
                            p_middle_name                   => p_corporate_associate.person.middle_name,
                            p_is_politically_exposed        => p_corporate_associate.person.is_politically_exposed,
                            p_politically_exposed_details   => p_corporate_associate.person.politically_exposed_details,
                            p_financial_detail_version      => p_corporate_associate.person.financial_detail_version,
                            p_investment_portfolio          => p_corporate_associate.person.investment_portfolio,
                            p_investment_portfolio_ccy      => p_corporate_associate.person.investment_portfolio_currency,
                            p_liquid_assets                 => p_corporate_associate.person.liquid_assets,
                            p_liquid_assets_currency        => p_corporate_associate.person.liquid_assets_currency,
                            p_cumulative_risk_limit         => p_corporate_associate.person.cumulative_risk_limit,
                            p_cumulative_risk_limit_ccy     => p_corporate_associate.person.cumulative_risk_limit_currency,
                            p_exchng_data_clssfctn_cnfrmd   => p_corporate_associate.person.exchng_data_clssfctn_cnfrmd,
                            p_is_accredited_investor        => p_corporate_associate.person.is_accredited_investor,
                            p_is_related_to_regulated_firm  => p_corporate_associate.person.is_related_to_regulated_firm,
                            p_is_insider_of_public_company  => p_corporate_associate.person.is_insider_of_public_company,
                            p_person_mifid_idntfctns        => p_corporate_associate.person.person_mifid_idntfctns,
                            p_first_name_latin              => p_corporate_associate.person.first_name_latin,
                            p_last_name_latin               => p_corporate_associate.person.last_name_latin,
                            p_nationality_country_code      => p_corporate_associate.person.nationality_country_code,
                            p_is_mifid_details_remediated   => p_corporate_associate.person.is_mifid_details_remediated,
                            p_mkt_cpty_rgltry_clssfctn      => p_corporate_associate.person.mkt_cpty_rgltry_clssfctn,
                            p_country_of_birth              => p_corporate_associate.person.country_of_birth,
                            p_vulnerability_level           => p_corporate_associate.person.vulnerability_level
                            );

    END IF;

    BEGIN
      INSERT INTO company_corporate_associates(corporate_associate_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               company_id,
                                               person_id,
                                               company_associate_id,
                                               associate_version,
                                               associate_type,
                                               class_name,
                                               company_associate_name,
                                               is_deleted,
                                               communication_group_member,
                                               type_details)
                                        VALUES(p_corporate_associate.associate_id,
                                               p_logical_load_timestamp,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_effective_start_timestamp,
                                               p_corporate_associate.company_id,
                                               p_corporate_associate.person.person_id,
                                               p_corporate_associate.company_associate_id,
                                               lv_version,
                                               p_corporate_associate.associate_type,
                                               p_corporate_associate.class_name,
                                               p_corporate_associate.company_associate_name,
                                               p_corporate_associate.is_deleted,
                                               p_corporate_associate.communication_group_member,
                                               p_corporate_associate.type_details);


    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        lv_insert := 0;
        BEGIN
          SELECT *
          INTO lv_old_record
          FROM company_corporate_associates
          WHERE corporate_associate_id = p_corporate_associate.associate_id;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            RAISE lex_data_after_insert;
          WHEN TOO_MANY_ROWS THEN
            RAISE lex_invalid_key;
        END;


        lv_old_version := lv_old_record.associate_version;
    END;

    CASE
      WHEN lv_insert = 1 THEN
        --
        -- Since data has been inserted nothing to do
        --

        NULL;

      WHEN lv_insert = 0 AND lv_old_version <= lv_version THEN
        UPDATE company_corporate_associates
        SET logical_load_timestamp = p_logical_load_timestamp,
            updated_by = p_user,
            update_timestamp = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            company_id = p_corporate_associate.company_id,
            person_id = p_corporate_associate.person.person_id,
            company_associate_id = p_corporate_associate.company_associate_id,
            associate_version = lv_version,
            associate_type = p_corporate_associate.associate_type,
            class_name = p_corporate_associate.class_name,
            company_associate_name = p_corporate_associate.company_associate_name,
            is_deleted = p_corporate_associate.is_deleted,
            communication_group_member = p_corporate_associate.communication_group_member,
            type_details = p_corporate_associate.type_details
        WHERE corporate_associate_id = p_corporate_associate.associate_id  AND
              (nrg_common.has_value_changed(company_id, p_corporate_associate.company_id) = 1 OR
               nrg_common.has_value_changed(person_id, p_corporate_associate.person.person_id) = 1 OR
               nrg_common.has_value_changed(company_associate_id, p_corporate_associate.company_associate_id) = 1 OR
               nrg_common.has_value_changed(associate_version, lv_version) = 1 OR
               nrg_common.has_value_changed(associate_type, p_corporate_associate.associate_type) = 1 OR
               nrg_common.has_value_changed(class_name, p_corporate_associate.class_name) = 1 OR
               nrg_common.has_value_changed(company_associate_name, p_corporate_associate.company_associate_name) = 1 OR
               nrg_common.has_value_changed(is_deleted, p_corporate_associate.is_deleted) = 1 OR
               nrg_common.has_value_changed(communication_group_member,p_corporate_associate.communication_group_member) = 1 OR
               nrg_common.has_value_changed(type_details, p_corporate_associate.type_details) = 1);

        IF SQL%ROWCOUNT > 0 THEN
          put_history(p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U',
                      p_corporate_associate_record => lv_old_record);
        END IF;
      WHEN lv_insert = 0 AND lv_old_version > lv_version THEN
        SELECT p_corporate_associate.associate_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_corporate_associate.company_id,
               p_corporate_associate.person.person_id,
               p_corporate_associate.company_associate_id,
               lv_version,
               p_corporate_associate.associate_type,
               p_corporate_associate.class_name,
               p_corporate_associate.company_associate_name,
               p_corporate_associate.is_deleted,
               p_corporate_associate.communication_group_member,
               p_corporate_associate.type_details
        INTO lv_old_record.corporate_associate_id,
             lv_old_record.logical_load_timestamp,
             lv_old_record.created_by,
             lv_old_record.create_timestamp,
             lv_old_record.updated_by,
             lv_old_record.update_timestamp,
             lv_old_record.effective_start_timestamp,
             lv_old_record.company_id,
             lv_old_record.person_id,
             lv_old_record.company_associate_id,
             lv_old_record.associate_version,
             lv_old_record.associate_type,
             lv_old_record.class_name,
             lv_old_record.company_associate_name,
             lv_old_record.is_deleted,
             lv_old_record.communication_group_member,
             lv_old_record.type_details
        FROM DUAL;

        put_history(p_effective_end_timestamp => p_effective_start_timestamp,
                    p_action => 'I',
                    p_corporate_associate_record => lv_old_record);
    END CASE;

    IF p_corporate_associate.shareholding IS NULL OR p_corporate_associate.shareholding.shareholding_id IS NULL THEN
      BEGIN
        SELECT *
        INTO lv_cmpny_crprt_assct_shrhldng
        FROM company_crprt_assct_shrhldng
        WHERE company_associate_id = p_corporate_associate.associate_id AND
              company_id = p_corporate_associate.company_id;

        put_history (p_effective_end_timestamp => p_effective_start_timestamp,
                     p_action => 'D',
                     p_old_record => lv_cmpny_crprt_assct_shrhldng);

        DELETE company_crprt_assct_shrhldng
        WHERE company_associate_id = p_corporate_associate.associate_id AND
              company_id = p_corporate_associate.company_id;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          NULL;
      END;
    ELSE

      put_associate_shareholding(p_company_id => p_corporate_associate.company_id,
                                 p_associate_id => p_corporate_associate.associate_id,
                                 p_logical_load_timestamp => p_logical_load_timestamp,
                                 p_user => p_user,
                                 p_effective_start_timestamp => p_effective_start_timestamp,
                                 p_old_effective_timestamp => nvl(lv_old_record.effective_start_timestamp, p_effective_start_timestamp),
                                 p_shareholding => p_corporate_associate.shareholding);
    END IF;

  END;

  --
  -- ===================================================================================
  -- put_company_primary_contact
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the primary contacts for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_primary_contact                This is the primary contact
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_company_primary_contact (p_company_id                  company_primary_contacts.company_id%TYPE,
                                         p_primary_contact             company_primary_contact_obj,
                                         p_effective_start_timestamp   company_primary_contacts.effective_start_timestamp%TYPE,
                                         p_user                        company_primary_contacts.created_by%TYPE,
                                         p_logical_load_timestamp      company_primary_contacts.logical_load_timestamp%TYPE)
  IS

    lv_old_version                        NUMBER;
    lv_effective_old_start_tmstmp         TIMESTAMP;

    lv_old_primary_contact                company_primary_contacts%ROWTYPE;


  BEGIN

    IF p_primary_contact IS NOT NULL THEN
      --Call put_person to write person record
      nrg_person.put_person(p_user                          => p_user,

                            p_effective_start_timestamp     => p_effective_start_timestamp,
                            p_person_id                     => p_primary_contact.person.person_id,
                            p_person_version                => p_primary_contact.person.person_version,
                            p_title                         => p_primary_contact.person.title,
                            p_first_name                    => p_primary_contact.person.first_name,
                            p_last_name                     => p_primary_contact.person.last_name,
                            p_gender                        => p_primary_contact.person.gender,
                            p_date_of_birth                 => p_primary_contact.person.date_of_birth,
                            p_nationality                   => p_primary_contact.person.nationality,
                            p_spoken_language               => p_primary_contact.person.spoken_language,
                            p_exchange_data_classification  => p_primary_contact.person.exchange_data_classification,
                            p_financial_detail_id           => p_primary_contact.person.financial_detail_id,
                            p_origin_of_wealth              => p_primary_contact.person.origin_of_wealth,
                            p_origin_of_wealth_detail       => p_primary_contact.person.origin_of_wealth_detail,
                            p_annual_income_range           => p_primary_contact.person.annual_income_range,
                            p_annual_income_currency        => p_primary_contact.person.annual_income_currency,
                            p_val_svngs_invstmnt_range      => p_primary_contact.person.val_svngs_invstmnt_range,
                            p_val_svngs_invstmnt_currency   => p_primary_contact.person.val_svngs_invstmnt_currency,
                            p_sources_of_funds              => p_primary_contact.person.sources_of_funds,
                            p_sources_of_income             => p_primary_contact.person.sources_of_income,
                            p_employment_details            => p_primary_contact.person.employment_details,
                            p_person_addresses              => p_primary_contact.person.person_addresses,
                            p_person_aliases                => p_primary_contact.person.person_aliases,
                            p_person_email_addresses        => p_primary_contact.person.person_email_addresses,
                            p_person_national_identifiers   => p_primary_contact.person.person_national_identifiers,
                            p_person_relationships          => p_primary_contact.person.person_relationships,
                            p_person_telephone_numbers      => p_primary_contact.person.person_telephone_numbers,
                            p_customer_ids                  => p_primary_contact.person.customer_ids,
                            p_power_of_attorneys            => p_primary_contact.person.power_of_attorneys,
                            p_is_deleted                    => p_primary_contact.person.is_deleted,
                            p_person_trading_experience     => p_primary_contact.person.person_trading_experience,
                            p_initial_lead_channel          => p_primary_contact.person.initial_lead_channel,
                            p_person_trading_profiles       => p_primary_contact.person.person_trading_profiles,
                            p_us_citizen                    => p_primary_contact.person.us_citizen,
                            p_marital_status                => p_primary_contact.person.marital_status,
                            p_market_counterpart_partnr_id  => p_primary_contact.person.market_counterparty_partner_id,
                            p_middle_name                   => p_primary_contact.person.middle_name,
                            p_is_politically_exposed        => p_primary_contact.person.is_politically_exposed,
                            p_politically_exposed_details   => p_primary_contact.person.politically_exposed_details,
                            p_financial_detail_version      => p_primary_contact.person.financial_detail_version,
                            p_investment_portfolio          => p_primary_contact.person.investment_portfolio,
                            p_investment_portfolio_ccy      => p_primary_contact.person.investment_portfolio_currency,
                            p_liquid_assets                 => p_primary_contact.person.liquid_assets,
                            p_liquid_assets_currency        => p_primary_contact.person.liquid_assets_currency,
                            p_cumulative_risk_limit         => p_primary_contact.person.cumulative_risk_limit,
                            p_cumulative_risk_limit_ccy     => p_primary_contact.person.cumulative_risk_limit_currency,
                            p_exchng_data_clssfctn_cnfrmd   => p_primary_contact.person.exchng_data_clssfctn_cnfrmd,
                            p_is_accredited_investor        => p_primary_contact.person.is_accredited_investor,
                            p_is_related_to_regulated_firm  => p_primary_contact.person.is_related_to_regulated_firm,
                            p_is_insider_of_public_company  => p_primary_contact.person.is_insider_of_public_company,
                            p_person_mifid_idntfctns        => p_primary_contact.person.person_mifid_idntfctns,
                            p_first_name_latin              => p_primary_contact.person.first_name_latin,
                            p_last_name_latin               => p_primary_contact.person.last_name_latin,
                            p_nationality_country_code      => p_primary_contact.person.nationality_country_code,
                            p_is_mifid_details_remediated   => p_primary_contact.person.is_mifid_details_remediated,
                            p_mkt_cpty_rgltry_clssfctn      => p_primary_contact.person.mkt_cpty_rgltry_clssfctn,
                            p_country_of_birth              => p_primary_contact.person.country_of_birth,
                            p_vulnerability_level           => p_primary_contact.person.vulnerability_level
                            );

    END IF;

     BEGIN
       SELECT *
         INTO lv_old_primary_contact
         FROM company_primary_contacts
        WHERE company_id = p_company_id
          AND (nrg_common.has_value_changed(primary_contact_id, p_primary_contact.primary_contact_id) = 1 OR
               nrg_common.has_value_changed(primary_contact_version, p_primary_contact.primary_contact_version) = 1 OR
               nrg_common.has_value_changed(job_title, p_primary_contact.job_title) = 1 OR
               nrg_common.has_value_changed(company_id, p_primary_contact.company_id) = 1 OR
               nrg_common.has_value_changed(person_id, p_primary_contact.person.person_id) = 1 );

        IF SQL%ROWCOUNT > 0 AND p_primary_contact IS NOT NULL THEN
          put_history (p_primary_contact_record        => lv_old_primary_contact,
                       p_effective_end_timestamp       => p_effective_start_timestamp,
                       p_action                        => 'U');
        END IF;

     EXCEPTION
        WHEN no_data_found THEN
        -- No existing data for criteria found
          -- Therefore no action required
          NULL;
     END;

     IF p_primary_contact IS NOT NULL THEN

       MERGE INTO company_primary_contacts old_version USING
     (SELECT p_primary_contact.primary_contact_id primary_contact_id,
             p_primary_contact.primary_contact_version primary_contact_version,
             p_primary_contact.job_title job_title,
             p_company_id AS company_id,
             p_primary_contact.person.person_id person_id
        FROM dual) new_version
          ON (new_version.company_id = old_version.company_id)
        WHEN MATCHED THEN
      UPDATE
         SET logical_load_timestamp = p_logical_load_timestamp,
             updated_by = p_user,
             update_timestamp = SYSTIMESTAMP,
             effective_start_timestamp = p_effective_start_timestamp,
             primary_contact_id = new_version.primary_contact_id,
             person_id = new_version.person_id,
             primary_contact_version = new_version.primary_contact_version,
             job_title = new_version.job_title
        WHEN NOT MATCHED THEN
      INSERT (company_id,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              primary_contact_id,
              person_id,
              primary_contact_version,
              job_title)
      VALUES (p_company_id,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              new_version.primary_contact_id,
              new_version.person_id,
              new_version.primary_contact_version,
              new_version.job_title);

    END IF;

    IF p_primary_contact IS NULL OR p_primary_contact.primary_contact_id IS NULL THEN

      IF lv_old_primary_contact.primary_contact_id IS NOT NULL THEN
        put_history (p_primary_contact_record        => lv_old_primary_contact,
                     p_effective_end_timestamp       => p_effective_start_timestamp,
                     p_action                        => 'D');
      END IF;

      -- If there is no primary contact sent on the message then delete the existing primary contact
      DELETE company_primary_contacts
      WHERE company_id = p_company_id;

    END IF;

  END;
  --
  --
  -- ===================================================================================
  -- put_company_employee
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the employees for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_employees                      This is the list of employees
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_company_employee (p_company_id                  company_employees.company_id%TYPE,
                                  p_employees                   company_employee_tab,
                                  p_effective_start_timestamp   company_employees.effective_start_timestamp%TYPE,
                                  p_user                        company_employees.created_by%TYPE,
                                  p_logical_load_timestamp      company_employees.logical_load_timestamp%TYPE)
  IS

    TYPE ltab_employees IS TABLE OF company_employees%ROWTYPE INDEX BY BINARY_INTEGER;

    lv_old_data                            ltab_employees;


  BEGIN

    --
    --Call the put person to write person records
    --

    IF p_employees IS NOT NULL THEN
      FOR lv_cnt IN 1..p_employees.COUNT LOOP
        nrg_person.put_person(p_user                          => p_user,
                              p_effective_start_timestamp     => p_effective_start_timestamp,
                              p_person_id                     => p_employees(lv_cnt).person.person_id,
                              p_person_version                => p_employees(lv_cnt).person.person_version,
                              p_title                         => p_employees(lv_cnt).person.title,
                              p_first_name                    => p_employees(lv_cnt).person.first_name,
                              p_last_name                     => p_employees(lv_cnt).person.last_name,
                              p_gender                        => p_employees(lv_cnt).person.gender,
                              p_date_of_birth                 => p_employees(lv_cnt).person.date_of_birth,
                              p_nationality                   => p_employees(lv_cnt).person.nationality,
                              p_spoken_language               => p_employees(lv_cnt).person.spoken_language,
                              p_exchange_data_classification  => p_employees(lv_cnt).person.exchange_data_classification,
                              p_financial_detail_id           => p_employees(lv_cnt).person.financial_detail_id,
                              p_origin_of_wealth              => p_employees(lv_cnt).person.origin_of_wealth,
                              p_origin_of_wealth_detail       => p_employees(lv_cnt).person.origin_of_wealth_detail,
                              p_annual_income_range           => p_employees(lv_cnt).person.annual_income_range,
                              p_annual_income_currency        => p_employees(lv_cnt).person.annual_income_currency,
                              p_val_svngs_invstmnt_range      => p_employees(lv_cnt).person.val_svngs_invstmnt_range,
                              p_val_svngs_invstmnt_currency   => p_employees(lv_cnt).person.val_svngs_invstmnt_currency,
                              p_sources_of_funds              => p_employees(lv_cnt).person.sources_of_funds,
                              p_sources_of_income             => p_employees(lv_cnt).person.sources_of_income,
                              p_employment_details            => p_employees(lv_cnt).person.employment_details,
                              p_person_addresses              => p_employees(lv_cnt).person.person_addresses,
                              p_person_aliases                => p_employees(lv_cnt).person.person_aliases,
                              p_person_email_addresses        => p_employees(lv_cnt).person.person_email_addresses,
                              p_person_national_identifiers   => p_employees(lv_cnt).person.person_national_identifiers,
                              p_person_relationships          => p_employees(lv_cnt).person.person_relationships,
                              p_person_telephone_numbers      => p_employees(lv_cnt).person.person_telephone_numbers,
                              p_customer_ids                  => p_employees(lv_cnt).person.customer_ids,
                              p_power_of_attorneys            => p_employees(lv_cnt).person.power_of_attorneys,
                              p_is_deleted                    => p_employees(lv_cnt).person.is_deleted,
                              p_person_trading_experience     => p_employees(lv_cnt).person.person_trading_experience,
                              p_initial_lead_channel          => p_employees(lv_cnt).person.initial_lead_channel,
                              p_person_trading_profiles       => p_employees(lv_cnt).person.person_trading_profiles,
                              p_us_citizen                    => p_employees(lv_cnt).person.us_citizen,
                              p_marital_status                => p_employees(lv_cnt).person.marital_status,
                              p_market_counterpart_partnr_id  => p_employees(lv_cnt).person.market_counterparty_partner_id,
                              p_middle_name                   => p_employees(lv_cnt).person.middle_name,
                              p_is_politically_exposed        => p_employees(lv_cnt).person.is_politically_exposed,
                              p_politically_exposed_details   => p_employees(lv_cnt).person.politically_exposed_details,
                              p_financial_detail_version      => p_employees(lv_cnt).person.financial_detail_version,
                              p_investment_portfolio          => p_employees(lv_cnt).person.investment_portfolio,
                              p_investment_portfolio_ccy      => p_employees(lv_cnt).person.investment_portfolio_currency,
                              p_liquid_assets                 => p_employees(lv_cnt).person.liquid_assets,
                              p_liquid_assets_currency        => p_employees(lv_cnt).person.liquid_assets_currency,
                              p_cumulative_risk_limit         => p_employees(lv_cnt).person.cumulative_risk_limit,
                              p_cumulative_risk_limit_ccy     => p_employees(lv_cnt).person.cumulative_risk_limit_currency,
                              p_exchng_data_clssfctn_cnfrmd   => p_employees(lv_cnt).person.exchng_data_clssfctn_cnfrmd,
                              p_is_accredited_investor        => p_employees(lv_cnt).person.is_accredited_investor,
                              p_is_related_to_regulated_firm  => p_employees(lv_cnt).person.is_related_to_regulated_firm,
                              p_is_insider_of_public_company  => p_employees(lv_cnt).person.is_insider_of_public_company,
                              p_person_mifid_idntfctns        => p_employees(lv_cnt).person.person_mifid_idntfctns,
                              p_first_name_latin              => p_employees(lv_cnt).person.first_name_latin,
                              p_last_name_latin               => p_employees(lv_cnt).person.last_name_latin,
                              p_nationality_country_code      => p_employees(lv_cnt).person.nationality_country_code,
                              p_is_mifid_details_remediated   => p_employees(lv_cnt).person.is_mifid_details_remediated,
                              p_mkt_cpty_rgltry_clssfctn      => p_employees(lv_cnt).person.mkt_cpty_rgltry_clssfctn,
                              p_country_of_birth              => p_employees(lv_cnt).person.country_of_birth,
                              p_vulnerability_level           => p_employees(lv_cnt).person.vulnerability_level
                              );


      END LOOP;

    --
    --Find the employees that have been updated
    --

    SELECT old_employee.*
    BULK COLLECT INTO lv_old_data
    FROM company_employees old_employee,
         TABLE(CAST(p_employees AS company_employee_tab)) new_employee
  WHERE old_employee.person_id = new_employee.person.person_id AND
          old_employee.company_id = p_company_id AND
          nvl(old_employee.employee_version,0) <= nvl(new_employee.employee_version, 0) AND
          (nrg_common.has_value_changed(old_employee.job_title, new_employee.job_title) = 1 OR
           nrg_common.has_value_changed(old_employee.Is_Deleted, new_employee.is_deleted) = 1 OR
           nrg_common.has_value_changed(old_employee.is_communication_group_member, new_employee.is_communication_group_member) = 1);

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_employee_record          => lv_old_data(lv_cnt),
                   p_effective_end_timestamp  => p_effective_start_timestamp,
                   p_action                   => 'U');
    END LOOP;


    --
    --Merge the existing data
    --

    MERGE INTO company_employees existing
    USING (SELECT *
           FROM TABLE(CAST(p_employees AS company_employee_tab))) new_data
     ON (existing.company_id = p_company_id AND
        existing.person_id = new_data.person.person_id)
    WHEN MATCHED THEN
      UPDATE
      SET existing.logical_load_timestamp = p_logical_load_timestamp,
          existing.updated_by = p_user,
          existing.update_timestamp = SYSTIMESTAMP,
          existing.effective_start_timestamp = p_effective_start_timestamp,
          existing.employee_version = nvl(new_data.employee_version, 0),
          existing.job_title = new_data.job_title,
          existing.employee_id = new_data.employee_id,
          existing.is_deleted = new_data.is_deleted,
          existing.is_communication_group_member = new_data.is_communication_group_member
      WHERE nvl(existing.employee_version,0) <= nvl(new_data.employee_version, 0) AND
            (nrg_common.has_value_changed(existing.job_title, new_data.job_title) = 1 OR
             nrg_common.has_value_changed(existing.employee_id, new_data.employee_id) = 1 OR
             nrg_common.has_value_changed(existing.is_deleted, new_data.is_deleted) = 1 OR
             nrg_common.has_value_changed(existing.is_communication_group_member, new_data.is_communication_group_member) = 1 )
    WHEN NOT MATCHED THEN
      INSERT (company_id,
              person_id,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              employee_version,
              job_title,
              employee_id,
              is_deleted,
              is_communication_group_member)
      VALUES (p_company_id,
              new_data.person.person_id,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              new_data.employee_version,
              new_data.job_title,
              new_data.employee_id,
              new_data.is_deleted,
              new_data.is_communication_group_member);

    --
    --Delete the data that has been deleted with this update
    --

    END IF;

    SELECT old_employee.*
    BULK COLLECT INTO lv_old_data
    FROM company_employees old_employee
    WHERE person_id NOT IN (SELECT a.person.person_id
                            FROM TABLE(CAST(p_employees AS company_employee_tab)) a) AND
          old_employee.company_id = p_company_id;


    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_employee_record          => lv_old_data(lv_cnt),
                   p_effective_end_timestamp  => p_effective_start_timestamp,
                   p_action                   => 'D');
    END LOOP;


    DELETE FROM company_employees ce
    WHERE  ce.company_id = p_company_id
      AND NOT EXISTS (SELECT 1 FROM (SELECT a.person.person_id person_id
                       FROM TABLE(CAST(p_employees AS company_employee_tab)) a) tab
                       WHERE ce.person_id = tab.person_id);

  END;
  --
  --
  -- ===================================================================================
  -- put_company_email_address
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the email addresses for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_email_addresses                This is the list of email addresses
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_company_email_address  (p_company_id                  company_email_addresses.company_id%TYPE,
                                        p_email_addresses             company_email_address_tab,
                                        p_effective_start_timestamp   company_email_addresses.effective_start_timestamp%TYPE,
                                        p_user                        company_email_addresses.created_by%TYPE,
                                        p_logical_load_timestamp      company_email_addresses.logical_load_timestamp%TYPE)
  IS

    TYPE ltab_email_addresses IS TABLE OF company_email_addresses%ROWTYPE INDEX BY BINARY_INTEGER;


    lv_old_data                                 ltab_email_addresses;

  BEGIN

    IF p_email_addresses IS NOT NULL THEN
    --
    --Find the records which will be updated
    --

    SELECT old_email_address.*
    BULK COLLECT INTO lv_old_data
    FROM company_email_addresses old_email_address,
         TABLE(CAST(p_email_addresses AS company_email_address_tab)) new_email_address
    WHERE old_email_address.email_address_id = new_email_address.email_address_id AND
          old_email_address.company_id = p_company_id AND
          (nrg_common.has_value_changed(old_email_address.email_type, new_email_address.email_address_type) = 1 OR
           nrg_common.has_value_changed(old_email_address.email_address, new_email_address.email_address) = 1 OR
           nrg_common.has_value_changed(old_email_address.is_primary, new_email_address.is_primary) = 1 OR
           nrg_common.has_value_changed(old_email_address.is_deleted, new_email_address.is_deleted) = 1 );

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
          put_history (p_email_address_record    => lv_old_data(lv_cnt),
                       p_effective_end_timestamp => p_effective_start_timestamp,
                       p_action                  => 'U');
    END LOOP;

    --
    --Merge New Data Into This Table
    --

    MERGE INTO company_email_addresses existing
    USING (SELECT * FROM
           TABLE(CAST(p_email_addresses AS company_email_address_tab))) new_data
    ON (existing.email_address_id = new_data.email_address_id)
    WHEN MATCHED THEN
      UPDATE
      SET existing.logical_load_timestamp = p_logical_load_timestamp,
          existing.updated_by = p_user,
          existing.update_timestamp = SYSTIMESTAMP,
          existing.effective_start_timestamp = p_effective_start_timestamp,
          existing.email_version = nvl(new_data.email_address_version, 0),
          existing.email_type = new_data.email_address_type,
          existing.email_address = new_data.email_address,
          existing.is_primary = new_data.is_primary,
          existing.company_id = p_company_id,
          existing.is_deleted = new_data.is_deleted
      WHERE (nrg_common.has_value_changed(existing.email_type, new_data.email_address_type) = 1 OR
             nrg_common.has_value_changed(existing.email_address, new_data.email_address) = 1 OR
             nrg_common.has_value_changed(existing.is_primary, new_data.is_primary) = 1 OR
             nrg_common.has_value_changed(existing.company_id, p_company_id) = 1 OR
             nrg_common.has_value_changed(existing.is_deleted, new_data.is_deleted) = 1)
    WHEN NOT MATCHED THEN
      INSERT (company_id,
              email_address_id,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              email_version,
              email_type,
              email_address,
              is_primary,
              is_deleted)
       VALUES(p_company_id,
              new_data.email_address_id,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              nvl(new_data.email_address_version, 0),
              new_data.email_address_type,
              new_data.email_address,
              new_data.is_primary,
              new_data.is_deleted);

    --
    --Delete the data that has been deleted from the source
    --
    END IF;

    SELECT *
    BULK COLLECT INTO lv_old_data
    FROM company_email_addresses
    WHERE email_address_id NOT IN (SELECT email_address_id
                                   FROM TABLE(CAST(p_email_addresses AS company_email_address_tab))) AND
          company_id = p_company_id AND
          effective_start_timestamp < p_effective_start_timestamp;



    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
          put_history (p_email_address_record    => lv_old_data(lv_cnt),
                       p_effective_end_timestamp => p_effective_start_timestamp,
                       p_action                  => 'D');
    END LOOP;


   DELETE FROM company_email_addresses ce
     WHERE email_address_id NOT IN (SELECT /*+cardinality(tab,1)*/ email_address_id
                                     FROM TABLE(CAST(p_email_addresses AS company_email_address_tab)) tab) AND
          company_id = p_company_id AND
          effective_start_timestamp < p_effective_start_timestamp;



  END;
  --
  --
  -- ===================================================================================
  -- put_company_address
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the addresses for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_addresses                      This is the list of addresses
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_company_address (p_company_id                  company_addresses.company_id%TYPE,
                                 p_addresses                   company_address_tab,
                                 p_effective_start_timestamp   company_addresses.effective_start_timestamp%TYPE,
                                 p_user                        company_addresses.created_by%TYPE,
                                 p_logical_load_timestamp      company_addresses.logical_load_timestamp%TYPE)
  IS

    TYPE ltab_addresses IS TABLE OF company_addresses%ROWTYPE INDEX BY BINARY_INTEGER;

    lv_old_data                           ltab_addresses;

  BEGIN

    IF p_addresses IS NOT NULL THEN
    --
    --Find out the rows which will go in for a update
    --

    SELECT existing.*
    BULK COLLECT INTO lv_old_data
    FROM company_addresses existing,
    (SELECT *
       FROM TABLE(CAST(p_addresses AS company_address_tab))) new_data
    WHERE existing.company_id = p_company_id
      AND existing.address_id = new_data.address_id
     AND (nrg_common.has_value_changed(existing.address_type, new_data.address_type) = 1 OR
          nrg_common.has_value_changed(existing.years_at_address, new_data.years_at_address) = 1 OR
          nrg_common.has_value_changed(existing.address_line_1, new_data.address_line_1) = 1 OR
          nrg_common.has_value_changed(existing.address_line_2, new_data.address_line_2) = 1 OR
          nrg_common.has_value_changed(existing.address_line_3, new_data.address_line_3) = 1 OR
          nrg_common.has_value_changed(existing.address_line_4, new_data.address_line_4) = 1 OR
          nrg_common.has_value_changed(existing.address_line_5, new_data.address_line_5) = 1 OR
          nrg_common.has_value_changed(existing.postal_code, new_data.postal_code) = 1 OR
          nrg_common.has_value_changed(existing.country_code, new_data.country) = 1 OR
          nrg_common.has_value_changed(existing.is_primary, new_data.is_primary) = 1 OR
          nrg_common.has_value_changed(existing.is_previous, new_data.is_previous) = 1 OR
          nrg_common.has_value_changed(existing.is_deleted, new_data.is_deleted) = 1 OR
          nrg_common.has_value_changed(existing.related_employment_details_id, new_data.related_employment_details_id) = 1);

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_address_record          => lv_old_data(lv_cnt),
                   p_effective_end_timestamp => p_effective_start_timestamp,
                   p_action                  => 'U');
    END LOOP;

    --
    --Merge new data into the table
    --

    MERGE INTO company_addresses existing
    USING (SELECT *
           FROM TABLE(CAST(p_addresses AS company_address_tab))) new_data
    ON (existing.company_id = p_company_id
    AND existing.address_id = new_data.address_id)
    WHEN MATCHED THEN
      UPDATE
      SET existing.logical_load_timestamp            = p_logical_load_timestamp,
          existing.updated_by                        = p_user,
          existing.update_timestamp                  = SYSTIMESTAMP,
          effective_start_timestamp                  = p_effective_start_timestamp,
          existing.address_version                   = new_data.address_version,
          existing.address_type                      = new_data.address_type,
          existing.years_at_address                  = new_data.years_at_address,
          existing.address_line_1                    = new_data.address_line_1,
          existing.address_line_2                    = new_data.address_line_2,
          existing.address_line_3                    = new_data.address_line_3,
          existing.address_line_4                    = new_data.address_line_4,
          existing.address_line_5                    = new_data.address_line_5,
          existing.postal_code                       = new_data.postal_code,
          existing.country_code                      = new_data.country,
          existing.is_primary                        = new_data.is_primary,
          existing.is_previous                       = new_data.is_previous,
          existing.is_deleted                        = new_data.is_deleted,
          existing.related_employment_details_id     = new_data.related_employment_details_id
      WHERE (nrg_common.has_value_changed(existing.address_type, new_data.address_type) = 1 OR
            nrg_common.has_value_changed(existing.years_at_address, new_data.years_at_address) = 1 OR
            nrg_common.has_value_changed(existing.address_line_1, new_data.address_line_1) = 1 OR
            nrg_common.has_value_changed(existing.address_line_2, new_data.address_line_2) = 1 OR
            nrg_common.has_value_changed(existing.address_line_3, new_data.address_line_3) = 1 OR
            nrg_common.has_value_changed(existing.address_line_4, new_data.address_line_4) = 1 OR
            nrg_common.has_value_changed(existing.address_line_5, new_data.address_line_5) = 1 OR
            nrg_common.has_value_changed(existing.postal_code, new_data.postal_code) = 1 OR
            nrg_common.has_value_changed(existing.country_code, new_data.country) = 1 OR
            nrg_common.has_value_changed(existing.is_primary, new_data.is_primary) = 1 OR
            nrg_common.has_value_changed(existing.is_previous, new_data.is_previous) = 1 OR
            nrg_common.has_value_changed(existing.is_deleted, new_data.is_deleted) = 1 OR
            nrg_common.has_value_changed(existing.related_employment_details_id, new_data.related_employment_details_id) = 1)
    WHEN NOT MATCHED THEN
      INSERT (company_id,
              address_id,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              address_version,
              address_type,
              years_at_address,
              address_line_1,
              address_line_2,
              address_line_3,
              address_line_4,
              address_line_5,
              postal_code,
              country_code,
              is_primary,
              is_previous,
              is_deleted,
              related_employment_details_id)
       VALUES(p_company_id,
              new_data.address_id,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              nvl(new_data.address_version, 0),
              new_data.address_type,
              new_data.years_at_address,
              new_data.address_line_1,
              new_data.address_line_2,
              new_data.address_line_3,
              new_data.address_line_4,
              new_data.address_line_5,
              new_data.postal_code,
              new_data.country,
              new_data.is_primary,
              new_data.is_previous,
              new_data.is_deleted,
              new_data.related_employment_details_id);

    END IF;
    --
    --Find the data that has been deleted
    --

    SELECT *
    BULK COLLECT INTO lv_old_data
    FROM company_addresses
    WHERE address_id NOT IN (SELECT address_id
                             FROM TABLE(CAST(p_addresses AS company_address_tab))) AND
          company_id = p_company_id;

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_address_record          => lv_old_data(lv_cnt),
                   p_effective_end_timestamp => p_effective_start_timestamp,
                   p_action                  => 'D');
    END LOOP;

    DELETE company_addresses
    WHERE address_id NOT IN (SELECT address_id
                             FROM TABLE(CAST(p_addresses AS company_address_tab))) AND
          company_id = p_company_id AND
          effective_start_timestamp < p_effective_start_timestamp;


  END;
--------DR------

-- ===================================================================================
  -- put_lei_Address
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the addresses for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_addresses                      This is the list of addresses
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_lei_address (p_user                        company_lei_addresses.created_by%TYPE,
                             p_logical_load_timestamp      company_lei_addresses.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp   company_lei_addresses.effective_start_timestamp%TYPE,
                             p_legal_entity_identifier     company_lei_addresses.legal_entity_identifier%TYPE,
                             p_lei_addr_details            company_lei_addr_tab)
  IS

    TYPE ltab_lei_address IS TABLE OF company_lei_addresses%ROWTYPE INDEX BY BINARY_INTEGER;
    lv_old_addr_data      ltab_lei_address;

  BEGIN

    SELECT la.*
      BULK COLLECT INTO lv_old_addr_data
      FROM company_lei_addresses la
      JOIN (SELECT * FROM TABLE(CAST(p_lei_addr_details AS company_lei_addr_tab))) new_version
        ON new_version.legal_entity_identifier = la.legal_entity_identifier
       AND new_version.address_type = la.address_type
     WHERE (nrg_common.has_value_changed(la.first_address_line,new_version.first_address_line) = 1 OR
            nrg_common.has_value_changed(la.address_number,new_version.address_number) = 1 OR
            nrg_common.has_value_changed(la.number_within_building,new_version.number_within_building) = 1 OR
            nrg_common.has_value_changed(la.mail_routing,new_version.mail_routing) = 1 OR
            nrg_common.has_value_changed(la.additional_address_lines,new_version.additional_address_lines) = 1 OR
            nrg_common.has_value_changed(la.city,new_version.city) = 1 OR
            nrg_common.has_value_changed(la.region,new_version.region) = 1 OR
            nrg_common.has_value_changed(la.country,new_version.country) = 1 OR
            nrg_common.has_value_changed(la.postal_code,new_version.postal_code) = 1);

    FOR lv_cnt IN 1..lv_old_addr_data.COUNT LOOP
      put_history (p_old_addr_record          => lv_old_addr_data(lv_cnt),
                   p_effective_end_timestamp  => p_effective_start_timestamp,
                   p_action                   => 'U');
    END LOOP;

    MERGE INTO company_lei_addresses existing
    USING (SELECT * FROM TABLE(CAST(p_lei_addr_details AS company_lei_addr_tab))) new_data
    ON (existing.legal_entity_identifier = new_data.legal_entity_identifier AND existing.address_type = new_data.address_type)
    WHEN MATCHED THEN
      UPDATE
         SET existing.logical_load_timestamp   = p_logical_load_timestamp,
             existing.updated_by               = p_user,
             existing.update_timestamp         = systimestamp,
             effective_start_timestamp         = p_effective_start_timestamp,
             existing.first_address_line       = new_data.first_address_line,
             existing.address_number           = new_data.address_number,
             existing.number_within_building   = new_data.number_within_building,
             existing.mail_routing             = new_data.mail_routing,
             existing.additional_address_lines = new_data.additional_address_lines,
             existing.city                     = new_data.city,
             existing.region                   = new_data.region,
             existing.country                  = new_data.country,
             existing.postal_code              = new_data.postal_code
      WHERE (nrg_common.has_value_changed(existing.first_address_line,new_data.first_address_line) = 1 OR
             nrg_common.has_value_changed(existing.address_number,new_data.address_number) = 1 OR
             nrg_common.has_value_changed(existing.number_within_building,new_data.number_within_building) = 1 OR
             nrg_common.has_value_changed(existing.mail_routing,new_data.mail_routing) = 1 OR
             nrg_common.has_value_changed(existing.additional_address_lines,new_data.additional_address_lines) = 1 OR
             nrg_common.has_value_changed(existing.city,new_data.city) = 1 OR
             nrg_common.has_value_changed(existing.region,new_data.region) = 1 OR
             nrg_common.has_value_changed(existing.country,new_data.country) = 1 OR
             nrg_common.has_value_changed(existing.postal_code,new_data.postal_code) = 1)
    WHEN NOT MATCHED THEN
      INSERT
        (legal_entity_identifier,
         logical_load_timestamp,
         created_by,
         create_timestamp,
         updated_by,
         update_timestamp,
         effective_start_timestamp,
         address_type,
         first_address_line,
         address_number,
         number_within_building,
         mail_routing,
         additional_address_lines,
         city,
         region,
         country,
         postal_code)
      VALUES
        (p_legal_entity_identifier,
         p_logical_load_timestamp,
         p_user,
         systimestamp,
         p_user,
         systimestamp,
         p_effective_start_timestamp,
         new_data.address_type,
         new_data.first_address_line,
         new_data.address_number,
         new_data.number_within_building,
         new_data.mail_routing,
         new_data.additional_address_lines,
         new_data.city,
         new_data.region,
         new_data.country,
         new_data.postal_code);

	EXCEPTION
      WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);

  END;

-- ===================================================================================
  -- put_lei_details
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the addresses for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_addresses                      This is the list of addresses
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_lei_details (p_user                        company_lei_details.created_by%type,
                             p_effective_start_timestamp   company_lei_details.effective_start_timestamp%type,
                             p_identifier 				         company_lei_details.legal_entity_identifier%type,
                             p_name 						           company_lei_details.legal_name%type,
                             p_jurisdication 			         company_lei_details.legal_jurisdiction%type,
                             p_category_type 			         company_lei_details.entity_category_type%type,
                             p_legal_form_code 			       company_lei_details.entity_legal_form_code%type,
                             p_status 					           company_lei_details.entity_status%type,
                             p_expiration_date 			       company_lei_details.entity_expiration_date%type,
                             p_expiration_reason 		       company_lei_details.entity_expiration_reason%type,
                             p_initial_registration_date   company_lei_details.initial_registration_date%type,
                             p_last_update_date 			     company_lei_details.last_update_date%type,
                             p_registration_status 		     company_lei_details.registration_status%type,
                             p_next_renewal_date 		       company_lei_details.next_renewal_date%type,
                             p_managing_lou 				       company_lei_details.managing_lou%type,
                             p_validation_sources 		     company_lei_details.validation_sources%type,
                             p_address_tab					       company_lei_addr_tab)
  IS

    lv_logical_load_timestamp    company_lei_details.logical_load_timestamp%TYPE;
    lv_effective_start_timestamp company_lei_details.effective_start_timestamp%TYPE;
    lv_old_lei_data              company_lei_details%rowtype;

  BEGIN

    lv_logical_load_timestamp := SYSTIMESTAMP;

    BEGIN

      SELECT old_version.legal_entity_identifier,
             old_version.logical_load_timestamp,
             old_version.created_by,
             old_version.create_timestamp,
             old_version.updated_by,
             old_version.update_timestamp,
             old_version.effective_start_timestamp,
             old_version.legal_name,
             old_version.legal_jurisdiction,
             old_version.entity_category_type,
             old_version.entity_legal_form_code,
             old_version.entity_status,
             old_version.entity_expiration_date,
             old_version.entity_expiration_reason,
             old_version.initial_registration_date,
             old_version.last_update_date,
             old_version.registration_status,
             old_version.next_renewal_date,
             old_version.managing_lou,
             old_version.validation_sources
        INTO lv_old_lei_data
        FROM company_lei_details old_version,
             (SELECT p_user,
                     systimestamp,
                     p_user,
                     systimestamp,
                     lv_logical_load_timestamp,
                     p_effective_start_timestamp,
                     p_identifier                legal_entity_identifier,
                     p_name                      legal_name,
                     p_jurisdication             legal_jurisdiction,
                     p_category_type             entity_category_type,
                     p_legal_form_code           entity_legal_form_code,
                     p_status                    entity_status,
                     p_expiration_date           entity_expiration_date,
                     p_expiration_reason         entity_expiration_reason,
                     p_initial_registration_date initial_registration_date,
                     p_last_update_date          last_update_date,
                     p_registration_status       registration_status,
                     p_next_renewal_date         next_renewal_date,
                     p_managing_lou              managing_lou,
                     p_validation_sources        validation_sources
                FROM dual) new_version
               WHERE old_version.legal_entity_identifier =
                     new_version.legal_entity_identifier
                 AND (nrg_common.has_value_changed(old_version.legal_name,new_version.legal_name) = 1 OR
                     nrg_common.has_value_changed(old_version.legal_jurisdiction,new_version.legal_jurisdiction) = 1 OR
                     nrg_common.has_value_changed(old_version.entity_category_type,new_version.entity_category_type) = 1 OR
                     nrg_common.has_value_changed(old_version.entity_legal_form_code,new_version.entity_legal_form_code) = 1 OR
                     nrg_common.has_value_changed(old_version.entity_status,new_version.entity_status) = 1 OR
                     nrg_common.has_value_changed(old_version.entity_expiration_date,new_version.entity_expiration_date) = 1 OR
                     nrg_common.has_value_changed(old_version.entity_expiration_reason,new_version.entity_expiration_reason) = 1 OR
                     nrg_common.has_value_changed(old_version.initial_registration_date,new_version.initial_registration_date) = 1 OR
                     nrg_common.has_value_changed(old_version.registration_status,new_version.registration_status) = 1 OR
                     nrg_common.has_value_changed(old_version.next_renewal_date,new_version.next_renewal_date) = 1 OR
                     nrg_common.has_value_changed(old_version.managing_lou,new_version.managing_lou) = 1 OR
                     nrg_common.has_value_changed(old_version.validation_sources,new_version.validation_sources) = 1);

      put_history (p_old_lei_record          => lv_old_lei_data,
                   p_effective_end_timestamp => p_effective_start_timestamp,
                   p_action                  => 'U');

    EXCEPTION
	    WHEN NO_DATA_FOUND THEN
	    NULL;

	  END;

    MERGE INTO company_lei_details existing
    USING (SELECT p_identifier                AS legal_entity_identifier,
                  p_name                      AS legal_name,
                  p_jurisdication             AS legal_jurisdiction,
                  p_category_type             AS entity_category_type,
                  p_legal_form_code           AS entity_legal_form_code,
                  p_status                    AS entity_status,
                  p_expiration_date           AS entity_expiration_date,
                  p_expiration_reason         AS entity_expiration_reason,
                  p_initial_registration_date AS initial_registration_date,
                  p_last_update_date          AS last_update_date,
                  p_registration_status       AS registration_status,
                  p_next_renewal_date         AS next_renewal_date,
                  p_managing_lou              AS managing_lou,
                  p_validation_sources        AS validation_sources
             FROM dual) new_data
    ON (existing.legal_entity_identifier = new_data.legal_entity_identifier)
    WHEN MATCHED THEN
      UPDATE
         SET existing.logical_load_timestamp    = lv_logical_load_timestamp,
             existing.updated_by                = p_user,
             existing.update_timestamp          = systimestamp,
             existing.effective_start_timestamp = p_effective_start_timestamp,
             existing.legal_name                = new_data.legal_name,
             existing.legal_jurisdiction        = new_data.legal_jurisdiction,
             existing.entity_category_type      = new_data.entity_category_type,
             existing.entity_legal_form_code    = new_data.entity_legal_form_code,
             existing.entity_status             = new_data.entity_status,
             existing.entity_expiration_date    = new_data.entity_expiration_date,
             existing.entity_expiration_reason  = new_data.entity_expiration_reason,
             existing.initial_registration_date = new_data.initial_registration_date,
             existing.last_update_date          = new_data.last_update_date,
             existing.registration_status       = new_data.registration_status,
             existing.next_renewal_date         = new_data.next_renewal_date,
             existing.managing_lou              = new_data.managing_lou,
             existing.validation_sources        = new_data.validation_sources
       WHERE (nrg_common.has_value_changed(existing.legal_name,new_data.legal_name) = 1 OR
             nrg_common.has_value_changed(existing.legal_jurisdiction,new_data.legal_jurisdiction) = 1 OR
             nrg_common.has_value_changed(existing.entity_category_type,new_data.entity_category_type) = 1 OR
             nrg_common.has_value_changed(existing.entity_legal_form_code,new_data.entity_legal_form_code) = 1 OR
             nrg_common.has_value_changed(existing.entity_status,new_data.entity_status) = 1 OR
             nrg_common.has_value_changed(existing.entity_expiration_date,new_data.entity_expiration_date) = 1 OR
             nrg_common.has_value_changed(existing.entity_expiration_reason,new_data.entity_expiration_reason) = 1 OR
             nrg_common.has_value_changed(existing.initial_registration_date,new_data.initial_registration_date) = 1 OR
             nrg_common.has_value_changed(existing.registration_status,new_data.registration_status) = 1 OR
             nrg_common.has_value_changed(existing.next_renewal_date,new_data.next_renewal_date) = 1 OR
             nrg_common.has_value_changed(existing.managing_lou,new_data.managing_lou) = 1 OR
             nrg_common.has_value_changed(existing.validation_sources,new_data.validation_sources) = 1)
    WHEN NOT MATCHED THEN
      INSERT (legal_entity_identifier,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              legal_name,
              legal_jurisdiction,
              entity_category_type,
              entity_legal_form_code,
              entity_status,
              entity_expiration_date,
              entity_expiration_reason,
              initial_registration_date,
              last_update_date,
              registration_status,
              next_renewal_date,
              managing_lou,
              validation_sources)
      VALUES (p_identifier,
              lv_logical_load_timestamp,
              p_user,
              systimestamp,
              p_user,
              systimestamp,
              p_effective_start_timestamp,
              new_data.legal_name,
              new_data.legal_jurisdiction,
              new_data.entity_category_type,
              new_data.entity_legal_form_code,
              new_data.entity_status,
              new_data.entity_expiration_date,
              new_data.entity_expiration_reason,
              new_data.initial_registration_date,
              new_data.last_update_date,
              new_data.registration_status,
              new_data.next_renewal_date,
              new_data.managing_lou,
              new_data.validation_sources);

		put_lei_address (p_user                      	=> p_user,
                     p_logical_load_timestamp   	=> lv_logical_load_timestamp,
								     p_effective_start_timestamp 	=> p_effective_start_timestamp,
								     p_legal_entity_identifier    => p_identifier,
                     p_lei_addr_details          	=> p_address_tab
                     );

	EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);

  END;

  -- ===================================================================================
  -- get_legal_entity_identifiers
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve company legal entity identifiers
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a data_id_tab representing the data id for missing legal entity identifiers
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------


 FUNCTION get_legal_entity_identifiers RETURN SYS_REFCURSOR IS

    lv_company_perm_request SYS_REFCURSOR;

  BEGIN

    logger.logger.set_module('Request company lei id');

    OPEN lv_company_perm_request FOR
    SELECT distinct c.legal_entity_identifier
    FROM bi_ods.companies c
    WHERE c.legal_entity_identifier is not null ;

    RETURN lv_company_perm_request;

  EXCEPTION
    WHEN OTHERS THEN
  --    logger.logger.severe(logger.logger.error_backtrace);
--      logger.logger.set_module(NULL);
    --  raise_application_error(-20004, logger.logger.error_backtrace);
    null;
  END;

--DR
  -- ===================================================================================
  -- put_telephone_number
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the telephone numbers for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_telephone_numbers              This is the list of telephone numbers
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_telephone_number(p_company_id                  company_telephone_numbers.company_id%TYPE,
                                 p_telehone_numbers            company_telephone_number_tab,
                                 p_effective_start_timestamp   company_telephone_numbers.effective_start_timestamp%TYPE,
                                 p_user                        company_telephone_numbers.created_by%TYPE,
                                 p_logical_load_timestamp      company_telephone_numbers.logical_load_timestamp%TYPE)
  IS

    TYPE ltab_table_data IS TABLE OF company_telephone_numbers%ROWTYPE INDEX BY BINARY_INTEGER;

    lv_old_data ltab_table_data;

  BEGIN

    IF p_telehone_numbers IS NOT NULL THEN
    --
    --Lock all the records for update
    --

    SELECT ctn.*
    BULK COLLECT INTO lv_old_data
    FROM company_telephone_numbers ctn,
    (SELECT *
       FROM TABLE(CAST(p_telehone_numbers AS company_telephone_number_tab))) newctn
    WHERE ctn.company_id   = p_company_id AND
          ctn.telephone_id = newctn.telephone_id
     AND (nrg_common.has_value_changed(ctn.telephone_type, newctn.telephone_type) = 1 OR
          nrg_common.has_value_changed(ctn.telephone_number, newctn.telephone_number) = 1 OR
          nrg_common.has_value_changed(ctn.is_primary, newctn.is_primary) = 1 OR
          nrg_common.has_value_changed(ctn.is_deleted, newctn.is_deleted) = 1 );

    --
    --Write To History
    --

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_telephone_record        => lv_old_data(lv_cnt),
                   p_effective_end_timestamp => p_effective_start_timestamp,
                   p_action                  => 'U');
    END LOOP;

    --
    --Insert and update new data
    --

    MERGE INTO company_telephone_numbers existing
    USING (SELECT *
           FROM TABLE(CAST(p_telehone_numbers AS company_telephone_number_tab))) new_records
    ON (existing.telephone_id = new_records.telephone_id)
    WHEN MATCHED THEN
      UPDATE
      SET existing.logical_load_timestamp = p_logical_load_timestamp,
          existing.updated_by = p_user,
          existing.update_timestamp = SYSTIMESTAMP,
          existing.effective_start_timestamp = p_effective_start_timestamp,
          existing.telephone_version = nvl(new_records.telephone_version, 0),
          existing.telephone_type = new_records.telephone_type,
          existing.telephone_number = new_records.telephone_number,
          existing.is_primary = new_records.is_primary,
          existing.company_id = p_company_id,
          existing.is_deleted = new_records.is_deleted
      WHERE existing.telephone_version <= nvl(new_records.telephone_version, 0) AND
            (nrg_common.has_value_changed(existing.telephone_type, new_records.telephone_type) = 1 OR
             nrg_common.has_value_changed(existing.telephone_number, new_records.telephone_number) = 1 OR
             nrg_common.has_value_changed(existing.is_primary, new_records.is_primary) = 1 OR
             nrg_common.has_value_changed(existing.is_deleted, new_records.is_deleted) = 1 )
    WHEN NOT MATCHED THEN
      INSERT(existing.company_id,
             existing.telephone_id,
             existing.logical_load_timestamp,
             existing.created_by,
             existing.create_timestamp,
             existing.updated_by,
             existing.update_timestamp,
             existing.effective_start_timestamp,
             existing.telephone_version,
             existing.telephone_type,
             existing.telephone_number,
             existing.is_primary,
             existing.Is_Deleted)
      VALUES (p_company_id,
              new_records.telephone_id,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              nvl(new_records.telephone_version, 0),
              new_records.telephone_type,
              new_records.telephone_number,
              new_records.is_primary,
              new_records.is_deleted);

    END IF;
    --
    --Delete the data that has been deleted with this message
    --

    SELECT *
    BULK COLLECT INTO lv_old_data
    FROM company_telephone_numbers
    WHERE telephone_id NOT IN (SELECT telephone_id
                               FROM TABLE(CAST(p_telehone_numbers AS company_telephone_number_tab)))
    AND company_id = p_company_id;

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_telephone_record        => lv_old_data(lv_cnt),
                   p_effective_end_timestamp => p_effective_start_timestamp,
                   p_action                  => 'D');
    END LOOP;

    DELETE company_telephone_numbers
    WHERE telephone_id NOT IN (SELECT telephone_id
                               FROM TABLE(CAST(p_telehone_numbers AS company_telephone_number_tab)))
     AND company_id = p_company_id
     AND effective_start_timestamp < p_effective_start_timestamp;

  END;
  -- ===================================================================================
  -- put_telephone_number
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the telephone numbers for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_telephone_numbers              This is the list of telephone numbers
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_company_tax_registration(p_company_id                  company_tax_registrations.company_id%TYPE,
                                         p_company_tax_registration    company_tax_registrations_tab,
                                         p_effective_start_timestamp   company_tax_registrations.effective_start_timestamp%TYPE,
                                         p_user                        company_tax_registrations.created_by%TYPE,
                                         p_logical_load_timestamp      company_tax_registrations.logical_load_timestamp%TYPE)
  IS

    TYPE ltab_company_tax_registration IS TABLE OF company_tax_registrations%ROWTYPE INDEX BY BINARY_INTEGER;

    lv_old_data ltab_company_tax_registration;

  BEGIN

    IF p_company_tax_registration IS NOT NULL THEN
    --
    --Lock all the records for update
    --

    SELECT  old_data.company_id
           ,old_data.country_of_issue
           ,old_data.logical_load_timestamp
           ,old_data.created_by
           ,old_data.create_timestamp
           ,old_data.updated_by
           ,old_data.update_timestamp
           ,old_data.effective_start_timestamp
           ,old_data.registration_number
           ,old_data.is_deleted
    BULK COLLECT INTO lv_old_data
    FROM   company_tax_registrations old_data,
           (SELECT /*+cardinality (tab, 1)*/ company_id, country_of_issue, Registration_Number, Is_Deleted
             FROM TABLE(CAST(p_company_tax_registration AS company_tax_registrations_tab)) tab) new_data
    WHERE old_data.company_id = new_data.company_id
      AND old_data.country_of_issue = new_data.country_of_issue
      AND (nrg_common.has_value_changed(old_data.Registration_Number, new_data.Registration_Number) = 1 OR
           nrg_common.has_value_changed(old_data.Is_Deleted, new_data.Is_Deleted) = 1);

    --
    --Write To History
    --

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_company_tax_registration  => lv_old_data(lv_cnt),
                   p_effective_end_timestamp   => p_effective_start_timestamp,
                   p_action                    => 'U');
    END LOOP;

    --
    --Insert and update new data
    --

    MERGE INTO company_tax_registrations existing
    USING (SELECT *
           FROM TABLE(CAST(p_company_tax_registration AS company_tax_registrations_tab)) tab) new_records
    ON (existing.company_id = new_records.company_id AND
        existing.Country_Of_Issue = new_records.country_of_issue)
    WHEN MATCHED THEN
      UPDATE
      SET existing.logical_load_timestamp = p_logical_load_timestamp,
          existing.updated_by = p_user,
          existing.update_timestamp = SYSTIMESTAMP,
          existing.effective_start_timestamp = p_effective_start_timestamp,
          existing.registration_number = new_records.registration_number,
          existing.is_deleted = new_records.is_deleted
      WHERE (nrg_common.has_value_changed(existing.registration_number, new_records.registration_number) = 1 OR
             nrg_common.has_value_changed(existing.is_deleted, new_records.is_deleted) = 1)
    WHEN NOT MATCHED THEN
      INSERT(company_id,
             country_of_issue,
             logical_load_timestamp,
             created_by,
             create_timestamp,
             updated_by,
             update_timestamp,
             effective_start_timestamp,
             registration_number,
             is_deleted)
      VALUES (p_company_id,
              new_records.country_of_issue,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              new_records.registration_number,
              new_records.is_deleted);

    END IF;
    --
    --Delete the data that has been deleted with this message
    --

    SELECT *
    BULK COLLECT INTO lv_old_data
    FROM company_tax_registrations old_data
    WHERE (company_id, country_of_issue) NOT IN (SELECT /*+cardinality (tab, 1)*/ company_id, country_of_issue
                                                 FROM TABLE(CAST(p_company_tax_registration AS company_tax_registrations_tab)) tab)
    AND company_id = p_company_id;


    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_company_tax_registration  => lv_old_data(lv_cnt),
                   p_effective_end_timestamp   => p_effective_start_timestamp,
                   p_action                    => 'D');
    END LOOP;

    DELETE company_tax_registrations
    WHERE (company_id, country_of_issue) NOT IN
     (SELECT /*+cardinality (tab, 1)*/ company_id, country_of_issue
        FROM TABLE(CAST(p_company_tax_registration AS company_tax_registrations_tab)) tab)
     AND company_id = p_company_id
     AND effective_start_timestamp <= p_effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_company_addtnl_trdng_cntrys
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the telephone numbers for the company
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_company_id                     This is the company Id
  --     p_company_addtnl_trdng_cntrys    This is the list of additional countries
  --     p_effective_start_timestamp      This is the start timestamp for the record
  --     p_user                           This is the name of the user writing this record
  --     p_logical_load_timestamp         This is the logical_load_timestamp
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --    -20001      Unknown Exception. Fatal Error
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_company_addtnl_trdng_cntrys(p_company_id                   company_addtnl_trdng_cntrys.company_id%TYPE,
                                            p_company_addtnl_trdng_cntrys  company_add_trd_cntry_tab,
                                            p_effective_start_timestamp    company_addtnl_trdng_cntrys.effective_start_timestamp%TYPE,
                                            p_user                         company_addtnl_trdng_cntrys.created_by%TYPE,
                                            p_logical_load_timestamp       company_addtnl_trdng_cntrys.logical_load_timestamp%TYPE)
  IS

    TYPE ltab_company_addtnl_trdng_cntrys IS TABLE OF company_addtnl_trdng_cntrys%ROWTYPE INDEX BY BINARY_INTEGER;

    lv_old_data ltab_company_addtnl_trdng_cntrys;

  BEGIN

    IF p_company_addtnl_trdng_cntrys IS NOT NULL THEN
      --
      --Lock all the records for update
      --

      SELECT  old_data.company_id
             ,old_data.country_code
             ,old_data.logical_load_timestamp
             ,old_data.created_by
             ,old_data.create_timestamp
             ,old_data.updated_by
             ,old_data.update_timestamp
             ,old_data.effective_start_timestamp
             ,old_data.is_deleted
        BULK COLLECT INTO lv_old_data
        FROM company_addtnl_trdng_cntrys old_data,
             (SELECT /*+cardinality (tab, 1)*/
                     p_company_id AS company_id,
                     country_code,
                     is_deleted
                FROM TABLE(CAST(p_company_addtnl_trdng_cntrys AS company_add_trd_cntry_tab)) tab) new_data
       WHERE old_data.company_id   = new_data.company_id
         AND old_data.country_code = new_data.country_code
         AND (nrg_common.has_value_changed(old_data.is_deleted, new_data.is_deleted) = 1);

      --
      --Write To History
      --

      FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
        put_history (p_company_addtnl_trdng_cntrys  => lv_old_data(lv_cnt),
                     p_effective_end_timestamp      => p_effective_start_timestamp,
                     p_action                       => 'U');
      END LOOP;

      --
      --Insert and update new data
      --

      MERGE INTO company_addtnl_trdng_cntrys existing USING
      (SELECT p_company_id AS company_id,
              country_code,
              is_deleted
         FROM TABLE(CAST(p_company_addtnl_trdng_cntrys AS company_add_trd_cntry_tab)) tab) new_records
      ON (existing.company_id   = new_records.company_id AND
          existing.country_code = new_records.country_code)
      WHEN MATCHED THEN
        UPDATE
        SET existing.logical_load_timestamp    = p_logical_load_timestamp,
            existing.updated_by                = p_user,
            existing.update_timestamp          = SYSTIMESTAMP,
            existing.effective_start_timestamp = p_effective_start_timestamp,
            existing.is_deleted                = new_records.is_deleted
        WHERE (nrg_common.has_value_changed(existing.is_deleted, new_records.is_deleted) = 1)
      WHEN NOT MATCHED THEN
        INSERT(company_id,
               country_code,
               logical_load_timestamp,
               created_by,
               create_timestamp,
               updated_by,
               update_timestamp,
               effective_start_timestamp,
               is_deleted)
        VALUES (p_company_id,
                new_records.country_code,
                p_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_records.is_deleted);

      --
      --Delete the data that has been deleted with this message
      --

    END IF;

    SELECT *
      BULK COLLECT INTO lv_old_data
      FROM company_addtnl_trdng_cntrys old_data
     WHERE company_id = p_company_id
       AND (country_code) NOT IN (SELECT /*+cardinality (tab, 1)*/ country_code
                                               FROM TABLE(CAST(p_company_addtnl_trdng_cntrys AS company_add_trd_cntry_tab)) tab);

    FOR lv_cnt IN 1..lv_old_data.COUNT LOOP
      put_history (p_company_addtnl_trdng_cntrys  => lv_old_data(lv_cnt),
                   p_effective_end_timestamp      => p_effective_start_timestamp,
                   p_action                       => 'D');
    END LOOP;

    DELETE company_addtnl_trdng_cntrys
    WHERE company_id = p_company_id
      AND (country_code) NOT IN
     (SELECT /*+cardinality (tab, 1)*/ country_code
        FROM TABLE(CAST(p_company_addtnl_trdng_cntrys AS company_add_trd_cntry_tab)) tab)
     AND effective_start_timestamp <= p_effective_start_timestamp;

  END;

  --
  --
  -- ===================================================================================
  --  PUBLIC MODULES
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC

    IS

    BEGIN
      logger.logger.set_module('version');
      RETURN gc_version;
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.SEVERE(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        RAISE;
    END version;

  -- ===================================================================================
  -- create_company_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a company stub in case the company is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     COMPANIES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_company_id                     Company Id
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

    PROCEDURE create_company_stub (p_user                         IN companies.created_by%TYPE,
                                   p_logical_load_timestamp       IN companies.logical_load_timestamp%TYPE,
                                   p_effective_start_timestamp    IN companies.effective_start_timestamp%TYPE,
                                   p_company_id                   IN companies.company_id%TYPE)
    IS
      PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      IF p_company_id IS NOT NULL THEN
        INSERT INTO companies (company_id,
                               logical_load_timestamp,
                               created_by,
                               create_timestamp,
                               updated_by,
                               update_timestamp,
                               effective_start_timestamp)
                        VALUES(p_company_id,
                               p_logical_load_timestamp,
                               p_user,
                               SYSTIMESTAMP,
                               p_user,
                               SYSTIMESTAMP,
                               gc_default_timestamp
                               );
      END IF;
      COMMIT;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the company already exists no need to create the stub
        --
        NULL;
    END;

  -- ===================================================================================
  -- create_company_employee_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a company_employees stub in case the employee_id is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     COMPANY_EMPLOYEES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_employee_id                    Employee Id
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

    PROCEDURE create_company_employee_stub (p_user                         IN Company_Employees.created_by%TYPE,
                                            p_logical_load_timestamp       IN Company_Employees.logical_load_timestamp%TYPE,
                                            p_effective_start_timestamp    IN Company_Employees.effective_start_timestamp%TYPE,
                                            p_employee_id                  IN Company_Employees.Employee_Id%TYPE)
    IS
      PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      IF p_employee_id IS NOT NULL THEN
        INSERT INTO Company_Employees (employee_id,
                                      logical_load_timestamp,
                                      created_by,
                                      create_timestamp,
                                      updated_by,
                                      update_timestamp,
                                      effective_start_timestamp)
                        VALUES(p_employee_id,
                               p_logical_load_timestamp,
                               p_user,
                               SYSTIMESTAMP,
                               p_user,
                               SYSTIMESTAMP,
                               gc_default_timestamp
                               );
      END IF;
      COMMIT;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the company already exists no need to create the stub
        --
        NULL;
    END create_company_employee_stub;

  PROCEDURE put_company_source_wealth(p_company_id                  company_fnncl_dtl_srcs.company_id%TYPE,
                                      p_source_of_wealth            company_fnncl_dtl_src_tab,
                                      p_effective_start_timestamp   company_fnncl_dtl_srcs.effective_start_timestamp%TYPE,
                                      p_user                        company_fnncl_dtl_srcs.created_by%TYPE,
                                      p_logical_load_timestamp      company_fnncl_dtl_srcs.logical_load_timestamp%TYPE) IS

    TYPE ltab_company_fnncl_dtl_srcs IS TABLE OF company_fnncl_dtl_srcs%ROWTYPE;

    lv_updated_records ltab_company_fnncl_dtl_srcs;

  BEGIN
    --
    --Identify Updates
    --
    SELECT old_records.*
    BULK COLLECT INTO lv_updated_records
    FROM company_fnncl_dtl_srcs old_records,
         TABLE(CAST(p_source_of_wealth AS company_fnncl_dtl_src_tab)) new_records
    WHERE old_records.company_id = p_company_id AND
          old_records.source_name = new_records.source_name AND
          (nrg_common.has_value_changed(old_records.source_id, new_records.source_id) = 1 OR
           nrg_common.has_value_changed(old_records.source_details, new_records.source_details) = 1 OR
           nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1);

    --
    --Put History
    --

    FOR lv_cnt IN 1..lv_updated_records.COUNT LOOP
      put_history(p_old_record                 =>lv_updated_records(lv_cnt),
                  p_effective_end_timestamp    => p_effective_start_timestamp,
                  p_action                     => 'U');
    END LOOP;

    --
    --Insert update new records
    --

    MERGE INTO company_fnncl_dtl_srcs old_recs
    USING (SELECT * FROM TABLE(CAST(p_source_of_wealth AS company_fnncl_dtl_src_tab)))new_recs
    ON (old_recs.company_id = p_company_id AND
        old_recs.source_name = new_recs.source_name)
    WHEN MATCHED THEN
      UPDATE
      SET logical_load_timestamp = p_logical_load_timestamp,
          effective_start_timestamp = p_effective_start_timestamp,
          updated_by = p_user,
          update_timestamp = SYSTIMESTAMP,
          source_id = new_recs.source_id,
          source_details = new_recs.source_details,
          is_deleted = new_recs.is_deleted
      WHERE (nrg_common.has_value_changed(old_recs.source_id, new_recs.source_id) = 1 OR
             nrg_common.has_value_changed(old_recs.source_details, new_recs.source_details) = 1 OR
             nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1)
    WHEN NOT MATCHED THEN
      INSERT (company_id,
              source_id,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              source_name,
              source_details,
              is_deleted)
       VALUES(p_company_id,
              new_recs.source_id,
              p_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              new_recs.source_name,
              new_recs.source_details,
              new_recs.is_deleted);

    SELECT old_records.*
      BULK COLLECT INTO lv_updated_records
      FROM company_fnncl_dtl_srcs old_records
     WHERE company_id = p_company_id
       AND (source_name) NOT IN (SELECT /*+cardinality (tab, 1)*/ source_name
                                               FROM TABLE(CAST(p_source_of_wealth AS company_fnncl_dtl_src_tab)) tab);

    FOR lv_cnt IN 1..lv_updated_records.COUNT LOOP
      put_history(p_old_record                 => lv_updated_records(lv_cnt),
                  p_effective_end_timestamp    => p_effective_start_timestamp,
                  p_action                     => 'D');
    END LOOP;

    DELETE company_fnncl_dtl_srcs old_records
     WHERE company_id = p_company_id
       AND (source_name) NOT IN (SELECT /*+cardinality (tab, 1)*/ source_name
                                               FROM TABLE(CAST(p_source_of_wealth AS company_fnncl_dtl_src_tab)) tab)
       AND effective_start_timestamp <= p_effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_company
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a company
  --
  -- Notes:
  -- ------
  --
  --    Tables potentially populated:
  --
  --      COMPANIES
  --      COMPANIES_H
  --      COMPANY_ADDRESSES
  --      COMPANY_ADDRESSES_H
  --      COMPANY_CORPORATE_ASSOCIATES
  --      COMPANY_CORPORATE_ASSOCIATES_H
  --      COMPANY_CRPRT_ASSCT_SHRHLDNG
  --      COMPANY_CRPRT_ASSCT_SHRHLDNG_H
  --      COMPANY_EMAIL_ADDRESSES
  --      COMPANY_EMAIL_ADDRESSES_H
  --      COMPANY_EMPLOYEES
  --      COMPANY_EMPLOYEES_H
  --      COMPANY_PRIMARY_CONTACTS
  --      COMPANY_PRIMARY_CONTACTS_H
  --      COMPANY_TELEPHONE_NUMBERS
  --      COMPANY_TELEPHONE_NUMBERS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --    p_user                          User
  --    p_effective_start_timestamp     Effective Start Timestamp
  --    p_company_id                    Company Id
  --    p_partner_id                    Partner Id
  --    p_company_name                  Company Name
  --    p_full_legal_name               Full Legal Name
  --    p_company_type                  Company Type
  --    p_company_version               Version
  --    p_industry                      Industry
  --    p_fnncl_srvcs_license_number    Financial Services License Number
  --    p_registration_number           Registration Number
  --    p_spoken_language               Spoken Language
  --    p_date_of_incorporation         Date Of Incorporation
  --    p_country_of_incorporation      Country Of Incorporation
  --    p_place_of_incorporation        Place Of Incorporation
  --    p_telephone_numbers             Table Of Telephone Numbers
  --    p_addresses                     Table Of Addresses
  --    p_email_addresses               Table Of Email Addresses
  --    p_company_employees             Table Of Company Employees
  --    p_corporate_associates          Table Of Corporate Associates
  --    p_customer_ids                  Table Of Customer Ids
  --    p_primary_contact               Primary Contact Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_company(p_user                        companies.created_by%TYPE,
                          p_effective_start_timestamp   companies.effective_start_timestamp%TYPE,
                          p_company_id                  companies.company_id%TYPE,
                          p_partner_id                  companies.partner_id%TYPE,
                          p_company_name                companies.company_name%TYPE,
                          p_full_legal_name             companies.full_legal_name%TYPE,
                          p_company_type                companies.company_type%TYPE,
                          p_company_version             companies.company_version%TYPE,
                          p_industry                    companies.industry%TYPE,
                          p_fnncl_srvcs_license_number  companies.fnncl_srvcs_license_number%TYPE,
                          p_registration_number         companies.registration_number%TYPE,
                          p_spoken_language             companies.spoken_language%TYPE,
                          p_date_of_incorporation       companies.date_of_incorporation%TYPE,
                          p_country_of_incorporation    companies.country_of_incorporation%TYPE,
                          p_place_of_incorporation      companies.place_of_incorporation%TYPE,
                          p_is_deleted                  companies.is_deleted%TYPE,
                          p_customer_sub_type           companies.customer_sub_type%TYPE,             ---NEW
                          p_vat_registration_number     companies.vat_registration_number%TYPE,       ---NEW
                          p_country_of_domicile         companies.country_of_domicile%TYPE,           ---NEW
                          p_fatca_entity_classification companies.fatca_entity_classification%TYPE,   ---NEW
                          p_nffe_classification         companies.nffe_classification%TYPE,           ---NEW
                          p_giin                        companies.giin%TYPE,                          ---NEW
                          p_legal_entity_identifier     companies.legal_entity_identifier%TYPE,       ---NEW
                          p_legal_entity_identifier_expiry companies.legal_entity_identifier_expiry%TYPE,       ---NEW
                          p_corporate_sector            companies.corporate_sector %TYPE,             ---NEW
                          p_is_financial_counterparty   companies.is_financial_counterparty%TYPE,     ---NEW
                          p_is_delegated_report_req     companies.is_delegated_report_required%TYPE,  ---NEW
                          p_is_daily_report_extr_req    companies.is_daily_report_extr_required%TYPE, ---NEW
                          p_customer_id                 companies.customer_id%TYPE,                   ---NEW
                          p_industry_further_details    companies.industry_further_details%TYPE,      ---NEW
                          p_industry_other_details      companies.industry_other_details%TYPE,        ---NEW
                          p_is_politically_exposed      companies.is_politically_exposed%TYPE,        ---NEW
                          p_politically_exposed_details companies.politically_exposed_details%TYPE,   ---NEW
                          p_telehone_numbers            company_telephone_number_tab,
                          p_addresses                   company_address_tab,
                          p_email_addresses             company_email_address_tab,
                          p_company_employees           company_employee_tab,
                          p_corporate_associates        company_associate_tab,
                          p_customer_ids                customer_id_tab,
                          p_primary_contact             company_primary_contact_obj,
                          p_company_tax_registrations   company_tax_registrations_tab,                  --NEW
                          p_regulatory_body             companies.regulatory_body%TYPE,
                          p_net_worth_range             companies.net_worth_range%TYPE,
                          p_net_worth_range_crrncy      companies.net_worth_range_crrncy%TYPE,
                          p_cumulative_risk_limit_amount companies.cumulative_risk_limit_amount%TYPE,
                          p_cumulative_risk_limit_crrncy companies.cumulative_risk_limit_crrncy%TYPE,
                          p_source_of_wealth             company_fnncl_dtl_src_tab,
                          p_is_institutional_investor    companies.is_institutional_investor%TYPE,
                          p_knowledge_assessor_person_id companies.knowledge_assessor_person_id%TYPE,
                          p_company_form                 companies.company_form%TYPE,
                          p_company_name_latin           companies.company_name_latin%TYPE,
                          p_is_no_shrhldrs_over_25_perc  companies.is_no_shrhldrs_over_25_perc%TYPE,
                          p_corporate_sector_type        companies.corporate_sector_type%TYPE,
                          p_additional_trading_countries company_add_trd_cntry_tab
                          ) IS

      lv_logical_load_timestamp     TIMESTAMP(6);

      ltab_old_company_record       COMPANIES%ROWTYPE;

      lv_insert_status              NUMBER(1);--insert status will be 1 if company inserted else it will be 0

      lv_company_version            NUMBER; -- This is to normalise the version field

      TYPE ltab_corporate_associates IS TABLE OF company_corporate_associates%ROWTYPE;

      lv_deleted_associates ltab_corporate_associates;

      TYPE ltab_assoct_shrldng IS TABLE OF company_crprt_assct_shrhldng%ROWTYPE;

      lv_cmpny_crprt_assct_shrhldng company_crprt_assct_shrhldng%ROWTYPE;

    BEGIN

      lv_logical_load_timestamp := SYSTIMESTAMP;

      --
      --Normalise the fields
      --

      lv_company_version := NVL(p_company_version, 0);

      --
      --Create All The Stubs For Referential Integrity
      --

      --
      --Partner Stub
      --

      /*nrg_partner.create_partner_stub(p_user                         => p_user,
                                      p_logical_load_timestamp       => lv_logical_load_timestamp,
                                      p_effective_start_timestamp    => p_effective_start_timestamp,
                                      p_partner_id                   => p_partner_id);*/

      --
      --Customer Id Stub
      --

      IF p_customer_ids IS NOT NULL AND p_customer_ids.COUNT > 0 THEN
        nrg_customer.create_customer_stub(p_user                         => p_user,
                                          p_logical_load_timestamp       => lv_logical_load_timestamp,
                                          p_effective_start_timestamp    => p_effective_start_timestamp,
                                          p_customer_id                  => p_customer_ids);
      END IF;

      --
      --Try to insert the company.
      --If it exists then handle the dup val on index by locking that row for update
      --
      BEGIN
        INSERT INTO companies(company_id,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              effective_start_timestamp,
                              partner_id,
                              company_name,
                              full_legal_name,
                              company_type,
                              company_version,
                              industry,
                              fnncl_srvcs_license_number,
                              registration_number,
                              spoken_language,
                              date_of_incorporation,
                              country_of_incorporation,
                              place_of_incorporation,
                              is_deleted,
                              customer_sub_type,
                              vat_registration_number ,
                              country_of_domicile,
                              fatca_entity_classification,
                              nffe_classification,
                              giin,
                              legal_entity_identifier,
                              legal_entity_identifier_expiry,
                              corporate_sector,
                              is_financial_counterparty,
                              is_delegated_report_required,
                              is_daily_report_extr_required,
                              customer_id,
                              industry_further_details,
                              industry_other_details,
                              is_politically_exposed,
                              politically_exposed_details,
                              regulatory_body,
                              net_worth_range_crrncy,
                              net_worth_range,
                              cumulative_risk_limit_crrncy,
                              cumulative_risk_limit_amount,
                              is_institutional_investor,
                              knowledge_assessor_person_id,
                              company_form,
                              company_name_latin,
                              is_no_shrhldrs_over_25_perc,
                              corporate_sector_type
                              )
                      VALUES  (p_company_id,
                               lv_logical_load_timestamp,
                               p_user,
                               SYSTIMESTAMP,
                               p_user,
                               SYSTIMESTAMP,
                               p_effective_start_timestamp,
                               p_partner_id,
                               p_company_name,
                               p_full_legal_name,
                               p_company_type,
                               lv_company_version,
                               p_industry,
                               p_fnncl_srvcs_license_number,
                               p_registration_number,
                               p_spoken_language,
                               p_date_of_incorporation,
                               p_country_of_incorporation,
                               p_place_of_incorporation,
                               p_is_deleted,
                               p_customer_sub_type,
                               p_vat_registration_number,
                               p_country_of_domicile,
                               p_fatca_entity_classification,
                               p_nffe_classification,
                               p_giin,
                               p_legal_entity_identifier,
                               p_legal_entity_identifier_expiry,
                               p_corporate_sector,
                               p_is_financial_counterparty,
                               p_is_delegated_report_req,
                               p_is_daily_report_extr_req ,
                               p_customer_id,
                               p_industry_further_details,
                               p_industry_other_details,
                               p_is_politically_exposed,
                               p_politically_exposed_details,
                               p_regulatory_body,
                               p_net_worth_range_crrncy,
                               p_net_worth_range,
                               p_cumulative_risk_limit_crrncy,
                               p_cumulative_risk_limit_amount,
                               p_is_institutional_investor,
                               p_knowledge_assessor_person_id,
                               p_company_form,
                               p_company_name_latin,
                               p_is_no_shrhldrs_over_25_perc,
                               p_corporate_sector_type);

        lv_insert_status := 1;

      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          SELECT *
          INTO ltab_old_company_record
          FROM companies
          WHERE company_id = p_company_id
          FOR UPDATE;

          lv_insert_status := 0;
      END;

      CASE
        WHEN lv_insert_status = 1 THEN
          --
          --Since the company has been successfully inserted no need to process it now
          --

          NULL;
        WHEN lv_insert_status = 0 AND (ltab_old_company_record.company_version <= lv_company_version OR ltab_old_company_record.effective_start_timestamp = gc_default_timestamp) THEN
          --
          --This means an update for the company has been sent
          --

          UPDATE companies
          SET logical_load_timestamp        = lv_logical_load_timestamp,
              updated_by                    = p_user,
              update_timestamp              = SYSTIMESTAMP,
              effective_start_timestamp     = p_effective_start_timestamp,
              partner_id                    = p_partner_id,
              company_name                  = p_company_name,
              full_legal_name               = p_full_legal_name,
              company_type                  = p_company_type,
              company_version               = lv_company_version,
              industry                      = p_industry,
              fnncl_srvcs_license_number    = p_fnncl_srvcs_license_number,
              registration_number           = p_registration_number,
              spoken_language               = p_spoken_language,
              date_of_incorporation         = p_date_of_incorporation,
              country_of_incorporation      = p_country_of_incorporation,
              place_of_incorporation        = p_place_of_incorporation,
              is_deleted                    = p_is_deleted,
              customer_sub_type             = p_customer_sub_type,
              vat_registration_number       = p_vat_registration_number,
              country_of_domicile           = p_country_of_domicile,
              fatca_entity_classification   = p_fatca_entity_classification,
              nffe_classification           = p_nffe_classification,
              giin                          = p_giin,
              legal_entity_identifier       = p_legal_entity_identifier,
              legal_entity_identifier_expiry= p_legal_entity_identifier_expiry,
              corporate_sector              = p_corporate_sector,
              is_financial_counterparty     = p_is_financial_counterparty,
              is_delegated_report_required  = p_is_delegated_report_req,
              is_daily_report_extr_required = p_is_daily_report_extr_req ,
              customer_id                   = p_customer_id,
              industry_further_details      = p_industry_further_details,
              industry_other_details        = p_industry_other_details,
              is_politically_exposed        = p_is_politically_exposed,
              politically_exposed_details   = p_politically_exposed_details,
              regulatory_body               = p_regulatory_body,
              net_worth_range_crrncy        = p_net_worth_range_crrncy,
              net_worth_range               = p_net_worth_range,
              cumulative_risk_limit_crrncy  = p_cumulative_risk_limit_crrncy,
              cumulative_risk_limit_amount  = p_cumulative_risk_limit_amount,
              is_institutional_investor     = p_is_institutional_investor,
              knowledge_assessor_person_id  = p_knowledge_assessor_person_id,
              company_form                  = p_company_form,
              company_name_latin            = p_company_name_latin,
              is_no_shrhldrs_over_25_perc   = p_is_no_shrhldrs_over_25_perc,
              corporate_sector_type         = p_corporate_sector_type
          WHERE company_id = p_company_id AND
                (nrg_common.has_value_changed(partner_id,p_partner_id) = 1 OR
                nrg_common.has_value_changed(company_name,p_company_name) = 1 OR
                nrg_common.has_value_changed(full_legal_name,p_full_legal_name) = 1 OR
                nrg_common.has_value_changed(company_type,p_company_type) = 1 OR
                nrg_common.has_value_changed(company_version,lv_company_version) = 1 OR
                nrg_common.has_value_changed(industry,p_industry) = 1 OR
                nrg_common.has_value_changed(fnncl_srvcs_license_number,p_fnncl_srvcs_license_number) = 1 OR
                nrg_common.has_value_changed(registration_number,p_registration_number) = 1 OR
                nrg_common.has_value_changed(spoken_language,p_spoken_language) = 1 OR
                nrg_common.has_value_changed(date_of_incorporation,p_date_of_incorporation) = 1 OR
                nrg_common.has_value_changed(country_of_incorporation,p_country_of_incorporation) = 1 OR
                nrg_common.has_value_changed(place_of_incorporation,p_place_of_incorporation) = 1 OR
                nrg_common.has_value_changed(is_deleted,p_is_deleted) = 1 OR
                nrg_common.has_value_changed(customer_sub_type, p_customer_sub_type) = 1 OR
                nrg_common.has_value_changed(vat_registration_number, p_vat_registration_number) = 1 OR
                nrg_common.has_value_changed(country_of_domicile, p_country_of_domicile) = 1 OR
                nrg_common.has_value_changed(fatca_entity_classification, p_fatca_entity_classification) = 1 OR
                nrg_common.has_value_changed(nffe_classification, p_nffe_classification) = 1 OR
                nrg_common.has_value_changed(giin, p_giin) = 1 OR
                nrg_common.has_value_changed(legal_entity_identifier, p_legal_entity_identifier) = 1 OR
                nrg_common.has_value_changed(legal_entity_identifier_expiry, p_legal_entity_identifier_expiry) = 1 OR
                nrg_common.has_value_changed(corporate_sector, p_corporate_sector) = 1 OR
                nrg_common.has_value_changed(is_financial_counterparty, p_is_financial_counterparty) = 1 OR
                nrg_common.has_value_changed(is_delegated_report_required, p_is_delegated_report_req) = 1 OR
                nrg_common.has_value_changed(is_daily_report_extr_required, p_is_daily_report_extr_req ) = 1 OR
                nrg_common.has_value_changed(customer_id, p_customer_id) = 1 OR
                nrg_common.has_value_changed(industry_further_details, p_industry_further_details) = 1 OR
                nrg_common.has_value_changed(industry_other_details, p_industry_other_details) = 1 OR
                nrg_common.has_value_changed(is_politically_exposed, p_is_politically_exposed) = 1 OR
                nrg_common.has_value_changed(politically_exposed_details, p_politically_exposed_details) = 1 OR
                nrg_common.has_value_changed(regulatory_body, p_regulatory_body) = 1 OR
                nrg_common.has_value_changed(net_worth_range_crrncy, p_net_worth_range_crrncy) = 1 OR
                nrg_common.has_value_changed(net_worth_range, p_net_worth_range) = 1 OR
                nrg_common.has_value_changed(cumulative_risk_limit_crrncy, p_cumulative_risk_limit_crrncy) = 1 OR
                nrg_common.has_value_changed(cumulative_risk_limit_amount, p_cumulative_risk_limit_amount) = 1 OR
                nrg_common.has_value_changed(is_institutional_investor, p_is_institutional_investor) = 1 OR
                nrg_common.has_value_changed(knowledge_assessor_person_id, p_knowledge_assessor_person_id) = 1 OR
                nrg_common.has_value_changed(company_form, p_company_form) = 1 OR
                nrg_common.has_value_changed(company_name_latin, p_company_name_latin) = 1 OR
                nrg_common.has_value_changed(is_no_shrhldrs_over_25_perc, p_is_no_shrhldrs_over_25_perc) = 1 OR
                nrg_common.has_value_changed(corporate_sector_type, p_corporate_sector_type) = 1
                );

        IF SQL%ROWCOUNT > 0 AND ltab_old_company_record.effective_start_timestamp <> gc_default_timestamp THEN
          put_history(p_company_record          => ltab_old_company_record,
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action                  => 'U');
        END IF;

        WHEN lv_insert_status = 0 AND ltab_old_company_record.company_version > lv_company_version THEN
          --
          --In case a old message is replayed
          --

          SELECT p_company_id,
                 lv_logical_load_timestamp,
                 p_user created_by,
                 SYSTIMESTAMP create_timestamp,
                 p_user updated_by,
                 SYSTIMESTAMP update_timestamp,
                 p_effective_start_timestamp effective_start_timestamp,
                 p_partner_id partner_id,
                 p_company_name company_name,
                 p_full_legal_name full_legal_name,
                 p_company_type company_type,
                 lv_company_version company_version,
                 p_industry industry,
                 p_fnncl_srvcs_license_number fnncl_srvcs_license_number,
                 p_registration_number registration_number,
                 p_spoken_language spoken_language,
                 p_date_of_incorporation date_of_incorporation,
                 p_country_of_incorporation country_of_incorporation,
                 p_place_of_incorporation place_of_incorporation,
                 p_is_deleted is_deleted,
                 p_customer_sub_type,
                 p_vat_registration_number,
                 p_country_of_domicile,
                 p_fatca_entity_classification,
                 p_nffe_classification,
                 p_giin,
                 p_legal_entity_identifier,
                 p_legal_entity_identifier_expiry legal_entity_identifier_expiry,
                 p_corporate_sector,
                 p_is_financial_counterparty,
                 p_is_delegated_report_req,
                 p_is_daily_report_extr_req ,
                 p_customer_id,
                 p_industry_further_details,
                 p_industry_other_details,
                 p_is_politically_exposed,
                 p_politically_exposed_details,
                 p_regulatory_body,
                 p_net_worth_range_crrncy,
                 p_net_worth_range,
                 p_cumulative_risk_limit_crrncy,
                 p_cumulative_risk_limit_amount,
                 p_is_institutional_investor,
                 p_knowledge_assessor_person_id,
                 p_company_form,
                 p_company_name_latin,
                 p_is_no_shrhldrs_over_25_perc,
                 p_corporate_sector_type
          INTO   ltab_old_company_record.company_id,
                 ltab_old_company_record.logical_load_timestamp,
                 ltab_old_company_record.created_by,
                 ltab_old_company_record.create_timestamp,
                 ltab_old_company_record.updated_by,
                 ltab_old_company_record.update_timestamp,
                 ltab_old_company_record.effective_start_timestamp,
                 ltab_old_company_record.partner_id,
                 ltab_old_company_record.company_name,
                 ltab_old_company_record.full_legal_name,
                 ltab_old_company_record.company_type,
                 ltab_old_company_record.company_version,
                 ltab_old_company_record.industry,
                 ltab_old_company_record.fnncl_srvcs_license_number,
                 ltab_old_company_record.registration_number,
                 ltab_old_company_record.spoken_language,
                 ltab_old_company_record.date_of_incorporation,
                 ltab_old_company_record.country_of_incorporation,
                 ltab_old_company_record.place_of_incorporation,
                 ltab_old_company_record.is_deleted,
                 ltab_old_company_record.customer_sub_type,
                 ltab_old_company_record.vat_registration_number,
                 ltab_old_company_record.country_of_domicile,
                 ltab_old_company_record.fatca_entity_classification,
                 ltab_old_company_record.nffe_classification,
                 ltab_old_company_record.giin,
                 ltab_old_company_record.legal_entity_identifier,
                 ltab_old_company_record.legal_entity_identifier_expiry,
                 ltab_old_company_record.corporate_sector,
                 ltab_old_company_record.is_financial_counterparty,
                 ltab_old_company_record.is_delegated_report_required,
                 ltab_old_company_record.is_daily_report_extr_required,
                 ltab_old_company_record.customer_id,
                 ltab_old_company_record.industry_further_details,
                 ltab_old_company_record.industry_other_details,
                 ltab_old_company_record.is_politically_exposed,
                 ltab_old_company_record.politically_exposed_details,
                 ltab_old_company_record.regulatory_body,
                 ltab_old_company_record.net_worth_range_crrncy,
                 ltab_old_company_record.net_worth_range,
                 ltab_old_company_record.cumulative_risk_limit_crrncy,
                 ltab_old_company_record.cumulative_risk_limit_amount,
                 ltab_old_company_record.is_institutional_investor,
                 ltab_old_company_record.knowledge_assessor_person_id,
                 ltab_old_company_record.company_form,
                 ltab_old_company_record.company_name_latin,
                 ltab_old_company_record.is_no_shrhldrs_over_25_perc,
                 ltab_old_company_record.corporate_sector_type
          FROM DUAL;

          put_history(p_company_record          => ltab_old_company_record,
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action                  => 'I');

      END CASE;

      --
      --Now write all the detail tables
      --

      --
      --Telephone Numbers
      --
      --IF p_telehone_numbers IS NOT NULL THEN
        put_telephone_number(p_company_id                  => p_company_id,
                             p_telehone_numbers            => p_telehone_numbers,
                             p_effective_start_timestamp   => p_effective_start_timestamp,
                             p_user                        => p_user,
                             p_logical_load_timestamp      => lv_logical_load_timestamp);
      --END IF;
      --
      --Corporate Associates
      --

      --
      --In case when a company does not have any associate on the message
      --
      IF p_corporate_associates IS NULL THEN

        --
        --Deleted All Associates
        --

        SELECT *
        BULK COLLECT INTO lv_deleted_associates
        FROM company_corporate_associates
        WHERE company_id = p_company_id;

        --
        --Soft Delete Associates
        --

        DELETE company_corporate_associates
        WHERE company_id = p_company_id;

        --
        --Put Previous Record To History
        --

        FOR lv_cnt IN 1..lv_deleted_associates.count LOOP
          --
          --Delete Shareholding
          --

          BEGIN
            SELECT *
            INTO lv_cmpny_crprt_assct_shrhldng
            FROM company_crprt_assct_shrhldng
            WHERE company_associate_id = lv_deleted_associates(lv_cnt).corporate_associate_id AND
                  company_id = lv_deleted_associates(lv_cnt).company_id;

            put_history (p_effective_end_timestamp => p_effective_start_timestamp,
                         p_action => 'D',
                         p_old_record => lv_cmpny_crprt_assct_shrhldng);

            DELETE company_crprt_assct_shrhldng
            WHERE company_associate_id = lv_deleted_associates(lv_cnt).corporate_associate_id AND
                  company_id = lv_deleted_associates(lv_cnt).company_id;
          EXCEPTION
            WHEN NO_DATA_FOUND THEN
              NULL;
          END;

          put_history(p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D',
                      p_corporate_associate_record=> lv_deleted_associates(lv_cnt));

        END LOOP;
      ELSE

        --
        --Identify Deleted Associates
        --

        BEGIN
          SELECT a.*
          BULK COLLECT INTO lv_deleted_associates
          FROM company_corporate_associates a LEFT OUTER JOIN TABLE(CAST(p_corporate_associates AS company_associate_tab)) b
          ON a.company_id = b.company_id AND a.corporate_associate_id = b.associate_id
          WHERE a.company_id = p_company_id AND
                b.associate_id IS NULL;

          --
          --Soft Delete Associates
          --

          DELETE company_corporate_associates
          WHERE company_id = p_company_id AND
                corporate_associate_id IN (SELECT a.corporate_associate_id
                                            FROM company_corporate_associates a LEFT OUTER JOIN TABLE(CAST(p_corporate_associates AS company_associate_tab)) b
                                            ON a.company_id = b.company_id AND a.corporate_associate_id = b.associate_id
                                            WHERE a.company_id = p_company_id AND
                                                  b.associate_id IS NULL);

          --
          --Put Previous Record To History
          --

          FOR lv_cnt IN 1..lv_deleted_associates.count LOOP

            BEGIN
              SELECT *
              INTO lv_cmpny_crprt_assct_shrhldng
              FROM company_crprt_assct_shrhldng
              WHERE company_associate_id = lv_deleted_associates(lv_cnt).corporate_associate_id AND
                    company_id = lv_deleted_associates(lv_cnt).company_id;

              put_history (p_effective_end_timestamp => p_effective_start_timestamp,
                           p_action => 'D',
                           p_old_record => lv_cmpny_crprt_assct_shrhldng);

              DELETE company_crprt_assct_shrhldng
              WHERE company_associate_id = lv_deleted_associates(lv_cnt).corporate_associate_id AND
                    company_id = lv_deleted_associates(lv_cnt).company_id;
            EXCEPTION
              WHEN NO_DATA_FOUND THEN
                NULL;
            END;
            put_history(p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action => 'D',
                        p_corporate_associate_record=> lv_deleted_associates(lv_cnt));


          END LOOP;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL;
        END;


        FOR lv_cnt IN 1..p_corporate_associates.COUNT LOOP
          put_corporate_associate(p_user                          => p_user,
                                  p_effective_start_timestamp     => p_effective_start_timestamp,
                                  p_logical_load_timestamp        => lv_logical_load_timestamp,
                                  p_corporate_associate           => p_corporate_associates(lv_cnt));
        END LOOP;

      END IF;

      --
      --Company Addresses
      --
      --IF p_addresses IS NOT NULL THEN
        put_company_address (p_company_id                  => p_company_id,
                             p_addresses                   => p_addresses,
                             p_effective_start_timestamp   => p_effective_start_timestamp,
                             p_user                        => p_user,
                             p_logical_load_timestamp      => lv_logical_load_timestamp);
      --END IF;
      --
      --Company Email Addresses
      --
      --IF p_email_addresses IS NOT NULL THEN
        put_company_email_address  (p_company_id                  => p_company_id,
                                    p_email_addresses             => p_email_addresses,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_user                        => p_user,
                                    p_logical_load_timestamp      => lv_logical_load_timestamp);
      --END IF;
      --
      -- Company Employees
      --

      --IF p_company_employees IS NOT NULL THEN
        put_company_employee (p_company_id                  => p_company_id,
                              p_employees                   => p_company_employees,
                              p_effective_start_timestamp   => p_effective_start_timestamp,
                              p_user                        => p_user,
                              p_logical_load_timestamp      => lv_logical_load_timestamp);
      --END IF;
      --
      -- Company Tax Registrations
      --

      --IF p_company_tax_registrations IS NOT NULL THEN
        put_company_tax_registration(p_company_id                  => p_company_id,
                                     p_company_tax_registration    => p_company_tax_registrations,
                                     p_effective_start_timestamp   => p_effective_start_timestamp,
                                     p_user                        => p_user,
                                     p_logical_load_timestamp      => lv_logical_load_timestamp);
      --END IF;

     -- IF p_additional_trading_countries IS NOT NULL THEN
        put_company_addtnl_trdng_cntrys(p_company_id                  => p_company_id,
                                        p_company_addtnl_trdng_cntrys => p_additional_trading_countries,
                                        p_effective_start_timestamp   => p_effective_start_timestamp,
                                        p_user                        => p_user,
                                        p_logical_load_timestamp      => lv_logical_load_timestamp);
     -- END IF;

      --IF p_primary_contact IS NOT NULL THEN
        put_company_primary_contact(p_company_id                  => p_company_id,
                                    p_primary_contact             => p_primary_contact,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_user                        => p_user,
                                    p_logical_load_timestamp      => lv_logical_load_timestamp);
      --END IF;

      IF p_source_of_wealth IS NOT NULL AND
         (ltab_old_company_record.company_version <= lv_company_version OR
          ltab_old_company_record.effective_start_timestamp = gc_default_timestamp OR
          lv_insert_status = 1) THEN
        put_company_source_wealth(p_company_id                  => p_company_id,
                                  p_source_of_wealth            => p_source_of_wealth,
                                  p_effective_start_timestamp   => p_effective_start_timestamp,
                                  p_user                        => p_user,
                                  p_logical_load_timestamp      => lv_logical_load_timestamp);
      END IF;

    END;

  -- ===================================================================================
  -- get_stubbed_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed id's to return (Optional - not set returns all)
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
  FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR

  IS
    lcuv_result           SYS_REFCURSOR;

    lex_unknown_platform  EXCEPTION;
  BEGIN
    logger.logger.set_module('get_stubbed_ids');

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT company_id
               FROM   companies
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT company_id
               FROM   companies
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;

      logger.logger.set_module(NULL);

      RETURN lcuv_result;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END get_stubbed_ids;

END nrg_company;
/