DROP TYPE position_tab FORCE;
DROP TYPE position_obj FORCE;

CREATE OR REPLACE TYPE position_obj
IS
  OBJECT
  (
    snapshot_time                    TIMESTAMP(6),
    order_id                         VARCHAR2(50),
    custominfo_virt_portcode         VARCHAR2(50),
    direction                        VARCHAR2(10),
    financing_ratio                  NUMBER,
    quantity                         NUMBER,
    quantity_currency                VARCHAR2(3),
    margin                           NUMBER,
    margin_currency                  VARCHAR2(3),
    amount                           NUMBER,
    amount_currency                  VARCHAR2(3),
    margin_secondary                 NUMBER,
    margin_secondary_ccy             VARCHAR2(3),
    margin_in_trad_acnt_prim_ccy     NUMBER,
    amount_in_trad_acnt_prim_ccy     NUMBER,
    open_trade_id                    VARCHAR2(50),
    open_trade_price                 NUMBER,
    open_trade_margin_fx_rate        NUMBER,
    open_time                        TIMESTAMP(6),
    last_modified_time               TIMESTAMP(6),
    quantity_designator              VARCHAR2(50),
    product_instrument_code          VARCHAR2(50),
    product_wrapper_code             VARCHAR2(50),
    product_generation               NUMBER,
    product_point_multiplier         NUMBER,
    product_currency                 VARCHAR2(3),
    product_financing_rat_max        NUMBER,
    product_schema_code              VARCHAR2(50),
    trading_account_type             VARCHAR2(50),
    trading_account_code             NUMBER,
    trading_account_codifier         VARCHAR2(50),
    trading_account_function         VARCHAR2(50),
    trading_accnt_primary_ccy        VARCHAR2(3),
    opentrade_quantity_fx_rate       NUMBER,
    price_designator                 VARCHAR2(50),
    is_inst_ccy_in_fracnal_parts     VARCHAR2(10),
    fractional_part_ratio            NUMBER,
    normalised_order_type            VARCHAR2(50),
    normalised_open_price            NUMBER,
    normalised_direction_mulplr      NUMBER,
    normalised_open_qty_trad_ccy     NUMBER,
    normalised_open_val_trad_ccy     NUMBER,
    normalised_trading_ccy           VARCHAR2(3),
    normalised_margin_type           VARCHAR2(50),
    normalised_margin_reqrment       NUMBER,
    mm_backoffice_ref                NUMBER,
    mm_account_id                    NUMBER,
    mm_instrument_id                 NUMBER,
    is_primary                       VARCHAR2(10),
    mm_value_date                    DATE,
    eod_price                        NUMBER,
    counterparty_id                  NUMBER,
    opening_trade_amount_fx_rate     NUMBER,
    opening_trade_app_to_units       NUMBER,
    is_automatically_rolled          VARCHAR2(3),
    rolled_open_trade_id             VARCHAR2(50),
    eod_value                        NUMBER,
    eod_value_ccy                    VARCHAR2(3),
    eod_value_in_accnt_ccy           NUMBER,
    unrealised_pnl                   NUMBER,
    unrealised_pnl_ccy               VARCHAR2(3),
    unrealised_pnl_in_accnt_ccy      NUMBER,
    cnvrsn_rate_to_accnt_ccy         NUMBER,
    is_mm_old_style                  VARCHAR2(3),
    hedge_asset_class                VARCHAR2(100),
    hedge_instrument_code_external   VARCHAR2(100),
    hedge_expiry_month_code          VARCHAR2(100),
    hedge_risk_bucket                VARCHAR2(100),
    hedge_ProfitLoss                 NUMBER,
    hedge_Commission                 NUMBER,
    execution_type                   VARCHAR2(60),
    trading_scope                    VARCHAR2(100),
    managed_open_trade_info          eod_open_trds_managed_info_tab,
    binary_type                      VARCHAR2(50),
    settle_time                      TIMESTAMP(6),
    tenor                            VARCHAR2(50),
    strike_price                     NUMBER,
    strike_price_additional          NUMBER,
    tenor_start_time                 TIMESTAMP(6),
    open_trade_instrument_price      NUMBER,
    hedge_opening_reference          NUMBER,
    hdg_redemption_date              TIMESTAMP(6),
    hdg_current_coupon_date          TIMESTAMP(6),
    hdg_accrued_interest_days        NUMBER,
    hdg_accrued_interest_amount      NUMBER,
    hdg_eval_accrued_intrst_days     NUMBER,
    hdg_eval_accrued_intrst_amnt     NUMBER,
    hdg_effctv_intrst_rate           NUMBER,
    hdg_effctv_intrst_base_amnt      NUMBER,
    hdg_afs_reserve_amount           NUMBER,
    hdg_quantity_settlement_date     NUMBER,
    hdg_new_effctv_intrst_bse_amt    NUMBER,
    hdg_new_afs_reserve_amount       NUMBER,
    hdg_was_manually_corrected       VARCHAR2(3),
    hdg_crrnt_coupon_pymnt_date      TIMESTAMP(6),
    hdg_redemption_payment_date      TIMESTAMP(6),
    forced_margin_fx_rate            NUMBER,
    opn_accrd_trnvr_in_accnt_ccy     NUMBER,
    value_date                       TIMESTAMP(6),
    pair_currency                    VARCHAR2(3),
    primary_currency                 VARCHAR2(3),
    secondary_currency               VARCHAR2(3),
    primary_amount                   NUMBER,
    secondary_amount                 NUMBER,
    margin_percentage                NUMBER,
    accrued_fees                     NUMBER,
    accrued_fees_in_account_currency NUMBER,
    transaction_type	               VARCHAR2(100),
    hdg_evaluation_spot_price	       NUMBER,
    hdg_settlement_date	             DATE,
    hdg_settlement_time	             TIMESTAMP(6),
    hdg_settlement_status	           VARCHAR2(100),
    hdg_carrying_costs	             NUMBER
  );
/