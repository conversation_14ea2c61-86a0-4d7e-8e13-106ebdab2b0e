CREATE OR REPLACE PACKAGE bi_ods.nrg_order
AS
    -- ===================================================================================
    -- NRG_ORDER
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the orders model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     05/09/2011   Sanket Mittal      1.0    Creation
    --     14/10/2011   Manoj Kumar        1.1    Modified the put_order method
    --     19/10/2011   Manoj Kumar        1.2    Modified the put_order method for concurrent writes
    --     21/10/2011   Sanket Mittal      1.3    Modified to remove the business rules for enums
    --     21/10/2011   Sanket Mittal      1.4    Included the new field order_type
    --     02/11/2011   Paul Flynn         1.5    Added MM Account ID Attribute
    --     04/11/2011   Manoj Kumar        1.6    Updated the comments and variables,removed remove_order method
    --     04/11/2011   Patrick Dinwiddy   1.7    Rename use_custom_code to use_custom_quote, applied UPPER() to controlled_order_type
    --     17/11/2011   Mark Gornicki      1.9    Changed the parameter name p_trading_account_code to p_trading_account_id
  --                                            and renamed column limit_is_trailing to is_limit_trailing
    --                                            and added else clause to order type case statement
    --                                            bringing it into line with Trades
    --     18/11/2011   Mark Gornicki      2.0    Fixed bug in section for direct insert into history where the same parameter value
    --                                            p_is_ta_sspn_fr_trd_rvsl_ignrd was being inserted into 2 target columns by mistake.
    --                                            Also fixed spelling mistake on p_activation_time
    --     24/11/2011   Sanket Mittal      2.1    Included normalised_quantity
    --     25/11/2011   Sanket Mittal      2.2    Amended create_trade_stub to set BUSINESS_DATE to the gc_default_timestamp
    --                                            to ensure the column has a value for partitioning otherwise it will fail.
    --                                            Amended Put_Trade and Put_History to handle the processing for internal hedge trades.
    --                                            This is for MarketMaker data such that it would have no trading_account_id set and
    --                                            we must perform a lookup based on MarketMaker source ids, so set the correct
    --                                            Trading Account ID/Type combination for referential integrity
    --     29/11/2011   Sanket Mittal      2.3    Updated the business rule for normalised quantity
    --     01/12/2011   Sanket Mittal      2.4    Added logic to get the currency for TIQ instruments from MM using ref table
    --     08/12/2011   Patrick Dinwiddy   2.5    Included the call to populate quantity_designator
    --     08/12/2011   Sanket Mittal      2.6    Included the new parameter trading_account_type
    --     13/12/2011   Sanket Mittal      2.8    Included two new paramters margin_requirement and margin_type
    --     20/12/2011   Sanket Mittal      2.9    Added the call to write positions and updated the chnage for trade time to make it static
    --     29/12/2011   Sanket Mittal      3.0    Added the parameter for Reporting Date on put_order
    --     04/12/2011   Sanket Mittal      3.1    Added the method get_order_ids
    --     19/11/2012   Shanavaz Malayodu  4.1    Added parameters to put_order procedure
    --     03/01/2013   Sanket Mittal      4.2    Added new fields to put_orders
    --     15/01/2013   Sanket Mittal      4.3    Added sub routine to rec the stub id's
    --     12/03/2013   Sanket Mittal      4.5    Updated the spec for P2 changes
    --     05/03/2014   Adam Krasnicki     4.6    get_closed_executed_order_ids procedure added
    --     14/05/2014   Adam Krasnicki     4.7    get_stoploss_order_ids function added BER-907
    --     05/09/2014   Adam Krasnicki     5.8    3 new columns added to ORDERS[_H] tables
    --     29/01/2015   Adam Krasnicki     5.9    4 new cols added and one renamed in orders[_h] table, 4 nuew param in put_order proc
    --     08/05/2015   Adam Krasnicki     6.1    put_order: two new parameters p_execution_type, p_assigned_to
    --     29/07/2015   Adam Krasnicki     6.2    put_orders: new parameter p_close_sub_reason
    --     30/09/2015   Sanket Mittal      6.3    put_orders: new parameters p_trading_scope, p_allocation_order_id, p_allocation_trading_risk_schema and p_managed_order_info
    --     20/01/2016   Sanket Mittal      6.5    put_orders: Added new parameters for binary p_bo_type, p_bo_settle_time, p_bo_tenor, p_bo_strike_price, p_bo_strike_price_additional
    --     08/06/2016   Sanket Mittal      6.8    put_orders new attributes added
    --     22/08/2016   Sanket Mittal      7.1    BER-2828 New Attribute: OrderType
    --     18/10/2016   Sanket Mittal      7.3    BER-2894 Changed the get_order_type Function
    --     19/12/2016   Sanket Mittal      7.4    BER-3156 SWS 36 - BI_ODS.ORDERS - New Attributes
    --     16/03/2017   Sanket Mittal      7.5    BER-3432 SWS 37 - BI_ODS.ORDERS - New Attribute
    --     18/07/2017   Sanket Mittal      7.6    BER-3781 NRG_ORDER - bi_ods.orders - changes
    --     29/04/2019   Patrick Dinwiddy   8.0    JCS-10569 - BI_ODS.ORDERS - New Attributes
    --     03/09/2020   Patrick Dinwiddy   8.2    JCS-13252
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

    -- ===================================================================================
    -- create_order_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a order stub in case the order is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     ORDERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_order_id                       Order Id
    --     p_platform                       Platform
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_order_stub (p_user                       IN orders.created_by%TYPE,
                                 p_logical_load_timestamp     IN orders.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp  IN orders.effective_start_timestamp%TYPE,
                                 p_order_id                   IN orders.order_id%TYPE,
                                 p_platform                   IN orders.platform%TYPE);


    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the orders record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_order_record              This is the old version of the order record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_order_record         orders%ROWTYPE,
                           p_effective_end_timestamp  orders_h.effective_end_timestamp%TYPE,
                           p_action                   orders_h.action%TYPE);

    -- ===================================================================================
    -- put_order_close_comment
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write Order Close Comment
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_order_id                      Order Id
    --      p_platform                      Platform
    --      p_close_comment                 Close Comment Details
    --      p_user                          User
    --      p_effective_start_timestamp     Effective Start Timestamp
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_order_close_comment (p_order_id                   IN orders_close_comment.order_id%TYPE,
                                       p_platform                   IN orders_close_comment.platform%TYPE,
                                       p_close_comment              IN order_close_comment_obj,
                                       p_user                       IN orders_close_comment.created_by%TYPE,
                                       p_effective_start_timestamp  IN orders_close_comment.effective_start_timestamp%TYPE,
                                       p_old_effctv_strt_timestamp  IN orders_close_comment.effective_start_timestamp%TYPE,
                                       p_logical_load_timestamp     IN orders_close_comment.logical_load_timestamp%TYPE);

    -- ===================================================================================
    -- put_order
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put an order
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     ORDERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                            Description
    --     ---------------------------------   ----------------------------------------------
    --      p_user                                User
    --      p_effective_start_timestamp           Effective Start Timestamp
    --      p_business_date                       Business Date
    --      p_reporting_date                      Reporting Date
    --      p_publish_time                        Publish Time
    --      p_event_time                          Event Time
    --      p_changed_character                   Changed Character
    --      p_changed_reason_code                 Changed Reason code
    --      p_changed_reason_codifier             Changed Reason Codifier
    --      p_order_id                            Order Id
    --      p_version_number                      Version Number
    --      p_is_deleted                          Is Deleted
    --      p_creation_time                       Creation Time
    --      p_creation_identity_token             Creation Identity Token
    --      p_crtn_on_bhlf_of_idntty_tkn          Creation On Behalf Of Identity Token
    --      p_update_time                         Update Time
    --      p_update_identity_token               Update Identity Tokan
    --      p_updt_on_bhlf_of_idntty_tkn          Update On Behalf Of Identity Token
    --      p_product_instrument_code             Product Instrument Code
    --      p_product_wrapper_code                Product Wrapper Code
    --      p_feed_symbol                         Feed Symbol
    --      p_prophet_symbol                      Prophet Symbol
    --      p_mm_instrument_id                    MM Instrument Id
    --      p_product_fractional_part_rat         Product Fractional Part Ratio
    --      p_is_crrncy_in_fractnal_parts         Is Currency In Fractional Parts
    --      p_product_generation                  Product Generation
    --      p_product_point_multiplier            Product Point Multiplier
    --      p_product_currency                    Product Currency
    --      p_product_financing_ratio_max         Product Financing Ration Maximum
    --      p_product_schema_code                 Product Schema Code
    --      p_trading_account_id                  Trading Account Id
    --      p_trading_account_codifier            Trading Account Codifier
    --      p_trading_accnt_primary_curncy        Trading Account Primary Currency
    --      p_trading_account_function            Trading Account Function
    --      p_channel_id                          Channel Id
    --      p_request_id                          Request Id
    --      p_session_id                          Session Id
    --      p_requested_direction                 Requested Direction
    --      p_requested_financing_ratio           Requested Financing Ratio
    --      p_requested_quantity                  Requested Quantity
    --      p_requested_valid_till                Requested Valid Till
    --      p_reqsted_trade_close_oder_id         Requested Trade Close Order Id
    --      p_customer_info                       Custom Info
    --      p_cstm_info_vrtl_prtfl_cd             Custom Info Virtual Prtfl Code
    --      p_logical_update_time                 Logical Update Time
    --      p_visit_id                            Visit Id
    --      p_state                               State
    --      p_activation_time                    Activation Time
    --      p_actual_valid_till                   Actual Valid Till
    --      p_is_mandatory                        Is Mandatory
    --      p_execution_condition                 Execution Condition
    --      p_close_time                          Close Time
    --      p_close_comment                       Close Comment
    --      p_close_reason                        Close Reason
    --      p_close_sub_reason                    Close Sub Reason
    --      p_quantity_designator                 Quantity Designator
    --      p_quantity_matchable                  Quantity  Matchable
    --      p_quantity_match                      Quantity Match
    --      p_quantity_remaining                  Quantity Remaining
    --      p_quantity_currency                   Quantity Currency
    --      p_limit_price                         Limit Price
    --      p_limit_price_condition               Limit Price Condition
    --      p_is_limit_trailing                   Limit Is Trailing
    --      p_limit_trailing_distance             Limit Trailing Distance
    --      p_limit_trailing_best_price           Limit Trailing Best Price
    --      p_limit_traling_best_price_tme        Limit Trailing Best Price Time
    --      p_guaranteed_trade_price              Guaranteed Trade Price
    --      p_gurntd_trd_prc_cndtn                Guaranteed Trade Price Condition
    --      p_related_child_order_type            Related Child Order Type
    --      p_related_parent_order_id             Related Parent Order Id
    --      p_related_initiating_order_id         Related Initiating Order Id
    --      p_protection_type                     Protection Type
    --      p_open_trade_id                       Open Trade Id
    --      p_open_trade_quantity                 Open Trade Quantity
    --      p_open_trade_price                    Open Trade Price
    --      p_open_trade_time                     Open Trade Time
    --      p_open_trade_amount                   Open Trade Amount
    --      p_open_trade_amount_currency          Open Trade Amount Currency
    --      p_open_trade_margin                   Open Trade Margin
    --      p_open_trade_margin_currency          Open Trade Margin Currency
    --      p_open_trade_mrn_fx_rate_bid          Open Trade Margin Fx Rate Bid
    --      p_open_trade_mrn_fx_rate_ask          Open Trade Margin Fx rate Ask
    --      p_opn_trd_amnt_in_ta_prm_crncy        Open Trade Amount In Trading Account Primary Currency
    --      p_opn_trd_mrgn_in_ta_prm_crncy        Open Trade Margin In Trading Account Primary Currency
    --      p_open_trd_last_modified_time         Open Trade Last Modified Time
    --      p_client_state_request_time           Client State Request Time
    --      p_client_state_quote_l1_bid           Client State Quote L1 Bid
    --      p_client_state_quote_l1_ask           Client State Quote L1 Ask
    --      p_client_state_quote_bid              Client State Quote Bid
    --      p_client_state_quote_ask              Client State Quote Ask
    --      p_open_trade_financing_ratio          Open Trade Financing Ratio
    --      p_open_trade_qntty_fx_rate_bid        Open Trade Quantity Fx Rate Bid
    --      p_open_trade_qntty_fx_rate_ask        Open Trade Quantity Fx Rate Ask
    --      p_is_late_deal                        Is Late Deal
    --      p_is_primary                          Is Primary
    --      p_controlled_order_type               Controlled Order Type
    --      p_is_trdng_enbld_fr_prdt_ignrd        Is Trading Enabled For Product Ignored
    --      p_is_trdng_pssbl_fr_ta_ignrd          Is Trading Possible For Trading Account Ignored
    --      p_is_clrng_done_unconditionlly        Is Clearing Done Unconditionaaly
    --      p_is_ta_sspn_fr_trd_rvsl_ignrd        Is Trading Account Suspended For Trade Revrsal Ignored
    --      p_use_custom_quote                    Use Custom Quote
    --      p_custom_quote_id                     Custom Quote Id
    --      p_custom_quote_bid                    Custom Quote Bid
    --      p_custom_quote_ask                    Custom Quote Ask
    --      p_custom_fx_rate_id                   Custom Fx Rate Id
    --      p_custom_fx_rate_bid                  Custom Fx Rate Bid
    --      p_custom_fx_rate_ask                  Custom Fx Rate Ask
    --      p_comment                             Comment
    --      p_trd_reversal_trd_id_to_rvrse        Trdae Reversal Trade Id To reverse
    --      p_trd_reversal_pl_reverse             Trade Reversal P1 Reverse
    --      p_is_mtchg_use_lfo_opn_trd_cpr        Is Matching Use Lfo Open Trade Cpr
    --      p_is_mtc_rslt_in_shrt_trd_igrd        Is Matching Result In Short Trade Ignored
    --      p_is_mtch_rslt_in_psn_inc_igrd        Is Matching Result In Position Inc Ignored
    --      p_is_mtch_trd_sz_grtr_max_igrd        Is Matching Trade Size Greater Max Ignored
    --      p_is_trdng_sspnd_fr_prdct_igrd        Is Trading Suspended For Prodcut Ignored
    --      p_is_fnncng_ratio_chck_ignrd          Is Finacing Ration Check Ignored
    --      p_is_product_generation_check         Is Product Generation Check
    --      p_point_multiplier                    Point Multiplier
    --      p_platform                            Platform
    --      p_order_type                          Order_type
    --      p_mm_account_id                       Market Marker Account ID
    --      p_trading_account_type                Trading Account Type(CUSTOMER/INTERNAL)
    --      p_margin_requirement                  Margin Requirement
    --      p_margin_type                         Margin Type
    --      p_parent_order_type                   Parent Order Type
    --      p_parent_is_limit_trailing            Parent Is Limit Trailing
    --      p_parent_limit_price                  Parent Limit Price
    --      p_parent_open_trade_price             Parent Open Trade Price
    --      p_parent_open_trade_time              Parent Open Trade Time
    --      p_parent_lmt_trlng_best_price         Parent Limit Trailing Best Price
    --      p_parent_limit_trlng_distance         Parent Limit Trailing Distance
    --      p_parent_requested_direction          Parent Requested Direction
    --      p_client_state_quote_id               Client State Quote Id
    --      p_triggering_quote_id                 Triggering Quote Id
    --      p_triggering_time                     Triggering Time
    --      p_triggering_execution_price          Triggering Execution Price
    --      p_triggering_level1_price             Triggering Level 1 Price
    --      p_boundary_price                      Boundary Price
    --      p_trading_scope                       Trading Scope
    --      p_allocation_order_id                 Allocation Order Id
    --      p_alloc_trading_risk_schema           Allocation Trading Risk Schema
    --      p_order_managed_info                  List of Managed Orders
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_order(p_user                                                      IN orders.created_by%TYPE,
                        p_effective_start_timestamp                                 IN orders.effective_start_timestamp%TYPE,
                        p_publish_time                                              IN orders.publish_time%TYPE,
                        p_event_time                                                IN orders.event_time%TYPE,
                        p_changed_character                                         IN orders.changed_character%TYPE,
                        p_changed_reason_code                                       IN orders.changed_reason_code%TYPE,
                        p_changed_reason_codifier                                   IN orders.changed_reason_codifier%TYPE,
                        p_order_id                                                  IN orders.order_id%TYPE,
                        p_version_number                                            IN orders.version_number%TYPE,
                        p_is_deleted                                                IN orders.is_deleted%TYPE,
                        p_creation_time                                             IN orders.creation_time%TYPE,
                        p_creation_identity_token                                   IN orders.creation_identity_token%TYPE,
                        p_crtn_on_bhlf_of_idntty_tkn                                IN orders.crtn_on_bhlf_of_idntty_tkn%TYPE,
                        p_update_time                                               IN orders.update_time%TYPE,
                        p_update_identity_token                                     IN orders.update_identity_token%TYPE,
                        p_updt_on_bhlf_of_idntty_tkn                                IN orders.updt_on_bhlf_of_idntty_tkn%TYPE,
                        p_product_instrument_code                                   IN orders.product_instrument_code%TYPE,
                        p_product_wrapper_code                                      IN orders.product_wrapper_code%TYPE,
                        p_feed_symbol                                               IN orders.product_feed_symbol%TYPE,
                        p_prophet_symbol                                            IN orders.product_prophet_symbol%TYPE,
                        p_mm_instrument_id                                          IN orders.mm_instrument_id%TYPE,
                        p_product_fractional_part_rat                               IN orders.product_fractional_part_ratio%TYPE,
                        p_is_crrncy_in_fractnal_parts                               IN orders.is_crrncy_in_fractional_parts%TYPE,
                        p_product_generation                                        IN orders.product_generation%TYPE,
                        p_product_point_multiplier                                  IN orders.product_point_multiplier%TYPE,
                        p_product_currency                                          IN orders.product_currency%TYPE,
                        p_trading_account_id                                        IN orders.trading_account_id%TYPE,
                        p_trading_account_codifier                                  IN orders.trading_account_codifier%TYPE,
                        p_trading_accnt_primary_curncy                              IN orders.trading_accnt_primary_currency%TYPE,
                        p_trading_account_function                                  IN orders.trading_account_function%TYPE,
                        p_channel_id                                                IN orders.channel_id%TYPE,
                        p_request_id                                                IN orders.request_id%TYPE,
                        p_session_id                                                IN orders.session_key%TYPE,
                        p_requested_direction                                       IN orders.requested_direction%TYPE,
                        p_requested_quantity                                        IN orders.requested_quantity%TYPE,
                        p_requested_valid_till                                      IN orders.requested_valid_till%TYPE,
                        p_customer_info                                             IN orders.custom_info%TYPE,
                        p_state                                                     IN orders.state%TYPE,
                        p_actual_valid_till                                         IN orders.actual_valid_till%TYPE,
                        p_close_time                                                IN orders.close_time%TYPE,
                        p_close_reason                                              IN orders.close_reason%TYPE,
                        p_quantity_designator                                       IN orders.quantity_designator%TYPE,
                        p_quantity_match                                            IN orders.quantity_match%TYPE,
                        p_limit_price                                               IN orders.limit_price%TYPE,
                        p_limit_price_condition                                     IN orders.limit_price_condition%TYPE,
                        p_is_limit_trailing                                         IN orders.is_limit_trailing%TYPE,
                        p_limit_trailing_distance                                   IN orders.limit_trailing_distance%TYPE,
                        p_limit_trailing_best_price                                 IN orders.limit_trailing_best_price%TYPE,
                        p_limit_traling_best_price_tme                              IN orders.limit_trailing_best_price_time%TYPE,
                        p_related_child_order_type                                  IN orders.related_child_order_type%TYPE,
                        p_related_parent_order_id                                   IN orders.related_parent_order_id%TYPE,
                        p_client_state_request_time                                 IN orders.client_state_request_time%TYPE,
                        p_client_state_quote_l1_bid                                 IN orders.client_state_quote_l1_bid%TYPE,
                        p_client_state_quote_l1_ask                                 IN orders.client_state_quote_l1_ask%TYPE,
                        p_client_state_quote_bid                                    IN orders.client_state_quote_bid%TYPE,
                        p_client_state_quote_ask                                    IN orders.client_state_quote_ask%TYPE,
                        p_is_late_deal                                              IN orders.is_late_deal%TYPE,
                        p_is_primary                                                IN orders.is_primary%TYPE,
                        p_controlled_order_type                                     IN orders.controlled_order_type%TYPE,
                        p_is_trdng_enbld_fr_prdt_ignrd                              IN orders.is_trdng_enbld_fr_prdct_ignrd%TYPE,
                        p_is_trdng_pssbl_fr_ta_ignrd                                IN orders.is_trdng_pssbl_fr_ta_ignrd%TYPE,
                        p_is_ta_sspn_fr_trd_rvsl_ignrd                              IN orders.is_ta_sspndd_fr_trd_rvsl_ignrd%TYPE,
                        p_use_custom_quote                                          IN orders.use_custom_quote%TYPE,
                        p_custom_quote_id                                           IN orders.custom_quote_id%TYPE,
                        p_custom_quote_bid                                          IN orders.custom_quote_bid%TYPE,
                        p_custom_quote_ask                                          IN orders.custom_quote_ask%TYPE,
                        p_custom_fx_rate_id                                         IN orders.custom_fx_rate_id%TYPE,
                        p_comment                                                   IN orders.cntrld_ordr_comment%TYPE,
                        p_trd_reversal_trd_id_to_rvrse                              IN orders.trd_reversal_trd_id_to_reverse%TYPE,
                        p_trd_reversal_pl_reverse                                   IN orders.trd_reversal_pl_reverse%TYPE,
                        p_is_mtchg_use_lfo_opn_trd_cpr                              IN orders.is_mtchng_use_lfo_opn_trd_cmpr%TYPE,
                        p_is_mtc_rslt_in_shrt_trd_igrd                              IN orders.is_mtch_rslt_in_shrt_trd_ignrd%TYPE,
                        p_is_mtch_rslt_in_psn_inc_igrd                              IN orders.is_mtch_rslt_in_pstn_inc_ignrd%TYPE,
                        p_is_order_sz_grtr_max_ignrd                              IN orders.is_order_sz_grtr_max_ignrd%TYPE,
                        p_is_trdng_sspnd_fr_prdct_igrd                              IN orders.is_trdng_sspndd_fr_prdct_ignrd%TYPE,
                        p_is_product_generation_check                               IN orders.is_product_generation_check%TYPE,
                        p_point_multiplier                                          IN orders.point_multiplier%TYPE,
                        p_platform                                                  IN orders.platform%TYPE,
                        p_order_type                                                IN orders.order_type%TYPE,
                        p_mm_account_id                                             IN orders.mm_account_id%TYPE,
                        p_trading_account_type                                      IN orders.trading_account_type%TYPE,
                        p_parent_order_type                                         IN orders.order_type%TYPE,
                        p_parent_is_limit_trailing                                  IN orders.is_limit_trailing%TYPE,
                        p_parent_limit_price                                        IN orders.limit_price%TYPE,
                        p_parent_open_trade_price                                   IN orders.open_trade_price%TYPE,
                        p_parent_open_trade_time                                    IN orders.open_trade_time%TYPE,
                        p_parent_lmt_trlng_best_price                               IN orders.limit_trailing_best_price%TYPE,
                        p_parent_limit_trlng_distance                               IN orders.limit_trailing_distance%TYPE,
                        p_parent_requested_direction                                IN orders.requested_direction%TYPE,
                        p_client_state_quote_id                                     IN orders.client_state_quote_id%TYPE,
                        p_triggering_quote_id                                       IN orders.triggering_quote_id%TYPE,
                        p_triggering_time                                           IN orders.triggering_time%TYPE,
                        p_triggering_execution_price                                IN orders.triggering_execution_price%TYPE,
                        p_triggering_level1_price                                   IN orders.triggering_level1_price%TYPE,
                        p_boundary_price                                            IN orders.boundary_price%TYPE,
                        p_rqustd_open_trade_to_close                                IN trade_id_tab,
                        p_triggering_side                                           IN orders.triggering_side%TYPE,
                        p_is_funds_check_ignored                                    IN orders.is_funds_check_ignored%TYPE,
                        p_custom_amount_fx_rate                                     IN orders.custom_amount_fx_rate%TYPE,
                        p_client_state_parameter_info                               IN orders.client_state_parameter_info%TYPE,
                        p_amnt_to_chrg_in_comm_ccy                                  IN orders.amnt_to_chrg_in_comm_ccy%TYPE,
                        p_reinstated_order_id                                       IN orders.reinstated_order_id%TYPE,
                        p_is_position_auto_rolled                                   IN orders.is_position_auto_rolled%TYPE,
                        p_rolled_order_id                                           IN orders.rolled_order_id%TYPE,
                        P_CLIENT_AMOUNT                                             IN ORDERS.CLIENT_AMOUNT%TYPE,
                        P_CHILD_ORDER_IDS                                           IN ORDER_ID_TAB,
                        p_parent_order_state                                        IN orders.parent_order_state%TYPE,
                        p_commission_currency                                       IN orders.Commission_Currency%TYPE,
                        p_triggering_price_offset_idx                               IN orders.Triggering_Price_Offset_Index%TYPE,
                        p_queued_quote_id                                           IN orders.Queued_Quote_Id%TYPE,
                        p_activation_quantity                                       IN orders.activation_quantity%TYPE,
                        p_gslo_premium_per_unit                                     IN orders.gslo_premium_per_unit%TYPE,
                        p_gslo_premium_fxreval_rate                                 IN orders.gslo_premium_fxreval_rate%TYPE,
                        p_gslo_premium_refund_percentg                              IN orders.gslo_premium_refund_percentage%TYPE,
                        p_execution_type                                            IN orders.Execution_Type%TYPE,
                        p_assigned_to                                               IN orders.Assigned_To%TYPE,
                        p_close_sub_reason                                          IN orders.close_sub_reason%TYPE,
                        p_trading_scope                                             IN orders.trading_scope%TYPE,
                        p_allocation_order_id                                       IN orders.allocation_order_id%TYPE,
                        p_order_managed_info                                        IN orders_managed_info_tab,
                        p_binary_type                                               IN orders.binary_type%TYPE,
                        p_settle_time                                               IN orders.settle_time%TYPE,
                        p_tenor                                                     IN orders.tenor%TYPE,
                        p_strike_price                                              IN orders.strike_price%TYPE,
                        p_strike_price_additional                                   IN orders.strike_price_additional%TYPE,
                        p_alloc_instrument_schema                                   IN orders.alloc_instrument_schema%TYPE,
                        p_guaranteed_limit_price                                    IN orders.guaranteed_limit_price%TYPE,
                        p_was_guaranteed_executed                                   IN orders.was_guaranteed_executed%TYPE,
                        p_source_order_id                                           IN orders.source_order_id%TYPE,
                        p_source_trade_id                                           IN orders.source_trade_id%TYPE,
                        p_client_order_id                                           IN orders.client_order_id%TYPE,
                        p_limit_distance                                            IN orders.limit_distance%TYPE,
                        p_guaranteed_limit_distance                                 IN orders.guaranteed_limit_distance%TYPE,
                        p_metatrader_order_type                                     IN orders.metatrader_order_type%TYPE,
                        p_client_limit_price                                        IN orders.client_limit_price%TYPE,
                        p_order_chngd_chrg_cmsn                                     IN orders.order_chngd_chrg_cmsn%TYPE,
                        p_order_chngd_prev_state                                    IN orders.order_chngd_prev_state%TYPE,
                        p_order_close_comment                                       IN order_close_comment_obj,
                        p_migrated_order_id                                         IN orders.migrated_order_id%TYPE,
                        p_client_party_id                                           IN orders.client_party_id%TYPE,
                        p_liquidation_id                                            IN orders.liquidation_id%TYPE,
                        p_is_give_up                                                IN orders.is_give_up%TYPE,
                        p_exclude_from_turnover_report                              IN orders.exclude_from_turnover_report%TYPE,                        
                        p_fx_trade_date                                             IN orders.fx_trade_date%TYPE,
                        p_value_date                                                IN orders.value_date%TYPE,
                        p_requested_quantity_currency                               IN orders.requested_quantity_currency%TYPE,
                        p_requested_quote_id                                        IN orders.requested_quote_id%TYPE,
                        p_fx_tenor                                                  IN orders.fx_tenor%TYPE,
                        p_client_state_swap_quote_id                                IN orders.client_state_swap_quote_id%TYPE
                        );

  -- ===================================================================================
  -- get_order_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of order_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform MMCFD or MMSB or NG
  --     p_order_time_from                 order time from - Time order was created at source
  --     p_order_time_to                   order time to - Time order was created at source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
  FUNCTION get_order_ids(p_platform         IN orders.platform%TYPE DEFAULT NULL,
                         p_order_time_from  IN orders.creation_time%TYPE DEFAULT TRUNC(SYSDATE),
                         p_order_time_to    IN orders.creation_time%TYPE DEFAULT TRUNC(SYSDATE)) RETURN SYS_REFCURSOR;

  -- ===================================================================================
  -- get_stubbed_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed cash account id's
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
  --     p_platform                       Cash Account Platform
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER,
                             p_platform     IN VARCHAR2) RETURN SYS_REFCURSOR;

  -- ===================================================================================
  -- cleanup_contingent_orders
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure is to delete the contingent orders history so that only 1 day worth of contingent orders are stored
  --     This procedure is scheduled using the dbms scheduler to clear the contingent orders at regular intervals
  --     This is done to restrict the size of data on contingent orders
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
  --     p_platform                       Cash Account Platform
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------

  PROCEDURE cleanup_contingent_orders;

    -- ===================================================================================
    -- get_closed_executed_order_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of order_id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_order_update_time_from         order update time from
    --     p_order_update_time_to           order update time to
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --      -20005    Invalid Platform Name. Expected Platform names are NG
    --      -20004    Dafault Exception
    -- -----------------------------------------------------------------------------------
 FUNCTION get_closed_executed_order_ids(p_order_update_time_from  IN orders.Update_Time%TYPE,
                                        p_order_update_time_to    IN orders.Update_Time%TYPE) RETURN SYS_REFCURSOR;

    -- ===================================================================================
    -- get_stoploss_order_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of order_id attributes BER-907
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_order_update_time_from         order update time from
    --     p_order_update_time_to           order update time to
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --      -20005    Invalid Platform Name. Expected Platform names are NG
    --      -20004    Dafault Exception
    -- -----------------------------------------------------------------------------------
 FUNCTION get_stoploss_order_ids(p_order_update_time_from  IN orders.Update_Time%TYPE,
                                 p_order_update_time_to    IN orders.Update_Time%TYPE) RETURN SYS_REFCURSOR;
END nrg_order;
/