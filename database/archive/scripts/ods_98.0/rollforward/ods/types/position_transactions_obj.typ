DROP TYPE position_transactions_tab FORCE;
DROP TYPE position_transactions_obj FORCE;

CREATE OR REPLACE TYPE bi_ods.position_transactions_obj
IS
  OBJECT
  (
    p_user                       VARCHAR2(50) ,
    effective_start_timestamp    TIMESTAMP(6) ,
    transaction_id               VARCHAR2(50) ,
    open_trade_id                VARCHAR2(50) ,
    trade_id                     VARCHAR2(50) ,
    transaction_booking_number   NUMBER ,
    platform                     VARCHAR2(10) ,
    publish_time                 TIMESTAMP(6) ,
    event_time                   TIMESTAMP(6) ,
    version_number               NUMBER ,
    is_deleted                   VARCHAR2(3) ,
    creation_time                TIMESTAMP(6) ,
    creation_identity_token      NUMBER ,
    crtn_on_bhlf_of_idntty_tkn   NUMBER ,
    update_time                  TIMESTAMP(6) ,
    update_identity_token        NUMBER ,
    updt_on_bhlf_of_idntty_tkn   NUMBER ,
    session_key                  VARCHAR2(100) ,
    trading_account_id           NUMBER ,
    trading_account_type         VARCHAR2(20) ,
    trading_account_function     VARCHAR2(10) ,
    trading_account_codifier     VARCHAR2(20) ,
    trdng_accnt_primary_currency VARCHAR2(3) ,
    order_id                     VARCHAR2(50) ,
    product_instrument_code      VARCHAR2(50) ,
    product_wrapper_code         VARCHAR2(50) ,
    product_generation           NUMBER ,
    product_point_multiplier     NUMBER ,
    product_currency             VARCHAR2(20) ,
    product_frctnl_part_ratio    NUMBER ,
    iscurrencyinfractionalparts  VARCHAR2(3) ,
    channel_id                   VARCHAR2(50) ,
    request_id                   VARCHAR2(50) ,
    transaction_time             TIMESTAMP(6) ,
    transaction_type             VARCHAR2(50) ,
    transaction_refcodifier      VARCHAR2(50) ,
    transaction_refcode          VARCHAR2(50) ,
    is_automatically_rolled      VARCHAR2(3) ,
    position_direction           VARCHAR2(4) ,
    open_trade_quantity          NUMBER ,
    quantity_designator          VARCHAR2(15) ,
    open_trade_amount            NUMBER ,
    open_trade_amount_currency   VARCHAR2(3) ,
    open_trade_amount_in_tapc    NUMBER ,
    open_trade_price             NUMBER ,
    opening_trade_amount_fx_rate NUMBER ,
    open_trade_time              TIMESTAMP(6) ,
    record_source                VARCHAR2(20) ,
    rolled_open_trade_id         VARCHAR2(50) ,
    opening_trade_app_to_units   NUMBER ,
    load_latest_positions        VARCHAR2(20) ,
    execution_type               VARCHAR2(60) ,
    opening_trade_instrmnt_price NUMBER,
    trading_scope                VARCHAR2(100),
    binary_type                  VARCHAR2(50) ,
    settle_time                  TIMESTAMP(6) ,
    tenor                        VARCHAR2(50) ,
    strike_price                 NUMBER ,
    strike_price_additional      NUMBER ,
    tenor_start_time             TIMESTAMP(6) ,
    binary_result                VARCHAR2(4) ,
    alloc_instrument_schema      VARCHAR2(50),
    open_trade_instrmnt_amount   NUMBER,
    forced_margin_fx_rate        NUMBER,
    opn_accrd_trnvr_in_accnt_ccy NUMBER,
    is_pair_ccy_in_frctnl_prts   VARCHAR2(3),
    value_date                   TIMESTAMP(6),
    pair_currency                VARCHAR2(3),
    primary_currency             VARCHAR2(3),
    secondary_currency           VARCHAR2(3),
    primary_amount               NUMBER,
    secondary_amount             NUMBER  
  );
/