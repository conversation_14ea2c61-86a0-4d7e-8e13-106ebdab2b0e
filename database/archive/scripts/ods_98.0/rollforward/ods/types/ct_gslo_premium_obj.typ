CREATE OR REPLACE TYPE bi_ods.ct_gslo_premium_obj
IS OBJECT
 (trading_account_id            NUMBER,
  trading_account_currency      VARCHAR2(3),
  instrument_code               VARCHAR2(50),
  product_wrapper_code          VARCHAR2(10),
  premium_currency              VARCHAR2(3),
  premium_per_unit              NUMBER,
  open_trade_order_id           VARCHAR2(50),
  opening_trade_id              VARCHAR2(50),
  premium_quantity              NUMBER,
  gsl_order_id                  VARCHAR2(50),
  premium_in_premium_currency   NUMBER,
  fx_reval_rate                 NUMBER,
  premium_booking_number        NUMBER,
  premium_in_account_currency   NUMBER,
  premium_refund_rate           NUMBER,
  closing_trade_id              VARCHAR2(50),
  refund_quantity               NUMBER,
  closing_trade_order_id        VARCHAR2(50),
  execution_type                VARCHAR2(50)
  );
/