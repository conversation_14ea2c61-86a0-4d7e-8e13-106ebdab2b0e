DROP TYPE historic_account_value_tab FORCE;
DROP TYPE historic_account_value_obj FORCE;

CREATE OR REPLACE TYPE bi_ods.historic_account_value_obj IS OBJECT
 (trading_account_id            NUMBER,
  trading_account_type          VARCHAR2(50),
  trading_account_function      VARCHAR2(50),
  trading_account_primary_ccy   VARCHAR2(3),
  cash_balance                  NUMBER,
  total_margin                  NUMBER,
  standard_liquidation_amount   NUMBER,
  standard_reset_level          NUMBER,
  is_subsequent_evluatn_pndng   VARCHAR2(3),
  prdctd_tm_dpndnt_lqdtn_tm     NUMBER,
  standard_margin               NUMBER,
  prime_margin                  NUMBER,
  prime_liquidation_amount      NUMBER,
  prime_reset_level              NUMBER,
  withdrawable_amount            NUMBER,
  is_valid                      VARCHAR2(3),
  primary_cash_balance          NUMBER,
  secondary_balances            acnt_vl_snp_scndry_blncs_tab,
  accnt_mon_upnl_in_prmry_ccy    NUMBER,
  accnt_mon_equity              NUMBER,
  accnt_mon_free_equity          NUMBER,
  customer_upnl_in_prmry_ccy    NUMBER,
  customer_equity                NUMBER,
  customer_free_equity          NUMBER,
  instrument_schema_code        VARCHAR2(50),
  closeout_schema_code          VARCHAR2(50),
  cash_accounting_schema_code    VARCHAR2(50),
  payment_schema_code            VARCHAR2(50),
  commission_schema_code        VARCHAR2(50),
  carrying_costs_schema_code    VARCHAR2(50),
  crryng_csts_offst_schm_cd     VARCHAR2(50),
  price_feed_schema_code        VARCHAR2(50),
  independent_margin_amount     NUMBER,
  standard_liquidation_level    NUMBER,
  prime_liquidation_level       NUMBER,
  standard_level_type           VARCHAR2(50),
  prime_level_type              VARCHAR2(50),
  cmc_upnl_in_prmry_ccy          NUMBER,
  unrl_txbl_profit_in_prmry_ccy NUMBER,
  reserved_tax_in_prmry_ccy     NUMBER,
  margin_coverage               NUMBER,
  accrued_capital_gains         NUMBER,
  used_tax_free_allowance       NUMBER,
  loss_offset_account_balance   NUMBER,
  accrd_capital_gains_tax_amnt  NUMBER,
  accrd_reunfctn_srchrg_tx_amt  NUMBER,
  accrued_church_tax_amount     NUMBER
  );
/