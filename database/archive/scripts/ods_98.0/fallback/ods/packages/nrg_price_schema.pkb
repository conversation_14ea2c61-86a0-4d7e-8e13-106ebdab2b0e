CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_price_schema
AS
-- ===================================================================================
-- NRG_PRICE_SCHEMA
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     This package encapsulates NRG (Near Realtime Gatherer)
--
--     Management of the price_schema model
--
-- -----------------------------------------------------------------------------------
--
-- Notes:
-- ------
--
--     Run as BI_ODS
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--     -20001    Entity already exists
--
-- Modifications:
-- --------------
--
--   Date         Modified By         Vers    Action
--   ----------   ---------------     -----   ----------------------------------------
--   17/06/2016   Sakina Kinkhabwala  1.0     Creation
--   02/05/2019   Patrick Dinwiddy    1.1     Tidy up existing code and new price feed schema entities JCS-10576
--   10/09/2019   Patrick Dinwiddy    1.2     Refactor history handling process and new price feed schema entities JCS-11142
--   11/11/2019   Patrick Dinwiddy    1.3     JCS-11568 is_eod logic change
--
-- ===================================================================================
--
--
-- ===================================================================================
-- PACKAGE CONSTANTS
-- ===================================================================================

gc_version            CONSTANT VARCHAR2(3) := '1.3';
gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
gc_true               CONSTANT PLS_INTEGER := 1;
gc_false              CONSTANT PLS_INTEGER := 0;
gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

-- ===================================================================================
--  PUBLIC MODULES
-- ===================================================================================
--
--
--
-- ===================================================================================
-- version
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Function to retrieve the version of the package
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--
-- Return:
-- -------
--
--     Returns a VARCHAR2 representing the version of the package
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--
-- -----------------------------------------------------------------------------------

  FUNCTION version
    RETURN VARCHAR2 DETERMINISTIC
  IS
  BEGIN
    logger.logger.set_module('version');
  RETURN gc_version;
  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    RAISE;
  END version;

--
-- ===================================================================================
-- PRIVATE MODULES
-- ===================================================================================
-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for PATTERN_ALERT_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_price_schemas          This is the old version of the price_schemas data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_history  (p_tab_old_price_schemas       IN price_schemas%ROWTYPE,
                         p_effective_end_timestamp      IN price_schemas.effective_start_timestamp%TYPE,
                         p_action                       IN price_schemas_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO price_schemas_h (
                price_schema_name
               ,logical_load_timestamp
               ,created_by
               ,create_timestamp
               ,updated_by
               ,update_timestamp
               ,effective_start_timestamp
               ,effective_end_timestamp
               ,default_band
               ,is_deleted
               ,action
               ,action_timestamp)
        VALUES (p_tab_old_price_schemas.price_schema_name,
                p_tab_old_price_schemas.logical_load_timestamp,
                p_tab_old_price_schemas.created_by,
                p_tab_old_price_schemas.create_timestamp,
                p_tab_old_price_schemas.updated_by,
                p_tab_old_price_schemas.update_timestamp,
                p_tab_old_price_schemas.effective_start_timestamp,
                p_effective_end_timestamp,
                p_tab_old_price_schemas.default_band,
                p_tab_old_price_schemas.is_deleted,
                p_action,
                SYSTIMESTAMP);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE price_schemas_h
         SET updated_by                = p_tab_old_price_schemas.updated_by,
             update_timestamp          = p_tab_old_price_schemas.update_timestamp,
             logical_load_timestamp    = p_tab_old_price_schemas.logical_load_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             default_band              = p_tab_old_price_schemas.default_band,
             is_deleted                = p_tab_old_price_schemas.is_deleted,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP
       WHERE price_schema_name         = p_tab_old_price_schemas.price_schema_name
         AND effective_start_timestamp = p_tab_old_price_schemas.effective_start_timestamp
         AND (nrg_common.has_value_changed(default_band, p_tab_old_price_schemas.default_band) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_tab_old_price_schemas.is_deleted) = 1);

  END put_history;

-- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PRICE_SCHEMA_ITEMS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --    p_price_schema_item_tab
  --    p_effective_end_timestamp
--      p_price_schema_name
  --    p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_price_schema_item  IN price_schema_items%ROWTYPE,
                        p_effective_end_timestamp    IN price_schema_items.effective_start_timestamp%TYPE,
                        p_price_schema_name          IN price_schema_items.price_schema_name%TYPE,
                        p_action                     IN price_schema_items_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO price_schema_items_h
               (price_schema_name
               ,logical_load_timestamp
               ,created_by
               ,create_timestamp
               ,updated_by
               ,update_timestamp
               ,effective_start_timestamp
               ,effective_end_timestamp
               ,action
               ,action_timestamp
               ,override_type
               ,code
               ,price_band)
        VALUES (p_tab_old_price_schema_item.price_schema_name,
                p_tab_old_price_schema_item.logical_load_timestamp,
                p_tab_old_price_schema_item.created_by,
                p_tab_old_price_schema_item.create_timestamp,
                p_tab_old_price_schema_item.updated_by,
                p_tab_old_price_schema_item.update_timestamp,
                p_tab_old_price_schema_item.effective_start_timestamp,
                p_effective_end_timestamp,
                p_action,
                SYSTIMESTAMP,
                p_tab_old_price_schema_item.override_type,
                p_tab_old_price_schema_item.code,
                p_tab_old_price_schema_item.price_band);

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN

      UPDATE price_schema_items_h
         SET logical_load_timestamp    = p_tab_old_price_schema_item.logical_load_timestamp,
             updated_by                = p_tab_old_price_schema_item.updated_by,
             update_timestamp          = p_tab_old_price_schema_item.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP ,
             price_band                = p_tab_old_price_schema_item.price_band
       WHERE price_schema_name         = p_tab_old_price_schema_item.price_schema_name
         AND override_type             = p_tab_old_price_schema_item.override_type
         AND code                      = p_tab_old_price_schema_item.code
         AND effective_start_timestamp = p_tab_old_price_schema_item.effective_start_timestamp;

  END put_history;

-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for PATTERN_ALERT_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_price_schemas          This is the old version of the price_schemas data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_history (p_tab_old_price_feed_schemas   IN price_feed_schemas_h%ROWTYPE,
                         p_action                       IN price_feed_schemas_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO price_feed_schemas_h (
                price_feed_schema_name
               ,logical_load_timestamp
               ,created_by
               ,create_timestamp
               ,updated_by
               ,update_timestamp
               ,effective_start_timestamp
               ,effective_end_timestamp
               ,default_band
               ,default_price_stream_type
               ,default_adjust_id
               ,default_adjust_alias
               ,default_band_alias
               ,is_deleted
               ,dflt_price_stream_type_alias
               ,action
               ,action_timestamp)
        VALUES (p_tab_old_price_feed_schemas.price_feed_schema_name,
                p_tab_old_price_feed_schemas.logical_load_timestamp,
                p_tab_old_price_feed_schemas.created_by,
                p_tab_old_price_feed_schemas.create_timestamp,
                p_tab_old_price_feed_schemas.updated_by,
                p_tab_old_price_feed_schemas.update_timestamp,
                p_tab_old_price_feed_schemas.effective_start_timestamp,
                p_tab_old_price_feed_schemas.effective_end_timestamp,
                p_tab_old_price_feed_schemas.default_band,
                p_tab_old_price_feed_schemas.default_price_stream_type,
                p_tab_old_price_feed_schemas.default_adjust_id,
                p_tab_old_price_feed_schemas.default_adjust_alias,
                p_tab_old_price_feed_schemas.default_band_alias,
                p_tab_old_price_feed_schemas.is_deleted,
                p_tab_old_price_feed_schemas.dflt_price_stream_type_alias,
                p_action,
                SYSTIMESTAMP);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE price_feed_schemas_h
         SET updated_by                = p_tab_old_price_feed_schemas.updated_by,
             update_timestamp          = p_tab_old_price_feed_schemas.update_timestamp,
             logical_load_timestamp    = p_tab_old_price_feed_schemas.logical_load_timestamp,
             effective_end_timestamp   = p_tab_old_price_feed_schemas.effective_end_timestamp,
             default_band              = p_tab_old_price_feed_schemas.default_band,
             default_price_stream_type = p_tab_old_price_feed_schemas.default_price_stream_type,
             default_adjust_id         = p_tab_old_price_feed_schemas.default_adjust_id,
             default_adjust_alias      = p_tab_old_price_feed_schemas.default_adjust_alias,
             default_band_alias        = p_tab_old_price_feed_schemas.default_band_alias,
             dflt_price_stream_type_alias  = p_tab_old_price_feed_schemas.dflt_price_stream_type_alias,
             is_deleted                = p_tab_old_price_feed_schemas.is_deleted,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP
       WHERE price_feed_schema_name    = p_tab_old_price_feed_schemas.price_feed_schema_name
         AND effective_start_timestamp = p_tab_old_price_feed_schemas.effective_start_timestamp
         AND (nrg_common.has_value_changed(default_band, p_tab_old_price_feed_schemas.default_band) = 1 OR
              nrg_common.has_value_changed(default_price_stream_type, p_tab_old_price_feed_schemas.default_price_stream_type) = 1 OR
              nrg_common.has_value_changed(default_adjust_id, p_tab_old_price_feed_schemas.default_adjust_id) = 1 OR
              nrg_common.has_value_changed(default_adjust_alias, p_tab_old_price_feed_schemas.default_adjust_alias) = 1 OR
              nrg_common.has_value_changed(default_band_alias, p_tab_old_price_feed_schemas.default_band_alias) = 1 OR
              nrg_common.has_value_changed(dflt_price_stream_type_alias, p_tab_old_price_feed_schemas.dflt_price_stream_type_alias) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_tab_old_price_feed_schemas.is_deleted) = 1);

  END put_history;

-- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for price_feed_schema_ITEMS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --    p_price_feed_schema_item_tab
  --    p_effective_end_timestamp
--      p_price_feed_schema_name
  --    p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_price_feed_schema_it    IN price_feed_schema_items_h%ROWTYPE,
                        p_action                          IN price_feed_schema_items_h.action%TYPE)
  IS
  BEGIN

    INSERT INTO price_feed_schema_items_h
               (price_feed_schema_name
               ,logical_load_timestamp
               ,created_by
               ,create_timestamp
               ,updated_by
               ,update_timestamp
               ,effective_start_timestamp
               ,effective_end_timestamp
               ,action
               ,action_timestamp
               ,override_type
               ,code
               ,price_band
               ,price_stream_type
               ,price_adjust_id
               ,price_adjust_alias
               ,price_band_alias
               ,price_stream_type_alias)
        VALUES (p_tab_old_price_feed_schema_it.price_feed_schema_name,
                p_tab_old_price_feed_schema_it.logical_load_timestamp,
                p_tab_old_price_feed_schema_it.created_by,
                p_tab_old_price_feed_schema_it.create_timestamp,
                p_tab_old_price_feed_schema_it.updated_by,
                p_tab_old_price_feed_schema_it.update_timestamp,
                p_tab_old_price_feed_schema_it.effective_start_timestamp,
                p_tab_old_price_feed_schema_it.effective_end_timestamp,
                p_action,
                SYSTIMESTAMP,
                p_tab_old_price_feed_schema_it.override_type,
                p_tab_old_price_feed_schema_it.code,
                p_tab_old_price_feed_schema_it.price_band,
                p_tab_old_price_feed_schema_it.price_stream_type,
                p_tab_old_price_feed_schema_it.price_adjust_id,
                p_tab_old_price_feed_schema_it.price_adjust_alias,
                p_tab_old_price_feed_schema_it.price_band_alias,
                p_tab_old_price_feed_schema_it.price_stream_type_alias);

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN

      UPDATE price_feed_schema_items_h
         SET logical_load_timestamp    = p_tab_old_price_feed_schema_it.logical_load_timestamp,
             updated_by                = p_tab_old_price_feed_schema_it.updated_by,
             update_timestamp          = p_tab_old_price_feed_schema_it.update_timestamp,
             effective_end_timestamp   = p_tab_old_price_feed_schema_it.effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             price_band                = p_tab_old_price_feed_schema_it.price_band,
             price_stream_type         = p_tab_old_price_feed_schema_it.price_stream_type,
             price_adjust_id           = p_tab_old_price_feed_schema_it.price_adjust_id,
             price_adjust_alias        = p_tab_old_price_feed_schema_it.price_adjust_alias,
             price_band_alias          = p_tab_old_price_feed_schema_it.price_band_alias,
             price_stream_type_alias   = p_tab_old_price_feed_schema_it.price_stream_type_alias
       WHERE price_feed_schema_name    = p_tab_old_price_feed_schema_it.price_feed_schema_name
         AND override_type             = p_tab_old_price_feed_schema_it.override_type
         AND code                      = p_tab_old_price_feed_schema_it.code
         AND effective_start_timestamp = p_tab_old_price_feed_schema_it.effective_start_timestamp;

  END put_history;

-- ===================================================================================
  -- put_price_schema_item
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_price_schema_item
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PRICE_SCHEMA_ITEMS
  --     PRICE_SCHEMA_ITEMS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_price_schema_name
  --     p_price_schema_item_tab
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_price_schema_item (p_user                       IN price_schema_items.created_by%TYPE,
                                   p_logical_load_timestamp     IN price_schema_items.logical_load_timestamp%TYPE,
                                   p_effective_start_timestamp  IN price_schema_items.effective_start_timestamp%TYPE,
                                   p_price_schema_name          IN price_schema_items.price_schema_name%TYPE,
                                   p_price_schema_item_tab      IN price_schema_item_tab)
  IS
  TYPE ltype_price_schema_item_del IS TABLE OF price_schema_items%ROWTYPE;
        ltab_price_schema_item_del ltype_price_schema_item_del;

  BEGIN
    -------------------------
    --- Select for deleting a row for given p_price_schema_name for if it does not exist in a new msg
    -------------------------
    BEGIN

      SELECT old_version.price_schema_name
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.override_type
            ,old_version.code
            ,old_version.price_band
        BULK COLLECT INTO ltab_price_schema_item_del
        FROM price_schema_items old_version
       WHERE old_version.price_schema_name=p_price_schema_name
         AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_price_schema_item_tab AS price_schema_item_tab)) new_version
                          WHERE new_version.override_type = old_version.override_type
                            AND new_version.code          = old_version.code)
         FOR UPDATE OF old_version.price_schema_name;

    FOR l_vcount IN 1..ltab_price_schema_item_del.COUNT
    LOOP

      put_history(p_tab_old_price_schema_item     => ltab_price_schema_item_del(l_vcount),
                  p_effective_end_timestamp       => p_effective_start_timestamp,
                  p_price_schema_name             => p_price_schema_name,
                  p_action                        => 'D');
    END LOOP;

    FORALL l_vcount IN 1..ltab_price_schema_item_del.COUNT

      DELETE FROM price_schema_items
       WHERE price_schema_name = p_price_schema_name
         AND override_type     = ltab_price_schema_item_del(l_vcount).override_type
         AND code              = ltab_price_schema_item_del(l_vcount).code
         AND price_band        = ltab_price_schema_item_del(l_vcount).price_band;

      ltab_price_schema_item_del.delete();

    EXCEPTION WHEN NO_DATA_FOUND THEN
      NULL;
    END;

  -------------------------
    --- Select for updating a  row for given p_price_schema_name for if price_band value has changed in a new msg
    -------------------------
    BEGIN

      SELECT old_version.price_schema_name
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.override_type
            ,old_version.code
            ,old_version.price_band
      BULK COLLECT INTO ltab_price_schema_item_del
      FROM (SELECT override_type,code,
                   price_band
              FROM TABLE(CAST(p_price_schema_item_tab AS price_schema_item_tab)))new_version,
            price_schema_items old_version
     WHERE old_version.price_schema_name = p_price_schema_name
       AND old_version.override_type     = new_version.override_type
       AND old_version.code              = new_version.code
       AND (nrg_common.has_value_changed(new_version.price_band, old_version.price_band) = 1);

    FOR l_vcount IN 1..ltab_price_schema_item_del.COUNT
      LOOP

        put_history(p_tab_old_price_schema_item     => ltab_price_schema_item_del(l_vcount),
                    p_effective_end_timestamp       => p_effective_start_timestamp,
                    p_price_schema_name             => p_price_schema_name,
                    p_action                         => 'U');
    END LOOP;

    ltab_price_schema_item_del.delete();

    EXCEPTION
    WHEN NO_DATA_FOUND  THEN
      NULL;
    END;

    MERGE INTO price_schema_items old_version USING
     (SELECT override_type,code,price_band
        FROM TABLE(CAST(p_price_schema_item_tab AS price_schema_item_tab))) new_version
      ON (old_version.price_schema_name    = p_price_schema_name
      AND old_version.override_type        = new_version.override_type
      AND old_version.code                 = new_version.code
      )
    WHEN MATCHED THEN UPDATE
      SET logical_load_timestamp      = p_logical_load_timestamp
         ,updated_by                  = p_user
         ,update_timestamp            = SYSTIMESTAMP
         ,effective_start_timestamp   = p_effective_start_timestamp
         ,price_band                  = new_version.price_band
    WHERE p_price_schema_name         = old_version.price_schema_name
      AND new_version.override_type   = old_version.override_type
      AND new_version.code            = old_version.code
      AND (nrg_common.has_value_changed(new_version.price_band, old_version.price_band) = 1 )
    WHEN NOT MATCHED THEN INSERT
           (price_schema_name
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,override_type
           ,code
           ,price_band)
    VALUES (p_price_schema_name
           ,p_logical_load_timestamp
           ,p_user
           ,systimestamp
           ,p_user
           ,systimestamp
           ,p_effective_start_timestamp
           ,new_version.override_type
           ,new_version.code
           ,new_version.price_band
           );

    logger.logger.set_module(NULL);

  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_price_schema_item;

-- ===================================================================================
-- put_price_schemas
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put price_schema
--
-- Notes:
-- ------
--
--     Tables potentially populated:


--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------

--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_price_schemas (p_price_schemas IN price_schemas_tab)
    IS
    BEGIN
     FOR i IN (SELECT * FROM TABLE(CAST(p_price_schemas AS price_schemas_tab)))
       LOOP
         put_price_schema(p_price_schema_name          => i.price_schema_name
                         ,p_user                       => i.p_user
                         ,p_effective_start_timestamp  => i.effective_start_timestamp
                         ,p_default_band               => i.default_band
                         ,p_price_schema_item_tab      => i.price_schema_item
                         ,p_is_deleted                 => i.is_deleted
                         ,p_is_request_response        => i.is_request_response
                         ,p_price_schema_tab           => p_price_schemas
                        );
    END LOOP;
  END;



-- ===================================================================================
-- put_price_schema
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put price_schema
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--     PRICE_SCHEMAS
--    PRICE_SCHEMAS_H

--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--    p_user
--     p_effective_start_timestamp
--     p_price_schema_name
--     p_creation_time
--     p_last_modified_time
--     p_creator
--     p_last_modifier
--     p_default_band
--    p_price_schema_item_tab
--     p_is_deleted

--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------
  PROCEDURE put_price_schema(p_price_schema_name          IN price_schemas.price_schema_name%TYPE,
                             p_user                       IN price_schemas.created_by%TYPE,
                             p_effective_start_timestamp  IN price_schemas.effective_start_timestamp%TYPE,
                             p_default_band               IN price_schemas.default_band%TYPE,
                             p_price_schema_item_tab      IN price_schema_item_tab,
                             p_is_deleted                 IN price_schemas.is_deleted%TYPE,
                             p_is_request_response        IN VARCHAR2,
                             p_price_schema_tab           IN price_schemas_tab
                             )
  IS

    TYPE ltype_old_price_schemas IS TABLE OF price_schemas%ROWTYPE;
          ltab_old_price_schemas_del ltype_old_price_schemas;

    TYPE ltype_price_schema_item_del IS TABLE OF price_schema_items%ROWTYPE;
          ltab_price_schema_item_del ltype_price_schema_item_del;

    lv_logical_load_timestamp    price_schemas.logical_load_timestamp%TYPE;
    ltab_old_price_schemas     price_schemas%ROWTYPE;

  BEGIN

     logger.logger.set_module('put_price_schema');
     lv_logical_load_timestamp := SYSTIMESTAMP;

  --
  -- IF the message is a pub/sub detect the changes between source and ODS price schema ,if changes are found update ODS table and push the old record to history table
  --

    IF p_is_request_response = 'NO' THEN

      BEGIN
        SELECT old_version.price_schema_name
              ,old_version.logical_load_timestamp
              ,old_version.created_by
              ,old_version.create_timestamp
              ,old_version.updated_by
              ,old_version.update_timestamp
              ,old_version.effective_start_timestamp
              ,old_version.default_band
              ,old_version.is_deleted
        INTO ltab_old_price_schemas
        FROM (SELECT p_price_schema_name price_schema_name,
                     p_default_band      default_band,
                     p_is_deleted        is_deleted
                FROM dual) new_version,
             price_schemas old_version
       WHERE new_version.price_schema_name=old_version.price_schema_name
         AND (nrg_common.has_value_changed(new_version.default_band, old_version.default_band)= 1 OR
              nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted)= 1 )
         FOR UPDATE OF old_version.price_schema_name;

        put_history(p_tab_old_price_schemas        => ltab_old_price_schemas,
              p_effective_end_timestamp      => p_effective_start_timestamp,
              p_action                       => 'U');

      EXCEPTION
        WHEN NO_DATA_FOUND  THEN
          NULL;
      END;

    --
    -- If message is a request reponse check if the data in ODS table is same as data coming from source,if at source the record has been deleted, delete the same in ODS price schema and price schema item tables
    --
    ELSE

      BEGIN
      --
      --select the records from price_schema which are not coming as part of request/resp snapshot
      --
        SELECT old_version.price_schema_name
              ,old_version.logical_load_timestamp
              ,old_version.created_by
              ,old_version.create_timestamp
              ,old_version.updated_by
              ,old_version.update_timestamp
              ,old_version.effective_start_timestamp
              ,old_version.default_band
              ,old_version.is_deleted
          BULK COLLECT INTO ltab_old_price_schemas_del
          FROM price_schemas old_version
         WHERE NOT EXISTS (SELECT 1
                             FROM TABLE(CAST(p_price_schema_tab AS price_schemas_tab)) new_version
                            WHERE new_version.price_schema_name = old_version.price_schema_name)
           FOR UPDATE OF old_version.price_schema_name;
        --
        --select the records from price_schema_item which are not coming as part of request/resp snapshot
        --
        SELECT old_version.price_schema_name
              ,old_version.logical_load_timestamp
              ,old_version.created_by
              ,old_version.create_timestamp
              ,old_version.updated_by
              ,old_version.update_timestamp
              ,old_version.effective_start_timestamp
              ,old_version.override_type
              ,old_version.code
              ,old_version.price_band
          BULK COLLECT INTO ltab_price_schema_item_del
          FROM price_schema_items  old_version
         WHERE NOT EXISTS (SELECT 1
                             FROM TABLE(CAST(p_price_schema_tab AS price_schemas_tab)) new_version
                            WHERE new_version.price_schema_name = old_version.price_schema_name);

        --
        --delete child records (price_schema_item) and update the history table
        --

        FOR l_vcount IN 1..ltab_price_schema_item_del.COUNT
        LOOP

          put_history(p_tab_old_price_schema_item   => ltab_price_schema_item_del(l_vcount),
                      p_effective_end_timestamp     => p_effective_start_timestamp,
                      p_price_schema_name           => p_price_schema_name,
                      p_action                      => 'D');

        END LOOP;

        FORALL l_vcount IN 1..ltab_old_price_schemas_del.COUNT

          DELETE FROM price_schema_items
          WHERE price_schema_name = ltab_old_price_schemas_del(l_vcount).price_schema_name;

        --
        --delete parent records (price_schema) and update the history table
        --

        FOR l_vcount IN 1..ltab_old_price_schemas_del.COUNT
        LOOP

          put_history(p_tab_old_price_schemas       => ltab_old_price_schemas_del(l_vcount),
                      p_effective_end_timestamp     => p_effective_start_timestamp,
                      p_action                      => 'D');

        END LOOP;

        FORALL l_vcount IN 1..ltab_old_price_schemas_del.COUNT

          DELETE FROM price_schemas
           WHERE price_schema_name = ltab_old_price_schemas_del(l_vcount).price_schema_name;

          ltab_old_price_schemas_del.delete();
          ltab_price_schema_item_del.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;

    END IF;

  --
  -- Insert/Update the new data
  --
    MERGE INTO price_schemas old_version USING
       (SELECT p_price_schema_name price_schema_name,
               p_default_band      default_band,
               p_is_deleted        is_deleted
          FROM dual) new_version
    ON (old_version.price_schema_name = new_version.price_schema_name)
    WHEN MATCHED THEN UPDATE
        SET logical_load_timestamp    = lv_logical_load_timestamp
           ,updated_by                = p_user
           ,update_timestamp          = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           ,default_band              = p_default_band
           ,is_deleted                = p_is_deleted
    WHERE new_version.price_schema_name = old_version.price_schema_name
      AND (nrg_common.has_value_changed(new_version.default_band, old_version.default_band) = 1 OR
           nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 )
    WHEN NOT MATCHED THEN INSERT
           (price_schema_name,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            default_band,
            is_deleted)
    VALUES (new_version.price_schema_name
           ,lv_logical_load_timestamp
           ,p_user
           ,systimestamp
           ,p_user
           ,systimestamp
           ,p_effective_start_timestamp
           ,new_version.default_band
           ,new_version.is_deleted);

  --------------------
  --- PUT_PRICE_SCHEMA_ITEM
  --------------------

      put_price_schema_item (p_price_schema_name         => p_price_schema_name,
                             p_user                      => p_user,
                             p_logical_load_timestamp    => lv_logical_load_timestamp,
                             p_effective_start_timestamp => p_effective_start_timestamp,
                             p_price_schema_item_tab     => p_price_schema_item_tab);

    logger.logger.set_module(NULL);

  EXCEPTION
  WHEN DUP_VAL_ON_INDEX THEN
    NULL;
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END put_price_schema;

-- ===================================================================================
  -- put_price_feed_schema_item
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_price_feed_schema_item
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     price_feed_schema_ITEMS
  --     price_feed_schema_ITEMS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_price_feed_schema_name
  --     p_price_feed_schema_item_tab
  --
  --------------------------------------------------------------------------------------
/*
  PROCEDURE put_price_feed_schema_item (p_user                       IN price_feed_schema_items.created_by%TYPE,
                                        p_logical_load_timestamp     IN price_feed_schema_items.logical_load_timestamp%TYPE,
                                        p_effective_start_timestamp  IN price_feed_schema_items.effective_start_timestamp%TYPE,
                                        p_price_feed_schema_name     IN price_feed_schema_items.price_feed_schema_name%TYPE,
                                        p_override_type              IN price_feed_schema_items.override_type%TYPE,
                                        p_code                       IN price_feed_schema_items.code%TYPE,
                                        p_price_band                 IN price_feed_schema_items.price_band%TYPE,
                                        p_price_stream_type          IN price_feed_schema_items.price_stream_type%TYPE,
                                        p_price_adjust_id            IN price_feed_schema_items.price_adjust_id%TYPE,
                                        p_price_adjust_alias         IN price_feed_schema_items.price_adjust_alias%TYPE,
                                        p_price_band_alias           IN price_feed_schema_items.price_band_alias%TYPE,
                                        p_price_stream_type_alias    IN price_feed_schema_items.price_stream_type_alias%TYPE,
                                        p_price_feed_schema_item_tab IN price_feed_schema_items_tab)
  IS
    TYPE ltype_price_feed_schema_it_del      IS TABLE OF price_feed_schema_items_h%ROWTYPE;
         ltab_price_feed_schema_it_del       ltype_price_feed_schema_it_del;

    TYPE ltype_price_feed_schema_it_h          IS TABLE OF price_feed_schema_items_h%ROWTYPE;
         ltab_price_feed_schema_it_h           ltype_price_feed_schema_it_h;
         ltab_old_price_feed_schema_it         ltype_price_feed_schema_it_h;

    lv_logical_load_timestamp                price_feed_schemas.logical_load_timestamp%TYPE;

  BEGIN
    -------------------------
    --- Select for deleting a row for given p_price_feed_schema_name for if it does not exist in a new msg
    -------------------------
    BEGIN

     lv_logical_load_timestamp := SYSTIMESTAMP;

      SELECT old_version.price_feed_schema_name
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,p_effective_start_timestamp as effective_end_timestamp
            ,null
            ,null
            ,old_version.override_type
            ,old_version.code
            ,old_version.price_band
            ,old_version.price_stream_type
            ,old_version.price_adjust_id
            ,old_version.price_adjust_alias
            ,old_version.price_band_alias
            ,old_version.price_stream_type_alias
        BULK COLLECT INTO ltab_price_feed_schema_it_del
        FROM price_feed_schema_items old_version
       WHERE old_version.price_feed_schema_name = p_price_feed_schema_name
         AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_price_feed_schema_item_tab AS price_feed_schema_items_tab)) new_version
                          WHERE new_version.override_type = old_version.override_type
                            AND new_version.code          = old_version.code)
         AND old_version.effective_start_timestamp < p_effective_start_timestamp
         FOR UPDATE OF old_version.price_feed_schema_name;

    FOR l_vcount IN 1..ltab_price_feed_schema_it_del.COUNT
    LOOP

      put_history(p_tab_old_price_feed_schema_it   => ltab_price_feed_schema_it_del(l_vcount),
                  p_action                         => 'D');
    END LOOP;

    FORALL l_vcount IN 1..ltab_price_feed_schema_it_del.COUNT

      DELETE FROM price_feed_schema_items
       WHERE price_feed_schema_name = p_price_feed_schema_name
         AND override_type          = ltab_price_feed_schema_it_del(l_vcount).override_type
         AND code                   = ltab_price_feed_schema_it_del(l_vcount).code
         AND price_band             = ltab_price_feed_schema_it_del(l_vcount).price_band;

      ltab_price_feed_schema_it_del.delete();

    EXCEPTION WHEN NO_DATA_FOUND THEN
      NULL;
    END;

      BEGIN

      -- Evaluate whether incoming row is current, historical insert required, or effective period changes required
        SELECT p_price_feed_schema_name    price_feed_schema_name,
               systimestamp                logical_load_timestamp,
               p_user                      created_by,
               systimestamp                update_timestamp,
               p_user                      updated_by,
               systimestamp                update_timestamp,
               p_effective_start_timestamp effective_start_timestamp,
               next_timestamp              effective_end_timestamp,
               CASE WHEN upd_ins = 'U' AND (new_ora_hash != ora_hash AND new_ora_hash != next_ora_hash) THEN 'HI'
                    WHEN upd_ins = 'U' AND (new_ora_hash != ora_hash AND new_ora_hash = next_ora_hash)  THEN next_row_id -- yes horrible I know
                    WHEN upd_ins = 'U' AND (new_ora_hash = ora_hash AND prev_timestamp = '01-JAN-1970' AND p_effective_start_timestamp < effective_start_timestamp)  THEN row_id
                    WHEN upd_ins = 'I' THEN 'I' END AS action,
               p_effective_start_timestamp action_timestamp,
               p_override_type             override_type,
               p_code                      code,
               p_price_band                price_band,
               p_price_stream_type         price_stream_type,
               p_price_adjust_id           price_adjust_id,
               p_price_adjust_alias        price_adjust_alias,
               p_price_band_alias          price_band_alias,
               p_price_stream_type_alias   price_stream_type_alias
          BULK COLLECT INTO ltab_price_feed_schema_it_h
          FROM (SELECT price_feed_schema_name,
                       override_type,
                       code,
                       price_band,
                       price_stream_type,
                       price_adjust_id,
                       price_adjust_alias,
                       price_band_alias,
                       price_stream_type_alias,
                       effective_start_timestamp,
                       nvl(ora_hash(price_band || '~' || price_stream_type || '~' ||
                                    price_adjust_id || '~' || price_adjust_alias || '~' ||
                                    price_band_alias || '~' || price_stream_type_alias),
                           0) AS ora_hash,
                       nvl(lead(ora_hash(price_band || '~' || price_stream_type || '~' ||
                                    price_adjust_id || '~' || price_adjust_alias || '~' ||
                                    price_band_alias || '~' || price_stream_type_alias))
                           over(PARTITION BY price_feed_schema_name,override_type,code ORDER BY
                                effective_start_timestamp),
                           0) AS next_ora_hash,
                       nvl(ora_hash(p_price_band || '~' || p_price_stream_type || '~' ||
                                    p_price_adjust_id || '~' ||
                                    p_price_adjust_alias || '~' ||
                                    p_price_band_alias || '~' || p_price_stream_type_alias),
                           0) AS new_ora_hash,
                       nvl(lead(effective_start_timestamp)
                           over(PARTITION BY price_feed_schema_name,override_type,code ORDER BY
                                effective_start_timestamp),
                           '31-DEC-2049') AS next_timestamp,
                       nvl(lag(effective_start_timestamp)
                           over(PARTITION BY price_feed_schema_name,override_type,code ORDER BY
                                effective_start_timestamp),
                           '01-JAN-1970') AS prev_timestamp,
                       p_effective_start_timestamp,
                       p_price_feed_schema_name,
                       p_override_type,
                       p_code,
                       p_price_band,
                       p_price_stream_type,
                       p_price_adjust_id,
                       p_price_adjust_alias,
                       p_price_band_alias,
                       p_price_stream_type_alias,
                       row_id,
                       lead(row_id)
                          over(PARTITION BY price_feed_schema_name,override_type,code ORDER BY
                                   effective_start_timestamp) AS next_row_id,
                       CASE
                         WHEN (lead(effective_start_timestamp)
                          over(PARTITION BY price_feed_schema_name,override_type,code ORDER BY
                                   effective_start_timestamp) IS NULL AND p_effective_start_timestamp > effective_start_timestamp) THEN
                          'I'
                         ELSE
                          'U'
                       END AS upd_ins
                  FROM (SELECT *
                          FROM (SELECT p_price_feed_schema_name,
                                       p_override_type,
                                       p_code,
                                       p_price_band,
                                       p_price_stream_type,
                                       p_price_adjust_id,
                                       p_price_adjust_alias,
                                       p_price_band_alias,
                                       p_price_stream_type_alias,
                                       p_effective_start_timestamp
                                  FROM dual) new_version
                             LEFT JOIN (SELECT 'L~' || rowidtochar(old_version.rowid) AS row_id,
                                       old_version.price_feed_schema_name,
                                       old_version.override_type,
                                       old_version.code,
                                       old_version.effective_start_timestamp,
                                       old_version.price_band,
                                       old_version.price_stream_type,
                                       old_version.price_adjust_id,
                                       old_version.price_adjust_alias,
                                       old_version.price_band_alias,
                                       old_version.price_stream_type_alias
                                  FROM price_feed_schema_items old_version
                                 UNION ALL
                                SELECT 'H~' || rowidtochar(old_version.rowid) AS row_id,
                                       old_version.price_feed_schema_name,
                                       old_version.override_type,
                                       old_version.code,
                                       old_version.effective_start_timestamp,
                                       old_version.price_band,
                                       old_version.price_stream_type,
                                       old_version.price_adjust_id,
                                       old_version.price_adjust_alias,
                                       old_version.price_band_alias,
                                       old_version.price_stream_type_alias
                                  FROM price_feed_schema_items_h old_version) hist
                            ON new_version.p_price_feed_schema_name = hist.price_feed_schema_name
                           AND new_version.p_override_type          = hist.override_type
                           AND new_version.p_code                   = hist.code))
         WHERE ((p_effective_start_timestamp BETWEEN effective_start_timestamp AND
               next_timestamp) OR prev_timestamp = '01-JAN-1970');




      FOR i IN 1..ltab_price_feed_schema_it_h.COUNT LOOP

        IF ltab_price_feed_schema_it_h(i).action = 'HI' THEN

         -- If incoming row is not current, insert directly to history
          put_history(p_tab_old_price_feed_schema_it => ltab_price_feed_schema_it_h(i),
                      p_action                       => 'U');

        ELSIF ltab_price_feed_schema_it_h(i).action = 'I' THEN

         -- If new current row incoming, move current row to history
          BEGIN
            SELECT old_version.price_feed_schema_name,
                   old_version.logical_load_timestamp,
                   old_version.created_by,
                   old_version.create_timestamp,
                   old_version.updated_by,
                   old_version.update_timestamp,
                   old_version.effective_start_timestamp,
                   p_effective_start_timestamp,
                   null,
                   null,
                   old_version.override_type,
                   old_version.code,
                   old_version.price_band,
                   old_version.price_stream_type,
                   old_version.price_adjust_id,
                   old_version.price_adjust_alias,
                   old_version.price_band_alias,
                   old_version.price_stream_type_alias
            BULK COLLECT INTO ltab_old_price_feed_schema_it
            FROM (SELECT p_price_feed_schema_name  price_feed_schema_name,
                         p_override_type           override_type,
                         p_code                    code,
                         p_price_band              price_band,
                         p_price_stream_type       price_stream_type,
                         p_price_adjust_id         price_adjust_id,
                         p_price_adjust_alias      price_adjust_alias,
                         p_price_band_alias        price_band_alias,
                         p_price_stream_type_alias price_stream_type_alias
                    FROM dual) new_version,
                 price_feed_schema_items old_version
           WHERE new_version.price_feed_schema_name = old_version.price_feed_schema_name
             AND new_version.override_type          = old_version.override_type
             AND new_version.code                   = old_version.code
             AND (nrg_common.has_value_changed(new_version.price_band, old_version.price_band) = 1 OR
                  nrg_common.has_value_changed(new_version.price_stream_type, old_version.price_stream_type)= 1 OR
                  nrg_common.has_value_changed(new_version.price_adjust_id, old_version.price_adjust_id)= 1 OR
                  nrg_common.has_value_changed(new_version.price_adjust_alias, old_version.price_adjust_alias)= 1 OR
                  nrg_common.has_value_changed(new_version.price_band_alias, old_version.price_band_alias)= 1 OR
                  nrg_common.has_value_changed(new_version.price_stream_type_alias, old_version.price_stream_type_alias)= 1 );

            FOR i IN 1..ltab_old_price_feed_schema_it.COUNT LOOP

              put_history(p_tab_old_price_feed_schema_it => ltab_old_price_feed_schema_it(i),
                          p_action                       => 'U');

            END LOOP;

          EXCEPTION
            WHEN NO_DATA_FOUND  THEN
              NULL;
          END;

          -- Perform main table insert/update
          MERGE INTO price_feed_schema_items old_version USING
             (SELECT p_price_feed_schema_name  price_feed_schema_name,
                     p_override_type           override_type,
                     p_code                    code,
                     p_price_band              price_band,
                     p_price_stream_type       price_stream_type,
                     p_price_adjust_id         price_adjust_id,
                     p_price_adjust_alias      price_adjust_alias,
                     p_price_band_alias        price_band_alias,
                     p_price_stream_type_alias price_stream_type_alias
                FROM dual) new_version
          ON (old_version.price_feed_schema_name = new_version.price_feed_schema_name
          AND old_version.override_type          = new_version.override_type
          AND old_version.code                   = new_version.code)
          WHEN MATCHED THEN UPDATE
              SET logical_load_timestamp    = lv_logical_load_timestamp
                 ,updated_by                = p_user
                 ,update_timestamp          = SYSTIMESTAMP
                 ,effective_start_timestamp = p_effective_start_timestamp
                 ,price_band                = new_version.price_band
                 ,price_stream_type         = new_version.price_stream_type
                 ,price_adjust_id           = new_version.price_adjust_id
                 ,price_adjust_alias        = new_version.price_adjust_alias
                 ,price_band_alias          = new_version.price_band_alias
                 ,price_stream_type_alias   = new_version.price_stream_type_alias
          WHERE new_version.price_feed_schema_name = old_version.price_feed_schema_name
            AND new_version.override_type          = old_version.override_type
            AND new_version.code                   = old_version.code
            AND (nrg_common.has_value_changed(new_version.price_band, old_version.price_band) = 1 OR
                 nrg_common.has_value_changed(new_version.price_stream_type, old_version.price_stream_type)= 1 OR
                 nrg_common.has_value_changed(new_version.price_adjust_id, old_version.price_adjust_id)= 1 OR
                 nrg_common.has_value_changed(new_version.price_adjust_alias, old_version.price_adjust_alias)= 1 OR
                 nrg_common.has_value_changed(new_version.price_band_alias, old_version.price_band_alias)= 1 OR
                 nrg_common.has_value_changed(new_version.price_stream_type_alias, old_version.price_stream_type_alias)= 1)
          WHEN NOT MATCHED THEN INSERT
                 (price_feed_schema_name,
                  override_type,
                  code,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  price_band,
                  price_stream_type,
                  price_adjust_id,
                  price_adjust_alias,
                  price_band_alias,
                  price_stream_type_alias)
          VALUES (new_version.price_feed_schema_name,
                  new_version.override_type,
                  new_version.code,
                  lv_logical_load_timestamp,
                  p_user,
                  systimestamp,
                  p_user,
                  systimestamp,
                  p_effective_start_timestamp,
                  new_version.price_band,
                  new_version.price_stream_type,
                  new_version.price_adjust_id,
                  new_version.price_adjust_alias,
                  new_version.price_band_alias,
                  new_version.price_stream_type_alias);

        ELSE

        -- If incoming row is same as one in history but changes effective periods, update start time of next row
           IF substr(ltab_price_feed_schema_it_h(i).action,1,2) = 'H~' THEN

              UPDATE price_feed_schema_items_h pfh
                 SET pfh.effective_start_timestamp = p_effective_start_timestamp
               WHERE pfh.rowid = chartorowid(substr(ltab_price_feed_schema_it_h(i).action,3));

           ELSIF substr(ltab_price_feed_schema_it_h(i).action,1,2) = 'L~' THEN

              UPDATE price_feed_schema_items pfh
                 SET pfh.effective_start_timestamp = p_effective_start_timestamp
               WHERE pfh.rowid = chartorowid(substr(ltab_price_feed_schema_it_h(i).action,3));

           END IF;

        END IF;

        -- Align effective end timestamps
          MERGE INTO price_feed_schema_items_h ph
          USING (SELECT *
                   FROM (SELECT row_id,
                                nvl(lead(effective_start_timestamp)
                                    over(PARTITION BY price_feed_schema_name,override_type,code ORDER BY
                                         effective_start_timestamp),
                                    '31-DEC-2049') AS next_timestamp
                           FROM (SELECT NULL AS row_id,
                                        price_feed_schema_name,
                                        override_type,
                                        code,
                                        old_version.effective_start_timestamp
                                   FROM price_feed_schema_items old_version
                                  WHERE price_feed_schema_name = p_price_feed_schema_name
                                 UNION ALL
                                 SELECT old_version.rowid,
                                        price_feed_schema_name,
                                        override_type,
                                        code,
                                        old_version.effective_start_timestamp
                                   FROM price_feed_schema_items_h old_version
                                  WHERE price_feed_schema_name    = p_price_feed_schema_name))
                  WHERE row_id IS NOT NULL) tstmps
          ON (ph.rowid = tstmps.row_id)
          WHEN MATCHED THEN
            UPDATE SET ph.effective_end_timestamp = tstmps.next_timestamp;

      END LOOP;

      EXCEPTION
        WHEN NO_DATA_FOUND  THEN
          NULL;
      END;

    logger.logger.set_module(NULL);

  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_price_feed_schema_item;



-- ===================================================================================
-- put_price_feed_schema_items
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put price_feed_schema_items
--
-- Notes:
-- ------
--
--     Tables potentially populated:


--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------

--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_price_feed_schema_items (p_user                       IN price_feed_schema_items.created_by%TYPE,
                                         p_logical_load_timestamp     IN price_feed_schema_items.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp  IN price_feed_schema_items.effective_start_timestamp%TYPE,
                                         p_price_feed_schema_name     IN price_feed_schema_items.price_feed_schema_name%TYPE,
                                         p_price_feed_schema_item_tab IN price_feed_schema_items_tab)
    IS
    BEGIN
     FOR i IN (SELECT * FROM TABLE(CAST(p_price_feed_schema_item_tab AS price_feed_schema_items_tab)))
       LOOP
          put_price_feed_schema_item(p_user                            => p_user

                                    ,p_logical_load_timestamp          => p_logical_load_timestamp
                                    ,p_effective_start_timestamp       => p_effective_start_timestamp
                                    ,p_price_feed_schema_name          => p_price_feed_schema_name
                                    ,p_override_type                   => i.override_type
                                    ,p_code                            => i.code
                                    ,p_price_band                      => i.price_band
                                    ,p_price_stream_type               => i.price_stream_type
                                    ,p_price_adjust_id                 => i.price_adjust_id
                                    ,p_price_adjust_alias              => i.price_adjust_alias
                                    ,p_price_band_alias                => i.price_band_alias
                                    ,p_price_stream_type_alias         => i.price_stream_type_alias
                                    ,p_price_feed_schema_item_tab      => p_price_feed_schema_item_tab);
    END LOOP;
  END;

-- ===================================================================================
-- put_price_feed_schemas
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put price_feed_schema
--
-- Notes:
-- ------
--
--     Tables potentially populated:


--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------

--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_price_feed_schemas (p_price_feed_schemas IN price_feed_schemas_tab)
    IS
    BEGIN
     FOR i IN (SELECT * FROM TABLE(CAST(p_price_feed_schemas AS price_feed_schemas_tab)))
       LOOP
         put_price_feed_schema(p_price_feed_schema_name          => i.price_feed_schema_name
                              ,p_user                            => i.p_user
                              ,p_effective_start_timestamp       => i.effective_start_timestamp
                              ,p_default_band                    => i.default_band
                              ,p_default_price_stream_type       => i.default_price_stream_type
                              ,p_default_adjust_id               => i.default_adjust_id
                              ,p_default_adjust_alias            => i.default_adjust_alias
                              ,p_default_band_alias              => i.default_band_alias
                              ,p_dflt_price_stream_type_alias    => i.dflt_price_stream_type_alias
                              ,p_price_feed_schema_item_tab      => i.price_feed_schema_items
                              ,p_is_deleted                      => i.is_deleted
                              ,p_is_request_response             => i.is_request_response
                              ,p_price_feed_schema_tab           => p_price_feed_schemas);
    END LOOP;
  END;

-- ===================================================================================
-- put_price_feed_schema
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put price_feed_schema
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--     price_feed_schemaS
--    price_feed_schemaS_H

--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--    p_user
--     p_effective_start_timestamp
--     p_price_feed_schema_name
--     p_creation_time
--     p_last_modified_time
--     p_creator
--     p_last_modifier
--     p_default_band
--    p_price_feed_schema_item_tab
--     p_is_deleted

--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------
  PROCEDURE put_price_feed_schema(p_price_feed_schema_name          IN price_feed_schemas.price_feed_schema_name%TYPE,
                                  p_user                            IN price_feed_schemas.created_by%TYPE,
                                  p_effective_start_timestamp       IN price_feed_schemas.effective_start_timestamp%TYPE,
                                  p_default_band                    IN price_feed_schemas.default_band%TYPE,
                                  p_default_price_stream_type       IN price_feed_schemas.default_price_stream_type%TYPE,
                                  p_default_adjust_id               IN price_feed_schemas.default_adjust_id%TYPE,
                                  p_default_adjust_alias            IN price_feed_schemas.default_adjust_alias%TYPE,
                                  p_default_band_alias              IN price_feed_schemas.default_band_alias%TYPE,
                                  p_dflt_price_stream_type_alias    IN price_feed_schemas.dflt_price_stream_type_alias%TYPE,
                                  p_price_feed_schema_item_tab      IN price_feed_schema_items_tab,
                                  p_is_deleted                      IN price_feed_schemas.is_deleted%TYPE,
                                  p_is_request_response             IN VARCHAR2,
                                  p_price_feed_schema_tab           IN price_feed_schemas_tab)
  IS

    TYPE ltype_price_feed_schemas_h          IS TABLE OF price_feed_schemas_h%ROWTYPE;
         ltab_price_feed_schemas_h           ltype_price_feed_schemas_h;
         ltab_old_price_feed_schemas_dl      ltype_price_feed_schemas_h;

    TYPE ltype_price_feed_schema_it_dl       IS TABLE OF price_feed_schema_items_h%ROWTYPE;
         ltab_price_feed_schema_it_del       ltype_price_feed_schema_it_dl;

    lv_logical_load_timestamp                price_feed_schemas.logical_load_timestamp%TYPE;
    ltab_old_price_feed_schemas              price_feed_schemas_H%ROWTYPE;

    v_rowid_exists number;
  BEGIN

     logger.logger.set_module('put_price_feed_schema');
     lv_logical_load_timestamp := SYSTIMESTAMP;

      BEGIN

        SELECT old_version.price_feed_schema_name
              ,old_version.logical_load_timestamp
              ,old_version.created_by
              ,old_version.create_timestamp
              ,old_version.updated_by
              ,old_version.update_timestamp
              ,old_version.effective_start_timestamp
              ,p_effective_start_timestamp as effective_end_timestamp
              ,null
              ,null
              ,old_version.default_band
              ,old_version.default_price_stream_type
              ,old_version.default_adjust_id
              ,old_version.default_adjust_alias
              ,old_version.default_band_alias
              ,old_version.dflt_price_stream_type_alias
              ,old_version.is_deleted
          BULK COLLECT INTO ltab_old_price_feed_schemas_dl
          FROM price_feed_schemas old_version
         WHERE NOT EXISTS (SELECT 1
                             FROM TABLE(CAST(p_price_feed_schema_tab AS price_feed_schemas_tab)) new_version
                            WHERE new_version.price_feed_schema_name = old_version.price_feed_schema_name)
           AND old_version.effective_start_timestamp < p_effective_start_timestamp
           FOR UPDATE OF old_version.price_feed_schema_name;

        SELECT old_version.price_feed_schema_name
              ,old_version.logical_load_timestamp
              ,old_version.created_by
              ,old_version.create_timestamp
              ,old_version.updated_by
              ,old_version.update_timestamp
              ,old_version.effective_start_timestamp
              ,p_effective_start_timestamp as effective_end_timestamp
              ,null
              ,null
              ,old_version.override_type
              ,old_version.code
              ,old_version.price_band
              ,old_version.price_stream_type
              ,old_version.price_adjust_id
              ,old_version.price_adjust_alias
              ,old_version.price_band_alias
              ,old_version.price_stream_type_alias
          BULK COLLECT INTO ltab_price_feed_schema_it_del
          FROM price_feed_schema_items  old_version
         WHERE NOT EXISTS (SELECT 1
                             FROM TABLE(CAST(p_price_feed_schema_tab AS price_feed_schemas_tab)) new_version
                            WHERE new_version.price_feed_schema_name = old_version.price_feed_schema_name)
           AND old_version.effective_start_timestamp < p_effective_start_timestamp                            ;

        FOR l_vcount IN 1..ltab_price_feed_schema_it_del.COUNT
        LOOP

          put_history(p_tab_old_price_feed_schema_it     => ltab_price_feed_schema_it_del(l_vcount),
                      p_action                           => 'D');

        END LOOP;

        FORALL l_vcount IN 1..ltab_old_price_feed_schemas_dl.COUNT

          DELETE FROM price_feed_schema_items
          WHERE price_feed_schema_name = ltab_old_price_feed_schemas_dl(l_vcount).price_feed_schema_name;

        FOR l_vcount IN 1..ltab_old_price_feed_schemas_dl.COUNT
        LOOP

          put_history(p_tab_old_price_feed_schemas  => ltab_old_price_feed_schemas_dl(l_vcount),
                      p_action                      => 'D');

        END LOOP;

        FORALL l_vcount IN 1..ltab_old_price_feed_schemas_dl.COUNT

          DELETE FROM price_feed_schemas
           WHERE price_feed_schema_name = ltab_old_price_feed_schemas_dl(l_vcount).price_feed_schema_name;

          ltab_old_price_feed_schemas_dl.delete();
          ltab_price_feed_schema_it_del.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;

      BEGIN

      -- Evaluate whether incoming row is current, historical insert required, or effective period changes required
        SELECT p_price_feed_schema_name    price_feed_schema_name,
               systimestamp                logical_load_timestamp,
               p_user                      created_by,
               systimestamp                update_timestamp,
               p_user                      updated_by,
               systimestamp                update_timestamp,
               p_effective_start_timestamp effective_start_timestamp,
               next_timestamp              effective_end_timestamp,
               CASE WHEN upd_ins = 'U' AND (new_ora_hash != ora_hash AND new_ora_hash != next_ora_hash) THEN 'HI'
                    WHEN upd_ins = 'U' AND (new_ora_hash != ora_hash AND new_ora_hash = next_ora_hash)  THEN next_row_id -- yes horrible I know
                    WHEN upd_ins = 'U' AND (new_ora_hash = ora_hash AND prev_timestamp = '01-JAN-1970' AND p_effective_start_timestamp < effective_start_timestamp)  THEN row_id
                    WHEN upd_ins = 'I' THEN 'I' END AS action,
               p_effective_start_timestamp action_timestamp,
               p_default_band              default_band,
               p_default_price_stream_type default_price_stream_type,
               p_default_adjust_id         default_adjust_id,
               p_default_adjust_alias      default_adjust_alias,
               p_default_band_alias        default_band_alias,
               p_is_deleted                is_deleted,
               p_dflt_price_stream_type_alias dflt_price_stream_type_alias
         BULK COLLECT INTO ltab_price_feed_schemas_h
          FROM (SELECT price_feed_schema_name,
                       default_band,
                       default_price_stream_type,
                       default_adjust_id,
                       default_adjust_alias,
                       default_band_alias,
                       dflt_price_stream_type_alias,
                       is_deleted,
                       effective_start_timestamp,
                       nvl(ora_hash(default_band || '~' || default_price_stream_type || '~' ||
                                    default_adjust_id || '~' || default_adjust_alias || '~' ||
                                    default_band_alias || '~' || dflt_price_stream_type_alias || '~' || is_deleted),
                           0) AS ora_hash,
                       nvl(lead(ora_hash(default_band || '~' || default_price_stream_type || '~' ||
                                         default_adjust_id || '~' ||
                                         default_adjust_alias || '~' ||
                                         default_band_alias || '~' || dflt_price_stream_type_alias || '~' || is_deleted))
                           over(PARTITION BY price_feed_schema_name ORDER BY
                                effective_start_timestamp),
                           0) AS next_ora_hash,
                       nvl(ora_hash(p_default_band || '~' || p_default_price_stream_type || '~' ||
                                    p_default_adjust_id || '~' || p_default_adjust_alias || '~' ||
                                    p_default_band_alias || '~' || p_dflt_price_stream_type_alias || '~' || p_is_deleted),
                           0) AS new_ora_hash,
                       nvl(lead(effective_start_timestamp)
                           over(PARTITION BY price_feed_schema_name ORDER BY
                                effective_start_timestamp),
                           '31-DEC-2049') AS next_timestamp,
                       nvl(lag(effective_start_timestamp)
                           over(PARTITION BY price_feed_schema_name ORDER BY
                                effective_start_timestamp),
                           '01-JAN-1970') AS prev_timestamp,
                       p_effective_start_timestamp,
                       p_price_feed_schema_name,
                       p_default_band,
                       p_is_deleted,
                       p_default_price_stream_type,
                       p_default_adjust_id,
                       p_default_adjust_alias,
                       p_default_band_alias,
                       p_dflt_price_stream_type_alias,
                       row_id,
                       lead(row_id)
                          over(PARTITION BY p_price_feed_schema_name ORDER BY
                                   effective_start_timestamp) AS next_row_id,
                       CASE
                         WHEN (lead(effective_start_timestamp)
                          over(PARTITION BY p_price_feed_schema_name ORDER BY
                                   effective_start_timestamp) IS NULL AND p_effective_start_timestamp > effective_start_timestamp) THEN
                          'I'
                         ELSE
                          'U'
                       END AS upd_ins
                  FROM (SELECT *
                          FROM (SELECT p_price_feed_schema_name,
                                       p_default_band,
                                       p_is_deleted,
                                       p_default_price_stream_type,
                                       p_default_adjust_id,
                                       p_default_adjust_alias,
                                       p_default_band_alias,
                                       p_dflt_price_stream_type_alias,
                                       p_effective_start_timestamp
                                  FROM dual) new_version
                     LEFT JOIN (SELECT 'L~' || rowidtochar(old_version.rowid) AS row_id,
                                       old_version.price_feed_schema_name,
                                       old_version.effective_start_timestamp,
                                       old_version.default_band,
                                       old_version.default_price_stream_type,
                                       old_version.default_adjust_id,
                                       old_version.default_adjust_alias,
                                       old_version.default_band_alias,
                                       old_version.dflt_price_stream_type_alias,
                                       old_version.is_deleted
                                  FROM price_feed_schemas old_version
                                 UNION ALL
                                SELECT 'H~' || rowidtochar(old_version.rowid) AS row_id,
                                       old_version.price_feed_schema_name,
                                       old_version.effective_start_timestamp,
                                       old_version.default_band,
                                       old_version.default_price_stream_type,
                                       old_version.default_adjust_id,
                                       old_version.default_adjust_alias,
                                       old_version.default_band_alias,
                                       old_version.dflt_price_stream_type_alias,
                                       old_version.is_deleted
                                  FROM price_feed_schemas_h old_version) hist
                            ON new_version.p_price_feed_schema_name =
                               hist.price_feed_schema_name))
         WHERE ((p_effective_start_timestamp BETWEEN effective_start_timestamp AND
               next_timestamp) OR prev_timestamp = '01-JAN-1970');

      FOR i IN 1..ltab_price_feed_schemas_h.COUNT LOOP

        IF ltab_price_feed_schemas_h(i).action = 'HI' THEN

         -- If incoming row is not current, insert directly to history
          put_history(p_tab_old_price_feed_schemas => ltab_price_feed_schemas_h(i),
                      p_action                     => 'U');

        ELSIF ltab_price_feed_schemas_h(i).action = 'I' THEN

         -- If new current row incoming, move current row to history
          BEGIN
            SELECT old_version.price_feed_schema_name
                  ,old_version.logical_load_timestamp
                  ,old_version.created_by
                  ,old_version.create_timestamp
                  ,old_version.updated_by
                  ,old_version.update_timestamp
                  ,old_version.effective_start_timestamp
                  ,p_effective_start_timestamp as effective_end_timestamp
                  ,null
                  ,null
                  ,old_version.default_band
                  ,old_version.default_price_stream_type
                  ,old_version.default_adjust_id
                  ,old_version.default_adjust_alias
                  ,old_version.default_band_alias
                  ,old_version.is_deleted
                  ,old_version.dflt_price_stream_type_alias
            INTO ltab_old_price_feed_schemas
            FROM (SELECT p_price_feed_schema_name         price_feed_schema_name,
                         p_default_band                   default_band,
                         p_is_deleted                     is_deleted,
                         p_default_price_stream_type      default_price_stream_type,
                         p_default_adjust_id              default_adjust_id,
                         p_default_adjust_alias           default_adjust_alias,
                         p_default_band_alias             default_band_alias,
                         p_dflt_price_stream_type_alias   dflt_price_stream_type_alias
                    FROM dual) new_version,
                 price_feed_schemas old_version
           WHERE new_version.price_feed_schema_name=old_version.price_feed_schema_name
             AND (nrg_common.has_value_changed(new_version.default_band, old_version.default_band) = 1 OR
                  nrg_common.has_value_changed(new_version.default_price_stream_type, old_version.default_price_stream_type)= 1 OR
                  nrg_common.has_value_changed(new_version.default_adjust_id, old_version.default_adjust_id)= 1 OR
                  nrg_common.has_value_changed(new_version.default_adjust_alias, old_version.default_adjust_alias)= 1 OR
                  nrg_common.has_value_changed(new_version.default_band_alias, old_version.default_band_alias)= 1 OR
                  nrg_common.has_value_changed(new_version.dflt_price_stream_type_alias, old_version.dflt_price_stream_type_alias)= 1 OR
                  nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 );

            put_history(p_tab_old_price_feed_schemas => ltab_old_price_feed_schemas,
                        p_action                     => 'U');

          EXCEPTION
            WHEN NO_DATA_FOUND  THEN
              NULL;
          END;

          -- Perform main table insert/update
          MERGE INTO price_feed_schemas old_version USING
             (SELECT p_price_feed_schema_name price_feed_schema_name,
                     p_default_band           default_band,
                     p_is_deleted             is_deleted,
                     p_default_price_stream_type      default_price_stream_type,
                     p_default_adjust_id      default_adjust_id,
                     p_default_adjust_alias   default_adjust_alias,
                     p_default_band_alias     default_band_alias,
                     p_dflt_price_stream_type_alias dflt_price_stream_type_alias
                FROM dual) new_version
          ON (old_version.price_feed_schema_name = new_version.price_feed_schema_name)
          WHEN MATCHED THEN UPDATE
              SET logical_load_timestamp    = lv_logical_load_timestamp
                 ,updated_by                = p_user
                 ,update_timestamp          = SYSTIMESTAMP
                 ,effective_start_timestamp = p_effective_start_timestamp
                 ,default_band              = p_default_band
                 ,is_deleted                = p_is_deleted
                 ,default_price_stream_type         = p_default_price_stream_type
                 ,default_adjust_id         = p_default_adjust_id
                 ,default_adjust_alias      = p_default_adjust_alias
                 ,default_band_alias        = p_default_band_alias
                 ,dflt_price_stream_type_alias = p_dflt_price_stream_type_alias
          WHERE new_version.price_feed_schema_name = old_version.price_feed_schema_name
            AND (nrg_common.has_value_changed(new_version.default_band, old_version.default_band) = 1 OR
                 nrg_common.has_value_changed(new_version.default_price_stream_type, old_version.default_price_stream_type)= 1 OR
                 nrg_common.has_value_changed(new_version.default_adjust_id, old_version.default_adjust_id)= 1 OR
                 nrg_common.has_value_changed(new_version.default_adjust_alias, old_version.default_adjust_alias)= 1 OR
                 nrg_common.has_value_changed(new_version.default_band_alias, old_version.default_band_alias)= 1 OR
                 nrg_common.has_value_changed(new_version.dflt_price_stream_type_alias, old_version.dflt_price_stream_type_alias)= 1 OR
                 nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 )
          WHEN NOT MATCHED THEN INSERT
                 (price_feed_schema_name,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  default_band,
                  default_price_stream_type,
                  default_adjust_id,
                  default_adjust_alias,
                  default_band_alias,
                  dflt_price_stream_type_alias,
                  is_deleted)
          VALUES (new_version.price_feed_schema_name
                 ,lv_logical_load_timestamp
                 ,p_user
                 ,systimestamp
                 ,p_user
                 ,systimestamp
                 ,p_effective_start_timestamp
                 ,new_version.default_band
                 ,new_version.default_price_stream_type
                 ,new_version.default_adjust_id
                 ,new_version.default_adjust_alias
                 ,new_version.default_band_alias
                 ,new_version.dflt_price_stream_type_alias
                 ,new_version.is_deleted);

        ELSE

        -- If incoming row is same as one in history but changes effective periods, update start time of next row
           IF substr(ltab_price_feed_schemas_h(i).action,1,2) = 'H~' THEN

              UPDATE price_feed_schemas_h pfh
                 SET pfh.effective_start_timestamp = p_effective_start_timestamp
               WHERE pfh.rowid = chartorowid(substr(ltab_price_feed_schemas_h(i).action,3));

           ELSIF substr(ltab_price_feed_schemas_h(i).action,1,2) = 'L~' THEN

              UPDATE price_feed_schemas pfh
                 SET pfh.effective_start_timestamp = p_effective_start_timestamp
               WHERE pfh.rowid = chartorowid(substr(ltab_price_feed_schemas_h(i).action,3));

           END IF;

        END IF;

        -- Align effective end timestamps
          MERGE INTO price_feed_schemas_h ph
          USING (SELECT *
                   FROM (SELECT row_id,
                                nvl(lead(effective_start_timestamp)
                                    over(PARTITION BY price_feed_schema_name ORDER BY
                                         effective_start_timestamp),
                                    '31-DEC-2049') AS next_timestamp
                           FROM (SELECT NULL AS row_id,
                                        price_feed_schema_name,
                                        old_version.effective_start_timestamp
                                   FROM price_feed_schemas old_version
                                  WHERE price_feed_schema_name = p_price_feed_schema_name
                                 UNION ALL
                                 SELECT old_version.rowid,
                                        price_feed_schema_name,
                                        old_version.effective_start_timestamp
                                   FROM price_feed_schemas_h old_version
                                  WHERE price_feed_schema_name = p_price_feed_schema_name))
                  WHERE row_id IS NOT NULL) tstmps
          ON (ph.rowid = tstmps.row_id)
          WHEN MATCHED THEN
            UPDATE SET ph.effective_end_timestamp = tstmps.next_timestamp;

      END LOOP;

      EXCEPTION
        WHEN NO_DATA_FOUND  THEN
          NULL;
      END;

          -- Put price schema items
          put_price_feed_schema_items (p_price_feed_schema_name         => p_price_feed_schema_name,
                                      p_user                           => p_user,
                                      p_logical_load_timestamp         => lv_logical_load_timestamp,
                                      p_effective_start_timestamp      => p_effective_start_timestamp,
                                      p_price_feed_schema_item_tab     => p_price_feed_schema_item_tab);

    logger.logger.set_module(NULL);

  EXCEPTION
  WHEN DUP_VAL_ON_INDEX THEN
    NULL;
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END put_price_feed_schema; */

  -- ===================================================================================
  -- put_price_feed_schema_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     price_feed_schemaS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_price_feed_schema                    Anytime Position Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_price_feed_schema_set(p_user                      IN VARCHAR2,
                                      p_effective_start_timestamp IN TIMESTAMP,
                                      p_price_feed_schemas        IN price_feed_schemas_tab)
  IS

    lv_business_date price_feed_schemas.Business_Date%TYPE;
    lv_reporting_date price_feed_schemas.Reporting_Date%TYPE;

    TYPE lvtab_price_feed_schemas IS TABLE OF price_feed_schemas%rowtype;
    ltab_price_feed_schemas lvtab_price_feed_schemas;

  BEGIN

    logger.logger.set_module('nrg_position.put_price_feed_schema_set');

    lv_business_date := Nrg_Common.get_snapshot_business_date(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

       SELECT pfs_tab.price_feed_schema_name,
              SYSTIMESTAMP ,
              p_user,
              SYSTIMESTAMP,
              P_USER,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              pfs_tab.default_band,
              pfs_tab.default_price_stream_type,
              pfs_tab.default_adjust_id,
              pfs_tab.default_adjust_alias,
              pfs_tab.default_band_alias,
              pfs_tab.is_deleted,
              lv_business_date,
              lv_reporting_date,
              pfs_tab.is_eod,
              pfs_tab.dflt_price_stream_type_alias
         BULK COLLECT INTO ltab_price_feed_schemas
         FROM TABLE(CAST(p_price_feed_schemas AS price_feed_schemas_tab)) pfs_tab;

    DELETE price_feed_schemas_h
     WHERE effective_start_timestamp = p_effective_start_timestamp AND
           business_date = lv_business_Date;

     INSERT INTO price_feed_schemas_h
           (price_feed_schema_name,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,
            default_band,
            default_price_stream_type,
            default_adjust_id,
            default_adjust_alias,
            default_band_alias,
            is_deleted,
            dflt_price_stream_type_alias,
            business_date,
            reporting_date,
            is_eod)
     SELECT price_feed_schema_name
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,p_effective_start_timestamp effective_end_timestamp
           ,'U'
           ,SYSTIMESTAMP action_timestamp
           ,default_band
           ,default_price_stream_type
           ,default_adjust_id
           ,default_adjust_alias
           ,default_band_alias
           ,is_deleted
           ,dflt_price_stream_type_alias
           ,business_date
           ,reporting_date
           ,is_eod
     FROM   price_feed_schemas
     WHERE  effective_start_timestamp = p_effective_start_timestamp AND
            business_date = lv_business_date;


    DELETE price_feed_schemas
     WHERE effective_start_timestamp = p_effective_start_timestamp AND
           business_date = lv_business_Date;


      FORALL i IN ltab_price_feed_schemas.FIRST..ltab_price_feed_schemas.LAST
      INSERT INTO /*+ APPEND_VALUES */ price_feed_schemas
      VALUES
       (ltab_price_feed_schemas(i).price_feed_schema_name,
        ltab_price_feed_schemas(i).logical_load_timestamp,
        ltab_price_feed_schemas(i).created_by,
        ltab_price_feed_schemas(i).create_timestamp,
        ltab_price_feed_schemas(i).updated_by,
        ltab_price_feed_schemas(i).update_timestamp,
        ltab_price_feed_schemas(i).effective_start_timestamp,
        ltab_price_feed_schemas(i).default_band,
        ltab_price_feed_schemas(i).default_price_stream_type,
        ltab_price_feed_schemas(i).default_adjust_id,
        ltab_price_feed_schemas(i).default_adjust_alias,
        ltab_price_feed_schemas(i).default_band_alias,
        ltab_price_feed_schemas(i).is_deleted,        
        ltab_price_feed_schemas(i).business_date,
        ltab_price_feed_schemas(i).reporting_date,
        ltab_price_feed_schemas(i).is_eod,
        ltab_price_feed_schemas(i).dflt_price_stream_type_alias);

     DELETE price_feed_schema_items_h
      WHERE effective_start_timestamp = p_effective_start_timestamp AND
            business_date = lv_business_date;

     INSERT INTO price_feed_schema_items_h
           (price_feed_schema_name,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,
            override_type,
            code,
            price_band,
            price_stream_type,
            price_adjust_id,
            price_adjust_alias,
            price_band_alias,
            price_stream_type_alias,
            business_date,
            reporting_date,
            is_eod)
     SELECT price_feed_schema_name,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            p_effective_start_timestamp effective_end_timestamp,
            'U',
            SYSTIMESTAMP action_timestamp,
            override_type,
            code,
            price_band,
            price_stream_type,
            price_adjust_id,
            price_adjust_alias,
            price_band_alias,
            price_stream_type_alias,
            business_date,
            reporting_date,
            is_eod
       FROM price_feed_schema_items
      WHERE effective_start_timestamp = p_effective_start_timestamp AND
            business_date = lv_business_date;

     DELETE price_feed_schema_items
      WHERE effective_start_timestamp = p_effective_start_timestamp AND
            business_date = lv_business_date;

        FOR lv_count IN 1..p_price_feed_schemas.count LOOP

          put_price_feed_schema_item_set(p_price_feed_schema_items    => p_price_feed_schemas(lv_count).price_feed_schema_items,
                                         p_business_date              => lv_business_date,
                                         p_reporting_date             => lv_reporting_date,
                                         p_effective_start_timestamp  => p_effective_start_timestamp,
                                         p_user                       => p_user);

        END LOOP;


  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_price_feed_schema_set;

  -- ===================================================================================
  -- put_price_feed_schema_item_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     price_feed_schema_itemS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_price_feed_schema_item                    Anytime Position Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_price_feed_schema_item_set(p_price_feed_schema_items        IN price_feed_schema_items_tab,
                                           p_business_date                  IN DATE,
                                           p_reporting_date                 IN DATE,
                                           p_effective_start_timestamp      IN TIMESTAMP,
                                           p_user                           IN VARCHAR2)
  IS

    TYPE lvtab_price_feed_schema_items IS TABLE OF price_feed_schema_items%rowtype;
    ltab_price_feed_schema_items lvtab_price_feed_schema_items;

  BEGIN

    logger.logger.set_module('nrg_position.put_price_feed_schema_item_set');

       SELECT pfs_tab.price_feed_schema_name,
              SYSTIMESTAMP ,
              p_user,
              SYSTIMESTAMP,
              P_USER,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              pfs_tab.override_type,
              pfs_tab.code,
              pfs_tab.price_band,
              pfs_tab.price_stream_type,
              pfs_tab.price_adjust_id,
              pfs_tab.price_adjust_alias,
              pfs_tab.price_band_alias,             
              p_business_date,
              p_reporting_date,
              pfs_tab.is_eod,
              pfs_tab.price_stream_type_alias
         BULK COLLECT INTO ltab_price_feed_schema_items
         FROM TABLE(CAST(p_price_feed_schema_items AS price_feed_schema_items_tab)) pfs_tab;

      FORALL i IN ltab_price_feed_schema_items.FIRST..ltab_price_feed_schema_items.LAST
      INSERT INTO /*+ APPEND_VALUES */ price_feed_schema_items
      VALUES
       (ltab_price_feed_schema_items(i).price_feed_schema_name,
        ltab_price_feed_schema_items(i).logical_load_timestamp,
        ltab_price_feed_schema_items(i).created_by,
        ltab_price_feed_schema_items(i).create_timestamp,
        ltab_price_feed_schema_items(i).updated_by,
        ltab_price_feed_schema_items(i).update_timestamp,
        ltab_price_feed_schema_items(i).effective_start_timestamp,
        ltab_price_feed_schema_items(i).override_type,
        ltab_price_feed_schema_items(i).code,
        ltab_price_feed_schema_items(i).price_band,
        ltab_price_feed_schema_items(i).price_stream_type,
        ltab_price_feed_schema_items(i).price_adjust_id,
        ltab_price_feed_schema_items(i).price_adjust_alias,
        ltab_price_feed_schema_items(i).price_band_alias,        
        ltab_price_feed_schema_items(i).business_date,
        ltab_price_feed_schema_items(i).reporting_date,
        ltab_price_feed_schema_items(i).is_eod,
        ltab_price_feed_schema_items(i).price_stream_type_alias);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_price_feed_schema_item_set;



END nrg_price_schema;
/