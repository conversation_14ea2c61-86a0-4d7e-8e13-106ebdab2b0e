CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_trade
AS
  -- ===================================================================================
  -- NRG_TRADE
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the trades model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Trade Deleted Before Update and After Insert
  --     -20004    Default Exception
  --     -20005    Invalid Platform Name. Expected Platform names are NG or MM
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     01/09/2011   Sanket Mittal      1.0    Creation
  --     27/09/2011   Sanket Mittal      1.1    Updated the trades entity
  --     10/10/2011   Sanket Mittal      1.2    Updated the Logic for concurrent writes
  --     02/11/2011   Paul Flynn         1.3    Updated to include insertion of MM Account ID and Trade Quantity
  --     04/11/2011   Manoj Kumar        1.4    Updated the comments and variable names
  --     07/11/2011   Mark Gornicki      1.5    Removed the code for the remove_trade method, since it is not required.
  --                                            Updated the trade_ids function such that if the platform is not
  --                                            specified all trade_ids within the date range are returned.
  --                                            Implemented by clause  platform = NVL(p_platform,platform)
  --                                            Also changed the parameters and the cursor query to use creation_time (source)
  --                                            and not create_timestamp (dwh audit column)
  --     08/11/2011   Mark Gornicki      1.6    Reordered the code which updates trades and copies previous version to history
  --                                            The put_history is now only executed if the update actually updates any rows.
  --     09/11/2011   Sanket Mittal      1.7    Renamed the function trade_ids to get_trade_ids
  --                                            Changed the logic for getting the trade ids
  --     10/11/2011   Sanket Mittal      1.8    Included the logic for trade id short form and order id short form
  --     11/11/2011   Mark Gornicki      1.9    In put_trade renamed parameter p_platform to p_platform
  --     16/11/2011   Mark Gornicki      2.0    Added the parameter p_is_limit_trailing to put_trade to handle new column on underlying tables
  --     25/11/2011   Mark Gornicki      2.1    Amended create_trade_stub to set BUSINESS_DATE to the gc_default_timestamp
  --                                            to ensure the column has a value for partitioning otherwise it will fail.
  --                                            Amended Put_Trade and Put_History to handle the processing for internal hedge trades.
  --                                            This is for MarketMaker data such that it would have no trading_account_id set and
  --                                            we must perform a lookup based on MarketMaker source ids, so set the correct
  --                                            Trading Account ID/Type combination for referential integrity
  --     28/11/2011   Mark Gornicki      2.2    Amended the mm_account_id lookup to use to_char to ensure efficient index lookup
  --     29/11/2011   Sanket Mittal      2.3    Updated the business rule for normalised quantity and normalised trade currency
  --     01/12/2011   Sanket Mittal      2.4    Added logic to get the currency for TIQ instruments from MM using ref table
  --     08/12/2011   Patrick Dinwiddy   2.5    Added logic to populate quantity designator
  --     12/12/2011   Sanket Mittal      2.6    Changed for the traded and normalised quantity for absolute values
  --     13/12/2011   Sanket Mittal      2.7    Updated to add a new fundtion get_order_ids
  --     20/12/2011   Sanket Mittal      2.8    Updated the logic to keep the trade time static once written
  --     23/12/2011   Sanket Mittal      2.9    Updated the logic to keep the business date static once written
  --     29/12/2011   Sanket Mittal      3.0    Added the column reporting Date to put_trade
  --     04/01/2012   Sanket Mittal      3.1    Updated the condition effective_start_timestamp <> gc_default_timestamp for reconciliation methods
  --     25/01/2012    Sanket Mittal       3.2    Updated the calculation for normalised quantity
  --     26/01/2012   Mark Gornicki      3.3    The SB code for used to determine lv_trade_quantity_currency was misspelt
  --                                            resulting in null normalised trade quantity currency.'A-EVOH'  should be 'A-EOVH'
  --     27/01/2012   Mark Gornicki      3.4    Amended the calculation to default to the trading account currency if lv_trade_quantity_currency is null.
  --                                            populate new column normalised price
  --     15/02/2012   Manoj Kumar        3.5    Included the calculation for product_point_multiplier and product_fractional_part_ratio
  --     08/03/2012   Sanket Mittal      3.6    Updated the record comparision filter so that we do not include publish time, event time and changed character while comparing two trade messages
  --     10/04/2012   Sanket Mittal      3.7    Updated the lookup for trading accounts based on source account id and type <> CUSTOMER
  --     16/05/2012   Mark Gornicki      3.8    Added code to create identity stubs
  --     03/07/2012   Sanket Mittal      3.9    Added the column record_source and cash transaction seq
  --     11/07/2012   Sanket Mittal      4.0    Added the new field price_source
  --     06/09/2012    Sanket Mittal       4.1    Changed the get trade ids function to update the lookup to use the update time for MM lookups
  --     15/01/2013   Sanket Mittal      4.2    Added the sub routine get_stubbed_ids
  --     06/03/2013   Sanket Mittal      4.3    Updated for P2 Changes
  --     14/11/2013   Adam Krasnicki     4.4    BER-721: Instrument type changed to 'Shares' from 'Companies'
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added Logic to allow NRG to perform reconciliation of Hedge Trades
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Modified get_trade_ids function to return only non-hedge trades.
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added back p_trade_quantity_currency parameter which was commented out
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added logic to set values to p_trading_account_function,
  --                                                     lv_normalised_quantity, lv_trade_quantity_currency, p_trade_amount,
  --                                                     lv_trade_id_short_form and lv_order_id_short_form based on the values of
  --                                                     p_trading_account_type
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added logic to Consume the newly added Hedge trade attibutes
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added logic to create stubs for hedge_cancellation_reference,hedge_correction_reference
  --                                                     and hedge_cross_reference
  --     20/01/2013   Adam Krasnicki     4.5    Creation time is used instead of trade time in get_hedge_trade_ids function
  --     22/01/2013   Adam Krasnicki     4.51   Creation time used instead of order_time is setting business_day in put_trade procedure for NG
  --     15/05/2014   Adam Krasnicki     4.6    BER-984: LIMIT_TRAILING_BEST_PRICE column dropped from TRADES[_H]
  --     10/06/2014   Adam Krasnicki     4.6    HEDGE trades business_date and reporting _date are determined on similar logic
  --     15/08/2014   Adam Krasnicki     4.7    MMCFD GET_TRADE_IDS modification from o.ordertime BETWEEN p_starttime AND p_endtime
  --                                            to o.ordertime >= p_starttime AND o.ordertime < p_endtime for MM
  --     17/10/2014   Adam Krasnicki     4.8    Changes in trading amount measures for MMCFD
  --     12/11/2014   Adam Krasnicki     4.8    Changes in trade_quantity_currency derivation
  --     07/05/2015   Adam Krasnicki     4.9    4 new parameters added to trades[_h] tables (IS_ROLLOVER_CLOSING,PRICE_OFFSET_INDEX,EXECUTION_STARTED_TIME..)
  --     19/05/2015   Adam Krasnicki     4.9    3 new cols: cd_tenor, cd_state, cd_result
  --     27/05/2015   Adam Krasnicki     4.9    Exclude SPEEDBET trades from get functions
  --     16/06/2015   Adam Krasnicki     4.9    put_position, add new param p_direction_multiplier (NULL)
  --     26/06/2015   Adam Krasnicki     4.9    put_trade: new parameters: p_quote_depth_price
  --     01/07/2015   Adam Krasnicki     4.9    put_trade: changes in few column values for MM trades (lv_is_prdct_ccy_in_frtnl_prts, lv_fractional_part_ratio)
  --     01/07/2015   Adam Krasnicki     4.9    put_trade: changes in call of nrg_position.put_position
  --     15/07/2015   Adam Krasnicki     4.9    put_trade: logic for lv_normalised_quantity changed
  --     21/07/2015   Adam Krasnicki     4.9    put_trade: logic for lv_normalised_quantity corrected
  --     22/07/2015   Adam Krasnicki     4.9    put_trade: logic for lv_normalised_quantity corrected
  --     07/10/2015   Sanket Mittal      5.0    put_trade - put_position procedure changed to add trading_scope parameter BER-2081
  --     13/11/2015   Sanket Mittal      5.1    put_trade - Added new columns spread_l1_quantity and spear_l1_quantity_currency
  --     20/01/2016   Sanket Mittal      5.2    put_trade - Added new parameters for binary:p_binary_type, p_settle_time, p_tenor, p_strike_price_additional
  --     18/05/2016   Sanket Mittal      5.3    put_trade - Populate missing BI_ODS.TRADES columns for Binaries and Speedbets BER 2566
  --    10/06/2016    Sakina Kinkhabwala  5.4   put_trade - Added new parameters for countdowns:p_trade_instrument_amount, p_reference_trade_price
  --     28/07/2016   Sanket Mittal      5.5    create_session_stub to include channel id BER-2778
  --     22/08/2016   Sanket Mittal      5.6    BER-2830 New Attribute: LadderQuantitiesOverride
  --     22/08/2016   Sanket Mittal      5.7    BER-2845 Knockouts Normalisation - NRG_TRADE - put_trade
  --     01/09/2016  Sanket Mittal       5.8    BER-2872 Knockouts Normalisation - Add the STRIKE_PRICE POSITIONS
  --     12/10/2016   Sanket Mittal      5.9    BER-2967 add Knockout product wrapper to spread_l1 calcs
  --     12/10/2016   Sanket Mittal      5.9    BER-2957 New Hedge Attributes
  --     18/10/2016   Sanket Mittal      6.0    BER-2984 Change for order type function call
  --     16/03/2017   Sanket Mittal      6.1    BER-3434 SWS 37 - BI_ODS.TRADES - New Attribute
  --     14/07/2017   Sanket Mittal      6.2    BER-3786 NRG_TRADE - bi_ods.trades - Changes
  --     01/09/2017   Patrick Dinwiddy   6.3    BER-3905 New attribute
  --     15/01/2018   Sakina Kinkhabwala 6.4    BER-4127 put_trade/put_history - add new attributes
  --     22/05/2018   Sakina Kinkhabwala 6.5    BER-4593 put_trade/put_history - add new attributes
  -- ===================================================================================
  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================
  gc_version           constant VARCHAR2(3) := '6.5';
  gc_cr                constant VARCHAR2(1) := chr(10);
  gc_true              constant pls_integer := 1;
  gc_false             constant pls_integer := 0;
  gc_default_timestamp constant TIMESTAMP   := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');
  -- ===================================================================================
  -- PRIVATE MODULES
  -- ===================================================================================
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the trades record
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --      p_old_trade_record              This is the old version of the trade transaction
  --      p_effective_end_timestamp       This is the end time for the record
  --      p_action                        Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(
      p_old_trade_record        IN trades%rowtype,
      p_effective_end_timestamp IN trades_h.effective_end_timestamp%TYPE,
      p_action                  IN trades_h.action%TYPE)
  IS
  BEGIN
    INSERT
    INTO trades_h
      (
        trade_id,
        platform,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        effective_end_timestamp,
        action,
        action_timestamp,
        trading_account_id,
        trading_account_type,
        trading_account_codifier,
        order_id,
        product_instrument_code,
        product_wrapper_code,
        product_generation,
        product_schema_code,
        prdct_financing_ratio_max,
        session_key,
        booking_number,
        cp1_cash_account_number,
        cp2_cash_account_number,
        publish_time,
        event_time,
        creation_time,
        update_time,
        creation_identity_token,
        crtn_on_bhlf_of_idntty_tkn,
        update_identity_token,
        updt_on_bhlf_of_idntty_tkn,
        version_number,
        is_deleted,
        visit_id,
        channel_id,
        request_id,
        reduced_trade_id,
        reversed_trade_id,
        business_date,
        reporting_date,
        trade_time,
        order_type,
        controlled_order_type,
        trading_account_function,
        trading_account_currency,
        product_point_multiplier,
        product_fractional_part_ratio,
        product_currency,
        is_primary,
        is_late_deal,
        mm_value_date,
        direction,
        direction_multiplier,
        quoted_l1_ask_price,
        quoted_l1_bid_price,
        trade_price,
        price_designator,
        quantity_designator,
        normalised_quantity,
        normalised_quantity_currency,
        quantity_fx_rate_bid,
        quantity_fx_rate_ask,
        trade_amount,
        trade_amount_currency,
        trd_amnt_in_trdng_accnt_crrncy,
        financing_ratio,
        margin_type,
        margin_requirement,
        margin_fx_rate_bid,
        margin_fx_rate_ask,
        trade_margin_amount,
        trade_margin_currency,
        trd_mrgn_in_trdng_accnt_crrncy,
        trade_profit_loss,
        trade_profit_loss_currency,
        trd_pl_in_trdng_accnt_crrncy,
        trdng_accnt_custom_info,
        ta_cstm_info_vrtl_prtfl_cd,
        cp2_trading_account_id,
        cp2_trading_account_type,
        cp2_trading_account_codifier,
        cp2_custom_info,
        quote_id,
        quote_l1_bid,
        quote_l1_ask,
        quantity_fx_rate_id,
        margin_fx_rate_id,
        profit_loss_fx_rate_id,
        profit_loss_fx_rate_bid,
        profit_loss_fx_rate_ask,
        is_trd_of_cntrlld_ordr,
        reversed_order_id,
        mm_instrument_id,
        related_child_order_type,
        is_mandatory,
        requested_trade_close_order_id,
        related_parent_order_id,
        limit_price_condition,
        limit_trailing_distance,
        -- limit_trailing_best_price,
        limit_price,
        is_prdct_crrncy_in_frtnl_prts,
        quote_ask_price,
        quote_bid_price,
        trade_id_short_form,
        order_id_short_form,
        mm_account_id,
        trade_quantity,
        is_limit_trailing,
        normalised_trade_price,
        trade_quantity_currency,
        price_source,
        record_source,
        amount_fx_rate_id,
        amount_fx_rate,
        price_level,
        aggregated_price_quantity,
        quote_received_time,
        app_to_units,
        rollover_closing_trade_id,
        is_hedged
        /***** Begin Modification for V4.5 - BER774 *****/
        ,
        hdg_ext_trxn_id,
        hdg_milliways_seq_id,
        hdg_src,
        hdg_src_ref_id,
        hdg_trxn_type,
        hdg_execn_type,
        hdg_ext_trxn_time,
        hdg_trade_dt,
        hdg_ext_trade_dt,
        hdg_accnting_trade_dt,
        hdg_exec_broker_accnt_num,
        hdg_asset_class,
        hdg_instr_code_ext,
        hdg_expy_month_code,
        hdg_risk_bucket,
        hdg_cmc_trader,
        trade_comment,
        hdg_calc_commission,
        hdg_commission,
        hdg_is_risk_relevant,
        hdg_is_reg_repng_relvnt,
        hdg_cancln_ref,
        hdg_correction_ref,
        hdg_cross_ref,
        is_rollover_closing,
        price_offset_index,
        execution_started_time,
        trade_instrument_price,
        strike_price,
        cd_tenor,
        cd_state,
        cd_result,
        quote_depth_price,
        spread_l1_amount,
        spread_l1_amount_currency,
        binary_type,
        settle_time,
        tenor,
        strike_price_additional,
        trade_instrument_amount,
        reference_trade_price,
        ladder_quantities_override,
        --hdg_exec_broker_code,
        hdg_execution_commission,
        cmc_trade_instrument_price,
        --hdg_broker_code,
        --hdg_ext_broker_accnt_num
        hdg_spot_price,
        hdg_trade_settlement_date,
        hdg_redemption_date,
        hdg_current_coupon_date,
        hdg_accrued_interest_days,
        hdg_accrued_interest_amount,
        hdg_effctv_intrst_rate,
        hdg_effctv_intrst_base_amnt,
        hdg_afs_reserve_amount,
        hdg_opening_reference,
        hdg_client_order_link_id,
        hdg_crrnt_coupon_pymnt_date,
        hdg_redemption_payment_date,
        trade_time_2,
        trade_time_confirmation,
        hdg_report_source,
        hdg_original_currency,
        hdg_original_amount,
        hdg_crypto_inst_ccy_fx_rate,
        trade_price_offset,
        qt_is_frst_gd_in_cntns_trdng,
        hdg_trade_settlement_ref
      )
      VALUES
      (
        p_old_trade_record.trade_id,
        p_old_trade_record.platform,
        p_old_trade_record.logical_load_timestamp,
        p_old_trade_record.created_by,
        p_old_trade_record.create_timestamp,
        p_old_trade_record.updated_by,
        p_old_trade_record.update_timestamp,
        p_old_trade_record.effective_start_timestamp,
        p_effective_end_timestamp,
        p_action,
        SYSTIMESTAMP,
        p_old_trade_record.trading_account_id,
        p_old_trade_record.trading_account_type,
        p_old_trade_record.trading_account_codifier,
        p_old_trade_record.order_id,
        p_old_trade_record.product_instrument_code,
        p_old_trade_record.product_wrapper_code,
        p_old_trade_record.product_generation,
        p_old_trade_record.product_schema_code,
        p_old_trade_record.prdct_financing_ratio_max,
        p_old_trade_record.session_key,
        p_old_trade_record.booking_number,
        p_old_trade_record.cp1_cash_account_number,
        p_old_trade_record.cp2_cash_account_number,
        p_old_trade_record.publish_time,
        p_old_trade_record.event_time,
        p_old_trade_record.creation_time,
        p_old_trade_record.update_time,
        p_old_trade_record.creation_identity_token,
        p_old_trade_record.crtn_on_bhlf_of_idntty_tkn,
        p_old_trade_record.update_identity_token,
        p_old_trade_record.updt_on_bhlf_of_idntty_tkn,
        p_old_trade_record.version_number,
        p_old_trade_record.is_deleted,
        p_old_trade_record.visit_id,
        p_old_trade_record.channel_id,
        p_old_trade_record.request_id,
        p_old_trade_record.reduced_trade_id,
        p_old_trade_record.reversed_trade_id,
        p_old_trade_record.business_date,
        p_old_trade_record.reporting_date,
        p_old_trade_record.trade_time,
        p_old_trade_record.order_type,
        p_old_trade_record.controlled_order_type,
        p_old_trade_record.trading_account_function,
        p_old_trade_record.trading_account_currency,
        p_old_trade_record.product_point_multiplier,
        p_old_trade_record.product_fractional_part_ratio,
        p_old_trade_record.product_currency,
        p_old_trade_record.is_primary,
        p_old_trade_record.is_late_deal,
        p_old_trade_record.mm_value_date,
        p_old_trade_record.direction,
        p_old_trade_record.direction_multiplier,
        p_old_trade_record.quoted_l1_ask_price,
        p_old_trade_record.quoted_l1_bid_price,
        p_old_trade_record.trade_price,
        p_old_trade_record.price_designator,
        p_old_trade_record.quantity_designator,
        p_old_trade_record.normalised_quantity,
        p_old_trade_record.normalised_quantity_currency,
        p_old_trade_record.quantity_fx_rate_bid,
        p_old_trade_record.quantity_fx_rate_ask,
        p_old_trade_record.trade_amount,
        p_old_trade_record.trade_amount_currency,
        p_old_trade_record.trd_amnt_in_trdng_accnt_crrncy,
        p_old_trade_record.financing_ratio,
        p_old_trade_record.margin_type,
        p_old_trade_record.margin_requirement,
        p_old_trade_record.margin_fx_rate_bid,
        p_old_trade_record.margin_fx_rate_ask,
        p_old_trade_record.trade_margin_amount,
        p_old_trade_record.trade_margin_currency,
        p_old_trade_record.trd_mrgn_in_trdng_accnt_crrncy,
        p_old_trade_record.trade_profit_loss,
        p_old_trade_record.trade_profit_loss_currency,
        p_old_trade_record.trd_pl_in_trdng_accnt_crrncy,
        p_old_trade_record.trdng_accnt_custom_info,
        p_old_trade_record.ta_cstm_info_vrtl_prtfl_cd,
        p_old_trade_record.cp2_trading_account_id,
        p_old_trade_record.cp2_trading_account_type,
        p_old_trade_record.cp2_trading_account_codifier,
        p_old_trade_record.cp2_custom_info,
        p_old_trade_record.quote_id,
        p_old_trade_record.quote_l1_bid,
        p_old_trade_record.quote_l1_ask,
        p_old_trade_record.quantity_fx_rate_id,
        p_old_trade_record.margin_fx_rate_id,
        p_old_trade_record.profit_loss_fx_rate_id,
        p_old_trade_record.profit_loss_fx_rate_bid,
        p_old_trade_record.profit_loss_fx_rate_ask,
        p_old_trade_record.is_trd_of_cntrlld_ordr,
        p_old_trade_record.reversed_order_id,
        p_old_trade_record.mm_instrument_id,
        p_old_trade_record.related_child_order_type,
        p_old_trade_record.is_mandatory,
        p_old_trade_record.requested_trade_close_order_id,
        p_old_trade_record.related_parent_order_id,
        p_old_trade_record.limit_price_condition,
        p_old_trade_record.limit_trailing_distance,
        --    p_old_trade_record.limit_trailing_best_price,
        p_old_trade_record.limit_price,
        p_old_trade_record.is_prdct_crrncy_in_frtnl_prts,
        p_old_trade_record.quote_ask_price,
        p_old_trade_record.quote_bid_price,
        p_old_trade_record.trade_id_short_form,
        p_old_trade_record.order_id_short_form,
        p_old_trade_record.mm_account_id,
        p_old_trade_record.trade_quantity,
        p_old_trade_record.is_limit_trailing,
        p_old_trade_record.normalised_trade_price,
        p_old_trade_record.trade_quantity_currency,
        p_old_trade_record.price_source,
        p_old_trade_record.record_source,
        p_old_trade_record.amount_fx_rate_id,
        p_old_trade_record.amount_fx_rate,
        p_old_trade_record.price_level,
        p_old_trade_record.aggregated_price_quantity,
        p_old_trade_record.quote_received_time,
        p_old_trade_record.app_to_units,
        p_old_trade_record.rollover_closing_trade_id,
        p_old_trade_record.is_hedged
        /***** Begin Modification for V4.5 - BER774 *****/
        ,
        p_old_trade_record.hdg_ext_trxn_id,
        p_old_trade_record.hdg_milliways_seq_id,
        p_old_trade_record.hdg_src,
        p_old_trade_record.hdg_src_ref_id,
        p_old_trade_record.hdg_trxn_type,
        p_old_trade_record.hdg_execn_type,
        p_old_trade_record.hdg_ext_trxn_time,
        p_old_trade_record.hdg_trade_dt,
        p_old_trade_record.hdg_ext_trade_dt,
        p_old_trade_record.hdg_accnting_trade_dt,
        p_old_trade_record.hdg_exec_broker_accnt_num,
        p_old_trade_record.hdg_asset_class,
        p_old_trade_record.hdg_instr_code_ext,
        p_old_trade_record.hdg_expy_month_code,
        p_old_trade_record.hdg_risk_bucket,
        p_old_trade_record.hdg_cmc_trader,
        p_old_trade_record.trade_comment,
        p_old_trade_record.hdg_calc_commission,
        p_old_trade_record.hdg_commission,
        p_old_trade_record.hdg_is_risk_relevant,
        p_old_trade_record.hdg_is_reg_repng_relvnt,
        p_old_trade_record.hdg_cancln_ref,
        p_old_trade_record.hdg_correction_ref,
        p_old_trade_record.hdg_cross_ref,
        p_old_trade_record.is_rollover_closing,
        p_old_trade_record.price_offset_index,
        p_old_trade_record.execution_started_time,
        p_old_trade_record.trade_instrument_price,
        p_old_trade_record.strike_price,
        p_old_trade_record.cd_tenor,
        p_old_trade_record.cd_state,
        p_old_trade_record.cd_result,
        p_old_trade_record.quote_depth_price,
        p_old_trade_record.spread_l1_amount,
        p_old_trade_record.spread_l1_amount_currency,
        p_old_trade_record.binary_type,
        p_old_trade_record.settle_time,
        p_old_trade_record.tenor,
        p_old_trade_record.strike_price_additional,
        p_old_trade_record.trade_instrument_amount,
        p_old_trade_record.reference_trade_price,
        p_old_trade_record.ladder_quantities_override,
        --p_old_trade_record.hdg_exec_broker_code,
        p_old_trade_record.hdg_execution_commission,
        p_old_trade_record.cmc_trade_instrument_price,
        --p_old_trade_record.hdg_broker_code,
        --p_old_trade_record.hdg_ext_broker_accnt_num
        p_old_trade_record.hdg_spot_price,
        p_old_trade_record.hdg_trade_settlement_date,
        p_old_trade_record.hdg_redemption_date,
        p_old_trade_record.hdg_current_coupon_date,
        p_old_trade_record.hdg_accrued_interest_days,
        p_old_trade_record.hdg_accrued_interest_amount,
        p_old_trade_record.hdg_effctv_intrst_rate,
        p_old_trade_record.hdg_effctv_intrst_base_amnt,
        p_old_trade_record.hdg_afs_reserve_amount,
        p_old_trade_record.hdg_opening_reference,
        p_old_trade_record.hdg_client_order_link_id,
		    p_old_trade_record.hdg_crrnt_coupon_pymnt_date,
        p_old_trade_record.hdg_redemption_payment_date,
        p_old_trade_record.trade_time_2,
        p_old_trade_record.trade_time_confirmation,
        p_old_trade_record.hdg_report_source,
        p_old_trade_record.hdg_original_currency,
        p_old_trade_record.hdg_original_amount,
        p_old_trade_record.hdg_crypto_inst_ccy_fx_rate,
        p_old_trade_record.trade_price_offset,
        p_old_trade_record.qt_is_frst_gd_in_cntns_trdng,
        p_old_trade_record.hdg_trade_settlement_ref
      );
  exception
  WHEN dup_val_on_index THEN
    --
    --
    -- This would occur in the case when the same message is replayed
    --
    -- Possible ways in which message gets replayed
    -- Request Response
    -- Upstream publishes for manual updates
    -- Message replayed
    --
   UPDATE trades_h
      SET logical_load_timestamp         = p_old_trade_record.logical_load_timestamp,
          updated_by                     = p_old_trade_record.updated_by,
          update_timestamp               = p_old_trade_record.update_timestamp,
          effective_start_timestamp      = p_old_trade_record.effective_start_timestamp,
          trading_account_id             = p_old_trade_record.trading_account_id,
          trading_account_type           = p_old_trade_record.trading_account_type,
          trading_account_codifier       = p_old_trade_record.trading_account_codifier,
          order_id                       = p_old_trade_record.order_id,
          product_instrument_code        = p_old_trade_record.product_instrument_code,
          product_wrapper_code           = p_old_trade_record.product_wrapper_code,
          product_generation             = p_old_trade_record.product_generation,
          product_schema_code            = p_old_trade_record.product_schema_code,
          prdct_financing_ratio_max      = p_old_trade_record.prdct_financing_ratio_max,
          session_key                    = p_old_trade_record.session_key,
          booking_number                 = p_old_trade_record.booking_number,
          cp1_cash_account_number        = p_old_trade_record.cp1_cash_account_number,
          cp2_cash_account_number        = p_old_trade_record.cp2_cash_account_number,
          publish_time                   = p_old_trade_record.publish_time,
          event_time                     = p_old_trade_record.event_time,
          creation_time                  = p_old_trade_record.creation_time,
          update_time                    = p_old_trade_record.update_time,
          creation_identity_token        = p_old_trade_record.creation_identity_token,
          crtn_on_bhlf_of_idntty_tkn     = p_old_trade_record.crtn_on_bhlf_of_idntty_tkn,
          update_identity_token          = p_old_trade_record.update_identity_token,
          updt_on_bhlf_of_idntty_tkn     = p_old_trade_record.updt_on_bhlf_of_idntty_tkn,
          version_number                 = p_old_trade_record.version_number,
          is_deleted                     = p_old_trade_record.is_deleted,
          visit_id                       = p_old_trade_record.visit_id,
          channel_id                     = p_old_trade_record.channel_id,
          request_id                     = p_old_trade_record.request_id,
          reduced_trade_id               = p_old_trade_record.reduced_trade_id,
          reversed_trade_id              = p_old_trade_record.reversed_trade_id,
          business_date                  = p_old_trade_record.business_date,
          reporting_date                 = p_old_trade_record.reporting_date,
          trade_time                     = p_old_trade_record.trade_time,
          order_type                     = p_old_trade_record.order_type,
          controlled_order_type          = p_old_trade_record.controlled_order_type,
          trading_account_function       = p_old_trade_record.trading_account_function,
          trading_account_currency       = p_old_trade_record.trading_account_currency,
          product_point_multiplier       = p_old_trade_record.product_point_multiplier,
          product_fractional_part_ratio  = p_old_trade_record.product_fractional_part_ratio,
          product_currency               = p_old_trade_record.product_currency,
          is_primary                     = p_old_trade_record.is_primary,
          is_late_deal                   = p_old_trade_record.is_late_deal,
          mm_value_date                  = p_old_trade_record.mm_value_date,
          direction                      = p_old_trade_record.direction,
          direction_multiplier           = p_old_trade_record.direction_multiplier,
          quoted_l1_ask_price            = p_old_trade_record.quoted_l1_ask_price,
          quoted_l1_bid_price            = p_old_trade_record.quoted_l1_bid_price,
          trade_price                    = p_old_trade_record.trade_price,
          price_designator               = p_old_trade_record.price_designator,
          quantity_designator            = p_old_trade_record.quantity_designator,
          normalised_quantity            = p_old_trade_record.normalised_quantity,
          normalised_quantity_currency   = p_old_trade_record.normalised_quantity_currency,
          quantity_fx_rate_bid           = p_old_trade_record.quantity_fx_rate_bid,
          quantity_fx_rate_ask           = p_old_trade_record.quantity_fx_rate_ask,
          trade_amount                   = p_old_trade_record.trade_amount,
          trade_amount_currency          = p_old_trade_record.trade_amount_currency,
          trd_amnt_in_trdng_accnt_crrncy = p_old_trade_record.trd_amnt_in_trdng_accnt_crrncy,
          financing_ratio                = p_old_trade_record.financing_ratio,
          margin_type                    = p_old_trade_record.margin_type,
          margin_requirement             = p_old_trade_record.margin_requirement,
          margin_fx_rate_bid             = p_old_trade_record.margin_fx_rate_bid,
          margin_fx_rate_ask             = p_old_trade_record.margin_fx_rate_ask,
          trade_margin_amount            = p_old_trade_record.trade_margin_amount,
          trade_margin_currency          = p_old_trade_record.trade_margin_currency,
          trd_mrgn_in_trdng_accnt_crrncy = p_old_trade_record.trd_mrgn_in_trdng_accnt_crrncy,
          trade_profit_loss              = p_old_trade_record.trade_profit_loss,
          trade_profit_loss_currency     = p_old_trade_record.trade_profit_loss_currency,
          trd_pl_in_trdng_accnt_crrncy   = p_old_trade_record.trd_pl_in_trdng_accnt_crrncy,
          trdng_accnt_custom_info        = p_old_trade_record.trdng_accnt_custom_info,
          ta_cstm_info_vrtl_prtfl_cd     = p_old_trade_record.ta_cstm_info_vrtl_prtfl_cd,
          cp2_trading_account_id         = p_old_trade_record.cp2_trading_account_id,
          cp2_trading_account_type       = p_old_trade_record.cp2_trading_account_type,
          cp2_trading_account_codifier   = p_old_trade_record.cp2_trading_account_codifier,
          cp2_custom_info                = p_old_trade_record.cp2_custom_info,
          quote_id                       = p_old_trade_record.quote_id,
          quote_l1_bid                   = p_old_trade_record.quote_l1_bid,
          quote_l1_ask                   = p_old_trade_record.quote_l1_ask,
          quantity_fx_rate_id            = p_old_trade_record.quantity_fx_rate_id,
          margin_fx_rate_id              = p_old_trade_record.margin_fx_rate_id,
          profit_loss_fx_rate_id         = p_old_trade_record.profit_loss_fx_rate_id,
          profit_loss_fx_rate_bid        = p_old_trade_record.profit_loss_fx_rate_bid,
          profit_loss_fx_rate_ask        = p_old_trade_record.profit_loss_fx_rate_ask,
          is_trd_of_cntrlld_ordr         = p_old_trade_record.is_trd_of_cntrlld_ordr,
          reversed_order_id              = p_old_trade_record.reversed_order_id,
          mm_instrument_id               = p_old_trade_record.mm_instrument_id,
          related_child_order_type       = p_old_trade_record.related_child_order_type,
          is_mandatory                   = p_old_trade_record.is_mandatory,
          requested_trade_close_order_id = p_old_trade_record.requested_trade_close_order_id,
          related_parent_order_id        = p_old_trade_record.related_parent_order_id,
          limit_price_condition          = p_old_trade_record.limit_price_condition,
          limit_trailing_distance        = p_old_trade_record.limit_trailing_distance,
          --  limit_trailing_best_price      = p_old_trade_record.limit_trailing_best_price,
          limit_price                    = p_old_trade_record.limit_price,
          is_prdct_crrncy_in_frtnl_prts  = p_old_trade_record.is_prdct_crrncy_in_frtnl_prts,
          quote_ask_price                = p_old_trade_record.quote_ask_price,
          quote_bid_price                = p_old_trade_record.quote_bid_price,
          trade_id_short_form            = p_old_trade_record.trade_id_short_form,
          order_id_short_form            = p_old_trade_record.order_id_short_form,
          mm_account_id                  = p_old_trade_record.mm_account_id,
          trade_quantity                 = p_old_trade_record.trade_quantity,
          is_limit_trailing              = p_old_trade_record.is_limit_trailing,
          normalised_trade_price         = p_old_trade_record.normalised_trade_price,
          trade_quantity_currency        = p_old_trade_record.trade_quantity_currency,
          price_source                   = p_old_trade_record.price_source,
          record_source                  = p_old_trade_record.record_source,
          amount_fx_rate_id              = p_old_trade_record.amount_fx_rate_id,
          amount_fx_rate                 = p_old_trade_record.amount_fx_rate,
          price_level                    = p_old_trade_record.price_level,
          aggregated_price_quantity      = p_old_trade_record.aggregated_price_quantity,
          quote_received_time            = p_old_trade_record.quote_received_time,
          app_to_units                   = p_old_trade_record.app_to_units,
          rollover_closing_trade_id      = p_old_trade_record.rollover_closing_trade_id,
          is_hedged                      = p_old_trade_record.is_hedged,
          hdg_ext_trxn_id                = p_old_trade_record.hdg_ext_trxn_id,
          hdg_milliways_seq_id           = p_old_trade_record.hdg_milliways_seq_id,
          hdg_src                        = p_old_trade_record.hdg_src,
          hdg_src_ref_id                 = p_old_trade_record.hdg_src_ref_id,
          hdg_trxn_type                  = p_old_trade_record.hdg_trxn_type,
          hdg_execn_type                 = p_old_trade_record.hdg_execn_type,
          hdg_ext_trxn_time              = p_old_trade_record.hdg_ext_trxn_time,
          hdg_trade_dt                   = p_old_trade_record.hdg_trade_dt,
          hdg_ext_trade_dt               = p_old_trade_record.hdg_ext_trade_dt,
          hdg_accnting_trade_dt          = p_old_trade_record.hdg_accnting_trade_dt,
          hdg_exec_broker_accnt_num      = p_old_trade_record.hdg_exec_broker_accnt_num,
          hdg_asset_class                = p_old_trade_record.hdg_asset_class,
          hdg_instr_code_ext             = p_old_trade_record.hdg_instr_code_ext,
          hdg_expy_month_code            = p_old_trade_record.hdg_expy_month_code,
          hdg_risk_bucket                = p_old_trade_record.hdg_risk_bucket,
          hdg_cmc_trader                 = p_old_trade_record.hdg_cmc_trader,
          trade_comment                  = p_old_trade_record.trade_comment,
          hdg_calc_commission            = p_old_trade_record.hdg_calc_commission,
          hdg_commission                 = p_old_trade_record.hdg_commission,
          hdg_is_risk_relevant           = p_old_trade_record.hdg_is_risk_relevant,
          hdg_is_reg_repng_relvnt        = p_old_trade_record.hdg_is_reg_repng_relvnt,
          hdg_cancln_ref                 = p_old_trade_record.hdg_cancln_ref,
          hdg_correction_ref             = p_old_trade_record.hdg_correction_ref,
          hdg_cross_ref                  = p_old_trade_record.hdg_cross_ref,
          is_rollover_closing            = p_old_trade_record.is_rollover_closing,
          price_offset_index             = p_old_trade_record.price_offset_index,
          execution_started_time         = p_old_trade_record.execution_started_time,
          trade_instrument_price         = p_old_trade_record.trade_instrument_price,
          strike_price                   = p_old_trade_record.strike_price,
          cd_tenor                       = p_old_trade_record.cd_tenor,
          cd_state                       = p_old_trade_record.cd_state,
          cd_result                      = p_old_trade_record.cd_result,
          quote_depth_price              = p_old_trade_record.quote_depth_price,
          spread_l1_amount               = p_old_trade_record.spread_l1_amount,
          spread_l1_amount_currency      = p_old_trade_record.spread_l1_amount_currency,
          binary_type                    = p_old_trade_record.binary_type,
          settle_time                    = p_old_trade_record.settle_time,
          tenor                          = p_old_trade_record.tenor,
          strike_price_additional        = p_old_trade_record.strike_price_additional,
          trade_instrument_amount        = p_old_trade_record.trade_instrument_amount,
          reference_trade_price          = p_old_trade_record.reference_trade_price,
          ladder_quantities_override     = p_old_trade_record.ladder_quantities_override,
          --hdg_exec_broker_code         = p_old_trade_record.hdg_exec_broker_code,
          hdg_execution_commission       = p_old_trade_record.hdg_execution_commission,
          cmc_trade_instrument_price     = p_old_trade_record.cmc_trade_instrument_price,
          --hdg_broker_code              = p_old_trade_record.hdg_broker_code,
          --hdg_ext_broker_accnt_num     = p_old_trade_record.hdg_ext_broker_accnt_num
          hdg_spot_price                 = p_old_trade_record.hdg_spot_price,
          hdg_trade_settlement_date      = p_old_trade_record.hdg_trade_settlement_date,
          hdg_redemption_date            = p_old_trade_record.hdg_redemption_date,
          hdg_current_coupon_date        = p_old_trade_record.hdg_current_coupon_date,
          hdg_accrued_interest_days      = p_old_trade_record.hdg_accrued_interest_days,
          hdg_accrued_interest_amount    = p_old_trade_record.hdg_accrued_interest_amount,
          hdg_effctv_intrst_rate         = p_old_trade_record.hdg_effctv_intrst_rate,
          hdg_effctv_intrst_base_amnt    = p_old_trade_record.hdg_effctv_intrst_base_amnt,
          hdg_afs_reserve_amount         = p_old_trade_record.hdg_afs_reserve_amount,
          hdg_opening_reference          = p_old_trade_record.hdg_opening_reference,
          hdg_client_order_link_id       = p_old_trade_record.hdg_client_order_link_id,
          hdg_crrnt_coupon_pymnt_date    = p_old_trade_record.hdg_crrnt_coupon_pymnt_date,
          hdg_redemption_payment_date    = p_old_trade_record.hdg_redemption_payment_date,
          trade_time_2                   = p_old_trade_record.trade_time_2,
          trade_time_confirmation        = p_old_trade_record.trade_time_confirmation,
          hdg_report_source              = p_old_trade_record.hdg_report_source,
          hdg_original_currency          = p_old_trade_record.hdg_original_currency,
          hdg_original_amount            = p_old_trade_record.hdg_original_amount,
          hdg_crypto_inst_ccy_fx_rate    = p_old_trade_record.hdg_crypto_inst_ccy_fx_rate,
          trade_price_offset             = p_old_trade_record.trade_price_offset,
          qt_is_frst_gd_in_cntns_trdng   = p_old_trade_record.qt_is_frst_gd_in_cntns_trdng,
          hdg_trade_settlement_ref       = p_old_trade_record.hdg_trade_settlement_ref
    WHERE trade_id                                                                              = p_old_trade_record.trade_id
    AND platform                                                                                = p_old_trade_record.platform
    AND effective_start_timestamp                                                               = p_old_trade_record.effective_start_timestamp
    AND (nrg_common.has_value_changed(trading_account_id,p_old_trade_record.trading_account_id) = 1
    OR nrg_common.has_value_changed(trading_account_type,p_old_trade_record.trading_account_type) = 1
    OR nrg_common.has_value_changed(trading_account_codifier,p_old_trade_record.trading_account_codifier) = 1
    OR nrg_common.has_value_changed(order_id,p_old_trade_record.order_id) = 1
    OR nrg_common.has_value_changed(product_instrument_code,p_old_trade_record.product_instrument_code) = 1
    OR nrg_common.has_value_changed(product_wrapper_code,p_old_trade_record.product_wrapper_code)                     = 1
    OR nrg_common.has_value_changed(product_generation,p_old_trade_record.product_generation)                         = 1
    OR nrg_common.has_value_changed(product_schema_code,p_old_trade_record.product_schema_code)                       = 1
    OR nrg_common.has_value_changed(prdct_financing_ratio_max,p_old_trade_record.prdct_financing_ratio_max)           = 1
    OR nrg_common.has_value_changed(session_key,p_old_trade_record.session_key)                                       = 1
    OR nrg_common.has_value_changed(booking_number,p_old_trade_record.booking_number)                                 = 1
    OR nrg_common.has_value_changed(cp1_cash_account_number,p_old_trade_record.cp1_cash_account_number)               = 1
    OR nrg_common.has_value_changed(cp2_cash_account_number,p_old_trade_record.cp2_cash_account_number)               = 1
    OR nrg_common.has_value_changed(publish_time,p_old_trade_record.publish_time)                                     = 1
    OR nrg_common.has_value_changed(event_time,p_old_trade_record.event_time)                                         = 1
    OR nrg_common.has_value_changed(creation_time,p_old_trade_record.creation_time)                                   = 1
    OR nrg_common.has_value_changed(update_time,p_old_trade_record.update_time)                                       = 1
    OR nrg_common.has_value_changed(creation_identity_token,p_old_trade_record.creation_identity_token)               = 1
    OR nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_old_trade_record.crtn_on_bhlf_of_idntty_tkn)         = 1
    OR nrg_common.has_value_changed(update_identity_token,p_old_trade_record.update_identity_token)                   = 1
    OR nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_old_trade_record.updt_on_bhlf_of_idntty_tkn)         = 1
    OR nrg_common.has_value_changed(version_number,p_old_trade_record.version_number)                                 = 1
    OR nrg_common.has_value_changed(is_deleted,p_old_trade_record.is_deleted)                                         = 1
    OR nrg_common.has_value_changed(visit_id,p_old_trade_record.visit_id)                                             = 1
    OR nrg_common.has_value_changed(channel_id,p_old_trade_record.channel_id)                                         = 1
    OR nrg_common.has_value_changed(request_id,p_old_trade_record.request_id)                                         = 1
    OR nrg_common.has_value_changed(reduced_trade_id,p_old_trade_record.reduced_trade_id)                             = 1
    OR nrg_common.has_value_changed(reversed_trade_id,p_old_trade_record.reversed_trade_id)                           = 1
    OR nrg_common.has_value_changed(business_date,p_old_trade_record.business_date)                                   = 1
    OR nrg_common.has_value_changed(reporting_date, p_old_trade_record.reporting_date)                                = 1
    OR nrg_common.has_value_changed(trade_time,p_old_trade_record.trade_time)                                         = 1
    OR nrg_common.has_value_changed(order_type,p_old_trade_record.order_type)                                         = 1
    OR nrg_common.has_value_changed(controlled_order_type,p_old_trade_record.controlled_order_type)                   = 1
    OR nrg_common.has_value_changed(trading_account_function,p_old_trade_record.trading_account_function)             = 1
    OR nrg_common.has_value_changed(trading_account_currency,p_old_trade_record.trading_account_currency)             = 1
    OR nrg_common.has_value_changed(product_point_multiplier,p_old_trade_record.product_point_multiplier)             = 1
    OR nrg_common.has_value_changed(product_fractional_part_ratio,p_old_trade_record.product_fractional_part_ratio)   = 1
    OR nrg_common.has_value_changed(product_currency,p_old_trade_record.product_currency)                             = 1
    OR nrg_common.has_value_changed(is_primary,p_old_trade_record.is_primary)                                         = 1
    OR nrg_common.has_value_changed(is_late_deal,p_old_trade_record.is_late_deal)                                     = 1
    OR nrg_common.has_value_changed(mm_value_date,p_old_trade_record.mm_value_date)                                   = 1
    OR nrg_common.has_value_changed(direction,p_old_trade_record.direction)                                           = 1
    OR nrg_common.has_value_changed(direction_multiplier,p_old_trade_record.direction_multiplier)                     = 1
    OR nrg_common.has_value_changed(quoted_l1_ask_price,p_old_trade_record.quoted_l1_ask_price)                       = 1
    OR nrg_common.has_value_changed(quoted_l1_bid_price,p_old_trade_record.quoted_l1_bid_price)                       = 1
    OR nrg_common.has_value_changed(trade_price,p_old_trade_record.trade_price)                                       = 1
    OR nrg_common.has_value_changed(price_designator,p_old_trade_record.price_designator)                             = 1
    OR nrg_common.has_value_changed(quantity_designator,p_old_trade_record.quantity_designator)                       = 1
    OR nrg_common.has_value_changed(normalised_quantity,p_old_trade_record.normalised_quantity)                       = 1
    OR nrg_common.has_value_changed(normalised_quantity_currency,p_old_trade_record.normalised_quantity_currency)     = 1
    OR nrg_common.has_value_changed(quantity_fx_rate_bid,p_old_trade_record.quantity_fx_rate_bid)                     = 1
    OR nrg_common.has_value_changed(quantity_fx_rate_ask,p_old_trade_record.quantity_fx_rate_ask)                     = 1
    OR nrg_common.has_value_changed(trade_amount,p_old_trade_record.trade_amount)                                     = 1
    OR nrg_common.has_value_changed(trade_amount_currency,p_old_trade_record.trade_amount_currency)                   = 1
    OR nrg_common.has_value_changed(trd_amnt_in_trdng_accnt_crrncy,p_old_trade_record.trd_amnt_in_trdng_accnt_crrncy) = 1
    OR nrg_common.has_value_changed(financing_ratio,p_old_trade_record.financing_ratio)                               = 1
    OR nrg_common.has_value_changed(margin_type,p_old_trade_record.margin_type)                                       = 1
    OR nrg_common.has_value_changed(margin_requirement,p_old_trade_record.margin_requirement)                         = 1
    OR nrg_common.has_value_changed(margin_fx_rate_bid,p_old_trade_record.margin_fx_rate_bid)                         = 1
    OR nrg_common.has_value_changed(margin_fx_rate_ask,p_old_trade_record.margin_fx_rate_ask)                         = 1
    OR nrg_common.has_value_changed(trade_margin_amount,p_old_trade_record.trade_margin_amount)                       = 1
    OR nrg_common.has_value_changed(trade_margin_currency,p_old_trade_record.trade_margin_currency)                   = 1
    OR nrg_common.has_value_changed(trd_mrgn_in_trdng_accnt_crrncy,p_old_trade_record.trd_mrgn_in_trdng_accnt_crrncy) = 1
    OR nrg_common.has_value_changed(trade_profit_loss,p_old_trade_record.trade_profit_loss)                           = 1
    OR nrg_common.has_value_changed(trade_profit_loss_currency,p_old_trade_record.trade_profit_loss_currency)         = 1
    OR nrg_common.has_value_changed(trd_pl_in_trdng_accnt_crrncy,p_old_trade_record.trd_pl_in_trdng_accnt_crrncy)     = 1
    OR nrg_common.has_value_changed(trdng_accnt_custom_info,p_old_trade_record.trdng_accnt_custom_info)               = 1
    OR nrg_common.has_value_changed(ta_cstm_info_vrtl_prtfl_cd,p_old_trade_record.ta_cstm_info_vrtl_prtfl_cd)         = 1
    OR nrg_common.has_value_changed(cp2_trading_account_id,p_old_trade_record.cp2_trading_account_id)                 = 1
    OR nrg_common.has_value_changed(cp2_trading_account_type,p_old_trade_record.cp2_trading_account_type)             = 1
    OR nrg_common.has_value_changed(cp2_trading_account_codifier,p_old_trade_record.cp2_trading_account_codifier)     = 1
    OR nrg_common.has_value_changed(cp2_custom_info,p_old_trade_record.cp2_custom_info)                               = 1
    OR nrg_common.has_value_changed(quote_id,p_old_trade_record.quote_id)                                             = 1
    OR nrg_common.has_value_changed(quote_l1_bid,p_old_trade_record.quote_l1_bid)                                     = 1
    OR nrg_common.has_value_changed(quote_l1_ask,p_old_trade_record.quote_l1_ask)                                     = 1
    OR nrg_common.has_value_changed(quantity_fx_rate_id,p_old_trade_record.quantity_fx_rate_id)                       = 1
    OR nrg_common.has_value_changed(margin_fx_rate_id,p_old_trade_record.margin_fx_rate_id)                           = 1
    OR nrg_common.has_value_changed(profit_loss_fx_rate_id,p_old_trade_record.profit_loss_fx_rate_id)                 = 1
    OR nrg_common.has_value_changed(profit_loss_fx_rate_bid,p_old_trade_record.profit_loss_fx_rate_bid)               = 1
    OR nrg_common.has_value_changed(profit_loss_fx_rate_ask,p_old_trade_record.profit_loss_fx_rate_ask)               = 1
    OR nrg_common.has_value_changed(is_trd_of_cntrlld_ordr,p_old_trade_record.is_trd_of_cntrlld_ordr)                 = 1
    OR nrg_common.has_value_changed(reversed_order_id,p_old_trade_record.reversed_order_id)                           = 1
    OR nrg_common.has_value_changed(mm_instrument_id,p_old_trade_record.mm_instrument_id)                             = 1
    OR nrg_common.has_value_changed(related_child_order_type,p_old_trade_record.related_child_order_type)             = 1
    OR nrg_common.has_value_changed(is_mandatory,p_old_trade_record.is_mandatory)                                     = 1
    OR nrg_common.has_value_changed(requested_trade_close_order_id,p_old_trade_record.requested_trade_close_order_id) = 1
    OR nrg_common.has_value_changed(related_parent_order_id,p_old_trade_record.related_parent_order_id)               = 1
    OR nrg_common.has_value_changed(limit_price_condition,p_old_trade_record.limit_price_condition)                   = 1
    OR nrg_common.has_value_changed(limit_trailing_distance,p_old_trade_record.limit_trailing_distance)               = 1
    OR nrg_common.has_value_changed(limit_price,p_old_trade_record.limit_price)                                       = 1
    OR nrg_common.has_value_changed(is_prdct_crrncy_in_frtnl_prts, p_old_trade_record.is_prdct_crrncy_in_frtnl_prts)  = 1
    OR nrg_common.has_value_changed(quote_ask_price,p_old_trade_record.quote_ask_price)                               = 1
    OR nrg_common.has_value_changed(quote_bid_price,p_old_trade_record.quote_bid_price)                               = 1
    OR nrg_common.has_value_changed(order_id_short_form, p_old_trade_record.order_id_short_form)                      = 1
    OR nrg_common.has_value_changed(mm_account_id, p_old_trade_record.mm_account_id)                                  = 1
    OR nrg_common.has_value_changed(trade_quantity, p_old_trade_record.trade_quantity)                                = 1
    OR nrg_common.has_value_changed(is_limit_trailing, p_old_trade_record.is_limit_trailing)                          = 1
    OR nrg_common.has_value_changed(normalised_trade_price, p_old_trade_record.normalised_trade_price)                = 1
    OR nrg_common.has_value_changed(trade_quantity_currency, p_old_trade_record.trade_quantity_currency)              = 1
    OR nrg_common.has_value_changed(price_source, p_old_trade_record.price_source)                                    = 1
    OR nrg_common.has_value_changed(record_source, p_old_trade_record.record_source)                                  = 1
    OR nrg_common.has_value_changed(amount_fx_rate_id, p_old_trade_record.amount_fx_rate_id)                          = 1
    OR nrg_common.has_value_changed(amount_fx_rate, p_old_trade_record.amount_fx_rate)                                = 1
    OR nrg_common.has_value_changed(price_level, p_old_trade_record.price_level)                                      = 1
    OR nrg_common.has_value_changed(aggregated_price_quantity, p_old_trade_record.aggregated_price_quantity)          = 1
    OR nrg_common.has_value_changed(quote_received_time, p_old_trade_record.quote_received_time)                      = 1
    OR nrg_common.has_value_changed(app_to_units, p_old_trade_record.app_to_units)                                    = 1
    OR nrg_common.has_value_changed(rollover_closing_trade_id, p_old_trade_record.rollover_closing_trade_id)          = 1
    OR nrg_common.has_value_changed(is_hedged, p_old_trade_record.is_hedged)                                          = 1
    OR nrg_common.has_value_changed(hdg_ext_trxn_id,p_old_trade_record.hdg_ext_trxn_id)                               = 1
    OR nrg_common.has_value_changed(hdg_milliways_seq_id,p_old_trade_record.hdg_milliways_seq_id)                     = 1
    OR nrg_common.has_value_changed(hdg_src,p_old_trade_record.hdg_src)                                               = 1
    OR nrg_common.has_value_changed(hdg_src_ref_id,p_old_trade_record.hdg_src_ref_id)                                 = 1
    OR nrg_common.has_value_changed(hdg_trxn_type,p_old_trade_record.hdg_trxn_type)                                   = 1
    OR nrg_common.has_value_changed(hdg_execn_type,p_old_trade_record.hdg_execn_type)                                 = 1
    OR nrg_common.has_value_changed(hdg_ext_trxn_time,p_old_trade_record.hdg_ext_trxn_time)                           = 1
    OR nrg_common.has_value_changed(hdg_trade_dt,p_old_trade_record.hdg_trade_dt)                                     = 1
    OR nrg_common.has_value_changed(hdg_ext_trade_dt,p_old_trade_record.hdg_ext_trade_dt)                             = 1
    OR nrg_common.has_value_changed(hdg_accnting_trade_dt,p_old_trade_record.hdg_accnting_trade_dt)                   = 1
    OR nrg_common.has_value_changed(hdg_exec_broker_accnt_num,p_old_trade_record.hdg_exec_broker_accnt_num)           = 1
    OR nrg_common.has_value_changed(hdg_asset_class,p_old_trade_record.hdg_asset_class)                               = 1
    OR nrg_common.has_value_changed(hdg_instr_code_ext,p_old_trade_record.hdg_instr_code_ext)                         = 1
    OR nrg_common.has_value_changed(hdg_expy_month_code,p_old_trade_record.hdg_expy_month_code)                       = 1
    OR nrg_common.has_value_changed(hdg_risk_bucket,p_old_trade_record.hdg_risk_bucket)                               = 1
    OR nrg_common.has_value_changed(hdg_cmc_trader,p_old_trade_record.hdg_cmc_trader)                                 = 1
    OR nrg_common.has_value_changed(trade_comment,p_old_trade_record.trade_comment)                                   = 1
    OR nrg_common.has_value_changed(hdg_calc_commission,p_old_trade_record.hdg_calc_commission)                       = 1
    OR nrg_common.has_value_changed(hdg_commission,p_old_trade_record.hdg_commission)                                 = 1
    OR nrg_common.has_value_changed(hdg_is_risk_relevant,p_old_trade_record.hdg_is_risk_relevant)                     = 1
    OR nrg_common.has_value_changed(hdg_is_reg_repng_relvnt,p_old_trade_record.hdg_is_reg_repng_relvnt)               = 1
    OR nrg_common.has_value_changed(hdg_cancln_ref,p_old_trade_record.hdg_cancln_ref)                                 = 1
    OR nrg_common.has_value_changed(hdg_correction_ref,p_old_trade_record.hdg_correction_ref)                         = 1
    OR nrg_common.has_value_changed(hdg_cross_ref,p_old_trade_record.hdg_cross_ref)                                   = 1
    OR nrg_common.has_value_changed(is_rollover_closing, p_old_trade_record.is_rollover_closing)                      = 1
    OR nrg_common.has_value_changed(price_offset_index, p_old_trade_record.price_offset_index)                        = 1
    OR nrg_common.has_value_changed(execution_started_time, p_old_trade_record.execution_started_time)                = 1
    OR nrg_common.has_value_changed(trade_instrument_price, p_old_trade_record.trade_instrument_price)                = 1
    OR nrg_common.has_value_changed(strike_price, p_old_trade_record.strike_price)                                    = 1
    OR nrg_common.has_value_changed(cd_tenor, p_old_trade_record.cd_tenor)                                            = 1
    OR nrg_common.has_value_changed(cd_state, p_old_trade_record.cd_state)                                            = 1
    OR nrg_common.has_value_changed(cd_result, p_old_trade_record.cd_result)                                          = 1
    OR nrg_common.has_value_changed(quote_depth_price, p_old_trade_record.quote_depth_price)                          = 1
    OR nrg_common.has_value_changed(spread_l1_amount, p_old_trade_record.spread_l1_amount)                            = 1
    OR nrg_common.has_value_changed(spread_l1_amount_currency, p_old_trade_record.spread_l1_amount_currency)          = 1
    OR nrg_common.has_value_changed(binary_type, p_old_trade_record.binary_type)                                      = 1
    OR nrg_common.has_value_changed(settle_time, p_old_trade_record.settle_time)                                      = 1
    OR nrg_common.has_value_changed(tenor, p_old_trade_record.tenor)                                                  = 1
    OR nrg_common.has_value_changed(strike_price_additional, p_old_trade_record.strike_price_additional)              = 1
    OR nrg_common.has_value_changed(trade_instrument_amount, p_old_trade_record.trade_instrument_amount)              = 1
    OR nrg_common.has_value_changed(reference_trade_price, p_old_trade_record.reference_trade_price)                  = 1
    OR nrg_common.has_value_changed(ladder_quantities_override, p_old_trade_record.ladder_quantities_override)        = 1
    --OR nrg_common.has_value_changed(hdg_exec_broker_code, p_old_trade_record.hdg_exec_broker_code) = 1
    OR nrg_common.has_value_changed(hdg_execution_commission, p_old_trade_record.hdg_execution_commission)            = 1
    OR nrg_common.has_value_changed(cmc_trade_instrument_price, p_old_trade_record.cmc_trade_instrument_price)        = 1
    --OR nrg_common.has_value_changed(hdg_broker_code, p_old_trade_record.hdg_broker_code) = 1
    --OR nrg_common.has_value_changed(hdg_ext_broker_accnt_num, p_old_trade_record.hdg_ext_broker_accnt_num) = 1
    OR nrg_common.has_value_changed(hdg_spot_price, p_old_trade_record.hdg_spot_price)                                = 1
    OR nrg_common.has_value_changed(hdg_trade_settlement_date, p_old_trade_record.hdg_trade_settlement_date)          = 1
    OR nrg_common.has_value_changed(hdg_redemption_date, p_old_trade_record.hdg_redemption_date)                      = 1
    OR nrg_common.has_value_changed(hdg_current_coupon_date, p_old_trade_record.hdg_current_coupon_date)              = 1
    OR nrg_common.has_value_changed(hdg_accrued_interest_days, p_old_trade_record.hdg_accrued_interest_days)          = 1
    OR nrg_common.has_value_changed(hdg_accrued_interest_amount, p_old_trade_record.hdg_accrued_interest_amount)      = 1
    OR nrg_common.has_value_changed(hdg_effctv_intrst_rate, p_old_trade_record.hdg_effctv_intrst_rate)                = 1
    OR nrg_common.has_value_changed(hdg_effctv_intrst_base_amnt, p_old_trade_record.hdg_effctv_intrst_base_amnt)      = 1
    OR nrg_common.has_value_changed(hdg_afs_reserve_amount, p_old_trade_record.hdg_afs_reserve_amount)                = 1
    OR nrg_common.has_value_changed(hdg_opening_reference, p_old_trade_record.hdg_opening_reference)                  = 1
    OR nrg_common.has_value_changed(hdg_client_order_link_id, p_old_trade_record.hdg_client_order_link_id)            = 1
	  OR nrg_common.has_value_changed(hdg_crrnt_coupon_pymnt_date, p_old_trade_record.hdg_crrnt_coupon_pymnt_date)      = 1
    OR nrg_common.has_value_changed(hdg_redemption_payment_date, p_old_trade_record.hdg_redemption_payment_date)      = 1
    OR nrg_common.has_value_changed(trade_time_2, p_old_trade_record.trade_time_2)                                    = 1
    OR nrg_common.has_value_changed(trade_time_confirmation, p_old_trade_record.trade_time_confirmation)              = 1
    OR nrg_common.has_value_changed(hdg_report_source, p_old_trade_record.hdg_report_source)                          = 1
    OR nrg_common.has_value_changed(hdg_original_currency, p_old_trade_record.hdg_original_currency)                  = 1
    OR nrg_common.has_value_changed(hdg_original_amount, p_old_trade_record.hdg_original_amount)                      = 1
    OR nrg_common.has_value_changed(hdg_crypto_inst_ccy_fx_rate, p_old_trade_record.hdg_crypto_inst_ccy_fx_rate)      = 1
	);
  END put_history;
  -- ===================================================================================
  -- PUBLIC MODULES
  -- ===================================================================================
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------
  FUNCTION VERSION
    RETURN VARCHAR2 deterministic
  IS
  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  exception
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise;
  END VERSION;
  -- ===================================================================================
  -- get_contract_size_override
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve contract_size_override from instruments
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------
  FUNCTION get_contract_size_override(
      p_instrument_code IN trades.product_instrument_code%TYPE)
    RETURN NUMBER result_cache
  IS
    lv_contract_size_override instruments.contract_size_override%TYPE;
  BEGIN
    logger.logger.set_module('get_contract_size_override');
    BEGIN
      SELECT contract_size_override
      INTO lv_contract_size_override
      FROM instruments i
      WHERE i.instrument_code = p_instrument_code;
    exception
    WHEN no_data_found THEN
      RETURN NULL;
    END;
    RETURN lv_contract_size_override;
  exception
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise;
  END get_contract_size_override;
  -- ===================================================================================
  -- create_hedge_trade_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a hedge trade stub in case the trade is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trade_id                       Trade Id
  --     p_trading_account_type           Trading Account Type
  --     p_platform                       Platform
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE create_hedge_trade_stub(
      p_user                      IN trades.created_by%TYPE,
      p_logical_load_timestamp    IN trades.logical_load_timestamp%TYPE,
      p_effective_start_timestamp IN trades.effective_start_timestamp%TYPE,
      p_trade_id                  IN trades.trade_id%TYPE,
      p_trading_account_type      IN trades.trading_account_type%TYPE,
      p_platform                  IN trades.platform%TYPE)
  IS
    pragma autonomous_transaction;
  BEGIN
    INSERT
    INTO trades
      (
        trade_id,
        platform,
        trading_account_type,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        business_date,
        reporting_date
      )
      VALUES
      (
        p_trade_id,
        p_platform,
        p_trading_account_type,
        p_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        gc_default_timestamp,
        trunc(gc_default_timestamp),
        trunc(gc_default_timestamp)
      );
    COMMIT;
  exception
  WHEN dup_val_on_index THEN
    --
    --Since the trade already exists no need to create the stub
    --
    NULL;
  END create_hedge_trade_stub;
/***** End Modification for V4.5 *****/
  -- ===================================================================================
  -- create_trade_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a trade stub in case the trade is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trade_id                       Trade Id
  --     p_platform                       Platform
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE create_trade_stub
    (
      p_user                      IN trades.created_by%TYPE,
      p_logical_load_timestamp    IN trades.logical_load_timestamp%TYPE,
      p_effective_start_timestamp IN trades.effective_start_timestamp%TYPE,
      p_trade_id                  IN trades.trade_id%TYPE,
      p_platform                  IN trades.platform%TYPE
    )
  IS
    pragma autonomous_transaction;
  BEGIN
    INSERT
    INTO trades
      (
        trade_id,
        platform,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        business_date,
        reporting_date
      )
      VALUES
      (
        p_trade_id,
        p_platform,
        p_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        gc_default_timestamp,
        trunc(gc_default_timestamp),
        trunc(gc_default_timestamp)
      );
    COMMIT;
  exception
  WHEN dup_val_on_index THEN
    --
    --Since the trade already exists no need to create the stub
    --
    NULL;
  END create_trade_stub;
  -- ===================================================================================
  -- put_trade
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a trade
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trade_id                       Trade Id
  --     p_platform                       Platform
  --     p_trading_account_id             Trading Account Id
  --     p_trading_account_codifier       Trading Account Codifier
  --     p_order_id                       Order Id
  --     p_product_instrument_code        Product Instrument Code
  --     p_product_wrapper_code           Product Wrapper Code
  --     p_product_generation             Product Generation
  --     p_product_point_multiplier       Product Point Multiplier
  --     p_prdct_frctnl_prt_rto           Product Fractional Part Ratio
  --     p_product_currency               Product Currency
  --     p_product_schema_code            Product Schema Code
  --     p_prdct_financng_ratio_max       Product Financing Ratio Maximum
  --     p_session_key                    Session Key
  --     p_booking_number                 Booking Number
  --     p_cp1_cash_account_number        Counter Party 1 Cash Account Number
  --     p_cp2_cash_account_number        Counter Party 2 Cash Account Number
  --     p_publish_time                   Publish Time
  --     p_event_time                     Event Time
  --     p_creation_time                  Creation Time
  --     p_update_time                    Update Time
  --     p_creation_identity_token        Creation Identity Token
  --     p_crtn_on_bhlf_of_idntty_tkn     Creation On Behlaf Of Identity Token
  --     p_update_identity_token          Update Identity Token
  --     p_updt_on_bhlf_of_idntty_tkn     Update On Behalf Of Identity Token
  --     p_version_number                 Version Number
  --     p_is_deleted                     Is Deleted
  --     p_visit_id                       Visit Id
  --     p_channel_id                     Channel Id
  --     p_request_id                     Request Id
  --     p_reduced_trade_id               Reduced Trade Id
  --     p_reversed_trade_id              Reversed Trade Id
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --     p_trade_time                     Trade Time
  --     p_order_type                     Order Type
  --     p_controlled_order_type          Controlled Order Type
  --     p_trading_account_function       Trading Account Function
  --     p_trading_account_currency       Trading Account Currency
  --     p_product_point_multiplier       Product Point Multiplier
  --     p_product_frctonl_part_ratio     Product Functional Part Ratio
  --     p_product_currency               Product Currency
  --     p_feedsymbol                     Feed Symbol
  --     p_prophet_symbol                 Prophet Symbol
  --     p_is_primary                     Is Primay
  --     p_is_late_deal                   Is Late Deal
  --     p_mm_value_date                  MM Value Date
  --     p_direction                      Direction
  --     p_direction_multiplier           Direction Multiplier
  --     p_quoted_l1_ask_price            Quoted L1 Ask Price
  --     p_quoted_l1_bid_price            Quoted L1 Bid Price
  --     p_trade_price                    Trade Price
  --     p_price_designator               Price Designator
  --     p_quantity_designator            Quantity Designator
  --     p_trade_quantity                 Trade Quantity
  --     p_trade_quantity_currency        Trade Quantity Currency
  --     p_trades_quantity                Trade Quantity
  --     p_quantity_fx_rate_bid           Quantity FX Rate Bid
  --     p_quantity_fx_rate_ask           Quantity FX Rate Ask
  --     p_trade_amount                   Trade Amount
  --     p_trade_amount_currency          Trade Amount Currency
  --     p_trd_amt_in_trdng_accnt_crncy   Trade Amount In Trading Account Currency
  --     p_financing_ratio                Financing Ratio
  --     p_margin_type                    Margin Type
  --     p_margin_requirement             Margin Requirement
  --     p_margin_fx_rate_bid             Margin FX Rate Bid
  --     p_margin_fx_rate_ask             Margin FX Rate Ask
  --     p_trade_margin_amount            Trade Margin Amount
  --     p_trade_margin_currency          Trade Margin Currency
  --     p_trd_mrgn_in_trdng_accnt_crrncy Trade Margin In Trading Account Currnecy
  --     p_profit_loss_fx_rate_bid        Profit And Loss FX Rate Bid
  --     p_profit_loss_fx_rate_ask        Profit And Loss FX Rate Ask
  --     p_trade_profit_loss              Trade Profit And Loss
  --     p_trade_profit_loss_currency     Trade Profit And Loss Currency
  --     p_trd_pl_in_trdng_accnt_crrncy   Trade Profit And Loss In Trading Account Currency
  --     p_cp2_trading_account_id         Counter Party 2 Trading Account ID
  --     p_cp2_trading_account_codifier   Counter Party 2 Trading Account Codifier
  --     p_cp2_custom_info                Counter Party 2 Custom Info
  --     p_quote_id                       Quote ID
  --     p_quote_l1_bid                   Quote Level 1 Bid
  --     p_quote_l1_ask                   Quote Level 1 Ask
  --     p_quantity_fx_rate_id            Quantity FX Rate Id
  --     p_margin_fx_rate_id              Margin Fx Rate Id
  --     p_pl_fx_rate_id                  Profit And Loss FX Rate Id
  --     p_pl_fx_rate_bid                 Profit And Loss FX Rate Bid
  --     p_pl_fx_rate_ask                 Profit And Loss FX Rate Ask
  --     p_is_trd_of_cntrlld_ordr         Is Trade Of Controlled Order
  --     p_reversed_order_id              Reversed Order Id
  --     p_mm_instrument_id               MM Instrument Id
  --     p_related_child_order_type       Related Child Order Type
  --     p_is_mandatory                   Is Mandatory
  --     p_rqustd_trd_close_order_id      Requested Trade Close Order Id
  --     p_related_parent_order_id        Related Parent Order Id
  --     p_limit_price_condition          Limit Price Condition
  --     p_order_type                     Order Type
  --     p_limit_is_trailing_distance     Limit Trailing Distance
  --     p_limit_trailing_best_price      Limit Trailing Best Price
  --     p_limit_price                    Limit Price
  --     p_quote_ask_price                Quote Ask Price
  --     p_quote_bd_price                 Quote Bid Price
  --     p_mm_account_id                  Market Maker Account ID
  --     p_is_limit_trailing              Is Limit Trailing
  --     p_trading_account_type           Type Of Trading Account (CUSTOMER/INTERNAL)
  --     p_cp2_trading_account_type       Type Of Trading Account (CUSTOMER/INTERNAL)
  --     p_record_source                  Record Source for the booking
  --     p_price_source                   Identifies the source of the TradePrice
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Trade Deleted Before Update and After Insert
  --     -20004    Default Exception
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_trade  (p_user                            IN trades.created_by%TYPE,
                        p_effective_start_timestamp       IN trades.effective_start_timestamp%TYPE,
                        p_trade_id                        IN trades.trade_id%TYPE,
                        p_platform                        IN trades.platform%TYPE,
                        p_trading_account_id              IN trades.trading_account_id%TYPE,
                        p_trading_account_codifier        IN trades.trading_account_codifier%TYPE,
                        p_order_id                        IN trades.order_id%TYPE,
                        p_product_instrument_code         IN trades.product_instrument_code%TYPE,
                        p_product_wrapper_code            IN trades.product_wrapper_code%TYPE,
                        p_product_generation              IN trades.product_generation%TYPE,
                        p_product_point_multiplier        IN trades.product_point_multiplier%TYPE,
                        p_prdct_frctnl_prt_rto            IN trades.product_fractional_part_ratio%TYPE,
                        p_product_currency                IN trades.product_currency%TYPE,
                        --p_product_schema_code            IN trades.product_schema_code%TYPE,
                        --p_prdct_financng_ratio_max       IN trades.prdct_financing_ratio_max%TYPE,
                        p_session_key                     IN trades.session_key%TYPE,
                        p_booking_number                  IN trades.booking_number%TYPE,
                        --p_cp1_cash_account_number        IN trades.cp1_cash_account_number%TYPE,
                        --p_cp2_cash_account_number        IN trades.cp2_cash_account_number%TYPE,
                        p_publish_time                    IN trades.publish_time%TYPE,
                        p_event_time                      IN trades.event_time%TYPE,
                        p_creation_time                   IN trades.creation_time%TYPE,
                        p_update_time                     IN trades.update_time%TYPE,
                        p_creation_identity_token         IN trades.creation_identity_token%TYPE,
                        p_crtn_on_bhlf_of_idntty_tkn      IN trades.crtn_on_bhlf_of_idntty_tkn%TYPE,
                        p_update_identity_token           IN trades.update_identity_token%TYPE,
                        p_updt_on_bhlf_of_idntty_tkn      IN trades.updt_on_bhlf_of_idntty_tkn%TYPE,
                        p_version_number                  IN trades.version_number%TYPE,
                        p_is_deleted                      IN trades.is_deleted%TYPE,
                        --p_visit_id                       IN trades.visit_id%TYPE,
                        p_channel_id                      IN trades.channel_id%TYPE,
                        p_request_id                      IN trades.request_id%TYPE,
                        p_reduced_trade_id                IN trades.reduced_trade_id%TYPE,
                        p_reversed_trade_id               IN trades.reversed_trade_id%TYPE,
                        --p_business_date                  IN trades.business_date%TYPE,
                        --p_reporting_date                 IN trades.reporting_date%TYPE,
                        p_trade_time                      IN trades.trade_time%TYPE,
                        p_order_type                      IN trades.order_type%TYPE,
                        p_controlled_order_type           IN trades.controlled_order_type%TYPE,
                        p_trading_account_function        IN trades.trading_account_function%TYPE,
                        p_trading_account_currency        IN trades.trading_account_currency%TYPE,
                        --p_feedsymbol                     IN trades.feedsymbol%TYPE,
                        --p_prophet_symbol                 IN trades.prophet_symbol%TYPE,
                        p_is_primary                      IN trades.is_primary%TYPE,
                        p_is_late_deal                    IN trades.is_late_deal%TYPE,
                        p_mm_value_date                   IN trades.mm_value_date%TYPE,
                        p_direction                       IN trades.direction%TYPE,
                        p_direction_multiplier            IN trades.direction_multiplier%TYPE,
                        p_quoted_l1_ask_price             IN trades.quoted_l1_ask_price%TYPE,
                        p_quoted_l1_bid_price             IN trades.quoted_l1_bid_price%TYPE,
                        p_trade_price                     IN trades.trade_price%TYPE,
                        --p_price_designator               IN trades.price_designator%TYPE,
                        p_quantity_designator             IN trades.quantity_designator%TYPE,
                        p_trade_quantity                  IN trades.normalised_quantity%TYPE,
                        /***** Begin Modification for V4.5 - BER774 *****/
                        --p_trade_quantity_currency        IN trades.normalised_quantity_currency%TYPE,
                        /***** Added Back for BER-774 *****/
                        p_trade_quantity_currency         IN trades.normalised_quantity_currency%TYPE,
                        /***** End Modification for V4.5 *****/
                        --p_quantity_fx_rate_bid           IN trades.quantity_fx_rate_bid%TYPE,
                        --p_quantity_fx_rate_ask           IN trades.quantity_fx_rate_ask%TYPE,
                        p_trade_amount                    IN trades.trade_amount%TYPE,
                        p_trade_amount_currency           IN trades.trade_amount_currency%TYPE,
                        p_trd_amnt_in_trdng_acnt_crncy    IN trades.trd_amnt_in_trdng_accnt_crrncy%TYPE,
                        --p_financing_ratio                IN trades.financing_ratio%TYPE,
                        p_margin_type                     IN trades.margin_type%TYPE,
                        p_margin_requirement              IN trades.margin_requirement%TYPE,
                        --p_margin_fx_rate_bid             IN trades.margin_fx_rate_bid%TYPE,
                        --p_margin_fx_rate_ask             IN trades.margin_fx_rate_ask%TYPE,
                        --p_trade_margin_amount            IN trades.trade_margin_amount%TYPE,
                        --p_trade_margin_currency          IN trades.trade_margin_currency%TYPE,
                        --p_trd_mrgn_in_trdng_acnt_crncy   IN trades.trd_mrgn_in_trdng_accnt_crrncy%TYPE,
                        p_trade_profit_loss               IN trades.trade_profit_loss%TYPE,
                        p_trade_profit_loss_currency      IN trades.trade_profit_loss_currency%TYPE,
                        p_trd_pl_in_trdng_accnt_crrncy    IN trades.trd_pl_in_trdng_accnt_crrncy%TYPE,
                        p_trdng_accnt_custom_info         IN trades.trdng_accnt_custom_info%TYPE,
                        --p_ta_cstm_info_vrtl_prtfl_cd     IN trades.ta_cstm_info_vrtl_prtfl_cd%TYPE,
                        --p_cp2_trading_account_id         IN trades.cp2_trading_account_id%TYPE,
                        --p_cp2_trading_account_codifier   IN trades.cp2_trading_account_codifier%TYPE,
                        --p_cp2_custom_info                IN trades.cp2_custom_info%TYPE,
                        p_quote_id                        IN trades.quote_id%TYPE,
                        p_quote_l1_bid                    IN trades.quote_l1_bid%TYPE,
                        p_quote_l1_ask                    IN trades.quote_l1_ask%TYPE,
                        --p_quantity_fx_rate_id            IN trades.quantity_fx_rate_id%TYPE,
                        --p_margin_fx_rate_id              IN trades.margin_fx_rate_id%TYPE,
                        p_profit_loss_fx_rate_id          IN trades.profit_loss_fx_rate_id%TYPE,
                        p_profit_loss_fx_rate_bid         IN trades.profit_loss_fx_rate_bid%TYPE,
                        p_profit_loss_fx_rate_ask         IN trades.profit_loss_fx_rate_ask%TYPE,
                        p_is_trd_of_cntrlld_ordr          IN trades.is_trd_of_cntrlld_ordr%TYPE,
                        p_reversed_order_id               IN trades.reversed_order_id%TYPE,
                        p_mm_instrument_id                IN trades.mm_instrument_id%TYPE,
                        p_related_child_order_type        IN trades.related_child_order_type%TYPE,
                        p_is_mandatory                    IN trades.is_mandatory%TYPE,
                        p_rqustd_trd_close_order_id       IN trades.requested_trade_close_order_id%TYPE,
                        p_related_parent_order_id         IN trades.related_parent_order_id%TYPE,
                        p_limit_price_condition           IN trades.limit_price_condition%TYPE,
                        p_limit_trailing_distance         IN trades.limit_trailing_distance%TYPE,
                        --             p_limit_trailing_best_price      IN trades.limit_trailing_best_price%TYPE,  BER-984
                        p_limit_price                     IN trades.limit_price%TYPE,
                        p_is_prdct_crncy_in_frtnl_prts    IN trades.is_prdct_crrncy_in_frtnl_prts%TYPE,
                        p_quote_ask_price                 IN trades.quote_ask_price%TYPE,
                        p_quote_bid_price                 IN trades.quote_bid_price%TYPE,
                        p_mm_account_id                   IN trades.mm_account_id%TYPE,
                        p_is_limit_trailing               IN trades.is_limit_trailing%TYPE,
                        p_trading_account_type            IN trades.trading_account_type%TYPE,
                        p_record_source                   IN trades.record_source%TYPE,
                        --p_cp2_trading_account_type       IN trades.cp2_trading_account_type%TYPE,
                        p_quote_received_time             IN trades.quote_received_time%TYPE,
                        p_price_source                    IN trades.price_source%TYPE,
                        p_app_to_units                    IN trades.app_to_units%TYPE,
                        p_rollover_closing_trade_id       IN trades.rollover_closing_trade_id%TYPE,
                        p_price_level                     IN trades.price_level%TYPE,
                        p_amount_fx_rate_id               IN trades.amount_fx_rate_id%TYPE,
                        p_amount_fx_rate                  IN trades.amount_fx_rate%TYPE,
                        p_aggregated_price_quantity       IN trades.aggregated_price_quantity%TYPE,
                        p_rqustd_open_trade_to_close      IN trade_id_tab,
                        --p_is_hedged                       IN trades.is_hedged%TYPE,
                        p_order_time                      IN orders.creation_time%TYPE
                        /***** Begin Modification for V4.5 - BER774 *****/
                        ,
                        p_hdg_ext_trxn_id                 IN trades.hdg_ext_trxn_id%TYPE,
                        p_hdg_milliways_seq_id            IN trades.hdg_milliways_seq_id%TYPE,
                        p_hdg_src                         IN trades.hdg_src%TYPE,
                        p_hdg_src_ref_id                  IN trades.hdg_src_ref_id%TYPE,
                        p_hdg_trxn_type                   IN trades.hdg_trxn_type%TYPE,
                        p_hdg_execn_type                  IN trades.hdg_execn_type%TYPE,
                        p_hdg_ext_trxn_time               IN trades.hdg_ext_trxn_time%TYPE,
                        p_hdg_trade_dt                    IN trades.hdg_trade_dt%TYPE,
                        p_hdg_ext_trade_dt                IN trades.hdg_ext_trade_dt%TYPE,
                        p_hdg_accnting_trade_dt           IN trades.hdg_accnting_trade_dt%TYPE,
                        p_hdg_exec_broker_accnt_num       IN trades.hdg_exec_broker_accnt_num%TYPE,
                        p_hdg_asset_class                 IN trades.hdg_asset_class%TYPE,
                        p_hdg_instr_code_ext              IN trades.hdg_instr_code_ext%TYPE,
                        p_hdg_expy_month_code             IN trades.hdg_expy_month_code%TYPE,
                        p_hdg_risk_bucket                 IN trades.hdg_risk_bucket%TYPE,
                        p_hdg_cmc_trader                  IN trades.hdg_cmc_trader%TYPE,
                        p_trade_comment                   IN trades.trade_comment%TYPE,
                        p_hdg_calc_commission             IN trades.hdg_calc_commission%TYPE,
                        p_hdg_commission                  IN trades.hdg_commission%TYPE,
                        p_hdg_is_risk_relevant            IN trades.hdg_is_risk_relevant%TYPE,
                        p_hdg_is_reg_repng_relvnt         IN trades.hdg_is_reg_repng_relvnt%TYPE,
                        p_hdg_cancln_ref                  IN trades.hdg_cancln_ref%TYPE,
                        p_hdg_correction_ref              IN trades.hdg_correction_ref%TYPE,
                        p_hdg_cross_ref                   IN trades.hdg_cross_ref%TYPE,
                        p_is_rollover_closing             IN trades.is_rollover_closing%TYPE,
                        p_price_offset_index              IN trades.price_offset_index%TYPE,
                        p_execution_started_time          IN trades.execution_started_time%TYPE,
                        p_trade_instrument_price          IN trades.trade_instrument_price%TYPE,
                        p_strike_price                    IN trades.strike_price%TYPE,
                        p_order_execution_type            IN trades.order_type%TYPE,
                        p_quote_depth_price               IN trades.quote_depth_price%TYPE,
                        p_binary_type                     IN trades.binary_type%TYPE,
                        p_settle_time                     IN trades.settle_time%TYPE,
                        p_tenor                           IN trades.tenor%TYPE,
                        p_strike_price_additional         IN trades.strike_price_additional%TYPE,
                        p_trade_instrument_amount         IN trades.trade_instrument_amount%TYPE,
                        p_reference_trade_price           IN trades.reference_trade_price%TYPE,
                        p_ladder_quantities_override      IN trades.ladder_quantities_override%TYPE,
                        p_hdg_exec_broker_code            IN VARCHAR2,
                        p_hdg_execution_commission        IN trades.hdg_execution_commission%TYPE,
                        p_hdg_broker_code                 IN VARCHAR2,
                        p_hdg_ext_broker_accnt_num        IN VARCHAR2,
                        p_cmc_trade_instrument_price      IN trades.cmc_trade_instrument_price%TYPE,
                        p_hdg_spot_price                  IN trades.hdg_spot_price%TYPE,
                        p_hdg_trade_settlement_date       IN trades.hdg_trade_settlement_date%TYPE,
                        p_hdg_redemption_date             IN trades.hdg_redemption_date%TYPE,
                        p_hdg_current_coupon_date         IN trades.hdg_current_coupon_date%TYPE,
                        p_hdg_accrued_interest_days       IN trades.hdg_accrued_interest_days%TYPE,
                        p_hdg_accrued_interest_amount     IN trades.hdg_accrued_interest_amount%TYPE,
                        p_hdg_effctv_intrst_rate          IN trades.hdg_effctv_intrst_rate%TYPE,
                        p_hdg_effctv_intrst_base_amnt     IN trades.hdg_effctv_intrst_base_amnt%TYPE,
                        p_hdg_afs_reserve_amount          IN trades.hdg_afs_reserve_amount%TYPE,
                        p_hdg_opening_reference           IN trades.hdg_opening_reference%TYPE,
                        p_hdg_client_order_link_id        IN trades.hdg_client_order_link_id%TYPE,
                        p_hdg_crrnt_coupon_pymnt_date     IN trades.hdg_crrnt_coupon_pymnt_date%TYPE,
                        p_hdg_redemption_payment_date     IN trades.hdg_redemption_payment_date%TYPE,
                        p_trade_time_2       				      IN trades.trade_time_2%TYPE,
                        p_trade_time_confirmation     	  IN trades.trade_time_confirmation%TYPE,
                        p_hdg_report_source				        IN trades.hdg_report_source%TYPE,
                        p_hdg_original_currency			      IN trades.hdg_original_currency%TYPE,
                        p_hdg_original_amount				      IN trades.hdg_original_amount%TYPE,
                        p_hdg_crypto_inst_ccy_fx_rate		  IN trades.hdg_crypto_inst_ccy_fx_rate%TYPE,
                        p_trade_price_offset              IN trades.trade_price_offset%TYPE,
                        p_qt_is_frst_gd_in_cntns_trdng    IN trades.qt_is_frst_gd_in_cntns_trdng%TYPE,
                        p_hdg_trade_settlement_ref        IN trades.hdg_trade_settlement_ref%TYPE
                        )

  IS

    lv_effective_start_time trades.effective_start_timestamp%TYPE := NULL;
    lv_logical_load_timestamp trades.logical_load_timestamp%TYPE;
    /***** Begin Modification for V4.5 - BER774 *****/
    lv_trading_account_function trades.trading_account_function%TYPE;
    lv_trade_amount trades.trade_amount%TYPE;
    lv_dummy NUMBER:=0;
    /***** End Modification for V4.5 *****/
    lv_order_sub_type trades.controlled_order_type%TYPE;
    lv_trade_quantity_currency trades.normalised_quantity_currency%TYPE;
    lv_mm_trade_quantity_ccy trades.trade_quantity_currency%TYPE;
    lv_mm_trade_quantity_ccyi instruments.currency%TYPE;
    lv_normalised_quantity trades.normalised_quantity%TYPE;
    lv_normalised_trade_price trades.normalised_trade_price%TYPE;
    lv_trade_quantity trades.trade_quantity%TYPE;
    lv_order_type trades.order_type%TYPE;
    lv_trade_id_short_form trades.trade_id_short_form%TYPE;
    lv_order_id_short_form trades.trade_id_short_form%TYPE;
    lv_fractional_part_ratio trades.product_fractional_part_ratio%TYPE;
    lv_internal_account_id trades.trading_account_id%TYPE;
    lv_trading_account_type trades.trading_account_type%TYPE;
    --lv_cp2_trading_account_type trades.trading_account_type%TYPE;
    lv_product_currency trades.product_currency%TYPE;
    lv_trade_time trades.trade_time%TYPE;
    lv_business_date trades.business_date%TYPE;
    lv_reporting_date trades.reporting_date%TYPE;
    lv_product_point_multiplier trades.product_point_multiplier%TYPE := NULL;
    lv_trade_amount_currency trades.trading_account_currency%TYPE;
    lv_trd_amnt_in_trdng_acnt_crnc trades.trd_amnt_in_trdng_accnt_crrncy%TYPE;
    lv_mm_instrument_type instruments.instrument_type%TYPE;
    lv_mm_instrument_typei instruments.instrument_type%TYPE;
    lv_mm_pair_currencyi instruments.pair_currency%TYPE;
    lv_old_trade trades%rowtype;
    ltab_identity_id identity_id_tab;
    lv_cash_transaction_seq cash_transactions.cash_transaction_seq%TYPE;
    lv_instrument_ccy instruments.currency%TYPE;
    lex_unknown_operation_type    exception;
    lex_trade_not_found           exception;
    lv_is_prdct_ccy_in_frtnl_prts VARCHAR2(3);
    lv_spread_l1_amount           NUMBER;
    lv_spread_l1_amount_ccy       VARCHAR2(3);
    lv_qt_is_frst_gd_in_cntns_trdg VARCHAR2(3);

  BEGIN
    logger.logger.set_module('put_trade');
    -- set the logical load timestamp to now
    lv_logical_load_timestamp := SYSTIMESTAMP;
    --
    --Calculate the transformed values for MM and NG
    --
    --lv_fractional_part_ratio := nvl(p_prdct_frctnl_prt_rto, 1);
    --
    --Absolute value of trade quantity
    --
    --The trade quantity should always be a positive value for MM and NG
    --If we need to add the direction on the quantity then multiply the quantity
    --with the direction multiplier to append the -ve sign for SELL
    --
    lv_trade_quantity := abs(p_trade_quantity);
    CASE
    WHEN p_platform = 'MMCFD' OR p_platform = 'MMSB' THEN
      --
      -- Trading Account Function
      --
      lv_trading_account_function := p_trading_account_function;
      --
      --Business_date
      --
      lv_business_date := nrg_common.get_business_date(p_order_time);
      --
      --Reporting Date
      --
      lv_reporting_date := nrg_common.get_reporting_date(p_order_time);
      --
      -- Product Point Multiplier
      --
      lv_product_point_multiplier := p_product_point_multiplier;
      -- BER-1381
      -- Trade quantity currency
      --
      BEGIN
        SELECT i.currency,
          i.instrument_type,
          i.pair_currency
        INTO lv_mm_trade_quantity_ccyi,
          lv_mm_instrument_typei,
          lv_mm_pair_currencyi
        FROM bi_ods.instruments i
        WHERE i.instrument_code = p_product_instrument_code;
      exception
      WHEN no_data_found THEN
        lv_mm_trade_quantity_ccy:=NULL;
        lv_mm_instrument_type   :=NULL;
      END;
      CASE
      WHEN p_product_instrument_code = 'K-GWOT' THEN
        CASE
        WHEN p_is_primary           = 'YES' THEN
          lv_mm_trade_quantity_ccy := 'XAG';
        ELSE
          lv_mm_trade_quantity_ccy := lv_mm_trade_quantity_ccyi;
        END CASE;
      WHEN p_product_instrument_code IN ('A-AUMK','K-MBJD','K-MBJA','K-MBKY') THEN
        CASE
        WHEN p_is_primary           = 'YES' THEN
          lv_mm_trade_quantity_ccy := 'XAU';
        ELSE
          lv_mm_trade_quantity_ccy := lv_mm_trade_quantity_ccyi;
        END CASE;
      ELSE
        CASE
        WHEN lv_mm_instrument_typei='Currencies' THEN
          CASE
          WHEN p_is_primary           = 'YES' THEN
            lv_mm_trade_quantity_ccy := lv_mm_pair_currencyi;
          ELSE
            lv_mm_trade_quantity_ccy := lv_mm_trade_quantity_ccyi;
          END CASE;
        ELSE
          lv_mm_trade_quantity_ccy := NULL;
        END CASE;
      END CASE;
      --
      --Product Currency
      --
      IF p_product_currency IS NOT NULL AND LENGTH(p_product_currency) > 3 THEN
        --
        --This scenario occurs for the TIQ instruments. For these instruments the product currency
        --does not comes in as ISO3 code. In order to identify the primary currency for such instruments
        --we need to lookup on the reference table and identify the actual product currency
        --
        BEGIN
          SELECT decode(upper(p_is_primary), 'YES', primary_currency, secondary_currency)
          INTO lv_product_currency
          FROM bi_ref.mm_instrument_currencies
          WHERE mm_instrument_id = p_mm_instrument_id;
        exception
        WHEN too_many_rows THEN
          --
          --Since the product currency is more than 3 characters and we can not find the mapping for
          --it on the reference table we will have to leave it as it is on the source so that we can
          --identify it and then later it can be fixed.
          --
          lv_product_currency := p_product_currency;
        WHEN no_data_found THEN
          --
          --Since the product currency is more than 3 characters and we can not find the mapping for
          --it on the reference table we will have to leave it as it is on the source so that we can
          --identify it and then later it can be fixed
          --
          lv_product_currency := p_product_currency;
        END;
      ELSE
        lv_product_currency := p_product_currency;
      END IF;
      --
      --Sub Order Type
      --
      CASE
      WHEN upper(p_controlled_order_type) = 'LIQUIDATION' THEN
        lv_order_sub_type                := 'POSITIONCLOSEOUT';
      WHEN upper(p_controlled_order_type) = 'TRADECANCEL' THEN
        lv_order_sub_type                := 'TRADEREVERSAL';
      ELSE
        lv_order_sub_type := 'OTHER';
      END CASE;
      --
      --Product fractional part ratio
      --
      CASE
      WHEN p_platform             = 'MMSB' THEN
        lv_fractional_part_ratio := 1;
      WHEN p_platform             = 'MMCFD' THEN
        BEGIN
          SELECT 0.01
          INTO lv_fractional_part_ratio
          FROM instruments,
            countries
          WHERE countries.country_code = instruments.country_code
          AND countries.iso3           = 'GBR'
          AND instrument_code          = p_product_instrument_code
          AND instrument_type          = 'Shares';
        exception
        WHEN no_data_found THEN
          lv_fractional_part_ratio := 1;
        END;
      ELSE
        lv_fractional_part_ratio := 1;
      END CASE;
      lv_normalised_quantity := abs(round(p_trade_quantity, 0)/(1/nvl(lv_product_point_multiplier, 1)) * nvl(lv_fractional_part_ratio,1));
      --
      -- Normalised Trade Price
      --
      IF p_is_primary              = 'NO' AND nvl(p_trade_price,0) != 0 THEN
        lv_normalised_trade_price := 1/p_trade_price;
      ELSE
        lv_normalised_trade_price := p_trade_price;
      END IF;
      -- BER-1381
      -- Trade amount
      --
      lv_trade_amount := lv_normalised_quantity * lv_normalised_trade_price;
      --
      -- Trade amount currency
      --
      lv_trade_amount_currency := lv_product_currency;
      --
      --is_prdct_ccy_in_frtnl_prts
      --
      CASE
      WHEN p_platform                  = 'MMSB' THEN
        lv_is_prdct_ccy_in_frtnl_prts := 'NO';
      WHEN p_platform                  = 'MMCFD' THEN
        BEGIN
          SELECT 'YES'
          INTO lv_is_prdct_ccy_in_frtnl_prts
          FROM instruments,
            countries
          WHERE countries.country_code = instruments.country_code
          AND countries.iso3           = 'GBR'
          AND instrument_code          = p_product_instrument_code
          AND instrument_type          = 'Shares';
        exception
        WHEN no_data_found THEN
          lv_is_prdct_ccy_in_frtnl_prts := 'NO';
        END;
      ELSE
        lv_is_prdct_ccy_in_frtnl_prts := 'NO';
      END CASE;
    WHEN p_platform = 'NG' THEN
      /***** Begin Modification for V4.5 - BER774 *****/
      lv_trading_account_function :=
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        'LIVE'
      ELSE
        p_trading_account_function
      END;
      lv_trade_amount :=
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        abs(p_trade_amount)
      ELSE
        p_trade_amount
      END;
      /***** End Modification for V4.5 *****/
      --
      --Business_date
      --
      lv_business_date :=
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        nrg_common.get_business_date(p_creation_time)
      ELSE
        nrg_common.get_business_date(p_trade_time)
      END;
      --
      --Reporting Date
      --
      /*
      lv_reporting_date := nrg_common.get_reporting_date(p_trade_time);*/
      lv_reporting_date :=
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        nrg_common.get_reporting_date(p_creation_time)
      ELSE
        nrg_common.get_reporting_date(p_trade_time)
      END;
      --
      -- Product Point Multiplier
      --
      CASE
      WHEN p_quantity_designator     = 'UNITS' THEN
        lv_product_point_multiplier := NULL;
      WHEN p_quantity_designator     = 'AMOUNT' THEN
        lv_product_point_multiplier := NULL;
      WHEN p_quantity_designator     = 'AMOUNTPERPOINT' THEN --2
        lv_product_point_multiplier := p_product_point_multiplier;
      WHEN p_quantity_designator = 'KOUNITS' THEN
        lv_product_point_multiplier := p_product_point_multiplier;
      ELSE
        lv_product_point_multiplier := NULL;
      END CASE;
      --
      -- Product fractional part ratio
      --
      CASE
        WHEN p_quantity_designator = 'UNITS' THEN
          CASE
          WHEN p_is_prdct_crncy_in_frtnl_prts = 'YES' THEN
            lv_fractional_part_ratio         := p_prdct_frctnl_prt_rto;
          WHEN p_is_prdct_crncy_in_frtnl_prts = 'NO' THEN
            lv_fractional_part_ratio         := NULL;
          ELSE
            lv_fractional_part_ratio := NULL;
          END CASE;
      WHEN p_quantity_designator  = 'AMOUNT' THEN
        lv_fractional_part_ratio := NULL;
      WHEN p_quantity_designator  = 'AMOUNTPERPOINT' THEN --2
        lv_fractional_part_ratio := NULL;
      WHEN p_quantity_designator = 'KOUNITS' THEN
        lv_fractional_part_ratio := NULL;
      ELSE
        lv_fractional_part_ratio := NULL;
      END CASE;
      --
      --Product Currency
      --
      lv_product_currency := p_product_currency;
      --
      --Order Id Short Form
      --
      /***** Begin Modification for V4.5 - Ber774 *****/
      --lv_order_id_short_form := SUBSTR(p_order_id,1,1)||LTRIM(SUBSTR(p_order_id,2,2),'0')||'-'||LTRIM(SUBSTR(p_order_id,4,4),'0')||'-'||LTRIM(SUBSTR(p_order_id,8,13),'0');
      lv_order_id_short_form :=
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        NULL
      ELSE
        substr(p_order_id,1,1)||ltrim(substr(p_order_id,2,2),'0')||'-'||ltrim(substr(p_order_id,4,4),'0')||'-'||ltrim(substr(p_order_id,8,13),'0')
      END;
      /***** End Modification for V4.5 *****/
      --
      --Trade Id Short Form
      --
      /***** Begin Modification for V4.5 - BER774 *****/
      --lv_trade_id_short_form := SUBSTR(p_trade_id,1,1)||LTRIM(SUBSTR(p_trade_id,2,2),'0')||'-'||LTRIM(SUBSTR(p_trade_id,4,4),'0')||'-'||LTRIM(SUBSTR(p_trade_id,8,13),'0');
      lv_trade_id_short_form :=
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        p_trade_id
      ELSE
        substr(p_trade_id,1,1)||ltrim(substr(p_trade_id,2,2),'0')||'-'||ltrim(substr(p_trade_id,4,4),'0')||'-'||ltrim(substr(p_trade_id,8,13),'0')
      END;
      /***** End Modification for V4.5 *****/
      --
      -- Order Type
      --
      lv_order_sub_type := upper(p_controlled_order_type);
      --
      --Traded Quantity
      --
      CASE
      WHEN p_trading_account_type = 'HEDGE' THEN
        CASE
        WHEN p_is_prdct_crncy_in_frtnl_prts = 'YES' THEN
          lv_normalised_quantity           := p_trade_quantity * nvl(get_contract_size_override(p_product_instrument_code),1) * nvl(lv_fractional_part_ratio,1);
        WHEN p_is_prdct_crncy_in_frtnl_prts = 'NO' THEN
          lv_normalised_quantity           := p_trade_quantity * nvl(get_contract_size_override(p_product_instrument_code),1);
        ELSE
          lv_normalised_quantity := NULL;
        END CASE;
      ELSE
        CASE
        WHEN p_product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
          lv_normalised_quantity := p_trade_quantity;
        WHEN p_quantity_designator = 'AMOUNTPERPOINT' THEN
          lv_normalised_quantity  := p_trade_quantity * nvl(lv_product_point_multiplier,1);
        WHEN p_quantity_designator = 'UNITS' AND p_is_prdct_crncy_in_frtnl_prts = 'YES' THEN
          lv_normalised_quantity  := p_trade_quantity * nvl(lv_fractional_part_ratio,1);
        WHEN p_quantity_designator = 'UNITS' AND p_is_prdct_crncy_in_frtnl_prts = 'NO' THEN
          lv_normalised_quantity  := p_trade_quantity;
        WHEN p_quantity_designator = 'AMOUNT' THEN
          CASE
          WHEN p_trade_price        = 0 THEN
            lv_normalised_quantity := 0;
          ELSE
            lv_normalised_quantity := lv_trade_amount / p_trade_price;
          END CASE;
        WHEN p_quantity_designator = 'KOUNITS' THEN
          lv_normalised_quantity := p_trade_quantity * nvl(lv_product_point_multiplier,1);
        ELSE
          lv_normalised_quantity := NULL;
        END CASE;
      END CASE;
      lv_is_prdct_ccy_in_frtnl_prts := p_is_prdct_crncy_in_frtnl_prts;

      lv_qt_is_frst_gd_in_cntns_trdg := nvl(p_qt_is_frst_gd_in_cntns_trdng,'NO');
    END CASE;
    --
    --Common Business Rules between MMCFD, MMSB and NG
    --
    --
    --Order Type
    --
    lv_order_type := nrg_common.get_order_type(p_platform => p_platform,
                                               p_controlled_order_type => p_controlled_order_type,
                                               p_order_type => p_order_type);
    --
    --Trade Quantity Currency
    --
    CASE
      /***** Begin Modification for V4.5 - Ber774 *****/
    WHEN p_product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
      lv_trade_quantity_currency := p_trading_account_currency;
    WHEN p_trading_account_type   = 'HEDGE' THEN
      lv_trade_quantity_currency := p_trade_quantity_currency;
      /***** End Modification for V4.5 *****/
    WHEN p_product_wrapper_code   = 'A-EOVH' THEN
      lv_trade_quantity_currency := p_trading_account_currency;
    WHEN p_product_wrapper_code   = 'X-A' THEN
      lv_trade_quantity_currency := lv_product_currency;
    WHEN p_product_wrapper_code = 'X-QOQH' THEN
      lv_trade_quantity_currency := p_trading_account_currency;
    ELSE
      lv_trade_quantity_currency := NULL;
    END CASE;
    --
    -- Normalised Trade Price
    --
    /*IF p_is_primary              = 'NO' AND nvl(p_trade_price,0) != 0 THEN
      lv_normalised_trade_price := 1/p_trade_price;
    ELSE
      lv_normalised_trade_price := p_trade_price;
    END IF;*/

    CASE
      WHEN p_product_wrapper_Code IN ('X-MNRM','X-MNRN') THEN
        lv_normalised_trade_price := p_trade_price;
      WHEN p_product_wrapper_Code = 'X-QOQH' THEN
        lv_normalised_trade_price := p_trade_price;
      WHEN p_is_primary              = 'NO' AND nvl(p_trade_price,0) != 0 THEN
        lv_normalised_trade_price := 1/p_trade_price;
      ELSE
        lv_normalised_trade_price := p_trade_price;
    END CASE;
    --
    -- This is to ensure that the trading account record exists for referential integrity
    -- Also set the Trading Account Type
    --
    IF p_trading_account_id   IS NOT NULL THEN
      lv_trading_account_type := p_trading_account_type;
      nrg_trading_account.create_trading_account_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_trading_account_id => p_trading_account_id, p_trading_account_type => lv_trading_account_type);
    END IF;
    --
    -- This is to ensure that the cp2 trading account record exists for referential integrity
    --
    /*IF p_cp2_trading_account_id IS NOT NULL THEN
    lv_cp2_trading_account_type := p_cp2_trading_account_type;
    nrg_trading_account.create_trading_account_stub (p_user                       => p_user,
    p_logical_load_timestamp     => lv_logical_load_timestamp,
    p_effective_start_timestamp  => p_effective_start_timestamp,
    p_trading_account_id         => p_cp2_trading_account_id,
    p_trading_account_type       => lv_cp2_trading_account_type);
    END IF;*/
    CASE
    WHEN p_record_source       = 'NG' AND p_platform = 'NG' and p_product_wrapper_code IN ('X-A', 'A-EOVH', 'X-QOQH')
 THEN
      lv_spread_l1_amount     := abs(((p_quote_l1_bid - p_quote_l1_ask) / 2)* lv_normalised_quantity);
      lv_spread_l1_amount_ccy := nvl(lv_trade_quantity_currency,p_trade_quantity_currency);
    ELSE
      lv_spread_l1_amount     := NULL;
      lv_spread_l1_amount_ccy := NULL;
    END CASE;
    --
    -- This is to ensure that the session record exists for the referential integrity
    --
    IF p_session_key IS NOT NULL THEN
      nrg_session.create_session_stub(p_user => p_user,
                                      p_logical_load_timestamp => lv_logical_load_timestamp,
                                      p_effective_start_timestamp => p_effective_start_timestamp,
                                      p_session_key => p_session_key,
                                      p_channel_id => p_channel_id,
                                      p_identity_id                => p_creation_identity_token,
                                      p_on_behalf_of_identity_id   => p_crtn_on_bhlf_of_idntty_tkn);
    END IF;
    --
    -- This is to ensure that the order record exists for referential integrity
    --
    IF p_order_id IS NOT NULL THEN
      nrg_order.create_order_stub(p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_order_id => p_order_id, p_platform => p_platform);
    END IF;
    --
    -- This is to ensure that the cash transaction(Booking) record exists for the referential integrity
    --
    IF p_booking_number IS NOT NULL THEN
      nrg_cash_transaction.create_cash_transaction_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_booking_number => p_booking_number, p_platform => p_platform, p_record_source => p_platform, p_cash_transaction_seq => lv_cash_transaction_seq);
    END IF;
    --
    --
    --
    IF p_rollover_closing_trade_id IS NOT NULL THEN
      create_trade_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_trade_id => p_rollover_closing_trade_id, p_platform => p_platform);
    END IF;
    --
    -- This is to ensure that the product instrument record exists for the referential integrity
    --
    IF p_product_instrument_code IS NOT NULL AND p_product_wrapper_code IS NOT NULL THEN
      nrg_products.create_instrument_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_instrument_code => p_product_instrument_code, p_product_platform => p_platform, p_product_wrapper => p_product_wrapper_code, p_mm_instrument_id => p_mm_instrument_id);
    END IF;
    --
    --Insert the controlled order type
    --
    IF lv_order_sub_type IS NOT NULL THEN
      nrg_controlled_order_type.put_controlled_order_type (p_user => p_user, p_effective_start_timestamp => p_effective_start_timestamp, p_logical_load_timestamp => lv_logical_load_timestamp, p_controlled_order_type => lv_order_sub_type);
    END IF;
    --
    --Insert the order type
    --
    IF lv_order_type IS NOT NULL THEN
      nrg_order_type.put_order_type (p_user => p_user, p_effective_start_timestamp => p_effective_start_timestamp, p_logical_load_timestamp => lv_logical_load_timestamp, p_order_type => lv_order_type);
    END IF;
    --
    --Insert the quantity designator
    --
    IF p_quantity_designator IS NOT NULL THEN
      nrg_quantity_designator.put_quantity_designator (p_user => p_user, p_effective_start_timestamp => p_effective_start_timestamp, p_logical_load_timestamp => lv_logical_load_timestamp, p_quantity_designator => p_quantity_designator);
    END IF;
    --
    -- Identity Stubs for referential integrity
    --
    -- Build array of ids
    --
    SELECT identity_id_obj(identity_id) BULK COLLECT
    INTO ltab_identity_id
    FROM
      (SELECT DISTINCT t.identity_id
      FROM
        (SELECT p_creation_identity_token AS identity_id FROM dual
        UNION
        SELECT p_crtn_on_bhlf_of_idntty_tkn FROM dual
        UNION
        SELECT p_update_identity_token FROM dual
        UNION
        SELECT p_updt_on_bhlf_of_idntty_tkn FROM dual
        ) t
      WHERE t.identity_id IS NOT NULL
      );
    --
    -- Create Identity Stubs
    --
    IF ltab_identity_id.count <> 0 THEN
      nrg_identity.create_identity_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_identity_id => ltab_identity_id);
    END IF;
    --
    -- Perform the trading account lookup for internal hedge trades.
    -- The indicator for this is where the Trading_Account_Id is not set and the MM_Account_Id is set.
    -- Since the mm_account_id is numeric on the trades table but varchar2 on the trading_accounts table
    -- we need to do an explicit to_char to ensure index use during lookup
    --
    IF p_trading_account_id IS NULL AND p_mm_account_id IS NOT NULL THEN
      BEGIN
        SELECT t.trading_account_id,
          t.trading_account_type
        INTO lv_internal_account_id,
          lv_trading_account_type
        FROM trading_accounts t
        WHERE t.trading_account_type <> 'CUSTOMER'
        AND t.source_account_id       = to_char(p_mm_account_id);
      exception
      WHEN no_data_found THEN
        -- Perform no action since it is impossible to correctly assign a trading_account_id.
        -- An external process will have to monitor and resolve any orphans if they occur.
        lv_internal_account_id  := NULL;
        lv_trading_account_type := NULL;
      WHEN too_many_rows THEN
        -- This is an unlikely scenario but if it should occur
        -- Perform no action since it is ambiguous and needs intervention to correctly assign a trading_account_id.
        -- An external process will have to monitor and resolve any orphans if they occur.
        lv_internal_account_id  := NULL;
        lv_trading_account_type := NULL;
      END;
    END IF;
    /***** Begin Modification for V4.5 - BER774 *****/
    --Check for Reference Trade Ids and create Stubs if the trade is not present
    IF p_hdg_cancln_ref IS NOT NULL THEN
      BEGIN
        SELECT 1
        INTO lv_dummy
        FROM trades
        WHERE trade_id = p_hdg_cancln_ref
        AND platform   = p_platform;
        lv_dummy      :=0;
      exception
      WHEN no_data_found THEN
        create_hedge_trade_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_trade_id => p_hdg_cancln_ref, p_trading_account_type => p_trading_account_type, p_platform => p_platform);
      END;
    END IF;
    IF p_hdg_correction_ref IS NOT NULL THEN
      BEGIN
        SELECT 1
        INTO lv_dummy
        FROM trades
        WHERE trade_id = p_hdg_correction_ref
        AND platform   = p_platform;
        lv_dummy      :=0;
      exception
      WHEN no_data_found THEN
        create_hedge_trade_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_trade_id => p_hdg_correction_ref, p_trading_account_type => p_trading_account_type, p_platform => p_platform);
      END;
    END IF;
    IF p_hdg_cross_ref IS NOT NULL THEN
      BEGIN
        SELECT 1
        INTO lv_dummy
        FROM trades
        WHERE trade_id = p_hdg_cross_ref
        AND platform   = p_platform;
        lv_dummy      :=0;
      exception
      WHEN no_data_found THEN
        create_hedge_trade_stub (p_user => p_user, p_logical_load_timestamp => lv_logical_load_timestamp, p_effective_start_timestamp => p_effective_start_timestamp, p_trade_id => p_hdg_cross_ref, p_trading_account_type => p_trading_account_type, p_platform => p_platform);
      END;
    END IF;
    /***** End Modification for V4.5 *****/
    --
    --Insert the new trade on the table
    --
    BEGIN
      INSERT
      INTO trades
        (
          trade_id,
          platform,
          logical_load_timestamp,
          created_by,
          create_timestamp,
          updated_by,
          update_timestamp,
          effective_start_timestamp,
          trading_account_id,
          trading_account_type,
          trading_account_codifier,
          order_id,
          product_instrument_code,
          product_wrapper_code,
          product_generation,
          --product_schema_code,
          --prdct_financing_ratio_max,
          session_key,
          booking_number,
          --cp1_cash_account_number,
          --cp2_cash_account_number,
          publish_time,
          event_time,
          creation_time,
          update_time,
          creation_identity_token,
          crtn_on_bhlf_of_idntty_tkn,
          update_identity_token,
          updt_on_bhlf_of_idntty_tkn,
          version_number,
          is_deleted,
          --visit_id,
          channel_id,
          request_id,
          reduced_trade_id,
          reversed_trade_id,
          business_date,
          reporting_date,
          trade_time,
          order_type,
          controlled_order_type,
          trading_account_function,
          trading_account_currency,
          product_point_multiplier,
          product_fractional_part_ratio,
          product_currency,
          is_primary,
          is_late_deal,
          mm_value_date,
          direction,
          direction_multiplier,
          quoted_l1_ask_price,
          quoted_l1_bid_price,
          trade_price,
          --price_designator,
          quantity_designator,
          normalised_quantity,
          normalised_quantity_currency,
          --quantity_fx_rate_bid,
          --quantity_fx_rate_ask,
          trade_amount,
          trade_amount_currency,
          trd_amnt_in_trdng_accnt_crrncy,
          --financing_ratio,
          margin_type,
          margin_requirement,
          --margin_fx_rate_bid,
          --margin_fx_rate_ask,
          --trade_margin_amount,
          --trade_margin_currency,
          --trd_mrgn_in_trdng_accnt_crrncy,
          trade_profit_loss,
          trade_profit_loss_currency,
          trd_pl_in_trdng_accnt_crrncy,
          trdng_accnt_custom_info,
          --ta_cstm_info_vrtl_prtfl_cd,
          --cp2_trading_account_id,
          --cp2_trading_account_type,
          --cp2_trading_account_codifier,
          --cp2_custom_info,
          quote_id,
          quote_l1_bid,
          quote_l1_ask,
          --quantity_fx_rate_id,
          --margin_fx_rate_id,
          profit_loss_fx_rate_id,
          profit_loss_fx_rate_bid,
          profit_loss_fx_rate_ask,
          is_trd_of_cntrlld_ordr,
          reversed_order_id,
          mm_instrument_id,
          related_child_order_type,
          is_mandatory,
          requested_trade_close_order_id,
          related_parent_order_id,
          limit_price_condition,
          limit_trailing_distance,
          --  limit_trailing_best_price,
          limit_price,
          is_prdct_crrncy_in_frtnl_prts,
          quote_ask_price,
          quote_bid_price,
          trade_id_short_form,
          order_id_short_form,
          mm_account_id,
          trade_quantity,
          is_limit_trailing,
          normalised_trade_price,
          /***** Begin Modification for V4.5 - BER774 *****/
          --trade_quantity_currency,
          /***** Added Back for BER-774 *****/
          trade_quantity_currency,
          /***** End Modification for V4.5 *****/
          cash_transaction_seq,
          record_source,
          price_source,
          amount_fx_rate_id,
          amount_fx_rate,
          price_level,
          aggregated_price_quantity,
          quote_received_time,
          app_to_units,
          rollover_closing_trade_id,
          --is_hedged
          /***** Begin Modificatino for V4.5 - BER774 *****/
          --,
          hdg_ext_trxn_id,
          hdg_milliways_seq_id,
          hdg_src,
          hdg_src_ref_id,
          hdg_trxn_type,
          hdg_execn_type,
          hdg_ext_trxn_time,
          hdg_trade_dt,
          hdg_ext_trade_dt,
          hdg_accnting_trade_dt,
          hdg_exec_broker_accnt_num,
          hdg_asset_class,
          hdg_instr_code_ext,
          hdg_expy_month_code,
          hdg_risk_bucket,
          hdg_cmc_trader,
          trade_comment,
          hdg_calc_commission,
          hdg_commission,
          hdg_is_risk_relevant,
          hdg_is_reg_repng_relvnt,
          hdg_cancln_ref,
          hdg_correction_ref,
          hdg_cross_ref,
          is_rollover_closing,
          price_offset_index,
          execution_started_time,
          trade_instrument_price,
          strike_price,
          quote_depth_price,
          spread_l1_amount,
          spread_l1_amount_currency,
          binary_type,
          settle_time,
          tenor,
          strike_price_additional,
          trade_instrument_amount,
          reference_trade_price,
          ladder_quantities_override,
          --hdg_exec_broker_code,
          hdg_execution_commission,
          cmc_trade_instrument_price,
          --hdg_broker_code,
          --hdg_ext_broker_accnt_num
          hdg_spot_price,
          hdg_trade_settlement_date,
          hdg_redemption_date,
          hdg_current_coupon_date,
          hdg_accrued_interest_days,
          hdg_accrued_interest_amount,
          hdg_effctv_intrst_rate,
          hdg_effctv_intrst_base_amnt,
          hdg_afs_reserve_amount,
          hdg_opening_reference,
          hdg_client_order_link_id,
          hdg_crrnt_coupon_pymnt_date,
          hdg_redemption_payment_date,
          trade_time_2,
          trade_time_confirmation,
          hdg_report_source,
          hdg_original_currency,
          hdg_original_amount,
          hdg_crypto_inst_ccy_fx_rate,
          trade_price_offset,
          qt_is_frst_gd_in_cntns_trdng,
          hdg_trade_settlement_ref
        )
        VALUES
        (
          p_trade_id,
          p_platform,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          p_effective_start_timestamp,
          nvl(p_trading_account_id,lv_internal_account_id),
          lv_trading_account_type,
          p_trading_account_codifier,
          p_order_id,
          p_product_instrument_code,
          p_product_wrapper_code,
          p_product_generation,
          --p_product_schema_code,
          --p_prdct_financng_ratio_max,
          p_session_key,
          p_booking_number,
          --p_cp1_cash_account_number,
          --p_cp2_cash_account_number,
          p_publish_time,
          p_event_time,
          p_creation_time,
          p_update_time,
          p_creation_identity_token,
          p_crtn_on_bhlf_of_idntty_tkn,
          p_update_identity_token,
          p_updt_on_bhlf_of_idntty_tkn,
          p_version_number,
          p_is_deleted,
          --p_visit_id,
          p_channel_id,
          p_request_id,
          p_reduced_trade_id,
          p_reversed_trade_id,
          lv_business_date,
          lv_reporting_date,
          p_trade_time,
          lv_order_type,
          lv_order_sub_type,
          lv_trading_account_function,
          p_trading_account_currency,
          lv_product_point_multiplier,
          lv_fractional_part_ratio,
          lv_product_currency,
          p_is_primary,
          p_is_late_deal,
          p_mm_value_date,
          p_direction,
          p_direction_multiplier,
          p_quoted_l1_ask_price,
          p_quoted_l1_bid_price,
          p_trade_price,
          --p_price_designator,
          p_quantity_designator,
          lv_normalised_quantity,
          lv_trade_quantity_currency,
          --p_quantity_fx_rate_bid,
          --p_quantity_fx_rate_ask,
          lv_trade_amount,
          decode(p_platform,'NG',p_trade_amount_currency,lv_trade_amount_currency),
          p_trd_amnt_in_trdng_acnt_crncy,
          --p_financing_ratio,
          p_margin_type,
          p_margin_requirement,
          --p_margin_fx_rate_bid,
          --p_margin_fx_rate_ask,
          --p_trade_margin_amount,
          --p_trade_margin_currency,
          --p_trd_mrgn_in_trdng_acnt_crncy,
          p_trade_profit_loss,
          p_trade_profit_loss_currency,
          p_trd_pl_in_trdng_accnt_crrncy,
          p_trdng_accnt_custom_info,
          --p_ta_cstm_info_vrtl_prtfl_cd,
          --p_cp2_trading_account_id,
          --lv_cp2_trading_account_type,
          --p_cp2_trading_account_codifier,
          --p_cp2_custom_info,
          p_quote_id,
          p_quote_l1_bid,
          p_quote_l1_ask,
          --p_quantity_fx_rate_id,
          --p_margin_fx_rate_id,
          p_profit_loss_fx_rate_id,
          p_profit_loss_fx_rate_bid,
          p_profit_loss_fx_rate_ask,
          p_is_trd_of_cntrlld_ordr,
          p_reversed_order_id,
          p_mm_instrument_id,
          p_related_child_order_type,
          p_is_mandatory,
          p_rqustd_trd_close_order_id,
          p_related_parent_order_id,
          p_limit_price_condition,
          p_limit_trailing_distance,
          --      p_limit_trailing_best_price,
          p_limit_price,
          lv_is_prdct_ccy_in_frtnl_prts,
          p_quote_ask_price,
          p_quote_bid_price,
          lv_trade_id_short_form,
          lv_order_id_short_form,
          p_mm_account_id,
          lv_trade_quantity,
          p_is_limit_trailing,
          lv_normalised_trade_price,
          /***** Begin Modification for V4.5 - BER774 *****/
          --p_trade_quantity_currency,
          /***** Added Back for BER-774 *****/
          decode(p_platform,'NG',p_trade_quantity_currency,lv_mm_trade_quantity_ccy),
          /***** End Modification for V4.5 *****/
          lv_cash_transaction_seq,
          p_record_source,
          p_price_source,
          p_amount_fx_rate_id,
          p_amount_fx_rate,
          p_price_level,
          p_aggregated_price_quantity,
          p_quote_received_time,
          p_app_to_units,
          p_rollover_closing_trade_id,
          --p_is_hedged
          /***** Begin Modification for V4.5 - BER774 *****/
          --,
          p_hdg_ext_trxn_id,
          p_hdg_milliways_seq_id,
          p_hdg_src,
          p_hdg_src_ref_id,
          p_hdg_trxn_type,
          p_hdg_execn_type,
          p_hdg_ext_trxn_time,
          p_hdg_trade_dt,
          p_hdg_ext_trade_dt,
          p_hdg_accnting_trade_dt,
          p_hdg_exec_broker_accnt_num,
          p_hdg_asset_class,
          p_hdg_instr_code_ext,
          p_hdg_expy_month_code,
          p_hdg_risk_bucket,
          p_hdg_cmc_trader,
          p_trade_comment,
          p_hdg_calc_commission,
          p_hdg_commission,
          p_hdg_is_risk_relevant,
          p_hdg_is_reg_repng_relvnt,
          p_hdg_cancln_ref,
          p_hdg_correction_ref,
          p_hdg_cross_ref,
          p_is_rollover_closing,
          p_price_offset_index,
          p_execution_started_time,
          p_trade_instrument_price,
          p_strike_price,
          p_quote_depth_price,
          lv_spread_l1_amount,
          lv_spread_l1_amount_ccy,
          p_binary_type,
          p_settle_time,
          p_tenor,
          p_strike_price_additional,
          p_trade_instrument_amount,
          p_reference_trade_price,
          p_ladder_quantities_override,
          --p_hdg_exec_broker_code,
          p_hdg_execution_commission,
          p_cmc_trade_instrument_price,
          --p_hdg_broker_code,
          --p_hdg_ext_broker_accnt_num
          p_hdg_spot_price,
          p_hdg_trade_settlement_date,
          p_hdg_redemption_date,
          p_hdg_current_coupon_date,
          p_hdg_accrued_interest_days,
          p_hdg_accrued_interest_amount,
          p_hdg_effctv_intrst_rate,
          p_hdg_effctv_intrst_base_amnt,
          p_hdg_afs_reserve_amount,
          p_hdg_opening_reference,
          p_hdg_client_order_link_id,
          p_hdg_crrnt_coupon_pymnt_date,
          p_hdg_redemption_payment_date,
          p_trade_time_2,
          p_trade_time_confirmation,
          p_hdg_report_source,
          p_hdg_original_currency,
          p_hdg_original_amount,
          p_hdg_crypto_inst_ccy_fx_rate,
          p_trade_price_offset,
          lv_qt_is_frst_gd_in_cntns_trdg,
          p_hdg_trade_settlement_ref
        );
      IF p_platform <> 'NG' THEN
        BEGIN
          SELECT i.currency
          INTO lv_instrument_ccy
          FROM bi_ods.instruments i
          WHERE i.instrument_code = p_product_instrument_code;
        exception
        WHEN no_data_found THEN
          lv_instrument_ccy:=NULL;
        END;
        nrg_latest_position.put_position(p_user => p_user,
                                  p_effective_start_timestamp => p_effective_start_timestamp,
                                  p_order_id => p_order_id,
                                  p_state => 'EXECUTED',
                                  p_platform => p_platform,
                                  p_trading_account_id => nvl(p_trading_account_id, lv_internal_account_id),
                                  p_trading_account_type => lv_trading_account_type,
                                  p_trading_account_codifier => p_trading_account_codifier,
                                  p_trading_account_function => lv_trading_account_function,
                                  p_mm_account_id => p_mm_account_id,
                                  p_product_instrument_code => p_product_instrument_code,
                                  p_product_wrapper_code => p_product_wrapper_code,
                                  p_product_point_multiplier => p_product_point_multiplier,
                                  p_product_financing_ratio_max => NULL,
                                  p_product_generation => p_product_generation,
                                  p_is_crncy_in_frctnl_prts => lv_is_prdct_ccy_in_frtnl_prts,
                                  p_fractional_part_ratio => lv_fractional_part_ratio,
                                  p_mm_instrument_id => p_mm_instrument_id,
                                  p_requested_direction => p_direction,
                                  p_creation_time => p_creation_time,
                                  p_trade_id => p_trade_id, p_trade_time => p_trade_time,
                                  p_is_primary => p_is_primary,
                                  p_trdng_accnt_prmry_crrncy => p_trading_account_currency,
                                  p_product_currency => CASE
                                                          WHEN p_is_primary = 'NO' THEN
                                                            lv_instrument_ccy
                                                          ELSE
                                                            lv_product_currency
                                                          END,
                                  p_trade_quantity => CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_trade_amount
                                                        ELSE
                                                          lv_trade_quantity
                                                        END,
                                  p_trade_quantity_currency => CASE
                                                                  WHEN p_is_primary = 'NO' THEN
                                                                    lv_trade_amount_currency
                                                                  ELSE
                                                                    lv_mm_trade_quantity_ccy
                                                                  END,
                                  p_nrmlsd_opn_qty_in_trdng_ccy => CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_normalised_quantity*lv_normalised_trade_price
                                                                    ELSE
                                                                      lv_normalised_quantity
                                                                    END,
                                  p_trade_price => p_trade_price,
                                  p_normalised_open_price => lv_normalised_trade_price,
                                  p_open_trade_financing_ratio => NULL,
                                  p_trade_amount =>                CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_normalised_quantity
                                                                    ELSE
                                                                      lv_normalised_quantity*lv_normalised_trade_price
                                                                    END,
                                  p_trade_amount_currency =>
                                                                    CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_mm_trade_quantity_ccy
                                                                    ELSE
                                                                      lv_trade_amount_currency
                                                                    END,
                                  p_amt_in_trdng_accnt_prmry_ccy => p_trd_amnt_in_trdng_acnt_crncy,
                                  p_nrmlsd_opn_vle_in_trdng_ccy =>
                                                                    CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_normalised_quantity
                                                                    ELSE
                                                                      lv_normalised_quantity*lv_normalised_trade_price
                                                                    END,
                                  p_margin_type => p_margin_type,
                                  p_normalised_margin_type => p_margin_type,
                                  p_normalised_margin_req => p_margin_requirement,
                                  p_margin => NULL,
                                  p_mrgn_trdng_accnt_prmry_ccy => NULL,
                                  p_margin_currency =>
                                                                    CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_trade_amount_currency
                                                                    ELSE
                                                                      lv_mm_trade_quantity_ccy
                                                                    END,
                                  p_margin_secondary => NULL,
                                  p_margin_secondary_currency =>
                                                                    CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_mm_trade_quantity_ccy
                                                                    ELSE
                                                                      lv_trade_amount_currency
                                                                    END,
                                  p_margin_fx_rate => NULL,
                                  p_open_trade_qntty_fx_rate => NULL,
                                  p_cstm_info_vrtl_prtfl_cd => NULL,
                                  p_quantity_designator => p_quantity_designator,
                                  p_mm_backoffice_ref => NULL,
                                  p_mm_value_date => p_mm_value_date,
                                  p_normalised_trading_currency =>
                                                                    CASE
                                                                    WHEN p_is_primary = 'NO' THEN
                                                                      lv_instrument_ccy
                                                                    ELSE
                                                                      lv_product_currency
                                                                    END,
                                  p_last_modified_time => p_update_time,
                                  p_old_effective_start_time => NULL,
                                  p_logical_load_timestamp => lv_logical_load_timestamp ,
                                  p_record_source => NULL ,
                                  p_load_latest_positions => 'YES' ,
                                  p_direction_multiplier => NULL ,
                                  p_trading_scope => NULL,
                                  p_strike_price => p_strike_price);
      END IF;
    exception
    WHEN dup_val_on_index THEN
      --Get the effective start time of the previous version of the trade
      BEGIN
        SELECT trd.*
        INTO lv_old_trade
        FROM trades trd
        WHERE trade_id = p_trade_id
        AND platform   = p_platform FOR UPDATE;
        --
        -- Trade Time
        -- We will take the trade time that was written initially for MM to make it static
        -- In order to do this we will store it in a local variable and use it whenever we write an trade update
        -- This will be done for MM only
        --
        IF p_platform        = 'NG' THEN
          lv_trade_time     := p_trade_time;
          lv_business_date  := lv_business_date;
          lv_reporting_date := lv_reporting_date;
        ELSE
          IF lv_old_trade.business_date = trunc(gc_default_timestamp) THEN
            lv_trade_time              := p_trade_time;
            lv_business_date           := lv_business_date;
            lv_reporting_date          := lv_reporting_date;
          ELSE
            lv_trade_time     := lv_old_trade.trade_time;
            lv_business_date  := lv_old_trade.business_date;
            lv_reporting_date := lv_old_trade.reporting_date;
          END IF;
        END IF;
        lv_effective_start_time := lv_old_trade.effective_start_timestamp;
      exception
      WHEN no_data_found THEN
        raise lex_trade_not_found;
      END;
    END;
    CASE
    WHEN lv_effective_start_time IS NULL THEN
      --
      --Since the trade is already inserted no operation will be performed
      --
      NULL;
    WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time <= p_effective_start_timestamp THEN
      --
      --In case when the effective start time of the incoming record is greater than the effective start time
      --of the trade on the trades table then this specifies the update of the trade
      --Update trade with the latest version
      --
      UPDATE trades
      SET logical_load_timestamp                             = lv_logical_load_timestamp,
          updated_by                                         = p_user,
          update_timestamp                                   = SYSTIMESTAMP,
          effective_start_timestamp                          = p_effective_start_timestamp,
          trading_account_id                                 = nvl(p_trading_account_id,lv_internal_account_id),
          trading_account_type                               = lv_trading_account_type,
          trading_account_codifier                           = p_trading_account_codifier,
          order_id                                           = p_order_id,
          product_instrument_code                            = p_product_instrument_code,
          product_wrapper_code                               = p_product_wrapper_code,
          product_generation                                 = p_product_generation,
          --product_schema_code                              = p_product_schema_code,
          --prdct_financing_ratio_max                        = p_prdct_financng_ratio_max,
          session_key                                        = p_session_key,
          cash_transaction_seq                               = lv_cash_transaction_seq,
          booking_number                                     = p_booking_number,
          record_source                                      = p_record_source,
          --cp1_cash_account_number                          = p_cp1_cash_account_number,
          --cp2_cash_account_number                          = p_cp2_cash_account_number,
          publish_time                                       = p_publish_time,
          event_time                                         = p_event_time,
          creation_time                                      = p_creation_time,
          update_time                                        = p_update_time,
          creation_identity_token                            = p_creation_identity_token,
          crtn_on_bhlf_of_idntty_tkn                         = p_crtn_on_bhlf_of_idntty_tkn,
          update_identity_token                              = p_update_identity_token,
          updt_on_bhlf_of_idntty_tkn                         = p_updt_on_bhlf_of_idntty_tkn,
          version_number                                     = p_version_number,
          is_deleted                                         = p_is_deleted,
          --visit_id                       = p_visit_id,
          channel_id                                         = p_channel_id,
          request_id                                         = p_request_id,
          reduced_trade_id                                   = p_reduced_trade_id,
          reversed_trade_id                                  = p_reversed_trade_id,
          business_date                                      = lv_business_date,
          reporting_date                                     = lv_reporting_date,
          trade_time                                         = lv_trade_time,
          order_type                                         = lv_order_type,
          controlled_order_type                              = lv_order_sub_type,
          trading_account_function                           = lv_trading_account_function,
          trading_account_currency                           = p_trading_account_currency,
          product_point_multiplier                           = lv_product_point_multiplier,
          product_fractional_part_ratio                      = lv_fractional_part_ratio,
          product_currency                                   = lv_product_currency,
          is_primary                                         = p_is_primary,
          is_late_deal                                       = p_is_late_deal,
          mm_value_date                                      = p_mm_value_date,
          direction                                          = p_direction,
          direction_multiplier                               = p_direction_multiplier,
          quoted_l1_ask_price                                = p_quoted_l1_ask_price,
          quoted_l1_bid_price                                = p_quoted_l1_bid_price,
          trade_price                                        = p_trade_price,
          --price_designator                                    = p_price_designator,
          quantity_designator                                = p_quantity_designator,
          normalised_quantity                                = lv_normalised_quantity,
          normalised_quantity_currency                       = lv_trade_quantity_currency,
          --quantity_fx_rate_bid                               = p_quantity_fx_rate_bid,
          --quantity_fx_rate_ask                               = p_quantity_fx_rate_ask,
          trade_amount                                       = lv_trade_amount,
          trade_amount_currency                              = decode(p_platform,'NG',p_trade_amount_currency,lv_trade_amount_currency),
          trd_amnt_in_trdng_accnt_crrncy                     = p_trd_amnt_in_trdng_acnt_crncy,
          --financing_ratio                                    = p_financing_ratio,
          margin_type                                        = p_margin_type,
          margin_requirement                                 = p_margin_requirement,
          --margin_fx_rate_bid                               = p_margin_fx_rate_bid,
          --margin_fx_rate_ask                               = p_margin_fx_rate_ask,
          --trade_margin_amount                              = p_trade_margin_amount,
          --trade_margin_currency                            = p_trade_margin_currency,
          --trd_mrgn_in_trdng_accnt_crrncy                   = p_trd_mrgn_in_trdng_acnt_crncy,
          trade_profit_loss                                  = p_trade_profit_loss,
          trade_profit_loss_currency                         = p_trade_profit_loss_currency,
          trd_pl_in_trdng_accnt_crrncy                       = p_trd_pl_in_trdng_accnt_crrncy,
          trdng_accnt_custom_info                            = p_trdng_accnt_custom_info,
          --ta_cstm_info_vrtl_prtfl_cd                       = p_ta_cstm_info_vrtl_prtfl_cd,
          --cp2_trading_account_id                           = p_cp2_trading_account_id,
          --cp2_trading_account_type                         = lv_cp2_trading_account_type,
          --cp2_trading_account_codifier                     = p_cp2_trading_account_codifier,
          --cp2_custom_info                                  = p_cp2_custom_info,
          quote_id                                           = p_quote_id,
          quote_l1_bid                                       = p_quote_l1_bid,
          quote_l1_ask                                       = p_quote_l1_ask,
          --quantity_fx_rate_id                              = p_quantity_fx_rate_id,
          --margin_fx_rate_id                                = p_margin_fx_rate_id,
          profit_loss_fx_rate_id                             = p_profit_loss_fx_rate_id,
          profit_loss_fx_rate_bid                            = p_profit_loss_fx_rate_bid,
          profit_loss_fx_rate_ask                            = p_profit_loss_fx_rate_ask,
          is_trd_of_cntrlld_ordr                             = p_is_trd_of_cntrlld_ordr,
          reversed_order_id                                  = p_reversed_order_id,
          mm_instrument_id                                   = p_mm_instrument_id,
          related_child_order_type                           = p_related_child_order_type,
          is_mandatory                                       = p_is_mandatory,
          requested_trade_close_order_id                     = p_rqustd_trd_close_order_id,
          related_parent_order_id                            = p_related_parent_order_id,
          limit_price_condition                              = p_limit_price_condition,
          limit_trailing_distance                            = p_limit_trailing_distance,
          --   limit_trailing_best_price                        = p_limit_trailing_best_price,
          limit_price                                        = p_limit_price,
          is_prdct_crrncy_in_frtnl_prts                      = lv_is_prdct_ccy_in_frtnl_prts,
          quote_ask_price                                    = p_quote_ask_price,
          quote_bid_price                                    = p_quote_bid_price,
          trade_id_short_form                                = lv_trade_id_short_form,
          order_id_short_form                                = lv_order_id_short_form,
          mm_account_id                                      = p_mm_account_id,
          trade_quantity                                     = lv_trade_quantity,
          is_limit_trailing                                  = p_is_limit_trailing,
          normalised_trade_price                             = lv_normalised_trade_price,
          /***** Begin Modification for V4.5 - BER774 *****/
          --trade_quantity_currency        = p_trade_quantity_currency,
          /***** Added Back for BER-774 *****/
          trade_quantity_currency                            = decode(p_platform,'NG',p_trade_quantity_currency,lv_mm_trade_quantity_ccy),
          /***** End Modification for V4.5 *****/
          price_source                                       = p_price_source,
          amount_fx_rate_id                                  = p_amount_fx_rate_id,
          amount_fx_rate                                     = p_amount_fx_rate,
          price_level                                        = p_price_level,
          aggregated_price_quantity                          = p_aggregated_price_quantity,
          quote_received_time                                = p_quote_received_time,
          app_to_units                                       = p_app_to_units,
          rollover_closing_trade_id                          = p_rollover_closing_trade_id,
          --is_hedged                 = p_is_hedged
          /***** Begin Modification for V4.5 - BER774 *****/
          --,
          hdg_ext_trxn_id                                    = p_hdg_ext_trxn_id,
          hdg_milliways_seq_id                               = p_hdg_milliways_seq_id,
          hdg_src                                            = p_hdg_src,
          hdg_src_ref_id                                     = p_hdg_src_ref_id,
          hdg_trxn_type                                      = p_hdg_trxn_type,
          hdg_execn_type                                     = p_hdg_execn_type,
          hdg_ext_trxn_time                                  = p_hdg_ext_trxn_time,
          hdg_trade_dt                                       = p_hdg_trade_dt,
          hdg_ext_trade_dt                                   = p_hdg_ext_trade_dt,
          hdg_accnting_trade_dt                              = p_hdg_accnting_trade_dt,
          hdg_exec_broker_accnt_num                          = p_hdg_exec_broker_accnt_num,
          hdg_asset_class                                    = p_hdg_asset_class,
          hdg_instr_code_ext                                 = p_hdg_instr_code_ext,
          hdg_expy_month_code                                = p_hdg_expy_month_code,
          hdg_risk_bucket                                    = p_hdg_risk_bucket,
          hdg_cmc_trader                                     = p_hdg_cmc_trader,
          trade_comment                                      = p_trade_comment,
          hdg_calc_commission                                = p_hdg_calc_commission,
          hdg_commission                                     = p_hdg_commission,
          hdg_is_risk_relevant                               = p_hdg_is_risk_relevant,
          hdg_is_reg_repng_relvnt                            = p_hdg_is_reg_repng_relvnt,
          hdg_cancln_ref                                     = p_hdg_cancln_ref,
          hdg_correction_ref                                 = p_hdg_correction_ref,
          hdg_cross_ref                                      = p_hdg_cross_ref,
          is_rollover_closing                                = p_is_rollover_closing,
          price_offset_index                                 = p_price_offset_index,
          execution_started_time                             = p_execution_started_time,
          trade_instrument_price                             = p_trade_instrument_price,
          strike_price                                       = p_strike_price,
          quote_depth_price                                  = p_quote_depth_price,
          spread_l1_amount                                   = lv_spread_l1_amount,
          spread_l1_amount_currency                          = lv_spread_l1_amount_ccy,
          binary_type                                        = p_binary_type,
          settle_time                                        = p_settle_time,
          tenor                                              = p_tenor,
          strike_price_additional                            = p_strike_price_additional,
          trade_instrument_amount                            = p_trade_instrument_amount,
          reference_trade_price                              = p_reference_trade_price,
          ladder_quantities_override                         = p_ladder_quantities_override,
          --hdg_exec_broker_code                             = p_hdg_exec_broker_code,
          hdg_execution_commission                           = p_hdg_execution_commission,
          cmc_trade_instrument_price                         = p_cmc_trade_instrument_price,
          --hdg_broker_code                                  = p_hdg_broker_code,
          --hdg_ext_broker_accnt_num                         = p_hdg_ext_broker_accnt_num
          hdg_spot_price                                     = p_hdg_spot_price,
          hdg_trade_settlement_date                          = p_hdg_trade_settlement_date,
          hdg_redemption_date                                = p_hdg_redemption_date,
          hdg_current_coupon_date                            = p_hdg_current_coupon_date,
          hdg_accrued_interest_days                          = p_hdg_accrued_interest_days,
          hdg_accrued_interest_amount                        = p_hdg_accrued_interest_amount,
          hdg_effctv_intrst_rate                             = p_hdg_effctv_intrst_rate,
          hdg_effctv_intrst_base_amnt                        = p_hdg_effctv_intrst_base_amnt,
          hdg_afs_reserve_amount                             = p_hdg_afs_reserve_amount,
          hdg_opening_reference                              = p_hdg_opening_reference,
          hdg_client_order_link_id                           = p_hdg_client_order_link_id,
          hdg_crrnt_coupon_pymnt_date                        = p_hdg_crrnt_coupon_pymnt_date,
          hdg_redemption_payment_date                        = p_hdg_redemption_payment_date,
          trade_time_2                                       = p_trade_time_2,
          trade_time_confirmation                            = p_trade_time_confirmation,
          hdg_report_source                                  = p_hdg_report_source,
          hdg_original_currency                              = p_hdg_original_currency,
          hdg_original_amount                                = p_hdg_original_amount,
          hdg_crypto_inst_ccy_fx_rate                        = p_hdg_crypto_inst_ccy_fx_rate,
          trade_price_offset                                 = p_trade_price_offset,
          qt_is_frst_gd_in_cntns_trdng                       = lv_qt_is_frst_gd_in_cntns_trdg,
          hdg_trade_settlement_ref                           = p_hdg_trade_settlement_ref
      WHERE trade_id                                                                                         = p_trade_id
      AND platform                                                                                           = p_platform
      AND (nrg_common.has_value_changed(trading_account_id,nvl(p_trading_account_id,lv_internal_account_id)) = 1
        OR nrg_common.has_value_changed(trading_account_type,lv_trading_account_type)                          = 1
        OR nrg_common.has_value_changed(trading_account_codifier,p_trading_account_codifier)                   = 1
        OR nrg_common.has_value_changed(order_id,p_order_id)                                                   = 1
        OR nrg_common.has_value_changed(product_instrument_code,p_product_instrument_code)                     = 1
        OR nrg_common.has_value_changed(product_wrapper_code,p_product_wrapper_code)                           = 1
        OR nrg_common.has_value_changed(product_generation,p_product_generation)                               = 1
        OR
          --nrg_common.has_value_changed(product_schema_code,p_product_schema_code) = 1 OR
          --nrg_common.has_value_changed(prdct_financing_ratio_max,p_prdct_financng_ratio_max)= 1 OR
          nrg_common.has_value_changed(session_key,p_session_key)       = 1
        OR nrg_common.has_value_changed(booking_number,p_booking_number)= 1
        OR
          --nrg_common.has_value_changed(cp1_cash_account_number,p_cp1_cash_account_number)= 1 OR
          --nrg_common.has_value_changed(cp2_cash_account_number,p_cp2_cash_account_number)= 1 OR
          nrg_common.has_value_changed(creation_time,p_creation_time)                           = 1
        OR nrg_common.has_value_changed(update_time,p_update_time)                              = 1
        OR nrg_common.has_value_changed(creation_identity_token,p_creation_identity_token)      = 1
        OR nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_crtn_on_bhlf_of_idntty_tkn)= 1
        OR nrg_common.has_value_changed(update_identity_token,p_update_identity_token)          = 1
        OR nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_updt_on_bhlf_of_idntty_tkn)= 1
        OR nrg_common.has_value_changed(version_number,p_version_number)                        = 1
        OR nrg_common.has_value_changed(is_deleted,p_is_deleted)                                = 1
        OR
          --nrg_common.has_value_changed(visit_id,p_visit_id)= 1 OR
          nrg_common.has_value_changed(channel_id,p_channel_id)                                                                           = 1
        OR nrg_common.has_value_changed(request_id,p_request_id)                                                                          = 1
        OR nrg_common.has_value_changed(reduced_trade_id,p_reduced_trade_id)                                                              = 1
        OR nrg_common.has_value_changed(reversed_trade_id,p_reversed_trade_id)                                                            = 1
        OR nrg_common.has_value_changed(order_type,lv_order_type)                                                                         = 1
        OR nrg_common.has_value_changed(controlled_order_type,lv_order_sub_type)                                                          = 1
        OR nrg_common.has_value_changed(trading_account_function,lv_trading_account_function)                                             = 1
        OR nrg_common.has_value_changed(trading_account_currency,decode(p_platform,'NG',p_trade_amount_currency,lv_trade_amount_currency))= 1
        OR nrg_common.has_value_changed(product_point_multiplier,lv_product_point_multiplier)                                             = 1
        OR nrg_common.has_value_changed(product_fractional_part_ratio,lv_fractional_part_ratio)                                           = 1
        OR nrg_common.has_value_changed(product_currency,lv_product_currency)                                                             = 1
        OR nrg_common.has_value_changed(is_primary,p_is_primary)                                                                          = 1
        OR nrg_common.has_value_changed(is_late_deal,p_is_late_deal)                                                                      = 1
        OR nrg_common.has_value_changed(mm_value_date,p_mm_value_date)                                                                    = 1
        OR nrg_common.has_value_changed(direction,p_direction)                                                                            = 1
        OR nrg_common.has_value_changed(direction_multiplier,p_direction_multiplier)                                                      = 1
        OR nrg_common.has_value_changed(quoted_l1_ask_price,p_quoted_l1_ask_price)                                                        = 1
        OR nrg_common.has_value_changed(quoted_l1_bid_price,p_quoted_l1_bid_price)                                                        = 1
        OR nrg_common.has_value_changed(trade_price,p_trade_price)                                                                        = 1
        OR
          --nrg_common.has_value_changed(price_designator,p_price_designator)= 1 OR
          nrg_common.has_value_changed(quantity_designator,p_quantity_designator)= 1
        OR
          --nrg_common.has_value_changed(quantity_fx_rate_bid,p_quantity_fx_rate_bid)= 1 OR
          --nrg_common.has_value_changed(quantity_fx_rate_ask,p_quantity_fx_rate_ask)= 1 OR
          nrg_common.has_value_changed(trade_amount,lv_trade_amount)                                  = 1
        OR nrg_common.has_value_changed(trade_amount_currency,p_trade_amount_currency)                = 1
        OR nrg_common.has_value_changed(trd_amnt_in_trdng_accnt_crrncy,p_trd_amnt_in_trdng_acnt_crncy)= 1
        OR
          --nrg_common.has_value_changed(financing_ratio,p_financing_ratio)= 1 OR
          nrg_common.has_value_changed(margin_type,p_margin_type)               = 1
        OR nrg_common.has_value_changed(margin_requirement,p_margin_requirement)= 1
        OR
          --nrg_common.has_value_changed(margin_fx_rate_bid,p_margin_fx_rate_bid)= 1 OR
          --nrg_common.has_value_changed(margin_fx_rate_ask,p_margin_fx_rate_ask)= 1 OR
          --nrg_common.has_value_changed(trade_margin_amount,p_trade_margin_amount)= 1 OR
          --nrg_common.has_value_changed(trade_margin_currency,p_trade_margin_currency)= 1 OR
          --nrg_common.has_value_changed(trd_mrgn_in_trdng_accnt_crrncy,p_trd_mrgn_in_trdng_acnt_crncy)= 1 OR
          nrg_common.has_value_changed(trade_profit_loss,p_trade_profit_loss)                       = 1
        OR nrg_common.has_value_changed(trade_profit_loss_currency,p_trade_profit_loss_currency)    = 1
        OR nrg_common.has_value_changed(trd_pl_in_trdng_accnt_crrncy,p_trd_pl_in_trdng_accnt_crrncy)= 1
        OR nrg_common.has_value_changed(trdng_accnt_custom_info,p_trdng_accnt_custom_info)          = 1
        OR
          --nrg_common.has_value_changed(ta_cstm_info_vrtl_prtfl_cd,p_ta_cstm_info_vrtl_prtfl_cd)= 1 OR
          --nrg_common.has_value_changed(cp2_trading_account_id,p_cp2_trading_account_id)= 1 OR
          --nrg_common.has_value_changed(cp2_trading_account_type,lv_cp2_trading_account_type) = 1 OR
          --nrg_common.has_value_changed(cp2_trading_account_codifier,p_cp2_trading_account_codifier)= 1 OR
          --nrg_common.has_value_changed(cp2_custom_info,p_cp2_custom_info)= 1 OR
          nrg_common.has_value_changed(quote_id,p_quote_id)         = 1
        OR nrg_common.has_value_changed(quote_l1_bid,p_quote_l1_bid)= 1
        OR nrg_common.has_value_changed(quote_l1_ask,p_quote_l1_ask)= 1
        OR
          --nrg_common.has_value_changed(quantity_fx_rate_id,p_quantity_fx_rate_id)= 1 OR
          --nrg_common.has_value_changed(margin_fx_rate_id,p_margin_fx_rate_id)= 1 OR
          nrg_common.has_value_changed(profit_loss_fx_rate_id,p_profit_loss_fx_rate_id)             = 1
        OR nrg_common.has_value_changed(profit_loss_fx_rate_bid,p_profit_loss_fx_rate_bid)          = 1
        OR nrg_common.has_value_changed(profit_loss_fx_rate_ask,p_profit_loss_fx_rate_ask)          = 1
        OR nrg_common.has_value_changed(is_trd_of_cntrlld_ordr,p_is_trd_of_cntrlld_ordr)            = 1
        OR nrg_common.has_value_changed(reversed_order_id,p_reversed_order_id)                      = 1
        OR nrg_common.has_value_changed(mm_instrument_id,p_mm_instrument_id)                        = 1
        OR nrg_common.has_value_changed(related_child_order_type,p_related_child_order_type)        = 1
        OR nrg_common.has_value_changed(is_mandatory,p_is_mandatory)                                = 1
        OR nrg_common.has_value_changed(requested_trade_close_order_id,p_rqustd_trd_close_order_id) = 1
        OR nrg_common.has_value_changed(related_parent_order_id,p_related_parent_order_id)          = 1
        OR nrg_common.has_value_changed(limit_price_condition,p_limit_price_condition)              = 1
        OR nrg_common.has_value_changed(limit_trailing_distance,p_limit_trailing_distance)          = 1
        OR
          --      nrg_common.has_value_changed(limit_trailing_best_price,p_limit_trailing_best_price) = 1 OR
          nrg_common.has_value_changed(limit_price,p_limit_price)                                     = 1
        OR nrg_common.has_value_changed(is_prdct_crrncy_in_frtnl_prts, lv_is_prdct_ccy_in_frtnl_prts) = 1
        OR nrg_common.has_value_changed(quote_ask_price,p_quote_ask_price)                            = 1
        OR nrg_common.has_value_changed(quote_bid_price,p_quote_bid_price)                            = 1
        OR nrg_common.has_value_changed(order_id_short_form, lv_order_id_short_form)                  = 1
        OR nrg_common.has_value_changed(mm_account_id, p_mm_account_id)                               = 1
        OR nrg_common.has_value_changed(trade_quantity, lv_trade_quantity)                            = 1
        OR nrg_common.has_value_changed(is_limit_trailing,p_is_limit_trailing)                        = 1
        OR nrg_common.has_value_changed(normalised_trade_price,lv_normalised_trade_price)             = 1
        OR
          /***** Begin Modification for V4.5 - BER774 *****/
          --nrg_common.has_value_changed(trade_quantity_currency,p_trade_quantity_currency) = 1 OR
          /***** Added Back for BER-774 *****/
          nrg_common.has_value_changed(trade_quantity_currency,decode(p_platform,'NG',p_trade_quantity_currency,lv_mm_trade_quantity_ccy)) = 1
        OR
          /***** End Modification for V4.5 *****/
          nrg_common.has_value_changed(cash_transaction_seq,lv_cash_transaction_seq)            = 1
        OR nrg_common.has_value_changed(price_source,p_price_source)                            = 1
        OR nrg_common.has_value_changed(record_source,p_record_source)                          = 1
        OR nrg_common.has_value_changed(amount_fx_rate_id, p_amount_fx_rate_id)                 = 1
        OR nrg_common.has_value_changed(amount_fx_rate, p_amount_fx_rate)                       = 1
        OR nrg_common.has_value_changed(price_level, p_price_level)                             = 1
        OR nrg_common.has_value_changed(aggregated_price_quantity, p_aggregated_price_quantity) = 1
        OR nrg_common.has_value_changed(quote_received_time, p_quote_received_time)             = 1
        OR nrg_common.has_value_changed(app_to_units, p_app_to_units)                           = 1
        OR nrg_common.has_value_changed(rollover_closing_trade_id, p_rollover_closing_trade_id) = 1
        --OR nrg_common.has_value_changed(is_hedged, p_is_hedged)                                 = 1
          /***** Begin Modification for V4.5 - BER774 *****/
        OR nrg_common.has_value_changed(hdg_ext_trxn_id,p_hdg_ext_trxn_id)                     = 1
        OR nrg_common.has_value_changed(hdg_milliways_seq_id,p_hdg_milliways_seq_id)           = 1
        OR nrg_common.has_value_changed(hdg_src,p_hdg_src)                                     = 1
        OR nrg_common.has_value_changed(hdg_src_ref_id,p_hdg_src_ref_id)                       = 1
        OR nrg_common.has_value_changed(hdg_trxn_type,p_hdg_trxn_type)                         = 1
        OR nrg_common.has_value_changed(hdg_execn_type,p_hdg_execn_type)                       = 1
        OR nrg_common.has_value_changed(hdg_ext_trxn_time,p_hdg_ext_trxn_time)                 = 1
        OR nrg_common.has_value_changed(hdg_trade_dt,p_hdg_trade_dt)                           = 1
        OR nrg_common.has_value_changed(hdg_ext_trade_dt,p_hdg_ext_trade_dt)                   = 1
        OR nrg_common.has_value_changed(hdg_accnting_trade_dt,p_hdg_accnting_trade_dt)         = 1
        OR nrg_common.has_value_changed(hdg_exec_broker_accnt_num,p_hdg_exec_broker_accnt_num) = 1
        OR nrg_common.has_value_changed(hdg_asset_class,p_hdg_asset_class)                     = 1
        OR nrg_common.has_value_changed(hdg_instr_code_ext,p_hdg_instr_code_ext)               = 1
        OR nrg_common.has_value_changed(hdg_expy_month_code,p_hdg_expy_month_code)             = 1
        OR nrg_common.has_value_changed(hdg_risk_bucket,p_hdg_risk_bucket)                     = 1
        OR nrg_common.has_value_changed(hdg_cmc_trader,p_hdg_cmc_trader)                       = 1
        OR nrg_common.has_value_changed(trade_comment,p_trade_comment)                             = 1
        OR nrg_common.has_value_changed(hdg_calc_commission,p_hdg_calc_commission)             = 1
        OR nrg_common.has_value_changed(hdg_commission,p_hdg_commission)                       = 1
        OR nrg_common.has_value_changed(hdg_is_risk_relevant,p_hdg_is_risk_relevant)           = 1
        OR nrg_common.has_value_changed(hdg_is_reg_repng_relvnt,p_hdg_is_reg_repng_relvnt)     = 1
        OR nrg_common.has_value_changed(hdg_cancln_ref,p_hdg_cancln_ref)                       = 1
        OR nrg_common.has_value_changed(hdg_correction_ref,p_hdg_correction_ref)               = 1
        OR nrg_common.has_value_changed(hdg_cross_ref,p_hdg_cross_ref)                         = 1
        OR nrg_common.has_value_changed(is_rollover_closing, p_is_rollover_closing )           = 1
        OR nrg_common.has_value_changed(price_offset_index, p_price_offset_index )             = 1
        OR nrg_common.has_value_changed(execution_started_time, p_execution_started_time )     = 1
        OR nrg_common.has_value_changed(trade_instrument_price, p_trade_instrument_price )     = 1
        OR nrg_common.has_value_changed(strike_price, p_strike_price )                         = 1
        OR nrg_common.has_value_changed(quote_depth_price, p_quote_depth_price )               = 1
        OR nrg_common.has_value_changed(spread_l1_amount, lv_spread_l1_amount)                 = 1
        OR nrg_common.has_value_changed(spread_l1_amount_currency, lv_spread_l1_amount_ccy)    = 1
        OR nrg_common.has_value_changed(binary_type, p_binary_type)                                    = 1
        OR nrg_common.has_value_changed(settle_time, p_settle_time)                      = 1
        OR nrg_common.has_value_changed(tenor, p_tenor)                                  = 1
        OR nrg_common.has_value_changed(strike_price_additional, p_strike_price_additional) = 1
        OR nrg_common.has_value_changed(trade_instrument_amount, p_trade_instrument_amount) = 1
        OR nrg_common.has_value_changed(reference_trade_price, p_reference_trade_price) = 1
        OR nrg_common.has_value_changed(ladder_quantities_override, p_ladder_quantities_override) = 1
        --OR nrg_common.has_value_changed(hdg_exec_broker_code, p_hdg_exec_broker_code) = 1
        OR nrg_common.has_value_changed(hdg_execution_commission, p_hdg_execution_commission) = 1
        --OR nrg_common.has_value_changed(hdg_broker_code, p_hdg_broker_code) = 1
        --OR nrg_common.has_value_changed(hdg_ext_broker_accnt_num, p_hdg_ext_broker_accnt_num) = 1
        OR nrg_common.has_value_changed(cmc_trade_instrument_price, p_cmc_trade_instrument_price) = 1
        OR nrg_common.has_value_changed(hdg_spot_price, p_hdg_spot_price) = 1
        OR nrg_common.has_value_changed(hdg_trade_settlement_date, p_hdg_trade_settlement_date) = 1
        OR nrg_common.has_value_changed(hdg_redemption_date, p_hdg_redemption_date) = 1
        OR nrg_common.has_value_changed(hdg_current_coupon_date, p_hdg_current_coupon_date) = 1
        OR nrg_common.has_value_changed(hdg_accrued_interest_days, p_hdg_accrued_interest_days) = 1
        OR nrg_common.has_value_changed(hdg_accrued_interest_amount, p_hdg_accrued_interest_amount) = 1
        OR nrg_common.has_value_changed(hdg_effctv_intrst_rate, p_hdg_effctv_intrst_rate) = 1
        OR nrg_common.has_value_changed(hdg_effctv_intrst_base_amnt, p_hdg_effctv_intrst_base_amnt) = 1
        OR nrg_common.has_value_changed(hdg_afs_reserve_amount, p_hdg_afs_reserve_amount) = 1
        OR nrg_common.has_value_changed(hdg_opening_reference, p_hdg_opening_reference) = 1
        OR nrg_common.has_value_changed(hdg_client_order_link_id, p_hdg_client_order_link_id) = 1
        OR nrg_common.has_value_changed(hdg_crrnt_coupon_pymnt_date, p_hdg_crrnt_coupon_pymnt_date) = 1
        OR nrg_common.has_value_changed(hdg_redemption_payment_date, p_hdg_redemption_payment_date) = 1
        OR nrg_common.has_value_changed(trade_time_2, p_trade_time_2) = 1
        OR nrg_common.has_value_changed(trade_time_confirmation, p_trade_time_confirmation) = 1
        OR nrg_common.has_value_changed(hdg_report_source, p_hdg_report_source) = 1
        OR nrg_common.has_value_changed(hdg_original_currency, p_hdg_original_currency) = 1
        OR nrg_common.has_value_changed(hdg_original_amount, p_hdg_original_amount) = 1
        OR nrg_common.has_value_changed(hdg_crypto_inst_ccy_fx_rate, p_hdg_crypto_inst_ccy_fx_rate) = 1
        OR nrg_common.has_value_changed(trade_price_offset, p_trade_price_offset) = 1
        OR nrg_common.has_value_changed(qt_is_frst_gd_in_cntns_trdng, lv_qt_is_frst_gd_in_cntns_trdg) = 1
        OR nrg_common.has_value_changed(hdg_trade_settlement_ref, p_hdg_trade_settlement_ref) = 1
      );
      IF p_platform                                                                         <> 'NG' THEN
        BEGIN
          SELECT i.currency
          INTO lv_instrument_ccy
          FROM bi_ods.instruments i
          WHERE i.instrument_code = p_product_instrument_code;
        exception
        WHEN no_data_found THEN
          lv_instrument_ccy:=NULL;
        END;

        nrg_latest_position.put_position(p_user => p_user,
                                  p_effective_start_timestamp => p_effective_start_timestamp,
                                  p_order_id => p_order_id,
                                  p_state => 'EXECUTED',
                                  p_platform => p_platform,
                                  p_trading_account_id => nvl(p_trading_account_id, lv_internal_account_id),
                                  p_trading_account_type => lv_trading_account_type,
                                  p_trading_account_codifier => p_trading_account_codifier,
                                  p_trading_account_function => lv_trading_account_function,
                                  p_mm_account_id => p_mm_account_id,
                                  p_product_instrument_code => p_product_instrument_code,
                                  p_product_wrapper_code => p_product_wrapper_code,
                                  p_product_point_multiplier => p_product_point_multiplier,
                                  p_product_financing_ratio_max => NULL,
                                  p_product_generation => p_product_generation,
                                  p_is_crncy_in_frctnl_prts => lv_is_prdct_ccy_in_frtnl_prts,
                                  p_fractional_part_ratio => lv_fractional_part_ratio,
                                  p_mm_instrument_id => p_mm_instrument_id,
                                  p_requested_direction => p_direction,
                                  p_creation_time => p_creation_time,
                                  p_trade_id => p_trade_id,
                                  p_trade_time => lv_trade_time,
                                  p_is_primary => p_is_primary,
                                  p_trdng_accnt_prmry_crrncy => p_trading_account_currency,
                                  p_product_currency =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_instrument_ccy
                                                        ELSE
                                                          lv_product_currency
                                                        END,
                                  p_trade_quantity =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_trade_amount
                                                        ELSE
                                                          lv_trade_quantity
                                                        END,
                                  p_trade_quantity_currency =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_trade_amount_currency
                                                        ELSE
                                                          lv_mm_trade_quantity_ccy
                                                        END,
                                  p_nrmlsd_opn_qty_in_trdng_ccy =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_normalised_quantity*lv_normalised_trade_price
                                                        ELSE
                                                          lv_normalised_quantity
                                                        END,
                                  p_trade_price => p_trade_price, p_normalised_open_price => lv_normalised_trade_price, p_open_trade_financing_ratio => NULL, p_trade_amount =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_normalised_quantity
                                                        ELSE
                                                          lv_normalised_quantity*lv_normalised_trade_price
                                                        END,
                                  p_trade_amount_currency =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_mm_trade_quantity_ccy
                                                        ELSE
                                                          lv_trade_amount_currency
                                                        END,
                                  p_amt_in_trdng_accnt_prmry_ccy => p_trd_amnt_in_trdng_acnt_crncy,
                                  p_nrmlsd_opn_vle_in_trdng_ccy =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_normalised_quantity
                                                        ELSE
                                                          lv_normalised_quantity*lv_normalised_trade_price
                                                        END,
                                  p_margin_type => p_margin_type,
                                  p_normalised_margin_type => p_margin_type,
                                  p_normalised_margin_req => p_margin_requirement,
                                  p_margin => NULL,
                                  p_mrgn_trdng_accnt_prmry_ccy => NULL,
                                  p_margin_currency =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_trade_amount_currency
                                                        ELSE
                                                          lv_mm_trade_quantity_ccy
                                                        END,
                                  p_margin_secondary => NULL, p_margin_secondary_currency =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_mm_trade_quantity_ccy
                                                        ELSE
                                                          lv_trade_amount_currency
                                                        END,
                                  p_margin_fx_rate => NULL,
                                  p_open_trade_qntty_fx_rate => NULL,
                                  p_cstm_info_vrtl_prtfl_cd => NULL,
                                  p_quantity_designator => p_quantity_designator,
                                  p_mm_backoffice_ref => NULL,
                                  p_mm_value_date => p_mm_value_date,
                                  p_normalised_trading_currency =>
                                                        CASE
                                                        WHEN p_is_primary = 'NO' THEN
                                                          lv_instrument_ccy
                                                        ELSE
                                                          lv_product_currency
                                                        END,
                                  p_last_modified_time => p_update_time,
                                  p_old_effective_start_time => lv_effective_start_time,
                                  p_logical_load_timestamp => lv_logical_load_timestamp ,
                                  p_record_source => NULL ,
                                  p_load_latest_positions => 'YES' ,
                                  p_direction_multiplier => NULL ,
                                  p_trading_scope => NULL,
                                  p_strike_price => p_strike_price);
      END IF;
      --
      -- Write To History if update occurred
      --
      IF lv_old_trade.effective_start_timestamp <> gc_default_timestamp AND SQL%rowcount > 0 THEN
        --
        --If the existing trade is not a stub record and the same record has not been replayed
        --then put the old record in the history
        --
        put_history (p_old_trade_record => lv_old_trade, p_effective_end_timestamp => p_effective_start_timestamp, p_action => 'U');
      END IF;
      lv_old_trade               := NULL;
    WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time > p_effective_start_timestamp THEN
      --
      --When the effective start time of the incoming record is smaller than the effective start time of the
      --trade on the trades table then it signifies old message is being replayed.
      --Write to history table directly
      --
      SELECT p_trade_id,
          p_platform,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          p_effective_start_timestamp,
          nvl(p_trading_account_id,lv_internal_account_id),
          lv_trading_account_type,
          p_trading_account_codifier,
          p_order_id,
          p_product_instrument_code,
          p_product_wrapper_code,
          p_product_generation,
          --p_product_schema_code,
          --p_prdct_financng_ratio_max,
          p_session_key,
          p_booking_number,
          --p_cp1_cash_account_number,
          --p_cp2_cash_account_number,
          p_publish_time,
          p_event_time,
          p_creation_time,
          p_update_time,
          p_creation_identity_token,
          p_crtn_on_bhlf_of_idntty_tkn,
          p_update_identity_token,
          p_updt_on_bhlf_of_idntty_tkn,
          p_version_number,
          p_is_deleted,
          --p_visit_id,
          p_channel_id,
          p_request_id,
          p_reduced_trade_id,
          p_reversed_trade_id,
          lv_business_date,
          lv_reporting_date,
          lv_trade_time,
          lv_order_type,
          lv_order_sub_type,
          lv_trading_account_function,
          p_trading_account_currency,
          lv_product_point_multiplier,
          lv_fractional_part_ratio,
          lv_product_currency,
          p_is_primary,
          p_is_late_deal,
          p_mm_value_date,
          p_direction,
          p_direction_multiplier,
          p_quoted_l1_ask_price,
          p_quoted_l1_bid_price,
          p_trade_price,
          --p_price_designator,
          p_quantity_designator,
          lv_normalised_quantity,
          lv_trade_quantity_currency,
          --p_quantity_fx_rate_bid,
          --p_quantity_fx_rate_ask,
          lv_trade_amount,
          p_trade_amount_currency,
          p_trd_amnt_in_trdng_acnt_crncy,
          --p_financing_ratio,
          p_margin_type,
          p_margin_requirement,
          --p_margin_fx_rate_bid,
          --p_margin_fx_rate_ask,
          --p_trade_margin_amount,
          --p_trade_margin_currency,
          --p_trd_mrgn_in_trdng_acnt_crncy,
          p_trade_profit_loss,
          p_trade_profit_loss_currency,
          p_trd_pl_in_trdng_accnt_crrncy,
          p_trdng_accnt_custom_info,
          --p_ta_cstm_info_vrtl_prtfl_cd,
          --p_cp2_trading_account_id,
          --lv_cp2_trading_account_type,
          --p_cp2_trading_account_codifier,
          --p_cp2_custom_info,
          p_quote_id,
          p_quote_l1_bid,
          p_quote_l1_ask,
          --p_quantity_fx_rate_id,
          --p_margin_fx_rate_id,
          p_profit_loss_fx_rate_id,
          p_profit_loss_fx_rate_bid,
          p_profit_loss_fx_rate_ask,
          p_is_trd_of_cntrlld_ordr,
          p_reversed_order_id,
          p_mm_instrument_id,
          p_related_child_order_type,
          p_is_mandatory,
          p_rqustd_trd_close_order_id,
          p_related_parent_order_id,
          p_limit_price_condition,
          p_limit_trailing_distance,
          --       p_limit_trailing_best_price,
          p_limit_price,
          lv_is_prdct_ccy_in_frtnl_prts,
          p_quote_ask_price,
          p_quote_bid_price,
          lv_trade_id_short_form,
          lv_order_id_short_form,
          p_mm_account_id,
          lv_trade_quantity,
          p_is_limit_trailing,
          lv_normalised_trade_price,
          /***** Begin Modification for V4.5 - BER774 *****/
          --p_trade_quantity_currency,
          /***** Added Back for BER-774 *****/
          p_trade_quantity_currency,
          /***** End Modification for V4.5 *****/
          p_price_source,
          p_record_source,
          p_amount_fx_rate_id,
          p_amount_fx_rate,
          p_price_level,
          p_aggregated_price_quantity,
          p_quote_received_time,
          p_app_to_units,
          p_rollover_closing_trade_id,
          --p_is_hedged
          /***** Begin Modification for V4.5 - BER774 *****/
          --,
          p_hdg_ext_trxn_id,
          p_hdg_milliways_seq_id,
          p_hdg_src,
          p_hdg_src_ref_id,
          p_hdg_trxn_type,
          p_hdg_execn_type,
          p_hdg_ext_trxn_time,
          p_hdg_trade_dt,
          p_hdg_ext_trade_dt,
          p_hdg_accnting_trade_dt,
          p_hdg_exec_broker_accnt_num,
          p_hdg_asset_class,
          p_hdg_instr_code_ext,
          p_hdg_expy_month_code,
          p_hdg_risk_bucket,
          p_hdg_cmc_trader,
          p_trade_comment,
          p_hdg_calc_commission,
          p_hdg_commission,
          p_hdg_is_risk_relevant,
          p_hdg_is_reg_repng_relvnt,
          p_hdg_cancln_ref,
          p_hdg_correction_ref,
          p_hdg_cross_ref,
          p_is_rollover_closing,
          p_price_offset_index,
          p_execution_started_time,
          p_trade_instrument_price,
          p_strike_price,
          p_quote_depth_price,
          lv_spread_l1_amount,
          lv_spread_l1_amount_ccy,
          p_binary_type,
          p_settle_time,
          p_tenor,
          p_strike_price_additional,
          p_trade_instrument_amount,
          p_reference_trade_price,
          p_ladder_quantities_override,
          --p_hdg_exec_broker_code,
          p_hdg_execution_commission,
          p_cmc_trade_instrument_price,
          --p_hdg_broker_code,
          --p_hdg_ext_broker_accnt_num
          p_hdg_spot_price,
          p_hdg_trade_settlement_date,
          p_hdg_redemption_date,
          p_hdg_current_coupon_date,
          p_hdg_accrued_interest_days,
          p_hdg_accrued_interest_amount,
          p_hdg_effctv_intrst_rate,
          p_hdg_effctv_intrst_base_amnt,
          p_hdg_afs_reserve_amount,
          p_hdg_opening_reference,
          p_hdg_client_order_link_id,
          p_hdg_crrnt_coupon_pymnt_date,
          p_hdg_redemption_payment_date,
          p_trade_time_2,
          p_trade_time_confirmation,
          p_hdg_report_source,
          p_hdg_original_currency,
          p_hdg_original_amount,
          p_hdg_crypto_inst_ccy_fx_rate,
          p_trade_price_offset,
          lv_qt_is_frst_gd_in_cntns_trdg,
          p_hdg_trade_settlement_ref
      INTO lv_old_trade.trade_id,
        lv_old_trade.platform,
        lv_old_trade.logical_load_timestamp,
        lv_old_trade.created_by,
        lv_old_trade.create_timestamp,
        lv_old_trade.updated_by,
        lv_old_trade.update_timestamp,
        lv_old_trade.effective_start_timestamp,
        lv_old_trade.trading_account_id,
        lv_old_trade.trading_account_type,
        lv_old_trade.trading_account_codifier,
        lv_old_trade.order_id,
        lv_old_trade.product_instrument_code,
        lv_old_trade.product_wrapper_code,
        lv_old_trade.product_generation,
        --lv_old_trade.product_schema_code,
        --lv_old_trade.prdct_financing_ratio_max,
        lv_old_trade.session_key,
        lv_old_trade.booking_number,
        --lv_old_trade.cp1_cash_account_number,
        --lv_old_trade.cp2_cash_account_number,
        lv_old_trade.publish_time,
        lv_old_trade.event_time,
        lv_old_trade.creation_time,
        lv_old_trade.update_time,
        lv_old_trade.creation_identity_token,
        lv_old_trade.crtn_on_bhlf_of_idntty_tkn,
        lv_old_trade.update_identity_token,
        lv_old_trade.updt_on_bhlf_of_idntty_tkn,
        lv_old_trade.version_number,
        lv_old_trade.is_deleted,
        --lv_old_trade.visit_id,
        lv_old_trade.channel_id,
        lv_old_trade.request_id,
        lv_old_trade.reduced_trade_id,
        lv_old_trade.reversed_trade_id,
        lv_old_trade.business_date,
        lv_old_trade.reporting_date,
        lv_old_trade.trade_time,
        lv_old_trade.order_type,
        lv_old_trade.controlled_order_type,
        lv_old_trade.trading_account_function,
        lv_old_trade.trading_account_currency,
        lv_old_trade.product_point_multiplier,
        lv_old_trade.product_fractional_part_ratio,
        lv_old_trade.product_currency,
        lv_old_trade.is_primary,
        lv_old_trade.is_late_deal,
        lv_old_trade.mm_value_date,
        lv_old_trade.direction,
        lv_old_trade.direction_multiplier,
        lv_old_trade.quoted_l1_ask_price,
        lv_old_trade.quoted_l1_bid_price,
        lv_old_trade.trade_price,
        --lv_old_trade.price_designator,
        lv_old_trade.quantity_designator,
        lv_old_trade.normalised_quantity,
        lv_old_trade.normalised_quantity_currency,
        --lv_old_trade.quantity_fx_rate_bid,
        --lv_old_trade.quantity_fx_rate_ask,
        lv_old_trade.trade_amount,
        lv_old_trade.trade_amount_currency,
        lv_old_trade.trd_amnt_in_trdng_accnt_crrncy,
        --lv_old_trade.financing_ratio,
        lv_old_trade.margin_type,
        lv_old_trade.margin_requirement,
        --lv_old_trade.margin_fx_rate_bid,
        --lv_old_trade.margin_fx_rate_ask,
        --lv_old_trade.trade_margin_amount,
        --lv_old_trade.trade_margin_currency,
        --lv_old_trade.trd_mrgn_in_trdng_accnt_crrncy,
        lv_old_trade.trade_profit_loss,
        lv_old_trade.trade_profit_loss_currency,
        lv_old_trade.trd_pl_in_trdng_accnt_crrncy,
        lv_old_trade.trdng_accnt_custom_info,
        --lv_old_trade.ta_cstm_info_vrtl_prtfl_cd,
        --lv_old_trade.cp2_trading_account_id,
        --lv_old_trade.cp2_trading_account_type,
        --lv_old_trade.cp2_trading_account_codifier,
        --lv_old_trade.cp2_custom_info,
        lv_old_trade.quote_id,
        lv_old_trade.quote_l1_bid,
        lv_old_trade.quote_l1_ask,
        --lv_old_trade.quantity_fx_rate_id,
        --lv_old_trade.margin_fx_rate_id,
        lv_old_trade.profit_loss_fx_rate_id,
        lv_old_trade.profit_loss_fx_rate_bid,
        lv_old_trade.profit_loss_fx_rate_ask,
        lv_old_trade.is_trd_of_cntrlld_ordr,
        lv_old_trade.reversed_order_id,
        lv_old_trade.mm_instrument_id,
        lv_old_trade.related_child_order_type,
        lv_old_trade.is_mandatory,
        lv_old_trade.requested_trade_close_order_id,
        lv_old_trade.related_parent_order_id,
        lv_old_trade.limit_price_condition,
        lv_old_trade.limit_trailing_distance,
        --      lv_old_trade.limit_trailing_best_price,
        lv_old_trade.limit_price,
        lv_old_trade.is_prdct_crrncy_in_frtnl_prts,
        lv_old_trade.quote_ask_price,
        lv_old_trade.quote_bid_price,
        lv_old_trade.trade_id_short_form,
        lv_old_trade.order_id_short_form,
        lv_old_trade.mm_account_id,
        lv_old_trade.trade_quantity,
        lv_old_trade.is_limit_trailing,
        lv_old_trade.normalised_trade_price,
        /***** Begin Modification for V4.5 - BER774 *****/
        --lv_old_trade.trade_quantity_currency,
        /***** Added Back for BER-774 *****/
        lv_old_trade.trade_quantity_currency,
        /***** End Modification for V4.5 *****/
        lv_old_trade.price_source,
        lv_old_trade.record_source,
        lv_old_trade.amount_fx_rate_id,
        lv_old_trade.amount_fx_rate,
        lv_old_trade.price_level,
        lv_old_trade.aggregated_price_quantity,
        lv_old_trade.quote_received_time,
        lv_old_trade.app_to_units,
        lv_old_trade.rollover_closing_trade_id,
        --lv_old_trade.is_hedged
        /***** Begin Modification for V4.5 - BER774 *****/
        --,
        lv_old_trade.hdg_ext_trxn_id,
        lv_old_trade.hdg_milliways_seq_id,
        lv_old_trade.hdg_src,
        lv_old_trade.hdg_src_ref_id,
        lv_old_trade.hdg_trxn_type,
        lv_old_trade.hdg_execn_type,
        lv_old_trade.hdg_ext_trxn_time,
        lv_old_trade.hdg_trade_dt,
        lv_old_trade.hdg_ext_trade_dt,
        lv_old_trade.hdg_accnting_trade_dt,
        lv_old_trade.hdg_exec_broker_accnt_num,
        lv_old_trade.hdg_asset_class,
        lv_old_trade.hdg_instr_code_ext,
        lv_old_trade.hdg_expy_month_code,
        lv_old_trade.hdg_risk_bucket,
        lv_old_trade.hdg_cmc_trader,
        lv_old_trade.trade_comment,
        lv_old_trade.hdg_calc_commission,
        lv_old_trade.hdg_commission,
        lv_old_trade.hdg_is_risk_relevant,
        lv_old_trade.hdg_is_reg_repng_relvnt,
        lv_old_trade.hdg_cancln_ref,
        lv_old_trade.hdg_correction_ref,
        lv_old_trade.hdg_cross_ref,
        lv_old_trade.is_rollover_closing,
        lv_old_trade.price_offset_index,
        lv_old_trade.execution_started_time,
        lv_old_trade.trade_instrument_price,
        lv_old_trade.strike_price,
        lv_old_trade.quote_depth_price,
        lv_old_trade.spread_l1_amount,
        lv_old_trade.spread_l1_amount_currency,
        lv_old_trade.binary_type,
        lv_old_trade.settle_time,
        lv_old_trade.tenor,
        lv_old_trade.strike_price_additional,
        lv_old_trade.trade_instrument_amount,
        lv_old_trade.reference_trade_price,
        lv_old_trade.ladder_quantities_override,
        --lv_old_trade.hdg_exec_broker_code,
        lv_old_trade.hdg_execution_commission,
        lv_old_trade.cmc_trade_instrument_price,
        --lv_old_trade.hdg_broker_code,
        --lv_old_trade.hdg_ext_broker_accnt_num
        lv_old_trade.hdg_spot_price,
        lv_old_trade.hdg_trade_settlement_date,
        lv_old_trade.hdg_redemption_date,
        lv_old_trade.hdg_current_coupon_date,
        lv_old_trade.hdg_accrued_interest_days,
        lv_old_trade.hdg_accrued_interest_amount,
        lv_old_trade.hdg_effctv_intrst_rate,
        lv_old_trade.hdg_effctv_intrst_base_amnt,
        lv_old_trade.hdg_afs_reserve_amount,
        lv_old_trade.hdg_opening_reference,
        lv_old_trade.hdg_client_order_link_id,
        lv_old_trade.hdg_crrnt_coupon_pymnt_date,
        lv_old_trade.hdg_redemption_payment_date,
        lv_old_trade.trade_time_2,
        lv_old_trade.trade_time_confirmation,
        lv_old_trade.hdg_report_source,
        lv_old_trade.hdg_original_currency,
        lv_old_trade.hdg_original_amount,
        lv_old_trade.hdg_crypto_inst_ccy_fx_rate,
        lv_old_trade.trade_price_offset,
        lv_old_trade.qt_is_frst_gd_in_cntns_trdng,
        lv_old_trade.hdg_trade_settlement_ref
      FROM dual;
      put_history (p_old_trade_record => lv_old_trade, p_effective_end_timestamp => p_effective_start_timestamp, p_action => 'I');
      lv_old_trade := NULL;
    ELSE
      raise lex_unknown_operation_type;
    END CASE;
  exception
  WHEN lex_trade_not_found THEN
    logger.logger.SEVERE('Trade Deleted Before Update and After Insert');
    logger.logger.set_module(NULL);
    raise_application_error(-20003, 'Trade Deleted Before Update and After Insert');
  WHEN lex_unknown_operation_type THEN
    logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
    logger.logger.set_module(NULL);
    raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_trade;
  -- ===================================================================================
  -- get_order_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of order_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform MM or NG
  --     p_trade_time_from                 trade time from - Time trade was created at source
  --     p_trade_time_to                   trade time to - Time trade was created at source
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20005    Invalid Platform Name. Expected Platform names are NG or MMCFD or MMSB
  --      -20004    Dafault Exception
  -- -----------------------------------------------------------------------------------
  FUNCTION get_order_ids(
      p_platform        IN trades.platform%TYPE,
      p_trade_time_from IN trades.creation_time%TYPE,
      p_trade_time_to   IN trades.creation_time%TYPE)
    RETURN sys_refcursor
  IS
    lcuv_result sys_refcursor;
    lex_unknown_platform exception;
  BEGIN
    logger.logger.set_module('get_order_ids');
    CASE
    WHEN p_platform IS NULL THEN
      raise lex_unknown_platform;
    WHEN p_platform                                                                                                                                   = 'NG' THEN
      OPEN lcuv_result FOR SELECT order_id FROM trades WHERE trade_time                                                                              >= p_trade_time_from AND trade_time < p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND platform = 'NG' AND record_source != 'SPEEDBET';
    WHEN p_platform                                                                                                                                   = 'MMCFD' THEN
      OPEN lcuv_result FOR SELECT order_id FROM trades WHERE update_time BETWEEN p_trade_time_from AND p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND platform = 'MMCFD';
    WHEN p_platform                                                                                                                                   = 'MMSB' THEN
      OPEN lcuv_result FOR SELECT order_id FROM trades WHERE update_time BETWEEN p_trade_time_from AND p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND platform = 'MMSB';
    ELSE
      raise lex_unknown_platform;
    END CASE;
    logger.logger.set_module(NULL);
    RETURN lcuv_result;
  exception
  WHEN lex_unknown_platform THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20005, 'Invalid Platform Name. Expected Platform names are NG or MM');
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END get_order_ids;
/***** Begin Modification for V4.5 - BER774 *****/
  -- ===================================================================================
  -- get_hedge_trade_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of hedge trade_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform - NG
  --     p_trade_time_from                 trade time from - Time trade was created at source
  --     p_trade_time_to                   trade time to - Time trade was created at source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  --     -20005    Invalid Platform Name. Expected Platform name is NG
  -- -----------------------------------------------------------------------------------
  FUNCTION get_hedge_trade_ids(
      p_platform        IN trades.platform%TYPE,
      p_trade_time_from IN trades.creation_time%TYPE,
      p_trade_time_to   IN trades.creation_time%TYPE)
    RETURN sys_refcursor
  IS
    lcuv_result sys_refcursor;
    lex_unknown_platform exception;
  BEGIN
    logger.logger.set_module('get_hedge_trade_ids');
    CASE
    WHEN p_platform IS NULL THEN
      raise lex_unknown_platform;
    WHEN p_platform                                                         = 'NG' THEN
      OPEN lcuv_result FOR SELECT trade_id FROM trades WHERE creation_time >= p_trade_time_from AND creation_time < p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND platform = 'NG' AND trading_account_type = 'HEDGE';
    ELSE
      raise lex_unknown_platform;
    END CASE;
    logger.logger.set_module(NULL);
    RETURN lcuv_result;
  exception
  WHEN lex_unknown_platform THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20005, 'Invalid Platform Name. Expected Platform name is NG');
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END get_hedge_trade_ids;
/***** End Modification for V4.5 *****/
  -- ===================================================================================
  -- trade_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of trade_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform MMCFD or MMSB or NG
  --     p_trade_time_from                 trade time from - Time trade was created at source
  --     p_trade_time_to                   trade time to - Time trade was created at source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  --     -20005    Invalid Platform Name. Expected Platform names are NG or MMCFD or MMSB
  -- -----------------------------------------------------------------------------------
  FUNCTION get_trade_ids(
      p_platform        IN trades.platform%TYPE,
      p_trade_time_from IN trades.creation_time%TYPE,
      p_trade_time_to   IN trades.creation_time%TYPE)
    RETURN sys_refcursor
  IS
    lcuv_result sys_refcursor;
    lex_unknown_platform exception;
  BEGIN
    logger.logger.set_module('get_trade_ids');
    CASE
    WHEN p_platform IS NULL THEN
      raise lex_unknown_platform;
    WHEN p_platform                                                       = 'NG' THEN
      OPEN lcuv_result FOR SELECT trade_id FROM trades WHERE trade_time  >= p_trade_time_from AND trade_time < p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND trading_account_type = 'CUSTOMER' AND platform = 'NG' AND record_source != 'SPEEDBET';
    WHEN p_platform                                                       = 'MMCFD' THEN
      OPEN lcuv_result FOR SELECT trade_id FROM trades WHERE update_time >= p_trade_time_from AND update_time < p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND platform = 'MMCFD';
    WHEN p_platform                                                       = 'MMSB' THEN
      OPEN lcuv_result FOR SELECT trade_id FROM trades WHERE update_time >= p_trade_time_from AND update_time < p_trade_time_to AND effective_start_timestamp <> gc_default_timestamp AND platform = 'MMSB';
    ELSE
      raise lex_unknown_platform;
    END CASE;
    logger.logger.set_module(NULL);
    RETURN lcuv_result;
  exception
  WHEN lex_unknown_platform THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20005, 'Invalid Platform Name. Expected Platform names are NG or MM');
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END get_trade_ids;
-- ===================================================================================
-- get_stubbed_ids
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Function to return a set of stubbed cash account id's
--
-- Notes:
-- ------
--
--     None
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
--     p_platform                       Cash Account Platform
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--     -20004    Default Exception
-- -----------------------------------------------------------------------------------
  FUNCTION get_stubbed_ids(
      p_batch_limit IN NUMBER,
      p_platform    IN VARCHAR2)
    RETURN sys_refcursor
  IS
    lcuv_result sys_refcursor;
    lex_unknown_platform exception;
  BEGIN
    logger.logger.set_module('get_stubbed_ids');
    IF p_platform IS NULL OR p_platform NOT IN ('NG','MMSB','MMCFD') THEN
      raise lex_unknown_platform;
    END IF;
    IF p_platform = 'NG' THEN
      CASE
      WHEN p_batch_limit IS NOT NULL THEN
        OPEN lcuv_result FOR SELECT trade_id,
        order_id FROM trades WHERE effective_start_timestamp = gc_default_timestamp AND platform = p_platform AND record_source != 'SPEEDBET' AND ROWNUM <= p_batch_limit;
      ELSE
        OPEN lcuv_result FOR SELECT trade_id,
        order_id FROM trades WHERE effective_start_timestamp = gc_default_timestamp AND record_source != 'SPEEDBET' AND platform = p_platform;
      END CASE;
    ELSE
      CASE
      WHEN p_batch_limit IS NOT NULL THEN
        OPEN lcuv_result FOR SELECT trade_id,
        order_id FROM trades WHERE effective_start_timestamp = gc_default_timestamp AND platform = p_platform AND ROWNUM <= p_batch_limit AND order_id IS NOT NULL;
      ELSE
        OPEN lcuv_result FOR SELECT trade_id,
        order_id FROM trades WHERE effective_start_timestamp = gc_default_timestamp AND platform = p_platform AND order_id IS NOT NULL;
      END CASE;
    END IF;
    logger.logger.set_module(NULL);
    RETURN lcuv_result;
  exception
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END get_stubbed_ids;
END nrg_trade;
/