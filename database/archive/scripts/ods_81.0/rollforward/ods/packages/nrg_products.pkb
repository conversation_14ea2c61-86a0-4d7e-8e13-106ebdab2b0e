CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_products
AS
    -- ===================================================================================
    -- NRG_PRODUCTS
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the products model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     27/09/2011   Sanket Mittal      1.0     Creation
    --     12/10/2011   Manoj Kumar        1.1     Added the put_product,remove_product method
    --     27/10/2011   Sanket Mittal      1.2     Updated the package for changed structures
    --     08/11/2011   Manoj Kumar        1.3     Modifed to implement review comments
    --     15/11/2011   Sanket Mittal      1.4     Modified the create stub procedure to handle
    --                                             stub creation for only the instruments
    --     29/11/2011   Mark Gornicki      1.5     Incorporate the CFD Multiplier logic for MM Products
    --     28/05/2012   Mark Gornicki      1.6     Added the following additional attributes to Instruments
    --                                             MMUID,MMUID2,MMVALUEID for MarketMaker Underlying instrument ids
    --     09/10/2012   Sanket Mittal      1.7     Added new attributes to instrument entity
    --     21/11/2012   Shanavaz Malayodu  1.8     Added new attribute financial_instrument_type to instrument entity
    --     05/03/2013   Sanket Mittal      1.9     Applied a fix for instruments and mm_products
    --     18/04/2013   Sanket Mittal      2.0     P2 Changes. Added entities margin tier and instrument feed settings
    --     26/06/2013   Prachi Shah        2.1     Applied a fix for margin tier, to make is_deleted flag as NO when a margin tier is republished.
    --     01/08/2013   Prachi Shah        2.2     Included currency in insert for bi_ods.instruments table
    --     26/08/2013   Ravi Shankar       2.3     Included Logic to Calculate, Generate and Populate Margin Tiers Summary and History Tables as Part of BER - 588.
    --     12/11/2013   Adam Krasnicki     2.4     `RETURN` statement added to in put_mrgn_trs_summry procedure instead of RAISE ERROR
    --     18/11/2013   Adam Krasnicki     2.5     Update statement of the margin_tiers table in put_product_set is altered to consider the p_effective_start_timestamp
    --                                             due to iterative (in few batches usually) margin tiers load
    --     20/11/2013   Adam Krasnicki     2.6     put_mrgn_trs_summry is called from NRG after put_product_set
    --     27/11/2013   Adam Krasnicki     2.7     put_margin_tier_set is added to split the product_set load from margin_tiers
    --     06/12/2013   Adam Krasnicki     2.8     reg_instr_identification_tab added to put_product_set procedure
    --     16/12/2013   Adam Krasnicki     2.9     margin_tiers separated from the other product dimensions
    --     18/01/2014   Adam Krasnicki     3.0     Margin tiers insert changed by FORALL statement
    --     27/01/2014   Adam Krasnicki     3.1     REG_INSTR_IDENTIFICATION_TAB has 3 new columns
    --     06/05/2014   Adam Krasnicki     3.2     INSTRUMENT_OBJ/INSTRUMENTS[_H] takes two additional parameters BER-1032
    --     09/05/2014   Adam Krasnicki     3.2     Update Margin Tiers logic to use upper case when handling schema codes
    --     15/07/2014   Adam Krasnicki     3.2     Marting_tiers_current_data table truncated
    --     15/08/2014   Adam Krasnicki     3.2     Security_Typ column added to instruments[_h] and obj/tab
    --     15/08/2014   Adam Krasnicki     3.3     Type instrument_languages_tab added to instruments_obj
    --     03/11/2014   Adam Krasnicki     3.3     3 new attributes added to instrument table
    --     01/12/2014   Adam Krasnicki     3.3     Instrument_type_code column added to instruments[_h] tables; INSTRUMENT_TYPE_LANGUAGES[_H] tables added
    --     23/02/2014   Adam Krasnicki     3.3     p_products_gsko as a new parameter
    --     30/03/2015   Adam Krasnicki     3.4     Replace strange, highly inefficient, incorrect logic for populating margin_tiers_summary_h table (table was backdated, 90% redundand data removed)
    --     19/06/2015   Adam Krasnicki     3.5     Add 12 new cols to instruments[_h] and 2 new cols to products[_h]
    --     27/07/2015   Adam Krasnicki     3.6     PMS- StopBuffer change
    --     16/11/2015   Sanket Mittal      3.7     PMS CHANGES BER2153 to add new table instrument_markets
    --     30/01/2016   Sanket Mittal      3.8     Added changes for Margin Tiers on product_settings
    --     12/02/2016   Adam Krasnicki     3.9     Package refactoring, performance tunning of pms load
    --     19/02/2016   Adam Krasnicki     4.0     product_settings table: instrument_code column added
    --     26/02/2016   Adam Krasnicki     4.0     marging_tiers_summry_h insert commented
    --     27/02/2016   Adam Krasnicki     4.0     marging_tiers_summry: use_hash hints added
    --     03/03/2016   Sanket Mittal      4.1     Added new attribut primary_inst_code to instruments and changed logic for derived cash instrument
    --     07/03/2016   Sanket Mittal      4.2     BER-2354, BER-2390 Changed logic for instrument type companies and field length for isin
    --     24/06/2016   Sanket Mittal      4.3     BER-2703-SWS 34 - PMS Changes - Instruments
    --     30/06/2016   SKinkahbwala       4.4     Changed procedure put_product_gsko
    --     05/07/2016   Sanket Mittal      4.5     Changed for BER-2724
    --     05/07/2016   Sanket Mittal      4.6     Changed for BER-2725
    --     13/07/2016   Sanket Mittal      4.7     BER-2729 Retire: Margin Tiers Summary
    --     10/10/2016   Sanket Mittal      4.8     Aded call for instrument feed setting procedure
    --     24/10/2016   Sanket Mittal      4.9     Changed to add new attributes for countries
    --     29/11/2016   Sanket Mittal      5.0     BER-3024 PMS Snapshot - Economic Calendar Events and Instrument References
    --     19/11/2016   Sanket Mittal      5.1     BER-3218 NRG_PRODUCTS - new Intruments attribue: MarketDataMappingCode
    --     19/01/2017   Sanket Mittal      5.2     BER-3266 NRG_PRODUCTS - BI_ODS.INSTRUMENTS - New attributes
    --     07/02/2017   S Kinkhabwala      5.3     BER - 3365 - NRG_PRODUCTS - BI_ODS.INSTRUMENTS - missing attribute
    --     07/02/2017   S Kinkhabwala      5.4     BER - 3312 - Add a new proc put_snapshot_log
    --     04/04/2017   Sanket Mittal      5.5     BER-3547 NRG_PRODUCTS.put_economic_cal_events_lang - unique constraint error
    --     07/06/2017   Sanket Mittal      5.6     BER-3171 NRG_PRODUCTS - INSTRUMENT_TYPE_LANGUAGES_H - PK conflict
    --     03/07/2017   Sanket Mittal      5.7     BER-3768 ODS - Instrument Feed Settings needs additional columns
	  --	   17/01/2018	  Sakina Kinkhabwala 5.8	   BER-4271/BER-4296 - put_instruments/put_history - add new attributes
	  --	   25/01/2018	  Sakina Kinkhabwala 5.9		 BER-4271/BER-4296 - Add new proc put_inst_type_broker_margins
	  --     17/03/2018   Deepak Rajurkar    6.0     BER-4548 instruments - new attributes
	  --     24/07/2018   Patrick Dinwiddy   6.1     BER-4740/BER-4742 - 2 new procs/new instrument attributes
    --     22/03/2019   Patrick Dinwiddy   6.2     BER-5032 - new prdct_sttngs_inst_schema attribute
    -- ===================================================================================
    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================

    gc_version            CONSTANT VARCHAR2(3) := '6.2';
    gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
    gc_true               CONSTANT PLS_INTEGER := 1;
    gc_false              CONSTANT PLS_INTEGER := 0;
    gc_default_timestamp  CONSTANT TIMESTAMP :=to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================


    PROCEDURE put_history(p_old_margin_tiers IN product_settings_margin_defs%ROWTYPE,
                          p_effective_end_timestamp product_settings_margin_defs_h.effective_end_timestamp%TYPE,
                          p_action IN product_settings_margin_defs_h.action%TYPE) IS

    BEGIN
      INSERT INTO product_settings_margin_defs_h(product_setting_code,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               effective_end_timestamp,
                                               action,
                                               action_timestamp,
                                               product_schema_code,
                                               trading_risk_schema_code,
                                               trading_account_ccy,
                                               tier1_boundary,
                                               tier1_rate,
                                               tier2_boundary,
                                               tier2_rate,
                                               tier3_boundary,
                                               tier3_rate,
                                               tier4_boundary,
                                               tier4_rate,
                                               tier5_boundary,
                                               tier5_rate,
                                               is_deleted,
                                               instrument_schema_code)
                                        VALUES(p_old_margin_tiers.product_setting_code,
                                               p_old_margin_tiers.logical_load_timestamp,
                                               p_old_margin_tiers.created_by,
                                               p_old_margin_tiers.create_timestamp,
                                               p_old_margin_tiers.updated_by,
                                               p_old_margin_tiers.update_timestamp,
                                               p_old_margin_tiers.effective_start_timestamp,
                                               p_effective_end_timestamp,
                                               p_action,
                                               SYSTIMESTAMP,
                                               p_old_margin_tiers.product_schema_code,
                                               p_old_margin_tiers.trading_risk_schema_code,
                                               p_old_margin_tiers.trading_account_ccy,
                                               p_old_margin_tiers.tier1_boundary,
                                               p_old_margin_tiers.tier1_rate,
                                               p_old_margin_tiers.tier2_boundary,
                                               p_old_margin_tiers.tier2_rate,
                                               p_old_margin_tiers.tier3_boundary,
                                               p_old_margin_tiers.tier3_rate,
                                               p_old_margin_tiers.tier4_boundary,
                                               p_old_margin_tiers.tier4_rate,
                                               p_old_margin_tiers.tier5_boundary,
                                               p_old_margin_tiers.tier5_rate,
                                               p_old_margin_tiers.is_deleted,
                                               p_old_margin_tiers.instrument_schema_code);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE product_settings_margin_defs_h
        SET
            logical_load_timestamp = p_old_margin_tiers.logical_load_timestamp,
            created_by = p_old_margin_tiers.created_by,
            create_timestamp = p_old_margin_tiers.create_timestamp,
            updated_by = p_old_margin_tiers.updated_by,
            update_timestamp = p_old_margin_tiers.update_timestamp,
            effective_end_timestamp = p_effective_end_timestamp,
            action = p_action,
            action_timestamp = SYSTIMESTAMP,
            tier1_boundary = p_old_margin_tiers.tier1_boundary,
            tier1_rate = p_old_margin_tiers.tier1_rate,
            tier2_boundary = p_old_margin_tiers.tier2_boundary,
            tier2_rate = p_old_margin_tiers.tier2_rate,
            tier3_boundary = p_old_margin_tiers.tier3_boundary,
            tier3_rate = p_old_margin_tiers.tier3_rate,
            tier4_boundary = p_old_margin_tiers.tier4_boundary,
            tier4_rate = p_old_margin_tiers.tier4_rate,
            tier5_boundary = p_old_margin_tiers.tier5_boundary,
            tier5_rate = p_old_margin_tiers.tier5_rate
        WHERE product_setting_code = p_old_margin_tiers.product_setting_code AND
              --product_schema_code = p_old_margin_tiers.product_schema_code AND
              --trading_risk_schema_code = p_old_margin_tiers.trading_risk_schema_code AND
              trading_account_ccy = p_old_margin_tiers.trading_account_ccy AND
              is_deleted = p_old_margin_tiers.is_deleted AND
              effective_start_timestamp = p_old_margin_tiers.effective_start_timestamp AND
              instrument_schema_code = p_old_margin_tiers.instrument_schema_code;
    END;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the mm products record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_mm_product_record         This is the old version of the mm products record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_margin_tier_record   margin_tiers%ROWTYPE,
                           p_effective_end_timestamp  margin_tiers_h.effective_end_timestamp%TYPE,
                           p_action                   margin_tiers_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO margin_tiers_h
                              (
                                  margin_tier_code,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  product_setting_code,
                                  product_schema_code,
                                  trading_risk_schema_code,
                                  currency,
                                  margin_size_boundary,
                                  margin_rate,
                                  last_published_date,
                                  initial_published_date,
                                  is_deleted
                                  )
                              VALUES
                              (
                                 p_old_margin_tier_record.margin_tier_code,
                                 p_old_margin_tier_record.logical_load_timestamp,
                                 p_old_margin_tier_record.created_by,
                                 p_old_margin_tier_record.create_timestamp,
                                 p_old_margin_tier_record.updated_by,
                                 p_old_margin_tier_record.update_timestamp,
                                 p_old_margin_tier_record.effective_start_timestamp,
                                 p_effective_end_timestamp,
                                 p_action,
                                 SYSTIMESTAMP,
                                 p_old_margin_tier_record.product_setting_code,
                                 p_old_margin_tier_record.product_schema_code,
                                 p_old_margin_tier_record.trading_risk_schema_code,
                                 p_old_margin_tier_record.currency,
                                 p_old_margin_tier_record.margin_size_boundary,
                                 p_old_margin_tier_record.margin_rate,
                                 p_old_margin_tier_record.last_published_date,
                                 p_old_margin_tier_record.initial_published_date,
                                 p_old_margin_tier_record.is_deleted
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE margin_tiers_h
                SET logical_load_timestamp = p_old_margin_tier_record.logical_load_timestamp,
                    created_by = p_old_margin_tier_record.created_by,
                    create_timestamp = p_old_margin_tier_record.create_timestamp,
                    updated_by = p_old_margin_tier_record.updated_by,
                    update_timestamp = p_old_margin_tier_record.update_timestamp,
                   -- 08/11/2013 effective_start_timestamp = p_old_margin_tier_record.effective_start_timestamp,
                    effective_end_timestamp = p_effective_end_timestamp,
                    action = p_action,
                    action_timestamp = SYSTIMESTAMP,
                    product_setting_code = p_old_margin_tier_record.product_setting_code,
                    product_schema_code = p_old_margin_tier_record.product_schema_code,
                    trading_risk_schema_code = p_old_margin_tier_record.trading_risk_schema_code,
                    currency = p_old_margin_tier_record.currency,
                    margin_size_boundary = p_old_margin_tier_record.margin_size_boundary,
                    margin_rate = p_old_margin_tier_record.margin_rate,
                    last_published_date = p_old_margin_tier_record.last_published_date,
                    initial_published_date = p_old_margin_tier_record.initial_published_date,
                    is_deleted = p_old_margin_tier_record.is_deleted
              WHERE margin_tier_code = p_old_margin_tier_record.margin_tier_code
                AND effective_end_timestamp = p_effective_end_timestamp;
        END put_history;

  -- ===================================================================================
  -- trunc_margin_tier_current_data
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a product margin tier data set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     MARGIN_TIERS_CURRENT_DATA
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

 PROCEDURE trunc_margin_tier_current_data
  IS   PRAGMA AUTONOMOUS_TRANSACTION ;
 BEGIN
     EXECUTE IMMEDIATE 'TRUNCATE TABLE MARGIN_TIERS_CURRENT_DATA';
 END trunc_margin_tier_current_data;


  -- ===================================================================================
  -- put_margin_tier_merge
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a product margin tier data set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     MARGIN_TIERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

PROCEDURE put_margin_tier_merge

IS

    lv_logical_load_timestamp margin_tiers.logical_load_timestamp%TYPE;
    TYPE lvtyp_old_margin_tier IS TABLE OF margin_tiers%rowtype;
    lv_old_margin_tier lvtyp_old_margin_tier;
    l_effective_start_timestamp margin_tiers.effective_start_timestamp%TYPE;

  BEGIN

    logger.logger.set_module('put_margin_tier_merge');

    dbms_stats.gather_table_stats(ownname => 'BI_ODS', tabname => 'MARGIN_TIERS_CURRENT_DATA', cascade => TRUE);

    -- set the logical load timestamp to now

    lv_logical_load_timestamp := SYSTIMESTAMP;


    SELECT MAX(effective_start_timestamp) INTO l_effective_start_timestamp
    FROM margin_tiers_current_data;

    --
    --Select the Mrgin Tier that has got updated
    --

    SELECT mrgn_tr.*
    BULK COLLECT INTO lv_old_margin_tier
    FROM margin_tiers mrgn_tr, (SELECT * FROM margin_tiers_current_data WHERE effective_start_timestamp=l_effective_start_timestamp) new_mrgn_tr
    WHERE mrgn_tr.margin_tier_code = new_mrgn_tr.margin_tier_code AND
          (nrg_common.has_value_changed(mrgn_tr.product_setting_code, new_mrgn_tr.product_setting_code) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.product_schema_code, new_mrgn_tr.product_schema_code) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.trading_risk_schema_code, new_mrgn_tr.trading_risk_schema_code) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.currency, new_mrgn_tr.currency) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.margin_size_boundary, new_mrgn_tr.margin_size_boundary) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.margin_rate, new_mrgn_tr.margin_rate) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.last_published_date, new_mrgn_tr.last_published_date) = 1 OR
           nrg_common.has_value_changed(mrgn_tr.initial_published_date, new_mrgn_tr.initial_published_date) = 1 OR
           mrgn_tr.is_deleted='YES')
    FOR UPDATE OF mrgn_tr.margin_tier_code;

    --
    --Select the instrument feed settings that have got updated
    --


    --
    --Margin Tiers
    --

    FOR lv_cnt IN 1..lv_old_margin_tier.COUNT LOOP
      put_history(p_old_margin_tier_record     => lv_old_margin_tier(lv_cnt),
                  p_effective_end_timestamp    => l_effective_start_timestamp,
                  p_action                     => 'U');
    END LOOP;

    lv_old_margin_tier.DELETE;


    --
    --Margin Tiers
    --

    --
    -- Soft delete Margin Tiers
    --

    UPDATE margin_tiers mt
    SET is_deleted = 'YES',
        logical_load_timestamp = lv_logical_load_timestamp
    WHERE  NOT EXISTS (SELECT 1
                       FROM margin_tiers_current_data a
                       WHERE a.margin_tier_code = mt.margin_tier_code) AND
           is_deleted = 'NO';

    --
    --Merge New Data
    --

    MERGE INTO margin_tiers existing_margin_tier
    USING (SELECT * FROM margin_tiers_current_data WHERE effective_start_timestamp = l_effective_start_timestamp) new_margin_tier
    ON (existing_margin_tier.margin_tier_code = new_margin_tier.margin_tier_code)
    WHEN MATCHED THEN
      UPDATE
      SET logical_load_timestamp = lv_logical_load_timestamp,
          updated_by = new_margin_tier.updated_by,
          update_timestamp = SYSTIMESTAMP,
          effective_start_timestamp = new_margin_tier.effective_start_timestamp,
          product_setting_code = new_margin_tier.product_setting_code,
          product_schema_code = new_margin_tier.product_schema_code,
          trading_risk_schema_code = new_margin_tier.trading_risk_schema_code,
          currency = new_margin_tier.currency,
          margin_size_boundary = new_margin_tier.margin_size_boundary,
          margin_rate = new_margin_tier.margin_rate,
          last_published_date = new_margin_tier.last_published_date,
          initial_published_date = new_margin_tier.initial_published_date,
          is_deleted = 'NO'
      WHERE existing_margin_tier.margin_tier_code = new_margin_tier.margin_tier_code AND
            (nrg_common.has_value_changed(existing_margin_tier.product_setting_code, new_margin_tier.product_setting_code) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.product_schema_code, new_margin_tier.product_schema_code) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.trading_risk_schema_code, new_margin_tier.trading_risk_schema_code) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.currency, new_margin_tier.currency) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.margin_size_boundary, new_margin_tier.margin_size_boundary) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.margin_rate, new_margin_tier.margin_rate) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.last_published_date, new_margin_tier.last_published_date) = 1 OR
             nrg_common.has_value_changed(existing_margin_tier.initial_published_date, new_margin_tier.initial_published_date) = 1 OR
             existing_margin_tier.is_deleted='YES')
    WHEN NOT MATCHED THEN
      INSERT (margin_tier_code,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              product_setting_code,
              product_schema_code,
              trading_risk_schema_code,
              currency,
              margin_size_boundary,
              margin_rate,
              last_published_date,
              initial_published_date,
              is_deleted)
      VALUES (new_margin_tier.margin_tier_code,
              lv_logical_load_timestamp,
              new_margin_tier.created_by,
              SYSTIMESTAMP,
              new_margin_tier.updated_by,
              SYSTIMESTAMP,
              new_margin_tier.effective_start_timestamp,
              new_margin_tier.product_setting_code,
              new_margin_tier.product_schema_code,
              new_margin_tier.trading_risk_schema_code,
              new_margin_tier.currency,
              new_margin_tier.margin_size_boundary,
              new_margin_tier.margin_rate,
              new_margin_tier.last_published_date,
              new_margin_tier.initial_published_date,
              'NO');

   trunc_margin_tier_current_data;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_margin_tier_merge;

  -- ===================================================================================
  --  END Modification for V2.3 - BER588
  -- ===================================================================================



  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for all the old versions of the countries record
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --      p_old_countries_record          This is the old version of the countries record
  --      p_effective_end_timestamp       This is the end time for the record
  --      p_action                        Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history (p_old_countries_record     countries%ROWTYPE,
                           p_effective_end_timestamp  countries_h.effective_end_timestamp%TYPE,
                           p_action                   countries_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO countries_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  country_code,
                                  country_name,
                                  iso2,
                                  iso3,
                                  iso_numeric,
                                  region_code,
                                  labour_force,
                                  unemployment_rate,
                                  exports_in_usd,
                                  imports_in_usd,
                                  population,
                                  currency,
                                  status,
                                  is_deleted,
                                  is_eea_member,
                                  is_eu_member,
                                  dividend_tax_rate,
                                  declaration_validity_period
                                  )
                              VALUES
                              (
                                  p_old_countries_record.created_by,
                                  p_old_countries_record.create_timestamp,
                                  p_old_countries_record.updated_by,
                                  p_old_countries_record.update_timestamp,
                                  p_old_countries_record.logical_load_timestamp,
                                  p_old_countries_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_countries_record.country_code,
                                  p_old_countries_record.country_name,
                                  p_old_countries_record.iso2,
                                  p_old_countries_record.iso3,
                                  p_old_countries_record.iso_numeric,
                                  p_old_countries_record.region_code,
                                  p_old_countries_record.labour_force,
                                  p_old_countries_record.unemployment_rate,
                                  p_old_countries_record.exports_in_usd,
                                  p_old_countries_record.imports_in_usd,
                                  p_old_countries_record.population,
                                  p_old_countries_record.currency,
                                  p_old_countries_record.status,
                                  p_old_countries_record.is_deleted,
                                  p_old_countries_record.is_eea_member,
                                  p_old_countries_record.is_eu_member,
                                  p_old_countries_record.dividend_tax_rate,
                                  p_old_countries_record.declaration_validity_period
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE countries_h
                SET updated_by                 = p_old_countries_record.updated_by,
                    action                     = p_action,
                    action_timestamp           = SYSTIMESTAMP,
                    update_timestamp           = p_old_countries_record.update_timestamp,
                    logical_load_timestamp     = p_old_countries_record.logical_load_timestamp,
                    country_name               = p_old_countries_record.country_name,
                    iso2                       = p_old_countries_record.iso2,
                    iso3                       = p_old_countries_record.iso3,
                    iso_numeric                = p_old_countries_record.iso_numeric,
                    region_code                = p_old_countries_record.region_code,
                    labour_force               = p_old_countries_record.labour_force,
                    unemployment_rate          = p_old_countries_record.unemployment_rate,
                    exports_in_usd             = p_old_countries_record.exports_in_usd,
                    imports_in_usd             = p_old_countries_record.imports_in_usd,
                    population                 = p_old_countries_record.population,
                    currency                   = p_old_countries_record.currency,
                    status                     = p_old_countries_record.status,
                    is_deleted                 = p_old_countries_record.is_deleted,
                    is_eea_member              = p_old_countries_record.is_eea_member,
                    is_eu_member               = p_old_countries_record.is_eu_member,
                    dividend_tax_rate          = p_old_countries_record.dividend_tax_rate,
                    declaration_validity_period= p_old_countries_record.declaration_validity_period
              WHERE country_code               = p_old_countries_record.country_code AND
                    effective_start_timestamp  = p_old_countries_record.effective_start_timestamp ;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the regions record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_regions_record            This is the old version of the regions record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_regions_record       regions%ROWTYPE,
                           p_effective_end_timestamp  regions_h.effective_end_timestamp%TYPE,
                           p_action                   regions_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO regions_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  region_code,
                                  region_name,
                                  iso_numeric,
                                  is_deleted
                                  )
                              VALUES
                              (
                                  p_old_regions_record.created_by,
                                  p_old_regions_record.create_timestamp,
                                  p_old_regions_record.updated_by,
                                  p_old_regions_record.update_timestamp,
                                  p_old_regions_record.logical_load_timestamp,
                                  p_old_regions_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  systimestamp,
                                  p_old_regions_record.region_code,
                                  p_old_regions_record.region_name,
                                  p_old_regions_record.iso_numeric,
                                  p_old_regions_record.is_deleted

                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE regions_h
                SET updated_by                 = p_old_regions_record.updated_by,
                    action                     = p_action,
                    action_timestamp           = SYSTIMESTAMP,
                    update_timestamp           = p_old_regions_record.update_timestamp,
                    logical_load_timestamp     = p_old_regions_record.logical_load_timestamp,
                    region_name                = p_old_regions_record.region_name,
                    iso_numeric                = p_old_regions_record.iso_numeric,
                    is_deleted                 = p_old_regions_record.is_deleted
              WHERE region_code                = p_old_regions_record.region_code AND
                    effective_start_timestamp  = p_old_regions_record.effective_start_timestamp ;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the product wrapper record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_wrapper_record            This is the old version of the product wrapper record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_wrapper_record       product_wrappers%ROWTYPE,
                           p_effective_end_timestamp  product_wrappers_h.effective_end_timestamp%TYPE,
                           p_action                   product_wrappers_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO product_wrappers_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  wrapper_code,
                                  product_wrapper_type,
                                  short_name,
                                  name_long,
                                  is_quantity_amount_available,
                                  is_quantity_units_available,
                                  is_qntty_units_administrable,
                                  is_qntty_amount_administrable,
                                  is_qntty_amt_per_pnt_avlbl,
                                  is_qntty_amt_per_pnt_admnstrbl,
                                  is_deleted
                                  )
                              VALUES
                              (
                                  p_old_wrapper_record.created_by,
                                  p_old_wrapper_record.create_timestamp,
                                  p_old_wrapper_record.updated_by,
                                  p_old_wrapper_record.update_timestamp,
                                  p_old_wrapper_record.logical_load_timestamp,
                                  p_old_wrapper_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_wrapper_record.wrapper_code,
                                  p_old_wrapper_record.product_wrapper_type,
                                  p_old_wrapper_record.short_name,
                                  p_old_wrapper_record.name_long,
                                  p_old_wrapper_record.is_quantity_amount_available,
                                  p_old_wrapper_record.is_quantity_units_available,
                                  p_old_wrapper_record.is_qntty_units_administrable,
                                  p_old_wrapper_record.is_qntty_amount_administrable,
                                  p_old_wrapper_record.is_qntty_amt_per_pnt_avlbl,
                                  p_old_wrapper_record.is_qntty_amt_per_pnt_admnstrbl,
                                  p_old_wrapper_record.is_deleted

                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE product_wrappers_h
                SET updated_by                      = p_old_wrapper_record.updated_by,
                    action                          = p_action,
                    action_timestamp                = SYSTIMESTAMP,
                    update_timestamp                = p_old_wrapper_record.update_timestamp,
                    logical_load_timestamp          = p_old_wrapper_record.logical_load_timestamp,
                    product_wrapper_type            = p_old_wrapper_record.product_wrapper_type,
                    short_name                      = p_old_wrapper_record.short_name,
                    name_long                       = p_old_wrapper_record.name_long,
                    is_quantity_amount_available    = p_old_wrapper_record.is_quantity_amount_available,
                    is_quantity_units_available     = p_old_wrapper_record.is_quantity_units_available,
                    is_qntty_units_administrable    = p_old_wrapper_record.is_qntty_units_administrable,
                    is_qntty_amount_administrable   = p_old_wrapper_record.is_qntty_amount_administrable,
                    is_qntty_amt_per_pnt_avlbl      = p_old_wrapper_record.is_qntty_amt_per_pnt_avlbl,
                    is_qntty_amt_per_pnt_admnstrbl  = p_old_wrapper_record.is_qntty_amt_per_pnt_admnstrbl,
                    is_deleted                      = p_old_wrapper_record.is_deleted
              WHERE wrapper_code                = p_old_wrapper_record.wrapper_code AND
                    effective_start_timestamp   = p_old_wrapper_record.effective_start_timestamp ;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the instrument record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_instrument_record         This is the old version of the instrument record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_instrument_record    instruments%ROWTYPE,
                           p_effective_end_timestamp  instruments_h.effective_end_timestamp%TYPE,
                           p_action                   instruments_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO instruments_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  instrument_code,
                                  country_code,
                                  feed_symbol,
                                  instrument_type,
                                  short_name,
                                  isin,
                                  mic,
                                  ric,
                                  currency,
                                  pair_currency,
                                  company_sector_name,
                                  commodity_type_name,
                                  country_class_name,
                                  is_deleted,
                                  mmuid,
                                  mmuid2,
                                  mmvalueid,
                                  prophet_banding_algorithm,
                                  prophet_banding_fixedspread,
                                  prophet_banding_spreadfactor,
                                  mm_inst_id_cfd,
                                  mm_inst_id_sb_ir,
                                  mm_inst_id_sb_uk,
                                  pricing_start_date,
                                  first_trading_date,
                                  last_rollover_date,
                                  automatic_rollover_date,
                                  last_trading_date,
                                  cash_settlement_date,
                                  expiry_date,
                                  is_skip_rollover,
                                  last_trading_date_desc,
                                  last_settlement_date_desc,
                                  last_rollover_date_desc,
                                  contract_code,
                                  rollover_target_code,
                                  cmc_cash_instrument_code,
                                  financial_instrument_type,
                                  derived_cash_instrument_pms,
                                  is_prr_qualifying_index,
                                  position_risk_requirement_pct,
                                  Security_Typ,
                                  contract_size_override,
                                  commodity_base_name,
                                  commodity_detail_name,
                                  instrument_type_code,
                                  risk_country_code,
                                  tax_country_code,
                                  bloomberg_code,
                                  sedol,
                                  reuters_mic,
                                  operating_mic,
                                  main_exchange,
                                  market_status,
                                  cur_mkt_cap_usd,
                                  avg_daily_value_traded_30d_usd,
                                  avg_daily_value_traded_3m_usd,
                                  eqy_free_float_pct,
                                  coupon_rate,
                                  cheapest_to_deliver_date,
                                  market_code,
                                  market_alias,
                                  market_data_mapping_code,
                                  cfd_cfi,
                                  sb_cfi,
                                  is_mifid_ii_reportable,
                                  security_description,
                                  pht_rsk_brkr_mrgn_amnt,
                                  pht_rsk_brkr_mrgn_prcnt,
                                  pht_rsk_brkr_mrgn_tier,
                                  pht_rsk_brkr_mrgn_tier_fxdb,
                                  pht_rsk_brkr_mrgn_tier_fxubs,
                                  underlying_instrument_code,
                                  underlying_instrument_alias,
                                  is_ccy_in_frctnl_prts,
                                  is_pair_ccy_in_frctnl_prts,
                                  point_multiplier,
                                  financing_currency,
                                  im_is_test_instrument,
                                  im_is_demo_only,
                                  is_fixed_spread_instrument,
                                  is_guaranteed_stop_instrmnt,
                                  is_metatrader_instrument,
                                  is_esma_duplicate,
                                  is_findable_instrument,
                                  is_tradable_instrument
								  )
                              VALUES
                              (
                                  p_old_instrument_record.created_by,
                                  p_old_instrument_record.create_timestamp,
                                  p_old_instrument_record.updated_by,
                                  p_old_instrument_record.update_timestamp,
                                  p_old_instrument_record.logical_load_timestamp,
                                  p_old_instrument_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_instrument_record.instrument_code,
                                  p_old_instrument_record.country_code,
                                  p_old_instrument_record.feed_symbol,
                                  p_old_instrument_record.instrument_type,
                                  p_old_instrument_record.short_name,
                                  p_old_instrument_record.isin,
                                  p_old_instrument_record.mic,
                                  p_old_instrument_record.ric,
                                  p_old_instrument_record.currency,
                                  p_old_instrument_record.pair_currency,
                                  p_old_instrument_record.company_sector_name,
                                  p_old_instrument_record.commodity_type_name,
                                  p_old_instrument_record.country_class_name,
                                  p_old_instrument_record.is_deleted,
                                  p_old_instrument_record.mmuid,
                                  p_old_instrument_record.mmuid2,
                                  p_old_instrument_record.mmvalueid,
                                  p_old_instrument_record.prophet_banding_algorithm,
                                  p_old_instrument_record.prophet_banding_fixedspread,
                                  p_old_instrument_record.prophet_banding_spreadfactor,
                                  p_old_instrument_record.mm_inst_id_cfd,
                                  p_old_instrument_record.mm_inst_id_sb_ir,
                                  p_old_instrument_record.mm_inst_id_sb_uk,
                                  p_old_instrument_record.pricing_start_date,
                                  p_old_instrument_record.first_trading_date,
                                  p_old_instrument_record.last_rollover_date,
                                  p_old_instrument_record.automatic_rollover_date,
                                  p_old_instrument_record.last_trading_date,
                                  p_old_instrument_record.cash_settlement_date,
                                  p_old_instrument_record.expiry_date,
                                  p_old_instrument_record.is_skip_rollover,
                                  p_old_instrument_record.last_trading_date_desc,
                                  p_old_instrument_record.last_settlement_date_desc,
                                  p_old_instrument_record.last_rollover_date_desc,
                                  p_old_instrument_record.contract_code,
                                  p_old_instrument_record.rollover_target_code,
                                  p_old_instrument_record.cmc_cash_instrument_code,
                                  p_old_instrument_record.financial_instrument_type,
                                  p_old_instrument_record.derived_cash_instrument_pms,
                                  p_old_instrument_record.is_prr_qualifying_index,
                                  p_old_instrument_record.position_risk_requirement_pct,
                                  p_old_instrument_record.Security_Typ,
                                  p_old_instrument_record.contract_size_override,
                                  p_old_instrument_record.commodity_base_name,
                                  p_old_instrument_record.commodity_detail_name,
                                  p_old_instrument_record.instrument_type_code,
                                  p_old_instrument_record.risk_country_code,
                                  p_old_instrument_record.tax_country_code,
                                  p_old_instrument_record.bloomberg_code,
                                  p_old_instrument_record.sedol,
                                  p_old_instrument_record.reuters_mic,
                                  p_old_instrument_record.operating_mic,
                                  p_old_instrument_record.main_exchange,
                                  p_old_instrument_record.market_status,
                                  p_old_instrument_record.cur_mkt_cap_usd,
                                  p_old_instrument_record.avg_daily_value_traded_30d_usd,
                                  p_old_instrument_record.avg_daily_value_traded_3m_usd,
                                  p_old_instrument_record.eqy_free_float_pct,
                                  p_old_instrument_record.coupon_rate,
                                  p_old_instrument_record.cheapest_to_deliver_date,
                                  p_old_instrument_record.market_code,
                                  p_old_instrument_record.market_alias,
                                  p_old_instrument_record.market_data_mapping_code,
                                  p_old_instrument_record.cfd_cfi,
                                  p_old_instrument_record.sb_cfi,
                                  p_old_instrument_record.is_mifid_ii_reportable,
                                  p_old_instrument_record.security_description,
                                  p_old_instrument_record.pht_rsk_brkr_mrgn_amnt,
                                  p_old_instrument_record.pht_rsk_brkr_mrgn_prcnt,
                                  p_old_instrument_record.pht_rsk_brkr_mrgn_tier,
                                  p_old_instrument_record.pht_rsk_brkr_mrgn_tier_fxdb,
                                  p_old_instrument_record.pht_rsk_brkr_mrgn_tier_fxubs,
                                  p_old_instrument_record.underlying_instrument_code,
                                  p_old_instrument_record.underlying_instrument_alias,
                                  p_old_instrument_record.is_ccy_in_frctnl_prts,
                                  p_old_instrument_record.is_pair_ccy_in_frctnl_prts,
                                  p_old_instrument_record.point_multiplier,
                                  p_old_instrument_record.financing_currency,
                                  p_old_instrument_record.im_is_test_instrument,
                                  p_old_instrument_record.im_is_demo_only,
                                  p_old_instrument_record.is_fixed_spread_instrument,
                                  p_old_instrument_record.is_guaranteed_stop_instrmnt,
                                  p_old_instrument_record.is_metatrader_instrument,
                                  p_old_instrument_record.is_esma_duplicate,
                                  p_old_instrument_record.is_findable_instrument,
                                  p_old_instrument_record.is_tradable_instrument
								  );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE instruments_h
                SET updated_by                    = p_old_instrument_record.updated_by,
                    action                        = p_action,
                    action_timestamp              = SYSTIMESTAMP,
                    update_timestamp              = p_old_instrument_record.update_timestamp,
                    logical_load_timestamp        = p_old_instrument_record.logical_load_timestamp,
                    country_code                  = p_old_instrument_record.country_code,
                    feed_symbol                   = p_old_instrument_record.feed_symbol,
                    instrument_type               = p_old_instrument_record.instrument_type,
                    short_name                    = p_old_instrument_record.short_name,
                    isin                          = p_old_instrument_record.isin,
                    mic                           = p_old_instrument_record.mic,
                    ric                           = p_old_instrument_record.ric,
                    currency                      = p_old_instrument_record.currency,
                    pair_currency                 = p_old_instrument_record.pair_currency,
                    company_sector_name           = p_old_instrument_record.company_sector_name,
                    commodity_type_name           = p_old_instrument_record.commodity_type_name,
                    country_class_name            = p_old_instrument_record.country_class_name,
                    is_deleted                    = p_old_instrument_record.is_deleted,
                    mmuid                         = p_old_instrument_record.mmuid,
                    mmuid2                        = p_old_instrument_record.mmuid2,
                    mmvalueid                     = p_old_instrument_record.mmvalueid,
                    prophet_banding_algorithm     = p_old_instrument_record.prophet_banding_algorithm,
                    prophet_banding_fixedspread   = p_old_instrument_record.prophet_banding_fixedspread,
                    prophet_banding_spreadfactor  = p_old_instrument_record.prophet_banding_spreadfactor,
                    mm_inst_id_cfd                = p_old_instrument_record.mm_inst_id_cfd,
                    mm_inst_id_sb_ir              = p_old_instrument_record.mm_inst_id_sb_ir,
                    mm_inst_id_sb_uk              = p_old_instrument_record.mm_inst_id_sb_uk,
                    pricing_start_date            = p_old_instrument_record.pricing_start_date,
                    first_trading_date            = p_old_instrument_record.first_trading_date,
                    last_rollover_date            = p_old_instrument_record.last_rollover_date,
                    automatic_rollover_date       = p_old_instrument_record.automatic_rollover_date,
                    last_trading_date             = p_old_instrument_record.last_trading_date,
                    cash_settlement_date          = p_old_instrument_record.cash_settlement_date,
                    expiry_date                   = p_old_instrument_record.expiry_date,
                    is_skip_rollover              = p_old_instrument_record.is_skip_rollover,
                    last_trading_date_desc        = p_old_instrument_record.last_trading_date_desc,
                    last_settlement_date_desc     = p_old_instrument_record.last_settlement_date_desc,
                    last_rollover_date_desc       = p_old_instrument_record.last_rollover_date_desc,
                    contract_code                 = p_old_instrument_record.contract_code,
                    rollover_target_code          = p_old_instrument_record.rollover_target_code,
                    cmc_cash_instrument_code      = p_old_instrument_record.cmc_cash_instrument_code,
                    financial_instrument_type     = p_old_instrument_record.financial_instrument_type,
                    derived_cash_instrument_pms   = p_old_instrument_record.derived_cash_instrument_pms,
                    is_prr_qualifying_index       = p_old_instrument_record.is_prr_qualifying_index,
                    position_risk_requirement_pct = p_old_instrument_record.position_risk_requirement_pct,
                    Security_Typ                  = p_old_instrument_record.Security_Typ,
                    contract_size_override        = p_old_instrument_record.contract_size_override,
                    commodity_base_name           = p_old_instrument_record.commodity_base_name,
                    commodity_detail_name         = p_old_instrument_record.commodity_detail_name,
                    instrument_type_code          = p_old_instrument_record.instrument_type_code,
                    risk_country_code             = p_old_instrument_record.risk_country_code,
                    tax_country_code              = p_old_instrument_record.tax_country_code,
                    bloomberg_code                = p_old_instrument_record.bloomberg_code,
                    sedol                         = p_old_instrument_record.sedol,
                    reuters_mic                   = p_old_instrument_record.reuters_mic,
                    operating_mic                 = p_old_instrument_record.operating_mic,
                    main_exchange                 = p_old_instrument_record.main_exchange,
                    market_status                 = p_old_instrument_record.market_status,
                    cur_mkt_cap_usd               = p_old_instrument_record.cur_mkt_cap_usd,
                    avg_daily_value_traded_30d_usd= p_old_instrument_record.avg_daily_value_traded_30d_usd,
                    avg_daily_value_traded_3m_usd = p_old_instrument_record.avg_daily_value_traded_3m_usd,
                    eqy_free_float_pct            = p_old_instrument_record.eqy_free_float_pct,
                    coupon_rate                   = p_old_instrument_record.coupon_rate,
                    cheapest_to_deliver_date      = p_old_instrument_record.cheapest_to_deliver_date,
                    market_code                   = p_old_instrument_record.market_code,
                    market_alias                  = p_old_instrument_record.market_alias,
                    market_data_mapping_code      = p_old_instrument_record.market_data_mapping_code,
                    cfd_cfi                       = p_old_instrument_record.cfd_cfi,
                    sb_cfi                        = p_old_instrument_record.sb_cfi,
                    is_mifid_ii_reportable        = p_old_instrument_record.is_mifid_ii_reportable,
                    security_description			    = p_old_instrument_record.security_description,
                    pht_rsk_brkr_mrgn_amnt			  = p_old_instrument_record.pht_rsk_brkr_mrgn_amnt,
                    pht_rsk_brkr_mrgn_prcnt			  = p_old_instrument_record.pht_rsk_brkr_mrgn_prcnt,
                    pht_rsk_brkr_mrgn_tier			  = p_old_instrument_record.pht_rsk_brkr_mrgn_tier,
                    pht_rsk_brkr_mrgn_tier_fxdb		= p_old_instrument_record.pht_rsk_brkr_mrgn_tier_fxdb,
                    pht_rsk_brkr_mrgn_tier_fxubs	= p_old_instrument_record.pht_rsk_brkr_mrgn_tier_fxubs,
                    underlying_instrument_code    = p_old_instrument_record.underlying_instrument_code,
                    underlying_instrument_alias   = p_old_instrument_record.underlying_instrument_alias,
                    is_ccy_in_frctnl_prts         = p_old_instrument_record.is_ccy_in_frctnl_prts,
                    is_pair_ccy_in_frctnl_prts    = p_old_instrument_record.is_pair_ccy_in_frctnl_prts,
                    point_multiplier              = p_old_instrument_record.point_multiplier,
                    financing_currency            = p_old_instrument_record.financing_currency,
                    im_is_test_instrument         = p_old_instrument_record.im_is_test_instrument,
                    im_is_demo_only               = p_old_instrument_record.im_is_demo_only,
                    is_fixed_spread_instrument    = p_old_instrument_record.is_fixed_spread_instrument,
                    is_guaranteed_stop_instrmnt   = p_old_instrument_record.is_guaranteed_stop_instrmnt,
                    is_metatrader_instrument      = p_old_instrument_record.is_metatrader_instrument,
                    is_esma_duplicate             = p_old_instrument_record.is_esma_duplicate,
                    is_findable_instrument        = p_old_instrument_record.is_findable_instrument,
                    is_tradable_instrument        = p_old_instrument_record.is_tradable_instrument
              WHERE instrument_code           = p_old_instrument_record.instrument_code AND
                    effective_start_timestamp = p_old_instrument_record.effective_start_timestamp ;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the instrument_languages record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_instrument_record         This is the old version of the instrument record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    --PROCEDURE put_history (p_old_instrument_markets_rec     instrument_markets%ROWTYPE,
    --                       p_effective_end_timestamp        instrument_markets_h.effective_end_timestamp%TYPE,
    --                       p_action                         instrument_markets_h.action%TYPE)
    --IS

    --BEGIN

    --  INSERT INTO instrument_markets_h
    --  (instrument_code,
    --   market_code,
    --   logical_load_timestamp,
    --   created_by,
    --   create_timestamp,
    --   updated_by,
    --   update_timestamp,
    --   effective_start_timestamp,
    --   effective_end_timestamp,
    --   action,
    --   action_timestamp,
    --   is_deleted,
    --   market_alias,
    --   market_type)
    --   VALUES (p_old_instrument_markets_rec.instrument_code,
    --           p_old_instrument_markets_rec.market_code,
    --           p_old_instrument_markets_rec.logical_load_timestamp,
    --           p_old_instrument_markets_rec.created_by,
    --           p_old_instrument_markets_rec.create_timestamp,
    --           p_old_instrument_markets_rec.updated_by,
    --           p_old_instrument_markets_rec.update_timestamp,
    --           p_old_instrument_markets_rec.effective_start_timestamp,
    --           p_effective_end_timestamp,
    --           p_action,
    --           SYSTIMESTAMP,
    --           p_old_instrument_markets_rec.is_deleted,
    --           p_old_instrument_markets_rec.market_alias,
    --           p_old_instrument_markets_rec.market_type);
    --EXCEPTION
    --  WHEN DUP_VAL_ON_INDEX THEN
    --    UPDATE instrument_markets_h
    --    SET effective_end_timestamp = p_effective_end_timestamp,
    --        action = p_action,
    --        action_timestamp = SYSTIMESTAMP,
    --        is_deleted = p_old_instrument_markets_rec.is_deleted,
    --        market_alias = p_old_instrument_markets_rec.market_alias,
    --        market_type = p_old_instrument_markets_rec.market_type
    --    WHERE instrument_code = p_old_instrument_markets_rec.instrument_code AND
    --          market_code = p_old_instrument_markets_rec.market_code AND
    --          effective_start_timestamp = p_old_instrument_markets_rec.effective_start_timestamp;
    --END;
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the instrument_languages record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_instrument_record         This is the old version of the instrument record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_instruments_lang_record    instrument_languages%ROWTYPE,
                           p_effective_end_timestamp        instrument_languages_h.effective_end_timestamp%TYPE,
                           p_action                         instrument_languages_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO instrument_languages_h
                              (instrument_code,
                               logical_load_timestamp,
                               created_by,
                               create_timestamp,
                               updated_by,
                               update_timestamp,
                               effective_start_timestamp,
                               effective_end_timestamp,
                               action,
                               action_timestamp,
                               language_code,
                               short_name,
                               long_name)
                              VALUES
                                ( p_old_instruments_lang_record.instrument_code,
                                  p_old_instruments_lang_record.logical_load_timestamp,
                                  p_old_instruments_lang_record.created_by,
                                  p_old_instruments_lang_record.create_timestamp,
                                  p_old_instruments_lang_record.updated_by,
                                  p_old_instruments_lang_record.update_timestamp,
                                  p_old_instruments_lang_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_instruments_lang_record.language_code,
                                  p_old_instruments_lang_record.short_name,
                                  p_old_instruments_lang_record.long_name);
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE instrument_languages_h
                SET updated_by                    = p_old_instruments_lang_record.updated_by,
                    action                        = p_action,
                    action_timestamp              = SYSTIMESTAMP,
                    update_timestamp              = p_old_instruments_lang_record.update_timestamp,
                    logical_load_timestamp        = p_old_instruments_lang_record.logical_load_timestamp,
                    short_name                    = p_old_instruments_lang_record.short_name,
                    long_name                     = p_old_instruments_lang_record.long_name
              WHERE instrument_code           = p_old_instruments_lang_record.instrument_code AND
                    language_code             = p_old_instruments_lang_record.language_code AND
                    effective_start_timestamp = p_old_instruments_lang_record.effective_start_timestamp ;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the instrument_languages record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_instrument_record         This is the old version of the instrument record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_instrument_typ_lang_rec    instrument_type_languages%ROWTYPE,
                           p_effective_end_timestamp        instrument_type_languages_h.effective_end_timestamp%TYPE,
                           p_action                         instrument_type_languages_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO instrument_type_languages_h
                              (instrument_type_code,
                               logical_load_timestamp,
                               created_by,
                               create_timestamp,
                               updated_by,
                               update_timestamp,
                               effective_start_timestamp,
                               effective_end_timestamp,
                               action,
                               action_timestamp,
                               language_code,
                               instrument_type_default_name,
                               instrument_type_name)
                              VALUES
                                ( p_old_instrument_typ_lang_rec.instrument_type_code,
                                  p_old_instrument_typ_lang_rec.logical_load_timestamp,
                                  p_old_instrument_typ_lang_rec.created_by,
                                  p_old_instrument_typ_lang_rec.create_timestamp,
                                  p_old_instrument_typ_lang_rec.updated_by,
                                  p_old_instrument_typ_lang_rec.update_timestamp,
                                  p_old_instrument_typ_lang_rec.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_instrument_typ_lang_rec.language_code,
                                  p_old_instrument_typ_lang_rec.instrument_type_default_name,
                                  p_old_instrument_typ_lang_rec.instrument_type_name);
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE instrument_type_languages_h
                SET updated_by                    = p_old_instrument_typ_lang_rec.updated_by,
                    action                        = p_action,
                    action_timestamp              = SYSTIMESTAMP,
                    update_timestamp              = p_old_instrument_typ_lang_rec.update_timestamp,
                    logical_load_timestamp        = p_old_instrument_typ_lang_rec.logical_load_timestamp,
                    instrument_type_default_name  = p_old_instrument_typ_lang_rec.instrument_type_default_name,
                    instrument_type_name          = p_old_instrument_typ_lang_rec.instrument_type_name
              WHERE instrument_type_code         = p_old_instrument_typ_lang_rec.instrument_type_code AND
                    language_code                = p_old_instrument_typ_lang_rec.language_code AND
                    effective_start_timestamp    = p_old_instrument_typ_lang_rec.effective_start_timestamp ;
        END put_history;
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the products record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_product_record            This is the old version of the products record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_product_record       products%ROWTYPE,
                           p_effective_end_timestamp  products_h.effective_end_timestamp%TYPE,
                           p_action                   products_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO products_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  platform,
                                  instrument_code,
                                  wrapper_code,
                                  product_setting_code,
                                  instrument_term,
                                  short_name,
                                  long_name,
                                  underlying_name,
                                  is_ccy_in_fractional_parts,
                                  contract_size,
                                  currency,
                                  fractional_part_ratio,
                                  initial_published_date,
                                  is_findable,
                                  rm_code,
                                  rqustd_fnncng_ratio_dflt,
                                  is_deleted,
                                  is_pr_ccy_in_fractional_parts,
                                  point_multiplier)
                              VALUES
                              (
                                  p_old_product_record.created_by,
                                  p_old_product_record.create_timestamp,
                                  p_old_product_record.updated_by,
                                  p_old_product_record.update_timestamp,
                                  p_old_product_record.logical_load_timestamp,
                                  p_old_product_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_product_record.platform,
                                  p_old_product_record.instrument_code,
                                  p_old_product_record.wrapper_code,
                                  p_old_product_record.product_setting_code,
                                  p_old_product_record.instrument_term,
                                  p_old_product_record.short_name,
                                  p_old_product_record.long_name,
                                  p_old_product_record.underlying_name,
                                  p_old_product_record.is_ccy_in_fractional_parts,
                                  p_old_product_record.contract_size,
                                  p_old_product_record.currency,
                                  p_old_product_record.fractional_part_ratio,
                                  p_old_product_record.initial_published_date,
                                  p_old_product_record.is_findable,
                                  p_old_product_record.rm_code,
                                  p_old_product_record.rqustd_fnncng_ratio_dflt,
                                  p_old_product_record.is_deleted,
                                  p_old_product_record.is_pr_ccy_in_fractional_parts,
                                  p_old_product_record.point_multiplier
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE products_h
                SET updated_by                = p_old_product_record.updated_by,
                    action                    = p_action,
                    action_timestamp          = SYSTIMESTAMP,
                    update_timestamp          = p_old_product_record.update_timestamp,
                    logical_load_timestamp    = p_old_product_record.logical_load_timestamp,
                    contract_size             = p_old_product_record.contract_size,
                    currency                  = p_old_product_record.currency,
                    fractional_part_ratio     = p_old_product_record.fractional_part_ratio,
                    initial_published_date    = p_old_product_record.initial_published_date,
                    is_ccy_in_fractional_parts = p_old_product_record.is_ccy_in_fractional_parts,
                    instrument_term           = p_old_product_record.instrument_term,
                    product_setting_code      = p_old_product_record.product_setting_code,
                    short_name                = p_old_product_record.short_name,
                    long_name                 = p_old_product_record.long_name,
                    underlying_name           = p_old_product_record.underlying_name,
                    is_findable               = p_old_product_record.is_findable,
                    rm_code                   = p_old_product_record.rm_code,
                    is_deleted                = p_old_product_record.is_deleted,
                    is_pr_ccy_in_fractional_parts = p_old_product_record.is_pr_ccy_in_fractional_parts,
                    point_multiplier          = p_old_product_record.point_multiplier
              WHERE platform                  = p_old_product_record.platform AND
                    wrapper_code              = p_old_product_record.wrapper_code AND
                    instrument_code           = p_old_product_record.instrument_code and
                    effective_start_timestamp = p_old_product_record.effective_start_timestamp ;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the products record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_products_gsko             This is the old version of the products_gsko record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_products_gsko        IN products_gsko%ROWTYPE,
                           p_effective_end_timestamp  IN products_gsko_h.effective_end_timestamp%TYPE,
                           p_action                   IN products_gsko_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO products_gsko_h
                              ( product_setting_code,
                                product_setting_property,
                                trading_risk_schema_code,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp,
                                effective_end_timestamp,
                                action,
                                action_timestamp,
                                trading_risk_schema_value,
                                is_deleted,
                                instrument_schema_code,
                                instrument_schema_value)
                              VALUES
                              (
                                  p_old_products_gsko.product_setting_code,
                                  p_old_products_gsko.product_setting_property,
                                  p_old_products_gsko.trading_risk_schema_code,
                                  p_old_products_gsko.logical_load_timestamp,
                                  p_old_products_gsko.created_by,
                                  p_old_products_gsko.create_timestamp,
                                  p_old_products_gsko.updated_by,
                                  p_old_products_gsko.update_timestamp,
                                  p_old_products_gsko.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_products_gsko.trading_risk_schema_value,
                                  p_old_products_gsko.is_deleted,
                                  p_old_products_gsko.instrument_schema_code,
                                  p_old_products_gsko.instrument_schema_value
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE products_gsko_h
                SET updated_by                = p_old_products_gsko.updated_by,
                    action                    = p_action,
                    action_timestamp          = SYSTIMESTAMP,
                    update_timestamp          = p_old_products_gsko.update_timestamp,
                    logical_load_timestamp    = p_old_products_gsko.logical_load_timestamp,
                    trading_risk_schema_value = p_old_products_gsko.trading_risk_schema_value,
                    is_deleted                = p_old_products_gsko.is_deleted,
                    instrument_schema_code    = p_old_products_gsko.instrument_schema_code,
                    instrument_schema_value   = p_old_products_gsko.instrument_schema_value
              WHERE --platform                  = p_old_products_gsko.platform AND
                    product_setting_code      = p_old_products_gsko.product_setting_code AND
                    product_setting_property  = p_old_products_gsko.product_setting_property AND
                    instrument_schema_code  = p_old_products_gsko.instrument_schema_code AND
                    effective_start_timestamp = p_old_products_gsko.effective_start_timestamp ;

  END put_history;


    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the mm products record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_mm_product_record         This is the old version of the mm products record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_mm_product_record    mm_products%ROWTYPE,
                           p_effective_end_timestamp  mm_products_h.effective_end_timestamp%TYPE,
                           p_action                   mm_products_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO mm_products_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  mm_instrument_id,
                                  platform,
                                  instrument_code,
                                  wrapper_code,
                                  short_name,
                                  long_name,
                                  expiry_date,
                                  cfd_multiplier,
                                  point,
                                  currency_code,
                                  term,
                                  term_description,
                                  rm_code,
                                  currency_description,
                                  prophet_symbol,
                                  is_deleted
                                  )
                              VALUES
                              (
                                  p_old_mm_product_record.created_by,
                                  p_old_mm_product_record.create_timestamp,
                                  p_old_mm_product_record.updated_by,
                                  p_old_mm_product_record.update_timestamp,
                                  p_old_mm_product_record.logical_load_timestamp,
                                  p_old_mm_product_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_mm_product_record.mm_instrument_id,
                                  p_old_mm_product_record.platform,
                                  p_old_mm_product_record.instrument_code,
                                  p_old_mm_product_record.wrapper_code,
                                  p_old_mm_product_record.short_name,
                                  p_old_mm_product_record.long_name,
                                  p_old_mm_product_record.expiry_date,
                                  p_old_mm_product_record.cfd_multiplier,
                                  p_old_mm_product_record.point,
                                  p_old_mm_product_record.currency_code,
                                  p_old_mm_product_record.term,
                                  p_old_mm_product_record.term_description,
                                  p_old_mm_product_record.rm_code,
                                  p_old_mm_product_record.currency_description,
                                  p_old_mm_product_record.prophet_symbol,
                                  p_old_mm_product_record.is_deleted
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE mm_products_h
                SET updated_by                = p_old_mm_product_record.updated_by,
                    action                    = p_action,
                    action_timestamp          = SYSTIMESTAMP,
                    update_timestamp          = p_old_mm_product_record.update_timestamp,
                    logical_load_timestamp    = p_old_mm_product_record.logical_load_timestamp,
                    short_name                = p_old_mm_product_record.short_name,
                    long_name                 = p_old_mm_product_record.long_name,
                    expiry_date               = p_old_mm_product_record.expiry_date,
                    cfd_multiplier            = p_old_mm_product_record.cfd_multiplier,
                    point                     = p_old_mm_product_record.point,
                    currency_code             = p_old_mm_product_record.currency_code,
                    term                      = p_old_mm_product_record.term,
                    term_description          = p_old_mm_product_record.term_description,
                    rm_code                   = p_old_mm_product_record.rm_code,
                    currency_description      = p_old_mm_product_record.currency_description,
                    prophet_symbol            = p_old_mm_product_record.prophet_symbol,
                    is_deleted                = p_old_mm_product_record.is_deleted
              WHERE mm_instrument_id          = p_old_mm_product_record.mm_instrument_id AND
                    platform                  = p_old_mm_product_record.platform AND
                    wrapper_code              = p_old_mm_product_record.wrapper_code AND
                    instrument_code           = p_old_mm_product_record.instrument_code and
                    effective_start_timestamp = p_old_mm_product_record.effective_start_timestamp ;
        END put_history;



    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the mm products record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_mm_product_record         This is the old version of the mm products record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_instfeedsetting_record   instrument_feed_settings%ROWTYPE,
                           p_effective_end_timestamp      instrument_feed_settings_h.effective_end_timestamp%TYPE,
                           p_action                       instrument_feed_settings_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO instrument_feed_settings_h
                              (
                                  feed_setting_code,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  instrument_code,
                                  feed_type,
                                  ng_quote_symbol,
                                  band1_min_absolute_bid_margin,
                                  band1_min_absolute_ask_margin,
                                  band1_proportional_margin,
                                  band2_min_absolute_bid_margin,
                                  band2_min_absolute_ask_margin,
                                  band2_proportional_margin,
                                  band3_min_absolute_bid_margin,
                                  band3_min_absolute_ask_margin,
                                  band3_proportional_margin,
                                  band4_min_absolute_bid_margin,
                                  band4_min_absolute_ask_margin,
                                  band4_proportional_margin,
                                  band5_min_absolute_bid_margin,
                                  band5_min_absolute_ask_margin,
                                  band5_proportional_margin,
                                  feed_producer,
                                  feed_symbol,
                                  access_level,
                                  price_number_of_decimals,
                                  initial_published_date,
                                  last_published_date,
                                  is_deleted,
                                  band6_min_absolute_bid_margin,
                                  band6_min_absolute_ask_margin,
                                  band6_proportional_margin,
                                  band7_min_absolute_bid_margin,
                                  band7_min_absolute_ask_margin,
                                  band7_proportional_margin,
                                  band8_min_absolute_bid_margin,
                                  band8_min_absolute_ask_margin,
                                  band8_proportional_margin,
                                  band9_min_absolute_bid_margin,
                                  band9_min_absolute_ask_margin,
                                  band9_proportional_margin,
                                  band10_min_absolute_bid_margin,
                                  band10_min_absolute_ask_margin,
                                  band10_proportional_margin
                                  )
                              VALUES
                              (
                                  p_old_instfeedsetting_record.feed_setting_code,
                                  p_old_instfeedsetting_record.logical_load_timestamp,
                                  p_old_instfeedsetting_record.created_by,
                                  p_old_instfeedsetting_record.create_timestamp,
                                  p_old_instfeedsetting_record.updated_by,
                                  p_old_instfeedsetting_record.update_timestamp,
                                  p_old_instfeedsetting_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_instfeedsetting_record.instrument_code,
                                  p_old_instfeedsetting_record.feed_type,
                                  p_old_instfeedsetting_record.ng_quote_symbol,
                                  p_old_instfeedsetting_record.band1_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band1_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band1_proportional_margin,
                                  p_old_instfeedsetting_record.band2_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band2_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band2_proportional_margin,
                                  p_old_instfeedsetting_record.band3_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band3_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band3_proportional_margin,
                                  p_old_instfeedsetting_record.band4_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band4_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band4_proportional_margin,
                                  p_old_instfeedsetting_record.band5_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band5_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band5_proportional_margin,
                                  p_old_instfeedsetting_record.feed_producer,
                                  p_old_instfeedsetting_record.feed_symbol,
                                  p_old_instfeedsetting_record.access_level,
                                  p_old_instfeedsetting_record.price_number_of_decimals,
                                  p_old_instfeedsetting_record.initial_published_date,
                                  p_old_instfeedsetting_record.last_published_date,
                                  p_old_instfeedsetting_record.is_deleted,
                                  p_old_instfeedsetting_record.band6_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band6_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band6_proportional_margin,
                                  p_old_instfeedsetting_record.band7_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band7_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band7_proportional_margin,
                                  p_old_instfeedsetting_record.band8_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band8_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band8_proportional_margin,
                                  p_old_instfeedsetting_record.band9_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band9_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band9_proportional_margin,
                                  p_old_instfeedsetting_record.band10_min_absolute_bid_margin,
                                  p_old_instfeedsetting_record.band10_min_absolute_ask_margin,
                                  p_old_instfeedsetting_record.band10_proportional_margin
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE instrument_feed_settings_h
               SET logical_load_timestamp = p_old_instfeedsetting_record.logical_load_timestamp,
                   created_by = p_old_instfeedsetting_record.created_by,
                   create_timestamp = p_old_instfeedsetting_record.create_timestamp,
                   updated_by = p_old_instfeedsetting_record.updated_by,
                   update_timestamp = p_old_instfeedsetting_record.update_timestamp,
                   effective_start_timestamp = p_old_instfeedsetting_record.effective_start_timestamp,
                   effective_end_timestamp = p_effective_end_timestamp,
                   action = p_action,
                   action_timestamp = SYSTIMESTAMP,
                   instrument_code = p_old_instfeedsetting_record.instrument_code,
                   feed_type = p_old_instfeedsetting_record.feed_type,
                   ng_quote_symbol = p_old_instfeedsetting_record.ng_quote_symbol,
                   band1_min_absolute_bid_margin = p_old_instfeedsetting_record.band1_min_absolute_bid_margin,
                   band1_min_absolute_ask_margin = p_old_instfeedsetting_record.band1_min_absolute_ask_margin,
                   band1_proportional_margin = p_old_instfeedsetting_record.band1_proportional_margin,
                   band2_min_absolute_bid_margin = p_old_instfeedsetting_record.band2_min_absolute_bid_margin,
                   band2_min_absolute_ask_margin = p_old_instfeedsetting_record.band2_min_absolute_ask_margin,
                   band2_proportional_margin = p_old_instfeedsetting_record.band2_proportional_margin,
                   band3_min_absolute_bid_margin = p_old_instfeedsetting_record.band3_min_absolute_bid_margin,
                   band3_min_absolute_ask_margin = p_old_instfeedsetting_record.band3_min_absolute_ask_margin,
                   band3_proportional_margin = p_old_instfeedsetting_record.band3_proportional_margin,
                   band4_min_absolute_bid_margin = p_old_instfeedsetting_record.band4_min_absolute_bid_margin,
                   band4_min_absolute_ask_margin = p_old_instfeedsetting_record.band4_min_absolute_ask_margin,
                   band4_proportional_margin = p_old_instfeedsetting_record.band4_proportional_margin,
                   band5_min_absolute_bid_margin = p_old_instfeedsetting_record.band5_min_absolute_bid_margin,
                   band5_min_absolute_ask_margin = p_old_instfeedsetting_record.band5_min_absolute_ask_margin,
                   band5_proportional_margin = p_old_instfeedsetting_record.band5_proportional_margin,
                   feed_producer = p_old_instfeedsetting_record.feed_producer,
                   feed_symbol = p_old_instfeedsetting_record.feed_symbol,
                   access_level = p_old_instfeedsetting_record.access_level,
                   price_number_of_decimals = p_old_instfeedsetting_record.price_number_of_decimals,
                   initial_published_date = p_old_instfeedsetting_record.initial_published_date,
                   last_published_date = p_old_instfeedsetting_record.last_published_date,
                   is_deleted = p_old_instfeedsetting_record.is_deleted,
                   band6_min_absolute_bid_margin = p_old_instfeedsetting_record.band6_min_absolute_bid_margin,
                   band6_min_absolute_ask_margin = p_old_instfeedsetting_record.band6_min_absolute_ask_margin,
                   band6_proportional_margin = p_old_instfeedsetting_record.band6_proportional_margin,
                   band7_min_absolute_bid_margin = p_old_instfeedsetting_record.band7_min_absolute_bid_margin,
                   band7_min_absolute_ask_margin = p_old_instfeedsetting_record.band7_min_absolute_ask_margin,
                   band7_proportional_margin = p_old_instfeedsetting_record.band7_proportional_margin,
                   band8_min_absolute_bid_margin = p_old_instfeedsetting_record.band8_min_absolute_bid_margin,
                   band8_min_absolute_ask_margin = p_old_instfeedsetting_record.band8_min_absolute_ask_margin,
                   band8_proportional_margin = p_old_instfeedsetting_record.band8_proportional_margin,
                   band9_min_absolute_bid_margin = p_old_instfeedsetting_record.band9_min_absolute_bid_margin,
                   band9_min_absolute_ask_margin = p_old_instfeedsetting_record.band9_min_absolute_ask_margin,
                   band9_proportional_margin = p_old_instfeedsetting_record.band9_proportional_margin,
                   band10_min_absolute_bid_margin = p_old_instfeedsetting_record.band10_min_absolute_bid_margin,
                   band10_min_absolute_ask_margin = p_old_instfeedsetting_record.band10_min_absolute_ask_margin,
                   band10_proportional_margin = p_old_instfeedsetting_record.band10_proportional_margin
             WHERE feed_setting_code = p_old_instfeedsetting_record.feed_setting_code AND
                   EFFECTIVE_START_TIMESTAMP = p_old_instfeedsetting_record.effective_start_timestamp;
        END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the REG_INSTR_IDENTIFICATION record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_reg_instr_ident_record        This is the old version of the REG_INSTR_IDENTIFICATION record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_reg_instr_ident_record   reg_instr_identification%ROWTYPE,
                           p_effective_end_timestamp  reg_instr_identification_h.effective_end_timestamp%TYPE,
                           p_action                   reg_instr_identification_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO REG_INSTR_IDENTIFICATION_H
                              (
                                regulatory_identification_code
                               ,regulatory_identification_name
                               ,logical_load_timestamp
                               ,created_by
                               ,create_timestamp
                               ,updated_by
                               ,update_timestamp
                               ,effective_start_timestamp
                               ,effective_end_timestamp
                               ,action
                               ,action_timestamp
                               ,is_deleted
                               ,regulation
                               ,trading_account_type
                               ,instrument_code
                               ,instrument_type
                               ,instrument_country
                               ,instrument_mic
                               ,is_reportable
                               ,reportable_identifier_type
                               ,broker_code
                               ,reportable_instrument_type
                               ,derivative_type
                               )
                              VALUES
                              (
                                 p_reg_instr_ident_record.regulatory_identification_code
                                ,p_reg_instr_ident_record.regulatory_identification_name
                                ,p_reg_instr_ident_record.logical_load_timestamp
                                ,p_reg_instr_ident_record.created_by
                                ,p_reg_instr_ident_record.create_timestamp
                                ,p_reg_instr_ident_record.updated_by
                                ,p_reg_instr_ident_record.update_timestamp
                                ,p_reg_instr_ident_record.effective_start_timestamp
                                ,p_effective_end_timestamp
                                ,p_action
                                ,SYSTIMESTAMP
                                ,p_reg_instr_ident_record.is_deleted
                                ,p_reg_instr_ident_record.regulation
                                ,p_reg_instr_ident_record.trading_account_type
                                ,p_reg_instr_ident_record.instrument_code
                                ,p_reg_instr_ident_record.instrument_type
                                ,p_reg_instr_ident_record.instrument_country
                                ,p_reg_instr_ident_record.instrument_mic
                                ,p_reg_instr_ident_record.is_reportable
                                ,p_reg_instr_ident_record.reportable_identifier_type
                                ,p_reg_instr_ident_record.broker_code
                                ,p_reg_instr_ident_record.reportable_instrument_type
                                ,p_reg_instr_ident_record.derivative_type
                              );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            UPDATE REG_INSTR_IDENTIFICATION_H
                SET   regulatory_identification_name = p_reg_instr_ident_record.regulatory_identification_name
                      ,logical_load_timestamp = p_reg_instr_ident_record.logical_load_timestamp
                      ,updated_by = p_reg_instr_ident_record.updated_by
                      ,update_timestamp =p_reg_instr_ident_record.update_timestamp
                      ,action = p_action
                      ,action_timestamp = SYSTIMESTAMP
                      ,is_deleted = p_reg_instr_ident_record.is_deleted
                      ,regulation = p_reg_instr_ident_record.regulation
                      ,trading_account_type = p_reg_instr_ident_record.trading_account_type
                      ,instrument_code = p_reg_instr_ident_record.instrument_code
                      ,instrument_type = p_reg_instr_ident_record.instrument_type
                      ,instrument_country = p_reg_instr_ident_record.instrument_country
                      ,instrument_mic = p_reg_instr_ident_record.instrument_mic
                      ,is_reportable = p_reg_instr_ident_record.is_reportable
                      ,reportable_identifier_type = p_reg_instr_ident_record.reportable_identifier_type
                      ,broker_code = p_reg_instr_ident_record.broker_code
                      ,reportable_instrument_type =  p_reg_instr_ident_record.reportable_instrument_type
                      ,derivative_type = p_reg_instr_ident_record.derivative_type
              WHERE regulatory_identification_code = p_reg_instr_ident_record.regulatory_identification_code AND
                    effective_start_timestamp      = p_reg_instr_ident_record.effective_start_timestamp ;
        END put_history;


    -- ===================================================================================
    -- put_history
    -- ===================================================================================


    PROCEDURE put_history(p_old_product_identifier      IN product_snapshot_idntfrs%ROWTYPE,
                          p_effective_end_timestamp     IN product_snapshot_idntfrs_h.effective_end_timestamp%TYPE,
                          p_action                      IN product_snapshot_idntfrs_h.action%TYPE) IS

    BEGIN
      INSERT INTO product_snapshot_idntfrs_h (snapshot_identifier,
                                              logical_load_timestamp,
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              business_date,
                                              reporting_date,
                                              message_time)
                                       VALUES(p_old_product_identifier.snapshot_identifier,
                                              p_old_product_identifier.logical_load_timestamp,
                                              p_old_product_identifier.created_by,
                                              p_old_product_identifier.create_timestamp,
                                              p_old_product_identifier.updated_by,
                                              p_old_product_identifier.update_timestamp,
                                              p_old_product_identifier.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_product_identifier.business_date,
                                              p_old_product_identifier.reporting_date,
                                              p_old_product_identifier.message_time);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE product_snapshot_idntfrs_h
        SET logical_load_timestamp = p_old_product_identifier.logical_load_timestamp,
            created_by = p_old_product_identifier.created_by,
            create_timestamp = p_old_product_identifier.create_timestamp,
            updated_by = p_old_product_identifier.updated_by,
            update_timestamp = p_old_product_identifier.update_timestamp,
            effective_start_timestamp = p_old_product_identifier.effective_start_timestamp,
            effective_end_timestamp = p_effective_end_timestamp,
            action = p_action,
            action_timestamp = SYSTIMESTAMP,
            business_date = p_old_product_identifier.business_date,
            reporting_date = p_old_product_identifier.reporting_date,
            message_time = p_old_product_identifier.message_time
        WHERE snapshot_identifier = p_old_product_identifier.snapshot_identifier AND
              effective_start_timestamp = p_old_product_identifier.effective_start_timestamp;

    END;
        -- ===================================================================================
    -- put_history_snapshot_idntfrs
    -- ===================================================================================


    PROCEDURE put_history_snapshot_idntfrs(p_old_product_identifier      IN product_snapshot_idntfrs%ROWTYPE,
                          p_effective_end_timestamp     IN product_snapshot_idntfrs_h.effective_end_timestamp%TYPE,
                          p_action                      IN product_snapshot_idntfrs_h.action%TYPE) IS

    BEGIN
      INSERT INTO product_snapshot_idntfrs_h (snapshot_identifier,
                                              logical_load_timestamp,
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              business_date,
                                              reporting_date,
                                              message_time,
                                              is_processed)
                                       VALUES(p_old_product_identifier.snapshot_identifier,
                                              p_old_product_identifier.logical_load_timestamp,
                                              p_old_product_identifier.created_by,
                                              p_old_product_identifier.create_timestamp,
                                              p_old_product_identifier.updated_by,
                                              p_old_product_identifier.update_timestamp,
                                              p_old_product_identifier.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_product_identifier.business_date,
                                              p_old_product_identifier.reporting_date,
                                              p_old_product_identifier.message_time,
                                              p_old_product_identifier.is_processed);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE product_snapshot_idntfrs_h
        SET logical_load_timestamp = p_old_product_identifier.logical_load_timestamp,
            created_by = p_old_product_identifier.created_by,
            create_timestamp = p_old_product_identifier.create_timestamp,
            updated_by = p_old_product_identifier.updated_by,
            update_timestamp = p_old_product_identifier.update_timestamp,
            effective_start_timestamp = p_old_product_identifier.effective_start_timestamp,
            effective_end_timestamp = p_effective_end_timestamp,
            action = p_action,
            action_timestamp = SYSTIMESTAMP,
            business_date = p_old_product_identifier.business_date,
            reporting_date = p_old_product_identifier.reporting_date,
            message_time = p_old_product_identifier.message_time,
            is_processed = p_old_product_identifier.is_processed
        WHERE snapshot_identifier = p_old_product_identifier.snapshot_identifier AND
              effective_start_timestamp = p_old_product_identifier.effective_start_timestamp;

    END;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================

    PROCEDURE put_history(p_old_product_settings        IN product_settings%ROWTYPE,
                          p_effective_end_timestamp     IN product_settings_h.effective_end_timestamp%TYPE,
                          p_action                      IN product_settings_h.action%TYPE) IS

    BEGIN
      INSERT INTO product_settings_h (product_setting_code
                                     ,logical_load_timestamp
                                     ,created_by
                                     ,create_timestamp
                                     ,updated_by
                                     ,update_timestamp
                                     ,effective_start_timestamp
                                     ,effective_end_timestamp
                                     ,action
                                     ,action_timestamp
                                     ,product_schema_code
                                     ,trading_risk_schema_code
                                     ,stop_loss_buffer
                                     ,stop_loss_buffer_type
                                     ,stop_entry_buffer
                                     ,stop_entry_buffer_type
                                     ,is_deleted
                                     ,binary_type
                                     ,tenor)
                               VALUES(p_old_product_settings.product_setting_code,
                                      p_old_product_settings.logical_load_timestamp,
                                      p_old_product_settings.created_by,
                                      p_old_product_settings.create_timestamp,
                                      p_old_product_settings.updated_by,
                                      p_old_product_settings.update_timestamp,
                                      p_old_product_settings.effective_start_timestamp,
                                      p_effective_end_timestamp,
                                      p_action,
                                      SYSTIMESTAMP,
                                      p_old_product_settings.product_schema_code,
                                      p_old_product_settings.trading_risk_schema_code,
                                      p_old_product_settings.stop_loss_buffer,
                                      p_old_product_settings.stop_loss_buffer_type,
                                      p_old_product_settings.stop_entry_buffer,
                                      p_old_product_settings.stop_entry_buffer_type,
                                      p_old_product_settings.is_deleted,
                                      p_old_product_settings.binary_type,
                                      p_old_product_settings.tenor);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE product_settings_h
        SET logical_load_timestamp = p_old_product_settings.logical_load_timestamp,
            created_by = p_old_product_settings.created_by,
            create_timestamp = p_old_product_settings.create_timestamp,
            updated_by = p_old_product_settings.updated_by,
            update_timestamp = p_old_product_settings.update_timestamp,
            effective_start_timestamp = p_old_product_settings.effective_start_timestamp,
            effective_end_timestamp = p_effective_end_timestamp,
            action = p_action,
            action_timestamp = SYSTIMESTAMP,
            product_schema_code = p_old_product_settings.product_schema_code,
            trading_risk_schema_code = p_old_product_settings.trading_risk_schema_code,
            stop_loss_buffer = p_old_product_settings.stop_loss_buffer,
            stop_loss_buffer_type = p_old_product_settings.stop_loss_buffer_type,
            stop_entry_buffer = p_old_product_settings.stop_entry_buffer,
            stop_entry_buffer_type = p_old_product_settings.stop_entry_buffer_type,
            is_deleted = p_old_product_settings.is_deleted,
            binary_type = p_old_product_settings.binary_type,
            tenor = p_old_product_settings.tenor
      WHERE product_setting_code = p_old_product_settings.product_setting_code AND
            effective_start_timestamp = p_old_product_settings.effective_start_timestamp;

    END;
    -- ===================================================================================
    -- put_snapshot_identifier
    -- ===================================================================================
    --
    --

    PROCEDURE put_snapshot_identifier (p_user                            IN products.created_by%TYPE,
                                       p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                       p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
                                       p_logical_load_timestamp          IN product_snapshot_idntfrs.logical_load_timestamp%TYPE,
                                       p_is_processed                    IN product_snapshot_idntfrs.is_processed%TYPE
                                      ) IS
      lv_old_snapshot_id product_snapshot_idntfrs%ROWTYPE;
    BEGIN
      BEGIN
        SELECT *
        INTO lv_old_snapshot_id
        FROM product_snapshot_idntfrs
        WHERE snapshot_identifier = p_snapshot_identifier AND
              effective_start_timestamp <> p_effective_start_timestamp;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          lv_old_snapshot_id.snapshot_identifier := NULL;
      END;

      IF lv_old_snapshot_id.snapshot_identifier IS NOT NULL THEN
        put_history(p_old_product_identifier      => lv_old_snapshot_id,
                    p_effective_end_timestamp     => p_effective_start_timestamp,
                    p_action                      => 'U');
      END IF;

      MERGE INTO product_snapshot_idntfrs existing
      USING (SELECT p_snapshot_identifier AS snapshot_identifier
             FROM dual) new_identifier
      ON (existing.snapshot_identifier = new_identifier.snapshot_identifier)
      WHEN MATCHED THEN
        UPDATE
        SET logical_load_timestamp = p_logical_load_timestamp,
            updated_by = p_user,
            update_timestamp = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            business_date = nrg_common.get_business_date(p_effective_start_timestamp),
            reporting_date = nrg_common.get_reporting_date(p_effective_start_timestamp),
            message_time = p_effective_start_timestamp,
            is_processed = p_is_processed
        WHERE p_is_processed = 'YES'
      WHEN NOT MATCHED THEN
        INSERT (snapshot_identifier,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                business_date,
                reporting_date,
                message_time,
                is_processed)
         VALUES(new_identifier.snapshot_identifier,
                p_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                nrg_common.get_business_date(p_effective_start_timestamp),
                nrg_common.get_reporting_date(p_effective_start_timestamp),
                p_effective_start_timestamp,
                p_is_processed);
    END;

    -- ===================================================================================
    -- PUBLIC MODULES
    -- ===================================================================================
    --

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION VERSION
        RETURN VARCHAR2 deterministic
    IS

    BEGIN
        logger.logger.set_module('version');
        RETURN gc_version;
    exception
        WHEN others THEN
            logger.logger.SEVERE(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            raise;
    END VERSION;

    -- ===================================================================================
    -- create_instrument_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product stub in case the instrument is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_instrument_code                Instrument Code
    --     p_product_platform               Product Platform
    --     p_product_wrapper                Product Wrapper
    --     p_mm_instrument_id               MM Instrument Id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_instrument_stub (p_user                       IN products.created_by%TYPE,
                                      p_logical_load_timestamp     IN products.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp  IN products.effective_start_timestamp%TYPE,
                                      p_instrument_code            IN products.instrument_code%TYPE,
                                      p_product_platform           IN products.platform%TYPE,
                                      p_product_wrapper            IN products.wrapper_code%TYPE,
                                      p_mm_instrument_id           IN mm_products.mm_instrument_id%TYPE)
    IS
      PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      IF p_instrument_code IS NOT NULL THEN
        BEGIN
          INSERT INTO instruments (instrument_code,
                                   logical_load_timestamp,
                                   created_by,
                                   create_timestamp,
                                   updated_by,
                                   update_timestamp,
                                   effective_start_timestamp)
                           VALUES (p_instrument_code,
                                   p_logical_load_timestamp,
                                   p_user,
                                   SYSTIMESTAMP,
                                   p_user,
                                   SYSTIMESTAMP,
                                   gc_default_timestamp);
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            --
            --In case the instrument already exists no need to insert the data
            --
            NULL;
        END;
      END IF;

      IF p_product_wrapper IS NOT NULL THEN
        BEGIN
          INSERT INTO product_wrappers (wrapper_code,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp)
                                 VALUES (p_product_wrapper,
                                         p_logical_load_timestamp,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_user,
                                         SYSTIMESTAMP,
                                         gc_default_timestamp);
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            --
            --In case the instrument already exists no need to insert the data
            --
            NULL;
        END;
      END IF;

      IF p_instrument_code IS NOT NULL AND p_product_wrapper IS NOT NULL THEN
        BEGIN
          INSERT INTO products (instrument_code,
                                platform,
                                wrapper_code,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp)
                        VALUES( p_instrument_code,
                               p_product_platform,
                               p_product_wrapper,
                               p_logical_load_timestamp,
                               p_user,
                               SYSTIMESTAMP,
                               p_user,
                               SYSTIMESTAMP,
                               gc_default_timestamp);
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            --
            --Since the record exists we do not need to indert the record
            --
            NULL;
        END;
      END IF;

      IF p_product_platform <> 'NG' AND
         p_instrument_code IS NOT NULL AND
         p_product_wrapper IS NOT NULL AND
         p_mm_instrument_id IS NOT NULL THEN
        BEGIN
          INSERT INTO mm_products (mm_instrument_id,
                              instrument_code,
                              platform,
                              wrapper_code,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              effective_start_timestamp)
                      VALUES(p_mm_instrument_id,
                             p_instrument_code,
                             p_product_platform,
                             p_product_wrapper,
                             p_logical_load_timestamp,
                             p_user,
                             SYSTIMESTAMP,
                             p_user,
                             SYSTIMESTAMP,
                             gc_default_timestamp);
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            --
            --The entry already exists hence can be ignored
            --
            NULL;
        END;
      END IF;
      COMMIT;
    END create_instrument_stub;
    -- ===================================================================================
    -- put_inst_feed_setting
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     instrument_feed_settings
    --     instrument_feed_settings_h
    --
    --
    --  From NRG we get only the updated data set each time a new product snapshot is created by PMS. Also the freequency of updates
    --  to products is minimal.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_inst_feed_setting
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_inst_feed_setting (p_user                               IN products.created_by%TYPE,
                                     p_effective_start_timestamp          IN products.effective_start_timestamp%TYPE,
                                     p_inst_feed_setting                  IN instrument_feed_setting_tab)
    IS
      lv_logical_load_timestamp TIMESTAMP(6):= systimestamp;
    BEGIN
    --
    -- Select the instrument feed settings that have got updated
    --

    MERGE INTO instrument_feed_settings_h old_version
     USING (SELECT DISTINCT ifs.*
              FROM instrument_feed_settings ifs, TABLE(CAST(p_inst_feed_setting AS instrument_feed_setting_tab))ifs_new
              WHERE ifs.feed_setting_code = ifs_new.feed_setting_code AND
                    (nrg_common.has_value_changed(ifs.ng_quote_symbol, ifs_new.ng_quote_symbol) = 1 OR
                     nrg_common.has_value_changed(ifs.band1_min_absolute_bid_margin, ifs_new.band1_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band1_min_absolute_ask_margin, ifs_new.band1_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band1_proportional_margin, ifs_new.band1_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band2_min_absolute_bid_margin, ifs_new.band2_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band2_min_absolute_ask_margin, ifs_new.band2_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band2_proportional_margin, ifs_new.band2_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band3_min_absolute_bid_margin, ifs_new.band3_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band3_min_absolute_ask_margin, ifs_new.band3_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band3_proportional_margin, ifs_new.band3_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band4_min_absolute_bid_margin, ifs_new.band4_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band4_min_absolute_ask_margin, ifs_new.band4_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band4_proportional_margin, ifs_new.band4_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band5_min_absolute_bid_margin, ifs_new.band5_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band5_min_absolute_ask_margin, ifs_new.band5_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band5_proportional_margin, ifs_new.band5_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.feed_producer, ifs_new.feed_producer) = 1 OR
                     nrg_common.has_value_changed(ifs.feed_symbol, ifs_new.feed_symbol) = 1 OR
                     nrg_common.has_value_changed(ifs.access_level, ifs_new.access_level) = 1 OR
                     nrg_common.has_value_changed(ifs.price_number_of_decimals, ifs_new.price_number_of_decimals) = 1 OR
                     nrg_common.has_value_changed(ifs.initial_published_date, ifs_new.initial_published_date) = 1 OR
                     nrg_common.has_value_changed(ifs.last_published_date, ifs_new.last_published_date) = 1 OR
                     nrg_common.has_value_changed(ifs.band6_min_absolute_bid_margin, ifs_new.band6_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band6_min_absolute_ask_margin, ifs_new.band6_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band6_proportional_margin, ifs_new.band6_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band7_min_absolute_bid_margin, ifs_new.band7_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band7_min_absolute_ask_margin, ifs_new.band7_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band7_proportional_margin, ifs_new.band7_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band8_min_absolute_bid_margin, ifs_new.band8_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band8_min_absolute_ask_margin, ifs_new.band8_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band8_proportional_margin, ifs_new.band8_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band9_min_absolute_bid_margin, ifs_new.band9_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band9_min_absolute_ask_margin, ifs_new.band9_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band9_proportional_margin, ifs_new.band9_proportional_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band10_min_absolute_bid_margin, ifs_new.band10_min_absolute_bid_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band10_min_absolute_ask_margin, ifs_new.band10_min_absolute_ask_margin) = 1 OR
                     nrg_common.has_value_changed(ifs.band10_proportional_margin, ifs_new.band10_proportional_margin) = 1)) new_version
        ON (old_version.feed_setting_code = new_version.feed_setting_code AND
            old_version.EFFECTIVE_START_TIMESTAMP = new_version.effective_start_timestamp)
       WHEN MATCHED THEN
       UPDATE
               SET old_version.logical_load_timestamp = new_version.logical_load_timestamp,
                   old_version.created_by = new_version.created_by,
                   old_version.create_timestamp = new_version.create_timestamp,
                   old_version.updated_by = new_version.updated_by,
                   old_version.update_timestamp = new_version.update_timestamp,
                   --old_version.effective_start_timestamp = new_version.effective_start_timestamp,
                   old_version.effective_end_timestamp = p_effective_start_timestamp,
                   old_version.action = 'U',
                   old_version.action_timestamp = SYSTIMESTAMP,
                   old_version.instrument_code = new_version.instrument_code,
                   old_version.feed_type = new_version.feed_type,
                   old_version.ng_quote_symbol = new_version.ng_quote_symbol,
                   old_version.band1_min_absolute_bid_margin = new_version.band1_min_absolute_bid_margin,
                   old_version.band1_min_absolute_ask_margin = new_version.band1_min_absolute_ask_margin,
                   old_version.band1_proportional_margin = new_version.band1_proportional_margin,
                   old_version.band2_min_absolute_bid_margin = new_version.band2_min_absolute_bid_margin,
                   old_version.band2_min_absolute_ask_margin = new_version.band2_min_absolute_ask_margin,
                   old_version.band2_proportional_margin = new_version.band2_proportional_margin,
                   old_version.band3_min_absolute_bid_margin = new_version.band3_min_absolute_bid_margin,
                   old_version.band3_min_absolute_ask_margin = new_version.band3_min_absolute_ask_margin,
                   old_version.band3_proportional_margin = new_version.band3_proportional_margin,
                   old_version.band4_min_absolute_bid_margin = new_version.band4_min_absolute_bid_margin,
                   old_version.band4_min_absolute_ask_margin = new_version.band4_min_absolute_ask_margin,
                   old_version.band4_proportional_margin = new_version.band4_proportional_margin,
                   old_version.band5_min_absolute_bid_margin = new_version.band5_min_absolute_bid_margin,
                   old_version.band5_min_absolute_ask_margin = new_version.band5_min_absolute_ask_margin,
                   old_version.band5_proportional_margin = new_version.band5_proportional_margin,
                   old_version.feed_producer = new_version.feed_producer,
                   old_version.feed_symbol = new_version.feed_symbol,
                   old_version.access_level = new_version.access_level,
                   old_version.price_number_of_decimals = new_version.price_number_of_decimals,
                   old_version.initial_published_date = new_version.initial_published_date,
                   old_version.last_published_date = new_version.last_published_date,
                   old_version.is_deleted = new_version.is_deleted,
                   old_version.band6_min_absolute_bid_margin = new_version.band6_min_absolute_bid_margin,
                   old_version.band6_min_absolute_ask_margin = new_version.band6_min_absolute_ask_margin,
                   old_version.band6_proportional_margin = new_version.band6_proportional_margin,
                   old_version.band7_min_absolute_bid_margin = new_version.band7_min_absolute_bid_margin,
                   old_version.band7_min_absolute_ask_margin = new_version.band7_min_absolute_ask_margin,
                   old_version.band7_proportional_margin = new_version.band7_proportional_margin,
                   old_version.band8_min_absolute_bid_margin = new_version.band8_min_absolute_bid_margin,
                   old_version.band8_min_absolute_ask_margin = new_version.band8_min_absolute_ask_margin,
                   old_version.band8_proportional_margin = new_version.band8_proportional_margin,
                   old_version.band9_min_absolute_bid_margin = new_version.band9_min_absolute_bid_margin,
                   old_version.band9_min_absolute_ask_margin = new_version.band9_min_absolute_ask_margin,
                   old_version.band9_proportional_margin = new_version.band9_proportional_margin,
                   old_version.band10_min_absolute_bid_margin = new_version.band10_min_absolute_bid_margin,
                   old_version.band10_min_absolute_ask_margin = new_version.band10_min_absolute_ask_margin,
                   old_version.band10_proportional_margin = new_version.band10_proportional_margin
          WHEN NOT MATCHED THEN
           INSERT (feed_setting_code,
                   logical_load_timestamp,
                   created_by,
                   create_timestamp,
                   updated_by,
                   update_timestamp,
                   effective_start_timestamp,
                   effective_end_timestamp,
                   action,
                   action_timestamp,
                   instrument_code,
                   feed_type,
                   ng_quote_symbol,
                   band1_min_absolute_bid_margin,
                   band1_min_absolute_ask_margin,
                   band1_proportional_margin,
                   band2_min_absolute_bid_margin,
                   band2_min_absolute_ask_margin,
                   band2_proportional_margin,
                   band3_min_absolute_bid_margin,
                   band3_min_absolute_ask_margin,
                   band3_proportional_margin,
                   band4_min_absolute_bid_margin,
                   band4_min_absolute_ask_margin,
                   band4_proportional_margin,
                   band5_min_absolute_bid_margin,
                   band5_min_absolute_ask_margin,
                   band5_proportional_margin,
                   feed_producer,
                   feed_symbol,
                   access_level,
                   price_number_of_decimals,
                   initial_published_date,
                   last_published_date,
                   is_deleted,
                   band6_min_absolute_bid_margin,
                   band6_min_absolute_ask_margin,
                   band6_proportional_margin,
                   band7_min_absolute_bid_margin,
                   band7_min_absolute_ask_margin,
                   band7_proportional_margin,
                   band8_min_absolute_bid_margin,
                   band8_min_absolute_ask_margin,
                   band8_proportional_margin,
                   band9_min_absolute_bid_margin,
                   band9_min_absolute_ask_margin,
                   band9_proportional_margin,
                   band10_min_absolute_bid_margin,
                   band10_min_absolute_ask_margin,
                   band10_proportional_margin)
                  VALUES
                  (
                    new_version.feed_setting_code,
                    new_version.logical_load_timestamp,
                    new_version.created_by,
                    new_version.create_timestamp,
                    new_version.updated_by,
                    new_version.update_timestamp,
                    new_version.effective_start_timestamp,
                    p_effective_start_timestamp,
                    'U',
                    SYSTIMESTAMP,
                    new_version.instrument_code,
                    new_version.feed_type,
                    new_version.ng_quote_symbol,
                    new_version.band1_min_absolute_bid_margin,
                    new_version.band1_min_absolute_ask_margin,
                    new_version.band1_proportional_margin,
                    new_version.band2_min_absolute_bid_margin,
                    new_version.band2_min_absolute_ask_margin,
                    new_version.band2_proportional_margin,
                    new_version.band3_min_absolute_bid_margin,
                    new_version.band3_min_absolute_ask_margin,
                    new_version.band3_proportional_margin,
                    new_version.band4_min_absolute_bid_margin,
                    new_version.band4_min_absolute_ask_margin,
                    new_version.band4_proportional_margin,
                    new_version.band5_min_absolute_bid_margin,
                    new_version.band5_min_absolute_ask_margin,
                    new_version.band5_proportional_margin,
                    new_version.feed_producer,
                    new_version.feed_symbol,
                    new_version.access_level,
                    new_version.price_number_of_decimals,
                    new_version.initial_published_date,
                    new_version.last_published_date,
                    new_version.is_deleted,
                    new_version.band6_min_absolute_bid_margin,
                    new_version.band6_min_absolute_ask_margin,
                    new_version.band6_proportional_margin,
                    new_version.band7_min_absolute_bid_margin,
                    new_version.band7_min_absolute_ask_margin,
                    new_version.band7_proportional_margin,
                    new_version.band8_min_absolute_bid_margin,
                    new_version.band8_min_absolute_ask_margin,
                    new_version.band8_proportional_margin,
                    new_version.band9_min_absolute_bid_margin,
                    new_version.band9_min_absolute_ask_margin,
                    new_version.band9_proportional_margin,
                    new_version.band10_min_absolute_bid_margin,
                    new_version.band10_min_absolute_ask_margin,
                    new_version.band10_proportional_margin
                  );

    --
    --Soft Delete The Deleted Rows
    --

    UPDATE instrument_feed_settings ifs
    SET is_deleted = 'YES',
        logical_load_timestamp = lv_logical_load_timestamp
    WHERE NOT EXISTS (SELECT 1
                      FROM TABLE(CAST(p_inst_feed_setting AS instrument_feed_setting_tab))
                      WHERE feed_setting_code = ifs.feed_setting_code)
      AND ifs.Is_Deleted = 'NO';

    --
    --Insert and update data
    --

    MERGE INTO instrument_feed_settings ifs
    USING (SELECT *
           FROM TABLE(CAST(p_inst_feed_setting AS instrument_feed_setting_tab))) ifs_new
    ON (ifs.feed_setting_code = ifs_new.feed_setting_code)
    WHEN MATCHED THEN
      UPDATE
      SET   ifs.logical_load_timestamp = lv_logical_load_timestamp,
            ifs.updated_by = p_user,
            ifs.update_timestamp = SYSTIMESTAMP,
            ifs.effective_start_timestamp = p_effective_start_timestamp,
            ifs.instrument_code = ifs_new.instrument_code,
            ifs.feed_type = ifs_new.feed_type,
            ifs.ng_quote_symbol = ifs_new.ng_quote_symbol,
            ifs.band1_min_absolute_bid_margin = ifs_new.band1_min_absolute_bid_margin,
            ifs.band1_min_absolute_ask_margin = ifs_new.band1_min_absolute_ask_margin,
            ifs.band1_proportional_margin = ifs_new.band1_proportional_margin,
            ifs.band2_min_absolute_bid_margin = ifs_new.band2_min_absolute_bid_margin,
            ifs.band2_min_absolute_ask_margin = ifs_new.band2_min_absolute_ask_margin,
            ifs.band2_proportional_margin = ifs_new.band2_proportional_margin,
            ifs.band3_min_absolute_bid_margin = ifs_new.band3_min_absolute_bid_margin,
            ifs.band3_min_absolute_ask_margin = ifs_new.band3_min_absolute_ask_margin,
            ifs.band3_proportional_margin = ifs_new.band3_proportional_margin,
            ifs.band4_min_absolute_bid_margin = ifs_new.band4_min_absolute_bid_margin,
            ifs.band4_min_absolute_ask_margin = ifs_new.band4_min_absolute_ask_margin,
            ifs.band4_proportional_margin = ifs_new.band4_proportional_margin,
            ifs.band5_min_absolute_bid_margin = ifs_new.band5_min_absolute_bid_margin,
            ifs.band5_min_absolute_ask_margin = ifs_new.band5_min_absolute_ask_margin,
            ifs.band5_proportional_margin = ifs_new.band5_proportional_margin,
            ifs.feed_producer = ifs_new.feed_producer,
            ifs.feed_symbol = ifs_new.feed_symbol,
            ifs.access_level = ifs_new.access_level,
            ifs.price_number_of_decimals = ifs_new.price_number_of_decimals,
            ifs.initial_published_date = ifs_new.initial_published_date,
            ifs.last_published_date = ifs_new.last_published_date,
            ifs.is_deleted = 'NO',
            ifs.band6_min_absolute_bid_margin = ifs_new.band6_min_absolute_bid_margin,
            ifs.band6_min_absolute_ask_margin = ifs_new.band6_min_absolute_ask_margin,
            ifs.band6_proportional_margin = ifs_new.band6_proportional_margin,
            ifs.band7_min_absolute_bid_margin = ifs_new.band7_min_absolute_bid_margin,
            ifs.band7_min_absolute_ask_margin = ifs_new.band7_min_absolute_ask_margin,
            ifs.band7_proportional_margin = ifs_new.band7_proportional_margin,
            ifs.band8_min_absolute_bid_margin = ifs_new.band8_min_absolute_bid_margin,
            ifs.band8_min_absolute_ask_margin = ifs_new.band8_min_absolute_ask_margin,
            ifs.band8_proportional_margin = ifs_new.band8_proportional_margin,
            ifs.band9_min_absolute_bid_margin = ifs_new.band9_min_absolute_bid_margin,
            ifs.band9_min_absolute_ask_margin = ifs_new.band9_min_absolute_ask_margin,
            ifs.band9_proportional_margin = ifs_new.band9_proportional_margin,
            ifs.band10_min_absolute_bid_margin = ifs_new.band10_min_absolute_bid_margin,
            ifs.band10_min_absolute_ask_margin = ifs_new.band10_min_absolute_ask_margin,
            ifs.band10_proportional_margin = ifs_new.band10_proportional_margin
      WHERE (nrg_common.has_value_changed(ifs.ng_quote_symbol, ifs_new.ng_quote_symbol) = 1 OR
             nrg_common.has_value_changed(ifs.band1_min_absolute_bid_margin, ifs_new.band1_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band1_min_absolute_ask_margin, ifs_new.band1_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band1_proportional_margin, ifs_new.band1_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band2_min_absolute_bid_margin, ifs_new.band2_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band2_min_absolute_ask_margin, ifs_new.band2_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band2_proportional_margin, ifs_new.band2_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band3_min_absolute_bid_margin, ifs_new.band3_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band3_min_absolute_ask_margin, ifs_new.band3_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band3_proportional_margin, ifs_new.band3_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band4_min_absolute_bid_margin, ifs_new.band4_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band4_min_absolute_ask_margin, ifs_new.band4_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band4_proportional_margin, ifs_new.band4_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band5_min_absolute_bid_margin, ifs_new.band5_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band5_min_absolute_ask_margin, ifs_new.band5_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band5_proportional_margin, ifs_new.band5_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.feed_producer, ifs_new.feed_producer) = 1 OR
             nrg_common.has_value_changed(ifs.feed_symbol, ifs_new.feed_symbol) = 1 OR
             nrg_common.has_value_changed(ifs.access_level, ifs_new.access_level) = 1 OR
             nrg_common.has_value_changed(ifs.price_number_of_decimals, ifs_new.price_number_of_decimals) = 1 OR
             nrg_common.has_value_changed(ifs.initial_published_date, ifs_new.initial_published_date) = 1 OR
             nrg_common.has_value_changed(ifs.last_published_date, ifs_new.last_published_date) = 1 OR
             nrg_common.has_value_changed(ifs.is_deleted, 'NO') = 1 OR
             nrg_common.has_value_changed(ifs.band6_min_absolute_bid_margin, ifs_new.band6_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band6_min_absolute_ask_margin, ifs_new.band6_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band6_proportional_margin, ifs_new.band6_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band7_min_absolute_bid_margin, ifs_new.band7_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band7_min_absolute_ask_margin, ifs_new.band7_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band7_proportional_margin, ifs_new.band7_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band8_min_absolute_bid_margin, ifs_new.band8_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band8_min_absolute_ask_margin, ifs_new.band8_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band8_proportional_margin, ifs_new.band8_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band9_min_absolute_bid_margin, ifs_new.band9_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band9_min_absolute_ask_margin, ifs_new.band9_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band9_proportional_margin, ifs_new.band9_proportional_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band10_min_absolute_bid_margin, ifs_new.band10_min_absolute_bid_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band10_min_absolute_ask_margin, ifs_new.band10_min_absolute_ask_margin) = 1 OR
             nrg_common.has_value_changed(ifs.band10_proportional_margin, ifs_new.band10_proportional_margin) = 1)
    WHEN NOT MATCHED THEN
      INSERT (feed_setting_code,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              instrument_code,
              feed_type,
              ng_quote_symbol,
              band1_min_absolute_bid_margin,
              band1_min_absolute_ask_margin,
              band1_proportional_margin,
              band2_min_absolute_bid_margin,
              band2_min_absolute_ask_margin,
              band2_proportional_margin,
              band3_min_absolute_bid_margin,
              band3_min_absolute_ask_margin,
              band3_proportional_margin,
              band4_min_absolute_bid_margin,
              band4_min_absolute_ask_margin,
              band4_proportional_margin,
              band5_min_absolute_bid_margin,
              band5_min_absolute_ask_margin,
              band5_proportional_margin,
              feed_producer,
              feed_symbol,
              access_level,
              price_number_of_decimals,
              initial_published_date,
              last_published_date,
              is_deleted,
              band6_min_absolute_bid_margin,
              band6_min_absolute_ask_margin,
              band6_proportional_margin,
              band7_min_absolute_bid_margin,
              band7_min_absolute_ask_margin,
              band7_proportional_margin,
              band8_min_absolute_bid_margin,
              band8_min_absolute_ask_margin,
              band8_proportional_margin,
              band9_min_absolute_bid_margin,
              band9_min_absolute_ask_margin,
              band9_proportional_margin,
              band10_min_absolute_bid_margin,
              band10_min_absolute_ask_margin,
              band10_proportional_margin)
      VALUES (ifs_new.feed_setting_code,
              lv_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              ifs_new.instrument_code,
              ifs_new.feed_type,
              ifs_new.ng_quote_symbol,
              ifs_new.band1_min_absolute_bid_margin,
              ifs_new.band1_min_absolute_ask_margin,
              ifs_new.band1_proportional_margin,
              ifs_new.band2_min_absolute_bid_margin,
              ifs_new.band2_min_absolute_ask_margin,
              ifs_new.band2_proportional_margin,
              ifs_new.band3_min_absolute_bid_margin,
              ifs_new.band3_min_absolute_ask_margin,
              ifs_new.band3_proportional_margin,
              ifs_new.band4_min_absolute_bid_margin,
              ifs_new.band4_min_absolute_ask_margin,
              ifs_new.band4_proportional_margin,
              ifs_new.band5_min_absolute_bid_margin,
              ifs_new.band5_min_absolute_ask_margin,
              ifs_new.band5_proportional_margin,
              ifs_new.feed_producer,
              ifs_new.feed_symbol,
              ifs_new.access_level,
              ifs_new.price_number_of_decimals,
              ifs_new.initial_published_date,
              ifs_new.last_published_date,
              'NO',
              ifs_new.band6_min_absolute_bid_margin,
              ifs_new.band6_min_absolute_ask_margin,
              ifs_new.band6_proportional_margin,
              ifs_new.band7_min_absolute_bid_margin,
              ifs_new.band7_min_absolute_ask_margin,
              ifs_new.band7_proportional_margin,
              ifs_new.band8_min_absolute_bid_margin,
              ifs_new.band8_min_absolute_ask_margin,
              ifs_new.band8_proportional_margin,
              ifs_new.band9_min_absolute_bid_margin,
              ifs_new.band9_min_absolute_ask_margin,
              ifs_new.band9_proportional_margin,
              ifs_new.band10_min_absolute_bid_margin,
              ifs_new.band10_min_absolute_ask_margin,
              ifs_new.band10_proportional_margin);

    END;
    -- ===================================================================================
    -- put_product_settings
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     product_settings
    --     product_settings_h
    --
    --
    --  From NRG we get only the updated data set each time a new product snapshot is created by PMS. Also the freequency of updates
    --  to products is minimal.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_product_settings               product_settings_tab
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_product_settings(p_user                       IN products.created_by%TYPE,
                                   p_effective_start_timestamp  IN products.effective_start_timestamp%TYPE,
                                   p_product_settings           IN product_settings_tab)
    IS
     lv_logical_load_timestamp TIMESTAMP(6):= systimestamp;
     TYPE lvtyp_old_prd_sttngs_mrgn IS TABLE OF Product_Settings_Margin_Defs%rowtype;
     lv_old_prd_sttngs_margin lvtyp_old_prd_sttngs_mrgn;

     lv_del_prd_sttngs_margin lvtyp_old_prd_sttngs_mrgn;
    BEGIN


   MERGE INTO product_settings_h old_version
   USING (SELECT old_version.*
          FROM product_settings old_version, TABLE(CAST(p_product_settings AS product_settings_tab)) new_version
          WHERE old_version.product_setting_code = new_version.product_setting_code(+) AND
                (nrg_common.has_value_changed(old_version.product_schema_code, new_version.product_schema_code)= 1 OR
                 nrg_common.has_value_changed(old_version.trading_risk_schema_code, new_version.trading_risk_schema_code)= 1 OR
                 nrg_common.has_value_changed(old_version.stop_loss_buffer, new_version.stop_loss_buffer)= 1 OR
                 nrg_common.has_value_changed(old_version.stop_loss_buffer_type, decode(new_version.stop_loss_buffer_type,0,'PercentOfPrice',1,'PercentOfMinSpread',2,'AbsolutePointValue'))= 1 OR
                 nrg_common.has_value_changed(old_version.stop_entry_buffer, new_version.stop_entry_buffer)= 1 OR
                 nrg_common.has_value_changed(old_version.stop_entry_buffer_type, decode( new_version.stop_entry_buffer_type,0,'PercentOfPrice',1,'PercentOfMinSpread',2,'AbsolutePointValue'))= 1 OR
                 nrg_common.has_value_changed(old_version.binary_type, new_version.binary_type) = 1 OR
                 nrg_common.has_value_changed(old_version.tenor, new_version.tenor) = 1
                 ) AND
                old_version.Is_Deleted='NO') new_version
    ON (old_version.product_setting_code = new_version.product_setting_code AND
        old_version.effective_start_timestamp = new_version.effective_start_timestamp)
   WHEN MATCHED THEN
     UPDATE  SET old_version.logical_load_timestamp = new_version.logical_load_timestamp,
                 old_version.created_by = new_version.created_by,
                 old_version.create_timestamp = new_version.create_timestamp,
                 old_version.updated_by = new_version.updated_by,
                 old_version.update_timestamp = new_version.update_timestamp,
                 old_version.effective_end_timestamp = p_effective_start_timestamp,
                 old_version.action = 'U',
                 old_version.action_timestamp = SYSTIMESTAMP,
                 old_version.product_schema_code = new_version.product_schema_code,
                 old_version.trading_risk_schema_code = new_version.trading_risk_schema_code,
                 old_version.stop_loss_buffer = new_version.stop_loss_buffer,
                 old_version.stop_loss_buffer_type = new_version.stop_loss_buffer_type,
                 old_version.stop_entry_buffer = new_version.stop_entry_buffer,
                 old_version.stop_entry_buffer_type = new_version.stop_entry_buffer_type,
                 old_version.is_deleted = new_version.is_deleted,
                 old_version.binary_type = new_version.binary_type,
                 old_version.tenor = new_version.tenor
   WHEN NOT MATCHED THEN
    INSERT
           (product_setting_code
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,effective_end_timestamp
           ,action
           ,action_timestamp
           ,product_schema_code
           ,trading_risk_schema_code
           ,stop_loss_buffer
           ,stop_loss_buffer_type
           ,stop_entry_buffer
           ,stop_entry_buffer_type
           ,is_deleted
           ,binary_type
           ,tenor
           ,instrument_code)
     VALUES(new_version.product_setting_code,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.product_schema_code,
            new_version.trading_risk_schema_code,
            new_version.stop_loss_buffer,
            new_version.stop_loss_buffer_type,
            new_version.stop_entry_buffer,
            new_version.stop_entry_buffer_type,
            new_version.is_deleted,
            new_version.binary_type,
            new_version.tenor,
            new_version.instrument_code);

    --
    --PRODUCTS_GSKO Soft Delete The Deleted Rows
    --

    UPDATE product_settings old_version
    SET is_deleted = 'YES',
        logical_load_timestamp = lv_logical_load_timestamp
    WHERE NOT EXISTS (SELECT 1
                      FROM TABLE(CAST(p_product_settings  AS product_settings_tab))new_version
                      WHERE new_version.product_setting_code = old_version.product_setting_code)
         AND is_deleted = 'NO';


    --
    --Insert and update products_settings
    --

    MERGE INTO product_settings old_version
    USING (SELECT product_setting_code
                  ,product_schema_code
                  ,trading_risk_schema_code
                  ,stop_loss_buffer
                  ,decode(stop_loss_buffer_type,0,'PercentOfPrice',1,'PercentOfMinSpread',2,'AbsolutePointValue') stop_loss_buffer_type
                  ,stop_entry_buffer
                  ,decode(stop_entry_buffer_type,0,'PercentOfPrice',1,'PercentOfMinSpread',2,'AbsolutePointValue') stop_entry_buffer_type
                  ,binary_type
                  ,tenor
                  ,instrument_code
           FROM TABLE(CAST(p_product_settings  AS product_settings_tab)) ) new_version
    ON (new_version.product_setting_code = old_version.product_setting_code)
    WHEN MATCHED THEN
      UPDATE
      SET   old_version.product_schema_code = new_version.product_schema_code,
            old_version.logical_load_timestamp = lv_logical_load_timestamp,
            old_version.updated_by = p_user,
            old_version.update_timestamp = SYSTIMESTAMP,
            old_version.effective_start_timestamp = p_effective_start_timestamp,
            old_version.is_deleted = 'NO',
            old_version.trading_risk_schema_code = new_version.trading_risk_schema_code,
            old_version.stop_loss_buffer = new_version.stop_loss_buffer,
            old_version.stop_loss_buffer_type = new_version.stop_loss_buffer_type,
            old_version.stop_entry_buffer = new_version.stop_entry_buffer,
            old_version.stop_entry_buffer_type = new_version.stop_entry_buffer_type,
            old_version.binary_type = new_version.binary_type,
            old_version.tenor = new_version.tenor,
            old_version.instrument_code = new_version.instrument_code
      WHERE (nrg_common.has_value_changed(new_version.product_schema_code, old_version.product_schema_code) = 1 OR
             nrg_common.has_value_changed(new_version.trading_risk_schema_code, old_version.trading_risk_schema_code) = 1 OR
             nrg_common.has_value_changed(new_version.stop_loss_buffer, old_version.stop_loss_buffer) = 1 OR
             nrg_common.has_value_changed(new_version.stop_loss_buffer_type, old_version.stop_loss_buffer_type) = 1 OR
             nrg_common.has_value_changed(new_version.stop_entry_buffer, old_version.stop_entry_buffer) = 1 OR
             nrg_common.has_value_changed(new_version.stop_entry_buffer_type, old_version.stop_entry_buffer_type) = 1 OR
             nrg_common.has_value_changed(new_version.binary_type, old_version.binary_type) = 1 OR
             nrg_common.has_value_changed(new_version.tenor, old_version.tenor) = 1 OR
             nrg_common.has_value_changed('NO', old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(old_version.instrument_code, new_version.instrument_code) = 1
             )
        WHEN NOT MATCHED THEN
      INSERT ( product_setting_code
              ,logical_load_timestamp
              ,created_by
              ,create_timestamp
              ,updated_by
              ,update_timestamp
              ,effective_start_timestamp
              ,product_schema_code
              ,trading_risk_schema_code
              ,stop_loss_buffer
              ,stop_loss_buffer_type
              ,stop_entry_buffer
              ,stop_entry_buffer_type
              ,is_deleted
              ,binary_type
              ,tenor
              ,instrument_code)
      VALUES (new_version.product_setting_code
             ,lv_logical_load_timestamp
             ,p_user
             ,SYSTIMESTAMP
             ,p_user
             ,SYSTIMESTAMP
             ,p_effective_start_timestamp
             ,new_version.product_schema_code
             ,new_version.trading_risk_schema_code
             ,new_version.stop_loss_buffer
             ,new_version.stop_loss_buffer_type
             ,new_version.stop_entry_buffer
             ,new_version.stop_entry_buffer_type
             ,'NO'
             ,new_version.binary_type
             ,new_version.tenor
             ,new_version.instrument_code);



    --
    --Margin Tiers on product settings
    --

    MERGE INTO product_settings_margin_defs_h old_version
    USING (SELECT old_version.*
      FROM Product_Settings_Margin_Defs old_version,
         (SELECT main_ps.product_setting_code product_setting_code,
                 --nvl(nv.product_schema_code,'NA') product_schema_code,
                 --nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                 'NA' trading_account_ccy,
                 'NO' is_deleted,
                 nv.tier1_boundary tier1_boundary,
                 nv.tier1_rate tier1_rate,
                 nv.tier2_boundary tier2_boundary,
                 nv.tier2_rate tier2_rate,
                 nv.tier3_boundary tier3_boundary,
                 nv.tier3_rate tier3_rate,
                 nv.tier4_boundary tier4_boundary,
                 nv.tier4_rate tier4_rate,
                 nv.tier5_boundary tier5_boundary,
                 nv.tier5_rate tier5_rate,
                 nvl(nv.instrument_schema_code,'Default') instrument_schema_code
          FROM
          TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_cfd) nv
          WHERE main_ps.product_setting_code=nv.product_setting_code
          AND main_ps.margin_def_ng_cfd IS NOT NULL
          UNION ALL
           SELECT main_ps.product_setting_code product_setting_code,
                 --nvl(nv.product_schema_code,'NA') product_schema_code,
                 --nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                 nvl(nv.currency,'NA') trading_account_ccy,
                 'NO' is_deleted,
                 nv.tier1_boundary tier1_boundary,
                 nv.tier1_rate tier1_rate,
                 nv.tier2_boundary tier2_boundary,
                 nv.tier2_rate tier2_rate,
                 nv.tier3_boundary tier3_boundary,
                 nv.tier3_rate tier3_rate,
                 nv.tier4_boundary tier4_boundary,
                 nv.tier4_rate tier4_rate,
                 nv.tier5_boundary tier5_boundary,
                 nv.tier5_rate tier5_rate,
                 nvl(nv.instrument_schema_code, 'Default') instrument_schema_code
          FROM
          TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_sb) nv
          WHERE main_ps.product_setting_code = nv.product_setting_code
          AND main_ps.margin_def_ng_sb IS NOT NULL) new_version
    WHERE old_version.product_setting_code = new_version.product_setting_code(+) AND
          --old_version.product_schema_code = new_version.product_schema_code(+) AND
          --old_version.trading_risk_schema_code = new_version.trading_risk_schema_code(+) AND
          old_version.instrument_schema_code = new_version.instrument_schema_code (+) AND
          old_version.trading_account_ccy = new_version.trading_account_ccy AND
          old_version.is_deleted = 'NO' AND
          (nrg_common.has_value_changed(old_version.tier1_boundary, new_version.tier1_boundary) = 1 OR
           nrg_common.has_value_changed(old_version.tier1_rate, new_version.tier1_rate) = 1 OR
           nrg_common.has_value_changed(old_version.tier2_boundary, new_version.tier2_boundary) = 1 OR
           nrg_common.has_value_changed(old_version.tier2_rate, new_version.tier2_rate) = 1 OR
           nrg_common.has_value_changed(old_version.tier3_boundary, new_version.tier3_boundary) = 1 OR
           nrg_common.has_value_changed(old_version.tier3_rate, new_version.tier3_rate) = 1 OR
           nrg_common.has_value_changed(old_version.tier4_boundary, new_version.tier4_boundary) = 1 OR
           nrg_common.has_value_changed(old_version.tier4_rate, new_version.tier4_rate) = 1 OR
           nrg_common.has_value_changed(old_version.tier5_boundary, new_version.tier5_boundary) = 1 OR
           nrg_common.has_value_changed(old_version.tier5_rate, new_version.tier5_rate) = 1 )) new_version
      ON (old_version.product_setting_code = new_version.product_setting_code AND
          --old_version.product_schema_code = new_version.product_schema_code AND
          --old_version.trading_risk_schema_code = new_version.trading_risk_schema_code AND
          old_version.trading_account_ccy = new_version.trading_account_ccy AND
          old_version.effective_start_timestamp = new_version.effective_start_timestamp AND
          old_version.instrument_schema_code = new_version.instrument_schema_code)
       WHEN MATCHED THEN UPDATE
                SET
            old_version.logical_load_timestamp = new_version.logical_load_timestamp,
            old_version.created_by = new_version.created_by,
            old_version.create_timestamp = new_version.create_timestamp,
            old_version.updated_by = new_version.updated_by,
            old_version.update_timestamp = new_version.update_timestamp,
            old_version.effective_end_timestamp = p_effective_start_timestamp,
            old_version.action = 'U',
            old_version.action_timestamp = SYSTIMESTAMP,
            old_version.tier1_boundary = new_version.tier1_boundary,
            old_version.tier1_rate = new_version.tier1_rate,
            old_version.tier2_boundary = new_version.tier2_boundary,
            old_version.tier2_rate = new_version.tier2_rate,
            old_version.tier3_boundary = new_version.tier3_boundary,
            old_version.tier3_rate = new_version.tier3_rate,
            old_version.tier4_boundary = new_version.tier4_boundary,
            old_version.tier4_rate = new_version.tier4_rate,
            old_version.tier5_boundary = new_version.tier5_boundary,
            old_version.tier5_rate = new_version.tier5_rate
        WHEN NOT MATCHED THEN
                  INSERT
                        (product_setting_code,
                         logical_load_timestamp,
                         created_by,
                         create_timestamp,
                         updated_by,
                         update_timestamp,
                         effective_start_timestamp,
                         effective_end_timestamp,
                         action,
                         action_timestamp,
                         --product_schema_code,
                         --trading_risk_schema_code,
                         trading_account_ccy,
                         tier1_boundary,
                         tier1_rate,
                         tier2_boundary,
                         tier2_rate,
                         tier3_boundary,
                         tier3_rate,
                         tier4_boundary,
                         tier4_rate,
                         tier5_boundary,
                         tier5_rate,
                         is_deleted,
                         instrument_schema_code)
                  VALUES(new_version.product_setting_code,
                         new_version.logical_load_timestamp,
                         new_version.created_by,
                         new_version.create_timestamp,
                         new_version.updated_by,
                         new_version.update_timestamp,
                         new_version.effective_start_timestamp,
                         p_effective_start_timestamp,
                         'U',
                         SYSTIMESTAMP,
                         --new_version.product_schema_code,
                         --new_version.trading_risk_schema_code,
                         new_version.trading_account_ccy,
                         new_version.tier1_boundary,
                         new_version.tier1_rate,
                         new_version.tier2_boundary,
                         new_version.tier2_rate,
                         new_version.tier3_boundary,
                         new_version.tier3_rate,
                         new_version.tier4_boundary,
                         new_version.tier4_rate,
                         new_version.tier5_boundary,
                         new_version.tier5_rate,
                         new_version.is_deleted,
                         new_version.instrument_schema_code);

    --
    --Product Setting Margin Tiers
    --
  UPDATE
   (SELECT p.* /*+ index(p,PRODUCT_SETTINGS_MARGIN_DE_PK)*/ FROM product_settings_margin_defs p
    WHERE NOT EXISTS (SELECT 1 FROM
                        (SELECT main_ps.product_setting_code product_setting_code,
                                       --nvl(nv.product_schema_code,'NA') product_schema_code,
                                       --nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                                       nvl(nv.instrument_schema_code, 'Default') instrument_schema_code,
                                       'NA' trading_account_ccy,
                                       'NO' is_deleted
                                FROM
                                TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_cfd)nv
                                WHERE  main_ps.product_setting_code=nv.product_setting_code
                                  AND main_ps.margin_def_ng_cfd IS NOT NULL
                                UNION ALL
                                 SELECT DISTINCT main_ps.product_setting_code product_setting_code,
                                       --nvl(nv.product_schema_code,'NA') product_schema_code,
                                       --nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                                       nvl(nv.instrument_schema_code, 'Default') instrument_schema_code,
                                       nvl(nv.currency,'NA') trading_account_ccy,
                                       'NO' is_deleted
                                FROM
                                TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_sb)nv
                                WHERE main_ps.product_setting_code=nv.product_setting_code
                                AND main_ps.margin_def_ng_sb IS NOT NULL) x
                        WHERE p.Product_Setting_Code=x.Product_Setting_Code
                          --AND p.product_schema_code=x.product_schema_code
                          --AND p.trading_risk_schema_code=x.trading_risk_schema_code
                          AND p.instrument_schema_code=x.instrument_schema_code
                          AND p.trading_account_ccy=x.trading_account_ccy
                          AND p.Is_Deleted=x.is_deleted)) t
    SET t.is_deleted = 'YES',
        t.logical_load_timestamp = lv_logical_load_timestamp
    WHERE t.is_deleted = 'NO';

   /* UPDATE product_settings_margin_defs
    SET is_deleted = 'YES',
        logical_load_timestamp = lv_logical_load_timestamp
    WHERE (product_setting_code, product_schema_code, trading_risk_schema_code, trading_account_ccy, is_deleted) NOT IN
                               (SELECT main_ps.product_setting_code product_setting_code,
                                       nvl(nv.product_schema_code,'NA') product_schema_code,
                                       nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                                       'NA' trading_account_ccy,
                                       'NO' is_deleted
                                FROM
                                TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_cfd)nv
                                WHERE  main_ps.product_setting_code=nv.product_setting_code
                                  AND main_ps.margin_def_ng_cfd IS NOT NULL
                                UNION ALL
                                 SELECT DISTINCT main_ps.product_setting_code product_setting_code,
                                       nvl(nv.product_schema_code,'NA') product_schema_code,
                                       nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                                       nvl(nv.currency,'NA') trading_account_ccy,
                                       'NO' is_deleted
                                FROM
                                TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_sb)nv
                                WHERE main_ps.product_setting_code=nv.product_setting_code
                                AND main_ps.margin_def_ng_sb IS NOT NULL)
       AND is_deleted = 'NO';*/

    SELECT *
    BULK COLLECT INTO lv_del_prd_sttngs_margin
    FROM product_settings_margin_defs
    WHERE is_deleted = 'YES';


    FOR lv_cnt IN 1..lv_del_prd_sttngs_margin.COUNT LOOP
      put_history(p_old_margin_tiers => lv_del_prd_sttngs_margin(lv_cnt),
                  p_effective_end_timestamp => p_effective_start_timestamp,
                  p_action => 'D');
    END LOOP;

    DELETE product_settings_margin_defs
    WHERE is_deleted = 'YES';

    MERGE INTO product_settings_margin_defs old_version
    USING (SELECT main_ps.product_setting_code product_setting_code,
                 --nvl(nv.product_schema_code,'NA') product_schema_code,
                 --nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                 'NA' trading_account_ccy,
                 'NO' is_deleted,
                 nv.tier1_boundary tier1_boundary,
                 nv.tier1_rate tier1_rate,
                 nv.tier2_boundary tier2_boundary,
                 nv.tier2_rate tier2_rate,
                 nv.tier3_boundary tier3_boundary,
                 nv.tier3_rate tier3_rate,
                 nv.tier4_boundary tier4_boundary,
                 nv.tier4_rate tier4_rate,
                 nv.tier5_boundary tier5_boundary,
                 nv.tier5_rate tier5_rate,
                 nvl(nv.instrument_schema_code, 'Default') instrument_schema_code
          FROM
          TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_cfd)nv
          WHERE main_ps.product_setting_code=nv.product_setting_code
          AND main_ps.margin_def_ng_cfd IS NOT NULL
          UNION ALL
           SELECT main_ps.product_setting_code product_setting_code,
                 --nvl(nv.product_schema_code,'NA') product_schema_code,
                 --nvl(nv.trading_risk_schema_code,'NA') trading_risk_schema_code,
                 nvl(nv.currency,'NA') trading_account_ccy,
                 'NO' is_deleted,
                 nv.tier1_boundary tier1_boundary,
                 nv.tier1_rate tier1_rate,
                 nv.tier2_boundary tier2_boundary,
                 nv.tier2_rate tier2_rate,
                 nv.tier3_boundary tier3_boundary,
                 nv.tier3_rate tier3_rate,
                 nv.tier4_boundary tier4_boundary,
                 nv.tier4_rate tier4_rate,
                 nv.tier5_boundary tier5_boundary,
                 nv.tier5_rate tier5_rate,
                 nvl(nv.instrument_schema_code, 'Default') instrument_schema_code
          FROM
          TABLE(CAST(p_product_settings AS product_settings_tab)) main_ps, TABLE(main_ps.margin_def_ng_sb)nv
          WHERE main_ps.product_setting_code=nv.product_setting_code
          AND main_ps.margin_def_ng_sb IS NOT NULL ) new_version
    ON(old_version.product_setting_code = new_version.product_setting_code AND
       --old_version.product_schema_code = new_version.product_schema_code AND
       --old_version.trading_risk_schema_code = new_version.trading_risk_schema_code AND
       old_version.trading_account_ccy = new_version.trading_account_ccy AND
       old_version.instrument_schema_code = new_version.instrument_schema_code)
    WHEN MATCHED THEN
      UPDATE
      SET old_version.logical_load_timestamp = lv_logical_load_timestamp,
          old_version.updated_by = p_user,
          old_version.update_timestamp = SYSTIMESTAMP,
          old_version.effective_start_timestamp = p_effective_start_timestamp,
          old_version.is_deleted = new_version.is_deleted,
          old_version.tier1_boundary = new_version.tier1_boundary,
          old_version.tier1_rate = new_version.tier1_rate,
          old_version.tier2_boundary = new_version.tier2_boundary,
          old_version.tier2_rate = new_version.tier2_rate,
          old_version.tier3_boundary = new_version.tier3_boundary,
          old_version.tier3_rate = new_version.tier3_rate,
          old_version.tier4_boundary = new_version.tier4_boundary,
          old_version.tier4_rate = new_version.tier4_rate,
          old_version.tier5_boundary = new_version.tier5_boundary,
          old_version.tier5_rate = new_version.tier5_rate
       WHERE (nrg_common.has_value_changed(old_version.tier1_boundary, new_version.tier1_boundary) = 1 OR
             nrg_common.has_value_changed(old_version.tier1_rate, new_version.tier1_rate) = 1 OR
             nrg_common.has_value_changed(old_version.tier2_boundary, new_version.tier2_boundary) = 1 OR
             nrg_common.has_value_changed(old_version.tier2_rate, new_version.tier2_rate) = 1 OR
             nrg_common.has_value_changed(old_version.tier3_boundary, new_version.tier3_boundary) = 1 OR
             nrg_common.has_value_changed(old_version.tier3_rate, new_version.tier3_rate) = 1 OR
             nrg_common.has_value_changed(old_version.tier4_boundary, new_version.tier4_boundary) = 1 OR
             nrg_common.has_value_changed(old_version.tier4_rate, new_version.tier4_rate) = 1 OR
             nrg_common.has_value_changed(old_version.tier5_boundary, new_version.tier5_boundary) = 1 OR
             nrg_common.has_value_changed(old_version.tier5_rate, new_version.tier5_rate) = 1 OR
             nrg_common.has_value_changed(old_version.Is_Deleted, new_version.is_deleted) = 1
             )
    WHEN NOT MATCHED THEN
      INSERT (old_version.product_setting_code,
              --old_version.product_schema_code,
              --old_version.trading_risk_schema_code,
              old_version.trading_account_ccy,
              old_version.is_deleted,
              old_version.logical_load_timestamp,
              old_version.created_by,
              old_version.create_timestamp,
              old_version.updated_by,
              old_version.update_timestamp,
              old_version.effective_start_timestamp,
              old_version.tier1_boundary,
              old_version.tier1_rate,
              old_version.tier2_boundary,
              old_version.tier2_rate,
              old_version.tier3_boundary,
              old_version.tier3_rate,
              old_version.tier4_boundary,
              old_version.tier4_rate,
              old_version.tier5_boundary,
              old_version.tier5_rate,
              old_version.instrument_schema_code)
      VALUES (new_version.product_setting_code,
              --nvl(new_version.product_schema_code,'NA'),
              --nvl(new_version.trading_risk_schema_code,'NA'),
              nvl(new_version.trading_account_ccy,'NA'),
              new_version.is_deleted,
              lv_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              new_version.tier1_boundary,
              new_version.tier1_rate,
              new_version.tier2_boundary,
              new_version.tier2_rate,
              new_version.tier3_boundary,
              new_version.tier3_rate,
              new_version.tier4_boundary,
              new_version.tier4_rate,
              new_version.tier5_boundary,
              new_version.tier5_rate,
              new_version.instrument_schema_code);


    END put_product_settings;
    -- ===================================================================================
    -- put_product_set
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --     INSTRUMENTS
    --     PRODUCT_WRAPPERS
    --     REGIONS
    --     COUNTRIES
    --     MM_PRODUCTS
    --     PRODUCTS_H
    --     MM_PRODUCTS_H
    --     INSTRUMENRS_H
    --     REGIONS_H
    --
    --  Products data is always written in sets and hence it is different from all the other NRG packages like NRG_TRADES
    --  To ensure the referntial integrity we write the data for each of the set in a sequence. The sequence of writes is:
    --  1. REGIONS
    --  2. COUNTRIES
    --  3. INSTRUMENTS
    --  4. PRODUCT_WRAPPERS
    --  5. PRODUCTS
    --  6. MM_PRODUCTS
    --
    --  From NRG we get only the updated data set each time a new product snapshot is created by PMS. Also the freequency of updates
    --  to products is minimal.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_products                       Product Details
    --     p_product_wrappers               Product wrapper Details
    --     p_instruments                    Instrument Details
    --     p_regions                        Regions Details
    --     p_countries                      Countries Details
    --     p_inst_feed_setting
    --     p_reg_instr_identification
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_products_gsko  (p_user                            IN products.created_by%TYPE,
                                  p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                  p_products_gsko                   IN products_gsko_tab)
    IS
    lv_logical_load_timestamp TIMESTAMP(6):= systimestamp;
    BEGIN


          MERGE INTO products_gsko_h old_version
            USING ( SELECT old_version.*
                    FROM products_gsko old_version,
                         (SELECT product_setting_code,
                                 product_setting_property,
                                 DECODE(instrument_schema_code,'InstrumentSchema','Default', instrument_schema_code) instrument_schema_code,
                                 instrument_schema_value
                         FROM
                         TABLE(CAST(p_products_gsko AS products_gsko_tab))) products_gsko
                    WHERE old_version.product_setting_code = products_gsko.product_setting_code(+) AND
                          old_version.product_setting_property = products_gsko.product_setting_property(+) AND
                          old_version.instrument_schema_code = products_gsko.instrument_schema_code(+) AND
                          (--nrg_common.has_value_changed(upper(old_version.trading_risk_schema_value),upper(products_gsko.trading_risk_schema_value))= 1 OR
                           nrg_common.has_value_changed(upper(old_version.instrument_schema_value), upper(products_gsko.instrument_schema_value)) = 1) AND
                          old_version.Is_Deleted='NO' ) new_version
           ON (old_version.product_setting_code      = new_version.product_setting_code AND
               old_version.product_setting_property  = new_version.product_setting_property AND
               old_version.instrument_schema_code  = new_version.instrument_schema_code AND
               old_version.effective_start_timestamp = new_version.effective_start_timestamp )
          WHEN MATCHED THEN UPDATE
                SET old_version.updated_by            = new_version.updated_by,
                    old_version.action                    = 'U',
                    old_version.action_timestamp          = SYSTIMESTAMP,
                    old_version.update_timestamp          = new_version.update_timestamp,
                    old_version.logical_load_timestamp    = new_version.logical_load_timestamp,
                    --old_version.trading_risk_schema_value = new_version.trading_risk_schema_value,
                    old_version.is_deleted                = new_version.is_deleted,
                    old_version.instrument_schema_value   = UPPER(new_version.instrument_schema_value)
          WHEN NOT MATCHED THEN INSERT
               (product_setting_code,
                product_setting_property,
                --trading_risk_schema_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                effective_end_timestamp,
                action,
                action_timestamp,
                --trading_risk_schema_value,
                is_deleted,
                instrument_schema_code,
                instrument_schema_value)
              VALUES
              (new_version.product_setting_code,
               new_version.product_setting_property,
               --new_version.trading_risk_schema_code,
               new_version.logical_load_timestamp,
               new_version.created_by,
               new_version.create_timestamp,
               new_version.updated_by,
               new_version.update_timestamp,
               new_version.effective_start_timestamp,
               p_effective_start_timestamp,
               'U',
               SYSTIMESTAMP,
               --new_version.trading_risk_schema_value,
               new_version.is_deleted,
               new_version.instrument_schema_code,
               UPPER(new_version.instrument_schema_value)
              );
        --
        --PRODUCTS_GSKO Soft Delete The Deleted Rows
        --

        UPDATE products_gsko old_version
        SET is_deleted = 'YES',
            logical_load_timestamp = lv_logical_load_timestamp
        WHERE NOT EXISTS (SELECT 1
                          FROM TABLE(CAST(p_products_gsko AS products_gsko_tab))new_version
                          WHERE new_version.product_setting_code = old_version.product_setting_code  AND
                                new_version.product_setting_property = old_version.product_setting_property AND
                                DECODE(new_version.instrument_schema_code,'InstrumentSchema','Default', new_version.instrument_schema_code) = old_version.instrument_schema_code)
           AND is_deleted = 'NO';

        --
        --Insert and update data
        --

        MERGE INTO products_gsko old_version
        USING (SELECT product_setting_code,
                      product_setting_property,
                      DECODE(instrument_schema_code,'InstrumentSchema','Default', instrument_schema_code) instrument_schema_code,
                      instrument_schema_value
               FROM TABLE(CAST(p_products_gsko AS products_gsko_tab))) new_version
        ON (new_version.product_setting_code = old_version.product_setting_code  AND
            new_version.product_setting_property = old_version.product_setting_property AND
            new_version.instrument_schema_code = old_version.instrument_schema_code
            )
        WHEN MATCHED THEN
          UPDATE
          SET   --old_version.trading_risk_schema_value = upper(new_version.trading_risk_schema_value),
                old_version.logical_load_timestamp = lv_logical_load_timestamp,
                old_version.updated_by = p_user,
                old_version.update_timestamp = SYSTIMESTAMP,
                old_version.effective_start_timestamp = p_effective_start_timestamp,
                old_version.is_deleted = 'NO',
                old_version.instrument_schema_value = UPPER(new_version.instrument_schema_value)
          WHERE (nrg_common.has_value_changed(upper(new_version.instrument_schema_value), upper(old_version.instrument_schema_value)) = 1 OR
                 nrg_common.has_value_changed('NO', old_version.is_deleted) = 1)
        WHEN NOT MATCHED THEN
          INSERT (product_setting_code,
                  product_setting_property,
                  --trading_risk_schema_code,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  --trading_risk_schema_value,
                  is_deleted,
                  instrument_schema_code,
                  instrument_schema_value
                  )
          VALUES (new_version.product_setting_code
                 ,new_version.product_setting_property
                 --,new_version.trading_risk_schema_code
                 ,lv_logical_load_timestamp
                 ,p_user
                 ,SYSTIMESTAMP
                 ,p_user
                 ,SYSTIMESTAMP
                 ,p_effective_start_timestamp
                 --,upper(new_version.trading_risk_schema_value)
                 ,'NO',
                 new_version.instrument_schema_code,
                 UPPER(new_version.instrument_schema_value));


    END;
    -- ===================================================================================
    -- put_instruments
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --     PRODUCTS_H
    --
    --  From NRG we get only the updated data set each time a new product snapshot is created by PMS. Also the freequency of updates
    --  to products is minimal.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_products                       Product Details
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_instruments(p_user                            IN products.created_by%TYPE,
                              p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                              p_instruments                     IN instrument_tab)
                              --p_inst_feed_setting               IN instrument_feed_setting_tab)
    IS
    lv_logical_load_timestamp TIMESTAMP(6):= systimestamp;
    BEGIN

    --
    -- Select the instruments that have got updated
    --

    MERGE INTO instruments_h old_version
    USING (SELECT inst.*
    FROM instruments inst, TABLE(CAST(p_instruments AS instrument_tab)) new_inst
    WHERE inst.instrument_code = new_inst.inst_code (+) AND
          (nrg_common.has_value_changed(inst.country_code,new_inst.country_code)= 1 OR
           nrg_common.has_value_changed(inst.feed_symbol,new_inst.feed_symbol)= 1 OR
           nrg_common.has_value_changed(inst.instrument_type,new_inst.instrument_type)= 1 OR
           nrg_common.has_value_changed(inst.short_name,new_inst.short_name)= 1 OR
           nrg_common.has_value_changed(inst.isin,new_inst.isin)= 1 OR
           nrg_common.has_value_changed(inst.mic,new_inst.mic)= 1 OR
           nrg_common.has_value_changed(inst.ric,new_inst.ric)= 1 OR
           nrg_common.has_value_changed(inst.currency, new_inst.currency) = 1 OR
           nrg_common.has_value_changed(inst.pair_currency,new_inst.pair_currency)= 1 OR
           nrg_common.has_value_changed(inst.company_sector_name,new_inst.company_sector_name)= 1 OR
           nrg_common.has_value_changed(inst.commodity_type_name,new_inst.commodity_type_name)= 1 OR
           nrg_common.has_value_changed(inst.country_class_name,new_inst.country_class_name)= 1 OR
           nrg_common.has_value_changed(inst.is_deleted,new_inst.is_deleted)= 1 OR
           nrg_common.has_value_changed(inst.mmuid,new_inst.mmuid)= 1 OR
           nrg_common.has_value_changed(inst.mmuid2,new_inst.mmuid2)= 1 OR
           nrg_common.has_value_changed(inst.mmvalueid,new_inst.mmvalueid)= 1 OR
           nrg_common.has_value_changed(inst.prophet_banding_algorithm,new_inst.prophet_banding_algorithm)= 1 OR
           nrg_common.has_value_changed(inst.prophet_banding_fixedspread,new_inst.prophet_banding_fixedspread)= 1 OR
           nrg_common.has_value_changed(inst.prophet_banding_spreadfactor,new_inst.prophet_banding_spreadfactor)= 1 OR
           nrg_common.has_value_changed(inst.mm_inst_id_cfd, new_inst.mm_inst_id_cfd) = 1 OR
           nrg_common.has_value_changed(inst.mm_inst_id_sb_ir, new_inst.mm_inst_id_sb_ir) = 1 OR
           nrg_common.has_value_changed(inst.mm_inst_id_sb_uk, new_inst.mm_inst_id_sb_uk) = 1 OR
           nrg_common.has_value_changed(inst.pricing_start_date, new_inst.pricing_start_date) = 1 OR
           nrg_common.has_value_changed(inst.first_trading_date, new_inst.first_trading_date) = 1 OR
           nrg_common.has_value_changed(inst.last_rollover_date, new_inst.last_rollover_date) = 1 OR
           nrg_common.has_value_changed(inst.automatic_rollover_date, new_inst.automatic_rollover_date) = 1 OR
           nrg_common.has_value_changed(inst.last_trading_date, new_inst.last_trading_date) = 1 OR
           nrg_common.has_value_changed(inst.cash_settlement_date, new_inst.cash_settlement_date) = 1 OR
           nrg_common.has_value_changed(inst.expiry_date, new_inst.expiry_date) = 1 OR
           nrg_common.has_value_changed(inst.is_skip_rollover, new_inst.is_skip_rollover) = 1 OR
           nrg_common.has_value_changed(inst.last_trading_date_desc, new_inst.last_trading_date_desc) = 1 OR
           nrg_common.has_value_changed(inst.last_settlement_date_desc, new_inst.last_settlement_date_desc) = 1 OR
           nrg_common.has_value_changed(inst.last_rollover_date_desc, new_inst.last_rollover_date_desc) = 1 OR
           nrg_common.has_value_changed(inst.contract_code, new_inst.contract_code) = 1 OR
           nrg_common.has_value_changed(inst.rollover_target_code, new_inst.rollover_target_code) = 1 OR
           nrg_common.has_value_changed(inst.cmc_cash_instrument_code, new_inst.cmc_cash_instrument_code) = 1 OR
           nrg_common.has_value_changed(inst.financial_instrument_type, new_inst.financial_instrument_type) = 1 OR
           nrg_common.has_value_changed(inst.exchange_product_code, new_inst.exchange_product_code) = 1 OR
           nrg_common.has_value_changed(inst.is_prr_qualifying_index, new_inst.is_prr_qualifying_index) = 1 OR
           nrg_common.has_value_changed(inst.position_risk_requirement_pct, new_inst.position_risk_requirement_pct) = 1 OR
           nrg_common.has_value_changed(inst.Security_Typ, new_inst.Security_Typ) = 1 OR
           nrg_common.has_value_changed(inst.risk_country_code, new_inst.risk_country_code) = 1 OR
           nrg_common.has_value_changed(inst.tax_country_code, new_inst.tax_country_code) = 1 OR
           nrg_common.has_value_changed(inst.bloomberg_code, new_inst.bloomberg_code) = 1 OR
           nrg_common.has_value_changed(inst.sedol, new_inst.sedol) = 1 OR
           nrg_common.has_value_changed(inst.reuters_mic, new_inst.reuters_mic) = 1 OR
           nrg_common.has_value_changed(inst.operating_mic, new_inst.operating_mic) = 1 OR
           nrg_common.has_value_changed(inst.main_exchange, new_inst.main_exchange) = 1 OR
           nrg_common.has_value_changed(inst.market_status, new_inst.market_status) = 1 OR
           nrg_common.has_value_changed(inst.cur_mkt_cap_usd, new_inst.cur_mkt_cap_usd) = 1 OR
           nrg_common.has_value_changed(inst.avg_daily_value_traded_30d_usd, new_inst.avg_daily_value_traded_30d_usd) = 1 OR
           nrg_common.has_value_changed(inst.avg_daily_value_traded_3m_usd, new_inst.avg_daily_value_traded_3m_usd) = 1 OR
           nrg_common.has_value_changed(inst.eqy_free_float_pct, new_inst.eqy_free_float_pct) = 1 OR
           nrg_common.has_value_changed(inst.primary_inst_code, new_inst.primary_inst_code) = 1 OR
           nrg_common.has_value_changed(inst.coupon_rate, new_inst.coupon_rate) = 1 OR
           nrg_common.has_value_changed(inst.cheapest_to_deliver_date, new_inst.cheapest_to_deliver_date) = 1 OR
           nrg_common.has_value_changed(inst.market_code, new_inst.market_code) = 1 OR
           nrg_common.has_value_changed(inst.market_alias, new_inst.market_alias) = 1 OR
           nrg_common.has_value_changed(inst.market_data_mapping_code, new_inst.market_data_mapping_code) = 1 OR
           nrg_common.has_value_changed(inst.cfd_cfi, new_inst.cfd_cfi) = 1 OR
           nrg_common.has_value_changed(inst.sb_cfi, new_inst.sb_cfi) = 1 OR
           nrg_common.has_value_changed(inst.is_mifid_ii_reportable, new_inst.is_mifid_ii_reportable) = 1 OR
           nrg_common.has_value_changed(inst.security_description, new_inst.security_description) = 1 OR
           nrg_common.has_value_changed(inst.pht_rsk_brkr_mrgn_amnt, new_inst.pht_rsk_brkr_mrgn_amnt) = 1 OR
           nrg_common.has_value_changed(inst.pht_rsk_brkr_mrgn_prcnt, new_inst.pht_rsk_brkr_mrgn_prcnt) = 1 OR
           nrg_common.has_value_changed(inst.pht_rsk_brkr_mrgn_tier, new_inst.pht_rsk_brkr_mrgn_tier) = 1 OR
           nrg_common.has_value_changed(inst.pht_rsk_brkr_mrgn_tier_fxdb, new_inst.pht_rsk_brkr_mrgn_tier_fxdb) = 1 OR
           nrg_common.has_value_changed(inst.pht_rsk_brkr_mrgn_tier_fxubs, new_inst.pht_rsk_brkr_mrgn_tier_fxubs) = 1 OR
           nrg_common.has_value_changed(inst.underlying_instrument_code, new_inst.underlying_instrument_code) = 1 OR
           nrg_common.has_value_changed(inst.underlying_instrument_alias, new_inst.underlying_instrument_alias) = 1 OR
           nrg_common.has_value_changed(inst.is_ccy_in_frctnl_prts, new_inst.is_ccy_in_frctnl_prts) = 1 OR
           nrg_common.has_value_changed(inst.is_pair_ccy_in_frctnl_prts, new_inst.is_pair_ccy_in_frctnl_prts) = 1 OR
           nrg_common.has_value_changed(inst.point_multiplier, new_inst.point_multiplier) = 1 OR
           nrg_common.has_value_changed(inst.financing_currency, new_inst.financing_currency) = 1 OR
           nrg_common.has_value_changed(inst.im_is_test_instrument, new_inst.im_is_test_instrument) = 1 OR
           nrg_common.has_value_changed(inst.im_is_demo_only, new_inst.im_is_demo_only) = 1 OR
           nrg_common.has_value_changed(inst.is_fixed_spread_instrument, new_inst.is_fixed_spread_instrument) = 1 OR
           nrg_common.has_value_changed(inst.is_guaranteed_stop_instrmnt, new_inst.is_guaranteed_stop_instrmnt) = 1 OR
           nrg_common.has_value_changed(inst.is_metatrader_instrument, new_inst.is_metatrader_instrument) = 1 OR
           nrg_common.has_value_changed(inst.is_esma_duplicate, new_inst.is_esma_duplicate) = 1 OR
           nrg_common.has_value_changed(inst.is_findable_instrument, new_inst.is_findable_instrument) = 1 OR
           nrg_common.has_value_changed(inst.is_tradable_instrument, new_inst.is_tradable_instrument) = 1 OR
           nrg_common.has_value_changed(inst.api_code, new_inst.api_code) = 1
           )) new_version
           ON (old_version.instrument_code           = new_version.instrument_code AND
               old_version.effective_start_timestamp = new_version.effective_start_timestamp)
          WHEN MATCHED THEN UPDATE
                SET old_version.updated_by                    = new_version.updated_by,
                    old_version.action                        = 'U',
                    old_version.action_timestamp              = SYSTIMESTAMP,
                    old_version.update_timestamp              = new_version.update_timestamp,
                    old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
                    old_version.country_code                  = new_version.country_code,
                    old_version.feed_symbol                   = new_version.feed_symbol,
                    old_version.instrument_type               = new_version.instrument_type,
                    old_version.short_name                    = new_version.short_name,
                    old_version.isin                          = new_version.isin,
                    old_version.mic                           = new_version.mic,
                    old_version.ric                           = new_version.ric,
                    old_version.currency                      = new_version.currency,
                    old_version.pair_currency                 = new_version.pair_currency,
                    old_version.company_sector_name           = new_version.company_sector_name,
                    old_version.commodity_type_name           = new_version.commodity_type_name,
                    old_version.country_class_name            = new_version.country_class_name,
                    old_version.is_deleted                    = new_version.is_deleted,
                    old_version.mmuid                         = new_version.mmuid,
                    old_version.mmuid2                        = new_version.mmuid2,
                    old_version.mmvalueid                     = new_version.mmvalueid,
                    old_version.prophet_banding_algorithm     = new_version.prophet_banding_algorithm,
                    old_version.prophet_banding_fixedspread   = new_version.prophet_banding_fixedspread,
                    old_version.prophet_banding_spreadfactor  = new_version.prophet_banding_spreadfactor,
                    old_version.mm_inst_id_cfd                = new_version.mm_inst_id_cfd,
                    old_version.mm_inst_id_sb_ir              = new_version.mm_inst_id_sb_ir,
                    old_version.mm_inst_id_sb_uk              = new_version.mm_inst_id_sb_uk,
                    old_version.pricing_start_date            = new_version.pricing_start_date,
                    old_version.first_trading_date            = new_version.first_trading_date,
                    old_version.last_rollover_date            = new_version.last_rollover_date,
                    old_version.automatic_rollover_date       = new_version.automatic_rollover_date,
                    old_version.last_trading_date             = new_version.last_trading_date,
                    old_version.cash_settlement_date          = new_version.cash_settlement_date,
                    old_version.expiry_date                   = new_version.expiry_date,
                    old_version.is_skip_rollover              = new_version.is_skip_rollover,
                    old_version.last_trading_date_desc        = new_version.last_trading_date_desc,
                    old_version.last_settlement_date_desc     = new_version.last_settlement_date_desc,
                    old_version.last_rollover_date_desc       = new_version.last_rollover_date_desc,
                    old_version.contract_code                 = new_version.contract_code,
                    old_version.rollover_target_code          = new_version.rollover_target_code,
                    old_version.cmc_cash_instrument_code      = new_version.cmc_cash_instrument_code,
                    old_version.financial_instrument_type     = new_version.financial_instrument_type,
                    old_version.derived_cash_instrument_pms   = new_version.derived_cash_instrument_pms,
                    old_version.is_prr_qualifying_index       = new_version.is_prr_qualifying_index,
                    old_version.position_risk_requirement_pct = new_version.position_risk_requirement_pct,
                    old_version.Security_Typ                  = new_version.Security_Typ,
                    old_version.contract_size_override        = new_version.contract_size_override,
                    old_version.commodity_base_name           = new_version.commodity_base_name,
                    old_version.commodity_detail_name         = new_version.commodity_detail_name,
                    old_version.instrument_type_code          = new_version.instrument_type_code,
                    old_version.risk_country_code             = new_version.risk_country_code,
                    old_version.tax_country_code              = new_version.tax_country_code,
                    old_version.bloomberg_code                = new_version.bloomberg_code,
                    old_version.sedol                         = new_version.sedol,
                    old_version.reuters_mic                   = new_version.reuters_mic,
                    old_version.operating_mic                 = new_version.operating_mic,
                    old_version.main_exchange                 = new_version.main_exchange,
                    old_version.market_status                 = new_version.market_status,
                    old_version.cur_mkt_cap_usd               = new_version.cur_mkt_cap_usd,
                    old_version.avg_daily_value_traded_30d_usd= new_version.avg_daily_value_traded_30d_usd,
                    old_version.avg_daily_value_traded_3m_usd = new_version.avg_daily_value_traded_3m_usd,
                    old_version.eqy_free_float_pct            = new_version.eqy_free_float_pct,
                    old_version.primary_inst_code             = new_version.primary_inst_code,
                    old_version.coupon_rate                   = new_version.coupon_rate,
                    old_version.cheapest_to_deliver_date      = new_version.cheapest_to_deliver_date,
                    old_version.market_code                   = new_version.market_code,
                    old_version.market_alias                  = new_version.market_alias,
                    old_version.market_data_mapping_code      = new_version.market_data_mapping_code,
                    old_version.cfd_cfi                       = new_version.cfd_cfi,
                    old_version.sb_cfi                        = new_version.sb_cfi,
                    old_version.is_mifid_ii_reportable        = new_version.is_mifid_ii_reportable,
                    old_version.security_description          = new_version.security_description,
                    old_version.pht_rsk_brkr_mrgn_amnt        = new_version.pht_rsk_brkr_mrgn_amnt,
                    old_version.pht_rsk_brkr_mrgn_prcnt       = new_version.pht_rsk_brkr_mrgn_prcnt,
                    old_version.pht_rsk_brkr_mrgn_tier        = new_version.pht_rsk_brkr_mrgn_tier,
                    old_version.pht_rsk_brkr_mrgn_tier_fxdb   = new_version.pht_rsk_brkr_mrgn_tier_fxdb,
                    old_version.pht_rsk_brkr_mrgn_tier_fxubs  = new_version.pht_rsk_brkr_mrgn_tier_fxubs,
                    old_version.underlying_instrument_code    = new_version.underlying_instrument_code,
                    old_version.underlying_instrument_alias   = new_version.underlying_instrument_alias,
                    old_version.is_ccy_in_frctnl_prts         = new_version.is_ccy_in_frctnl_prts,
                    old_version.is_pair_ccy_in_frctnl_prts    = new_version.is_pair_ccy_in_frctnl_prts,
                    old_version.point_multiplier              = new_version.point_multiplier,
                    old_version.financing_currency            = new_version.financing_currency,
                    old_version.im_is_test_instrument         = new_version.im_is_test_instrument,
                    old_version.im_is_demo_only               = new_version.im_is_demo_only,
                    old_version.is_fixed_spread_instrument    = new_version.is_fixed_spread_instrument,
                    old_version.is_guaranteed_stop_instrmnt   = new_version.is_guaranteed_stop_instrmnt,
                    old_version.is_metatrader_instrument      = new_version.is_metatrader_instrument,
                    old_version.is_esma_duplicate             = new_version.is_esma_duplicate,
                    old_version.is_findable_instrument        = new_version.is_findable_instrument,
                    old_version.is_tradable_instrument        = new_version.is_tradable_instrument,
                    old_version.api_code                      = new_version.api_code
           WHEN NOT MATCHED THEN
              INSERT (created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      logical_load_timestamp,
                      effective_start_timestamp,
                      effective_end_timestamp,
                      action,
                      action_timestamp,
                      instrument_code,
                      country_code,
                      feed_symbol,
                      instrument_type,
                      short_name,
                      isin,
                      mic,
                      ric,
                      currency,
                      pair_currency,
                      company_sector_name,
                      commodity_type_name,
                      country_class_name,
                      is_deleted,
                      mmuid,
                      mmuid2,
                      mmvalueid,
                      prophet_banding_algorithm,
                      prophet_banding_fixedspread,
                      prophet_banding_spreadfactor,
                      mm_inst_id_cfd,
                      mm_inst_id_sb_ir,
                      mm_inst_id_sb_uk,
                      pricing_start_date,
                      first_trading_date,
                      last_rollover_date,
                      automatic_rollover_date,
                      last_trading_date,
                      cash_settlement_date,
                      expiry_date,
                      is_skip_rollover,
                      last_trading_date_desc,
                      last_settlement_date_desc,
                      last_rollover_date_desc,
                      contract_code,
                      rollover_target_code,
                      cmc_cash_instrument_code,
                      financial_instrument_type,
                      derived_cash_instrument_pms,
                      is_prr_qualifying_index,
                      position_risk_requirement_pct,
                      Security_Typ,
                      contract_size_override,
                      commodity_base_name,
                      commodity_detail_name,
                      instrument_type_code,
                      risk_country_code,
                      tax_country_code,
                      bloomberg_code,
                      sedol,
                      reuters_mic,
                      operating_mic,
                      main_exchange,
                      market_status,
                      cur_mkt_cap_usd,
                      avg_daily_value_traded_30d_usd,
                      avg_daily_value_traded_3m_usd,
                      eqy_free_float_pct,
                      primary_inst_code,
                      coupon_rate,
                      cheapest_to_deliver_date,
                      market_code,
                      market_alias,
                      market_data_mapping_code,
                      cfd_cfi,
                      sb_cfi,
                      is_mifid_ii_reportable,
                      security_description,
                      pht_rsk_brkr_mrgn_amnt,
                      pht_rsk_brkr_mrgn_prcnt,
                      pht_rsk_brkr_mrgn_tier,
                      pht_rsk_brkr_mrgn_tier_fxdb,
                      pht_rsk_brkr_mrgn_tier_fxubs,
                      underlying_instrument_code,
                      underlying_instrument_alias,
                      is_ccy_in_frctnl_prts,
                      is_pair_ccy_in_frctnl_prts,
                      point_multiplier,
                      financing_currency,
                      im_is_test_instrument,
                      im_is_demo_only,
                      is_fixed_spread_instrument,
                      is_guaranteed_stop_instrmnt,
                      is_metatrader_instrument,
                      is_esma_duplicate,
                      is_findable_instrument,
                      is_tradable_instrument,
                      api_code
					  )
                 VALUES (
                          new_version.created_by,
                          new_version.create_timestamp,
                          new_version.updated_by,
                          new_version.update_timestamp,
                          new_version.logical_load_timestamp,
                          new_version.effective_start_timestamp,
                          p_effective_start_timestamp,
                          'U',
                          SYSTIMESTAMP,
                          new_version.instrument_code,
                          new_version.country_code,
                          new_version.feed_symbol,
                          new_version.instrument_type,
                          new_version.short_name,
                          new_version.isin,
                          new_version.mic,
                          new_version.ric,
                          new_version.currency,
                          new_version.pair_currency,
                          new_version.company_sector_name,
                          new_version.commodity_type_name,
                          new_version.country_class_name,
                          new_version.is_deleted,
                          new_version.mmuid,
                          new_version.mmuid2,
                          new_version.mmvalueid,
                          new_version.prophet_banding_algorithm,
                          new_version.prophet_banding_fixedspread,
                          new_version.prophet_banding_spreadfactor,
                          new_version.mm_inst_id_cfd,
                          new_version.mm_inst_id_sb_ir,
                          new_version.mm_inst_id_sb_uk,
                          new_version.pricing_start_date,
                          new_version.first_trading_date,
                          new_version.last_rollover_date,
                          new_version.automatic_rollover_date,
                          new_version.last_trading_date,
                          new_version.cash_settlement_date,
                          new_version.expiry_date,
                          new_version.is_skip_rollover,
                          new_version.last_trading_date_desc,
                          new_version.last_settlement_date_desc,
                          new_version.last_rollover_date_desc,
                          new_version.contract_code,
                          new_version.rollover_target_code,
                          new_version.cmc_cash_instrument_code,
                          new_version.financial_instrument_type,
                          new_version.derived_cash_instrument_pms,
                          new_version.is_prr_qualifying_index,
                          new_version.position_risk_requirement_pct,
                          new_version.Security_Typ,
                          new_version.contract_size_override,
                          new_version.commodity_base_name,
                          new_version.commodity_detail_name,
                          new_version.instrument_type_code,
                          new_version.risk_country_code,
                          new_version.tax_country_code,
                          new_version.bloomberg_code,
                          new_version.sedol,
                          new_version.reuters_mic,
                          new_version.operating_mic,
                          new_version.main_exchange,
                          new_version.market_status,
                          new_version.cur_mkt_cap_usd,
                          new_version.avg_daily_value_traded_30d_usd,
                          new_version.avg_daily_value_traded_3m_usd,
                          new_version.eqy_free_float_pct,
                          new_version.primary_inst_code,
                          new_version.coupon_rate,
                          new_version.cheapest_to_deliver_date,
                          new_version.market_code,
                          new_version.market_alias,
                          new_version.market_data_mapping_code,
                          new_version.cfd_cfi,
                          new_version.sb_cfi,
                          new_version.is_mifid_ii_reportable,
                          new_version.security_description,
                          new_version.pht_rsk_brkr_mrgn_amnt,
                          new_version.pht_rsk_brkr_mrgn_prcnt,
                          new_version.pht_rsk_brkr_mrgn_tier,
                          new_version.pht_rsk_brkr_mrgn_tier_fxdb,
                          new_version.pht_rsk_brkr_mrgn_tier_fxubs,
                          new_version.underlying_instrument_code,
                          new_version.underlying_instrument_alias,
                          new_version.is_ccy_in_frctnl_prts,
                          new_version.is_pair_ccy_in_frctnl_prts,
                          new_version.point_multiplier,
                          new_version.financing_currency,
                          new_version.im_is_test_instrument,
                          new_version.im_is_demo_only,
                          new_version.is_fixed_spread_instrument,
                          new_version.is_guaranteed_stop_instrmnt,
                          new_version.is_metatrader_instrument,
                          new_version.is_esma_duplicate,
                          new_version.is_findable_instrument,
                          new_version.is_tradable_instrument,
                          new_version.api_code
                       );
    --
    -- Select the instrument_languages that have got updated
    --

    MERGE INTO instrument_languages_h old_version USING
        (SELECT old_version.*
            FROM instrument_languages old_version,
                  TABLE(CAST(p_instruments AS instrument_tab)) instr_main,
                  TABLE(instr_main.instrument_languages) instr_lang
            WHERE old_version.instrument_code = instr_lang.instrument_code AND
                  old_version.language_code = instr_lang.language_code AND
                  old_version.Instrument_Code = instr_main.inst_code AND
                  (nrg_common.has_value_changed(old_version.short_name,instr_lang.short_name)= 1 OR
                   nrg_common.has_value_changed(old_version.long_name,instr_lang.long_name)= 1)) new_version
       ON ( old_version.instrument_code           = new_version.instrument_code AND
            old_version.language_code             = new_version.language_code AND
            old_version.effective_start_timestamp = new_version.effective_start_timestamp)
      WHEN MATCHED THEN
        UPDATE  SET old_version.updated_by                    = new_version.updated_by,
                old_version.action                        = 'U',
                old_version.action_timestamp              = SYSTIMESTAMP,
                old_version.update_timestamp              = new_version.update_timestamp,
                old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
                old_version.short_name                    = new_version.short_name,
                old_version.long_name                     = new_version.long_name
       WHEN NOT MATCHED THEN
           INSERT
                  (instrument_code,
                   logical_load_timestamp,
                   created_by,
                   create_timestamp,
                   updated_by,
                   update_timestamp,
                   effective_start_timestamp,
                   effective_end_timestamp,
                   action,
                   action_timestamp,
                   language_code,
                   short_name,
                   long_name)
                  VALUES
                    ( new_version.instrument_code,
                      new_version.logical_load_timestamp,
                      new_version.created_by,
                      new_version.create_timestamp,
                      new_version.updated_by,
                      new_version.update_timestamp,
                      new_version.effective_start_timestamp,
                      p_effective_start_timestamp,
                      'U',
                      SYSTIMESTAMP,
                      new_version.language_code,
                      new_version.short_name,
                      new_version.long_name);


   --MERGE INTO instrument_type_languages_h old_version
   --USING (SELECT old_version.*
   -- FROM instrument_type_languages old_version, TABLE(CAST(p_instruments AS instrument_tab)) instr_main, TABLE(instr_main.instrument_type_languages) instr_lang
   -- WHERE old_version.instrument_type_code = instr_lang.instrument_type_code AND
   --       old_version.language_code = instr_lang.language_code AND
   --       (nrg_common.has_value_changed(old_version.instrument_type_default_name,instr_lang.instrument_type_default_name)= 1 OR
   --       nrg_common.has_value_changed(old_version.instrument_type_name,instr_lang.instrument_type_name)= 1) ) new_version
   --ON (old_version.instrument_type_code         = new_version.instrument_type_code AND
   --    old_version.language_code                = new_version.language_code AND
   --    old_version.effective_start_timestamp    = new_version.effective_start_timestamp)
   -- WHEN MATCHED THEN
   --   UPDATE SET old_version.updated_by                    = new_version.updated_by,
   --              old_version.action                        = 'U',
   --              old_version.action_timestamp              = SYSTIMESTAMP,
   --              old_version.update_timestamp              = new_version.update_timestamp,
   --              old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
   --             old_version.instrument_type_default_name  = new_version.instrument_type_default_name,
   --              old_version.instrument_type_name          = new_version.instrument_type_name
   -- WHEN NOT MATCHED THEN
   --   INSERT
   --   (instrument_type_code,
   --    logical_load_timestamp,
   --    created_by,
   --    create_timestamp,
   --    updated_by,
   --    update_timestamp,
   --    effective_start_timestamp,
   --    effective_end_timestamp,
   --    action,
   --    action_timestamp,
   --    language_code,
   --    instrument_type_default_name,
   --    instrument_type_name)
   -- VALUES
   --   ( new_version.instrument_type_code,
   --     new_version.logical_load_timestamp,
   --     new_version.created_by,
   --     new_version.create_timestamp,
   --     new_version.updated_by,
   --     new_version.update_timestamp,
   --    new_version.effective_start_timestamp,
   --     p_effective_start_timestamp,
   --     'U',
   --     SYSTIMESTAMP,
   --     new_version.language_code,
   --     new_version.instrument_type_default_name,
   --     new_version.instrument_type_name);


    --
    -- Select Instrument markets that have got updated
    --
    --MERGE INTO instrument_markets_h old_version
    --USING (SELECT old_version.*
    --         FROM instrument_markets old_version,
    --              TABLE(CAST(p_instruments AS instrument_tab)) instr_main,
    --              TABLE(instr_main.instrument_markets) instr_markets
    --        WHERE old_version.instrument_code = instr_markets.instrument_code AND
    --              old_version.market_code = instr_markets.market_code AND
    --              old_version.Instrument_Code = instr_main.inst_code AND
    --              (nrg_common.has_value_changed(old_version.market_alias,instr_markets.market_alias)= 1 OR
    --               nrg_common.has_value_changed(old_version.market_type,instr_markets.market_type)= 1 OR
    --               nrg_common.has_value_changed(old_version.market_type,'NO')= 1)) new_version
    --ON (old_version.instrument_code = new_version.instrument_code AND
    --    old_version.market_code = new_version.market_code AND
    --    old_version.effective_start_timestamp = new_version.effective_start_timestamp)
    --WHEN MATCHED THEN UPDATE
    --    SET old_version.effective_end_timestamp = p_effective_start_timestamp,
    --        old_version.action = 'U',
    --        old_version.action_timestamp = SYSTIMESTAMP,
    --        old_version.is_deleted = new_version.is_deleted,
    --        old_version.market_alias = new_version.market_alias,
    --        old_version.market_type = new_version.market_type
    --WHEN NOT MATCHED THEN INSERT
    --   (instrument_code,
    --   market_code,
    --  logical_load_timestamp,
    --  created_by,
    --   create_timestamp,
    --   updated_by,
    --   update_timestamp,
    --   effective_start_timestamp,
    --   effective_end_timestamp,
    --   action,
    --   action_timestamp,
    --   is_deleted,
    --   market_alias,
    --   market_type)
    --   VALUES (new_version.instrument_code,
    --           new_version.market_code,
    --           new_version.logical_load_timestamp,
    --           new_version.created_by,
    --           new_version.create_timestamp,
    --           new_version.updated_by,
    --           new_version.update_timestamp,
    --           new_version.effective_start_timestamp,
    --           p_effective_start_timestamp,
    --           'U',
    --           SYSTIMESTAMP,
    --           new_version.is_deleted,
    --           new_version.market_alias,
    --          new_version.market_type);

 ---------------------
 --Merge instruments
 ---------------------
   UPDATE
   (SELECT /*+ index(i,INSTRUMENTS_PK)*/ i.* FROM instruments i
     WHERE NOT EXISTS (SELECT /*+ cardinality(ii,10000)*/1 FROM (SELECT inst_code FROM TABLE(CAST(p_instruments AS instrument_tab))) ii
                        WHERE ii.inst_code=i.instrument_code)
           AND i.Is_Deleted = 'NO')t
    SET t.logical_load_timestamp    = lv_logical_load_timestamp,
        t.updated_by                = p_user,
        t.update_timestamp          = SYSTIMESTAMP,
        t.effective_start_timestamp = p_effective_start_timestamp,
        t.is_deleted                = 'YES';

 /* UPDATE instruments i
   SET    logical_load_timestamp    = lv_logical_load_timestamp,
          updated_by                = p_user,
          update_timestamp          = SYSTIMESTAMP,
          effective_start_timestamp = p_effective_start_timestamp,
          is_deleted                = 'YES'
   WHERE instrument_code NOT IN (SELECT inst_code
                                 FROM TABLE(CAST(p_instruments AS instrument_tab)) ii);*/

   MERGE INTO instruments instrmt
    USING (SELECT all_instruments.inst_code,
                  all_instruments.country_code,
                  all_instruments.feed_symbol,
                  CASE
                    WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCFORWARDEXPIRY' THEN
                      forward_expiry_instrument_type.instrument_type
                    ELSE
                      all_instruments.instrument_type
                  END instrument_type,
                  all_instruments.short_name,
                  all_instruments.isin,
                  all_instruments.mic,
                  all_instruments.ric,
                  all_instruments.currency,
                  all_instruments.pair_currency,
                  all_instruments.company_sector_name,
                  all_instruments.commodity_type_name,
                  all_instruments.country_class_name,
                  all_instruments.is_deleted,
                  all_instruments.mmuid,
                  all_instruments.mmuid2,
                  all_instruments.mmvalueid,
                  all_instruments.prophet_banding_algorithm,
                  all_instruments.prophet_banding_fixedspread,
                  all_instruments.prophet_banding_spreadfactor,
                  all_instruments.mm_inst_id_cfd,
                  all_instruments.mm_inst_id_sb_ir,
                  all_instruments.mm_inst_id_sb_uk,
                  all_instruments.pricing_start_date,
                  all_instruments.first_trading_date,
                  all_instruments.last_rollover_date,
                  all_instruments.automatic_rollover_date,
                  all_instruments.last_trading_date,
                  all_instruments.cash_settlement_date,
                  all_instruments.expiry_date,
                  all_instruments.is_skip_rollover,
                  all_instruments.last_trading_date_desc,
                  all_instruments.last_settlement_date_desc,
                  all_instruments.last_rollover_date_desc,
                  all_instruments.contract_code,
                  all_instruments.rollover_target_code,
                  all_instruments.cmc_cash_instrument_code,
                  all_instruments.financial_instrument_type,
                  --CASE
                  --  WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCFORWARDEXPIRY' THEN
                  --    forward_expiry_cash_code.inst_code
                  --  WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCFORWARDCONTRACT' THEN
                  --    forward_contract_cash_code.inst_code
                  --  ELSE
                  --    all_instruments.inst_code
                  --END derived_cash_instrument_pms,
                  CASE
                    WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCFORWARDEXPIRY' THEN
                      forward_expiry_cash_code.inst_code
                    WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCFORWARDCONTRACT' THEN
                      forward_contract_cash_code.inst_code
                    WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCINSTRUMENT' AND all_instruments.cmc_cash_instrument_code IS NOT NULL THEN
                      all_instruments.cmc_cash_instrument_code
                    WHEN UPPER(all_instruments.financial_instrument_type) = 'CMCINSTRUMENT' AND all_instruments.primary_inst_code IS NOT NULL THEN
                      all_instruments.primary_inst_code
                    ELSE
                      all_instruments.inst_code
                  END derived_cash_instrument_pms,
                  all_instruments.exchange_product_code,
                  all_instruments.is_prr_qualifying_index,
                  all_instruments.position_risk_requirement_pct,
                  all_instruments.Security_Typ,
                  all_instruments.contract_size_override,
                  all_instruments.commodity_base_name,
                  all_instruments.commodity_detail_name,
                  all_instruments.instrument_type_code,
                  all_instruments.risk_country_code,
                  all_instruments.tax_country_code,
                  all_instruments.bloomberg_code,
                  all_instruments.sedol,
                  all_instruments.reuters_mic,
                  all_instruments.operating_mic,
                  all_instruments.main_exchange,
                  all_instruments.market_status,
                  all_instruments.cur_mkt_cap_usd,
                  all_instruments.avg_daily_value_traded_30d_usd,
                  all_instruments.avg_daily_value_traded_3m_usd,
                  all_instruments.eqy_free_float_pct,
                  all_instruments.primary_inst_code,
                  all_instruments.coupon_rate,
                  all_instruments.cheapest_to_deliver_date,
                  all_instruments.market_code,
                  all_instruments.market_alias,
                  all_instruments.market_data_mapping_code,
                  all_instruments.cfd_cfi,
                  all_instruments.sb_cfi,
                  all_instruments.is_mifid_ii_reportable,
                  all_instruments.security_description,
                  all_instruments.pht_rsk_brkr_mrgn_amnt,
                  all_instruments.pht_rsk_brkr_mrgn_prcnt,
                  all_instruments.pht_rsk_brkr_mrgn_tier,
                  all_instruments.pht_rsk_brkr_mrgn_tier_fxdb,
                  all_instruments.pht_rsk_brkr_mrgn_tier_fxubs,
                  all_instruments.underlying_instrument_code,
                  all_instruments.underlying_instrument_alias,
                  all_instruments.is_ccy_in_frctnl_prts,
                  all_instruments.is_pair_ccy_in_frctnl_prts,
                  all_instruments.point_multiplier,
                  all_instruments.financing_currency,
                  all_instruments.im_is_test_instrument,
                  all_instruments.im_is_demo_only,
                  all_instruments.is_fixed_spread_instrument,
                  all_instruments.is_guaranteed_stop_instrmnt,
                  all_instruments.is_metatrader_instrument,
                  all_instruments.is_esma_duplicate,
                  all_instruments.is_findable_instrument,
                  all_instruments.is_tradable_instrument,
                  all_instruments.api_code
         FROM TABLE(CAST(p_instruments AS instrument_tab)) all_instruments,
              (SELECT *
               FROM TABLE(CAST(p_instruments AS instrument_tab))ins
               WHERE UPPER(ins.financial_instrument_type) <> 'CMCFORWARDEXPIRY') forward_expiry_instrument_type,
              --To Get The Cash Code For Forward Expiry
              (SELECT *
               FROM TABLE(CAST(p_instruments AS instrument_tab))ins
               WHERE UPPER(ins.financial_instrument_type) NOT IN ('CMCFORWARDEXPIRY', 'CMCFORWARDCONTRACT')) forward_expiry_cash_code,
              --To Get The Cash Code For Forward Contract
              (SELECT *
               FROM TABLE(CAST(p_instruments AS instrument_tab))ins
               WHERE UPPER(ins.financial_instrument_type) NOT IN ('CMCFORWARDEXPIRY', 'CMCFORWARDCONTRACT')) forward_contract_cash_code

         WHERE all_instruments.contract_code = forward_expiry_instrument_type.inst_code (+) AND
               forward_expiry_instrument_type.cmc_cash_instrument_code = forward_expiry_cash_code.inst_code (+) AND
               all_instruments.cmc_cash_instrument_code = forward_contract_cash_code.inst_code (+)
         ) new_instrmt
    ON (instrmt.instrument_code = new_instrmt.inst_code)
    WHEN MATCHED THEN
      UPDATE
      SET
            instrmt.logical_load_timestamp                = lv_logical_load_timestamp,
            instrmt.updated_by                            = p_user,
            instrmt.update_timestamp                      = SYSTIMESTAMP,
            instrmt.effective_start_timestamp             = p_effective_start_timestamp,
            instrmt.country_code                          = new_instrmt.country_code,
            instrmt.feed_symbol                           = new_instrmt.feed_symbol,
            instrmt.instrument_type                       = new_instrmt.instrument_type,
            instrmt.short_name                            = new_instrmt.short_name,
            instrmt.isin                                  = new_instrmt.isin,
            instrmt.mic                                   = new_instrmt.mic,
            instrmt.ric                                   = new_instrmt.ric,
            instrmt.currency                              = new_instrmt.currency,
            instrmt.pair_currency                         = new_instrmt.pair_currency,
            instrmt.company_sector_name                   = new_instrmt.company_sector_name,
            instrmt.commodity_type_name                   = new_instrmt.commodity_type_name,
            instrmt.country_class_name                    = new_instrmt.country_class_name,
            instrmt.is_deleted                            = new_instrmt.is_deleted,
            instrmt.mmuid                                 = new_instrmt.mmuid,
            instrmt.mmuid2                                = new_instrmt.mmuid2,
            instrmt.mmvalueid                             = new_instrmt.mmvalueid,
            instrmt.prophet_banding_algorithm             = new_instrmt.prophet_banding_algorithm,
            instrmt.prophet_banding_fixedspread           = new_instrmt.prophet_banding_fixedspread,
            instrmt.prophet_banding_spreadfactor          = new_instrmt.prophet_banding_spreadfactor,
            instrmt.mm_inst_id_cfd                        = new_instrmt.mm_inst_id_cfd,
            instrmt.mm_inst_id_sb_ir                      = new_instrmt.mm_inst_id_sb_ir,
            instrmt.mm_inst_id_sb_uk                      = new_instrmt.mm_inst_id_sb_uk,
            instrmt.pricing_start_date                    = new_instrmt.pricing_start_date,
            instrmt.first_trading_date                    = new_instrmt.first_trading_date,
            instrmt.last_rollover_date                    = new_instrmt.last_rollover_date,
            instrmt.automatic_rollover_date               = new_instrmt.automatic_rollover_date,
            instrmt.last_trading_date                     = new_instrmt.last_trading_date,
            instrmt.cash_settlement_date                  = new_instrmt.cash_settlement_date,
            instrmt.expiry_date                           = new_instrmt.expiry_date,
            instrmt.is_skip_rollover                      = new_instrmt.is_skip_rollover,
            instrmt.last_trading_date_desc                = new_instrmt.last_trading_date_desc,
            instrmt.last_settlement_date_desc             = new_instrmt.last_settlement_date_desc,
            instrmt.last_rollover_date_desc               = new_instrmt.last_rollover_date_desc,
            instrmt.contract_code                         = new_instrmt.contract_code,
            instrmt.rollover_target_code                  = new_instrmt.rollover_target_code,
            instrmt.cmc_cash_instrument_code              = new_instrmt.cmc_cash_instrument_code,
            instrmt.financial_instrument_type             = new_instrmt.financial_instrument_type,
            instrmt.derived_cash_instrument_pms           = new_instrmt.derived_cash_instrument_pms,
            instrmt.exchange_product_code                 = new_instrmt.exchange_product_code,
            instrmt.is_prr_qualifying_index               = new_instrmt.is_prr_qualifying_index,
            instrmt.position_risk_requirement_pct         = new_instrmt.position_risk_requirement_pct,
            instrmt.Security_Typ                          = new_instrmt.Security_Typ,
            instrmt.contract_size_override                = new_instrmt.contract_size_override,
            instrmt.commodity_base_name                   = new_instrmt.commodity_base_name,
            instrmt.commodity_detail_name                 = new_instrmt.commodity_detail_name,
            instrmt.instrument_type_code                  = new_instrmt.instrument_type_code,
            instrmt.risk_country_code                     = new_instrmt.risk_country_code,
            instrmt.tax_country_code                      = new_instrmt.tax_country_code,
            instrmt.bloomberg_code                        = new_instrmt.bloomberg_code,
            instrmt.sedol                                 = new_instrmt.sedol,
            instrmt.reuters_mic                           = new_instrmt.reuters_mic,
            instrmt.operating_mic                         = new_instrmt.operating_mic,
            instrmt.main_exchange                         = new_instrmt.main_exchange,
            instrmt.market_status                         = new_instrmt.market_status,
            instrmt.cur_mkt_cap_usd                       = new_instrmt.cur_mkt_cap_usd,
            instrmt.avg_daily_value_traded_30d_usd        = new_instrmt.avg_daily_value_traded_30d_usd,
            instrmt.avg_daily_value_traded_3m_usd         = new_instrmt.avg_daily_value_traded_3m_usd,
            instrmt.eqy_free_float_pct                    = new_instrmt.eqy_free_float_pct,
            instrmt.primary_inst_code                     = new_instrmt.primary_inst_code,
            instrmt.coupon_rate                           = new_instrmt.coupon_rate,
            instrmt.cheapest_to_deliver_date              = new_instrmt.cheapest_to_deliver_date,
            instrmt.market_code                           = new_instrmt.market_code,
            instrmt.market_alias                          = new_instrmt.market_alias,
            instrmt.market_data_mapping_code              = new_instrmt.market_data_mapping_code,
            instrmt.cfd_cfi                               = new_instrmt.cfd_cfi,
            instrmt.sb_cfi                                = new_instrmt.sb_cfi,
            instrmt.is_mifid_ii_reportable                = new_instrmt.is_mifid_ii_reportable,
            instrmt.security_description				          = new_instrmt.security_description,
            instrmt.pht_rsk_brkr_mrgn_amnt				        = new_instrmt.pht_rsk_brkr_mrgn_amnt,
            instrmt.pht_rsk_brkr_mrgn_prcnt				        = new_instrmt.pht_rsk_brkr_mrgn_prcnt,
            instrmt.pht_rsk_brkr_mrgn_tier				        = new_instrmt.pht_rsk_brkr_mrgn_tier,
            instrmt.pht_rsk_brkr_mrgn_tier_fxdb			      = new_instrmt.pht_rsk_brkr_mrgn_tier_fxdb,
            instrmt.pht_rsk_brkr_mrgn_tier_fxubs		      = new_instrmt.pht_rsk_brkr_mrgn_tier_fxubs,
            instrmt.underlying_instrument_code		        = new_instrmt.underlying_instrument_code,
            instrmt.underlying_instrument_alias		        = new_instrmt.underlying_instrument_alias,
            instrmt.is_ccy_in_frctnl_prts		              = new_instrmt.is_ccy_in_frctnl_prts,
            instrmt.is_pair_ccy_in_frctnl_prts		        = new_instrmt.is_pair_ccy_in_frctnl_prts,
            instrmt.point_multiplier		                  = new_instrmt.point_multiplier,
            instrmt.financing_currency		                = new_instrmt.financing_currency,
            instrmt.im_is_test_instrument		              = new_instrmt.im_is_test_instrument,
            instrmt.im_is_demo_only		                    = new_instrmt.im_is_demo_only,
            instrmt.is_fixed_spread_instrument		        = new_instrmt.is_fixed_spread_instrument,
            instrmt.is_guaranteed_stop_instrmnt		        = new_instrmt.is_guaranteed_stop_instrmnt,
            instrmt.is_metatrader_instrument		          = new_instrmt.is_metatrader_instrument,
            instrmt.is_esma_duplicate		                  = new_instrmt.is_esma_duplicate,
            instrmt.is_findable_instrument		            = new_instrmt.is_findable_instrument,
            instrmt.is_tradable_instrument		            = new_instrmt.is_tradable_instrument,
            instrmt.api_code		                          = new_instrmt.api_code
      WHERE (nrg_common.has_value_changed(instrmt.country_code,new_instrmt.country_code)= 1 OR
             nrg_common.has_value_changed(instrmt.feed_symbol,new_instrmt.feed_symbol)= 1 OR
             nrg_common.has_value_changed(instrmt.instrument_type,new_instrmt.instrument_type)= 1 OR
             nrg_common.has_value_changed(instrmt.short_name,new_instrmt.short_name)= 1 OR
             nrg_common.has_value_changed(instrmt.isin,new_instrmt.isin)= 1 OR
             nrg_common.has_value_changed(instrmt.mic,new_instrmt.mic)= 1 OR
             nrg_common.has_value_changed(instrmt.ric,new_instrmt.ric)= 1 OR
             nrg_common.has_value_changed(instrmt.currency, new_instrmt.currency) = 1 OR
             nrg_common.has_value_changed(instrmt.pair_currency,new_instrmt.pair_currency)= 1 OR
             nrg_common.has_value_changed(instrmt.company_sector_name,new_instrmt.company_sector_name)= 1 OR
             nrg_common.has_value_changed(instrmt.commodity_type_name,new_instrmt.commodity_type_name)= 1 OR
             nrg_common.has_value_changed(instrmt.country_class_name,new_instrmt.country_class_name)= 1 OR
             nrg_common.has_value_changed(instrmt.is_deleted,new_instrmt.is_deleted)= 1 OR
             nrg_common.has_value_changed(instrmt.mmuid,new_instrmt.mmuid)= 1 OR
             nrg_common.has_value_changed(instrmt.mmuid2,new_instrmt.mmuid2)= 1 OR
             nrg_common.has_value_changed(instrmt.mmvalueid,new_instrmt.mmvalueid)= 1 OR
             nrg_common.has_value_changed(instrmt.prophet_banding_algorithm,new_instrmt.prophet_banding_algorithm)= 1 OR
             nrg_common.has_value_changed(instrmt.prophet_banding_fixedspread,new_instrmt.prophet_banding_fixedspread)= 1 OR
             nrg_common.has_value_changed(instrmt.prophet_banding_spreadfactor,new_instrmt.prophet_banding_spreadfactor)= 1 OR
             nrg_common.has_value_changed(instrmt.mm_inst_id_cfd, new_instrmt.mm_inst_id_cfd) = 1 OR
             nrg_common.has_value_changed(instrmt.mm_inst_id_sb_ir, new_instrmt.mm_inst_id_sb_ir) = 1 OR
             nrg_common.has_value_changed(instrmt.mm_inst_id_sb_uk, new_instrmt.mm_inst_id_sb_uk) = 1 OR
             nrg_common.has_value_changed(instrmt.pricing_start_date, new_instrmt.pricing_start_date) = 1 OR
             nrg_common.has_value_changed(instrmt.first_trading_date, new_instrmt.first_trading_date) = 1 OR
             nrg_common.has_value_changed(instrmt.last_rollover_date, new_instrmt.last_rollover_date) = 1 OR
             nrg_common.has_value_changed(instrmt.automatic_rollover_date, new_instrmt.automatic_rollover_date) = 1 OR
             nrg_common.has_value_changed(instrmt.last_trading_date, new_instrmt.last_trading_date) = 1 OR
             nrg_common.has_value_changed(instrmt.cash_settlement_date, new_instrmt.cash_settlement_date) = 1 OR
             nrg_common.has_value_changed(instrmt.expiry_date, new_instrmt.expiry_date) = 1 OR
             nrg_common.has_value_changed(instrmt.is_skip_rollover, new_instrmt.is_skip_rollover) = 1 OR
             nrg_common.has_value_changed(instrmt.last_trading_date_desc, new_instrmt.last_trading_date_desc) = 1 OR
             nrg_common.has_value_changed(instrmt.last_settlement_date_desc, new_instrmt.last_settlement_date_desc) = 1 OR
             nrg_common.has_value_changed(instrmt.last_rollover_date_desc, new_instrmt.last_rollover_date_desc) = 1 OR
             nrg_common.has_value_changed(instrmt.contract_code, new_instrmt.contract_code) = 1 OR
             nrg_common.has_value_changed(instrmt.rollover_target_code, new_instrmt.rollover_target_code) = 1 OR
             nrg_common.has_value_changed(instrmt.cmc_cash_instrument_code, new_instrmt.cmc_cash_instrument_code) = 1 OR
             nrg_common.has_value_changed(instrmt.financial_instrument_type, new_instrmt.financial_instrument_type) = 1 OR
             nrg_common.has_value_changed(instrmt.derived_cash_instrument_pms, new_instrmt.derived_cash_instrument_pms) = 1 OR
             nrg_common.has_value_changed(instrmt.exchange_product_code, new_instrmt.exchange_product_code) = 1 OR
             nrg_common.has_value_changed(instrmt.Is_Prr_Qualifying_Index, new_instrmt.Is_Prr_Qualifying_Index) = 1 OR
             nrg_common.has_value_changed(instrmt.position_risk_requirement_pct, new_instrmt.position_risk_requirement_pct) = 1 OR
             nrg_common.has_value_changed(instrmt.Security_Typ, new_instrmt.Security_Typ) = 1 OR
             nrg_common.has_value_changed(instrmt.contract_size_override, new_instrmt.contract_size_override) = 1 OR
             nrg_common.has_value_changed(instrmt.commodity_base_name, new_instrmt.commodity_base_name) = 1 OR
             nrg_common.has_value_changed(instrmt.commodity_detail_name, new_instrmt.commodity_detail_name ) = 1 OR
             nrg_common.has_value_changed(instrmt.instrument_type_code, new_instrmt.instrument_type_code ) = 1 OR
             nrg_common.has_value_changed(instrmt.risk_country_code, new_instrmt.risk_country_code ) = 1 OR
             nrg_common.has_value_changed(instrmt.tax_country_code, new_instrmt.tax_country_code ) = 1 OR
             nrg_common.has_value_changed(instrmt.bloomberg_code, new_instrmt.bloomberg_code ) = 1 OR
             nrg_common.has_value_changed(instrmt.sedol, new_instrmt.sedol ) = 1 OR
             nrg_common.has_value_changed(instrmt.reuters_mic, new_instrmt.reuters_mic ) = 1 OR
             nrg_common.has_value_changed(instrmt.operating_mic, new_instrmt.operating_mic ) = 1 OR
             nrg_common.has_value_changed(instrmt.main_exchange, new_instrmt.main_exchange ) = 1 OR
             nrg_common.has_value_changed(instrmt.market_status, new_instrmt.market_status ) = 1 OR
             nrg_common.has_value_changed(instrmt.cur_mkt_cap_usd, new_instrmt.cur_mkt_cap_usd ) = 1 OR
             nrg_common.has_value_changed(instrmt.avg_daily_value_traded_30d_usd, new_instrmt.avg_daily_value_traded_30d_usd ) = 1 OR
             nrg_common.has_value_changed(instrmt.avg_daily_value_traded_3m_usd, new_instrmt.avg_daily_value_traded_3m_usd ) = 1 OR
             nrg_common.has_value_changed(instrmt.eqy_free_float_pct, new_instrmt.eqy_free_float_pct ) = 1 OR
             nrg_common.has_value_changed(instrmt.primary_inst_code, new_instrmt.primary_inst_code) = 1 OR
             nrg_common.has_value_changed(instrmt.coupon_rate, new_instrmt.coupon_rate) = 1 OR
             nrg_common.has_value_changed(instrmt.cheapest_to_deliver_date, new_instrmt.cheapest_to_deliver_date) = 1 OR
             nrg_common.has_value_changed(instrmt.market_code, new_instrmt.market_code) = 1 OR
             nrg_common.has_value_changed(instrmt.market_alias, new_instrmt.market_alias) = 1 OR
             nrg_common.has_value_changed(instrmt.market_data_mapping_code, new_instrmt.market_data_mapping_code) = 1 OR
             nrg_common.has_value_changed(instrmt.cfd_cfi, new_instrmt.cfd_cfi) = 1 OR
             nrg_common.has_value_changed(instrmt.sb_cfi, new_instrmt.sb_cfi) = 1 OR
             nrg_common.has_value_changed(instrmt.is_mifid_ii_reportable, new_instrmt.is_mifid_ii_reportable) = 1 OR
             nrg_common.has_value_changed(instrmt.security_description, new_instrmt.security_description) = 1 OR
             nrg_common.has_value_changed(instrmt.pht_rsk_brkr_mrgn_amnt, new_instrmt.pht_rsk_brkr_mrgn_amnt) = 1 OR
             nrg_common.has_value_changed(instrmt.pht_rsk_brkr_mrgn_prcnt, new_instrmt.pht_rsk_brkr_mrgn_prcnt) = 1 OR
             nrg_common.has_value_changed(instrmt.pht_rsk_brkr_mrgn_tier, new_instrmt.pht_rsk_brkr_mrgn_tier) = 1 OR
             nrg_common.has_value_changed(instrmt.pht_rsk_brkr_mrgn_tier_fxdb, new_instrmt.pht_rsk_brkr_mrgn_tier_fxdb) = 1 OR
             nrg_common.has_value_changed(instrmt.pht_rsk_brkr_mrgn_tier_fxubs, new_instrmt.pht_rsk_brkr_mrgn_tier_fxubs) = 1 OR
             nrg_common.has_value_changed(instrmt.underlying_instrument_code, new_instrmt.underlying_instrument_code) = 1 OR
             nrg_common.has_value_changed(instrmt.underlying_instrument_alias, new_instrmt.underlying_instrument_alias) = 1 OR
             nrg_common.has_value_changed(instrmt.is_ccy_in_frctnl_prts, new_instrmt.is_ccy_in_frctnl_prts) = 1 OR
             nrg_common.has_value_changed(instrmt.is_pair_ccy_in_frctnl_prts, new_instrmt.is_pair_ccy_in_frctnl_prts) = 1 OR
             nrg_common.has_value_changed(instrmt.point_multiplier, new_instrmt.point_multiplier) = 1 OR
             nrg_common.has_value_changed(instrmt.financing_currency, new_instrmt.financing_currency) = 1 OR
             nrg_common.has_value_changed(instrmt.im_is_test_instrument, new_instrmt.im_is_test_instrument) = 1 OR
             nrg_common.has_value_changed(instrmt.im_is_demo_only, new_instrmt.im_is_demo_only) = 1 OR
             nrg_common.has_value_changed(instrmt.is_fixed_spread_instrument, new_instrmt.is_fixed_spread_instrument) = 1 OR
             nrg_common.has_value_changed(instrmt.is_guaranteed_stop_instrmnt, new_instrmt.is_guaranteed_stop_instrmnt) = 1 OR
             nrg_common.has_value_changed(instrmt.is_metatrader_instrument, new_instrmt.is_metatrader_instrument) = 1 OR
             nrg_common.has_value_changed(instrmt.is_esma_duplicate, new_instrmt.is_esma_duplicate) = 1 OR
             nrg_common.has_value_changed(instrmt.is_findable_instrument, new_instrmt.is_findable_instrument) = 1 OR
             nrg_common.has_value_changed(instrmt.is_tradable_instrument, new_instrmt.is_tradable_instrument) = 1 OR
             nrg_common.has_value_changed(instrmt.api_code, new_instrmt.api_code) = 1
             )
    WHEN NOT MATCHED THEN
      INSERT  (
                instrument_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                country_code,
                feed_symbol,
                instrument_type,
                short_name,
                isin,
                mic,
                ric,
                currency,
                pair_currency,
                company_sector_name,
                commodity_type_name,
                country_class_name,
                is_deleted,
                mmuid,
                mmuid2,
                mmvalueid,
                prophet_banding_algorithm,
                prophet_banding_fixedspread,
                prophet_banding_spreadfactor,
                mm_inst_id_cfd,
                mm_inst_id_sb_ir,
                mm_inst_id_sb_uk,
                pricing_start_date,
                first_trading_date,
                last_rollover_date,
                automatic_rollover_date,
                last_trading_date,
                cash_settlement_date,
                expiry_date,
                is_skip_rollover,
                last_trading_date_desc,
                last_settlement_date_desc,
                last_rollover_date_desc,
                contract_code,
                rollover_target_code,
                cmc_cash_instrument_code,
                financial_instrument_type,
                derived_cash_instrument_pms,
                exchange_product_code,
                is_prr_qualifying_index,
                position_risk_requirement_pct,
                Security_Typ,
                contract_size_override,
                commodity_base_name,
                commodity_detail_name,
                instrument_type_code,
                risk_country_code,
                tax_country_code,
                bloomberg_code,
                sedol,
                reuters_mic,
                operating_mic,
                main_exchange,
                market_status,
                cur_mkt_cap_usd,
                avg_daily_value_traded_30d_usd,
                avg_daily_value_traded_3m_usd,
                eqy_free_float_pct,
                primary_inst_code,
                coupon_rate,
                cheapest_to_deliver_date,
                market_code,
                market_alias,
                market_data_mapping_code,
                cfd_cfi,
                sb_cfi,
                is_mifid_ii_reportable,
                security_description,
                pht_rsk_brkr_mrgn_amnt,
                pht_rsk_brkr_mrgn_prcnt,
                pht_rsk_brkr_mrgn_tier,
                pht_rsk_brkr_mrgn_tier_fxdb,
                pht_rsk_brkr_mrgn_tier_fxubs,
                underlying_instrument_code,
                underlying_instrument_alias,
                is_ccy_in_frctnl_prts,
                is_pair_ccy_in_frctnl_prts,
                point_multiplier,
                financing_currency,
                im_is_test_instrument,
                im_is_demo_only,
                is_fixed_spread_instrument,
                is_guaranteed_stop_instrmnt,
                is_metatrader_instrument,
                is_esma_duplicate,
                is_findable_instrument,
                is_tradable_instrument,
                api_code
				)
      VALUES (
                new_instrmt.inst_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_instrmt.country_code,
                new_instrmt.feed_symbol,
                new_instrmt.instrument_type,
                new_instrmt.short_name,
                new_instrmt.isin,
                new_instrmt.mic,
                new_instrmt.ric,
                new_instrmt.currency,
                new_instrmt.pair_currency,
                new_instrmt.company_sector_name,
                new_instrmt.commodity_type_name,
                new_instrmt.country_class_name,
                new_instrmt.is_deleted,
                new_instrmt.mmuid,
                new_instrmt.mmuid2,
                new_instrmt.mmvalueid,
                new_instrmt.prophet_banding_algorithm,
                new_instrmt.prophet_banding_fixedspread,
                new_instrmt.prophet_banding_spreadfactor,
                new_instrmt.mm_inst_id_cfd,
                new_instrmt.mm_inst_id_sb_ir,
                new_instrmt.mm_inst_id_sb_uk,
                new_instrmt.pricing_start_date,
                new_instrmt.first_trading_date,
                new_instrmt.last_rollover_date,
                new_instrmt.automatic_rollover_date,
                new_instrmt.last_trading_date,
                new_instrmt.cash_settlement_date,
                new_instrmt.expiry_date,
                new_instrmt.is_skip_rollover,
                new_instrmt.last_trading_date_desc,
                new_instrmt.last_settlement_date_desc,
                new_instrmt.last_rollover_date_desc,
                new_instrmt.contract_code,
                new_instrmt.rollover_target_code,
                new_instrmt.cmc_cash_instrument_code,
                new_instrmt.financial_instrument_type,
                new_instrmt.derived_cash_instrument_pms,
                new_instrmt.exchange_product_code,
                new_instrmt.is_prr_qualifying_index,
                new_instrmt.position_risk_requirement_pct,
                new_instrmt.Security_Typ,
                new_instrmt.contract_size_override,
                new_instrmt.commodity_base_name,
                new_instrmt.commodity_detail_name,
                new_instrmt.instrument_type_code,
                new_instrmt.risk_country_code,
                new_instrmt.tax_country_code,
                new_instrmt.bloomberg_code,
                new_instrmt.sedol,
                new_instrmt.reuters_mic,
                new_instrmt.operating_mic,
                new_instrmt.main_exchange,
                new_instrmt.market_status,
                new_instrmt.cur_mkt_cap_usd,
                new_instrmt.avg_daily_value_traded_30d_usd,
                new_instrmt.avg_daily_value_traded_3m_usd,
                new_instrmt.eqy_free_float_pct,
                new_instrmt.primary_inst_code,
                new_instrmt.coupon_rate,
                new_instrmt.cheapest_to_deliver_date,
                new_instrmt.market_code,
                new_instrmt.market_alias,
                new_instrmt.market_data_mapping_code,
                new_instrmt.cfd_cfi,
                new_instrmt.sb_cfi,
                new_instrmt.is_mifid_ii_reportable,
                new_instrmt.security_description,
                new_instrmt.pht_rsk_brkr_mrgn_amnt,
                new_instrmt.pht_rsk_brkr_mrgn_prcnt,
                new_instrmt.pht_rsk_brkr_mrgn_tier,
                new_instrmt.pht_rsk_brkr_mrgn_tier_fxdb,
                new_instrmt.pht_rsk_brkr_mrgn_tier_fxubs,
                new_instrmt.underlying_instrument_code,
                new_instrmt.underlying_instrument_alias,
                new_instrmt.is_ccy_in_frctnl_prts,
                new_instrmt.is_pair_ccy_in_frctnl_prts,
                new_instrmt.point_multiplier,
                new_instrmt.financing_currency,
                new_instrmt.im_is_test_instrument,
                new_instrmt.im_is_demo_only,
                new_instrmt.is_fixed_spread_instrument,
                new_instrmt.is_guaranteed_stop_instrmnt,
                new_instrmt.is_metatrader_instrument,
                new_instrmt.is_esma_duplicate,
                new_instrmt.is_findable_instrument,
                new_instrmt.is_tradable_instrument,
                new_instrmt.api_code
				);

    --
    -- Instrument languages
    --

    MERGE INTO instrument_languages old_version
    USING (SELECT instr_lang.instrument_code instrument_code
                 ,instr_lang.language_code language_code
                 ,instr_lang.short_name short_name
                 ,instr_lang.long_name long_name
         FROM TABLE(CAST(p_instruments AS instrument_tab)) main_instr, TABLE(main_instr.instrument_languages) instr_lang) new_version
    ON (old_version.instrument_code = new_version.instrument_code
        AND old_version.language_code = new_version.language_code)
    WHEN MATCHED THEN
      UPDATE
      SET
            old_version.logical_load_timestamp       = lv_logical_load_timestamp,
            old_version.updated_by                   = p_user,
            old_version.update_timestamp             = SYSTIMESTAMP,
            old_version.effective_start_timestamp    = p_effective_start_timestamp,
            old_version.short_name                   = new_version.short_name,
            old_version.long_name                    = new_version.long_name
      WHERE (nrg_common.has_value_changed(old_version.short_name,new_version.short_name)= 1 OR
             nrg_common.has_value_changed(old_version.long_name,new_version.long_name)= 1)
    WHEN NOT MATCHED THEN
      INSERT  (instrument_code,
               logical_load_timestamp,
               created_by,
               create_timestamp,
               updated_by,
               update_timestamp,
               effective_start_timestamp,
               language_code,
               short_name,
               long_name)
        VALUES( new_version.instrument_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_version.language_code,
                new_version.short_name,
                new_version.long_name);

    --
    --Soft Delete INSTRUMENT_MARKETS
    --
    --UPDATE
    --(SELECT /*+ index(i, INSTRUMENT_MARKETS_PK)*/* FROM instrument_markets i
    -- WHERE NOT EXISTS
    -- (SELECT 1 FROM TABLE(CAST(p_instruments AS instrument_tab)) main_instr
    --                    ,TABLE(main_instr.instrument_markets) instr_market
    --    WHERE main_instr.inst_code=instr_market.instrument_code
    --      AND instr_market.instrument_code=i.Instrument_Code
    --      AND instr_market.market_code=i.Market_Code)
    --    AND i.Is_Deleted = 'NO'
    --  ) t SET t.logical_load_timestamp     = lv_logical_load_timestamp,
    --          t.updated_by                 = p_user,
    --          t.update_timestamp           = SYSTIMESTAMP,
    --          t.effective_start_timestamp  = p_effective_start_timestamp,
    --          t.is_deleted                 = 'YES';

   /* UPDATE instrument_markets
    SET    logical_load_timestamp     = lv_logical_load_timestamp,
           updated_by                 = p_user,
           update_timestamp           = SYSTIMESTAMP,
           effective_start_timestamp  = p_effective_start_timestamp,
           is_deleted                 = 'YES'
    WHERE (instrument_code, market_code) NOT IN (SELECT instr_market.instrument_code instrument_code
                 ,instr_market.market_code market_code
         FROM TABLE(CAST(p_instruments AS instrument_tab)) main_instr, TABLE(main_instr.instrument_markets) instr_market)
      AND Is_Deleted = 'NO';*/

    --
    --Instrument Markets
    --

    --MERGE INTO instrument_markets old_version
    --USING (SELECT instr_market.instrument_code instrument_code
    --             ,instr_market.market_code market_code
    --             ,instr_market.market_alias market_alias
    --             ,instr_market.market_type
    --             ,'NO' AS is_deleted
    --     FROM TABLE(CAST(p_instruments AS instrument_tab)) main_instr, TABLE(main_instr.instrument_markets) instr_market) new_version
    --ON (old_version.instrument_code = new_version.instrument_code
    --    AND old_version.market_code = new_version.market_code)
    --WHEN MATCHED THEN
    --  UPDATE
    --  SET
    --        old_version.logical_load_timestamp       = lv_logical_load_timestamp,
    --        old_version.updated_by                   = p_user,
    --        old_version.update_timestamp             = SYSTIMESTAMP,
    --        old_version.effective_start_timestamp    = p_effective_start_timestamp,
    --        old_version.market_alias                 = new_version.market_alias,
    --        old_version.market_type                  = new_version.market_type,
    --        old_version.is_deleted                   = new_version.is_deleted
    -- WHERE (nrg_common.has_value_changed(old_version.market_alias,new_version.market_alias)= 1 OR
    --         nrg_common.has_value_changed(old_version.market_type,new_version.market_type)= 1 OR
    --         nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted)= 1
    --         )
    --WHEN NOT MATCHED THEN
    --  INSERT  (instrument_code,
    --           logical_load_timestamp,
    --           created_by,
    --           create_timestamp,
    --           updated_by,
    --           update_timestamp,
    --           effective_start_timestamp,
    --           is_deleted,
    --           market_code,
    --           market_alias,
    --           market_type)
    --    VALUES( new_version.instrument_code,
    --            lv_logical_load_timestamp,
    --            p_user,
    --            SYSTIMESTAMP,
    --            p_user,
    --            SYSTIMESTAMP,
    --            p_effective_start_timestamp,
    --            'NO',
    --            new_version.market_code,
    --            new_version.market_alias,
    --            new_version.market_type);

    --
    -- Instrument type languages
    --

   -- MERGE INTO instrument_type_languages old_version
   -- USING (SELECT DISTINCT instr_lang.instrument_type_code          AS instrument_type_code
   --              ,instr_lang.language_code                 AS language_code
   --              ,instr_lang.instrument_type_default_name  AS instrument_type_default_name
   --              ,instr_lang.instrument_type_name          AS instrument_type_name
   --      FROM TABLE(CAST(p_instruments AS instrument_tab)) main_instr, TABLE(main_instr.instrument_type_languages) instr_lang) new_version
   -- ON (old_version.instrument_type_code = new_version.instrument_type_code
   --     AND old_version.language_code = new_version.language_code)
   -- WHEN MATCHED THEN
   --   UPDATE
   --   SET   old_version.logical_load_timestamp       = lv_logical_load_timestamp,
   --         old_version.updated_by                   = p_user,
   --         old_version.update_timestamp             = SYSTIMESTAMP,
   --         old_version.effective_start_timestamp    = p_effective_start_timestamp,
   --         old_version.instrument_type_default_name = new_version.instrument_type_default_name,
   --         old_version.instrument_type_name         = new_version.instrument_type_name
   --   WHERE (nrg_common.has_value_changed(old_version.instrument_type_default_name,new_version.instrument_type_default_name)= 1 OR
   --          nrg_common.has_value_changed(old_version.instrument_type_name,new_version.instrument_type_name)= 1
   --          )
   -- WHEN NOT MATCHED THEN
   --   INSERT  (instrument_type_code,
   --            logical_load_timestamp,
   --            created_by,
   --            create_timestamp,
   --            updated_by,
   --            update_timestamp,
   --            effective_start_timestamp,
    --           language_code,
   --            instrument_type_default_name,
   --            instrument_type_name
   --            )
   --     VALUES( new_version.instrument_type_code,
   --             lv_logical_load_timestamp,
   --             p_user,
   --             SYSTIMESTAMP,
   --             p_user,
   --             SYSTIMESTAMP,
   --             p_effective_start_timestamp,
   --             new_version.language_code,
   --             new_version.instrument_type_default_name,
   --             new_version.instrument_type_name);


    END put_instruments;

    -- ===================================================================================
    -- put_products
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --     PRODUCTS_H
    --
    --  From NRG we get only the updated data set each time a new product snapshot is created by PMS. Also the freequency of updates
    --  to products is minimal.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_products                       Product Details
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_products(p_user                           IN products.created_by%TYPE,
                           --p_platform                       IN products.Platform%TYPE,
                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                           p_products                       IN product_tab)
    IS
      lv_logical_load_timestamp products.Logical_Load_Timestamp%TYPE := systimestamp;
    BEGIN
    --
    --Select The Products That Are Going For An Update So That They Can Be Written To The History
    --


        MERGE INTO products_h old_version
         USING    (SELECT DISTINCT platform,
                          instrument_code,
                          wrapper_code,
                          logical_load_timestamp,
                          created_by,
                          create_timestamp,
                          updated_by,
                          update_timestamp,
                          effective_start_timestamp,
                          rm_code,
                          product_setting_code,
                          instrument_term,
                          short_name,
                          long_name,
                          underlying_name,
                          is_ccy_in_fractional_parts,
                          contract_size,
                          currency,
                          fractional_part_ratio,
                          initial_published_date,
                          is_findable,
                          rqustd_fnncng_ratio_dflt,
                          is_deleted,
                          is_pr_ccy_in_fractional_parts,
                          point_multiplier
                   FROM (
                       SELECT prd.*
                         FROM   products prd,
                      (SELECT DISTINCT platform,
                              instrument_code,
                              wrapper_code,
                              mm_instrument_id,
                              product_setting_code,
                              instrument_term,
                              underlying_name,
                              is_ccy_in_fractional_parts,
                              contract_size,
                              currency_code,
                              currency,
                              mm_currency,
                              fractional_part_ratio,
                              initial_published_date,
                              is_findable,
                              short_name,
                              long_name,
                              expiry_date,
                              cfd_multiplier,
                              point,
                              term,
                              term_description,
                              is_deleted,
                              rqustd_fnncng_ratio_dflt,
                              is_pr_ccy_in_fractional_parts,
                              point_multiplier
                      FROM TABLE(CAST(p_products AS product_tab))) new_prd
                WHERE prd.platform = new_prd.platform (+) AND
                      prd.wrapper_code = new_prd.wrapper_code (+) AND
                      prd.instrument_code = new_prd.instrument_code (+) AND
                     (nrg_common.has_value_changed(prd.product_setting_code,new_prd.product_setting_code) = 1 OR
                      nrg_common.has_value_changed(prd.instrument_term,new_prd.instrument_term) = 1 OR
                      nrg_common.has_value_changed(prd.short_name,new_prd.short_name) = 1 OR
                      nrg_common.has_value_changed(prd.long_name,new_prd.long_name) = 1 OR
                      nrg_common.has_value_changed(prd.underlying_name,new_prd.underlying_name) = 1 OR
                      nrg_common.has_value_changed(prd.is_ccy_in_fractional_parts,new_prd.is_ccy_in_fractional_parts) = 1 OR
                      nrg_common.has_value_changed(prd.contract_size,new_prd.contract_size) = 1 OR
                      nrg_common.has_value_changed(prd.currency,new_prd.currency) = 1 OR
                      nrg_common.has_value_changed(prd.fractional_part_ratio,new_prd.fractional_part_ratio) = 1 OR
                      nrg_common.has_value_changed(prd.initial_published_date,new_prd.initial_published_date) = 1 OR
                      nrg_common.has_value_changed(prd.is_findable,new_prd.is_findable) = 1 OR
                      nrg_common.has_value_changed(prd.rqustd_fnncng_ratio_dflt,new_prd.rqustd_fnncng_ratio_dflt) = 1 OR
                      nrg_common.has_value_changed(prd.is_deleted,new_prd.is_deleted) = 1 OR
                      nrg_common.has_value_changed(prd.is_pr_ccy_in_fractional_parts,new_prd.is_pr_ccy_in_fractional_parts) = 1 OR
                      nrg_common.has_value_changed(prd.point_multiplier,new_prd.point_multiplier) = 1
                      )) ) new_version
         ON (old_version.platform                  = new_version.platform AND
             old_version.wrapper_code              = new_version.wrapper_code AND
             old_version.instrument_code           = new_version.instrument_code and
             old_version.effective_start_timestamp = new_version.effective_start_timestamp)
     WHEN MATCHED THEN
         UPDATE  SET old_version.updated_by               = new_version.updated_by,
                    old_version.action                    = 'U',
                    old_version.action_timestamp          = SYSTIMESTAMP,
                    old_version.update_timestamp          = new_version.update_timestamp,
                    old_version.logical_load_timestamp    = new_version.logical_load_timestamp,
                    old_version.contract_size             = new_version.contract_size,
                    old_version.currency                  = new_version.currency,
                    old_version.fractional_part_ratio     = new_version.fractional_part_ratio,
                    old_version.initial_published_date    = new_version.initial_published_date,
                    old_version.is_ccy_in_fractional_parts = new_version.is_ccy_in_fractional_parts,
                    old_version.instrument_term           = new_version.instrument_term,
                    old_version.product_setting_code      = new_version.product_setting_code,
                    old_version.short_name                = new_version.short_name,
                    old_version.long_name                 = new_version.long_name,
                    old_version.underlying_name           = new_version.underlying_name,
                    old_version.is_findable               = new_version.is_findable,
                    old_version.rm_code                   = new_version.rm_code,
                    old_version.is_deleted                = new_version.is_deleted,
                    old_version.is_pr_ccy_in_fractional_parts = new_version.is_pr_ccy_in_fractional_parts,
                    old_version.point_multiplier          = new_version.point_multiplier
     WHEN NOT MATCHED THEN
          INSERT
             VALUES
              (new_version.platform,
               new_version.instrument_code,
               new_version.wrapper_code,
               new_version.logical_load_timestamp,
               new_version.created_by,
               new_version.create_timestamp,
               new_version.updated_by,
               new_version.update_timestamp,
               new_version.effective_start_timestamp,
               p_effective_start_timestamp,
               'U',
               SYSTIMESTAMP,
               new_version.rm_code,
               new_version.product_setting_code,
               new_version.instrument_term,
               new_version.short_name,
               new_version.long_name,
               new_version.underlying_name,
               new_version.is_ccy_in_fractional_parts,
               new_version.contract_size,
               new_version.currency,
               new_version.fractional_part_ratio,
               new_version.initial_published_date,
               new_version.is_findable,
               new_version.rqustd_fnncng_ratio_dflt,
               new_version.is_deleted,
               new_version.is_pr_ccy_in_fractional_parts,
               new_version.point_multiplier
              );

/*
  MERGE INTO mm_products_h old_version USING
  ( SELECT *
      FROM (
    SELECT prd.*
    FROM mm_products prd,
         (SELECT DISTINCT platform,
                  instrument_code,
                  wrapper_code,
                  mm_instrument_id,
                  product_setting_code,
                  instrument_term,
                  underlying_name,
                  is_ccy_in_fractional_parts,
                  contract_size,
                  currency_code,
                  currency,
                  mm_currency,
                  fractional_part_ratio,
                  initial_published_date,
                  is_findable,
                  short_name,
                  long_name,
                  expiry_date,
                  cfd_multiplier,
                  point,
                  term,
                  term_description,
                  is_deleted,
                  rqustd_fnncng_ratio_dflt,
                  is_pr_ccy_in_fractional_parts,
                  point_multiplier
          FROM TABLE(CAST(p_products AS product_tab)))new_prd
    WHERE prd.platform =  new_prd.platform (+) AND
          prd.wrapper_code = new_prd.wrapper_code (+) AND
          prd.instrument_code = new_prd.instrument_code (+) AND
          prd.mm_instrument_id = new_prd.mm_instrument_id (+) AND
          (nrg_common.has_value_changed(prd.short_name,new_prd.short_name) = 1 OR
           nrg_common.has_value_changed(prd.long_name,new_prd.long_name) = 1 OR
           nrg_common.has_value_changed(prd.expiry_date,new_prd.expiry_date) = 1 OR
           nrg_common.has_value_changed(prd.cfd_multiplier,new_prd.cfd_multiplier) = 1 OR
           nrg_common.has_value_changed(prd.point,new_prd.point) = 1 OR
           nrg_common.has_value_changed(prd.currency_code,new_prd.currency_code) = 1 OR
           nrg_common.has_value_changed(prd.term,new_prd.term) = 1 OR
           nrg_common.has_value_changed(prd.term_description,new_prd.term_description) = 1 OR
           nrg_common.has_value_changed(prd.currency_description,new_prd.currency) = 1 OR
           nrg_common.has_value_changed(prd.is_deleted,new_prd.is_deleted) = 1))
    WHERE  platform = p_platform) new_version
    ON ( old_version.mm_instrument_id          = new_version.mm_instrument_id AND
         old_version.platform                  = new_version.platform AND
         old_version.wrapper_code              = new_version.wrapper_code AND
         old_version.instrument_code           = new_version.instrument_code AND
         old_version.effective_start_timestamp = new_version.effective_start_timestamp)
     WHEN MATCHED THEN
       UPDATE SET   old_version.updated_by                = new_version.updated_by,
                    old_version.action                    = 'U',
                    old_version.action_timestamp          = SYSTIMESTAMP,
                    old_version.update_timestamp          = new_version.update_timestamp,
                    old_version.logical_load_timestamp    = new_version.logical_load_timestamp,
                    old_version.short_name                = new_version.short_name,
                    old_version.long_name                 = new_version.long_name,
                    old_version.expiry_date               = new_version.expiry_date,
                    old_version.cfd_multiplier            = new_version.cfd_multiplier,
                    old_version.point                     = new_version.point,
                    old_version.currency_code             = new_version.currency_code,
                    old_version.term                      = new_version.term,
                    old_version.term_description          = new_version.term_description,
                    old_version.rm_code                   = new_version.rm_code,
                    old_version.currency_description      = new_version.currency_description,
                    old_version.prophet_symbol            = new_version.prophet_symbol,
                    old_version.is_deleted                = new_version.is_deleted
     WHEN NOT MATCHED THEN
       INSERT
             VALUES
             ('U',
              SYSTIMESTAMP,
              new_version.platform,
              new_version.instrument_code,
              new_version.wrapper_code,
              new_version.mm_instrument_id,
              new_version.logical_load_timestamp,
              new_version.created_by,
              new_version.create_timestamp,
              new_version.updated_by,
              new_version.update_timestamp,
              new_version.effective_start_timestamp,
              p_effective_start_timestamp,
              new_version.rm_code,
              new_version.short_name,
              new_version.long_name,
              new_version.expiry_date,
              new_version.cfd_multiplier,
              new_version.point,
              new_version.currency_code,
              new_version.term,
              new_version.term_description,
              new_version.currency_description,
              new_version.prophet_symbol,
              new_version.is_deleted);*/

    --
    --Soft Delete Products
    --

    UPDATE products
    SET    updated_by = p_user,
           update_timestamp = SYSTIMESTAMP,
           logical_load_timestamp = lv_logical_load_timestamp,
           effective_start_timestamp = p_effective_start_timestamp,
           is_deleted = 'YES'
    WHERE  (platform, instrument_code, wrapper_code) NOT IN (SELECT DISTINCT platform, instrument_code, wrapper_code
                                                             FROM TABLE(CAST(p_products AS product_tab))products)
       AND is_deleted = 'NO';


    UPDATE
    (SELECT /*+ index (prdct, MM_PRODUCTS_PK)*/ prdct.* FROM mm_products prdct WHERE NOT EXISTS
       (SELECT /*+ cardinality (pp, 10000)*/ 1 FROM (SELECT mm_instrument_id, platform, instrument_code, wrapper_code FROM TABLE(CAST(p_products AS product_tab))) pp
         WHERE prdct.PLATFORM= pp.PLATFORM
           AND prdct.INSTRUMENT_CODE = pp.INSTRUMENT_CODE
           AND prdct.WRAPPER_CODE = pp.WRAPPER_CODE
           AND prdct.MM_INSTRUMENT_ID = pp.MM_INSTRUMENT_ID)
       AND prdct.Is_Deleted = 'NO'
     ) t
    SET    t.logical_load_timestamp    = lv_logical_load_timestamp,
           t.updated_by                = p_user,
           t.update_timestamp          = SYSTIMESTAMP,
           t.effective_start_timestamp = p_effective_start_timestamp,
           t.is_deleted                = 'YES';


    MERGE INTO products prdct
    USING (SELECT DISTINCT products.platform,
                  products.instrument_code,
                  products.wrapper_code,
                  products.mm_instrument_id,
                  products.product_setting_code,
                  products.instrument_term,
                  products.underlying_name,
                  products.is_ccy_in_fractional_parts,
                  products.contract_size,
                  products.currency_code,
                  products.currency,
                  products.mm_currency,
                  products.fractional_part_ratio,
                  products.initial_published_date,
                  products.is_findable,
                  products.short_name,
                  products.long_name,
                  products.expiry_date,
                  products.cfd_multiplier,
                  products.point,
                  products.term,
                  products.term_description,
                  products.is_deleted,
                  products.rqustd_fnncng_ratio_dflt,
                  products.is_pr_ccy_in_fractional_parts,
                  products.point_multiplier,
                  rm_map.rm_code
         FROM TABLE(CAST(p_products AS product_tab))products, bi_ref.rm_mappings rm_map
         WHERE products.instrument_code = rm_map.ng_inst_id(+) AND
               platform = 'NG') new_prdct
    ON (prdct.platform = new_prdct.platform AND
      prdct.instrument_code = new_prdct.instrument_code AND
      prdct.wrapper_code = new_prdct.wrapper_code)
    WHEN MATCHED THEN
      UPDATE
        SET    prdct.contract_size = new_prdct.contract_size,
               prdct.currency= new_prdct.currency,
               prdct.fractional_part_ratio = new_prdct.fractional_part_ratio,
               prdct.initial_published_date = new_prdct.initial_published_date,
               prdct.is_ccy_in_fractional_parts = new_prdct.is_ccy_in_fractional_parts,
               prdct.instrument_term = new_prdct.instrument_term,
               prdct.rm_code = new_prdct.rm_code,
               prdct.product_setting_code = new_prdct.product_setting_code,
               prdct.short_name = new_prdct.short_name,
               prdct.long_name = new_prdct.long_name,
               prdct.underlying_name = new_prdct.underlying_name,
               prdct.is_findable = new_prdct.is_findable,
               prdct.updated_by = p_user,
               prdct.update_timestamp = SYSTIMESTAMP,
               prdct.logical_load_timestamp = lv_logical_load_timestamp,
               prdct.effective_start_timestamp = p_effective_start_timestamp,
               prdct.rqustd_fnncng_ratio_dflt = new_prdct.rqustd_fnncng_ratio_dflt,
               prdct.is_deleted = new_prdct.is_deleted,
               prdct.is_pr_ccy_in_fractional_parts = new_prdct.is_pr_ccy_in_fractional_parts,
               prdct.point_multiplier = new_prdct.point_multiplier
        WHERE  (nrg_common.has_value_changed(prdct.product_setting_code,new_prdct.product_setting_code) = 1 OR
                nrg_common.has_value_changed(prdct.instrument_term,new_prdct.instrument_term) = 1 OR
                nrg_common.has_value_changed(prdct.short_name,new_prdct.short_name) = 1 OR
                nrg_common.has_value_changed(prdct.long_name,new_prdct.long_name) = 1 OR
                nrg_common.has_value_changed(prdct.underlying_name,new_prdct.underlying_name) = 1 OR
                nrg_common.has_value_changed(prdct.is_ccy_in_fractional_parts,new_prdct.is_ccy_in_fractional_parts) = 1 OR
                nrg_common.has_value_changed(prdct.contract_size,new_prdct.contract_size) = 1 OR
                nrg_common.has_value_changed(prdct.currency,new_prdct.currency) = 1 OR
                nrg_common.has_value_changed(prdct.fractional_part_ratio,new_prdct.fractional_part_ratio) = 1 OR
                nrg_common.has_value_changed(prdct.initial_published_date,new_prdct.initial_published_date) = 1 OR
                nrg_common.has_value_changed(prdct.is_findable,new_prdct.is_findable) = 1 OR
                nrg_common.has_value_changed(prdct.rqustd_fnncng_ratio_dflt,new_prdct.rqustd_fnncng_ratio_dflt) = 1 OR
                nrg_common.has_value_changed(prdct.is_deleted,new_prdct.is_deleted) = 1 OR
                nrg_common.has_value_changed(prdct.rm_code,new_prdct.rm_code) = 1 OR
                nrg_common.has_value_changed(prdct.is_pr_ccy_in_fractional_parts, new_prdct.is_pr_ccy_in_fractional_parts) = 1 OR
                nrg_common.has_value_changed(prdct.point_multiplier, new_prdct.point_multiplier) = 1 )
      WHEN NOT MATCHED THEN
        INSERT  (
                platform,
                instrument_code,
                wrapper_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                rm_code,
                product_setting_code,
                instrument_term,
                short_name,
                long_name,
                underlying_name,
                is_ccy_in_fractional_parts,
                contract_size,
                currency,
                fractional_part_ratio,
                initial_published_date,
                is_findable,
                rqustd_fnncng_ratio_dflt,
                is_deleted,
                is_pr_ccy_in_fractional_parts,
                point_multiplier
              )
        VALUES(
                new_prdct.platform,
                new_prdct.instrument_code,
                new_prdct.wrapper_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_prdct.rm_code,
                new_prdct.product_setting_code,
                new_prdct.instrument_term,
                new_prdct.short_name,
                new_prdct.long_name,
                new_prdct.underlying_name,
                new_prdct.is_ccy_in_fractional_parts,
                new_prdct.contract_size,
                new_prdct.currency,
                new_prdct.fractional_part_ratio,
                new_prdct.initial_published_date,
                new_prdct.is_findable,
                new_prdct.rqustd_fnncng_ratio_dflt,
                new_prdct.is_deleted,
                new_prdct.is_pr_ccy_in_fractional_parts,
                new_prdct.point_multiplier
              );

  MERGE INTO products prdct
    USING (SELECT DISTINCT platform,
                  wrapper_code,
                  instrument_code,
                  product_setting_code,
                  underlying_name,
                  is_ccy_in_fractional_parts,
                  contract_size,
                  currency,
                  fractional_part_ratio,
                  initial_published_date,
                  is_findable,
                  is_deleted,
                  rm_code,
                  rqustd_fnncng_ratio_dflt,
                  is_pr_ccy_in_fractional_parts,
                  point_multiplier
         FROM TABLE(CAST(p_products AS product_tab)) products, bi_ref.rm_mappings rm_map
         WHERE products.instrument_code = rm_map.ng_inst_id(+) AND
               platform <> 'NG') new_prdct
    ON (prdct.platform = new_prdct.platform AND
      prdct.instrument_code = new_prdct.instrument_code AND
      prdct.wrapper_code = new_prdct.wrapper_code)
      WHEN MATCHED THEN
      UPDATE
        SET    prdct.contract_size = new_prdct.contract_size,
               prdct.currency= new_prdct.currency,
               prdct.fractional_part_ratio = new_prdct.fractional_part_ratio,
               prdct.initial_published_date = new_prdct.initial_published_date,
               prdct.is_ccy_in_fractional_parts = new_prdct.is_ccy_in_fractional_parts,
               prdct.rm_code = new_prdct.rm_code,
               prdct.product_setting_code = new_prdct.product_setting_code,
               prdct.underlying_name = new_prdct.underlying_name,
               prdct.is_findable = new_prdct.is_findable,
               prdct.updated_by = p_user,
               prdct.update_timestamp = SYSTIMESTAMP,
               prdct.logical_load_timestamp = lv_logical_load_timestamp,
               prdct.effective_start_timestamp = p_effective_start_timestamp,
               prdct.rqustd_fnncng_ratio_dflt = new_prdct.rqustd_fnncng_ratio_dflt,
               prdct.is_deleted = new_prdct.is_deleted,
               prdct.is_pr_ccy_in_fractional_parts = new_prdct.is_pr_ccy_in_fractional_parts,
               prdct.point_multiplier = new_prdct.point_multiplier
        WHERE  (nrg_common.has_value_changed(prdct.product_setting_code,new_prdct.product_setting_code) = 1 OR
                nrg_common.has_value_changed(prdct.underlying_name,new_prdct.underlying_name) = 1 OR
                nrg_common.has_value_changed(prdct.is_ccy_in_fractional_parts,new_prdct.is_ccy_in_fractional_parts) = 1 OR
                nrg_common.has_value_changed(prdct.contract_size,new_prdct.contract_size) = 1 OR
                nrg_common.has_value_changed(prdct.currency,new_prdct.currency) = 1 OR
                nrg_common.has_value_changed(prdct.fractional_part_ratio,new_prdct.fractional_part_ratio) = 1 OR
                nrg_common.has_value_changed(prdct.initial_published_date,new_prdct.initial_published_date) = 1 OR
                nrg_common.has_value_changed(prdct.is_findable,new_prdct.is_findable) = 1 OR
                nrg_common.has_value_changed(prdct.rqustd_fnncng_ratio_dflt,new_prdct.rqustd_fnncng_ratio_dflt) = 1 OR
                nrg_common.has_value_changed(prdct.is_deleted,new_prdct.is_deleted) = 1 OR
                nrg_common.has_value_changed(prdct.rm_code,new_prdct.rm_code) = 1 OR
                nrg_common.has_value_changed(prdct.is_pr_ccy_in_fractional_parts,new_prdct.is_pr_ccy_in_fractional_parts) = 1 OR
                nrg_common.has_value_changed(prdct.point_multiplier,new_prdct.point_multiplier) = 1
                )
      WHEN NOT MATCHED THEN
        INSERT  (
                platform,
                instrument_code,
                wrapper_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                product_setting_code,
                underlying_name,
                is_ccy_in_fractional_parts,
                contract_size,
                currency,
                fractional_part_ratio,
                initial_published_date,
                is_findable,
                rm_code,
                rqustd_fnncng_ratio_dflt,
                is_deleted,
                is_pr_ccy_in_fractional_parts,
                point_multiplier
              )
        VALUES(
                new_prdct.platform,
                new_prdct.instrument_code,
                new_prdct.wrapper_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_prdct.product_setting_code,
                new_prdct.underlying_name,
                new_prdct.is_ccy_in_fractional_parts,
                new_prdct.contract_size,
                new_prdct.currency,
                new_prdct.fractional_part_ratio,
                new_prdct.initial_published_date,
                new_prdct.is_findable,
                new_prdct.rm_code,
                new_prdct.rqustd_fnncng_ratio_dflt,
                new_prdct.is_deleted,
                new_prdct.is_pr_ccy_in_fractional_parts,
                new_prdct.point_multiplier
              );



    MERGE INTO mm_products prdct
    USING (SELECT products.*,
                rm_map.rm_code,
          pht_mapping.prophet_symbol,
          CASE
                     WHEN products.platform = 'MMSB' THEN
                          NULL
                     --WHEN products.platform = 'MMCFD' AND c.iso3 = 'GBR' AND UPPER(i.instrument_type) = 'COMPANIES' THEN
                     WHEN products.platform = 'MMCFD' AND c.iso3 = 'GBR' AND UPPER(i.instrument_type) = 'SHARES' THEN
                          1
                     ELSE
                          0.01
                  END  cfd_multiplier_override
         FROM TABLE(CAST(p_products AS product_tab)) products,
            bi_ref.rm_mappings rm_map,
        bi_ref.prophet_mappings pht_mapping,
        bi_ods.instruments i,
        bi_ods.countries c
         WHERE products.mm_instrument_id = rm_map.mm_inst_id(+) AND
               products.mm_instrument_id = pht_mapping.instrument_id(+) AND
               products.platform <> 'NG' AND
               products.instrument_code = i.instrument_code AND
               i.country_code = c.country_code(+)) new_prdct
    ON (prdct.mm_instrument_id = new_prdct.mm_instrument_id AND
        prdct.platform = new_prdct.platform AND
        prdct.instrument_code = new_prdct.instrument_code AND
        prdct.wrapper_code = new_prdct.wrapper_code)
    WHEN MATCHED THEN
      UPDATE
        SET
            prdct.logical_load_timestamp    = lv_logical_load_timestamp,
            prdct.updated_by                = p_user,
            prdct.update_timestamp          = SYSTIMESTAMP,
            prdct.effective_start_timestamp = p_effective_start_timestamp,
            prdct.rm_code                   = new_prdct.rm_code,
            prdct.short_name                = new_prdct.short_name,
            prdct.long_name                 = new_prdct.long_name,
            prdct.expiry_date               = new_prdct.expiry_date,
            prdct.cfd_multiplier            = new_prdct.cfd_multiplier_override,
            prdct.point                     = new_prdct.point,
            prdct.currency_code             = new_prdct.currency_code,
            prdct.term                      = new_prdct.term,
            prdct.term_description          = new_prdct.term_description,
            prdct.currency_description      = new_prdct.mm_currency,
            prdct.prophet_symbol            = new_prdct.prophet_symbol,
            prdct.is_deleted                = new_prdct.is_deleted
        WHERE (nrg_common.has_value_changed(prdct.short_name,new_prdct.short_name) = 1 OR
               nrg_common.has_value_changed(prdct.long_name,new_prdct.long_name) = 1 OR
               nrg_common.has_value_changed(prdct.expiry_date,new_prdct.expiry_date) = 1 OR
               nrg_common.has_value_changed(prdct.cfd_multiplier,new_prdct.cfd_multiplier) = 1 OR
               nrg_common.has_value_changed(prdct.point,new_prdct.point) = 1 OR
               nrg_common.has_value_changed(prdct.currency_code,new_prdct.currency_code) = 1 OR
               nrg_common.has_value_changed(prdct.term,new_prdct.term) = 1 OR
               nrg_common.has_value_changed(prdct.term_description,new_prdct.term_description) = 1 OR
               nrg_common.has_value_changed(prdct.currency_description,new_prdct.currency) = 1 OR
               nrg_common.has_value_changed(prdct.is_deleted,new_prdct.is_deleted) = 1)
      WHEN NOT MATCHED THEN
        INSERT (
                  prdct.mm_instrument_id,
                  prdct.logical_load_timestamp,
                  prdct.created_by,
                  prdct.create_timestamp,
                  prdct.updated_by,
                  prdct.update_timestamp,
                  prdct.effective_start_timestamp,
                  prdct.platform,
                  prdct.instrument_code,
                  prdct.wrapper_code,
                  prdct.rm_code,
                  prdct.short_name,
                  prdct.long_name,
                  prdct.expiry_date,
                  prdct.cfd_multiplier,
                  prdct.point,
                  prdct.currency_code,
                  prdct.term,
                  prdct.term_description,
                  prdct.currency_description,
                  prdct.prophet_symbol,
                  prdct.is_deleted
              )
        VALUES(
                  new_prdct.mm_instrument_id,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  new_prdct.platform,
                  new_prdct.instrument_code,
                  new_prdct.wrapper_code,
                  new_prdct.rm_code,
                  new_prdct.short_name,
                  new_prdct.long_name,
                  new_prdct.expiry_date,
                  new_prdct.cfd_multiplier_override,
                  new_prdct.point,
                  new_prdct.currency_code,
                  new_prdct.term,
                  new_prdct.term_description,
                  new_prdct.mm_currency,
                  new_prdct.prophet_symbol,
                  new_prdct.is_deleted
              );


    END;
    PROCEDURE put_economic_cal_events_lang(p_user                            IN products.created_by%TYPE,
                                           p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                           p_logical_load_timestamp          IN economic_calendar_events.logical_load_timestamp%TYPE,
                                           p_econ_event_code                 IN economic_calendar_events.econ_event_code%TYPE,
                                           p_economic_cal_events_lang        IN economic_cal_events_lang_tab) IS

    BEGIN
      --
      --Write History
      --


      MERGE INTO ecnmc_clndr_evnt_languages_h old_recs
      USING (SELECT old_records.*, p_effective_start_timestamp effective_end_timestamp
             FROM ecnmc_clndr_evnt_languages old_records,
                  TABLE(CAST(p_economic_cal_events_lang AS economic_cal_events_lang_tab)) new_records
            WHERE old_records.econ_event_code = new_records.econ_event_code AND
                  old_records.language_code = new_records.language_code
                  )new_recs
      ON (old_recs.econ_event_code = new_recs.econ_event_code AND
          old_recs.language_code = new_recs.language_code AND
          old_recs.effective_start_timestamp = new_recs.effective_start_timestamp)
      WHEN MATCHED THEN
        UPDATE
        SET logical_load_timestamp = new_recs.logical_load_timestamp,
            created_by = new_recs.created_by,
            create_timestamp = new_recs.create_timestamp,
            updated_by = new_recs.updated_by,
            update_timestamp = new_recs.update_timestamp,
            effective_end_timestamp = new_recs.effective_end_timestamp,
            action = 'U',
            action_timestamp = SYSTIMESTAMP,
            econ_event_default_name = new_recs.econ_event_default_name,
            econ_event_name = new_recs.econ_event_name,
            is_deleted = new_recs.is_deleted
        WHERE (nrg_common.has_value_changed(old_recs.econ_event_default_name, new_recs.econ_event_default_name) = 1 OR
               nrg_common.has_value_changed(old_recs.econ_event_name, new_recs.econ_event_name) = 1 OR
               nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1)
      WHEN NOT MATCHED THEN
        INSERT (econ_event_code,
                language_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                effective_end_timestamp,
                action,
                action_timestamp,
                econ_event_default_name,
                econ_event_name,
                is_deleted)
         VALUES(new_recs.econ_event_code,
                new_recs.language_code,
                new_recs.logical_load_timestamp,
                new_recs.created_by,
                new_recs.create_timestamp,
                new_recs.updated_by,
                new_recs.update_timestamp,
                new_recs.effective_start_timestamp,
                new_recs.effective_end_timestamp,
                'U',
                SYSTIMESTAMP,
                new_recs.econ_event_default_name,
                new_recs.econ_event_name,
                new_recs.is_deleted);

      --
      --Soft Delete Deleted Records
      --

      UPDATE ecnmc_clndr_evnt_languages
      SET is_deleted = 'YES',
          logical_load_timestamp = p_logical_load_timestamp,
          effective_start_timestamp = p_effective_start_timestamp,
          update_timestamp = SYSTIMESTAMP,
          updated_by = p_user
      WHERE econ_event_code = p_econ_event_code AND
            language_code NOT IN (SELECT language_code
                                  FROM TABLE(CAST(p_economic_cal_events_lang AS economic_cal_events_lang_tab))) AND
            is_deleted = 'NO';

      --
      --Update Base Table
      --

      MERGE INTO ecnmc_clndr_evnt_languages old_recs
      USING (SELECT new_records.*,
                    p_effective_start_timestamp effective_start_timestamp,
                    p_logical_load_timestamp logical_load_timestamp,
                    p_user created_by,
                    SYSTIMESTAMP create_timestamp,
                    p_user updated_by,
                    SYSTIMESTAMP update_timestamp,
                    'NO' is_deleted
             FROM TABLE(CAST(p_economic_cal_events_lang AS economic_cal_events_lang_tab)) new_records)new_recs
      ON (old_recs.econ_event_code = new_recs.econ_event_code AND
          old_recs.language_code = new_recs.language_code)
      WHEN MATCHED THEN
        UPDATE
        SET logical_load_timestamp = new_recs.logical_load_timestamp,
            created_by = new_recs.created_by,
            create_timestamp = new_recs.create_timestamp,
            updated_by = new_recs.updated_by,
            update_timestamp = new_recs.update_timestamp,
            effective_start_timestamp = new_recs.effective_start_timestamp,
            econ_event_default_name = new_recs.econ_event_default_name,
            econ_event_name = new_recs.econ_event_name,
            is_deleted = new_recs.is_deleted
       WHERE
          (nrg_common.has_value_changed(old_recs.econ_event_default_name, new_recs.econ_event_default_name) = 1 OR
           nrg_common.has_value_changed(old_recs.econ_event_name, new_recs.econ_event_name) = 1 OR
           nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1)
      WHEN NOT MATCHED THEN
        INSERT (econ_event_code,
                language_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                econ_event_default_name,
                econ_event_name,
                is_deleted)
         VALUES(new_recs.econ_event_code,
                new_recs.language_code,
                new_recs.logical_load_timestamp,
                new_recs.created_by,
                new_recs.create_timestamp,
                new_recs.updated_by,
                new_recs.update_timestamp,
                new_recs.effective_start_timestamp,
                new_recs.econ_event_default_name,
                new_recs.econ_event_name,
                new_recs.is_deleted);
    END;

    PROCEDURE put_economic_calendar_events(p_user                            IN products.created_by%TYPE,
                                           p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                           p_economic_calendar_events        IN economic_calendar_events_tab)
    IS
      TYPE tab_economic_calendar_events IS TABLE OF economic_calendar_events%ROWTYPE;

      lv_economic_calendar_events tab_economic_calendar_events;

      p_logical_load_timestamp TIMESTAMP(6);

    BEGIN
      p_logical_load_timestamp := SYSTIMESTAMP;
      --
      --Write updates
      --
      MERGE INTO economic_calendar_events_h old_rec
      USING (SELECT old_records.*, p_effective_start_timestamp effective_end_timestamp
             FROM economic_calendar_events old_records, TABLE(CAST(p_economic_calendar_events AS economic_calendar_events_tab))new_records
             WHERE old_records.econ_event_code = new_records.econ_event_code(+) AND
                  (nrg_common.has_value_changed(old_records.econ_event_name, new_records.econ_event_name) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_description, new_records.econ_event_description) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_measures, new_records.econ_event_measures) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_care, new_records.econ_event_care) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_frequency, new_records.econ_event_frequency) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_source, new_records.econ_event_source) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_effect, new_records.econ_event_effect) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_impact, new_records.econ_event_impact) = 1 OR
                   nrg_common.has_value_changed(old_records.is_visible, new_records.is_visible) = 1 OR
                   nrg_common.has_value_changed(old_records.econ_event_iso2, new_records.econ_event_iso2) = 1 OR
                   nrg_common.has_value_changed(old_records.has_specific_due_time, new_records.has_specific_due_time) = 1 OR
                   nrg_common.has_value_changed(old_records.event_codifier, new_records.event_codifier) = 1 OR
                   nrg_common.has_value_changed(old_records.event_code, new_records.event_code) = 1 OR
                   nrg_common.has_value_changed(old_records.reuters_event_code, new_records.reuters_event_code) = 1 OR
                   nrg_common.has_value_changed(old_records.organisation_code, new_records.organisation_code) = 1 OR
                   nrg_common.has_value_changed(old_records.is_deleted, CASE WHEN new_records.econ_event_code IS NULL THEN 'YES' ELSE 'NO' END) = 1)) new_rec
      ON (old_rec.econ_event_code = new_rec.econ_event_code AND
          old_rec.effective_start_timestamp = new_rec.effective_start_timestamp)
      WHEN
        MATCHED THEN
          UPDATE SET
            logical_load_timestamp = new_rec.logical_load_timestamp,
            created_by = new_rec.created_by,
            create_timestamp = new_rec.create_timestamp,
            updated_by = new_rec.updated_by,
            update_timestamp = new_rec.update_timestamp,
            action = 'U',
            action_timestamp = SYSTIMESTAMP,
            effective_end_timestamp = new_rec.effective_end_timestamp,
            econ_event_name = new_rec.econ_event_name,
            econ_event_description = new_rec.econ_event_description,
            econ_event_measures = new_rec.econ_event_measures,
            econ_event_care = new_rec.econ_event_care,
            econ_event_frequency = new_rec.econ_event_frequency,
            econ_event_source = new_rec.econ_event_source,
            econ_event_effect = new_rec.econ_event_effect,
            econ_event_impact = new_rec.econ_event_impact,
            is_visible = new_rec.is_visible,
            econ_event_iso2 = new_rec.econ_event_iso2,
            has_specific_due_time = new_rec.has_specific_due_time,
            event_codifier = new_rec.event_codifier,
            event_code = new_rec.event_code,
            reuters_event_code = new_rec.reuters_event_code,
            organisation_code = new_rec.organisation_code,
            is_deleted = new_rec.is_deleted
      WHEN
        NOT MATCHED THEN
              INSERT (econ_event_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      action,
                      action_timestamp,
                      effective_start_timestamp,
                      effective_end_timestamp,
                      econ_event_name,
                      econ_event_description,
                      econ_event_measures,
                      econ_event_care,
                      econ_event_frequency,
                      econ_event_source,
                      econ_event_effect,
                      econ_event_impact,
                      is_visible,
                      econ_event_iso2,
                      has_specific_due_time,
                      event_codifier,
                      event_code,
                      reuters_event_code,
                      organisation_code,
                      is_deleted)
              VALUES (new_rec.econ_event_code,
                      new_rec.logical_load_timestamp,
                      new_rec.created_by,
                      new_rec.create_timestamp,
                      new_rec.updated_by,
                      new_rec.update_timestamp,
                      'U',
                      SYSTIMESTAMP,
                      new_rec.effective_start_timestamp,
                      new_rec.effective_end_timestamp,
                      new_rec.econ_event_name,
                      new_rec.econ_event_description,
                      new_rec.econ_event_measures,
                      new_rec.econ_event_care,
                      new_rec.econ_event_frequency,
                      new_rec.econ_event_source,
                      new_rec.econ_event_effect,
                      new_rec.econ_event_impact,
                      new_rec.is_visible,
                      new_rec.econ_event_iso2,
                      new_rec.has_specific_due_time,
                      new_rec.event_codifier,
                      new_rec.event_code,
                      new_rec.reuters_event_code,
                      new_rec.organisation_code,
                      new_rec.is_deleted);


      --
      --Soft Delete Events
      --

      UPDATE economic_calendar_events
      SET is_deleted = 'YES',
          logical_load_timestamp = p_logical_load_timestamp,
          effective_start_timestamp = p_effective_start_timestamp,
          update_timestamp = SYSTIMESTAMP,
          updated_by = p_user
      WHERE econ_event_code NOT IN (SELECT econ_event_code
                                    FROM TABLE(CAST(p_economic_calendar_events AS economic_calendar_events_tab))) AND
            is_deleted = 'NO';

      --
      --Insert update new Records
      --

      MERGE INTO economic_calendar_events old_rec
      USING (SELECT new_records.*,
                    p_effective_start_timestamp effective_start_timestamp,
                    p_logical_load_timestamp logical_load_timestamp,
                    p_user created_by,
                    SYSTIMESTAMP create_timestamp,
                    p_user updated_by,
                    SYSTIMESTAMP update_timestamp,
                    'NO' is_deleted
             FROM TABLE(CAST(p_economic_calendar_events AS economic_calendar_events_tab))new_records) new_rec
      ON (old_rec.econ_event_code = new_rec.econ_event_code)
      WHEN
        MATCHED THEN
          UPDATE SET
            logical_load_timestamp = new_rec.logical_load_timestamp,
            updated_by = new_rec.updated_by,
            update_timestamp = new_rec.update_timestamp,
            effective_start_timestamp = new_rec.effective_start_timestamp,
            econ_event_name = new_rec.econ_event_name,
            econ_event_description = new_rec.econ_event_description,
            econ_event_measures = new_rec.econ_event_measures,
            econ_event_care = new_rec.econ_event_care,
            econ_event_frequency = new_rec.econ_event_frequency,
            econ_event_source = new_rec.econ_event_source,
            econ_event_effect = new_rec.econ_event_effect,
            econ_event_impact = new_rec.econ_event_impact,
            is_visible = new_rec.is_visible,
            econ_event_iso2 = new_rec.econ_event_iso2,
            has_specific_due_time = new_rec.has_specific_due_time,
            event_codifier = new_rec.event_codifier,
            event_code = new_rec.event_code,
            reuters_event_code = new_rec.reuters_event_code,
            organisation_code = new_rec.organisation_code,
            is_deleted = new_rec.is_deleted
   WHERE  (nrg_common.has_value_changed(old_rec.econ_event_name, new_rec.econ_event_name) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_description, new_rec.econ_event_description) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_measures, new_rec.econ_event_measures) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_care, new_rec.econ_event_care) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_frequency, new_rec.econ_event_frequency) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_source, new_rec.econ_event_source) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_effect, new_rec.econ_event_effect) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_impact, new_rec.econ_event_impact) = 1 OR
           nrg_common.has_value_changed(old_rec.is_visible, new_rec.is_visible) = 1 OR
           nrg_common.has_value_changed(old_rec.econ_event_iso2, new_rec.econ_event_iso2) = 1 OR
           nrg_common.has_value_changed(old_rec.has_specific_due_time, new_rec.has_specific_due_time) = 1 OR
           nrg_common.has_value_changed(old_rec.event_codifier, new_rec.event_codifier) = 1 OR
           nrg_common.has_value_changed(old_rec.event_code, new_rec.event_code) = 1 OR
           nrg_common.has_value_changed(old_rec.reuters_event_code, new_rec.reuters_event_code) = 1 OR
           nrg_common.has_value_changed(old_rec.organisation_code, new_rec.organisation_code) = 1)
      WHEN
        NOT MATCHED THEN
              INSERT (econ_event_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      econ_event_name,
                      econ_event_description,
                      econ_event_measures,
                      econ_event_care,
                      econ_event_frequency,
                      econ_event_source,
                      econ_event_effect,
                      econ_event_impact,
                      is_visible,
                      econ_event_iso2,
                      has_specific_due_time,
                      event_codifier,
                      event_code,
                      reuters_event_code,
                      organisation_code,
                      is_deleted)
              VALUES (new_rec.econ_event_code,
                      new_rec.logical_load_timestamp,
                      new_rec.created_by,
                      new_rec.create_timestamp,
                      new_rec.updated_by,
                      new_rec.update_timestamp,
                      new_rec.effective_start_timestamp,
                      new_rec.econ_event_name,
                      new_rec.econ_event_description,
                      new_rec.econ_event_measures,
                      new_rec.econ_event_care,
                      new_rec.econ_event_frequency,
                      new_rec.econ_event_source,
                      new_rec.econ_event_effect,
                      new_rec.econ_event_impact,
                      new_rec.is_visible,
                      new_rec.econ_event_iso2,
                      new_rec.has_specific_due_time,
                      new_rec.event_codifier,
                      new_rec.event_code,
                      new_rec.reuters_event_code,
                      new_rec.organisation_code,
                      new_rec.is_deleted);

      --
      --Put economic event languages
      --

      FOR lv_cnt IN 1..p_economic_calendar_events.count LOOP
        IF p_economic_calendar_events(lv_cnt).languages IS NOT NULL THEN
          put_economic_cal_events_lang(p_user                            => p_user,
                                       p_effective_start_timestamp       => p_effective_start_timestamp,
                                       p_logical_load_timestamp          => p_logical_load_timestamp,
                                       p_econ_event_code                 => p_economic_calendar_events(lv_cnt).econ_event_code,
                                       p_economic_cal_events_lang        => p_economic_calendar_events(lv_cnt).languages);
        END IF;
      END LOOP;
    END;

    PROCEDURE put_inst_to_economic_events(p_user                            IN products.created_by%TYPE,
                                          p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                          p_inst_to_economic_events         IN economic_cal_events_inst_tab) IS

      p_logical_load_timestamp TIMESTAMP(6);
    BEGIN

      p_logical_load_timestamp := SYSTIMESTAMP;
      --
      --Write History
      --

      MERGE INTO inst_to_ecnmc_clndr_evnt_h old_recs
      USING (SELECT old_records.*, p_effective_start_timestamp effective_end_timestamp
             FROM inst_to_ecnmc_clndr_evnt old_records,
                  TABLE(CAST(p_inst_to_economic_events AS economic_cal_events_inst_tab)) new_records
             WHERE old_records.inst_to_econ_event_code = new_records.inst_to_econ_event_code(+)
             )new_recs
      ON (old_recs.inst_to_econ_event_code = new_recs.inst_to_econ_event_code AND
          old_recs.effective_start_timestamp = new_recs.effective_start_timestamp)
      WHEN MATCHED THEN
        UPDATE
          SET logical_load_timestamp = new_recs.logical_load_timestamp,
              created_by = new_recs.created_by,
              create_timestamp = new_recs.create_timestamp,
              updated_by = new_recs.updated_by,
              update_timestamp = new_recs.update_timestamp,
              effective_end_timestamp = new_recs.effective_end_timestamp,
              action = 'U',
              action_timestamp = SYSTIMESTAMP,
              instrument_code = new_recs.instrument_code,
              econ_event_code = new_recs.econ_event_code,
              is_deleted = new_recs.is_deleted
          WHERE (nrg_common.has_value_changed(old_recs.instrument_code, new_recs.instrument_code) = 1 OR
                 nrg_common.has_value_changed(old_recs.econ_event_code, new_recs.econ_event_code) = 1 )
      WHEN NOT MATCHED THEN
        INSERT (inst_to_econ_event_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                effective_end_timestamp,
                action,
                action_timestamp,
                instrument_code,
                econ_event_code,
                is_deleted)
        VALUES (new_recs.inst_to_econ_event_code,
                new_recs.logical_load_timestamp,
                new_recs.created_by,
                new_recs.create_timestamp,
                new_recs.updated_by,
                new_recs.update_timestamp,
                new_recs.effective_start_timestamp,
                new_recs.effective_end_timestamp,
                'U',
                SYSTIMESTAMP,
                new_recs.instrument_code,
                new_recs.econ_event_code,
                new_recs.is_deleted);

      --
      --Soft dlete Deleted Records
      --

      UPDATE inst_to_ecnmc_clndr_evnt
      SET is_deleted = 'YES',
          logical_load_timestamp = p_logical_load_timestamp,
          effective_start_timestamp = p_effective_start_timestamp,
          update_timestamp = SYSTIMESTAMP,
          updated_by = p_user
      WHERE inst_to_econ_event_code NOT IN (SELECT inst_to_econ_event_code
                                            FROM TABLE(CAST(p_inst_to_economic_events AS economic_cal_events_inst_tab))) AND
            is_deleted = 'NO';

      --
      --Update the base table
      --

      MERGE INTO inst_to_ecnmc_clndr_evnt old_recs
      USING (SELECT new_records.*,
                    p_effective_start_timestamp effective_start_timestamp,
                    p_logical_load_timestamp logical_load_timestamp,
                    p_user created_by,
                    SYSTIMESTAMP create_timestamp,
                    p_user updated_by,
                    SYSTIMESTAMP update_timestamp,
                    'NO' is_deleted
             FROM TABLE(CAST(p_inst_to_economic_events AS economic_cal_events_inst_tab)) new_records
             )new_recs
      ON (old_recs.inst_to_econ_event_code = new_recs.inst_to_econ_event_code)
      WHEN MATCHED THEN
        UPDATE
          SET logical_load_timestamp = new_recs.logical_load_timestamp,
              created_by = new_recs.created_by,
              create_timestamp = new_recs.create_timestamp,
              updated_by = new_recs.updated_by,
              update_timestamp = new_recs.update_timestamp,
              effective_start_timestamp = new_recs.effective_start_timestamp,
              instrument_code = new_recs.instrument_code,
              econ_event_code = new_recs.econ_event_code,
              is_deleted = new_recs.is_deleted
         WHERE (nrg_common.has_value_changed(old_recs.instrument_code, new_recs.instrument_code) = 1 OR
                nrg_common.has_value_changed(old_recs.econ_event_code, new_recs.econ_event_code) = 1 OR
                nrg_common.has_value_changed(old_recs.is_deleted, 'NO') = 1)
      WHEN NOT MATCHED THEN
        INSERT (inst_to_econ_event_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                instrument_code,
                econ_event_code,
                is_deleted)
        VALUES (new_recs.inst_to_econ_event_code,
                new_recs.logical_load_timestamp,
                new_recs.created_by,
                new_recs.create_timestamp,
                new_recs.updated_by,
                new_recs.update_timestamp,
                new_recs.effective_start_timestamp,
                new_recs.instrument_code,
                new_recs.econ_event_code,
                new_recs.is_deleted);

    END;

    PROCEDURE put_tax_treaties(p_user                            IN products.created_by%TYPE,
                               p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                               p_tax_treaties                    IN tax_treaty_relations_tab) IS

      p_logical_load_timestamp TIMESTAMP(6);

    BEGIN

      p_logical_load_timestamp := SYSTIMESTAMP;
      --
      --Write History
      --

      MERGE INTO tax_treaty_relations_h old_recs
      USING (SELECT old_records.*, p_effective_start_timestamp effective_end_timestamp
             FROM tax_treaty_relations old_records,
                  TABLE(CAST(p_tax_treaties AS tax_treaty_relations_tab)) new_records
             WHERE old_records.tax_treaty_relation_code = new_records.tax_treaty_relation_code(+) AND
                   (nrg_common.has_value_changed(old_records.treaty_country_code, new_records.treaty_country_code) = 1 OR
                   nrg_common.has_value_changed(old_records.reporting_country_code, new_records.reporting_country_code) = 1 OR
                   nrg_common.has_value_changed(old_records.treaty_wht_tax_rate, new_records.treaty_wht_tax_rate) = 1 OR
                   nrg_common.has_value_changed(old_records.valid_from, new_records.valid_from) = 1 OR
                   nrg_common.has_value_changed(old_records.is_deleted, CASE WHEN new_records.tax_treaty_relation_code IS NULL THEN 'YES' ELSE 'NO' END) = 1))new_recs
      ON (old_recs.tax_treaty_relation_code = new_recs.tax_treaty_relation_code AND
          old_recs.effective_start_timestamp = new_recs.effective_start_timestamp)
      WHEN MATCHED THEN
        UPDATE
        SET logical_load_timestamp = new_recs.logical_load_timestamp,
            created_by = new_recs.created_by,
            create_timestamp = new_recs.create_timestamp,
            updated_by = new_recs.updated_by,
            update_timestamp = new_recs.update_timestamp,
            effective_end_timestamp = new_recs.effective_end_timestamp,
            action = 'U',
            action_timestamp = SYSTIMESTAMP,
            treaty_country_code = new_recs.treaty_country_code,
            reporting_country_code = new_recs.reporting_country_code,
            treaty_wht_tax_rate = new_recs.treaty_wht_tax_rate,
            valid_from = new_recs.valid_from,
            is_deleted = new_recs.is_deleted
      WHEN NOT MATCHED THEN
        INSERT (tax_treaty_relation_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                effective_end_timestamp,
                action,
                action_timestamp,
                treaty_country_code,
                reporting_country_code,
                treaty_wht_tax_rate,
                valid_from,
                is_deleted)
        VALUES (new_recs.tax_treaty_relation_code,
                new_recs.logical_load_timestamp,
                new_recs.created_by,
                new_recs.create_timestamp,
                new_recs.updated_by,
                new_recs.update_timestamp,
                new_recs.effective_start_timestamp,
                new_recs.effective_end_timestamp,
                'U',
                SYSTIMESTAMP,
                new_recs.treaty_country_code,
                new_recs.reporting_country_code,
                new_recs.treaty_wht_tax_rate,
                new_recs.valid_from,
                new_recs.is_deleted);

      --
      --Soft Delete Deleted Records
      --

      UPDATE tax_treaty_relations
      SET is_deleted = 'YES',
          logical_load_timestamp = p_logical_load_timestamp,
          effective_start_timestamp = p_effective_start_timestamp,
          update_timestamp = SYSTIMESTAMP,
          updated_by = p_user
      WHERE tax_treaty_relation_code NOT IN (SELECT tax_treaty_relation_code
                                             FROM TABLE(CAST(p_tax_treaties AS tax_treaty_relations_tab))) AND
            is_deleted = 'NO';

      --
      --Update base table
      --

      MERGE INTO tax_treaty_relations old_recs
      USING (SELECT new_records.*,
                    p_effective_start_timestamp effective_start_timestamp,
                    p_logical_load_timestamp logical_load_timestamp,
                    p_user created_by,
                    SYSTIMESTAMP create_timestamp,
                    p_user updated_by,
                    SYSTIMESTAMP update_timestamp,
                    'NO' is_deleted
             FROM TABLE(CAST(p_tax_treaties AS tax_treaty_relations_tab)) new_records)new_recs
      ON (old_recs.tax_treaty_relation_code = new_recs.tax_treaty_relation_code)
      WHEN MATCHED THEN
        UPDATE
        SET logical_load_timestamp = new_recs.logical_load_timestamp,
            created_by = new_recs.created_by,
            create_timestamp = new_recs.create_timestamp,
            updated_by = new_recs.updated_by,
            update_timestamp = new_recs.update_timestamp,
            effective_start_timestamp = new_recs.effective_start_timestamp,
            treaty_country_code = new_recs.treaty_country_code,
            reporting_country_code = new_recs.reporting_country_code,
            treaty_wht_tax_rate = new_recs.treaty_wht_tax_rate,
            valid_from = new_recs.valid_from,
            is_deleted = new_recs.is_deleted
      WHERE (nrg_common.has_value_changed(old_recs.treaty_country_code, new_recs.treaty_country_code) = 1 OR
             nrg_common.has_value_changed(old_recs.reporting_country_code, new_recs.reporting_country_code) = 1 OR
             nrg_common.has_value_changed(old_recs.treaty_wht_tax_rate, new_recs.treaty_wht_tax_rate) = 1 OR
             nrg_common.has_value_changed(old_recs.valid_from, new_recs.valid_from) = 1 OR
             nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1)
      WHEN NOT MATCHED THEN
        INSERT (tax_treaty_relation_code,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                treaty_country_code,
                reporting_country_code,
                treaty_wht_tax_rate,
                valid_from,
                is_deleted)
        VALUES (new_recs.tax_treaty_relation_code,
                new_recs.logical_load_timestamp,
                new_recs.created_by,
                new_recs.create_timestamp,
                new_recs.updated_by,
                new_recs.update_timestamp,
                new_recs.effective_start_timestamp,
                new_recs.treaty_country_code,
                new_recs.reporting_country_code,
                new_recs.treaty_wht_tax_rate,
                new_recs.valid_from,
                new_recs.is_deleted);
    END;

  PROCEDURE put_product_wrapper(p_user                           IN products.created_by%TYPE,
                                p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                p_product_wrappers                IN product_wrapper_tab) IS

    TYPE ltab_old_product_wrappers IS TABLE OF product_wrappers%ROWTYPE;
    lv_old_product_wrappers ltab_old_product_wrappers;

    lv_logical_load_timestamp TIMESTAMP(6);

  BEGIN
    lv_logical_load_timestamp := SYSTIMESTAMP;
    --
    -- Select The Product wrappers that have got updated
    --

    SELECT prd_wrp.*
    BULK COLLECT INTO lv_old_product_wrappers
    FROM product_wrappers prd_wrp, TABLE(CAST(p_product_wrappers AS product_wrapper_tab)) new_prd_wrp
    WHERE prd_wrp.wrapper_code = new_prd_wrp.wrapper_code (+) AND
          (nrg_common.has_value_changed(prd_wrp.product_wrapper_type,new_prd_wrp.product_wrapper_type)= 1 OR
           nrg_common.has_value_changed(prd_wrp.short_name,new_prd_wrp.short_name)= 1 OR
           nrg_common.has_value_changed(prd_wrp.name_long,new_prd_wrp.name_long)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_quantity_amount_available,new_prd_wrp.is_quantity_amount_available)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_quantity_units_available,new_prd_wrp.is_quantity_units_available)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_units_administrable,new_prd_wrp.is_qntty_units_administrable)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_amount_administrable,new_prd_wrp.is_qntty_amount_administrable)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_amt_per_pnt_avlbl,new_prd_wrp.is_qntty_amt_per_pnt_avlbl)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_amt_per_pnt_admnstrbl,new_prd_wrp.is_qntty_amt_per_pnt_admnstrbl)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_deleted,new_prd_wrp.is_deleted)= 1)
    FOR UPDATE OF prd_wrp.wrapper_code;


    --
    --Soft Delete the records
    --

    UPDATE product_wrappers
    SET    logical_load_timestamp         = lv_logical_load_timestamp,
           updated_by                     = p_user,
           update_timestamp               = SYSTIMESTAMP,
           effective_start_timestamp      = p_effective_start_timestamp,
           is_deleted                     = 'YES'
    WHERE  wrapper_code NOT IN (SELECT wrapper_code
                                FROM TABLE(CAST(p_product_wrappers AS product_wrapper_tab)))
      AND
          is_deleted                = 'NO' ;

    MERGE INTO product_wrappers wrpr
    USING (SELECT *
         FROM TABLE(CAST(p_product_wrappers AS product_wrapper_tab))) new_wrpr
    ON (wrpr.wrapper_code = new_wrpr.wrapper_code)
    WHEN MATCHED THEN
      UPDATE
      SET
            logical_load_timestamp         = lv_logical_load_timestamp,
            updated_by                     = p_user,
            update_timestamp               = SYSTIMESTAMP,
            effective_start_timestamp      = p_effective_start_timestamp,
            product_wrapper_type           = new_wrpr.product_wrapper_type,
            short_name                     = new_wrpr.short_name,
            name_long                      = new_wrpr.name_long,
            is_quantity_amount_available   = new_wrpr.is_quantity_amount_available,
            is_quantity_units_available    = new_wrpr.is_quantity_units_available,
            is_qntty_units_administrable   = new_wrpr.is_qntty_units_administrable,
            is_qntty_amount_administrable  = new_wrpr.is_qntty_amount_administrable,
            is_qntty_amt_per_pnt_avlbl     = new_wrpr.is_qntty_amt_per_pnt_avlbl,
            is_qntty_amt_per_pnt_admnstrbl = new_wrpr.is_qntty_amt_per_pnt_admnstrbl,
            is_deleted                     = new_wrpr.is_deleted
      WHERE (nrg_common.has_value_changed(wrpr.product_wrapper_type,new_wrpr.product_wrapper_type)= 1 OR
             nrg_common.has_value_changed(wrpr.short_name,new_wrpr.short_name)= 1 OR
             nrg_common.has_value_changed(wrpr.name_long,new_wrpr.name_long)= 1 OR
             nrg_common.has_value_changed(wrpr.is_quantity_amount_available,new_wrpr.is_quantity_amount_available)= 1 OR
             nrg_common.has_value_changed(wrpr.is_quantity_units_available,new_wrpr.is_quantity_units_available)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_units_administrable,new_wrpr.is_qntty_units_administrable)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_amount_administrable,new_wrpr.is_qntty_amount_administrable)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_amt_per_pnt_avlbl,new_wrpr.is_qntty_amt_per_pnt_avlbl)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_amt_per_pnt_admnstrbl,new_wrpr.is_qntty_amt_per_pnt_admnstrbl)= 1 OR
             nrg_common.has_value_changed(wrpr.is_deleted,new_wrpr.is_deleted)= 1)
    WHEN NOT MATCHED THEN
      INSERT  (
                      wrapper_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      product_wrapper_type,
                      short_name,
                      name_long,
                      is_quantity_amount_available,
                      is_quantity_units_available,
                      is_qntty_units_administrable,
                      is_qntty_amount_administrable,
                      is_qntty_amt_per_pnt_avlbl,
                      is_qntty_amt_per_pnt_admnstrbl,
                      is_deleted
                  )
        VALUES(
                new_wrpr.wrapper_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_wrpr.product_wrapper_type,
                new_wrpr.short_name,
                new_wrpr.name_long,
                new_wrpr.is_quantity_amount_available,
                new_wrpr.is_quantity_units_available,
                new_wrpr.is_qntty_units_administrable,
                new_wrpr.is_qntty_amount_administrable,
                new_wrpr.is_qntty_amt_per_pnt_avlbl,
                new_wrpr.is_qntty_amt_per_pnt_admnstrbl,
                new_wrpr.is_deleted
              );

    FOR lv_cnt IN 1..lv_old_product_wrappers.COUNT LOOP
      put_history(p_old_wrapper_record            => lv_old_product_wrappers(lv_cnt),
                     p_effective_end_timestamp    => p_effective_start_timestamp,
                     p_action                     => 'U');
    END LOOP;

  END;

  PROCEDURE put_regions(p_user                           IN products.created_by%TYPE,
                        p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                        p_regions                        IN region_tab) IS

    TYPE ltab_old_regions IS TABLE OF regions%ROWTYPE;

    lv_old_regions ltab_old_regions;

    lv_logical_load_timestamp TIMESTAMP(6);

  BEGIN

    lv_logical_load_timestamp := SYSTIMESTAMP;
    --
    -- Select the regions that have got updated
    --

    SELECT rgn.*
    BULK COLLECT INTO lv_old_regions
    FROM regions rgn, TABLE(CAST(p_regions AS region_tab)) new_rgn
    WHERE rgn.region_code = new_rgn.region_code (+) AND
          (nrg_common.has_value_changed(rgn.region_name,new_rgn.region_name)= 1 OR
           nrg_common.has_value_changed(rgn.iso_numeric,new_rgn.iso_numeric)= 1 OR
           nrg_common.has_value_changed(rgn.is_deleted,new_rgn.is_deleted) = 1)
    FOR UPDATE OF rgn.region_code;


    --
    --Soft Delete Regions
    --


    UPDATE regions
    SET    logical_load_timestamp    = lv_logical_load_timestamp,
           updated_by                = p_user,
           update_timestamp          = SYSTIMESTAMP,
           effective_start_timestamp = p_effective_start_timestamp,
           is_deleted                = 'YES'
    WHERE region_code NOT IN (SELECT rgn.region_code
                              FROM TABLE(CAST(p_regions AS region_tab)) rgn) AND
          (created_by = 'NRG' OR updated_by = 'NRG') AND
          is_deleted                = 'NO' ;

    --
    --Update the existing changed regions
    --

    MERGE INTO regions rgn
    USING (SELECT *
           FROM TABLE(CAST(p_regions AS region_tab))) new_rgn
    ON (rgn.region_code = new_rgn.region_code)
    WHEN MATCHED THEN
      UPDATE
            SET
            logical_load_timestamp    = lv_logical_load_timestamp,
            updated_by                = p_user,
            update_timestamp          = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            region_name               = new_rgn.region_name,
            iso_numeric               = new_rgn.iso_numeric,
            is_deleted                = new_rgn.is_deleted
      WHERE (nrg_common.has_value_changed(rgn.region_name,new_rgn.region_name)= 1 OR
             nrg_common.has_value_changed(rgn.iso_numeric,new_rgn.iso_numeric)= 1 OR
             nrg_common.has_value_changed(rgn.is_deleted,new_rgn.is_deleted) = 1)
    WHEN NOT MATCHED THEN
        INSERT  (
                      region_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      region_name,
                      iso_numeric,
                      is_deleted
                  )
        VALUES(
                new_rgn.region_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_rgn.region_name,
                new_rgn.iso_numeric,
                new_rgn.is_deleted
              );

    FOR lv_cnt IN 1..lv_old_regions.COUNT LOOP

      put_history(p_old_regions_record            => lv_old_regions(lv_cnt),
                  p_effective_end_timestamp    => p_effective_start_timestamp,
                  p_action                     => 'U');
    END LOOP;


  END;

  PROCEDURE put_countries(p_user                           IN products.created_by%TYPE,
                          p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                          p_countries                      IN country_tab) IS

    TYPE ltab_old_countries IS TABLE OF countries%ROWTYPE;
    lv_old_countries ltab_old_countries;

    lv_logical_load_timestamp TIMESTAMP(6);

  BEGIN

    lv_logical_load_timestamp := SYSTIMESTAMP;
    --
    -- Select the countries that have got updated
    --

    SELECT cntry.*
    BULK COLLECT INTO lv_old_countries
    FROM countries cntry, TABLE(CAST(p_countries AS country_tab)) new_cntry
    WHERE cntry.country_code = new_cntry.country_code (+) AND
          (nrg_common.has_value_changed(cntry.country_name,new_cntry.country_name)= 1 OR
           nrg_common.has_value_changed(cntry.iso2,new_cntry.iso2)= 1 OR
           nrg_common.has_value_changed(cntry.iso3,new_cntry.iso3)= 1 OR
           nrg_common.has_value_changed(cntry.iso_numeric,new_cntry.iso_numeric)= 1 OR
           nrg_common.has_value_changed(cntry.region_code,new_cntry.region_code)= 1 OR
           nrg_common.has_value_changed(cntry.labour_force,new_cntry.labour_force)= 1 OR
           nrg_common.has_value_changed(cntry.unemployment_rate,new_cntry.unemployment_rate)= 1 OR
           nrg_common.has_value_changed(cntry.exports_in_usd,new_cntry.exports_in_usd)= 1 OR
           nrg_common.has_value_changed(cntry.imports_in_usd,new_cntry.imports_in_usd)= 1 OR
           nrg_common.has_value_changed(cntry.population,new_cntry.population)= 1 OR
           nrg_common.has_value_changed(cntry.currency,new_cntry.currency)= 1 OR
           nrg_common.has_value_changed(cntry.status,new_cntry.status)= 1 OR
           nrg_common.has_value_changed(cntry.is_deleted,new_cntry.is_deleted)= 1 OR
           nrg_common.has_value_changed(cntry.is_eea_member,new_cntry.is_eea_member) = 1 OR
           nrg_common.has_value_changed(cntry.is_eu_member,new_cntry.is_eu_member) = 1 OR
           nrg_common.has_value_changed(cntry.dividend_tax_rate,new_cntry.dividend_tax_rate) = 1 OR
           nrg_common.has_value_changed(cntry.declaration_validity_period,new_cntry.declaration_validity_period) = 1)
    FOR UPDATE OF cntry.country_code;

    --
    --Soft Delete Countries
    --

    UPDATE countries
    SET    logical_load_timestamp     = lv_logical_load_timestamp,
           updated_by                 = p_user,
           update_timestamp           = SYSTIMESTAMP,
           effective_start_timestamp  = p_effective_start_timestamp,
           is_deleted                 = 'YES'
    WHERE country_code NOT IN (SELECT country_code
                               FROM TABLE(CAST(p_countries AS country_tab)))AND
          (created_by = 'NRG' OR updated_by = 'NRG')  AND
          is_deleted                = 'NO' ;

    MERGE INTO countries cntry
    USING (SELECT *
         FROM TABLE(CAST(p_countries AS country_tab))) new_cntry
    ON (cntry.country_code = new_cntry.country_code)
    WHEN MATCHED THEN
      UPDATE
        SET
            logical_load_timestamp     = lv_logical_load_timestamp,
            updated_by                 = p_user,
            update_timestamp           = SYSTIMESTAMP,
            effective_start_timestamp  = p_effective_start_timestamp,
            country_name               = new_cntry.country_name,
            iso2                       = new_cntry.iso2,
            iso3                       = new_cntry.iso3,
            iso_numeric                = new_cntry.iso_numeric,
            region_code                = new_cntry.region_code,
            labour_force               = new_cntry.labour_force,
            unemployment_rate          = new_cntry.unemployment_rate,
            exports_in_usd             = new_cntry.exports_in_usd,
            imports_in_usd             = new_cntry.imports_in_usd,
            population                 = new_cntry.population,
            currency                   = new_cntry.currency,
            status                     = new_cntry.status,
            is_deleted                 = new_cntry.is_deleted,
            is_eea_member              = new_cntry.is_eea_member,
            is_eu_member               = new_cntry.is_eu_member,
            dividend_tax_rate          = new_cntry.dividend_tax_rate,
            declaration_validity_period= new_cntry.declaration_validity_period
      WHERE (nrg_common.has_value_changed(cntry.country_name,new_cntry.country_name)= 1 OR
             nrg_common.has_value_changed(cntry.iso2,new_cntry.iso2)= 1 OR
             nrg_common.has_value_changed(cntry.iso3,new_cntry.iso3)= 1 OR
             nrg_common.has_value_changed(cntry.iso_numeric,new_cntry.iso_numeric)= 1 OR
             nrg_common.has_value_changed(cntry.region_code,new_cntry.region_code)= 1 OR
             nrg_common.has_value_changed(cntry.labour_force,new_cntry.labour_force)= 1 OR
             nrg_common.has_value_changed(cntry.unemployment_rate,new_cntry.unemployment_rate)= 1 OR
             nrg_common.has_value_changed(cntry.exports_in_usd,new_cntry.exports_in_usd)= 1 OR
             nrg_common.has_value_changed(cntry.imports_in_usd,new_cntry.imports_in_usd)= 1 OR
             nrg_common.has_value_changed(cntry.population,new_cntry.population)= 1 OR
             nrg_common.has_value_changed(cntry.currency,new_cntry.currency)= 1 OR
             nrg_common.has_value_changed(cntry.status,new_cntry.status)= 1 OR
             nrg_common.has_value_changed(cntry.is_deleted,new_cntry.is_deleted)= 1 OR
             nrg_common.has_value_changed(cntry.is_eea_member, new_cntry.is_eea_member) = 1 OR
             nrg_common.has_value_changed(cntry.is_eu_member, new_cntry.is_eu_member) = 1 OR
             nrg_common.has_value_changed(cntry.dividend_tax_rate, new_cntry.dividend_tax_rate)= 1 OR
             nrg_common.has_value_changed(cntry.declaration_validity_period, new_cntry.declaration_validity_period) = 1)
    WHEN NOT MATCHED THEN
      INSERT  (
                      country_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      country_name,
                      iso2,
                      iso3,
                      iso_numeric,
                      region_code,
                      labour_force,
                      unemployment_rate,
                      exports_in_usd,
                      imports_in_usd,
                      population,
                      currency,
                      status,
                      is_deleted,
                      is_eea_member,
                      is_eu_member,
                      dividend_tax_rate,
                      declaration_validity_period
              )
        VALUES(
                new_cntry.country_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_cntry.country_name,
                new_cntry.iso2,
                new_cntry.iso3,
                new_cntry.iso_numeric,
                new_cntry.region_code,
                new_cntry.labour_force,
                new_cntry.unemployment_rate,
                new_cntry.exports_in_usd,
                new_cntry.imports_in_usd,
                new_cntry.population,
                new_cntry.currency,
                new_cntry.status,
                new_cntry.is_deleted,
                new_cntry.is_eea_member,
                new_cntry.is_eu_member,
                new_cntry.dividend_tax_rate,
                new_cntry.declaration_validity_period
              );

    FOR lv_cnt IN 1..lv_old_countries.COUNT LOOP

         put_history(p_old_countries_record       => lv_old_countries(lv_cnt),
                     p_effective_end_timestamp    => p_effective_start_timestamp,
                     p_action                     => 'U');
    END LOOP;

  END;

  PROCEDURE put_reg_inst_identification(p_user                           IN products.created_by%TYPE,
                                        p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                        p_reg_instr_identification       IN reg_instr_identification_tab) IS

    TYPE ltab_old_reg_instr_ident IS TABLE OF reg_instr_identification%ROWTYPE;
    lv_old_reg_instr_ident ltab_old_reg_instr_ident;

    lv_logical_load_timestamp TIMESTAMP(6);

  BEGIN

    lv_logical_load_timestamp := SYSTIMESTAMP;

    --
    --Select the instrument feed settings that have got updated
    --

    SELECT old_version.*
    BULK COLLECT INTO lv_old_reg_instr_ident
    FROM reg_instr_identification old_version, TABLE(CAST(p_reg_instr_identification as reg_instr_identification_tab))new_version
    WHERE old_version.regulatory_identification_code = new_version.regulatory_identification_code AND
          (nrg_common.has_value_changed(old_version.regulatory_identification_name, new_version.regulatory_identification_name) = 1 OR
           nrg_common.has_value_changed(old_version.regulation, new_version.regulation) = 1 OR
           nrg_common.has_value_changed(old_version.trading_account_type, new_version.trading_account_type) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_code, new_version.instrument_code) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_type, new_version.instrument_type) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_country, new_version.instrument_country) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_mic, new_version.instrument_mic) = 1 OR
           nrg_common.has_value_changed(old_version.is_reportable, new_version.is_reportable) = 1 OR
           nrg_common.has_value_changed(old_version.reportable_identifier_type, new_version.reportable_identifier_type) = 1 OR
           nrg_common.has_value_changed(old_version.broker_code, new_version.broker_code) = 1 OR
           nrg_common.has_value_changed(old_version.reportable_instrument_type, new_version.reportable_instrument_type) = 1 OR
           nrg_common.has_value_changed(old_version.derivative_type, new_version.derivative_type) = 1);

    UPDATE reg_instr_identification old_version
    SET is_deleted = 'YES',
        logical_load_timestamp = lv_logical_load_timestamp
    WHERE NOT EXISTS (SELECT 1
                      FROM TABLE(CAST(p_reg_instr_identification AS reg_instr_identification_tab))new_version
                      WHERE old_version.regulatory_identification_code = new_version.regulatory_identification_code)
       AND is_deleted                = 'NO' ;

    --
    --Insert and update data
    --

    MERGE INTO reg_instr_identification old_version
    USING (SELECT *
           FROM TABLE(CAST(p_reg_instr_identification AS reg_instr_identification_tab))) new_version
    ON (new_version.regulatory_identification_code = old_version.regulatory_identification_code)
    WHEN MATCHED THEN
      UPDATE
      SET   regulatory_identification_name = new_version.regulatory_identification_name,
            logical_load_timestamp = lv_logical_load_timestamp,
            updated_by = p_user,
            update_timestamp = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            regulation = new_version.regulation,
            trading_account_type  = new_version.trading_account_type,
            instrument_code = new_version.instrument_code,
            instrument_type  = new_version.instrument_type,
            instrument_country  = new_version.instrument_country,
            instrument_mic = new_version.instrument_mic,
            is_reportable   = new_version.is_reportable,
            reportable_identifier_type  = new_version.reportable_identifier_type,
            is_deleted = 'NO',
            broker_code   = new_version.broker_code,
            reportable_instrument_type   = new_version.reportable_instrument_type,
            derivative_type   = new_version.derivative_type
      WHERE (nrg_common.has_value_changed(new_version.regulatory_identification_name, old_version.regulatory_identification_name) = 1 OR
             nrg_common.has_value_changed(new_version.regulation, old_version.regulation) = 1 OR
             nrg_common.has_value_changed(new_version.trading_account_type, old_version.trading_account_type) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_code, old_version.instrument_code) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_type, old_version.instrument_type) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_country, old_version.instrument_country) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_mic, old_version.instrument_mic) = 1 OR
             nrg_common.has_value_changed(new_version.is_reportable, old_version.is_reportable) = 1 OR
             nrg_common.has_value_changed(new_version.reportable_identifier_type, old_version.reportable_identifier_type) = 1 OR
             nrg_common.has_value_changed(new_version.broker_code, old_version.broker_code) = 1 OR
             nrg_common.has_value_changed(new_version.reportable_instrument_type, old_version.reportable_instrument_type) = 1 OR
             nrg_common.has_value_changed(new_version.derivative_type, old_version.derivative_type) = 1)
    WHEN NOT MATCHED THEN
      INSERT (regulatory_identification_code
              ,regulatory_identification_name
              ,logical_load_timestamp
              ,created_by
              ,create_timestamp
              ,updated_by
              ,update_timestamp
              ,effective_start_timestamp
              ,is_deleted
              ,regulation
              ,trading_account_type
              ,instrument_code
              ,instrument_type
              ,instrument_country
              ,instrument_mic
              ,is_reportable
              ,reportable_identifier_type
              ,broker_code
              ,reportable_instrument_type
              ,derivative_type)
      VALUES (new_version.regulatory_identification_code
             ,new_version.regulatory_identification_name
             ,lv_logical_load_timestamp
             ,p_user
             ,SYSTIMESTAMP
             ,p_user
             ,SYSTIMESTAMP
             ,p_effective_start_timestamp
             ,'NO'
             ,new_version.regulation
             ,new_version.trading_account_type
             ,new_version.instrument_code
             ,new_version.instrument_type
             ,new_version.instrument_country
             ,new_version.instrument_mic
             ,new_version.is_reportable
             ,new_version.reportable_identifier_type
             ,new_version.broker_code
             ,new_version.reportable_instrument_type
             ,new_version.derivative_type);

    FOR lv_cnt IN 1..lv_old_reg_instr_ident.COUNT LOOP
      put_history (p_reg_instr_ident_record   => lv_old_reg_instr_ident(lv_cnt),
                   p_effective_end_timestamp  => p_effective_start_timestamp,
                   p_action                   => 'U');
    END LOOP;
  END;

    -- ===================================================================================
    -- put_product_set
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --     INSTRUMENTS
    --     PRODUCT_WRAPPERS
    --     REGIONS
    --     COUNTRIES
    --     MM_PRODUCTS
    --     PRODUCTS_H
    --     MM_PRODUCTS_H
    --     INSTRUMENRS_H
    --     REGIONS_H
    --
    --  Products data is always written in sets and hence it is different from all the other NRG packages like NRG_TRADES
    --  To ensure the referntial integrity we write the data for each of the set in a sequence. The sequence of writes is:
    --  1. REGIONS
    --  2. COUNTRIES
    --  3. INSTRUMENTS
    --  4. PRODUCT_WRAPPERS
    --  5. PRODUCTS
    --  6. MM_PRODUCTS
    --
    --  From NRG we get only the updated data set each time a new product snapshot is created by PMS. Also the freequency of updates
    --  to products is minimal.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_products                       Product Details
    --     p_product_wrappers               Product wrapper Details
    --     p_instruments                    Instrument Details
    --     p_regions                        Regions Details
    --     p_countries                      Countries Details
    --     p_inst_feed_setting
    --     p_reg_instr_identification
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_product_set    (p_user                           IN products.created_by%TYPE,
                                  p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                  p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
                                  p_products                        IN product_tab,
                                  p_product_wrappers                IN product_wrapper_tab,
                                  p_instruments                     IN instrument_tab,
                                  p_regions                         IN region_tab,
                                  p_countries                       IN country_tab,
                                  p_inst_feed_setting               IN instrument_feed_setting_tab,
                                  p_reg_instr_identification        IN reg_instr_identification_tab,
                                  p_products_gsko                   IN products_gsko_tab,
                                  p_product_settings                IN product_settings_tab,
                                  p_economic_calendar_events        IN economic_calendar_events_tab,
                                  p_inst_to_economic_events         IN economic_cal_events_inst_tab,
                                  p_tax_treaties                    IN tax_treaty_relations_tab
                                  )
  IS

      lv_logical_load_timestamp    products.logical_load_timestamp%TYPE;

      TYPE lvtyp_old_product_wrappers IS TABLE OF product_wrappers%rowtype;
      lv_old_product_wrappers lvtyp_old_product_wrappers;

      TYPE lvtyp_old_countries IS TABLE OF countries%rowtype;
      lv_old_countries lvtyp_old_countries;

      TYPE lvtyp_old_regions IS TABLE OF regions%rowtype;
      lv_old_regions lvtyp_old_regions;

      TYPE lvtyp_old_reg_instr_ident IS TABLE OF reg_instr_identification%rowtype;
      lv_old_reg_instr_ident lvtyp_old_reg_instr_ident;

     lv_platform     VARCHAR2(10);

  BEGIN


    logger.logger.set_module('put_product');

    -- set the logical load timestamp to now

    lv_logical_load_timestamp := SYSTIMESTAMP;

    put_snapshot_identifier (p_user                            => p_user,
                             p_effective_start_timestamp       => p_effective_start_timestamp,
                             p_snapshot_identifier             => p_snapshot_identifier,
                             p_logical_load_timestamp          => lv_logical_load_timestamp,
                             p_is_processed                    => 'YES');

    lv_platform := NULL;

    BEGIN
      SELECT DISTINCT platform
      INTO lv_platform
      FROM TABLE(CAST(p_products AS product_tab))
      WHERE platform <> 'NG';
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        NULL;
      WHEN TOO_MANY_ROWS THEN
        NULL;
      WHEN OTHERS THEN
        NULL;
    END;



    --
    -- Select The Product wrappers that have got updated
    --

    SELECT prd_wrp.*
    BULK COLLECT INTO lv_old_product_wrappers
    FROM product_wrappers prd_wrp, TABLE(CAST(p_product_wrappers AS product_wrapper_tab)) new_prd_wrp
    WHERE prd_wrp.wrapper_code = new_prd_wrp.wrapper_code (+) AND
          (nrg_common.has_value_changed(prd_wrp.product_wrapper_type,new_prd_wrp.product_wrapper_type)= 1 OR
           nrg_common.has_value_changed(prd_wrp.short_name,new_prd_wrp.short_name)= 1 OR
           nrg_common.has_value_changed(prd_wrp.name_long,new_prd_wrp.name_long)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_quantity_amount_available,new_prd_wrp.is_quantity_amount_available)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_quantity_units_available,new_prd_wrp.is_quantity_units_available)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_units_administrable,new_prd_wrp.is_qntty_units_administrable)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_amount_administrable,new_prd_wrp.is_qntty_amount_administrable)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_amt_per_pnt_avlbl,new_prd_wrp.is_qntty_amt_per_pnt_avlbl)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_qntty_amt_per_pnt_admnstrbl,new_prd_wrp.is_qntty_amt_per_pnt_admnstrbl)= 1 OR
           nrg_common.has_value_changed(prd_wrp.is_deleted,new_prd_wrp.is_deleted)= 1)
    FOR UPDATE OF prd_wrp.wrapper_code;


    --
    -- Select the countries that have got updated
    --

    SELECT cntry.*
    BULK COLLECT INTO lv_old_countries
    FROM countries cntry, TABLE(CAST(p_countries AS country_tab)) new_cntry
    WHERE cntry.country_code = new_cntry.country_code (+) AND
          (nrg_common.has_value_changed(cntry.country_name,new_cntry.country_name)= 1 OR
           nrg_common.has_value_changed(cntry.iso2,new_cntry.iso2)= 1 OR
           nrg_common.has_value_changed(cntry.iso3,new_cntry.iso3)= 1 OR
           nrg_common.has_value_changed(cntry.iso_numeric,new_cntry.iso_numeric)= 1 OR
           nrg_common.has_value_changed(cntry.region_code,new_cntry.region_code)= 1 OR
           nrg_common.has_value_changed(cntry.labour_force,new_cntry.labour_force)= 1 OR
           nrg_common.has_value_changed(cntry.unemployment_rate,new_cntry.unemployment_rate)= 1 OR
           nrg_common.has_value_changed(cntry.exports_in_usd,new_cntry.exports_in_usd)= 1 OR
           nrg_common.has_value_changed(cntry.imports_in_usd,new_cntry.imports_in_usd)= 1 OR
           nrg_common.has_value_changed(cntry.population,new_cntry.population)= 1 OR
           nrg_common.has_value_changed(cntry.currency,new_cntry.currency)= 1 OR
           nrg_common.has_value_changed(cntry.status,new_cntry.status)= 1 OR
           nrg_common.has_value_changed(cntry.is_deleted,new_cntry.is_deleted)= 1 OR
           nrg_common.has_value_changed(cntry.is_eea_member,new_cntry.is_eea_member) = 1 OR
           nrg_common.has_value_changed(cntry.is_eu_member,new_cntry.is_eu_member) = 1 OR
           nrg_common.has_value_changed(cntry.dividend_tax_rate,new_cntry.dividend_tax_rate) = 1 OR
           nrg_common.has_value_changed(cntry.declaration_validity_period,new_cntry.declaration_validity_period) = 1)
    FOR UPDATE OF cntry.country_code;

    --
    -- Select the regions that have got updated
    --

    SELECT rgn.*
    BULK COLLECT INTO lv_old_regions
    FROM regions rgn, TABLE(CAST(p_regions AS region_tab)) new_rgn
    WHERE rgn.region_code = new_rgn.region_code (+) AND
          (nrg_common.has_value_changed(rgn.region_name,new_rgn.region_name)= 1 OR
           nrg_common.has_value_changed(rgn.iso_numeric,new_rgn.iso_numeric)= 1 OR
           nrg_common.has_value_changed(rgn.is_deleted,new_rgn.is_deleted) = 1)
    FOR UPDATE OF rgn.region_code;

    --
    --
    --



    --
    --Select the instrument feed settings that have got updated
    --

    SELECT old_version.*
    BULK COLLECT INTO lv_old_reg_instr_ident
    FROM reg_instr_identification old_version, TABLE(CAST(p_reg_instr_identification as reg_instr_identification_tab))new_version
    WHERE old_version.regulatory_identification_code = new_version.regulatory_identification_code AND
          (nrg_common.has_value_changed(old_version.regulatory_identification_name, new_version.regulatory_identification_name) = 1 OR
           nrg_common.has_value_changed(old_version.regulation, new_version.regulation) = 1 OR
           nrg_common.has_value_changed(old_version.trading_account_type, new_version.trading_account_type) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_code, new_version.instrument_code) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_type, new_version.instrument_type) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_country, new_version.instrument_country) = 1 OR
           nrg_common.has_value_changed(old_version.instrument_mic, new_version.instrument_mic) = 1 OR
           nrg_common.has_value_changed(old_version.is_reportable, new_version.is_reportable) = 1 OR
           nrg_common.has_value_changed(old_version.reportable_identifier_type, new_version.reportable_identifier_type) = 1 OR
           nrg_common.has_value_changed(old_version.broker_code, new_version.broker_code) = 1 OR
           nrg_common.has_value_changed(old_version.reportable_instrument_type, new_version.reportable_instrument_type) = 1 OR
           nrg_common.has_value_changed(old_version.derivative_type, new_version.derivative_type) = 1);








    --
    --Starting to write the history
    --

    --
    --Countries
    --

    FOR lv_cnt IN 1..lv_old_countries.COUNT LOOP

         put_history(p_old_countries_record       => lv_old_countries(lv_cnt),
                     p_effective_end_timestamp    => p_effective_start_timestamp,
                     p_action                     => 'U');
    END LOOP;

    lv_old_countries.DELETE;

    --
    --Regions
    --

    FOR lv_cnt IN 1..lv_old_regions.COUNT LOOP

    put_history(p_old_regions_record            => lv_old_regions(lv_cnt),
                     p_effective_end_timestamp    => p_effective_start_timestamp,
                     p_action                     => 'U');
    END LOOP;

    lv_old_regions.DELETE;

    --
    --Product Wrappers
    --

    FOR lv_cnt IN 1..lv_old_product_wrappers.COUNT LOOP
      put_history(p_old_wrapper_record            => lv_old_product_wrappers(lv_cnt),
                     p_effective_end_timestamp    => p_effective_start_timestamp,
                     p_action                     => 'U');
    END LOOP;

    lv_old_product_wrappers.DELETE;

    --
    --Regulatory Instrument Identification
    --

    FOR lv_cnt IN 1..lv_old_reg_instr_ident.COUNT LOOP
      put_history (p_reg_instr_ident_record   => lv_old_reg_instr_ident(lv_cnt),
                   p_effective_end_timestamp  => p_effective_start_timestamp,
                   p_action                   => 'U');
    END LOOP;

    lv_old_reg_instr_ident.DELETE;


    --
    --End of writing to history
    --

    --
    --Starting to write the data to table
    --

    --
    --Regions
    --

    --
    --Soft Delete Regions
    --


    UPDATE regions
    SET    logical_load_timestamp    = lv_logical_load_timestamp,
           updated_by                = p_user,
           update_timestamp          = SYSTIMESTAMP,
           effective_start_timestamp = p_effective_start_timestamp,
           is_deleted                = 'YES'
    WHERE region_code NOT IN (SELECT rgn.region_code
                              FROM TABLE(CAST(p_regions AS region_tab)) rgn) AND
          (created_by = 'NRG' OR updated_by = 'NRG') AND
          is_deleted                = 'NO' ;

    --
    --Update the existing changed regions
    --

    MERGE INTO regions rgn
    USING (SELECT *
           FROM TABLE(CAST(p_regions AS region_tab))) new_rgn
    ON (rgn.region_code = new_rgn.region_code)
    WHEN MATCHED THEN
      UPDATE
            SET
            logical_load_timestamp    = lv_logical_load_timestamp,
            updated_by                = p_user,
            update_timestamp          = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            region_name               = new_rgn.region_name,
            iso_numeric               = new_rgn.iso_numeric,
            is_deleted                = new_rgn.is_deleted
      WHERE (nrg_common.has_value_changed(rgn.region_name,new_rgn.region_name)= 1 OR
             nrg_common.has_value_changed(rgn.iso_numeric,new_rgn.iso_numeric)= 1 OR
             nrg_common.has_value_changed(rgn.is_deleted,new_rgn.is_deleted) = 1)
    WHEN NOT MATCHED THEN
        INSERT  (
                      region_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      region_name,
                      iso_numeric,
                      is_deleted
                  )
        VALUES(
                new_rgn.region_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_rgn.region_name,
                new_rgn.iso_numeric,
                new_rgn.is_deleted
              );

    --
    -- Countries
    --

    --
    --Soft Delete Countries
    --

    UPDATE countries
    SET    logical_load_timestamp     = lv_logical_load_timestamp,
           updated_by                 = p_user,
           update_timestamp           = SYSTIMESTAMP,
           effective_start_timestamp  = p_effective_start_timestamp,
           is_deleted                 = 'YES'
    WHERE country_code NOT IN (SELECT country_code
                               FROM TABLE(CAST(p_countries AS country_tab)))AND
          (created_by = 'NRG' OR updated_by = 'NRG')  AND
          is_deleted                = 'NO' ;

    MERGE INTO countries cntry
    USING (SELECT *
         FROM TABLE(CAST(p_countries AS country_tab))) new_cntry
    ON (cntry.country_code = new_cntry.country_code)
    WHEN MATCHED THEN
      UPDATE
        SET
            logical_load_timestamp     = lv_logical_load_timestamp,
            updated_by                 = p_user,
            update_timestamp           = SYSTIMESTAMP,
            effective_start_timestamp  = p_effective_start_timestamp,
            country_name               = new_cntry.country_name,
            iso2                       = new_cntry.iso2,
            iso3                       = new_cntry.iso3,
            iso_numeric                = new_cntry.iso_numeric,
            region_code                = new_cntry.region_code,
            labour_force               = new_cntry.labour_force,
            unemployment_rate          = new_cntry.unemployment_rate,
            exports_in_usd             = new_cntry.exports_in_usd,
            imports_in_usd             = new_cntry.imports_in_usd,
            population                 = new_cntry.population,
            currency                   = new_cntry.currency,
            status                     = new_cntry.status,
            is_deleted                 = new_cntry.is_deleted,
            is_eea_member              = new_cntry.is_eea_member,
            is_eu_member               = new_cntry.is_eu_member,
            dividend_tax_rate          = new_cntry.dividend_tax_rate,
            declaration_validity_period= new_cntry.declaration_validity_period
      WHERE (nrg_common.has_value_changed(cntry.country_name,new_cntry.country_name)= 1 OR
             nrg_common.has_value_changed(cntry.iso2,new_cntry.iso2)= 1 OR
             nrg_common.has_value_changed(cntry.iso3,new_cntry.iso3)= 1 OR
             nrg_common.has_value_changed(cntry.iso_numeric,new_cntry.iso_numeric)= 1 OR
             nrg_common.has_value_changed(cntry.region_code,new_cntry.region_code)= 1 OR
             nrg_common.has_value_changed(cntry.labour_force,new_cntry.labour_force)= 1 OR
             nrg_common.has_value_changed(cntry.unemployment_rate,new_cntry.unemployment_rate)= 1 OR
             nrg_common.has_value_changed(cntry.exports_in_usd,new_cntry.exports_in_usd)= 1 OR
             nrg_common.has_value_changed(cntry.imports_in_usd,new_cntry.imports_in_usd)= 1 OR
             nrg_common.has_value_changed(cntry.population,new_cntry.population)= 1 OR
             nrg_common.has_value_changed(cntry.currency,new_cntry.currency)= 1 OR
             nrg_common.has_value_changed(cntry.status,new_cntry.status)= 1 OR
             nrg_common.has_value_changed(cntry.is_deleted,new_cntry.is_deleted)= 1 OR
             nrg_common.has_value_changed(cntry.is_eea_member, new_cntry.is_eea_member) = 1 OR
             nrg_common.has_value_changed(cntry.is_eu_member, new_cntry.is_eu_member) = 1 OR
             nrg_common.has_value_changed(cntry.dividend_tax_rate, new_cntry.dividend_tax_rate)= 1 OR
             nrg_common.has_value_changed(cntry.declaration_validity_period, new_cntry.declaration_validity_period) = 1)
    WHEN NOT MATCHED THEN
      INSERT  (
                      country_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      country_name,
                      iso2,
                      iso3,
                      iso_numeric,
                      region_code,
                      labour_force,
                      unemployment_rate,
                      exports_in_usd,
                      imports_in_usd,
                      population,
                      currency,
                      status,
                      is_deleted,
                      is_eea_member,
                      is_eu_member,
                      dividend_tax_rate,
                      declaration_validity_period
              )
        VALUES(
                new_cntry.country_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_cntry.country_name,
                new_cntry.iso2,
                new_cntry.iso3,
                new_cntry.iso_numeric,
                new_cntry.region_code,
                new_cntry.labour_force,
                new_cntry.unemployment_rate,
                new_cntry.exports_in_usd,
                new_cntry.imports_in_usd,
                new_cntry.population,
                new_cntry.currency,
                new_cntry.status,
                new_cntry.is_deleted,
                new_cntry.is_eea_member,
                new_cntry.is_eu_member,
                new_cntry.dividend_tax_rate,
                new_cntry.declaration_validity_period
              );


    ---
    -- Call stored procedure for Instruments
    ---
           put_instruments(p_user                       => p_user,
                           p_effective_start_timestamp  => p_effective_start_timestamp,
                           p_instruments                => p_instruments);
                           --p_inst_feed_setting          => p_inst_feed_setting);
    --
    --Product Wrappers
    --

    --
    --Soft Delete the records
    --

    UPDATE product_wrappers
    SET    logical_load_timestamp         = lv_logical_load_timestamp,
           updated_by                     = p_user,
           update_timestamp               = SYSTIMESTAMP,
           effective_start_timestamp      = p_effective_start_timestamp,
           is_deleted                     = 'YES'
    WHERE  wrapper_code NOT IN (SELECT wrapper_code
                                FROM TABLE(CAST(p_product_wrappers AS product_wrapper_tab)))
      AND
          is_deleted                = 'NO' ;

    MERGE INTO product_wrappers wrpr
    USING (SELECT *
         FROM TABLE(CAST(p_product_wrappers AS product_wrapper_tab))) new_wrpr
    ON (wrpr.wrapper_code = new_wrpr.wrapper_code)
    WHEN MATCHED THEN
      UPDATE
      SET
            logical_load_timestamp         = lv_logical_load_timestamp,
            updated_by                     = p_user,
            update_timestamp               = SYSTIMESTAMP,
            effective_start_timestamp      = p_effective_start_timestamp,
            product_wrapper_type           = new_wrpr.product_wrapper_type,
            short_name                     = new_wrpr.short_name,
            name_long                      = new_wrpr.name_long,
            is_quantity_amount_available   = new_wrpr.is_quantity_amount_available,
            is_quantity_units_available    = new_wrpr.is_quantity_units_available,
            is_qntty_units_administrable   = new_wrpr.is_qntty_units_administrable,
            is_qntty_amount_administrable  = new_wrpr.is_qntty_amount_administrable,
            is_qntty_amt_per_pnt_avlbl     = new_wrpr.is_qntty_amt_per_pnt_avlbl,
            is_qntty_amt_per_pnt_admnstrbl = new_wrpr.is_qntty_amt_per_pnt_admnstrbl,
            is_deleted                     = new_wrpr.is_deleted
      WHERE (nrg_common.has_value_changed(wrpr.product_wrapper_type,new_wrpr.product_wrapper_type)= 1 OR
             nrg_common.has_value_changed(wrpr.short_name,new_wrpr.short_name)= 1 OR
             nrg_common.has_value_changed(wrpr.name_long,new_wrpr.name_long)= 1 OR
             nrg_common.has_value_changed(wrpr.is_quantity_amount_available,new_wrpr.is_quantity_amount_available)= 1 OR
             nrg_common.has_value_changed(wrpr.is_quantity_units_available,new_wrpr.is_quantity_units_available)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_units_administrable,new_wrpr.is_qntty_units_administrable)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_amount_administrable,new_wrpr.is_qntty_amount_administrable)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_amt_per_pnt_avlbl,new_wrpr.is_qntty_amt_per_pnt_avlbl)= 1 OR
             nrg_common.has_value_changed(wrpr.is_qntty_amt_per_pnt_admnstrbl,new_wrpr.is_qntty_amt_per_pnt_admnstrbl)= 1 OR
             nrg_common.has_value_changed(wrpr.is_deleted,new_wrpr.is_deleted)= 1)
    WHEN NOT MATCHED THEN
      INSERT  (
                      wrapper_code,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      effective_start_timestamp,
                      product_wrapper_type,
                      short_name,
                      name_long,
                      is_quantity_amount_available,
                      is_quantity_units_available,
                      is_qntty_units_administrable,
                      is_qntty_amount_administrable,
                      is_qntty_amt_per_pnt_avlbl,
                      is_qntty_amt_per_pnt_admnstrbl,
                      is_deleted
                  )
        VALUES(
                new_wrpr.wrapper_code,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                new_wrpr.product_wrapper_type,
                new_wrpr.short_name,
                new_wrpr.name_long,
                new_wrpr.is_quantity_amount_available,
                new_wrpr.is_quantity_units_available,
                new_wrpr.is_qntty_units_administrable,
                new_wrpr.is_qntty_amount_administrable,
                new_wrpr.is_qntty_amt_per_pnt_avlbl,
                new_wrpr.is_qntty_amt_per_pnt_admnstrbl,
                new_wrpr.is_deleted
              );


    ---
    -- Call stored procedure for products, mm_products
    ---

              put_products(p_user                           => p_user,
                           --p_platform                       => lv_platform,
                           p_effective_start_timestamp      => p_effective_start_timestamp,
                           p_products                       => p_products);

    --
    --REG_INSTR_IDENTIFICATION Soft Delete The Deleted Rows
    --

    UPDATE reg_instr_identification old_version
    SET is_deleted = 'YES',
        logical_load_timestamp = lv_logical_load_timestamp
    WHERE NOT EXISTS (SELECT 1
                      FROM TABLE(CAST(p_reg_instr_identification AS reg_instr_identification_tab))new_version
                      WHERE old_version.regulatory_identification_code = new_version.regulatory_identification_code)
       AND is_deleted                = 'NO' ;

    --
    --Insert and update data
    --

    MERGE INTO reg_instr_identification old_version
    USING (SELECT *
           FROM TABLE(CAST(p_reg_instr_identification AS reg_instr_identification_tab))) new_version
    ON (new_version.regulatory_identification_code = old_version.regulatory_identification_code)
    WHEN MATCHED THEN
      UPDATE
      SET   regulatory_identification_name = new_version.regulatory_identification_name,
            logical_load_timestamp = lv_logical_load_timestamp,
            updated_by = p_user,
            update_timestamp = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            regulation = new_version.regulation,
            trading_account_type  = new_version.trading_account_type,
            instrument_code = new_version.instrument_code,
            instrument_type  = new_version.instrument_type,
            instrument_country  = new_version.instrument_country,
            instrument_mic = new_version.instrument_mic,
            is_reportable   = new_version.is_reportable,
            reportable_identifier_type  = new_version.reportable_identifier_type,
            is_deleted = 'NO',
            broker_code   = new_version.broker_code,
            reportable_instrument_type   = new_version.reportable_instrument_type,
            derivative_type   = new_version.derivative_type
      WHERE (nrg_common.has_value_changed(new_version.regulatory_identification_name, old_version.regulatory_identification_name) = 1 OR
             nrg_common.has_value_changed(new_version.regulation, old_version.regulation) = 1 OR
             nrg_common.has_value_changed(new_version.trading_account_type, old_version.trading_account_type) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_code, old_version.instrument_code) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_type, old_version.instrument_type) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_country, old_version.instrument_country) = 1 OR
             nrg_common.has_value_changed(new_version.instrument_mic, old_version.instrument_mic) = 1 OR
             nrg_common.has_value_changed(new_version.is_reportable, old_version.is_reportable) = 1 OR
             nrg_common.has_value_changed(new_version.reportable_identifier_type, old_version.reportable_identifier_type) = 1 OR
             nrg_common.has_value_changed(new_version.broker_code, old_version.broker_code) = 1 OR
             nrg_common.has_value_changed(new_version.reportable_instrument_type, old_version.reportable_instrument_type) = 1 OR
             nrg_common.has_value_changed(new_version.derivative_type, old_version.derivative_type) = 1)
    WHEN NOT MATCHED THEN
      INSERT (regulatory_identification_code
              ,regulatory_identification_name
              ,logical_load_timestamp
              ,created_by
              ,create_timestamp
              ,updated_by
              ,update_timestamp
              ,effective_start_timestamp
              ,is_deleted
              ,regulation
              ,trading_account_type
              ,instrument_code
              ,instrument_type
              ,instrument_country
              ,instrument_mic
              ,is_reportable
              ,reportable_identifier_type
              ,broker_code
              ,reportable_instrument_type
              ,derivative_type)
      VALUES (new_version.regulatory_identification_code
             ,new_version.regulatory_identification_name
             ,lv_logical_load_timestamp
             ,p_user
             ,SYSTIMESTAMP
             ,p_user
             ,SYSTIMESTAMP
             ,p_effective_start_timestamp
             ,'NO'
             ,new_version.regulation
             ,new_version.trading_account_type
             ,new_version.instrument_code
             ,new_version.instrument_type
             ,new_version.instrument_country
             ,new_version.instrument_mic
             ,new_version.is_reportable
             ,new_version.reportable_identifier_type
             ,new_version.broker_code
             ,new_version.reportable_instrument_type
             ,new_version.derivative_type);


    ----
    -- Call stored procedure for products_gsko
    ----
              put_products_gsko  (p_user                        => p_user,
                                  p_effective_start_timestamp   => p_effective_start_timestamp,
                                  p_products_gsko               => p_products_gsko);
    ----
    -- Call stored procedure for products_gsko
    ----
              put_product_settings(p_user                       => p_user,
                                  p_effective_start_timestamp   => p_effective_start_timestamp,
                                  p_product_settings            => p_product_settings);

    put_inst_feed_setting (p_user                               => p_user,
                           p_effective_start_timestamp          => p_effective_start_timestamp,
                           p_inst_feed_setting                  => p_inst_feed_setting);

    put_economic_calendar_events(p_user                            => p_user,
                                 p_effective_start_timestamp       => p_effective_start_timestamp,
                                 p_economic_calendar_events        => p_economic_calendar_events);

    put_inst_to_economic_events(p_user                            => p_user,
                                p_effective_start_timestamp       => p_effective_start_timestamp,
                                p_inst_to_economic_events         => p_inst_to_economic_events);

     put_tax_treaties(p_user                            => p_user,
                      p_effective_start_timestamp       => p_effective_start_timestamp,
                      p_tax_treaties                    => p_tax_treaties);

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_product_set;

  -- ===================================================================================
  -- put_margin_tier_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a product margin tier data set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     MARGIN_TIERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Product Snapshot Start timestamp
  --     p_margin_tier                    Margin tiers table
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

/*PROCEDURE put_margin_tier_set ( p_user                            IN products.created_by%TYPE,
                                p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
                                p_margin_tier                     IN margin_tier_tab)
  IS

      lv_logical_load_timestamp    products.logical_load_timestamp%TYPE;

      TYPE lvtyp_old_margin_tier IS TABLE OF Margin_Tiers_Current_Data%rowtype;
      lv_old_margin_tier lvtyp_old_margin_tier;

  BEGIN

    logger.logger.set_module('put_margin_tier_set');

    -- set the logical load timestamp to now

    lv_logical_load_timestamp := SYSTIMESTAMP;


     put_snapshot_identifier (p_user                            => p_user,
                              p_effective_start_timestamp       => p_effective_start_timestamp,
                              p_snapshot_identifier             => p_snapshot_identifier,
                              p_logical_load_timestamp          => lv_logical_load_timestamp,
                              p_is_processed                    => 'YES');

       SELECT new_margin_tier.margin_tier_code,
              lv_logical_load_timestamp,
              p_user,
              SYSTIMESTAMP,
              p_user,
              SYSTIMESTAMP,
              p_effective_start_timestamp,
              new_margin_tier.product_setting_code,
              new_margin_tier.product_schema_code,
              new_margin_tier.trading_risk_schema_code,
              new_margin_tier.currency,
              new_margin_tier.margin_size_boundary,
              new_margin_tier.margin_rate,
              new_margin_tier.last_published_date,
              new_margin_tier.initial_published_date
              BULK COLLECT INTO lv_old_margin_tier
         FROM TABLE(CAST(p_margin_tier AS margin_tier_tab)) new_margin_tier;


   FORALL i IN 1..lv_old_margin_tier.COUNT
      INSERT  INTO margin_tiers_current_data
       VALUES lv_old_margin_tier(i);




  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_margin_tier_set;*/

  -- ===================================================================================
  -- put_snapshot_identifiers
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a product identifier list
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PRODUCT_SNAPSHOT_IDNTFRS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_snapshot_identifiers           Product Snapshot Identifiers
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_snapshot_identifiers(p_user                       IN product_snapshot_idntfrs.created_by%TYPE,
                                     p_snapshot_identifiers       IN product_snapshot_idntfr_tab) IS

    lv_logical_load_timestamp  product_snapshot_idntfrs.logical_load_timestamp%TYPE;
  BEGIN

    logger.logger.set_module('put_snapshot_identifiers');

    lv_logical_load_timestamp := SYSTIMESTAMP;

    IF p_snapshot_identifiers IS NOT NULL AND p_snapshot_identifiers.COUNT > 0 THEN
      FOR lv_cnt IN 1..p_snapshot_identifiers.COUNT LOOP
        put_snapshot_identifier (p_user                            => p_user,
                                 p_effective_start_timestamp       => gc_default_timestamp,
                                 p_snapshot_identifier             => p_snapshot_identifiers(lv_cnt).snapshot_id,
                                 p_logical_load_timestamp          => lv_logical_load_timestamp,
                                 p_is_processed                    => 'NO');
      END LOOP;
    END IF;
  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END;



  -- ===================================================================================
  -- put_snapshot_log
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a product identifier list
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PRODUCT_SNAPSHOT_IDNTFRS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_snapshot_identifiers           Product Snapshot Identifiers
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

    PROCEDURE put_snapshot_log      (p_user                            IN products.created_by%TYPE,
                                       p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                       p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
                                       p_is_processed                    IN product_snapshot_idntfrs.is_processed%TYPE
                                      ) IS
      lv_old_snapshot_id product_snapshot_idntfrs%ROWTYPE;

      lv_logical_load_timestamp  product_snapshot_idntfrs.logical_load_timestamp%TYPE;

    BEGIN

    logger.logger.set_module('put_snapshot_log');

    lv_logical_load_timestamp := SYSTIMESTAMP;


      BEGIN
        SELECT *
        INTO lv_old_snapshot_id
        FROM product_snapshot_idntfrs
        WHERE snapshot_identifier = p_snapshot_identifier;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          lv_old_snapshot_id.snapshot_identifier := NULL;
      END;

      IF lv_old_snapshot_id.snapshot_identifier IS NOT NULL THEN
        put_history_snapshot_idntfrs(p_old_product_identifier      => lv_old_snapshot_id,
                    p_effective_end_timestamp     => p_effective_start_timestamp,
                    p_action                      => 'U');
      END IF;

      MERGE INTO product_snapshot_idntfrs existing
      USING (SELECT p_snapshot_identifier AS snapshot_identifier
             FROM dual) new_identifier
      ON (existing.snapshot_identifier = new_identifier.snapshot_identifier)
      WHEN MATCHED THEN
        UPDATE
        SET logical_load_timestamp = lv_logical_load_timestamp,
            updated_by = p_user,
            update_timestamp = SYSTIMESTAMP,
            effective_start_timestamp = p_effective_start_timestamp,
            business_date = nrg_common.get_business_date(p_effective_start_timestamp),
            reporting_date = nrg_common.get_reporting_date(p_effective_start_timestamp),
            message_time = p_effective_start_timestamp,
            is_processed = p_is_processed
      WHEN NOT MATCHED THEN
        INSERT (snapshot_identifier,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                business_date,
                reporting_date,
                message_time,
                is_processed)
         VALUES(new_identifier.snapshot_identifier,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                nrg_common.get_business_date(p_effective_start_timestamp),
                nrg_common.get_reporting_date(p_effective_start_timestamp),
                p_effective_start_timestamp,
                p_is_processed);


    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);

    END put_snapshot_log;

    PROCEDURE put_instrument_type_language(p_user                           IN products.created_by%TYPE,
                                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                           p_instrument_type_language       IN instrument_type_languages_tab) IS

      lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

    BEGIN
      MERGE INTO instrument_type_languages_h old_version
       USING (SELECT old_version.*
        FROM instrument_type_languages old_version, TABLE(CAST(p_instrument_type_language AS instrument_type_languages_tab)) instr_lang
        WHERE old_version.instrument_type_code = instr_lang.instrument_type_code AND
              old_version.language_code = instr_lang.language_code AND
              (nrg_common.has_value_changed(old_version.instrument_type_default_name,instr_lang.instrument_type_default_name)= 1 OR
               nrg_common.has_value_changed(old_version.instrument_type_name,instr_lang.instrument_type_name)= 1) ) new_version
       ON (old_version.instrument_type_code         = new_version.instrument_type_code AND
           old_version.language_code                = new_version.language_code AND
           old_version.effective_start_timestamp    = new_version.effective_start_timestamp)
        WHEN MATCHED THEN
          UPDATE SET old_version.updated_by                    = new_version.updated_by,
                     old_version.action                        = 'U',
                     old_version.action_timestamp              = SYSTIMESTAMP,
                     old_version.update_timestamp              = new_version.update_timestamp,
                     old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
                     old_version.instrument_type_default_name  = new_version.instrument_type_default_name,
                     old_version.instrument_type_name          = new_version.instrument_type_name
        WHEN NOT MATCHED THEN
          INSERT
          (instrument_type_code,
           logical_load_timestamp,
           created_by,
           create_timestamp,
           updated_by,
           update_timestamp,
           effective_start_timestamp,
           effective_end_timestamp,
           action,
           action_timestamp,
           language_code,
           instrument_type_default_name,
           instrument_type_name)
        VALUES
          ( new_version.instrument_type_code,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.language_code,
            new_version.instrument_type_default_name,
            new_version.instrument_type_name);

      MERGE INTO instrument_type_languages old_version
      USING (SELECT DISTINCT instr_lang.instrument_type_code          AS instrument_type_code
                   ,instr_lang.language_code                 AS language_code
                   ,instr_lang.instrument_type_default_name  AS instrument_type_default_name
                   ,instr_lang.instrument_type_name          AS instrument_type_name
           FROM  TABLE(CAST(p_instrument_type_language AS instrument_type_languages_tab)) instr_lang) new_version
      ON (old_version.instrument_type_code = new_version.instrument_type_code
          AND old_version.language_code = new_version.language_code)
      WHEN MATCHED THEN
        UPDATE
        SET   old_version.logical_load_timestamp       = lv_logical_load_timestamp,
              old_version.updated_by                   = p_user,
              old_version.update_timestamp             = SYSTIMESTAMP,
              old_version.effective_start_timestamp    = p_effective_start_timestamp,
              old_version.instrument_type_default_name = new_version.instrument_type_default_name,
              old_version.instrument_type_name         = new_version.instrument_type_name
        WHERE (nrg_common.has_value_changed(old_version.instrument_type_default_name,new_version.instrument_type_default_name)= 1 OR
               nrg_common.has_value_changed(old_version.instrument_type_name,new_version.instrument_type_name)= 1
               )
      WHEN NOT MATCHED THEN
        INSERT  (instrument_type_code,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp,
                 language_code,
                 instrument_type_default_name,
                 instrument_type_name
                 )
          VALUES( new_version.instrument_type_code,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  new_version.language_code,
                  new_version.instrument_type_default_name,
                  new_version.instrument_type_name);
    END;

PROCEDURE put_holding_costs (p_user                           IN products.created_by%TYPE,
                             p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                             p_holding_costs                  IN holding_costs_tab) IS

      lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

BEGIN

MERGE INTO holding_costs_h old_version
       USING (SELECT old_version.*
        FROM holding_costs old_version, TABLE(CAST(p_holding_costs AS holding_costs_tab)) hcost
        WHERE old_version.holding_costs_code = hcost.holding_costs_code AND
              old_version.hc_schema_id = hcost.hc_schema_id AND
              (nrg_common.has_value_changed(old_version.hc_partner_charge,hcost.hc_partner_charge)= 1 OR
               nrg_common.has_value_changed(old_version.hc_customer_offset,hcost.hc_customer_offset)= 1 OR
               nrg_common.has_value_changed(old_version.hc_market_code,hcost.hc_market_code)= 1 OR
               nrg_common.has_value_changed(old_version.hc_instrument_type_code,hcost.hc_instrument_type_code)= 1 OR
               nrg_common.has_value_changed(old_version.hc_instrument_code,hcost.hc_instrument_code)= 1)) new_version
       ON (old_version.holding_costs_code        = new_version.holding_costs_code AND
           old_version.hc_schema_id              = new_version.hc_schema_id AND
           old_version.effective_start_timestamp = new_version.effective_start_timestamp)
        WHEN MATCHED THEN
          UPDATE SET old_version.updated_by                    = new_version.updated_by,
                     old_version.action                        = 'U',
                     old_version.action_timestamp              = SYSTIMESTAMP,
                     old_version.update_timestamp              = new_version.update_timestamp,
                     old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
                     old_version.hc_partner_charge             = new_version.hc_partner_charge,
                     old_version.hc_customer_offset            = new_version.hc_customer_offset,
                     old_version.hc_market_code                = new_version.hc_market_code,
                     old_version.hc_instrument_type_code       = new_version.hc_instrument_type_code,
                     old_version.hc_instrument_code            = new_version.hc_instrument_code
        WHEN NOT MATCHED THEN
          INSERT
          (holding_costs_code,
           hc_schema_id,
           logical_load_timestamp,
           created_by,
           create_timestamp,
           updated_by,
           update_timestamp,
           effective_start_timestamp,
           effective_end_timestamp,
           action,
           action_timestamp,
           hc_partner_charge,
           hc_customer_offset,
           hc_market_code,
           hc_instrument_type_code,
           hc_instrument_code)
        VALUES
          ( new_version.holding_costs_code,
            new_version.hc_schema_id,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.hc_partner_charge,
            new_version.hc_customer_offset,
            new_version.hc_market_code,
            new_version.hc_instrument_type_code,
            new_version.hc_instrument_code);


    DELETE holding_costs old_version
    WHERE NOT EXISTS (SELECT 1
                        FROM TABLE(CAST(p_holding_costs AS holding_costs_tab)) new_version
                       WHERE old_version.holding_costs_code = new_version.holding_costs_code
                         AND old_version.hc_schema_id       = new_version.hc_schema_id);

      MERGE INTO holding_costs old_version
      USING (SELECT DISTINCT hcost.holding_costs_code AS holding_costs_code,
                             hcost.hc_schema_id AS hc_schema_id,
                             hcost.hc_partner_charge AS hc_partner_charge,
                             hcost.hc_customer_offset AS hc_customer_offset,
                             hcost.hc_market_code AS hc_market_code,
                             hcost.hc_instrument_type_code AS hc_instrument_type_code,
                             hcost.hc_instrument_code AS hc_instrument_code
           FROM  TABLE(CAST(p_holding_costs AS holding_costs_tab)) hcost) new_version
      ON (old_version.holding_costs_code = new_version.holding_costs_code
          AND old_version.hc_schema_id = new_version.hc_schema_id)
      WHEN MATCHED THEN
        UPDATE
        SET   old_version.logical_load_timestamp       = lv_logical_load_timestamp,
              old_version.updated_by                   = p_user,
              old_version.update_timestamp             = SYSTIMESTAMP,
              old_version.effective_start_timestamp    = p_effective_start_timestamp,
              old_version.hc_partner_charge            = new_version.hc_partner_charge,
              old_version.hc_customer_offset           = new_version.hc_customer_offset,
              old_version.hc_market_code               = new_version.hc_market_code,
              old_version.hc_instrument_type_code      = new_version.hc_instrument_type_code,
              old_version.hc_instrument_code           = new_version.hc_instrument_code
        WHERE (nrg_common.has_value_changed(old_version.hc_partner_charge,new_version.hc_partner_charge)= 1 OR
               nrg_common.has_value_changed(old_version.hc_customer_offset,new_version.hc_customer_offset)= 1 OR
               nrg_common.has_value_changed(old_version.hc_market_code,new_version.hc_market_code)= 1 OR
               nrg_common.has_value_changed(old_version.hc_instrument_type_code,new_version.hc_instrument_type_code)= 1 OR
               nrg_common.has_value_changed(old_version.hc_instrument_code,new_version.hc_instrument_code)= 1
               )
      WHEN NOT MATCHED THEN
        INSERT  (holding_costs_code,
                 hc_schema_id,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp,
                 hc_partner_charge,
                 hc_customer_offset,
                 hc_market_code,
                 hc_instrument_type_code,
                 hc_instrument_code
                 )
          VALUES (new_version.holding_costs_code,
                  new_version.hc_schema_id,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  new_version.hc_partner_charge,
                  new_version.hc_customer_offset,
                  new_version.hc_market_code,
                  new_version.hc_instrument_type_code,
                  new_version.hc_instrument_code);

    END;

PROCEDURE put_reference_holding_costs (p_user                           IN products.created_by%TYPE,
                                       p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                       p_reference_holding_costs        IN reference_holding_costs_tab) IS

      lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

BEGIN

MERGE INTO reference_holding_costs_h old_version
       USING (SELECT old_version.*
        FROM reference_holding_costs old_version, TABLE(CAST(p_reference_holding_costs AS reference_holding_costs_tab)) rhcost
        WHERE old_version.reference_holding_costs_code = rhcost.reference_holding_costs_code AND
              (nrg_common.has_value_changed(old_version.rhc_schema_id,rhcost.rhc_schema_id)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_country_offset,rhcost.rhc_country_offset)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_market_code,rhcost.rhc_market_code)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_instrument_type_code,rhcost.rhc_instrument_type_code)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_instrument_code,rhcost.rhc_instrument_code)= 1)) new_version
       ON (old_version.reference_holding_costs_code = new_version.reference_holding_costs_code AND
           old_version.effective_start_timestamp    = new_version.effective_start_timestamp)
        WHEN MATCHED THEN
          UPDATE SET old_version.updated_by                    = new_version.updated_by,
                     old_version.action                        = 'U',
                     old_version.action_timestamp              = SYSTIMESTAMP,
                     old_version.update_timestamp              = new_version.update_timestamp,
                     old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
                     old_version.rhc_schema_id                 = new_version.rhc_schema_id,
                     old_version.rhc_country_offset            = new_version.rhc_country_offset,
                     old_version.rhc_market_code               = new_version.rhc_market_code,
                     old_version.rhc_instrument_type_code      = new_version.rhc_instrument_type_code,
                     old_version.rhc_instrument_code           = new_version.rhc_instrument_code
        WHEN NOT MATCHED THEN
          INSERT
          (reference_holding_costs_code,
           logical_load_timestamp,
           created_by,
           create_timestamp,
           updated_by,
           update_timestamp,
           effective_start_timestamp,
           effective_end_timestamp,
           action,
           action_timestamp,
           rhc_schema_id,
           rhc_country_offset,
           rhc_market_code,
           rhc_instrument_type_code,
           rhc_instrument_code)
        VALUES
          ( new_version.reference_holding_costs_code,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.rhc_schema_id,
            new_version.rhc_country_offset,
            new_version.rhc_market_code,
            new_version.rhc_instrument_type_code,
            new_version.rhc_instrument_code);


    DELETE reference_holding_costs old_version
    WHERE NOT EXISTS (SELECT 1
                        FROM TABLE(CAST(p_reference_holding_costs AS reference_holding_costs_tab)) new_version
                       WHERE old_version.reference_holding_costs_code = new_version.reference_holding_costs_code);

      MERGE INTO reference_holding_costs old_version
      USING (SELECT DISTINCT rhcost.reference_holding_costs_code AS reference_holding_costs_code,
                             rhcost.rhc_schema_id AS rhc_schema_id,
                             rhcost.rhc_country_offset AS rhc_country_offset,
                             rhcost.rhc_market_code AS rhc_market_code,
                             rhcost.rhc_instrument_type_code AS rhc_instrument_type_code,
                             rhcost.rhc_instrument_code AS rhc_instrument_code
           FROM  TABLE(CAST(p_reference_holding_costs AS reference_holding_costs_tab)) rhcost) new_version
      ON (old_version.reference_holding_costs_code = new_version.reference_holding_costs_code)
      WHEN MATCHED THEN
        UPDATE
        SET   old_version.logical_load_timestamp       = lv_logical_load_timestamp,
              old_version.updated_by                   = p_user,
              old_version.update_timestamp             = SYSTIMESTAMP,
              old_version.effective_start_timestamp    = p_effective_start_timestamp,
              old_version.rhc_schema_id                = new_version.rhc_schema_id,
              old_version.rhc_country_offset           = new_version.rhc_country_offset,
              old_version.rhc_market_code              = new_version.rhc_market_code,
              old_version.rhc_instrument_type_code     = new_version.rhc_instrument_type_code,
              old_version.rhc_instrument_code          = new_version.rhc_instrument_code
        WHERE (nrg_common.has_value_changed(old_version.rhc_schema_id,new_version.rhc_schema_id)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_country_offset,new_version.rhc_country_offset)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_market_code,new_version.rhc_market_code)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_instrument_type_code,new_version.rhc_instrument_type_code)= 1 OR
               nrg_common.has_value_changed(old_version.rhc_instrument_code,new_version.rhc_instrument_code)= 1
               )
      WHEN NOT MATCHED THEN
        INSERT  (reference_holding_costs_code,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp,
                 rhc_schema_id,
                 rhc_country_offset,
                 rhc_market_code,
                 rhc_instrument_type_code,
                 rhc_instrument_code
                 )
          VALUES (new_version.reference_holding_costs_code,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  new_version.rhc_schema_id,
                  new_version.rhc_country_offset,
                  new_version.rhc_market_code,
                  new_version.rhc_instrument_type_code,
                  new_version.rhc_instrument_code);

    END;



	 PROCEDURE put_inst_type_broker_margins(p_user                           IN products.created_by%TYPE,
                                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                           p_inst_type_broker_margins       IN inst_type_broker_margins_tab) IS

      lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

    BEGIN
      MERGE INTO inst_type_broker_margins_h old_version
       USING (SELECT old_version.*
        FROM inst_type_broker_margins old_version, TABLE(CAST(p_inst_type_broker_margins AS inst_type_broker_margins_tab)) instr_bro_marg
        WHERE old_version.instrument_type_code = instr_bro_marg.instrument_type_code AND
              old_version.broker_margin_name = instr_bro_marg.broker_margin_name AND
              (nrg_common.has_value_changed(old_version.broker_margin_value,instr_bro_marg.broker_margin_value)= 1 )) new_version
       ON (old_version.instrument_type_code         = new_version.instrument_type_code AND
           old_version.broker_margin_name                = new_version.broker_margin_name AND
           old_version.effective_start_timestamp    = new_version.effective_start_timestamp)
        WHEN MATCHED THEN
          UPDATE SET old_version.updated_by                    = new_version.updated_by,
                     old_version.action                        = 'U',
                     old_version.action_timestamp              = SYSTIMESTAMP,
                     old_version.update_timestamp              = new_version.update_timestamp,
                     old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
                     old_version.broker_margin_value  = new_version.broker_margin_value
        WHEN NOT MATCHED THEN
          INSERT
          (instrument_type_code,
		  broker_margin_name,
           logical_load_timestamp,
           created_by,
           create_timestamp,
           updated_by,
           update_timestamp,
           effective_start_timestamp,
           effective_end_timestamp,
           action,
           action_timestamp,
           broker_margin_value)
        VALUES
          ( new_version.instrument_type_code,
		  new_version.broker_margin_name,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.broker_margin_value);

      MERGE INTO inst_type_broker_margins old_version
      USING (SELECT DISTINCT instr_bro_marg.instrument_type_code          AS instrument_type_code
                   ,instr_bro_marg.broker_margin_name   AS broker_margin_name
                   ,instr_bro_marg.broker_margin_value  AS broker_margin_value

           FROM  TABLE(CAST(p_inst_type_broker_margins AS inst_type_broker_margins_tab)) instr_bro_marg) new_version
      ON (old_version.instrument_type_code = new_version.instrument_type_code
          AND old_version.broker_margin_name = new_version.broker_margin_name)
      WHEN MATCHED THEN
        UPDATE
        SET   old_version.logical_load_timestamp       = lv_logical_load_timestamp,
              old_version.updated_by                   = p_user,
              old_version.update_timestamp             = SYSTIMESTAMP,
              old_version.effective_start_timestamp    = p_effective_start_timestamp,
              old_version.broker_margin_value = new_version.broker_margin_value
        WHERE (nrg_common.has_value_changed(old_version.broker_margin_value,new_version.broker_margin_value)= 1
               )
      WHEN NOT MATCHED THEN
        INSERT  (instrument_type_code,
		broker_margin_name,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp,
                 broker_margin_value
                 )
          VALUES( new_version.instrument_type_code,
		  new_version.broker_margin_name,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  new_version.broker_margin_value);
    END;

  PROCEDURE put_prdct_sttngs_inst_schm (p_user                           IN prdct_sttngs_inst_schema.created_by%TYPE,
                                        p_effective_start_timestamp      IN prdct_sttngs_inst_schema.effective_start_timestamp%TYPE,
                                        p_prdct_sttngs_inst_schm         IN prdct_sttngs_key_tab) IS

    ltab_prod prdct_sttngs_inst_schm_tab;

  BEGIN

    SELECT prdct_sttngs_inst_schm_obj(
           product_setting_code,
           instrument_schema_code,
           instrument_code,
           platform,
           wrapper_code,
           is_findable,
           is_tradable,
           manual_execution_only,
           is_position_increasing_alwd,
           is_shorting_available,
           apply_carrying_costs,
           apply_prime_margin_buffer,
           is_guaranteed_stop_loss_alwd,
           is_regular_stop_loss_alwd,
           is_trailing_stop_loss_alwd,
           is_take_profit_alwd,
           is_limit_entry_alwd,
           is_stop_entry_alwd,
           is_market_order_alwd,
           Is_Rfq_Execution_Alwd,
           stop_loss_buffer,
           stop_loss_buffer_type,
           stop_entry_buffer,
           stop_entry_buffer_type,
           min_tenor,
           max_tenor,
           is_short_only)
      BULK COLLECT INTO ltab_prod
      FROM (SELECT product_setting_code                                                                               AS product_setting_code,
                   DECODE(dimension_key,'InstrumentSchema','Default',dimension_key)                                   AS instrument_schema_code,
                   instrument_code                                                                                    AS instrument_code,
                   'NG'                                                                                               AS platform,
                   wrapper_code                                                                                       AS wrapper_code,
                   DECODE("'IsFindable'",1,'YES',2,'NO')                                                              AS Is_Findable,
                   DECODE("'IsTradable'",1,'YES',2,'NO')                                                              AS Is_Tradable,
                   DECODE("'ManualExecutionOnly'",1,'YES',2,'NO')                                                     AS Manual_Execution_Only,
                   DECODE("'IsPositionIncreasingAllowed'",1,'YES',2,'NO')                                             AS is_position_increasing_alwd,
                   DECODE("'IsShortingAvailable'",1,'YES',2,'NO')                                                     AS Is_Shorting_Available,
                   DECODE("'ApplyCarryingCosts'",1,'YES',2,'NO')                                                      AS Apply_Carrying_Costs,
                   DECODE("'ApplyPrimeMarginBuffer'",1,'YES',2,'NO')                                                  AS Apply_Prime_Margin_Buffer,
                   DECODE("'IsGuaranteedStopLossAllowed'",1,'YES',2,'NO')                                             AS is_guaranteed_stop_loss_alwd,
                   DECODE("'IsRegularStopLossAllowed'",1,'YES',2,'NO')                                                AS is_regular_stop_loss_alwd,
                   DECODE("'IsTrailingStopLossAllowed'",1,'YES',2,'NO')                                               AS is_trailing_stop_loss_alwd,
                   DECODE("'IsTakeProfitAllowed'",1,'YES',2,'NO')                                                     AS is_take_profit_alwd,
                   DECODE("'IsLimitEntryAllowed'",1,'YES',2,'NO')                                                     AS is_limit_entry_alwd,
                   DECODE("'IsStopEntryAllowed'",1,'YES',2,'NO')                                                      AS is_stop_entry_alwd,
                   DECODE("'IsMarketOrderAllowed'",1,'YES',2,'NO')                                                    AS is_market_order_alwd,
                   DECODE("'IsRfqExecutionAllowed'",1,'YES',2,'NO')                                                   AS Is_Rfq_Execution_Alwd,
                   "'StopLossBuffer'"                                                                                 AS Stop_Loss_Buffer,
                   DECODE("'StopLossBufferType'",0,'PercentOfPrice',1,'PercentOfMinSpread',2,'AbsolutePointValue')    AS Stop_Loss_Buffer_Type,
                   "'StopEntryBuffer'"                                                                                AS Stop_Entry_Buffer,
                   DECODE("'StopEntryBufferType'",0,'PercentOfPrice',1,'PercentOfMinSpread',2,'AbsolutePointValue')   AS Stop_Entry_Buffer_Type,
                   "'MinTenor'"                                                                                       AS Min_Tenor,
                   "'MaxTenor'"                                                                                       AS Max_Tenor,
                   DECODE("'ShortOnly'",1,'YES',2,'NO')                                                               AS is_short_only
      FROM (SELECT DISTINCT
                   product_setting_code,
                   property_key,
                   instrument_code,
                   wrapper_code,
                   dimension_key,
                   dimension_value
                   FROM
                   TABLE(CAST(p_prdct_sttngs_inst_schm AS prdct_sttngs_key_tab)))
                                             pivot(MIN(dimension_value) FOR property_key IN('ApplyPrimeMarginBuffer',
                                                                                            'IsFindable',
                                                                                            'IsTradable',
                                                                                            'ManualExecutionOnly',
                                                                                            'IsPositionIncreasingAllowed',
                                                                                            'IsShortingAvailable',
                                                                                            'ApplyCarryingCosts',
                                                                                            'IsGuaranteedStopLossAllowed',
                                                                                            'IsRegularStopLossAllowed',
                                                                                            'IsTrailingStopLossAllowed',
                                                                                            'IsTakeProfitAllowed',
                                                                                            'IsLimitEntryAllowed',
                                                                                            'IsStopEntryAllowed',
                                                                                            'IsMarketOrderAllowed',
                                                                                            'IsRfqExecutionAllowed',
                                                                                            'MinTenor',
                                                                                            'MaxTenor',
                                                                                            'StopLossBufferType',
                                                                                            'StopLossBuffer',
                                                                                            'StopEntryBufferType',
                                                                                            'StopEntryBuffer',
                                                                                            'ShortOnly')));



  MERGE INTO prdct_sttngs_inst_schema_h history
       USING (SELECT old_version.*
        FROM prdct_sttngs_inst_schema old_version
        LEFT JOIN (SELECT * FROM TABLE(ltab_prod)) new_version
           ON old_version.product_setting_code   = new_version.product_setting_code
          AND old_version.instrument_schema_code = new_version.instrument_schema_code
        WHERE (old_version.is_deleted IS NULL OR old_version.is_deleted = 'NO')
          AND ((nrg_common.has_value_changed(old_version.instrument_code,new_version.instrument_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.platform,new_version.platform)= 1 )
           OR  (nrg_common.has_value_changed(old_version.wrapper_code,new_version.wrapper_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_findable,new_version.is_findable)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_tradable,new_version.is_tradable)= 1 )
           OR  (nrg_common.has_value_changed(old_version.manual_execution_only,new_version.manual_execution_only)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_position_increasing_alwd,new_version.is_position_increasing_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_shorting_available,new_version.is_shorting_available)= 1 )
           OR  (nrg_common.has_value_changed(old_version.apply_carrying_costs,new_version.apply_carrying_costs)= 1 )
           OR  (nrg_common.has_value_changed(old_version.apply_prime_margin_buffer,new_version.apply_prime_margin_buffer)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_guaranteed_stop_loss_alwd,new_version.is_guaranteed_stop_loss_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_regular_stop_loss_alwd,new_version.is_regular_stop_loss_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_trailing_stop_loss_alwd,new_version.is_trailing_stop_loss_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_take_profit_alwd,new_version.is_take_profit_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_limit_entry_alwd,new_version.is_limit_entry_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_stop_entry_alwd,new_version.is_stop_entry_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_market_order_alwd,new_version.is_market_order_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_rfq_execution_alwd,new_version.is_rfq_execution_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_loss_buffer,new_version.stop_loss_buffer)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_loss_buffer_type,new_version.stop_loss_buffer_type)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_entry_buffer,new_version.stop_entry_buffer)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_entry_buffer_type,new_version.stop_entry_buffer_type)= 1 )
           OR  (nrg_common.has_value_changed(old_version.min_tenor,new_version.min_tenor)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_tenor,new_version.max_tenor)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_short_only,new_version.is_short_only)= 1 )
          )) changes
       ON (history.product_setting_code      = changes.product_setting_code
       AND history.instrument_schema_code    = changes.instrument_schema_code
       AND history.effective_start_timestamp = changes.effective_start_timestamp)
     WHEN MATCHED THEN UPDATE SET
            history.updated_by                    	 = changes.updated_by,
            history.action                           = 'U',
            history.action_timestamp                 = SYSTIMESTAMP,
            history.update_timestamp                 = changes.update_timestamp,
            history.effective_end_timestamp          = p_effective_start_timestamp,
            history.instrument_code                  = changes.instrument_code,
            history.platform                         = changes.platform,
            history.wrapper_code                     = changes.wrapper_code,
            history.is_findable                      = changes.is_findable,
            history.is_tradable                      = changes.is_tradable,
            history.manual_execution_only            = changes.manual_execution_only,
            history.is_position_increasing_alwd      = changes.is_position_increasing_alwd,
            history.is_shorting_available            = changes.is_shorting_available,
            history.apply_carrying_costs             = changes.apply_carrying_costs,
            history.apply_prime_margin_buffer        = changes.apply_prime_margin_buffer,
            history.is_guaranteed_stop_loss_alwd     = changes.is_guaranteed_stop_loss_alwd,
            history.is_regular_stop_loss_alwd        = changes.is_regular_stop_loss_alwd,
            history.is_trailing_stop_loss_alwd       = changes.is_trailing_stop_loss_alwd,
            history.is_take_profit_alwd              = changes.is_take_profit_alwd,
            history.is_limit_entry_alwd              = changes.is_limit_entry_alwd,
            history.is_stop_entry_alwd               = changes.is_stop_entry_alwd,
            history.is_market_order_alwd             = changes.is_market_order_alwd,
            history.is_rfq_execution_alwd            = changes.is_rfq_execution_alwd,
            history.stop_loss_buffer                 = changes.stop_loss_buffer,
            history.stop_loss_buffer_type            = changes.stop_loss_buffer_type,
            history.stop_entry_buffer                = changes.stop_entry_buffer,
            history.stop_entry_buffer_type           = changes.stop_entry_buffer_type,
            history.min_tenor                        = changes.min_tenor,
            history.max_tenor                        = changes.max_tenor,
            history.is_short_only                    = changes.is_short_only
     WHEN NOT MATCHED THEN INSERT
           (product_setting_code,
            instrument_schema_code,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,
            instrument_code,
            platform,
            wrapper_code,
            is_findable,
            is_tradable,
            manual_execution_only,
            is_position_increasing_alwd,
            is_shorting_available,
            apply_carrying_costs,
            apply_prime_margin_buffer,
            is_guaranteed_stop_loss_alwd,
            is_regular_stop_loss_alwd,
            is_trailing_stop_loss_alwd,
            is_take_profit_alwd,
            is_limit_entry_alwd,
            is_stop_entry_alwd,
            is_market_order_alwd,
            is_rfq_execution_alwd,
            stop_loss_buffer,
            stop_loss_buffer_type,
            stop_entry_buffer,
            stop_entry_buffer_type,
            min_tenor,
            max_tenor,
            is_short_only,
            is_deleted)
    VALUES (changes.product_setting_code,
            changes.instrument_schema_code,
            changes.logical_load_timestamp,
            changes.created_by,
            changes.create_timestamp,
            changes.updated_by,
            changes.update_timestamp,
            changes.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            changes.instrument_code,
            changes.platform,
            changes.wrapper_code,
            changes.is_findable,
            changes.is_tradable,
            changes.manual_execution_only,
            changes.is_position_increasing_alwd,
            changes.is_shorting_available,
            changes.apply_carrying_costs,
            changes.apply_prime_margin_buffer,
            changes.is_guaranteed_stop_loss_alwd,
            changes.is_regular_stop_loss_alwd,
            changes.is_trailing_stop_loss_alwd,
            changes.is_take_profit_alwd,
            changes.is_limit_entry_alwd,
            changes.is_stop_entry_alwd,
            changes.is_market_order_alwd,
            changes.is_rfq_execution_alwd,
            changes.stop_loss_buffer,
            changes.stop_loss_buffer_type,
            changes.stop_entry_buffer,
            changes.stop_entry_buffer_type,
            changes.min_tenor,
            changes.max_tenor,
            changes.is_short_only,
            changes.is_deleted);

  MERGE INTO prdct_sttngs_inst_schema old_version
       USING (SELECT * FROM TABLE(ltab_prod)) new_version
          ON (old_version.product_setting_code   = new_version.product_setting_code
          AND old_version.instrument_schema_code = new_version.instrument_schema_code)
     WHEN MATCHED THEN UPDATE SET
            old_version.updated_by                       = p_user,
            old_version.update_timestamp                 = systimestamp,
            old_version.effective_start_timestamp        = p_effective_start_timestamp,
            old_version.instrument_code                  = new_version.instrument_code,
            old_version.platform                         = new_version.platform,
            old_version.wrapper_code                     = new_version.wrapper_code,
            old_version.is_findable                      = new_version.is_findable,
            old_version.is_tradable                      = new_version.is_tradable,
            old_version.manual_execution_only            = new_version.manual_execution_only,
            old_version.is_position_increasing_alwd      = new_version.is_position_increasing_alwd,
            old_version.is_shorting_available            = new_version.is_shorting_available,
            old_version.apply_carrying_costs             = new_version.apply_carrying_costs,
            old_version.apply_prime_margin_buffer        = new_version.apply_prime_margin_buffer,
            old_version.is_guaranteed_stop_loss_alwd     = new_version.is_guaranteed_stop_loss_alwd,
            old_version.is_regular_stop_loss_alwd        = new_version.is_regular_stop_loss_alwd,
            old_version.is_trailing_stop_loss_alwd       = new_version.is_trailing_stop_loss_alwd,
            old_version.is_take_profit_alwd              = new_version.is_take_profit_alwd,
            old_version.is_limit_entry_alwd              = new_version.is_limit_entry_alwd,
            old_version.is_stop_entry_alwd               = new_version.is_stop_entry_alwd,
            old_version.is_market_order_alwd             = new_version.is_market_order_alwd,
            old_version.is_rfq_execution_alwd            = new_version.is_rfq_execution_alwd,
            old_version.stop_loss_buffer                 = new_version.stop_loss_buffer,
            old_version.stop_loss_buffer_type            = new_version.stop_loss_buffer_type,
            old_version.stop_entry_buffer                = new_version.stop_entry_buffer,
            old_version.stop_entry_buffer_type           = new_version.stop_entry_buffer_type,
            old_version.min_tenor                        = new_version.min_tenor,
            old_version.max_tenor                        = new_version.max_tenor,
            old_version.is_short_only                    = new_version.is_short_only
        WHERE ((nrg_common.has_value_changed(old_version.instrument_code,new_version.instrument_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.platform,new_version.platform)= 1 )
           OR  (nrg_common.has_value_changed(old_version.wrapper_code,new_version.wrapper_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_findable,new_version.is_findable)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_tradable,new_version.is_tradable)= 1 )
           OR  (nrg_common.has_value_changed(old_version.manual_execution_only,new_version.manual_execution_only)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_position_increasing_alwd,new_version.is_position_increasing_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_shorting_available,new_version.is_shorting_available)= 1 )
           OR  (nrg_common.has_value_changed(old_version.apply_carrying_costs,new_version.apply_carrying_costs)= 1 )
           OR  (nrg_common.has_value_changed(old_version.apply_prime_margin_buffer,new_version.apply_prime_margin_buffer)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_guaranteed_stop_loss_alwd,new_version.is_guaranteed_stop_loss_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_regular_stop_loss_alwd,new_version.is_regular_stop_loss_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_trailing_stop_loss_alwd,new_version.is_trailing_stop_loss_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_take_profit_alwd,new_version.is_take_profit_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_limit_entry_alwd,new_version.is_limit_entry_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_stop_entry_alwd,new_version.is_stop_entry_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_market_order_alwd,new_version.is_market_order_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_rfq_execution_alwd,new_version.is_rfq_execution_alwd)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_loss_buffer,new_version.stop_loss_buffer)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_loss_buffer_type,new_version.stop_loss_buffer_type)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_entry_buffer,new_version.stop_entry_buffer)= 1 )
           OR  (nrg_common.has_value_changed(old_version.stop_entry_buffer_type,new_version.stop_entry_buffer_type)= 1 )
           OR  (nrg_common.has_value_changed(old_version.min_tenor,new_version.min_tenor)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_tenor,new_version.max_tenor)= 1 )
           OR  (nrg_common.has_value_changed(old_version.is_short_only,new_version.is_short_only)= 1 )
          )
     WHEN NOT MATCHED THEN INSERT
           (product_setting_code,
            instrument_schema_code,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            instrument_code,
            platform,
            wrapper_code,
            is_findable,
            is_tradable,
            manual_execution_only,
            is_position_increasing_alwd,
            is_shorting_available,
            apply_carrying_costs,
            apply_prime_margin_buffer,
            is_guaranteed_stop_loss_alwd,
            is_regular_stop_loss_alwd,
            is_trailing_stop_loss_alwd,
            is_take_profit_alwd,
            is_limit_entry_alwd,
            is_stop_entry_alwd,
            is_market_order_alwd,
            is_rfq_execution_alwd,
            stop_loss_buffer,
            stop_loss_buffer_type,
            stop_entry_buffer,
            stop_entry_buffer_type,
            min_tenor,
            max_tenor,
            is_short_only,
            is_deleted)
    VALUES (new_version.product_setting_code,
            new_version.instrument_schema_code,
            systimestamp,
            p_user,
            systimestamp,
            p_user,
            systimestamp,
            p_effective_start_timestamp,
            new_version.instrument_code,
            new_version.platform,
            new_version.wrapper_code,
            new_version.is_findable,
            new_version.is_tradable,
            new_version.manual_execution_only,
            new_version.is_position_increasing_alwd,
            new_version.is_shorting_available,
            new_version.apply_carrying_costs,
            new_version.apply_prime_margin_buffer,
            new_version.is_guaranteed_stop_loss_alwd,
            new_version.is_regular_stop_loss_alwd,
            new_version.is_trailing_stop_loss_alwd,
            new_version.is_take_profit_alwd,
            new_version.is_limit_entry_alwd,
            new_version.is_stop_entry_alwd,
            new_version.is_market_order_alwd,
            new_version.is_rfq_execution_alwd,
            new_version.stop_loss_buffer,
            new_version.stop_loss_buffer_type,
            new_version.stop_entry_buffer,
            new_version.stop_entry_buffer_type,
            new_version.min_tenor,
            new_version.max_tenor,
            new_version.is_short_only,
            'NO');

    UPDATE prdct_sttngs_inst_schema ps
    SET ps.is_deleted = 'YES'
    WHERE NOT EXISTS
    (SELECT 1 FROM TABLE(ltab_prod) nw
      WHERE nw.product_setting_code   = ps.product_setting_code
        AND nw.instrument_schema_code = ps.instrument_schema_code);

  END;


  PROCEDURE put_prdct_sttngs_inst_schm_ccy (p_user                           IN prdct_sttngs_inst_schema_ccy.created_by%TYPE,
                                            p_effective_start_timestamp      IN prdct_sttngs_inst_schema_ccy.effective_start_timestamp%TYPE,
                                            p_prdct_sttngs_inst_schm_ccy     IN prdct_sttngs_key_tab) IS

    ltab_prod_ccy prdct_sttngs_inst_schm_ccy_tab;

  BEGIN

    SELECT prdct_sttngs_inst_schm_ccy_obj(
           product_setting_code,
           instrument_schema_code,
           currency,
           instrument_code,
           platform,
           wrapper_code,
           cluster_limit,
           gslo_cluster_limit,
           max_pstn_sz_long,
           max_pstn_sz_shrt,
           max_pstn_sz_gslo_long,
           max_pstn_sz_gslo_shrt,
           min_requested_quantity,
           max_trade_quantity,
           no_of_decimals,
           max_limit_entry_order_sz,
           max_stop_entry_order_sz,
           min_stake_size,
           max_stake_sz_individual_up,
           max_stake_sz_individual_dn,
           max_stake_sz_concurrent_up,
           max_stake_sz_concurrent_dn)
      BULK COLLECT INTO ltab_prod_ccy
      FROM (
    SELECT product_setting_code                                                                                                       AS product_setting_code,
           CASE WHEN substr(dimension_key, 1, 3) = 'CMC' AND instr(dimension_key, '|', 1, 1) = 0 THEN dimension_key
                WHEN substr(dimension_key, 1, 3) = 'CMC' AND instr(dimension_key, '|', 1, 1) != 0 THEN
                nvl(replace(substr(dimension_key, 1, instr(dimension_key, '|',1, 1) -1),'InstrumentSchema','Default'),'Default')
             ELSE 'Default' END                                                                                                       AS instrument_schema_code,
           CASE WHEN length(dimension_key) = 3 AND instr(dimension_key, '|', 1, 1) = 0 THEN dimension_key
                WHEN length(dimension_key) != 3 AND instr(dimension_key, '|', 1, 1) != 0 THEN
                nvl(replace(substr(dimension_key, instr(dimension_key, '|', 1, 1) + 1, 3),'Cur','NA'),'NA')
                ELSE 'NA' END                                                                                                         AS currency,
           instrument_code                                                                                                            AS instrument_code,
           'NG'                                                                                                                       AS platform,
           wrapper_code                                                                                                               AS wrapper_code,
           decode(wrapper_code,'A-EOVH',cluster_limit_sb,cluster_limit_cfd)                                                           AS cluster_limit,
           decode(wrapper_code,'A-EOVH',gslo_cluster_limit_sb,gslo_cluster_limit_cfd)                                                 AS gslo_cluster_limit,
           decode(wrapper_code,'A-EOVH',max_pstn_sz_long_sb,max_pstn_sz_long_cfd)                                                     AS max_pstn_sz_long,
           decode(wrapper_code,'A-EOVH',max_pstn_sz_shrt_sb,max_pstn_sz_shrt_cfd)                                                     AS max_pstn_sz_shrt,
           decode(wrapper_code,'A-EOVH',max_pstn_sz_gslo_long_sb,max_pstn_sz_gslo_long_cfd)                                           AS max_pstn_sz_gslo_long,
           decode(wrapper_code,'A-EOVH',max_pstn_sz_gslo_shrt_sb,max_pstn_sz_gslo_shrt_cfd)                                           AS max_pstn_sz_gslo_shrt,
           decode(wrapper_code,'A-EOVH',min_requested_quantity_sb,min_requested_quantity_cfd)                                         AS min_requested_quantity,
           decode(wrapper_code,'A-EOVH',max_trade_quantity_sb,max_trade_quantity_cfd)                                                 AS max_trade_quantity,
           decode(wrapper_code,'A-EOVH',no_of_decimals_sb,no_of_decimals_cfd)                                                         AS no_of_decimals,
           decode(wrapper_code,'A-EOVH',max_limit_entry_order_sz_sb,max_limit_entry_order_sz_cfd)                                     AS max_limit_entry_order_sz,
           decode(wrapper_code,'A-EOVH',max_stop_entry_order_sz_sb,max_stop_entry_order_sz_cfd)                                       AS max_stop_entry_order_sz,
           min_stake_size                                                                                                             AS min_stake_size,
           max_stake_sz_individual_up                                                                                                 AS max_stake_sz_individual_up,
           max_stake_sz_individual_dn                                                                                                 AS max_stake_sz_individual_dn,
           max_stake_sz_concurrent_up                                                                                                 AS max_stake_sz_concurrent_up,
           max_stake_sz_concurrent_dn                                                                                                 AS max_stake_sz_concurrent_dn
      FROM (SELECT DISTINCT
                   product_setting_code,
                   property_key,
                   instrument_code,
                   wrapper_code,
                   dimension_key,
                   dimension_value
              FROM TABLE(CAST(p_prdct_sttngs_inst_schm_ccy AS prdct_sttngs_key_tab))
             WHERE wrapper_code not in ('X-MNRM','X-MNRN'))
             pivot(MIN(dimension_value) FOR property_key IN('PendingOrderClusterLimitNgCfd'                   AS cluster_limit_cfd,
                                                            'PendingOrderClusterLimitNgSb'                    AS cluster_limit_sb,
                                                            'GsloClusterLimitNgCfd'                           AS gslo_cluster_limit_cfd,
                                                            'GsloClusterLimitNgSb'                            AS gslo_cluster_limit_sb,
                                                            'MaxPositionSizeLongNgCfd'                        AS max_pstn_sz_long_cfd,
                                                            'MaxPositionSizeLongNgSb'                         AS max_pstn_sz_long_sb,
                                                            'MaxPositionSizeShortNgCfd'                       AS max_pstn_sz_shrt_cfd,
                                                            'MaxPositionSizeShortNgSb'                        AS max_pstn_sz_shrt_sb,
                                                            'MaxGsloPositionSizeLongNgCfd'                    AS max_pstn_sz_gslo_long_cfd,
                                                            'MaxGsloPositionSizeLongNgSb'                     AS max_pstn_sz_gslo_long_sb,
                                                            'MaxGsloPositionSizeShortNgCfd'                   AS max_pstn_sz_gslo_shrt_cfd,
                                                            'MaxGsloPositionSizeShortNgSb'                    AS max_pstn_sz_gslo_shrt_sb,
                                                            'RequestedQuantityUnitsMinimum'                   AS min_requested_quantity_cfd,
                                                            'RequestedQuantityAmountPerPointMinimum'          AS min_requested_quantity_sb,
                                                            'TradeQuantityUnitsMaximum'                       AS max_trade_quantity_cfd,
                                                            'TradeQuantityAmountPerPointMaximum'              AS max_trade_quantity_sb,
                                                            'QuantityUnitsNumberOfDecimals'                   AS no_of_decimals_cfd,
                                                            'QuantityAmountPerPointNumberOfDecimals'          AS no_of_decimals_sb,
                                                            'LimitEntryOrderSizeUnitsMaximumNgCfd'            AS max_limit_entry_order_sz_cfd,
                                                            'LimitEntryOrderSizeAmountPerPointMaximumNgSb'    AS max_limit_entry_order_sz_sb,
                                                            'StopEntryOrderSizeUnitsMaximumNgCfd'             AS max_stop_entry_order_sz_cfd,
                                                            'StopEntryOrderSizeAmountPerPointMaximumNgSb'     AS max_stop_entry_order_sz_sb,
                                                            'MinStakeSize'                                    AS min_stake_size,
                                                            'MaxStakeSizeIndividualUp'                        AS max_stake_sz_individual_up,
                                                            'MaxStakeSizeIndividualDown'                      AS max_stake_sz_individual_dn,
                                                            'MaxStakeSizeConcurrentUp'                        AS max_stake_sz_concurrent_up,
                                                            'MaxStakeSizeConcurrentDown'                      AS max_stake_sz_concurrent_dn)))
     WHERE (instrument_schema_code,currency,wrapper_code) NOT IN (SELECT 'Default','NA','A-EOVH' FROM DUAL
                                                                  UNION ALL
                                                                  SELECT 'Default','NA','X-MNRK' FROM DUAL
                                                                  UNION ALL
                                                                  SELECT 'Default','NA','X-MNRL' FROM DUAL);

  MERGE INTO prdct_sttngs_inst_schema_ccy_h history
       USING (SELECT old_version.*
        FROM prdct_sttngs_inst_schema_ccy old_version
        LEFT JOIN TABLE(ltab_prod_ccy) new_version
           ON old_version.product_setting_code   = new_version.product_setting_code
          AND old_version.instrument_schema_code = new_version.instrument_schema_code
          AND old_version.currency               = new_version.currency
        WHERE (old_version.is_deleted IS NULL OR old_version.is_deleted = 'NO')
          AND ((nrg_common.has_value_changed(old_version.instrument_code,new_version.instrument_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.platform,new_version.platform)= 1 )
           OR  (nrg_common.has_value_changed(old_version.wrapper_code,new_version.wrapper_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.cluster_limit,new_version.cluster_limit)= 1 )
           OR  (nrg_common.has_value_changed(old_version.gslo_cluster_limit,new_version.gslo_cluster_limit)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_long,new_version.max_pstn_sz_long)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_shrt,new_version.max_pstn_sz_shrt)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_gslo_long,new_version.max_pstn_sz_gslo_long)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_gslo_shrt,new_version.max_pstn_sz_gslo_shrt)= 1 )
           OR  (nrg_common.has_value_changed(old_version.min_requested_quantity,new_version.min_requested_quantity)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_trade_quantity,new_version.max_trade_quantity)= 1 )
           OR  (nrg_common.has_value_changed(old_version.no_of_decimals,new_version.no_of_decimals)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_limit_entry_order_sz,new_version.max_limit_entry_order_sz)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stop_entry_order_sz,new_version.max_stop_entry_order_sz)= 1 )
           OR  (nrg_common.has_value_changed(old_version.min_stake_size,new_version.min_stake_size)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_individual_up,new_version.max_stake_sz_individual_up)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_individual_dn,new_version.max_stake_sz_individual_dn)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_concurrent_up,new_version.max_stake_sz_concurrent_up)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_concurrent_dn,new_version.max_stake_sz_concurrent_dn)= 1 )
          )) changes
       ON (history.product_setting_code      = changes.product_setting_code
       AND history.instrument_schema_code    = changes.instrument_schema_code
       AND history.currency                  = changes.currency
       AND history.effective_start_timestamp = changes.effective_start_timestamp)
     WHEN MATCHED THEN UPDATE SET
            history.updated_by                    	 = changes.updated_by,
            history.action                           = 'U',
            history.action_timestamp                 = SYSTIMESTAMP,
            history.update_timestamp                 = changes.update_timestamp,
            history.effective_end_timestamp          = p_effective_start_timestamp,
            history.instrument_code                  = changes.instrument_code,
            history.platform                         = changes.platform,
            history.wrapper_code                     = changes.wrapper_code,
            history.cluster_limit                    = changes.cluster_limit,
            history.gslo_cluster_limit               = changes.gslo_cluster_limit,
            history.max_pstn_sz_long                 = changes.max_pstn_sz_long,
            history.max_pstn_sz_shrt                 = changes.max_pstn_sz_shrt,
            history.max_pstn_sz_gslo_long            = changes.max_pstn_sz_gslo_long,
            history.max_pstn_sz_gslo_shrt            = changes.max_pstn_sz_gslo_shrt,
            history.min_requested_quantity           = changes.min_requested_quantity,
            history.max_trade_quantity               = changes.max_trade_quantity,
            history.no_of_decimals                   = changes.no_of_decimals,
            history.max_limit_entry_order_sz         = changes.max_limit_entry_order_sz,
            history.max_stop_entry_order_sz          = changes.max_stop_entry_order_sz,
            history.min_stake_size                   = changes.min_stake_size,
            history.max_stake_sz_individual_up       = changes.max_stake_sz_individual_up,
            history.max_stake_sz_individual_dn       = changes.max_stake_sz_individual_dn,
            history.max_stake_sz_concurrent_up       = changes.max_stake_sz_concurrent_up,
            history.max_stake_sz_concurrent_dn       = changes.max_stake_sz_concurrent_dn
     WHEN NOT MATCHED THEN INSERT
           (product_setting_code,
            instrument_schema_code,
            currency,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,
            instrument_code,
            platform,
            wrapper_code,
            cluster_limit,
            gslo_cluster_limit,
            max_pstn_sz_long,
            max_pstn_sz_shrt,
            max_pstn_sz_gslo_long,
            max_pstn_sz_gslo_shrt,
            min_requested_quantity,
            max_trade_quantity,
            no_of_decimals,
            max_limit_entry_order_sz,
            max_stop_entry_order_sz,
            min_stake_size,
            max_stake_sz_individual_up,
            max_stake_sz_individual_dn,
            max_stake_sz_concurrent_up,
            max_stake_sz_concurrent_dn,
            is_deleted)
    VALUES (changes.product_setting_code,
            changes.instrument_schema_code,
            changes.currency,
            changes.logical_load_timestamp,
            changes.created_by,
            changes.create_timestamp,
            changes.updated_by,
            changes.update_timestamp,
            changes.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            changes.instrument_code,
            changes.platform,
            changes.wrapper_code,
            changes.cluster_limit,
            changes.gslo_cluster_limit,
            changes.max_pstn_sz_long,
            changes.max_pstn_sz_shrt,
            changes.max_pstn_sz_gslo_long,
            changes.max_pstn_sz_gslo_shrt,
            changes.min_requested_quantity,
            changes.max_trade_quantity,
            changes.no_of_decimals,
            changes.max_limit_entry_order_sz,
            changes.max_stop_entry_order_sz,
            changes.min_stake_size,
            changes.max_stake_sz_individual_up,
            changes.max_stake_sz_individual_dn,
            changes.max_stake_sz_concurrent_up,
            changes.max_stake_sz_concurrent_dn,
            changes.is_deleted);

  MERGE INTO prdct_sttngs_inst_schema_ccy old_version
       USING (SELECT * FROM TABLE(ltab_prod_ccy)) new_version
          ON (old_version.product_setting_code   = new_version.product_setting_code
          AND old_version.instrument_schema_code = new_version.instrument_schema_code
          AND old_version.currency               = new_version.currency)
     WHEN MATCHED THEN UPDATE SET
            old_version.updated_by                       = p_user,
            old_version.update_timestamp                 = systimestamp,
            old_version.effective_start_timestamp        = p_effective_start_timestamp,
            old_version.instrument_code                  = new_version.instrument_code,
            old_version.platform                         = new_version.platform,
            old_version.wrapper_code                     = new_version.wrapper_code,
            old_version.cluster_limit                    = new_version.cluster_limit,
            old_version.gslo_cluster_limit               = new_version.gslo_cluster_limit,
            old_version.max_pstn_sz_long                 = new_version.max_pstn_sz_long,
            old_version.max_pstn_sz_shrt                 = new_version.max_pstn_sz_shrt,
            old_version.max_pstn_sz_gslo_long            = new_version.max_pstn_sz_gslo_long,
            old_version.max_pstn_sz_gslo_shrt            = new_version.max_pstn_sz_gslo_shrt,
            old_version.min_requested_quantity           = new_version.min_requested_quantity,
            old_version.max_trade_quantity               = new_version.max_trade_quantity,
            old_version.no_of_decimals                   = new_version.no_of_decimals,
            old_version.max_limit_entry_order_sz         = new_version.max_limit_entry_order_sz,
            old_version.max_stop_entry_order_sz          = new_version.max_stop_entry_order_sz,
            old_version.min_stake_size                   = new_version.min_stake_size,
            old_version.max_stake_sz_individual_up       = new_version.max_stake_sz_individual_up,
            old_version.max_stake_sz_individual_dn       = new_version.max_stake_sz_individual_dn,
            old_version.max_stake_sz_concurrent_up       = new_version.max_stake_sz_concurrent_up,
            old_version.max_stake_sz_concurrent_dn       = new_version.max_stake_sz_concurrent_dn
        WHERE ((nrg_common.has_value_changed(old_version.instrument_code,new_version.instrument_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.platform,new_version.platform)= 1 )
           OR  (nrg_common.has_value_changed(old_version.wrapper_code,new_version.wrapper_code)= 1 )
           OR  (nrg_common.has_value_changed(old_version.cluster_limit,new_version.cluster_limit)= 1 )
           OR  (nrg_common.has_value_changed(old_version.gslo_cluster_limit,new_version.gslo_cluster_limit)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_long,new_version.max_pstn_sz_long)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_shrt,new_version.max_pstn_sz_shrt)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_gslo_long,new_version.max_pstn_sz_gslo_long)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_pstn_sz_gslo_shrt,new_version.max_pstn_sz_gslo_shrt)= 1 )
           OR  (nrg_common.has_value_changed(old_version.min_requested_quantity,new_version.min_requested_quantity)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_trade_quantity,new_version.max_trade_quantity)= 1 )
           OR  (nrg_common.has_value_changed(old_version.no_of_decimals,new_version.no_of_decimals)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_limit_entry_order_sz,new_version.max_limit_entry_order_sz)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stop_entry_order_sz,new_version.max_stop_entry_order_sz)= 1 )
           OR  (nrg_common.has_value_changed(old_version.min_stake_size,new_version.min_stake_size)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_individual_up,new_version.max_stake_sz_individual_up)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_individual_dn,new_version.max_stake_sz_individual_dn)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_concurrent_up,new_version.max_stake_sz_concurrent_up)= 1 )
           OR  (nrg_common.has_value_changed(old_version.max_stake_sz_concurrent_dn,new_version.max_stake_sz_concurrent_dn)= 1 )
          )
     WHEN NOT MATCHED THEN INSERT
           (product_setting_code,
            instrument_schema_code,
            currency,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            instrument_code,
            platform,
            wrapper_code,
            cluster_limit,
            gslo_cluster_limit,
            max_pstn_sz_long,
            max_pstn_sz_shrt,
            max_pstn_sz_gslo_long,
            max_pstn_sz_gslo_shrt,
            min_requested_quantity,
            max_trade_quantity,
            no_of_decimals,
            max_limit_entry_order_sz,
            max_stop_entry_order_sz,
            min_stake_size,
            max_stake_sz_individual_up,
            max_stake_sz_individual_dn,
            max_stake_sz_concurrent_up,
            max_stake_sz_concurrent_dn,
            is_deleted)
    VALUES (new_version.product_setting_code,
            new_version.instrument_schema_code,
            new_version.currency,
            systimestamp,
            p_user,
            systimestamp,
            p_user,
            systimestamp,
            p_effective_start_timestamp,
            new_version.instrument_code,
            new_version.platform,
            new_version.wrapper_code,
            new_version.cluster_limit,
            new_version.gslo_cluster_limit,
            new_version.max_pstn_sz_long,
            new_version.max_pstn_sz_shrt,
            new_version.max_pstn_sz_gslo_long,
            new_version.max_pstn_sz_gslo_shrt,
            new_version.min_requested_quantity,
            new_version.max_trade_quantity,
            new_version.no_of_decimals,
            new_version.max_limit_entry_order_sz,
            new_version.max_stop_entry_order_sz,
            new_version.min_stake_size,
            new_version.max_stake_sz_individual_up,
            new_version.max_stake_sz_individual_dn,
            new_version.max_stake_sz_concurrent_up,
            new_version.max_stake_sz_concurrent_dn,
            'NO');

    UPDATE prdct_sttngs_inst_schema_ccy ps
    SET ps.is_deleted = 'YES'
    WHERE NOT EXISTS
    (SELECT 1 FROM TABLE(ltab_prod_ccy) nw
      WHERE nw.product_setting_code   = ps.product_setting_code
        AND nw.instrument_schema_code = ps.instrument_schema_code
        AND nw.currency               = ps.currency);

  END;

END nrg_products;
/