DROP TYPE ct_payment_tab FORCE;
DROP TYPE ct_payment_obj FORCE;

CREATE OR REPLACE TYPE bi_ods.ct_payment_obj IS OBJECT
(
  payment_id                    VARCHAR2(20),
  payment_action                VARCHAR2(50),
  payment_amount                NUMBER,
  payment_method                VARCHAR2(10),
  payment_type                  VARCHAR2(50),
  payment_channel               VARCHAR2(30),
  payment_schema                VARCHAR2(30),
  original_amount               NUMBER,
  original_amount_currency      VARCHAR2(3),
  fx_reval_rate                 NUMBER,
  card_number_masked            VARCHAR2(30),
  card_expiry_date              DATE,
  bank_account_number           VARCHAR2(50),
  bank_branch_code              VARCHAR2(50),
  bank_iban                     VARCHAR2(50),
  bank_swift_code               VARCHAR2(50),
  bank_name                     VARCHAR2(100),
  mm_trading_account            VARCHAR2(100),
  charge_id                     VARCHAR2(50),
  charge_amount                 NUMBER,
  trading_account_codifier      VARCHAR2(100),
  trading_account_id            NUMBER,
  trading_account_type          VARCHAR2(20),
  payment_codifier              VARCHAR2(100),
  payment_code                  VARCHAR2(100),
  custom_text                   VARCHAR2(100),
  original_amount_type          VARCHAR2(50),
  ta_acc_value_cash_balance     NUMBER,
  ta_acc_value_total_margin     NUMBER,
  ta_acc_value_equity           NUMBER,
  ta_acc_value_unrealized_pnl   NUMBER,
  ta_acc_value_withdrawable_amt NUMBER
)
;
/