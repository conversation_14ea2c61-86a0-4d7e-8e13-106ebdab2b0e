------------------------------
-- DROP PRICE_OPTIONS_CONTRACTS TABLES
------------------------------
DROP TABLE price_option_contracts;
DROP TABLE price_option_contracts_h;
DROP TABLE option_contract_anytime_prices;

------------------------------
-- CREATE OPTION CONTRACT PRICES TABLES
------------------------------
CREATE TABLE option_contract_prices (
    option_code	                varchar2(100),
    logical_load_timestamp	    timestamp(6),
    created_by	                varchar2(50),
    create_timestamp	        timestamp(6),
    updated_by	                varchar2(50),
    update_timestamp	        timestamp(6),
    effective_start_timestamp	timestamp(6),
    business_date	            date,
    reporting_date	            date,
    requested_snapshot_time	    timestamp(6),
    platform	                varchar2(10),
    is_incr_long_pos_allowed	varchar2(100),
    is_incr_short_pos_allowed	varchar2(100),
    symbol	                    varchar2(100),
    instrument_code	            varchar2(100),
    asset_type              	varchar2(100),
    currency	                varchar2(100),
    quote_id	                varchar2(100),
    time	                    timestamp(6),
    book_time	                timestamp(6),
    status	                    varchar2(100) not null,
    good_for	                varchar2(100),
    underlying_stream_code   	varchar2(100),
    subs_expiry_time	        timestamp(6),
    price_exponent	            number,
    strike_exponent	            number,
    maturity_date	            date,
    underlying_bid_price	    varchar2(100),
    underlying_ask_price	    varchar2(100),
    bid_sizes_order_book	    varchar2(500),
    ask_sizes_order_book	    varchar2(500),
    bid_prices_order_book	    varchar2(500),
    ask_prices_order_book	    varchar2(500),
    option_trading_class	    varchar2(100),
    option_type    	            varchar2(100),
    strike_price	            number,
    reason	                    varchar2(100),
    full_symbol	                varchar2(100),
    size_exponent	            number
)
    partition by range (reporting_date)
(
partition P0 values less than (TO_DATE(' 01/03/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_PRC_OPTN_CNTRCTS_DAT pctfree 10 initrans 1 maxtrans 255 storage (initial 8M next 1M minextents 1 maxextents unlimited),
partition P1 values less than (TO_DATE(' 01/04/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_PRC_OPTN_CNTRCTS_DAT pctfree 10 initrans 1 maxtrans 255 storage (initial 8M next 1M minextents 1 maxextents unlimited)
);

alter table bi_ods.option_contract_prices
    add constraint option_contract_prices_pk primary key (business_date,reporting_date,option_code,instrument_code)
    using index
  tablespace ODS_PRC_OPTN_CNTRCTS_IND
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

alter table option_contract_prices set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));

CREATE TABLE option_contract_prices_h (
    option_code	                varchar2(100),
    logical_load_timestamp	    timestamp(6),
    created_by	                varchar2(50),
    create_timestamp	        timestamp(6),
    updated_by	                varchar2(50),
    update_timestamp	        timestamp(6),
    effective_start_timestamp	timestamp(6),
    effective_end_timestamp     timestamp(6),
    action                      varchar2(1),
    action_timestamp            timestamp(6),
    business_date	            date,
    reporting_date	            date,
    requested_snapshot_time	    timestamp(6),
    platform	                varchar2(10),
    is_incr_long_pos_allowed	varchar2(100),
    is_incr_short_pos_allowed	varchar2(100),
    symbol	                    varchar2(100),
    instrument_code	            varchar2(100),
    asset_type              	varchar2(100),
    currency	                varchar2(100),
    quote_id	                varchar2(100),
    time	                    timestamp(6),
    book_time	                timestamp(6),
    status	                    varchar2(100) not null,
    good_for	                varchar2(100),
    underlying_stream_code   	varchar2(100),
    subs_expiry_time	        timestamp(6),
    price_exponent	            number,
    strike_exponent	            number,
    maturity_date	            date,
    underlying_bid_price	    varchar2(100),
    underlying_ask_price	    varchar2(100),
    bid_sizes_order_book	    varchar2(500),
    ask_sizes_order_book	    varchar2(500),
    bid_prices_order_book	    varchar2(500),
    ask_prices_order_book	    varchar2(500),
    option_trading_class	    varchar2(100),
    option_type    	            varchar2(100),
    strike_price	            number,
    reason	                    varchar2(100),
    full_symbol	                varchar2(100),
    size_exponent	            number
)
    partition by range (reporting_date)
(
partition P0 values less than (TO_DATE(' 01/03/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_PRC_OPTN_CNTRCTS_DAT pctfree 10 initrans 1 maxtrans 255 storage (initial 8M next 1M minextents 1 maxextents unlimited),
partition P1 values less than (TO_DATE(' 01/04/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_PRC_OPTN_CNTRCTS_DAT pctfree 10 initrans 1 maxtrans 255 storage (initial 8M next 1M minextents 1 maxextents unlimited)
);

alter table bi_ods.option_contract_prices_h
    add constraint option_contract_prices_h_pk primary key (business_date,reporting_date,option_code,instrument_code)
    using index
  tablespace ODS_PRC_OPTN_CNTRCTS_IND
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

alter table option_contract_prices_h set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));

CREATE TABLE option_contract_anytime_prices (
    option_code	                varchar2(100),
    logical_load_timestamp	    timestamp(6),
    created_by	                varchar2(50),
    create_timestamp	        timestamp(6),
    updated_by	                varchar2(50),
    update_timestamp	        timestamp(6),
    effective_start_timestamp	timestamp(6),
    business_date	            date,
    reporting_date	            date,
    requested_snapshot_time	    timestamp(6),
    platform	                varchar2(10),
    is_incr_long_pos_allowed	varchar2(100),
    is_incr_short_pos_allowed	varchar2(100),
    symbol	                    varchar2(100),
    instrument_code	            varchar2(100),
    asset_type              	varchar2(100),
    currency	                varchar2(100),
    quote_id	                varchar2(100),
    time	                    timestamp(6),
    book_time	                timestamp(6),
    status	                    varchar2(100) not null,
    good_for	                varchar2(100),
    underlying_stream_code   	varchar2(100),
    subs_expiry_time	        timestamp(6),
    price_exponent	            number,
    strike_exponent	            number,
    maturity_date	            date,
    underlying_bid_price	    varchar2(100),
    underlying_ask_price	    varchar2(100),
    bid_sizes_order_book	    varchar2(500),
    ask_sizes_order_book	    varchar2(500),
    bid_prices_order_book	    varchar2(500),
    ask_prices_order_book	    varchar2(500),
    option_trading_class	    varchar2(100),
    option_type    	            varchar2(100),
    strike_price	            number,
    reason	                    varchar2(100),
    full_symbol	                varchar2(100),
    size_exponent	            number
)
    partition by range (reporting_date)
(
partition P0 values less than (TO_DATE(' 01/03/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_PRC_OPTN_CNTRCTS_DAT pctfree 10 initrans 1 maxtrans 255 storage (initial 8M next 1M minextents 1 maxextents unlimited),
partition P1 values less than (TO_DATE(' 01/04/2023 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_PRC_OPTN_CNTRCTS_DAT pctfree 10 initrans 1 maxtrans 255 storage (initial 8M next 1M minextents 1 maxextents unlimited)
);

alter table bi_ods.option_contract_anytime_prices
    add constraint option_contract_anytime_prices_pk primary key (business_date,reporting_date,option_code,instrument_code,requested_snapshot_time)
    using index
  tablespace ODS_PRC_OPTN_CNTRCTS_IND
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

alter table option_contract_anytime_prices set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));