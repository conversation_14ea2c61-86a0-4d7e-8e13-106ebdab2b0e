create or replace PACKAGE BODY        nrg_common
AS
    -- ===================================================================================
    -- NRG_COMMON
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     25/08/2011   Richard Sadd       1.1    Creation
    --     25/01/2012   Patrick Dinwiddy   1.2    Added get_business_date and
    --                                            get_reporting_date functions
    --     17/04/2012   Sanket Mittal      1.3    Updated the get_business_date function to use timestamp as parameter
    --     22/04/2013   Sanket Mittal      1.4    Updated the get_business_date function to treat the time at 22:00 as next business date
    --     08/02/2013   Sanket Mittal      1.5    Added the function get_order_type
    --     25/03/2014   Adam Krasnicki     1.6    Changes in get_order_type function BER-934
    --     28/01/2015   Adam Krasnicki     1.6    Added new order type = 'STOPLOSS (GUARANTEED)'
    --     15/05/2015   Adam Krasnicki            p_order_execution type parameter added to get_order_type function
    --     04/08/2016   Sanket Mittal      1.7    Added get_snapshot_business_date and get_snapshot_reporting_date function
    --     18/10/2016   Sanket Mittal      1.8    Added get_price_band and get_order_type (Overloaded)
    --
    -- ===================================================================================

    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================

    gc_version CONSTANT VARCHAR2(3) := '1.8';
    gc_cr      CONSTANT VARCHAR2(1) := CHR(10);
    gc_true    CONSTANT PLS_INTEGER := 1;
    gc_false   CONSTANT PLS_INTEGER := 0;

    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================

    -- ===================================================================================
    -- PUBLIC MODULES
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC
    IS
    BEGIN
        logger.logger.set_module('version');
        RETURN gc_version;
    EXCEPTION
        WHEN OTHERS THEN
            logger.logger.severe(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            RAISE;
    END version;

    -- ===================================================================================
    -- uuid
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve a formatted random UUID
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a formatted random UUID
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION uuid
        RETURN VARCHAR2
    IS
        lv_sys_guid VARCHAR2(32);
    BEGIN
        logger.logger.set_module('uuid');

        lv_sys_guid := sys_guid();

        RETURN SUBSTR(lv_sys_guid, 1, 8) || '-' ||
               SUBSTR(lv_sys_guid, 9, 4) || '-' ||
               SUBSTR(lv_sys_guid, 13, 4) || '-' ||
               SUBSTR(lv_sys_guid, 17, 4) || '-' ||
               SUBSTR(lv_sys_guid, 21);

        logger.logger.set_module(NULL);
    EXCEPTION
        WHEN OTHERS THEN
            logger.logger.severe(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            RAISE;
    END uuid;

    -- ===================================================================================
    -- profit_centre_id_for_name
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve a profit centre id for its name
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_profit_centre_name             Profit centre name
    --
    -- Return:
    -- -------
    --
    --     Returns an id for the name
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Profit centre not found
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION profit_centre_id_for_name(p_profit_centre_name IN profit_centres.profit_centre_name%TYPE)
        RETURN profit_centres.profit_centre_id%TYPE
    IS
        lex_profit_centre_not_found EXCEPTION;
        lv_profit_centre_id profit_centres.profit_centre_id%TYPE;
    BEGIN
        logger.logger.set_module('profit_centre_id_for_name');

        BEGIN
            SELECT profit_centre_id
            INTO   lv_profit_centre_id
            FROM   profit_centres
            WHERE  profit_centre_name = p_profit_centre_name;
        EXCEPTION
            WHEN no_data_found THEN
                RAISE lex_profit_centre_not_found;
        END;

        RETURN lv_profit_centre_id;

        logger.logger.set_module(NULL);
    EXCEPTION
        WHEN lex_profit_centre_not_found THEN
            logger.logger.severe('ORA-20001: Profit centre not found');
            logger.logger.set_module(NULL);
            raise_application_error(-20001, 'Profit centre not found');
            RAISE;
        WHEN OTHERS THEN
            logger.logger.severe(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            RAISE;
    END profit_centre_id_for_name;
    -- ===================================================================================
    -- has_value_changed
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to compare two values and return a 1 if the two values are different
    --     else return a 0
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_value1                          First Value
    --     p_value2                          Second Value
    --
    -- Return:
    -- -------
    --
    --     Returns a true if the two values match else returns a false
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION has_value_changed(p_value1 IN VARCHAR2, p_value2 IN VARCHAR2)
        RETURN NUMBER IS
      lv_result NUMBER;
    BEGIN
      CASE
        WHEN (p_value1 IS NULL AND p_value2 IS NOT NULL)THEN
          lv_result := 1;
        WHEN (p_value1 IS NOT NULL AND p_value2 IS NULL) THEN
          lv_result := 1;
        WHEN (p_value1 <> p_value2) THEN
          lv_result := 1;
        ELSE
          lv_result := 0;
      END CASE;

      RETURN lv_result;
    END has_value_changed;    

    FUNCTION has_value_changed(p_value1 IN NUMBER, p_value2 IN NUMBER)
        RETURN NUMBER IS
      lv_result NUMBER;
    BEGIN
        CASE
        WHEN (p_value1 IS NULL AND p_value2 IS NOT NULL)THEN
          lv_result := 1;
        WHEN (p_value1 IS NOT NULL AND p_value2 IS NULL) THEN
          lv_result := 1;
        WHEN (p_value1 <> p_value2) THEN
          lv_result := 1;
        ELSE
          lv_result := 0;
      END CASE;

      RETURN lv_result;
    END has_value_changed;

    FUNCTION has_value_changed(p_value1 IN TIMESTAMP, p_value2 IN TIMESTAMP)
        RETURN NUMBER IS
      lv_result NUMBER;
    BEGIN
        CASE
        WHEN (p_value1 IS NULL AND p_value2 IS NOT NULL)THEN
          lv_result := 1;
        WHEN (p_value1 IS NOT NULL AND p_value2 IS NULL) THEN
          lv_result := 1;
        WHEN (p_value1 <> p_value2) THEN
          lv_result := 1;
        ELSE
          lv_result := 0;
      END CASE;

      RETURN lv_result;
    END has_value_changed;

    FUNCTION has_value_changed(p_value1 IN DATE, p_value2 IN DATE)
        RETURN NUMBER IS
      lv_result NUMBER;
    BEGIN
        CASE
        WHEN (p_value1 IS NULL AND p_value2 IS NOT NULL)THEN
          lv_result := 1;
        WHEN (p_value1 IS NOT NULL AND p_value2 IS NULL) THEN
          lv_result := 1;
        WHEN (p_value1 <> p_value2) THEN
          lv_result := 1;
        ELSE
          lv_result := 0;
      END CASE;

      RETURN lv_result;
    END has_value_changed;

    FUNCTION has_value_changed(p_value1 IN TIMESTAMP WITH TIME ZONE, p_value2 IN TIMESTAMP WITH TIME ZONE)
        RETURN NUMBER IS
      lv_result NUMBER;
    BEGIN
        CASE
        WHEN (p_value1 IS NULL AND p_value2 IS NOT NULL)THEN
          lv_result := 1;
        WHEN (p_value1 IS NOT NULL AND p_value2 IS NULL) THEN
          lv_result := 1;
        WHEN (p_value1 <> p_value2) THEN
          lv_result := 1;
        ELSE
          lv_result := 0;
      END CASE;

      RETURN lv_result;
    END has_value_changed;
    -- ===================================================================================
    -- get_business_date
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get the business date.
    --
    -- Parameters:
    -- -----------
    --
    --     p_timestamp timestamp to convert to business date
    --     p_rollover_minute_second the time after which a date is the next business day
    --
    -- Return:
    -- -------
    --
    --     date business date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION get_business_date
        (
          p_transaction_date_utc IN TIMESTAMP     DEFAULT SYSDATE
        , p_rollover_hour        IN INTEGER  DEFAULT 22
        , p_rollover_minute      IN INTEGER  DEFAULT 0
        , p_rollover_second      IN INTEGER  DEFAULT 0
        , p_rollover_time_zone   IN STRING   DEFAULT 'Europe/London'
        )
        RETURN DATE
    IS
      v_business_date TIMESTAMP DEFAULT NULL;
    BEGIN
      logger.logger.set_module('get_business_date');
      /* A transaction, such as a Trade, has a local time
      that the business event happened in the real world.
      The transaction system will transform local time
      to Universal Time Coordinated (UTC) ('Z'ulu Time)
      known as the transaction time stored in the database.
      UTC does not change with a change of seasons,
      but local time may change if a time zone jurisdiction
      observes daylight saving time
      , such as during the british summer.
      Based on the end of day roll over time
      , currently 22:00 local time Europe/London
      , which is 21:00 UTC in British Summer Time
      , 22:00 UTC in British Winter Time
      , transform transaction time to business date (7 day week)
      and report date (5 day week because also roll over
      Sat and Sun Business Day into report day monday).
      */
      /*
      For example,
      In Chicago, in summer, a trade executed by a person there on Sat afternoon
      , local time in Chicago is 15:03:00, with daylight saving time in Chicago
      , the time offset is UTC "-06:00", transaction time is Saturday 21:03:00 UTC
      , with daylight saving time in London, the time offset is UTC "+01:00"
      , local time in London is Saturday 22:03:00 BST
      , Business Day is Sunday, Report Day is Monday.
      */

      IF p_transaction_date_utc IS NULL THEN
        RETURN NULL;
      END IF;

      v_business_date := CAST(
                              from_tz(
                                      to_timestamp(
                                                   to_char(p_transaction_date_utc,'DD-Mon-YYYY') ||
                                                           ' ' || p_rollover_hour || ':00:00.000'
                                                           ,'DD-Mon-YYYY HH24:MI:SSXFF')
                                                    , p_rollover_time_zone) at TIME ZONE 'UTC' AS TIMESTAMP);

      --dbms_output.put_line(v_business_date);

      IF ( p_transaction_date_utc >= v_business_date ) THEN
        -- after 22:00 so next business day
        v_business_date := trunc( v_business_date ) + 1;
      ELSE
        -- on or before 22:00 so same business day
        v_business_date := trunc( v_business_date );
      END IF;
      logger.logger.set_module(NULL);
      RETURN v_business_date;

    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_business_date;

    -- ===================================================================================
    -- get_snapshot_business_date
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get the snapshot business date.
    --
    -- Parameters:
    -- -----------
    --
    --     p_timestamp timestamp to convert to business date
    --     p_rollover_minute_second the time after which a date is the next business day
    --
    -- Return:
    -- -------
    --
    --     date business date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION get_snapshot_business_date(
                                        p_snapshot_date_utc IN TIMESTAMP DEFAULT SYSDATE,
                                        p_rollover_hour IN INTEGER DEFAULT 22,
                                        p_rollover_minute IN INTEGER DEFAULT 0,
                                        p_rollover_second IN INTEGER DEFAULT 0,
                                        p_rollover_time_zone IN string DEFAULT 'Europe/London'
                                        ) RETURN DATE IS
      v_business_date TIMESTAMP(6);
    BEGIN
      logger.logger.set_module('get_business_date');
      /* A transaction, such as a Trade, has a local time
      that the business event happened in the real world.
      The transaction system will transform local time
      to Universal Time Coordinated (UTC) ('Z'ulu Time)
      known as the transaction time stored in the database.
      UTC does not change with a change of seasons,
      but local time may change if a time zone jurisdiction
      observes daylight saving time
      , such as during the british summer.
      Based on the end of day roll over time
      , currently 22:00 local time Europe/London
      , which is 21:00 UTC in British Summer Time
      , 22:00 UTC in British Winter Time
      , transform transaction time to business date (7 day week)
      and report date (5 day week because also roll over
      Sat and Sun Business Day into report day monday).
      */
      /*
      For example,
      In Chicago, in summer, a trade executed by a person there on Sat afternoon
      , local time in Chicago is 15:03:00, with daylight saving time in Chicago
      , the time offset is UTC "-06:00", transaction time is Saturday 21:03:00 UTC
      , with daylight saving time in London, the time offset is UTC "+01:00"
      , local time in London is Saturday 22:03:00 BST
      , Business Day is Sunday, Report Day is Monday.
      */

      IF p_snapshot_date_utc IS NULL THEN
        RETURN NULL;
      END IF;

      v_business_date := CAST(
                              from_tz(
                                      to_timestamp(
                                                   to_char(p_snapshot_date_utc,'DD-Mon-YYYY') ||
                                                           ' ' || p_rollover_hour || ':00:00.000'
                                                           ,'DD-Mon-YYYY HH24:MI:SSXFF')
                                                    , p_rollover_time_zone) at TIME ZONE 'UTC' AS TIMESTAMP);

      --dbms_output.put_line(v_business_date);

      IF ( p_snapshot_date_utc <= v_business_date ) THEN
        -- after 22:00 so next business day
        v_business_date := trunc( v_business_date );
      ELSE
        -- on or before 22:00 so same business day
        v_business_date := trunc( v_business_date + 1);
      END IF;
      logger.logger.set_module(NULL);
      RETURN v_business_date;

    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- get_reporting_date
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get the reporting date - this is the same as business date
    --     except for weekends - reporting date Monday encompasses 2200 UK Local Friday
    --     until 21:59:59.999999 UK Local Monday
    --
    -- Parameters:
    -- -----------
    --
    --     p_date_utc - Transaction date in UTC
    --
    -- Return:
    -- -------
    --
    --     date reporting date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION get_reporting_date
      (p_date_utc IN TIMESTAMP DEFAULT SYSTIMESTAMP)

    RETURN DATE IS

      v_business_date   DATE DEFAULT NULL;
      v_reporting_date  DATE DEFAULT NULL;

    BEGIN
      logger.logger.set_module('get_reporting_date');

    IF p_date_utc IS NULL THEN
      RETURN NULL;
    END IF;

      v_business_date := get_business_date(p_transaction_date_utc => p_date_utc);

      CASE
        WHEN TRIM(to_char(v_business_date,'DAY')) = 'SATURDAY' THEN v_reporting_date := v_business_date + 2;
        WHEN TRIM(to_char(v_business_date,'DAY')) = 'SUNDAY' THEN v_reporting_date := v_business_date + 1;
        ELSE v_reporting_date := v_business_date;
      END CASE;

    RETURN v_reporting_date;
        EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);

    END get_reporting_date;

    -- ===================================================================================
    -- get_snapshot_reporting_date
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get the reporting date - this is the same as business date
    --     except for weekends - reporting date Monday encompasses 2200 UK Local Friday
    --     until 21:59:59.999999 UK Local Monday
    --
    -- Parameters:
    -- -----------
    --
    --     p_date_utc - Transaction date in UTC
    --
    -- Return:
    -- -------
    --
    --     date reporting date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION get_snapshot_reporting_date
      (p_snapshot_date_utc IN TIMESTAMP DEFAULT SYSTIMESTAMP)

    RETURN DATE IS

      v_business_date   DATE DEFAULT NULL;
      v_reporting_date  DATE DEFAULT NULL;

    BEGIN
      logger.logger.set_module('get_reporting_date');

    IF p_snapshot_date_utc IS NULL THEN
      RETURN NULL;
    END IF;

      v_business_date := get_snapshot_business_date(p_snapshot_date_utc => p_snapshot_date_utc);

      CASE
        WHEN TRIM(to_char(v_business_date,'DAY')) = 'SATURDAY' THEN v_reporting_date := v_business_date + 2;
        WHEN TRIM(to_char(v_business_date,'DAY')) = 'SUNDAY' THEN v_reporting_date := v_business_date + 1;
        ELSE v_reporting_date := v_business_date;
      END CASE;

    RETURN v_reporting_date;
        EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);

    END get_snapshot_reporting_date;

    -- ===================================================================================
    -- convert_varchar_into_date
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to convert a string into date.
    --
    -- Parameters:
    -- -----------
    --
    --     p_date_v - Date in Varchar2
    --     p_date_format - Format of the date in string
    --
    -- Return:
    -- -------
    --
    --     date reporting date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION convert_varchar_into_date(p_date_v VARCHAR2,
                                       p_date_format VARCHAR2) RETURN DATE IS

      lv_transaction_date TIMESTAMP(6) := NULL;
      lv_date             DATE         := NULL;

    BEGIN

      logger.logger.set_module('convert_varchar_into_date');

      IF p_date_v IS NULL THEN
        RETURN lv_date;
      END IF;

      BEGIN
        lv_transaction_date := TO_TIMESTAMP(REPLACE(REPLACE(p_date_v, 'T', ' '), 'Z' , ''), p_date_format);
      EXCEPTION
        WHEN OTHERS THEN
          raise_application_error(-20001, sqlerrm || ' Date Passed ' || p_date_v || ' Format Passed: ' || p_date_format);
      END;

      lv_date := to_date(to_char(lv_transaction_date, 'DD-Mon-YYYY HH24:MI:SS'), 'DD-Mon-YYYY HH24:MI:SS');

      RETURN lv_date;

    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- convert_varchar_into_timestamp
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to convert a string into timestamp.
    --
    -- Parameters:
    -- -----------
    --
    --     p_date_v - Date in Varchar2
    --     p_date_format - Format of the date in string
    --
    -- Return:
    -- -------
    --
    --     date reporting date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION convert_varchar_into_timestamp(p_date_v VARCHAR2,
                                            p_date_format VARCHAR2) RETURN TIMESTAMP IS

      lv_transaction_date TIMESTAMP(6) := NULL;

    BEGIN
      logger.logger.set_module('convert_varchar_into_timestamp');

      IF p_date_v IS NULL THEN
        RETURN lv_transaction_date;
      END IF;

      BEGIN
        lv_transaction_date := TO_TIMESTAMP(REPLACE(REPLACE(p_date_v, 'T', ' '), 'Z' , ''), p_date_format);
      EXCEPTION
        WHEN OTHERS THEN
          raise_application_error(-20001, sqlerrm || ' Date Passed ' || p_date_v || ' Format Passed: ' || p_date_format);
      END;

      RETURN lv_transaction_date;

    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- get_order_type
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get the order type
    --
    -- Parameters:
    -- -----------
    --
    --     p_date_v - Date in Varchar2
    --     p_date_format - Format of the date in string
    --
    -- Return:
    -- -------
    --
    --     date reporting date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION get_order_type(p_platform                       IN orders.platform%TYPE,
                            p_rqustd_open_trade_to_close     IN trade_id_tab,
                            p_limit_price_condition          IN orders.limit_price_condition%TYPE,
                            p_related_parent_order_id        IN orders.related_parent_order_id%TYPE,
                            p_requested_direction            IN orders.requested_direction%TYPE,
                            p_related_child_order_type       IN orders.related_child_order_type%TYPE,
                            p_controlled_order_type          IN orders.controlled_order_type%TYPE,
                            p_order_type                     IN orders.order_type%TYPE,
                            p_order_execution_type           IN orders.Execution_Type%TYPE) RETURN VARCHAR2 IS

      lv_order_type           VARCHAR2(50);
    BEGIN

      CASE
        WHEN p_platform = 'NG' THEN
          CASE
            WHEN p_rqustd_open_trade_to_close IS NOT NULL AND p_rqustd_open_trade_to_close.COUNT > 0 THEN
              lv_order_type := 'TRADECLOSEORDER';
            WHEN UPPER(p_limit_price_condition) = 'NONE' THEN
              lv_order_type := 'MARKET';
            WHEN p_related_parent_order_id != '-1' AND p_related_child_order_type = 'STOPLOSS' THEN
              lv_order_type := 'STOPLOSS (REGULAR)';
            WHEN p_related_parent_order_id != '-1' AND p_related_child_order_type = 'TRAILINGSTOPLOSS' THEN
              lv_order_type := 'STOPLOSS (TRAILING)';
            WHEN p_related_parent_order_id != '-1' AND p_related_child_order_type = 'GUARANTEEDSTOPLOSS' THEN
              lv_order_type := 'STOPLOSS (GUARANTEED)';
            WHEN p_related_parent_order_id != '-1' AND p_related_child_order_type = 'TAKEPROFIT' THEN
              lv_order_type := 'TAKEPROFIT';
            WHEN p_related_parent_order_id = '-1' AND UPPER(p_limit_price_condition) != 'NONE' THEN
              CASE
                WHEN (UPPER(p_requested_direction) = 'BUY' AND UPPER(p_limit_price_condition) = 'GREATEROREQUAL') OR (UPPER(p_requested_direction) = 'SELL' AND UPPER(p_limit_price_condition) = 'LESSOREQUAL') THEN
                  lv_order_type := 'STOPENTRY';
                WHEN (UPPER(p_requested_direction) = 'BUY' AND UPPER(p_limit_price_condition) = 'LESSOREQUAL') OR (UPPER(p_requested_direction) = 'SELL' AND UPPER(p_limit_price_condition) = 'GREATEROREQUAL') THEN
                  lv_order_type := 'LIMIT';
                ELSE
                  lv_order_type := 'UNKNOWN';
              END CASE;
            WHEN p_order_execution_type = 'REQUESTFORQUOTE' THEN
                 lv_order_type := 'MARKET (RFQ)';
            WHEN p_order_execution_type = 'DIRECTMARKETACCESS' THEN
                 lv_order_type := 'MARKET (DMA)';
            WHEN p_order_execution_type = 'KNOCKOUT' THEN
                 lv_order_type := 'MARKET (KNOCKOUT)';
              ELSE
              lv_order_type := 'UNKNOWN';
          END CASE;
        WHEN p_platform <> 'NG' THEN

        CASE WHEN upper(p_order_type) = 'BACKOFFICE ORDER' THEN
            CASE WHEN upper(p_controlled_order_type) = 'ROLLOVER' THEN
                 lv_order_type := 'ROLLOVER';
            ELSE
                 lv_order_type := 'BACKOFFICE ORDER';
            END CASE;
        WHEN upper(p_order_type) = 'MARKET' THEN
             lv_order_type := 'MARKET';
        WHEN upper(p_order_type) = 'LIMIT' THEN
             lv_order_type := 'LIMIT';
        WHEN upper(p_order_type) = 'STOP' THEN
             lv_order_type := 'STOP';
        WHEN upper(p_order_type) = 'CASH' THEN
             lv_order_type := 'CASH';
        ELSE
             lv_order_type := 'UNKNOWN';
        END CASE;


/*          CASE
            WHEN UPPER(p_order_type) = 'MARKET' AND UPPER(p_controlled_order_type) = 'ROLLOVER' THEN
              lv_order_type := 'ROLLOVER';
            WHEN UPPER(p_order_type) = 'MARKET' AND UPPER(p_controlled_order_type) <> 'ROLLOVER' THEN
              lv_order_type := 'MARKET';
            ELSE
              lv_order_type := p_order_type;
          END CASE;*/
      END CASE;

      RETURN lv_order_type;
    END;

    -- ===================================================================================
    -- get_price_band
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get price band
    --
    -- Parameters:
    -- -----------
    --
    --     p_trading_account_id         Trading Account Id
    --     p_instrument_code            Product Instrument Code
    --
    -- Return:
    -- -------
    --
    --     date reporting date at midnight
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION get_price_band(p_trading_account_id   IN trading_accounts.trading_account_id%TYPE,
                            p_instrument_code      IN instruments.instrument_code%TYPE)
    RETURN VARCHAR2 IS

      lv_price_band VARCHAR2(50);

    BEGIN

      logger.logger.set_module('NRG_COMMON.GET_PRICE_BAND');


        SELECT NVL(NVL(NVL(psi1.price_band, psi2.price_band),psi3.price_band),ps.default_band) AS price_band
          INTO lv_price_band
          FROM bi_ods.trading_accounts ta
     LEFT JOIN bi_ods.price_schemas ps
            ON ta.price_schema_code    = ps.price_schema_name
           AND ps.is_deleted           = 'NO'
     LEFT JOIN bi_ods.instruments i
            ON 1=1
     LEFT JOIN bi_ods.price_schema_items psi1
            ON ps.price_schema_name    = psi1.price_schema_name
           AND psi1.override_type      = 'INSTRUMENT'
           AND CASE WHEN i.financial_instrument_type = 'CMCForwardExpiry'
                         THEN i.contract_code
                    ELSE i.instrument_code
               END = psi1.code
     LEFT JOIN bi_ods.price_schema_items psi2
            ON ps.price_schema_name    = psi2.price_schema_name
           AND psi2.override_type      = 'MARKET'
           AND i.market_code           = psi2.code
     LEFT JOIN bi_ods.price_schema_items psi3
            ON ps.price_schema_name    = psi3.price_schema_name
           AND psi3.override_type      = 'ASSET_CLASS'
           AND i.instrument_type_code  = psi3.code
         WHERE ta.trading_account_type = 'CUSTOMER'
           AND ta.trading_account_id   = p_trading_account_id
           AND i.instrument_code       = p_instrument_code
           ;

      RETURN lv_price_band;

    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- get_order_type
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to get price band
    --
    -- Parameters:
    -- -----------
    --
    --     p_platform                   Platform
    --     p_order_type                 Order Type
    --     p_controlled_order_type      Controlled Order Type
    --
    -- Return:
    -- -------
    --
    --     Order Type
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION get_order_type(p_platform              IN orders.platform%TYPE,
                            p_order_type            IN orders.order_type%TYPE,
                            p_controlled_order_type IN orders.controlled_order_type%TYPE DEFAULT NULL
                            )
    RETURN VARCHAR2 IS
      lv_order_type VARCHAR2(50);
    BEGIN
      CASE
        WHEN p_platform = 'NG' THEN
          CASE
            WHEN upper(p_order_type) = 'NONE' THEN
              lv_order_type := 'NONE';
            WHEN upper(p_order_type) = 'MARKET' THEN
              lv_order_type := 'MARKET';
            WHEN upper(p_order_type) = 'MARKETREQUESTFORQUOTE' THEN
              lv_order_type := 'MARKET (RFQ)';
            WHEN upper(p_order_type) = 'MARKETDIRECTMARKETACCESS' THEN
              lv_order_type := 'MARKET (DMA)';
            WHEN upper(p_order_type) = 'MARKETKNOCKOUT' THEN
              lv_order_type := 'MARKET (KNOCKOUT)';
            WHEN upper(p_order_type) = 'STOPENTRY' THEN
              lv_order_type := 'STOPENTRY';
            WHEN upper(p_order_type) = 'LIMITENTRY' THEN
              lv_order_type := 'LIMIT';
            WHEN upper(p_order_type) = 'TRADECLOSEORDER' THEN
              lv_order_type := 'TRADECLOSEORDER';
            WHEN upper(p_order_type) = 'REGULARSTOPLOSS' THEN
              lv_order_type := 'STOPLOSS (REGULAR)';
            WHEN upper(p_order_type) = 'TAKEPROFIT' THEN
              lv_order_type := 'TAKEPROFIT';
            WHEN upper(p_order_type) = 'TRAILINGSTOPLOSS' THEN
              lv_order_type := 'STOPLOSS (TRAILING)';
            WHEN upper(p_order_type) = 'GUARANTEEDSTOPLOSS' THEN
              lv_order_type := 'STOPLOSS (GUARANTEED)';
            WHEN upper(p_order_type) = 'VALUELIMITORDER' THEN
              lv_order_type := 'VALUELIMITORDER';
            ELSE
              lv_order_type := 'UNKNOWN';
          END CASE;

        WHEN p_platform <> 'NG' THEN
          CASE
            WHEN upper(p_order_type) = 'BACKOFFICE ORDER' THEN
              CASE
                WHEN upper(p_controlled_order_type) = 'ROLLOVER' THEN
                  lv_order_type := 'ROLLOVER';
                ELSE
                  lv_order_type := 'BACKOFFICE ORDER';
              END CASE;
            WHEN upper(p_order_type) = 'MARKET' THEN
              lv_order_type := 'MARKET';
            WHEN upper(p_order_type) = 'LIMIT' THEN
              lv_order_type := 'LIMIT';
            WHEN upper(p_order_type) = 'STOP' THEN
              lv_order_type := 'STOP';
            WHEN upper(p_order_type) = 'CASH' THEN
              lv_order_type := 'CASH';
            ELSE
              lv_order_type := 'UNKNOWN';
          END CASE;

      END CASE;

      RETURN lv_order_type;
    END;
END nrg_common;
/