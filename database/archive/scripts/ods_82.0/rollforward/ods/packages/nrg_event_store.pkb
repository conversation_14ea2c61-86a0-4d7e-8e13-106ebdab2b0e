CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_event_store
AS
-- ===================================================================================
-- NRG_APOLLO
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     This package encapsulates NRG (Near Realtime Gatherer)
--
--     Management of the Event Store Service model
--
-- -----------------------------------------------------------------------------------
--
-- Notes:
-- ------
--
--     Run as BI_ODS
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--     -20001    Entity already exists
--
-- Modifications:
-- --------------
--
--   Date         Modified By       Vers    Action
--   ----------   ---------------   -----   ----------------------------------------
--   09/04/2019   Patrick Dinwiddy  1.0     Creation
-- ===================================================================================
--
--
-- ===================================================================================
-- PACKAGE CONSTANTS
-- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '1.0';

-- ===================================================================================
-- version
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Function to retrieve the version of the package
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--
-- Return:
-- -------
--
--     Returns a VARCHAR2 representing the version of the package
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--
-- -----------------------------------------------------------------------------------

  FUNCTION version
    RETURN VARCHAR2 DETERMINISTIC
  IS

  BEGIN
  logger.logger.set_module('version');
  RETURN gc_version;
  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    RAISE;
  END version; 
 
-- ===================================================================================
-- put_event_attributes
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate as_config_properties
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_as_qstnaire_sub_scores         table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_event_attributes (p_user                       IN event_store_attributes.created_by%TYPE,
                                  p_effective_start_timestamp  IN event_store_attributes.effective_start_timestamp%TYPE,
                                  p_event_attributes           IN event_store_attributes_tab) IS
                                  
    lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;                

  BEGIN

    MERGE INTO event_store_attributes_h history USING 
       (SELECT old_version.*
          FROM event_store_attributes old_version
          JOIN TABLE(CAST(p_event_attributes AS event_store_attributes_tab)) event_attributes
            ON (old_version.event_id       = event_attributes.event_id AND
                old_version.attribute_name = event_attributes.attribute_name)
        WHERE ((nrg_common.has_value_changed(old_version.string_data,event_attributes.string_data)= 1 ) OR
               (nrg_common.has_value_changed(old_version.date_data,event_attributes.date_data)= 1 ) OR
               (nrg_common.has_value_changed(old_version.number_data,event_attributes.number_data)= 1 ))
               ) new_version
        ON (history.event_id                  = new_version.event_id AND
            history.attribute_name            = new_version.attribute_name AND
            history.effective_start_timestamp = new_version.effective_start_timestamp)
    WHEN MATCHED THEN UPDATE 
        SET history.updated_by                 = new_version.updated_by,
            history.action                     = 'U',
            history.action_timestamp           = SYSTIMESTAMP,
            history.update_timestamp           = new_version.update_timestamp,
            history.logical_load_timestamp     = new_version.logical_load_timestamp,
            history.string_data                = new_version.string_data,
            history.date_data                  = new_version.date_data,
            history.number_data                = new_version.number_data
    WHEN NOT MATCHED THEN INSERT
           (event_id,
            attribute_name,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,   
            string_data,
            date_data,
            number_data)
    VALUES (new_version.event_id,
            new_version.attribute_name,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.string_data,
            new_version.date_data,
            new_version.number_data);
  
    MERGE INTO event_store_attributes old_version USING 
         (SELECT DISTINCT event_attributes.event_id,
                          event_attributes.attribute_name,
                          event_attributes.string_data,
                          event_attributes.date_data,
                          event_attributes.number_data
            FROM TABLE(CAST(p_event_attributes AS event_store_attributes_tab)) event_attributes) new_version
        ON (old_version.event_id       = new_version.event_id AND
            old_version.attribute_name = new_version.attribute_name)
    WHEN MATCHED THEN UPDATE
        SET old_version.logical_load_timestamp       = lv_logical_load_timestamp,
            old_version.updated_by                   = p_user,
            old_version.update_timestamp             = SYSTIMESTAMP,
            old_version.effective_start_timestamp    = p_effective_start_timestamp,
            old_version.string_data                  = new_version.string_data,
            old_version.date_data                    = new_version.date_data,
            old_version.number_data                  = new_version.number_data
    WHERE ((nrg_common.has_value_changed(old_version.string_data,new_version.string_data)= 1 ) OR
           (nrg_common.has_value_changed(old_version.date_data,new_version.date_data)= 1 ) OR
           (nrg_common.has_value_changed(old_version.number_data,new_version.number_data)= 1 ))
    WHEN NOT MATCHED THEN INSERT
           (event_id,
            attribute_name,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            string_data,
            date_data,
            number_data)
    VALUES (new_version.event_id,
            new_version.attribute_name,
            lv_logical_load_timestamp,
            p_user,
            SYSTIMESTAMP,
            p_user,
            SYSTIMESTAMP,
            p_effective_start_timestamp,
            new_version.string_data,
            new_version.date_data,
            new_version.number_data);
                                    
  END put_event_attributes;  

-- ===================================================================================
-- put_events
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate as_questionnaires and call put procedures for nested objects
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_as_questionnaires              table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_events(p_user   IN event_store_events.created_by%TYPE,
                       p_events IN event_store_event_tab) IS
                                  
    lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;
    
  BEGIN
    
    MERGE INTO event_store_events_h history USING 
       (SELECT old_version.*,events.date_time as new_eff_start_timestamp
          FROM event_store_events old_version
          JOIN TABLE(CAST(p_events AS event_store_event_tab)) events
            ON old_version.event_id = events.event_id
        WHERE ((nrg_common.has_value_changed(old_version.username,events.username)= 1 ) OR
               (nrg_common.has_value_changed(old_version.date_time,events.date_time)= 1 ) OR
               (nrg_common.has_value_changed(old_version.customer_id,events.customer_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.event_type,events.event_type)= 1 ))
               ) new_version
        ON (history.event_id                  = new_version.event_id AND
            history.effective_start_timestamp = new_version.effective_start_timestamp)
    WHEN MATCHED THEN UPDATE 
        SET history.updated_by                 = new_version.updated_by,
            history.action                     = 'U',
            history.action_timestamp           = SYSTIMESTAMP,
            history.update_timestamp           = new_version.update_timestamp,
            history.logical_load_timestamp     = new_version.logical_load_timestamp,
            history.reporting_date             = new_version.reporting_date,
            history.business_date              = new_version.business_date,
            history.customer_id                = new_version.customer_id,
            history.username                   = new_version.username,
            history.date_time                  = new_version.date_time,
            history.event_type                 = new_version.event_type
    WHEN NOT MATCHED THEN INSERT
           (event_id,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,   
            reporting_date,
            business_date,
            customer_id,
            username,
            date_time,
            event_type)
    VALUES (new_version.event_id,
            new_version.logical_load_timestamp,
            new_version.created_by,
            new_version.create_timestamp,
            new_version.updated_by,
            new_version.update_timestamp,
            new_version.effective_start_timestamp,
            new_version.new_eff_start_timestamp,
            'U',
            SYSTIMESTAMP,
            new_version.reporting_date,
            new_version.business_date,
            new_version.customer_id,
            new_version.username,
            new_version.date_time,
            new_version.event_type);
  
    MERGE INTO event_store_events old_version USING 
         (SELECT DISTINCT events.event_id,
                          events.customer_id,
                          events.username,
                          events.date_time,
                          events.event_type
            FROM TABLE(CAST(p_events AS event_store_event_tab)) events) new_version
        ON (old_version.event_id = new_version.event_id)
    WHEN MATCHED THEN UPDATE
        SET old_version.logical_load_timestamp       = lv_logical_load_timestamp,
            old_version.updated_by                   = p_user,
            old_version.update_timestamp             = SYSTIMESTAMP,
            old_version.effective_start_timestamp    = new_version.date_time,
            old_version.reporting_date               = nrg_common.get_reporting_date(new_version.date_time),
            old_version.business_date                = nrg_common.get_business_date(new_version.date_time),
            old_version.customer_id                  = new_version.customer_id,
            old_version.username                     = new_version.username,
            old_version.date_time                    = new_version.date_time,
            old_version.event_type                   = new_version.event_type
    WHERE ((nrg_common.has_value_changed(old_version.username,new_version.username)= 1 ) OR
           (nrg_common.has_value_changed(old_version.date_time,new_version.date_time)= 1 ) OR
           (nrg_common.has_value_changed(old_version.customer_id,new_version.customer_id)= 1 ) OR
           (nrg_common.has_value_changed(old_version.event_type,new_version.event_type)= 1 ))
    WHEN NOT MATCHED THEN INSERT
           (event_id,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            reporting_date,
            business_date,
            customer_id,
            username,
            date_time,
            event_type)
    VALUES (new_version.event_id,
            lv_logical_load_timestamp,
            p_user,
            SYSTIMESTAMP,
            p_user,
            SYSTIMESTAMP,
            new_version.date_time,
            nrg_common.get_reporting_date(new_version.date_time),
            nrg_common.get_business_date(new_version.date_time),
            new_version.customer_id,
            new_version.username,
            new_version.date_time,
            new_version.event_type);             
             
    IF p_events IS NOT NULL THEN  
      
      FOR lv_count in 1.. p_events.count LOOP  
        
        put_event_attributes (p_user                      => p_user,
                              p_effective_start_timestamp => p_events(lv_count).date_time,
                              p_event_attributes          => p_events(lv_count).event_store_attributes);                          
             
      END LOOP;
    
    END IF;                         
             
  EXCEPTION WHEN OTHERS THEN

    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);    
                                                        
  END put_events;
  
END nrg_event_store;
/