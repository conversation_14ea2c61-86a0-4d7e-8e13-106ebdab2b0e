CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_cash_account
AS
    -- ===================================================================================
    -- NRG_CASH_ACCOUNT
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the cash accounts model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Cash Account Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     07/12/2011   Manoj Kumar         1.0   Creation
    --     21/12/2011   Mark Gornicki       1.1   Amended both create stub and put account to
    --                                            setup a cash_balance account record
    --     03/12/2011   Manoj Kumar         1.2   Renamed channelid to channel_id, requestid to request_id
    --     03/12/2011   Mark Gornicki      1.3   Added reporting_date when creating cash_balance records
    --     23/01/2012   Mark Gornicki       1.4   Due to an issue at source for the cash service, using multiple servers
    --                                            and there being milliseconds difference in server system times
    --                                            we can get the scenario of a transaction being created with a creation_time
    --                                            milliseconds **before** the account itself was created according to its creation_time,
    --                                            hence the balance record that was created with the account is not updated i.e. left at zero.
    --                                            Since we no longer actually store any balance history (now a view on entries) this
    --                                            make identifing and tracking this somewhat more problematic.
    --                                            The same situation can arise when dealing with multiple transactions occuring on the
    --                                            multiple servers within milliseconds of each other, when the creation_time/modification time of the physically more recent
    --                                            transaction is prior to the creation_time/modification time of an earlier transaction.
    --                                            Partial Solution
    --                                            We cannot do anything to alleviate the later without comprising the integrity of the data and
    --                                            it requires a proper solution at source.
    --                                            However when creating accounts we can set the effective_start_timestamp of the balance
    --                                            to our default timestamp such that the first transaction received would be guaranteed to
    --                                            update the balance since its effective_start_timestamp would be more recent than the default.
    --                                            This is implemented put_cash_account since default is already set in the create_cash_account_stub
    --     09/02/2012   Mark Gornicki       1.5   Added the additional parameter p_platform which is going to form part of the primary key for cash accounts
    --                                            and all related changes.  Also adding a secondary check for cash schema definitions to verify is_provider and cash_schema_code
    --                                            Due to potential NRG caching issues
    --     08/03/2012   Mark Gornicki       1.6   Added code to create an artifical trading account of type 'NG_INTERNAL' if the cash account
    --                                            is an internal cmc provider account i.e. no customer trading account exists.
    --                                            Cash Account attribute values will be used to set some of the trading account values
    --     16/05/2012   Mark Gornicki       1.7   Added code to create identity stubs
    --     28/01/2013   Sanket Mittal       1.8   Updated the code for ng cash balance
    --     08/05/2013   Sanket Mittal       1.9   Updated for nrg_trading account P2 changes
    --     31/07/2013   Prachi Shah         2.1  Updated for nrg_trading account customer change reasons
    --     26/09/2013   Ravi Shankar Gopal  2.2   Included logic to include newly added columns in the Trading_accounts table
    --     11/10/2013   Ravi Shankar Gopal  2.3   Removed Reference to BI_ODS.ACCNT_RISK_CLASSIFICATIONS and BI_ODS.ACCNT_RISK_CLASSIFICATIONS_H Tables as per BER-665
    --     16/10/2013   Ravi Shankar Gopal  2.3   Removed Reference to Absolute liquidation levels and added logic to consume newly added column Liquidation_type as per BER-668
    --     29/11/2013   Adam Krasnicki      2.4   New parameter added to execution of the trading account pkg procedure
    --     17/12/2013   Adam Krasnicki      2.5   Adjusted to trading account changes
    --     07/01/2014   Adam Krasnicki      2.6   trading_account_type for cash_accounts table changed
    --     05/03/2014   Adam Krasnicki      2.6   Change in put_cash_account in stub procedure call for trading accounts
    --     26/03/2014   Adam Krasnicki      2.6   Change in put_trading_account call (instead of partner_id now entire object is sent to TA)
    --     09/05/2014   Adam Krasnicki      2.7   Adjusted for -ve customer_id (put_customer call in put_cash_account proc)
    --     13/05/2014   Adam Krasnicki      2.8   Get_subbed_ids modified. It returns now cash accounts for which customers with -ve customer_id do not have company_id
    --     19/05/2014   Adam Krasnicki      2.8   in put_customer in put_cash_account procedure p_effective_start_timestamp is set to systimestamp, because any customer data was not updated if existed earlier
    --     29/05/2014   Adam Krasnicki      2.8   in put_customer p_is_customer_segregated set to 'NO'
    --     10/06/2014   Adam Krasnicki      2.8   in put_trading_account and put_customer bi_ods.trading_accounts.open_date and bi_ods.customers.start_date
    --                                            are populated with bi_ods.cash_accounts.creation_time
    --     10/06/2014   Adam Krasnicki      2.8   p_is_deleted in put_trading_account set from 'NO' to pararemetr sent to put_cash_account to be in sync
    --     13/06/2014   Adam Krasnicki      2.8   in Put_customer 2 fatca parameters added
    --     18/06/2014   Adam Krasnicki      2.8   Case for p_refcodifier = 'MARKETMAKER'
    --     10/09/2014   Adam Krasnicki      2.8   nrg_trading_account.put_trading procedure was changed, (new parameters added)
    --     09/10/2014   Adam Krasnicki      2.9   two new attributes added to put_cash_account procedure
    --     06/01/2015   Adam Krasnicki      2.9   New parameter p_blocked_cash added to put_trading_accounts call
    --     28/01/2015   Adam Krasnicki      2.9   New parameter to put_trading_account (p_guaran_stop_loss_or_style)
    --     23/07/2015   Adam Krasnicki      2.9   New parameter to put_customer
    --     29/07/2015   Adam Krasnicki      2.9   New parameter to put_trading_account
    --     05/08/2015   Adam Krasnicki      2.9   p_account_code param added into put procedure
    --     27/08/2015   Adam Krasnicki      2.9   put_trading_account call modified in nrg_trading_account pkg
    --     16/10/2015   Sanket Mittal       3.0   put_customer call modified for is_segreagted value BER-2107
    --     28/07/2016   Sanket Mittal       3.1   create_session_stub to include channel id BER-2778
    --     20/10/2016   Karolina Krasnicka  3.2   put_cash_account: Handle new RefCodifier 'MarketMakerCustomer' and 'MarketMakerPartner' BER-3003
    --     16/02/2016   S Kinkhabwala       3.3   JIRA - 3344 ifx the reference to nrg_customer.put_customer and nrg_trading_account.put_trading_account
    --     16/02/2016   Sanket Mittal       3.4   BER-3540 Impact of changes to NRG_TRADING_ACCOUNT, NRG_CUSTOMER on NRG_CASH_ACCOUNT
    --     07/06/2017   Sanket Mittal       3.5   BER-3692 ODS Fix Trading_Account.Account_Function for Next Gen Partner Cash Accounts
    --     31/08/2017   Patrick Dinwiddy    3.6   BER-3916 Add new attribs to put_trading_account
	  --		 22/05/2018 	S Kinkhabwala       3.7		BER-4443 - 	Add/deletenew attribs to put_trading_account
    --     14/09/2018   Patrick Dinwiddy    3.8   BER-4807 Added new procedure for financial general ledger entries
    --     29/04/2019   Patrick Dinwiddy    3.9   New parameter to put_customer
    
    -- ===================================================================================
    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================

    gc_version            CONSTANT VARCHAR2(3) := '3.9';
    gc_cr                 CONSTANT VARCHAR2(1) := CHR(10);
    gc_true               CONSTANT PLS_INTEGER := 1;
    gc_false              CONSTANT PLS_INTEGER := 0;
    gc_default_timestamp  CONSTANT TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');


    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the cash account record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_cash_account_record       This is the old version of the cash account transaction
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_cash_account_record cash_accounts%ROWTYPE,
                           p_effective_end_timestamp cash_accounts_h.effective_end_timestamp%TYPE,
                           p_action                  cash_accounts_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO cash_accounts_h (
                              account_number,
                              platform,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              effective_start_timestamp,
                              effective_end_timestamp,
                              action,
                              action_timestamp,
                              trading_account_id,
                              trading_account_type,
                              session_key,
                              is_provider,
                              cash_schema_code,
                              account_type,
                              account_currency,
                              account_comment,
                              refcodifier,
                              refcode,
                              version_number,
                              creation_time,
                              creation_identity_token,
                              crtn_on_bhlf_of_idntty_tkn,
                              update_time,
                              update_identity_token,
                              updt_on_bhlf_of_idntty_tkn,
                              account_function,
                              channel_id,
                              request_id,
                              account_limit,
                              is_conditional_debit_locked,
                              is_deleted,
                              Account_Category,
                              Is_Segregated,
                              Account_Code
                            )
                     VALUES (
                              p_old_cash_account_record.account_number,
                p_old_cash_account_record.platform,
                              p_old_cash_account_record.logical_load_timestamp,
                              p_old_cash_account_record.created_by,
                              p_old_cash_account_record.create_timestamp,
                              p_old_cash_account_record.updated_by,
                              p_old_cash_account_record.update_timestamp,
                              p_old_cash_account_record.effective_start_timestamp,
                              p_effective_end_timestamp,
                              p_action,
                              SYSTIMESTAMP,
                              p_old_cash_account_record.trading_account_id,
                              p_old_cash_account_record.trading_account_type,
                              p_old_cash_account_record.session_key,
                              p_old_cash_account_record.is_provider,
                              p_old_cash_account_record.cash_schema_code,
                              p_old_cash_account_record.account_type,
                              p_old_cash_account_record.account_currency,
                              p_old_cash_account_record.account_comment,
                              p_old_cash_account_record.refcodifier,
                              p_old_cash_account_record.refcode,
                              p_old_cash_account_record.version_number,
                              p_old_cash_account_record.creation_time,
                              p_old_cash_account_record.creation_identity_token,
                              p_old_cash_account_record.crtn_on_bhlf_of_idntty_tkn,
                              p_old_cash_account_record.update_time,
                              p_old_cash_account_record.update_identity_token,
                              p_old_cash_account_record.updt_on_bhlf_of_idntty_tkn,
                              p_old_cash_account_record.account_function,
                              p_old_cash_account_record.channel_id,
                              p_old_cash_account_record.request_id,
                              p_old_cash_account_record.account_limit,
                              p_old_cash_account_record.is_conditional_debit_locked,
                              p_old_cash_account_record.is_deleted,
                              p_old_cash_account_record.Account_Category,
                              p_old_cash_account_record.Is_Segregated,
                              p_old_cash_account_record.Account_Code
                            );
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --
        -- This would occur in the case when the same message is replayed
        --
        -- Possible ways in which message gets replayed
        -- Request Response
        -- Upstream publishes for manual updates
        -- Message replayed
        --
        UPDATE cash_accounts_h
        SET logical_load_timestamp         = p_old_cash_account_record.logical_load_timestamp,
            updated_by                     = p_old_cash_account_record.updated_by,
            update_timestamp               = p_old_cash_account_record.update_timestamp,
            effective_start_timestamp      = p_old_cash_account_record.effective_start_timestamp,
            trading_account_id             = p_old_cash_account_record.trading_account_id,
            trading_account_type           = p_old_cash_account_record.trading_account_type,
            session_key                    = p_old_cash_account_record.session_key,
            is_provider                    = p_old_cash_account_record.is_provider,
            cash_schema_code               = p_old_cash_account_record.cash_schema_code,
            account_type                   = p_old_cash_account_record.account_type,
            account_currency               = p_old_cash_account_record.account_currency,
            account_comment                = p_old_cash_account_record.account_comment,
            refcodifier                    = p_old_cash_account_record.refcodifier,
            refcode                        = p_old_cash_account_record.refcode,
            version_number                 = p_old_cash_account_record.version_number,
            creation_time                  = p_old_cash_account_record.creation_time,
            creation_identity_token        = p_old_cash_account_record.creation_identity_token,
            crtn_on_bhlf_of_idntty_tkn     = p_old_cash_account_record.crtn_on_bhlf_of_idntty_tkn,
            update_time                    = p_old_cash_account_record.update_time,
            update_identity_token          = p_old_cash_account_record.update_identity_token,
            updt_on_bhlf_of_idntty_tkn     = p_old_cash_account_record.updt_on_bhlf_of_idntty_tkn,
            account_function               = p_old_cash_account_record.account_function,
            channel_id                     = p_old_cash_account_record.channel_id,
            request_id                     = p_old_cash_account_record.request_id,
            account_limit                  = p_old_cash_account_record.account_limit,
            is_conditional_debit_locked    = p_old_cash_account_record.is_conditional_debit_locked,
            is_deleted                     = p_old_cash_account_record.is_deleted,
            account_category               = p_old_cash_account_record.Account_Category,
            is_segregated                  = p_old_cash_account_record.Is_Segregated,
            account_code                   = p_old_cash_account_record.Account_Code
        WHERE account_number               = p_old_cash_account_record.account_number AND
              platform                     = p_old_cash_account_record.platform AND
              effective_start_timestamp    = p_old_cash_account_record.effective_start_timestamp AND
              (nrg_common.has_value_changed(trading_account_id,p_old_cash_account_record.trading_account_id) = 1 OR
               nrg_common.has_value_changed(trading_account_type,p_old_cash_account_record.trading_account_type) = 1 OR
               nrg_common.has_value_changed(session_key,p_old_cash_account_record.session_key) = 1 OR
               nrg_common.has_value_changed(is_provider,p_old_cash_account_record.is_provider) = 1 OR
               nrg_common.has_value_changed(cash_schema_code,p_old_cash_account_record.cash_schema_code) = 1 OR
               nrg_common.has_value_changed(account_type,p_old_cash_account_record.account_type) = 1 OR
               nrg_common.has_value_changed(account_currency,p_old_cash_account_record.account_currency) = 1 OR
               nrg_common.has_value_changed(account_comment,p_old_cash_account_record.account_comment) = 1 OR
               nrg_common.has_value_changed(refcodifier,p_old_cash_account_record.refcodifier) = 1 OR
               nrg_common.has_value_changed(refcode,p_old_cash_account_record.refcode) = 1 OR
               nrg_common.has_value_changed(version_number,p_old_cash_account_record.version_number) = 1 OR
               nrg_common.has_value_changed(creation_time,p_old_cash_account_record.creation_time) = 1 OR
               nrg_common.has_value_changed(creation_identity_token,p_old_cash_account_record.creation_identity_token) = 1 OR
               nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_old_cash_account_record.crtn_on_bhlf_of_idntty_tkn) = 1 OR
               nrg_common.has_value_changed(update_time,p_old_cash_account_record.update_time) = 1 OR
               nrg_common.has_value_changed(update_identity_token,p_old_cash_account_record.update_identity_token) = 1 OR
               nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_old_cash_account_record.updt_on_bhlf_of_idntty_tkn) = 1 OR
               nrg_common.has_value_changed(account_function,p_old_cash_account_record.account_function) = 1 OR
               nrg_common.has_value_changed(channel_id,p_old_cash_account_record.channel_id) = 1 OR
               nrg_common.has_value_changed(request_id,p_old_cash_account_record.request_id) = 1 OR
               nrg_common.has_value_changed(account_limit,p_old_cash_account_record.account_limit) = 1 OR
               nrg_common.has_value_changed(is_conditional_debit_locked,p_old_cash_account_record.is_conditional_debit_locked) = 1 OR
               nrg_common.has_value_changed(is_deleted,p_old_cash_account_record.is_deleted) = 1 OR
               nrg_common.has_value_changed(account_category,p_old_cash_account_record.account_category) = 1 OR
               nrg_common.has_value_changed(is_segregated,p_old_cash_account_record.is_segregated) = 1 OR
               nrg_common.has_value_changed(account_code, p_old_cash_account_record.Account_Code) = 1
        );

    END put_history;

    -- ===================================================================================
    -- PUBLIC MODULES
    -- ===================================================================================
    --
    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC
    IS
    BEGIN
        logger.logger.set_module('version');
        RETURN gc_version;
    EXCEPTION
        WHEN OTHERS THEN
            logger.logger.severe(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            RAISE;
    END version;

    -- ===================================================================================
    -- create_cash_account_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a cash account stub in case the cash account is missing
  --     Since every account should have a balance, then we also stub a record in cash balances
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CASH_ACCOUNTS
  --     CASH_BALANCES
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_account_number                 Cash Account Number
  --     p_platform                       Platform e.g. NG,MMCFD,MMSB
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_cash_account_stub ( p_user                       IN cash_accounts.created_by%TYPE,
                                         p_logical_load_timestamp     IN cash_accounts.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp  IN cash_accounts.effective_start_timestamp%TYPE,
                                         p_account_number             IN cash_accounts.account_number%TYPE,
                                         p_platform                   IN cash_accounts.platform%TYPE )
    IS

    PRAGMA AUTONOMOUS_TRANSACTION;

    BEGIN

      BEGIN

          INSERT INTO cash_accounts (
                                     account_number,
                                     platform,
                                     logical_load_timestamp,
                                     created_by,
                                     create_timestamp,
                                     updated_by,
                                     update_timestamp,
                                     effective_start_timestamp
                                    )
                            VALUES (
                                    p_account_number,
                                    p_platform,
                                    p_logical_load_timestamp,
                                    p_user,
                                    SYSTIMESTAMP,
                                    p_user,
                                    SYSTIMESTAMP,
                                    gc_default_timestamp
                                   );
        EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            --
            --Since the cash account already exists no need to create the stub
            --
            NULL;
      END;

      --
      --Create the 0 cash balance for the NG cash account. For MM the cash balance will be populated by Backoffice EOD
      --


      IF p_platform = 'NG' THEN
        BEGIN
            INSERT INTO ng_cash_balances (account_number,
                                          platform,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          effective_start_timestamp,
                                          booking_number,
                                          record_source,
                                          balance_timestamp,
                                          currency,
                                          cleared_account_balance,
                                          uncleared_account_balance
                                          )
                                   VALUES (p_account_number,
                                           p_platform,
                                           p_logical_load_timestamp,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_user,
                                           SYSTIMESTAMP,
                                           gc_default_timestamp,
                                           -1,
                                           NULL,
                                           gc_default_timestamp,
                                           NULL,
                                           0,
                                           0
                                          );
          EXCEPTION
            WHEN DUP_VAL_ON_INDEX THEN
              --
              --Since the cash account already exists no need to create the stub
              --
              NULL;
        END;

      END IF;
      --
      COMMIT;

    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the cash account already exists no need to create the stub
        --
        NULL;



    END create_cash_account_stub;

    -- ===================================================================================
    -- put_cash_account
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a cash accounts
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CASH_ACCOUNTS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_trading_account_id             Trading account id
    --     p_trading_account_type           Trading account type
    --     p_account_number                 Cash Account Number
    --     p_session_key                    Session key
    --     p_is_provider                    Is provider
    --     p_cash_schema_code               Cash schema code
    --     p_account_type                   Account type
    --     p_account_currency               Account currency
    --     p_account_comment                Account comment
    --     p_refcodifier                    Refcodifier
    --     p_refcode                        Refcode
    --     p_version_number                 Version Number
    --     p_creation_time                  Creation Time
    --     p_creation_identity_token        Creation identity token
    --     p_crtn_on_bhlf_of_idntty_tkn     Creation on behalf of identity token
    --     p_update_time                    Update time
    --     p_update_identity_token          Update identity token
    --     p_updt_on_bhlf_of_idntty_tkn     Update on behalf of identity token
    --     p_account_function               Account function
    --     p_channel_id                      Channel id
    --     p_request_id                      Request id
    --     p_account_limit                  Account limit
    --     p_is_conditional_debit_locked    Is conditional debit locked
    --     p_is_deleted                     Is deleted
  --     p_platform                       Platform e.g. NG,MMCFD,MMSB
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Cash Account Deleted Before Update and After Insert
    --     -20004    Default Exception
    --     -20005    Multiple Cash Schema Definitions Exist For The Account
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_cash_account (p_user                           IN cash_accounts.created_by%TYPE,
                                p_effective_start_timestamp      IN cash_accounts.effective_start_timestamp%TYPE,
                                p_account_number                 IN cash_accounts.account_number%TYPE,
                                p_trading_account_id             IN cash_accounts.trading_account_id%TYPE,
                                p_trading_account_type           IN cash_accounts.trading_account_type%TYPE,
                                p_session_key                    IN cash_accounts.session_key%TYPE,
                                p_is_provider                    IN cash_accounts.is_provider%TYPE,
                                p_cash_schema_code               IN cash_accounts.cash_schema_code%TYPE,
                                p_account_type                   IN cash_accounts.account_type%TYPE,
                                p_account_currency               IN cash_accounts.account_currency%TYPE,
                                p_account_comment                IN cash_accounts.account_comment%TYPE,
                                p_refcodifier                    IN cash_accounts.refcodifier%TYPE,
                                p_refcode                        IN cash_accounts.refcode%TYPE,
                                p_version_number                 IN cash_accounts.version_number%TYPE,
                                p_creation_time                  IN cash_accounts.creation_time%TYPE,
                                p_creation_identity_token        IN cash_accounts.creation_identity_token%TYPE,
                                p_crtn_on_bhlf_of_idntty_tkn     IN cash_accounts.crtn_on_bhlf_of_idntty_tkn%TYPE,
                                p_update_time                    IN cash_accounts.update_time%TYPE,
                                p_update_identity_token          IN cash_accounts.update_identity_token%TYPE,
                                p_updt_on_bhlf_of_idntty_tkn     IN cash_accounts.updt_on_bhlf_of_idntty_tkn%TYPE,
                                p_account_function               IN cash_accounts.account_function%TYPE,
                                p_channel_id                     IN cash_accounts.channel_id%TYPE,
                                p_request_id                     IN cash_accounts.request_id%TYPE,
                                p_account_limit                  IN cash_accounts.account_limit%TYPE,
                                p_is_conditional_debit_locked    IN cash_accounts.is_conditional_debit_locked%TYPE,
                                p_is_deleted                     IN cash_accounts.is_deleted%TYPE,
                                p_platform                       IN cash_accounts.platform%TYPE,
                                p_account_category               IN cash_accounts.Account_Category%TYPE,
                                p_is_segregated                  IN cash_accounts.Is_Segregated%TYPE,
                                p_account_code                   IN Cash_Accounts.Account_Code%TYPE
                        )
    IS
    lv_effective_start_time     cash_accounts.effective_start_timestamp%TYPE := NULL;
    lv_logical_load_timestamp   cash_accounts.logical_load_timestamp%TYPE;
    lv_trading_account_id       cash_accounts.trading_account_id%TYPE := NULL;
    lv_trading_account_type     cash_accounts.trading_account_type%TYPE := NULL;
    lv_ca_trading_account_type  cash_accounts.trading_account_type%TYPE := NULL;
    lv_old_cash_account         cash_accounts%ROWTYPE;
    lv_is_provider              cash_accounts.is_provider%TYPE;
    lv_cash_schema_code         cash_accounts.cash_schema_code%TYPE;
    lv_partner_trading_account  partner_trading_account_obj;
    lv_partner_id               partners.Partner_Id%TYPE;
    lv_profit_center_name       partners.Profit_Centre_Name%TYPE;
    lv_ca_schm_cd               partners.Ca_Schm_Cd%TYPE;
    lv_company_id               partners.Company_Id%TYPE;
    lv_is_segregated            cash_accounts.is_segregated%TYPE;

    ltab_identity_id            identity_id_tab;
    lv_trading_account_id_tab   trading_account_id_tab;

    lex_unknown_operation_type  EXCEPTION;
    lex_cash_account_not_found  EXCEPTION;
    lex_multiple_cash_schm_def  EXCEPTION;

    BEGIN
      logger.logger.set_module('put_cash_account');

      -- set the logical load timestamp to now

      lv_logical_load_timestamp := SYSTIMESTAMP;

      --
      -- Initial setting for trading_account_type (may be overridden later)
      --
      lv_trading_account_type := NVL(p_trading_account_type,'CUSTOMER');
      lv_is_segregated        := NVL(p_is_segregated,'NO');

      --
      -- This is to ensure that the session record exists for the referential integrity
      --
      IF p_session_key IS NOT NULL THEN
        nrg_session.create_session_stub(p_user                       => p_user,
                                        p_logical_load_timestamp     => lv_logical_load_timestamp,
                                        p_effective_start_timestamp  => p_effective_start_timestamp,
                                        p_session_key                => p_session_key,
                                        p_channel_id                 => p_channel_id,
                                        p_identity_id                => p_creation_identity_token,
                                        p_on_behalf_of_identity_id   => p_crtn_on_bhlf_of_idntty_tkn);
      END IF;

      ---
      --- Determine the trading_account_type for the cash_accounts NG platform
      ---

  IF (p_platform = 'NG') THEN
     --BEGIN
     --SELECT CASE WHEN account_number IS NOT NULL THEN 'NG_INTERNAL'
     --         ELSE  NULL END
     --       INTO   lv_ca_trading_account_type
     -- FROM (SELECT account_number FROM bi_ods.cash_account_schema_defns
     --       UNION
     --       SELECT account_number FROM bi_ods.cash_account_schema_defns_h)
     --WHERE account_number = p_account_number;

     --EXCEPTION WHEN NO_DATA_FOUND THEN

      --IF upper(p_refcodifier) IN ('TRADINGACCOUNT', 'MARKETMAKER','MARKETMAKERCUSTOMER')  THEN lv_ca_trading_account_type:='CUSTOMER';
      --  ELSIF upper(p_refcodifier) = 'BROKERACCOUNT' THEN lv_ca_trading_account_type:='HEDGE';
      --  ELSIF upper(p_refcodifier) IN ('PARTNERACCOUNT','MARKETMAKERPARTNER') THEN lv_ca_trading_account_type:='PARTNER_CASH';
      --END IF;
     --END;

     CASE
      WHEN upper(p_refcodifier) IN ('TRADINGACCOUNT', 'MARKETMAKER','MARKETMAKERCUSTOMER') THEN
        lv_ca_trading_account_type:='CUSTOMER';
      WHEN upper(p_refcodifier) = 'BROKERACCOUNT' THEN
        lv_ca_trading_account_type:='HEDGE';
      WHEN upper(p_refcodifier) IN ('PARTNERACCOUNT','MARKETMAKERPARTNER') THEN
        lv_ca_trading_account_type:='PARTNER_CASH';
      WHEN NVL(UPPER(p_refcodifier),'`') NOT IN ('TRADINGACCOUNT', 'MARKETMAKER','MARKETMAKERCUSTOMER', 'BROKERACCOUNT', 'PARTNERACCOUNT', 'MARKETMAKERPARTNER') THEN
        lv_ca_trading_account_type:='NG_INTERNAL';
      ELSE
        lv_ca_trading_account_type := NULL;
     END CASE;

   END IF;
    --
    -- Secondary Check for the correct IS_PROVIDER and CASH_SCHEMA_CODE settings
    -- This is required if NRG has not cached the NextGen Cash Schema Definitions properly
    -- and for MM non trading accounts where it would not come through NRG at this time
    --
    --BEGIN
    --  CASE
    --  WHEN p_platform <> 'NG' AND lv_trading_account_type <> 'CUSTOMER' THEN
    --       lv_is_provider := 'YES';
    --     lv_cash_schema_code := NULL;
    --  WHEN p_platform = 'NG' AND NVL(p_is_provider,'NO') <> 'YES' THEN
    --       -- do secondary check
    --     SELECT 'YES', schema
    --     INTO lv_is_provider, lv_cash_schema_code
    --     FROM cash_account_schema_defns
    --     WHERE account_number = p_account_number
    --     AND platform = p_platform;
    --  ELSE
    --       lv_is_provider := NVL(p_is_provider,'NO');
    --     lv_cash_schema_code := p_cash_schema_code;
    --  END CASE;

    --EXCEPTION
    --  WHEN NO_DATA_FOUND THEN
    --     lv_is_provider := NVL(p_is_provider,'NO');
    --   lv_cash_schema_code := p_cash_schema_code;
    --WHEN TOO_MANY_ROWS THEN
    --     RAISE lex_multiple_cash_schm_def;
    --END;

    CASE
      WHEN p_platform <> 'NG' AND lv_trading_account_type = 'CUSTOMER' THEN
        lv_is_provider := 'NO';
        lv_cash_schema_code := NULL;
      WHEN p_platform = 'NG' AND NVL(UPPER(p_refcodifier),'`') NOT IN ('TRADINGACCOUNT', 'MARKETMAKER','MARKETMAKERCUSTOMER', 'BROKERACCOUNT', 'PARTNERACCOUNT', 'MARKETMAKERPARTNER') THEN
        lv_is_provider := 'YES';
        lv_cash_schema_code := p_cash_schema_code;
      WHEN p_platform <> 'NG' AND lv_trading_account_type <> 'CUSTOMER' THEN
        lv_is_provider := 'YES';
        lv_cash_schema_code := NULL;
      ELSE
        lv_is_provider := 'NO';
        lv_cash_schema_code := p_cash_schema_code;
    END CASE;



      --
      -- This is to ensure that the trading account record exists for referential integrity
    -- If this is an NG Provider cash account and trading_account_id was null then create
    -- an NG_INTERNAL stub record
      --
    CASE
      WHEN p_trading_account_id IS NOT NULL THEN
           lv_trading_account_id := p_trading_account_id;  -- default setting
             nrg_trading_account.create_trading_account_stub (p_user                       => p_user,
                                                              p_logical_load_timestamp     => lv_logical_load_timestamp,
                                                              p_effective_start_timestamp  => p_effective_start_timestamp,
                                                              p_trading_account_id         => lv_trading_account_id,
                                                              p_trading_account_type       => CASE WHEN p_platform = 'NG' THEN lv_ca_trading_account_type ELSE lv_trading_account_type END
                                                             );

      WHEN p_platform = 'NG' AND p_trading_account_id IS NULL AND lv_is_provider = 'YES' THEN

         lv_trading_account_id := p_account_number;  -- use the cash account number as a trading account id for the ODS generated stub
         lv_trading_account_type := 'NG_INTERNAL';

             nrg_trading_account.put_trading_account (p_user                         => p_user,
                                                      p_effective_start_timestamp    => p_effective_start_timestamp,
                                                      p_trading_account_id           => lv_trading_account_id,
                                                      p_partner_trading_account_obj  => NULL,
                                                      p_account_version              => p_version_number,
                                                      p_source_platform              => p_platform,
                                                      p_source_account_id            => NULL,
                                                      p_account_name                 => p_account_type,
                                                      p_account_type                 => 'NG Internal',
                                                      p_account_function             => p_account_function,
                                                      p_open_date                    => NULL,
                                                      p_closure_date                 => NULL,
                                                      p_unauthorised_to_trade_date   => NULL,
                                                      p_authorised_to_trade          => 'NO',
                                                      p_is_managed                   => 'NO',
                                                      p_is_locked                    => 'NO',
                                                      p_is_closed                    => 'NO',
                                                      p_allow_shortening             => 'NO',
                                                      p_allow_financing              => 'NO',
                                                      p_allow_position_increase      => 'NO',
                                                      p_allow_trans_margin_model     => 'NO',
                                                      p_is_internal_test_account     => 'NO',
                                                      p_currency                     => p_account_currency,
                                                      p_profit_centre                => 'Dummy',
                                                      p_profile_description          => NULL,
                                                      p_power_of_attorneys           => NULL,
                                                      p_terms_and_conditions         => NULL,
                                                      p_tenant_template_code         => NULL,
                                                      p_is_dormant                   => 'NO',
                                                      p_dormant_date                 => NULL,
                                                      p_last_marked_non_dormant_date => NULL,
                                                      p_unauthorised_to_trade_reason => NULL,
                                                      p_unauth_to_trade_other_detail => NULL,
                                                      p_closure_reason               => NULL,
                                                      p_closure_other_details        => NULL,
                                                      p_cash_accounting_schema_code  => lv_cash_schema_code,
                                                      p_product_schema_code          => NULL,
                                                      p_trading_risk_schema_code     => NULL,
                                                      p_payment_schema_code          => NULL,
                                                      p_crm_schema_code              => NULL,
                                                      p_locale_id                    => NULL,
                                                      p_locale_version               => NULL,
                                                      p_locale_language              => NULL,
                                                      p_locale_territory             => NULL,
                                                      p_locale_timezone              => NULL,
                                                      p_payment_profile_id           => NULL,
                                                      p_pay_prof_block_all_payments  => NULL,
                                                      p_pay_prof_block_withdrawals   => NULL,
                                                      p_payment_profile_warn_fct     => NULL,
                                                      p_pay_prf_blck_withd_prcng_spk => NULL,
                                                      p_pay_prf_blck_withd_reason    => NULL,
                                                      p_pay_prf_blck_all_pay_reason  => NULL,
                                                      p_payment_prof_warn_fct_reason => NULL,
                                                      p_product_wrapper_settings     => NULL,
                                                      /***** Begin Modificaiton for V2.3 - BER665 *****/
                                                      --p_trad_risk_classifications    => NULL, -- Removed as per BER-665
                                                      /***** End Modification for v2.3 *****/
                                                      p_customer_ids                 => NULL,
                                                      p_is_deleted                   => 'NO',
                                                      p_trading_account_type         => lv_trading_account_type,
                                                      p_primary_account_holder_id    => NULL,
                                                      p_is_joint_account             => 'NO',
                                                      p_independent_margin_amount    => NULL,
                                                      /***** Begin Modification for V2.3 - BER668 *****/
                                                      --p_absolute_liquidation_level   => NULL, --Removed as per BER-668
                                                      /***** End Modification for V2.3 *****/
                                                      p_liquidation_level            => NULL,
                                                      p_liquidation_warning_level    => NULL,
                                                      p_bi_trading_status            => NULL,
                                                      p_bi_trading_status_date       => NULL,
                                                      /***** Begin Modification for V2.2 - BER633 *****/
                                                      p_sales_tax_country            => NULL,
                                                      p_commission_schema_code       => NULL,
                                                      p_carrying_costs_schema_code   => NULL,
                                                      p_crryng_csts_offst_schm_cd    => NULL,
                                                      p_price_schema_code            => NULL,
                                                      /***** End Modification for V2.2 *****/
                                                      p_accnt_cust_chng_rsns_tab     => NULL
                                                      /***** Begin Modification for V2.3 - BER668 *****/
                                                      ,p_liquidation_type            => NULL
                                                      /***** End Modification for V2.3 *****/
                                                      /***** Begin Modification for - BER646 *****/
                                                      ,p_liquidation_reset_level     => NULL
                                                      /***** End Modification *****/
                                                      ,p_hedge_closeout_schema_code  => NULL
                                                      ,p_hedge_broker_code           => NULL
                                                      ,p_hedge_broker_account_number => NULL
                                                      ,p_hedge_execution_broker      => NULL
                                                      ,p_hedge_clearing_broker       => NULL
                                                      ,p_is_hedge_tradable           => NULL
                                                      ,p_hedge_calculate_commission  => NULL
                                                      ,p_hedge_firm_reference_number => NULL
                                                      ,p_hedge_legal_entity_identifie => NULL
                                                      ,p_hedge_bank_identifier_code   => NULL
                                                      ,p_hedge_corporate_sector       => NULL
                                                      ,p_hedge_financial_counterparty => NULL
                                                      ,p_hedge_account_address_tab   => NULL
                                                      ,p_hedge_account_contact_tab   => NULL
                                                      ,p_utt_reason_tab              => NULL
                                                      ,p_order_verification_time     => NULL
                                                      ,p_introducer_reference        => NULL
                                                      ,p_trading_desk_id             => NULL
                                                      ,p_crng_costs_offset_threshold => NULL
                                                      ,p_apply_crng_costs_offset     => NULL
                                                      ,p_liquidation_method          => NULL
                                                      ,p_ovrrde_ccy_cc_offset        => NULL
                                                      ,p_ovrrde_ccy_cc_offset_thrshld=> NULL
                                                      ,p_match_open_trades           => NULL
                                                      ,p_apply_carrying_costs        => NULL
                                                      ,p_block_all_payments_ref      => NULL
                                                      ,p_block_withdrawals_ref       => NULL
                                                      ,p_has_too_many_cards          => NULL
                                                      ,p_funded_by_bank_account      => NULL
                                                      ,p_closeout_schema_code        => NULL
                                                      ,p_ref_commission_schema_code  => NULL
                                                      ,p_ref_price_schema_code       => NULL
                                                      ,p_accnt_manual_products_tab   => NULL
                                                      ,p_ref_price_schm_cds_asst_tab => NULL
                                                      ,p_manual_liquidation          => NULL
                                                      ,p_all_products_are_manual_trd => NULL
                                                      ,p_blocked_cash                => NULL
                                                      ,p_guaran_stop_loss_or_style   => NULL
                                                      ,p_hedge_account_asset_class   => NULL
                                                      ,p_time_dependent_liquidation  => NULL
                                                      ,p_time_dep_liquidation_period => NULL
                                                      ,p_charge_commission           => NULL
                                                      ,p_quote_proposed_timeout      => NULL
                                                      ,p_accnt_liquidation_levels_tab => NULL
                                                      ,p_accnt_order_exec_type_tab   => NULL
                                                      ,p_binary_buffer               => NULL
                                                      ,p_order_time_delay            => NULL
                                                      ,p_loss_limit                  => NULL
                                                      ,p_trading_account_multiplier => NULL
                                                      ,p_record_source               => 'CM'
                                                      ,p_is_phone_trading_only       => NULL
                                                      ,p_is_manual_execution_only      => NULL
                                                      ,p_knockout_account_min_balance  => NULL
                                                      ,p_is_holding_costs_modify_gslo  => NULL
                                                      ,p_previous_partner_id           => NULL
                                                      ,p_instrument_schema_code        => NULL
                                                      ,p_apply_ko_frwrd_rllvr_csts     => NULL
                                                      ,p_locale                        => NULL
                                                      ,p_is_segregated                 => NULL
                                                      ,p_tax_declarations              => NULL
                                                      ,p_fxr_schema_code               => NULL
                                                      ,p_exec_commission_schema_code   => NULL
                                                      ,p_is_gslo_widening_allowed      => NULL
                                                      ,p_is_write_off_deficit          => NULL
                                                      ,p_is_multicurrency_enabled      => NULL
                                                      ,p_multicurrency_cnvrsn_period   => NULL
                                                      ,p_order_execution_mode          => NULL
                                                      ,p_hedge_usage_type              => NULL
                                                      ,p_ta_pll_exec_trad_checks       => NULL
                                                      ,p_stockbroking_account_id       => NULL
                                                      ,p_is_institutional              => NULL
                                                      ,p_is_mifid_reduce_only          => NULL
                                                      ,p_use_tradable_rights			     => NULL
                                                      ,p_hedge_crypto_broker_type		   => NULL
                                                      ,p_evolutions                    => NULL
                                                      ,p_open_order_trade_limit        => NULL
                                                      );

  WHEN p_platform = 'NG' AND p_trading_account_id IS NULL AND lv_is_provider = 'NO' AND upper(p_refcodifier)='PARTNERACCOUNT' THEN

        lv_trading_account_id := p_account_number;  -- use the cash account number as a trading account id for the ODS generated stub
        lv_trading_account_type := 'PARTNER_CASH';

        -- initialize object to send partner_id to tarding_accounts table
        lv_partner_id := to_number(p_refcode);

        lv_partner_trading_account := partner_trading_account_obj(NULL,NULL,lv_partner_id,NULL, NULL, NULL, NULL, NULL);

        --get the profit center name and cash account schema code from partners table

       BEGIN

        SELECT p.Profit_Centre_Name, p.Ca_Schm_Cd, p.Company_Id
         INTO lv_profit_center_name, lv_ca_schm_cd, lv_company_id
         FROM partners p WHERE p.partner_id = lv_partner_id;

       EXCEPTION
         WHEN NO_DATA_FOUND THEN

           lv_profit_center_name := NULL;
           lv_ca_schm_cd := NULL;
           lv_company_id := NULL;

        END;


             nrg_trading_account.put_trading_account (p_user                         => p_user,
                                                      p_effective_start_timestamp    => p_effective_start_timestamp,
                                                      p_trading_account_id           => lv_trading_account_id,
                                                      p_partner_trading_account_obj  => lv_partner_trading_account,
                                                      p_account_version              => p_version_number,
                                                      p_source_platform              => p_platform,
                                                      p_source_account_id            => NULL,
                                                      p_account_name                 => p_account_type,
                                                      p_account_type                 => 'Partners Cash',
                                                      p_account_function             => p_account_function,
                                                      p_open_date                    => p_creation_time,
                                                      p_closure_date                 => NULL,
                                                      p_unauthorised_to_trade_date   => NULL,
                                                      p_authorised_to_trade          => 'NO',
                                                      p_is_managed                   => 'NO',
                                                      p_is_locked                    => 'NO',
                                                      p_is_closed                    => 'NO',
                                                      p_allow_shortening             => 'NO',
                                                      p_allow_financing              => 'NO',
                                                      p_allow_position_increase      => 'NO',
                                                      p_allow_trans_margin_model     => 'NO',
                                                      p_is_internal_test_account     => 'NO',
                                                      p_currency                     => p_account_currency,
                                                      p_profit_centre                => NVL(lv_profit_center_name,'Dummy'),
                                                      p_profile_description          => NULL,
                                                      p_power_of_attorneys           => NULL,
                                                      p_terms_and_conditions         => NULL,
                                                      p_tenant_template_code         => NULL,
                                                      p_is_dormant                   => 'NO',
                                                      p_dormant_date                 => NULL,
                                                      p_last_marked_non_dormant_date => NULL,
                                                      p_unauthorised_to_trade_reason => NULL,
                                                      p_unauth_to_trade_other_detail => NULL,
                                                      p_closure_reason               => NULL,
                                                      p_closure_other_details        => NULL,
                                                      p_cash_accounting_schema_code  => lv_ca_schm_cd,
                                                      p_product_schema_code          => NULL,
                                                      p_trading_risk_schema_code     => NULL,
                                                      p_payment_schema_code          => NULL,
                                                      p_crm_schema_code              => NULL,
                                                      p_locale_id                    => NULL,
                                                      p_locale_version               => NULL,
                                                      p_locale_language              => NULL,
                                                      p_locale_territory             => NULL,
                                                      p_locale_timezone              => NULL,
                                                      p_payment_profile_id           => NULL,
                                                      p_pay_prof_block_all_payments  => NULL,
                                                      p_pay_prof_block_withdrawals   => NULL,
                                                      p_payment_profile_warn_fct     => NULL,
                                                      p_pay_prf_blck_withd_prcng_spk => NULL,
                                                      p_pay_prf_blck_withd_reason    => NULL,
                                                      p_pay_prf_blck_all_pay_reason  => NULL,
                                                      p_payment_prof_warn_fct_reason => NULL,
                                                      p_product_wrapper_settings     => NULL,
                                                      /***** Begin Modificaiton for V2.3 - BER665 *****/
                                                      --p_trad_risk_classifications    => NULL, -- Removed as per BER-665
                                                      /***** End Modification for v2.3 *****/
                                                      p_customer_ids                 => NULL,
                                                      p_is_deleted                   => p_is_deleted,
                                                      p_trading_account_type         => lv_trading_account_type,
                                                      p_primary_account_holder_id    => NULL,
                                                      p_is_joint_account             => 'NO',
                                                      p_independent_margin_amount    => NULL,
                                                      /***** Begin Modification for V2.3 - BER668 *****/
                                                      --p_absolute_liquidation_level   => NULL, --Removed as per BER-668
                                                      /***** End Modification for V2.3 *****/
                                                      p_liquidation_level            => NULL,
                                                      p_liquidation_warning_level    => NULL,
                                                      p_bi_trading_status            => NULL,
                                                      p_bi_trading_status_date       => NULL,
                                                      /***** Begin Modification for V2.2 - BER633 *****/
                                                      p_sales_tax_country            => NULL,
                                                      p_commission_schema_code       => NULL,
                                                      p_carrying_costs_schema_code   => NULL,
                                                      p_crryng_csts_offst_schm_cd    => NULL,
                                                      p_price_schema_code            => NULL,
                                                      /***** End Modification for V2.2 *****/
                                                      p_accnt_cust_chng_rsns_tab     => NULL
                                                      /***** Begin Modification for V2.3 - BER668 *****/
                                                      ,p_liquidation_type            => NULL
                                                      /***** End Modification for V2.3 *****/
                                                      /***** Begin Modification for - BER646 *****/
                                                      ,p_liquidation_reset_level     => NULL
                                                      /***** End Modification *****/
                                                      ,p_hedge_closeout_schema_code  => NULL
                                                      ,p_hedge_broker_code           => NULL
                                                      ,p_hedge_broker_account_number => NULL
                                                      ,p_hedge_execution_broker      => NULL
                                                      ,p_hedge_clearing_broker       => NULL
                                                      ,p_is_hedge_tradable           => NULL
                                                      ,p_hedge_calculate_commission  => NULL
                                                      ,p_hedge_firm_reference_number => NULL
                                                      ,p_hedge_legal_entity_identifie => NULL
                                                      ,p_hedge_bank_identifier_code   => NULL
                                                      ,p_hedge_corporate_sector       => NULL
                                                      ,p_hedge_financial_counterparty => NULL
                                                      ,p_hedge_account_address_tab   => NULL
                                                      ,p_hedge_account_contact_tab   => NULL
                                                      ,p_utt_reason_tab              => NULL
                                                      ,p_order_verification_time     => NULL
                                                      ,p_introducer_reference        => NULL
                                                      ,p_trading_desk_id             => NULL
                                                      ,p_crng_costs_offset_threshold => NULL
                                                      ,p_apply_crng_costs_offset     => NULL
                                                      ,p_liquidation_method          => NULL
                                                      ,p_ovrrde_ccy_cc_offset        => NULL
                                                      ,p_ovrrde_ccy_cc_offset_thrshld=> NULL
                                                      ,p_match_open_trades           => NULL
                                                      ,p_apply_carrying_costs        => NULL
                                                      ,p_block_all_payments_ref      => NULL
                                                      ,p_block_withdrawals_ref       => NULL
                                                      ,p_has_too_many_cards          => NULL
                                                      ,p_funded_by_bank_account      => NULL
                                                      ,p_closeout_schema_code        => NULL
                                                      ,p_ref_commission_schema_code  => NULL
                                                       ,p_ref_price_schema_code       => NULL
                                                      ,p_accnt_manual_products_tab   => NULL
                                                      ,p_ref_price_schm_cds_asst_tab => NULL
                                                      ,p_manual_liquidation          => NULL
                                                      ,p_all_products_are_manual_trd => NULL
                                                      ,p_blocked_cash                => NULL
                                                      ,p_guaran_stop_loss_or_style   => NULL
                                                      ,p_hedge_account_asset_class   => NULL
                                                      ,p_time_dependent_liquidation  => NULL
                                                      ,p_time_dep_liquidation_period => NULL
                                                      ,p_charge_commission           => NULL
                                                      ,p_quote_proposed_timeout      => NULL
                                                      ,p_accnt_liquidation_levels_tab => NULL
                                                      ,p_accnt_order_exec_type_tab   => NULL
                                                      ,p_binary_buffer               => NULL
                                                      ,p_order_time_delay            => NULL
                                                      ,p_loss_limit                  => NULL
                                                      ,p_trading_account_multiplier => NULL
                                                      ,p_record_source               => 'CM'
                                                      ,p_is_phone_trading_only       => NULL
                                                      ,p_is_manual_execution_only      => NULL
                                                      ,p_knockout_account_min_balance  => NULL
                                                      ,p_is_holding_costs_modify_gslo  => NULL
                                                      ,p_previous_partner_id           => NULL
                                                      ,p_instrument_schema_code        => NULL
                                                      ,p_apply_ko_frwrd_rllvr_csts     => NULL
                                                      ,p_locale                        => NULL
                                                      ,p_is_segregated                 => NULL
                                                      ,p_tax_declarations              => NULL
                                                      ,p_fxr_schema_code               => NULL
                                                      ,p_exec_commission_schema_code   => NULL
                                                      ,p_is_write_off_deficit          => NULL
                                                      ,p_is_gslo_widening_allowed      => NULL
                                                      ,p_is_multicurrency_enabled      => NULL
                                                      ,p_multicurrency_cnvrsn_period   => NULL
                                                      ,p_order_execution_mode          => NULL
                                                      ,p_hedge_usage_type              => NULL
                                                      ,p_ta_pll_exec_trad_checks       => NULL
                                                      ,p_stockbroking_account_id       => NULL
                                                      ,p_is_institutional              => NULL
                                                      ,p_is_mifid_reduce_only          => NULL
                                                      ,p_use_tradable_rights			     => NULL
                                                      ,p_hedge_crypto_broker_type		   => NULL
                                                      ,p_evolutions                    => NULL
                                                      ,p_open_order_trade_limit        => NULL
                                                      );

       ----
       ---- Put csutomer with customer_id = -1*cash_account_id. It is required to create a connection used in trading account mapping
       ----

       --initilize table of objects
       lv_trading_account_id_tab := trading_account_id_tab (trading_account_id_obj(lv_trading_account_id));

                         nrg_customer.put_customer(p_user                            => p_user,
                                                   p_effective_start_timestamp       => systimestamp,
                                                   p_customer_id                     => -1*abs(lv_trading_account_id),
                                                   p_customer_version                => NULL,
                                                   p_customer_type                   => 'CORPORATE',
                                                   p_customer_sub_type               => 'Company',
                                                   p_customer_status                 => NULL,
                                                   p_start_date                      => p_creation_time,
                                                   p_is_politically_exposed          => NULL,
                                                   p_probability_of_default          => NULL,
                                                   p_risk_level                      => NULL,
                                                   p_regulatory_classification       => NULL,
                                                   p_is_sales_trader_managed         => NULL,
                                                   p_is_do_not_contact               => NULL,
                                                   p_is_out_of_area                  => NULL,
                                                   p_is_chargeback_requested         => NULL,
                                                   p_risk_level_change_reason        => NULL,
                                                   p_marketing_data_id               => NULL,
                                                   p_marketing_data_version          => NULL,
                                                   p_allow_email                     => NULL,
                                                   p_allow_mail                      => NULL,
                                                   p_allow_telephone                 => NULL,
                                                   p_allow_sms                       => NULL,
                                                   p_tax_profile_id                  => NULL,
                                                   p_tax_profile_version             => NULL,
                                                   p_is_tax_exempt                   => NULL,
                                                   p_tax_residency_code              => NULL,
                                                   p_withholding_tax_rate            => NULL,
                                                   p_withholding_tax_exption_cde     => NULL,
                                                   --p_appropriateness_id              => NULL,
                                                   --p_is_appropriate                  => NULL,
                                                   --p_appropriateness_reason          => NULL,
                                                   --p_agreed_to_proceed               => NULL,
                                                   --p_financial_suitability_score     => NULL,
                                                   --p_knowledge_experience_score      => NULL,
                                                   p_company_id                      => lv_company_id,
                                                   p_person_id                       => NULL,
                                                   p_trading_account_ids             => lv_trading_account_id_tab,
                                                   p_customer_registrations          => NULL,
                                                   p_is_deleted                      => NULL,
                                                   p_assigned_agent_identity         => NULL,
                                                   p_assigned_sales_trdr_identity    => NULL,
                                                   p_bi_segmentation                 => NULL,
                                                   p_is_potential_premium            => NULL,
                                                   p_trust_type                      => NULL,
                                                   p_lead_office                     => NULL,
                                                   p_assigned_agent_identity_date    => NULL,
                                                   p_last_remediation_date           => NULL,
                                                   p_next_remediation_date           => NULL,
                                                   p_next_remediation_alert_date     => NULL,
                                                   p_online_declaration              => NULL,
                                                   p_block_marketing                 => NULL,
                                                   p_is_customer_segregated          => lv_is_segregated,
                                                   p_referrer_reference              => NULL,
                                                   p_full_remediation_required       => NULL,
                                                   p_fatca_status                    => NULL,
                                                   p_fatca_assessment_date           => NULL,
                                                   --p_apprtns_assessment_date         => NULL,
                                                   p_remediation_enabled             => NULL,
                                                   p_market_countrpart_partner_id    => NULL,
                                                   p_speedbet_opt_out                => NULL,
                                                   p_speedbet_opt_out_status_date    => NULL,
                                                   p_is_binaries_opt_out             => NULL,
                                                   p_binaries_opt_out_status_date    => NULL,
                                                   p_perm_agent_identity             => NULL,
                                                   p_perm_agent_identity_date        => NULL,
                                                   p_is_cfd_provider                 => NULL,
                                                   p_tax_declarations                => NULL,
                                                   p_customer_apprprtnss_info        => NULL,
                                                   p_customer_enquiries              => NULL,
                                                   p_lead_profile_country            => NULL,
                                                   p_is_profiling_allowed			       => NULL,
                                                   p_lifecycle_status                => NULL,
                                                   p_customer_tax_germany            => NULL);

     ELSE
         lv_trading_account_id := NULL;
    END CASE;

    --
      -- Identity Stubs for referential integrity
      --
    -- Build array of ids
    --
      select  identity_id_obj(identity_id)
      bulk collect into ltab_identity_id
      from
         (select distinct t.identity_id
          from
            (select p_creation_identity_token as identity_id
             from dual
             union
             select p_crtn_on_bhlf_of_idntty_tkn
             from dual
             union
             select p_update_identity_token
             from dual
             union
             select p_updt_on_bhlf_of_idntty_tkn
             from dual
            ) t
          where t.identity_id is not null
         );
    --
    -- Create Identity Stubs
    --
    IF ltab_identity_id.COUNT <> 0 THEN
       nrg_identity.create_identity_stub (p_user                       => p_user,
                                            p_logical_load_timestamp     => lv_logical_load_timestamp,
                                            p_effective_start_timestamp  => p_effective_start_timestamp,
                                            p_identity_id                => ltab_identity_id);
    END IF;



      --
      --Insert the new cash account on the table
      --
      BEGIN
        INSERT INTO cash_accounts(account_number,
                                  platform,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  effective_start_timestamp,
                                  trading_account_id,
                                  trading_account_type,
                                  session_key,
                                  is_provider,
                                  cash_schema_code,
                                  account_type,
                                  account_currency,
                                  account_comment,
                                  refcodifier,
                                  refcode,
                                  version_number,
                                  creation_time,
                                  creation_identity_token,
                                  crtn_on_bhlf_of_idntty_tkn,
                                  update_time,
                                  update_identity_token,
                                  updt_on_bhlf_of_idntty_tkn,
                                  account_function,
                                  channel_id,
                                  request_id,
                                  account_limit,
                                  is_conditional_debit_locked,
                                  is_deleted,
                                  Account_Category,
                                  Is_Segregated,
                                  Account_Code
                                )
                         VALUES ( p_account_number,
                                  p_platform,
                                  lv_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_effective_start_timestamp,
                                  lv_trading_account_id,
                                  decode(p_platform,'NG', lv_ca_trading_account_type, lv_trading_account_type),
                                  p_session_key,
                                  lv_is_provider,
                                  lv_cash_schema_code,
                                  p_account_type,
                                  p_account_currency,
                                  p_account_comment,
                                  p_refcodifier,
                                  p_refcode,
                                  p_version_number,
                                  p_creation_time,
                                  p_creation_identity_token,
                                  p_crtn_on_bhlf_of_idntty_tkn,
                                  p_update_time,
                                  p_update_identity_token,
                                  p_updt_on_bhlf_of_idntty_tkn,
                                  p_account_function,
                                  p_channel_id,
                                  p_request_id,
                                  p_account_limit,
                                  p_is_conditional_debit_locked,
                                  p_is_deleted,
                                  p_account_category,
                                  lv_is_segregated,
                                  p_account_code
                                );
    --
        -- Assuming insert of cash account proceeds normally, attempt to insert the cash balance
        -- Should a cash balance record exist perform no actions
        --
    -- 1.4 Fix
    -- Originally the effective_start_timestamp,business_date,reporting_date,balance_timestamp
    -- were set to p_effective_start_timestamp,TRUNC(p_effective_start_timestamp),TRUNC(p_effective_start_timestamp),p_effective_start_timestamp
    -- but now are set to gc_default_timestamp to resolve the issue where transaction data has creation_time milliseconds
    -- prior to the account creation_time and hence p_effective_start_timestamp value
    --
      BEGIN
        IF p_platform = 'NG' THEN
          INSERT INTO ng_cash_balances (account_number,
                                        platform,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        booking_number,
                                        record_source,
                                        balance_timestamp,
                                        currency,
                                        cleared_account_balance,
                                        uncleared_account_balance
                                                  )
                        VALUES (p_account_number,
                                p_platform,
                                lv_logical_load_timestamp,
                                p_user,
                                SYSTIMESTAMP,
                                p_user,
                                SYSTIMESTAMP,
                                gc_default_timestamp,
                                -1,
                                NULL,
                                gc_default_timestamp,
                                p_account_currency,
                                0,
                                0
                                );
        END IF;
      EXCEPTION
          WHEN DUP_VAL_ON_INDEX THEN
            --
            --Since the cash account already exists no need to create the stub
            --
            NULL;
      END;

      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          --Get the effective start time of the previous version of the cash account

          BEGIN
            SELECT cacnt.*
            INTO lv_old_cash_account
            FROM cash_accounts cacnt
            WHERE account_number = p_account_number
        AND platform = p_platform
            FOR UPDATE;

            lv_effective_start_time := lv_old_cash_account.effective_start_timestamp;
          EXCEPTION
            WHEN no_data_found THEN
              RAISE lex_cash_account_not_found;
          END;
      END;

      CASE
        WHEN lv_effective_start_time IS NULL THEN
          --
          --Since the cash account is already inserted no operation will be performed
          --
          NULL;
        WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time <= p_effective_start_timestamp THEN

          --
          --In case when the effective start time of the incoming record is greater than the effective start time
          --of the cash account on the cash_accounts table then this specifies the update of the cash account
          --Update cash account with the latest version
          --
          UPDATE cash_accounts
          SET logical_load_timestamp       = lv_logical_load_timestamp,
            updated_by                     = p_user,
            update_timestamp               = SYSTIMESTAMP,
            effective_start_timestamp      = p_effective_start_timestamp,
            trading_account_id             = lv_trading_account_id,
            trading_account_type           = decode(p_platform,'NG', lv_ca_trading_account_type, lv_trading_account_type),
            session_key                    = p_session_key,
            is_provider                    = lv_is_provider,
            cash_schema_code               = lv_cash_schema_code,
            account_type                   = p_account_type,
            account_currency               = p_account_currency,
            account_comment                = p_account_comment,
            refcodifier                    = p_refcodifier,
            refcode                        = p_refcode,
            version_number                 = p_version_number,
            creation_time                  = p_creation_time,
            creation_identity_token        = p_creation_identity_token,
            crtn_on_bhlf_of_idntty_tkn     = p_crtn_on_bhlf_of_idntty_tkn,
            update_time                    = p_update_time,
            update_identity_token          = p_update_identity_token,
            updt_on_bhlf_of_idntty_tkn     = p_updt_on_bhlf_of_idntty_tkn,
            account_function               = p_account_function,
            channel_id                      = p_channel_id,
            request_id                      = p_request_id,
            account_limit                  = p_account_limit,
            is_conditional_debit_locked    = p_is_conditional_debit_locked,
            is_deleted                     = p_is_deleted,
            account_category               = p_account_category,
            is_segregated                  = lv_is_segregated,
            account_code                   = p_account_code
          WHERE account_number = p_account_number AND
            platform = p_platform AND
                (nrg_common.has_value_changed(trading_account_id,lv_trading_account_id) = 1 OR
                 nrg_common.has_value_changed(trading_account_type,decode(p_platform,'NG', lv_ca_trading_account_type, lv_trading_account_type)) = 1 OR
                 nrg_common.has_value_changed(session_key,p_session_key) = 1 OR
                 nrg_common.has_value_changed(is_provider,lv_is_provider) = 1 OR
                 nrg_common.has_value_changed(cash_schema_code,lv_cash_schema_code) = 1 OR
                 nrg_common.has_value_changed(account_type,p_account_type) = 1 OR
                 nrg_common.has_value_changed(account_currency,p_account_currency) = 1 OR
                 nrg_common.has_value_changed(account_comment,p_account_comment) = 1 OR
                 nrg_common.has_value_changed(refcodifier,p_refcodifier) = 1 OR
                 nrg_common.has_value_changed(refcode,p_refcode) = 1 OR
                 nrg_common.has_value_changed(version_number,p_version_number) = 1 OR
                 nrg_common.has_value_changed(creation_time,p_creation_time) = 1 OR
                 nrg_common.has_value_changed(creation_identity_token,p_creation_identity_token) = 1 OR
                 nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_crtn_on_bhlf_of_idntty_tkn) = 1 OR
                 nrg_common.has_value_changed(update_time,p_update_time) = 1 OR
                 nrg_common.has_value_changed(update_identity_token,p_update_identity_token) = 1 OR
                 nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_updt_on_bhlf_of_idntty_tkn) = 1 OR
                 nrg_common.has_value_changed(account_function,p_account_function) = 1 OR
                 nrg_common.has_value_changed(channel_id,p_channel_id) = 1 OR
                 nrg_common.has_value_changed(request_id,p_request_id) = 1 OR
                 nrg_common.has_value_changed(account_limit,p_account_limit) = 1 OR
                 nrg_common.has_value_changed(is_conditional_debit_locked,p_is_conditional_debit_locked) = 1 OR
                 nrg_common.has_value_changed(account_category,p_account_category) = 1 OR
                 nrg_common.has_value_changed(is_segregated,lv_is_segregated) = 1 OR
                 Nrg_Common.has_value_changed(account_code, p_account_code) = 1);
          --
          -- Write To History if update occurred
          --
          IF lv_old_cash_account.effective_start_timestamp <> gc_default_timestamp AND SQL%ROWCOUNT > 0  THEN
                --
                --If the existing cash account is not a stub record and the same record has not been replayed
                --then put the old record in the history
                --
                put_history (p_old_cash_account_record => lv_old_cash_account,
                             p_effective_end_timestamp => p_effective_start_timestamp,
                             p_action                  => 'U');
          END IF;
          lv_old_cash_account := NULL;

        WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time > p_effective_start_timestamp THEN
          --
          --When the effective start time of the incoming record is smaller than the effective start time of the
          --cash account on the cash_accounts table then it signifies old message is being replayed.
          --Write to history table directly
          --
          SELECT p_account_number,
                 p_platform,
                 lv_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp,
                 lv_trading_account_id,
                 lv_trading_account_type,
                 p_session_key,
                 lv_is_provider,
                 lv_cash_schema_code,
                 p_account_type,
                 p_account_currency,
                 p_account_comment,
                 p_refcodifier,
                 p_refcode,
                 p_version_number,
                 p_creation_time,
                 p_creation_identity_token,
                 p_crtn_on_bhlf_of_idntty_tkn,
                 p_update_time,
                 p_update_identity_token,
                 p_updt_on_bhlf_of_idntty_tkn,
                 p_account_function,
                 p_channel_id,
                 p_request_id,
                 p_account_limit,
                 p_is_conditional_debit_locked,
                 p_is_deleted,
                 p_account_category,
                 lv_is_segregated,
                 p_account_code
          INTO   lv_old_cash_account.account_number,
                 lv_old_cash_account.platform,
                 lv_old_cash_account.logical_load_timestamp,
                 lv_old_cash_account.created_by,
                 lv_old_cash_account.create_timestamp,
                 lv_old_cash_account.updated_by,
                 lv_old_cash_account.update_timestamp,
                 lv_old_cash_account.effective_start_timestamp,
                 lv_old_cash_account.trading_account_id,
                 lv_old_cash_account.trading_account_type,
                 lv_old_cash_account.session_key,
                 lv_old_cash_account.is_provider,
                 lv_old_cash_account.cash_schema_code,
                 lv_old_cash_account.account_type,
                 lv_old_cash_account.account_currency,
                 lv_old_cash_account.account_comment,
                 lv_old_cash_account.refcodifier,
                 lv_old_cash_account.refcode,
                 lv_old_cash_account.version_number,
                 lv_old_cash_account.creation_time,
                 lv_old_cash_account.creation_identity_token,
                 lv_old_cash_account.crtn_on_bhlf_of_idntty_tkn,
                 lv_old_cash_account.update_time,
                 lv_old_cash_account.update_identity_token,
                 lv_old_cash_account.updt_on_bhlf_of_idntty_tkn,
                 lv_old_cash_account.account_function,
                 lv_old_cash_account.channel_id,
                 lv_old_cash_account.request_id,
                 lv_old_cash_account.account_limit,
                 lv_old_cash_account.is_conditional_debit_locked,
                 lv_old_cash_account.is_deleted,
                 lv_old_cash_account.Account_Category,
                 lv_old_cash_account.Is_Segregated,
                 lv_old_cash_account.Account_Code
          FROM DUAL;

          put_history (p_old_cash_account_record => lv_old_cash_account,
                       p_effective_end_timestamp => p_effective_start_timestamp,
                       p_action                  => 'I');

          lv_old_cash_account := NULL;

        ELSE
          RAISE lex_unknown_operation_type;
      END CASE;

    EXCEPTION
        WHEN lex_cash_account_not_found THEN
          logger.logger.severe('Cash Account Deleted Before Update and After Insert');
          logger.logger.set_module(NULL);
          raise_application_error(-20003, 'Cash Account Deleted Before Update and After Insert');
        WHEN lex_unknown_operation_type THEN
          logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
          logger.logger.set_module(NULL);
          raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
    WHEN lex_multiple_cash_schm_def THEN
          logger.logger.warning('Multiple Cash Schema Definitions Exist For The Account');
          logger.logger.set_module(NULL);
          raise_application_error(-20005, 'Multiple Cash Schema Definitions Exist For The Account');
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
          --RAISE;
    END put_cash_account;

    -- ===================================================================================
    -- put_fnnc_gnrl_ldgr_mppngs
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to put the financial general ledger mapping entries
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_put_fnnc_gnrl_ldgr_mppngs      Table of financial general ledger mapping entries
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------

 PROCEDURE put_fnnc_gnrl_ldgr_mppngs (p_user                         IN finnc_gnrl_ldgr_mppngs.created_by%TYPE,
                                      p_effective_start_timestamp    IN finnc_gnrl_ldgr_mppngs.effective_start_timestamp%TYPE,
                                      p_finnc_gnrl_ldgr_mppngs       IN finnc_gnrl_ldgr_mppngs_tab) IS

      lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;
      ltab_fnc finnc_gnrl_ldgr_mppngs_tab;

BEGIN

  SELECT finnc_gnrl_ldgr_mppngs_obj(fnnc_gnrl_ldgr_mppng_id,
                                    version_number,
                                    is_deleted,
                                    creation_time,
                                    update_time,
                                    creation_identity_token,
                                    crtn_on_bhlf_of_idntty_tkn,
                                    update_identity_token,
                                    updt_on_bhlf_of_idntty_tkn,
                                    account_number,
                                    account_type,
                                    account_code,
                                    account_category,
                                    cash_accounting_schema_code,
                                    decode(is_segregated,'TRUE','YES','FALSE','NO'),
                                    primary_legal_entity,
                                    primary_account,
                                    primary_department,
                                    primary_profit_centre,
                                    primary_affiliate,
                                    b2b_bs_legal_entity,
                                    b2b_bs_account,
                                    b2b_bs_department,
                                    b2b_bs_profit_centre,
                                    b2b_bs_affiliate,
                                    b2b_pnl_legal_entity,
                                    b2b_pnl_account,
                                    b2b_pnl_department,
                                    b2b_pnl_profit_centre,
                                    b2b_pnl_affiliate,
                                    retained_earnings_account,
                                    decode(trl_blnc_prft_cntr_splt,'TRUE','YES','FALSE','NO'))
                               BULK COLLECT INTO ltab_fnc
                          FROM TABLE(CAST(p_finnc_gnrl_ldgr_mppngs AS finnc_gnrl_ldgr_mppngs_tab));

    MERGE INTO finnc_gnrl_ldgr_mppngs_h history
         USING (SELECT old_version.*
          FROM finnc_gnrl_ldgr_mppngs old_version
          JOIN (SELECT * FROM TABLE(ltab_fnc)) new_version
            ON old_version.fnnc_gnrl_ldgr_mppng_id  = new_version.fnnc_gnrl_ldgr_mppng_id
           AND old_version.version_number           = new_version.version_number
         WHERE ((nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted)= 1 ) OR
                (nrg_common.has_value_changed(old_version.creation_time,new_version.creation_time)= 1 ) OR
                (nrg_common.has_value_changed(old_version.update_time,new_version.update_time)= 1 ) OR
                (nrg_common.has_value_changed(old_version.creation_identity_token,new_version.creation_identity_token)= 1 ) OR
                (nrg_common.has_value_changed(old_version.crtn_on_bhlf_of_idntty_tkn,new_version.crtn_on_bhlf_of_idntty_tkn)= 1 ) OR
                (nrg_common.has_value_changed(old_version.update_identity_token,new_version.update_identity_token)= 1 ) OR
                (nrg_common.has_value_changed(old_version.updt_on_bhlf_of_idntty_tkn,new_version.updt_on_bhlf_of_idntty_tkn)= 1 ) OR
                (nrg_common.has_value_changed(old_version.account_number,new_version.account_number)= 1 ) OR
                (nrg_common.has_value_changed(old_version.account_type,new_version.account_type)= 1 ) OR
                (nrg_common.has_value_changed(old_version.account_code,new_version.account_code)= 1 ) OR
                (nrg_common.has_value_changed(old_version.account_category,new_version.account_category)= 1 ) OR
                (nrg_common.has_value_changed(old_version.cash_accounting_schema_code,new_version.cash_accounting_schema_code)= 1 ) OR
                (nrg_common.has_value_changed(old_version.is_segregated,new_version.is_segregated)= 1 ) OR
                (nrg_common.has_value_changed(old_version.primary_legal_entity,new_version.primary_legal_entity)= 1 ) OR
                (nrg_common.has_value_changed(old_version.primary_account,new_version.primary_account)= 1 ) OR
                (nrg_common.has_value_changed(old_version.primary_department,new_version.primary_department)= 1 ) OR
                (nrg_common.has_value_changed(old_version.primary_profit_centre,new_version.primary_profit_centre)= 1 ) OR
                (nrg_common.has_value_changed(old_version.primary_affiliate,new_version.primary_affiliate)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_bs_legal_entity,new_version.b2b_bs_legal_entity)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_bs_account,new_version.b2b_bs_account)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_bs_department,new_version.b2b_bs_department)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_bs_profit_centre,new_version.b2b_bs_profit_centre)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_bs_affiliate,new_version.b2b_bs_affiliate)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_pnl_legal_entity,new_version.b2b_pnl_legal_entity)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_pnl_account,new_version.b2b_pnl_account)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_pnl_department,new_version.b2b_pnl_department)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_pnl_profit_centre,new_version.b2b_pnl_profit_centre)= 1 ) OR
                (nrg_common.has_value_changed(old_version.b2b_pnl_affiliate,new_version.b2b_pnl_affiliate)= 1 ) OR
                (nrg_common.has_value_changed(old_version.retained_earnings_account,new_version.retained_earnings_account)= 1 ) OR
                (nrg_common.has_value_changed(old_version.trl_blnc_prft_cntr_splt,new_version.trl_blnc_prft_cntr_splt)= 1 )
                )) changes
       ON (history.fnnc_gnrl_ldgr_mppng_id      = changes.fnnc_gnrl_ldgr_mppng_id
       AND history.version_number               = changes.version_number
       AND history.effective_start_timestamp    = changes.effective_start_timestamp)
      WHEN MATCHED THEN UPDATE SET
            history.updated_by                    	 = changes.updated_by,
            history.action                           = 'U',
            history.action_timestamp                 = SYSTIMESTAMP,
            history.update_timestamp                 = changes.update_timestamp,
            history.effective_end_timestamp          = p_effective_start_timestamp,
            history.is_deleted                       = changes.is_deleted,
            history.creation_time                    = changes.creation_time,
            history.update_time                      = changes.update_time,
            history.creation_identity_token          = changes.creation_identity_token,
            history.crtn_on_bhlf_of_idntty_tkn       = changes.crtn_on_bhlf_of_idntty_tkn,
            history.update_identity_token            = changes.update_identity_token,
            history.updt_on_bhlf_of_idntty_tkn       = changes.updt_on_bhlf_of_idntty_tkn,
            history.account_number                   = changes.account_number,
            history.account_type                     = changes.account_type,
            history.account_code                     = changes.account_code,
            history.account_category                 = changes.account_category,
            history.cash_accounting_schema_code      = changes.cash_accounting_schema_code,
            history.is_segregated                    = changes.is_segregated,
            history.primary_legal_entity             = changes.primary_legal_entity,
            history.primary_account                  = changes.primary_account,
            history.primary_department               = changes.primary_department,
            history.primary_profit_centre            = changes.primary_profit_centre,
            history.primary_affiliate                = changes.primary_affiliate,
            history.b2b_bs_legal_entity              = changes.b2b_bs_legal_entity,
            history.b2b_bs_account                   = changes.b2b_bs_account,
            history.b2b_bs_department                = changes.b2b_bs_department,
            history.b2b_bs_profit_centre             = changes.b2b_bs_profit_centre,
            history.b2b_bs_affiliate                 = changes.b2b_bs_affiliate,
            history.b2b_pnl_legal_entity             = changes.b2b_pnl_legal_entity,
            history.b2b_pnl_account                  = changes.b2b_pnl_account,
            history.b2b_pnl_department               = changes.b2b_pnl_department,
            history.b2b_pnl_profit_centre            = changes.b2b_pnl_profit_centre,
            history.b2b_pnl_affiliate                = changes.b2b_pnl_affiliate,
            history.retained_earnings_account        = changes.retained_earnings_account,
            history.trl_blnc_prft_cntr_splt          = changes.trl_blnc_prft_cntr_splt
      WHEN NOT MATCHED THEN INSERT
           (fnnc_gnrl_ldgr_mppng_id,
            version_number,
            logical_load_timestamp,
            created_by,
            create_timestamp,
            updated_by,
            update_timestamp,
            effective_start_timestamp,
            effective_end_timestamp,
            action,
            action_timestamp,
            is_deleted,
            creation_time,
            update_time,
            creation_identity_token,
            crtn_on_bhlf_of_idntty_tkn,
            update_identity_token,
            updt_on_bhlf_of_idntty_tkn,
            account_number,
            account_type,
            account_code,
            account_category,
            cash_accounting_schema_code,
            is_segregated,
            primary_legal_entity,
            primary_account,
            primary_department,
            primary_profit_centre,
            primary_affiliate,
            b2b_bs_legal_entity,
            b2b_bs_account,
            b2b_bs_department,
            b2b_bs_profit_centre,
            b2b_bs_affiliate,
            b2b_pnl_legal_entity,
            b2b_pnl_account,
            b2b_pnl_department,
            b2b_pnl_profit_centre,
            b2b_pnl_affiliate,
            retained_earnings_account,
            trl_blnc_prft_cntr_splt)
    VALUES (changes.fnnc_gnrl_ldgr_mppng_id,
            changes.version_number,
            changes.logical_load_timestamp,
            changes.created_by,
            changes.create_timestamp,
            changes.updated_by,
            changes.update_timestamp,
            changes.effective_start_timestamp,
            p_effective_start_timestamp,
            'U',
            SYSTIMESTAMP,
            changes.is_deleted,
            changes.creation_time,
            changes.update_time,
            changes.creation_identity_token,
            changes.crtn_on_bhlf_of_idntty_tkn,
            changes.update_identity_token,
            changes.updt_on_bhlf_of_idntty_tkn,
            changes.account_number,
            changes.account_type,
            changes.account_code,
            changes.account_category,
            changes.cash_accounting_schema_code,
            changes.is_segregated,
            changes.primary_legal_entity,
            changes.primary_account,
            changes.primary_department,
            changes.primary_profit_centre,
            changes.primary_affiliate,
            changes.b2b_bs_legal_entity,
            changes.b2b_bs_account,
            changes.b2b_bs_department,
            changes.b2b_bs_profit_centre,
            changes.b2b_bs_affiliate,
            changes.b2b_pnl_legal_entity,
            changes.b2b_pnl_account,
            changes.b2b_pnl_department,
            changes.b2b_pnl_profit_centre,
            changes.b2b_pnl_affiliate,
            changes.retained_earnings_account,
            changes.trl_blnc_prft_cntr_splt);

  MERGE INTO finnc_gnrl_ldgr_mppngs old_version
       USING (SELECT * FROM TABLE(ltab_fnc)) new_version
          ON (old_version.fnnc_gnrl_ldgr_mppng_id  = new_version.fnnc_gnrl_ldgr_mppng_id
         AND  old_version.version_number           = new_version.version_number)
     WHEN MATCHED THEN UPDATE SET
            old_version.logical_load_timestamp           = systimestamp,
            old_version.updated_by                       = p_user,
            old_version.update_timestamp                 = systimestamp,
            old_version.effective_start_timestamp        = p_effective_start_timestamp,
            old_version.is_deleted                       = new_version.is_deleted,
            old_version.creation_time                    = new_version.creation_time,
            old_version.update_time                      = new_version.update_time,
            old_version.creation_identity_token          = new_version.creation_identity_token,
            old_version.crtn_on_bhlf_of_idntty_tkn       = new_version.crtn_on_bhlf_of_idntty_tkn,
            old_version.update_identity_token            = new_version.update_identity_token,
            old_version.updt_on_bhlf_of_idntty_tkn       = new_version.updt_on_bhlf_of_idntty_tkn,
            old_version.account_number                   = new_version.account_number,
            old_version.account_type                     = new_version.account_type,
            old_version.account_code                     = new_version.account_code,
            old_version.account_category                 = new_version.account_category,
            old_version.cash_accounting_schema_code      = new_version.cash_accounting_schema_code,
            old_version.is_segregated                    = new_version.is_segregated,
            old_version.primary_legal_entity             = new_version.primary_legal_entity,
            old_version.primary_account                  = new_version.primary_account,
            old_version.primary_department               = new_version.primary_department,
            old_version.primary_profit_centre            = new_version.primary_profit_centre,
            old_version.primary_affiliate                = new_version.primary_affiliate,
            old_version.b2b_bs_legal_entity              = new_version.b2b_bs_legal_entity,
            old_version.b2b_bs_account                   = new_version.b2b_bs_account,
            old_version.b2b_bs_department                = new_version.b2b_bs_department,
            old_version.b2b_bs_profit_centre             = new_version.b2b_bs_profit_centre,
            old_version.b2b_bs_affiliate                 = new_version.b2b_bs_affiliate,
            old_version.b2b_pnl_legal_entity             = new_version.b2b_pnl_legal_entity,
            old_version.b2b_pnl_account                  = new_version.b2b_pnl_account,
            old_version.b2b_pnl_department               = new_version.b2b_pnl_department,
            old_version.b2b_pnl_profit_centre            = new_version.b2b_pnl_profit_centre,
            old_version.b2b_pnl_affiliate                = new_version.b2b_pnl_affiliate,
            old_version.retained_earnings_account        = new_version.retained_earnings_account,
            old_version.trl_blnc_prft_cntr_splt          = new_version.trl_blnc_prft_cntr_splt
       WHERE ((nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted)= 1 ) OR
              (nrg_common.has_value_changed(old_version.creation_time,new_version.creation_time)= 1 ) OR
              (nrg_common.has_value_changed(old_version.update_time,new_version.update_time)= 1 ) OR
              (nrg_common.has_value_changed(old_version.creation_identity_token,new_version.creation_identity_token)= 1 ) OR
              (nrg_common.has_value_changed(old_version.crtn_on_bhlf_of_idntty_tkn,new_version.crtn_on_bhlf_of_idntty_tkn)= 1 ) OR
              (nrg_common.has_value_changed(old_version.update_identity_token,new_version.update_identity_token)= 1 ) OR
              (nrg_common.has_value_changed(old_version.updt_on_bhlf_of_idntty_tkn,new_version.updt_on_bhlf_of_idntty_tkn)= 1 ) OR
              (nrg_common.has_value_changed(old_version.account_number,new_version.account_number)= 1 ) OR
              (nrg_common.has_value_changed(old_version.account_type,new_version.account_type)= 1 ) OR
              (nrg_common.has_value_changed(old_version.account_code,new_version.account_code)= 1 ) OR
              (nrg_common.has_value_changed(old_version.account_category,new_version.account_category)= 1 ) OR
              (nrg_common.has_value_changed(old_version.cash_accounting_schema_code,new_version.cash_accounting_schema_code)= 1 ) OR
              (nrg_common.has_value_changed(old_version.is_segregated,new_version.is_segregated)= 1 ) OR
              (nrg_common.has_value_changed(old_version.primary_legal_entity,new_version.primary_legal_entity)= 1 ) OR
              (nrg_common.has_value_changed(old_version.primary_account,new_version.primary_account)= 1 ) OR
              (nrg_common.has_value_changed(old_version.primary_department,new_version.primary_department)= 1 ) OR
              (nrg_common.has_value_changed(old_version.primary_profit_centre,new_version.primary_profit_centre)= 1 ) OR
              (nrg_common.has_value_changed(old_version.primary_affiliate,new_version.primary_affiliate)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_bs_legal_entity,new_version.b2b_bs_legal_entity)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_bs_account,new_version.b2b_bs_account)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_bs_department,new_version.b2b_bs_department)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_bs_profit_centre,new_version.b2b_bs_profit_centre)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_bs_affiliate,new_version.b2b_bs_affiliate)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_pnl_legal_entity,new_version.b2b_pnl_legal_entity)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_pnl_account,new_version.b2b_pnl_account)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_pnl_department,new_version.b2b_pnl_department)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_pnl_profit_centre,new_version.b2b_pnl_profit_centre)= 1 ) OR
              (nrg_common.has_value_changed(old_version.b2b_pnl_affiliate,new_version.b2b_pnl_affiliate)= 1 ) OR
              (nrg_common.has_value_changed(old_version.retained_earnings_account,new_version.retained_earnings_account)= 1 ) OR
              (nrg_common.has_value_changed(old_version.trl_blnc_prft_cntr_splt,new_version.trl_blnc_prft_cntr_splt)= 1 ))
     WHEN NOT MATCHED THEN INSERT
             (fnnc_gnrl_ldgr_mppng_id,
              version_number,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              is_deleted,
              creation_time,
              update_time,
              creation_identity_token,
              crtn_on_bhlf_of_idntty_tkn,
              update_identity_token,
              updt_on_bhlf_of_idntty_tkn,
              account_number,
              account_type,
              account_code,
              account_category,
              cash_accounting_schema_code,
              is_segregated,
              primary_legal_entity,
              primary_account,
              primary_department,
              primary_profit_centre,
              primary_affiliate,
              b2b_bs_legal_entity,
              b2b_bs_account,
              b2b_bs_department,
              b2b_bs_profit_centre,
              b2b_bs_affiliate,
              b2b_pnl_legal_entity,
              b2b_pnl_account,
              b2b_pnl_department,
              b2b_pnl_profit_centre,
              b2b_pnl_affiliate,
              retained_earnings_account,
              trl_blnc_prft_cntr_splt)
      VALUES (new_version.fnnc_gnrl_ldgr_mppng_id,
              new_version.version_number,
              lv_logical_load_timestamp,
              p_user,
              systimestamp,
              p_user,
              systimestamp,
              p_effective_start_timestamp,
              new_version.is_deleted,
              new_version.creation_time,
              new_version.update_time,
              new_version.creation_identity_token,
              new_version.crtn_on_bhlf_of_idntty_tkn,
              new_version.update_identity_token,
              new_version.updt_on_bhlf_of_idntty_tkn,
              new_version.account_number,
              new_version.account_type,
              new_version.account_code,
              new_version.account_category,
              new_version.cash_accounting_schema_code,
              new_version.is_segregated,
              new_version.primary_legal_entity,
              new_version.primary_account,
              new_version.primary_department,
              new_version.primary_profit_centre,
              new_version.primary_affiliate,
              new_version.b2b_bs_legal_entity,
              new_version.b2b_bs_account,
              new_version.b2b_bs_department,
              new_version.b2b_bs_profit_centre,
              new_version.b2b_bs_affiliate,
              new_version.b2b_pnl_legal_entity,
              new_version.b2b_pnl_account,
              new_version.b2b_pnl_department,
              new_version.b2b_pnl_profit_centre,
              new_version.b2b_pnl_affiliate,
              new_version.retained_earnings_account,
              new_version.trl_blnc_prft_cntr_splt
              );

    UPDATE finnc_gnrl_ldgr_mppngs ps
       SET ps.is_deleted = 'YES',
           ps.update_timestamp = systimestamp,
           ps.logical_load_timestamp = systimestamp
     WHERE NOT EXISTS
          (SELECT 1 FROM TABLE(ltab_fnc) nw
            WHERE nw.fnnc_gnrl_ldgr_mppng_id   = ps.fnnc_gnrl_ldgr_mppng_id
              AND nw.version_number            = ps.version_number)
       AND ps.is_deleted != 'YES';

END;


    -- ===================================================================================
    -- get_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of stubbed cash account id's
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
    --     p_platform                       Cash Account Platform
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER,
                             p_platform     IN VARCHAR2) RETURN SYS_REFCURSOR

    IS
        lcuv_result           SYS_REFCURSOR;

        lex_unknown_platform  EXCEPTION;
    BEGIN
        logger.logger.set_module('get_stubbed_ids');

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
              FOR
               SELECT DISTINCT account_number FROM
                (
                 SELECT account_number
                   FROM cash_accounts
                  WHERE effective_start_timestamp = gc_default_timestamp
                    AND platform = p_platform
                  UNION ---second part valid only for NG platform
                 SELECT ca.Account_Number
                   FROM cash_accounts ca
                       ,customers     c
                       ,partners      p
                  WHERE ca.account_number = -c.customer_id AND
                        ca.account_number = ca.trading_account_id AND
                        to_number(ca.Refcode) = p.Partner_Id AND
                        p.Company_Id IS NOT NULL AND
                        ca.trading_account_type = 'PARTNER_CASH' AND
                        ca.platform = 'NG' AND
                        c.company_id IS NULL
                   ) WHERE rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
                SELECT DISTINCT account_number FROM (
                 SELECT account_number
                   FROM cash_accounts
                  WHERE effective_start_timestamp = gc_default_timestamp
                    AND platform = p_platform
                  UNION ---second part valid only for NG platform
                  SELECT ca.Account_Number
                   FROM cash_accounts ca
                       ,customers     c
                       ,partners      p
                  WHERE ca.account_number = -c.customer_id AND
                        ca.account_number = ca.trading_account_id AND
                        to_number(ca.Refcode) = p.Partner_Id AND
                        p.Company_Id IS NOT NULL AND
                        ca.trading_account_type = 'PARTNER_CASH' AND
                        ca.platform = 'NG' AND
                        c.company_id IS NULL);
    END CASE;

        logger.logger.set_module(NULL);

        RETURN lcuv_result;

    EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_stubbed_ids;

END nrg_cash_account;
/