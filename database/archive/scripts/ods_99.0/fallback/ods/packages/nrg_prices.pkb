CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_prices
as
  -- ===================================================================================
  -- NRG_PRICES
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the prices model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  --  Technical Scenarios to handle data for prices :-
  --
  --  Scenario 1 - The current price snapshot is received - In such a scenario we need to find out the prices that have got updated
  --                                                      and move them to history table and update the base table with the latest
  --                                                      prices. Any price that does not exist on the snapshot and exist on the base table
  --                                                      need to be left on the base table as they are the latest price
  --
  --  Scenario 2 - An older snapshot is received - In case when a older snapshot is received we need to check if the
  --                                            base table contains the price symbol for an older effective start timestamp.
  --                                            If such prices exists then we need to move these prices into history and then update the
  --                                            base table.
  --
  --                                            In case if there are prices on the snapshot that do not exist on the base table
  --                                            then insert the missing prices to the base table to ensure that the latest prices are
  --                                            always there in the base table
  --
  --                                            The rest of the prices on the snapshot which exists on the base table and are
  --                                            older than the ones on the base table needs to be written to the history directly
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     11/11/2011   Paul Flynn        1.0     Creation
  --     14/11/2011   Sanket Mittal     1.1     Created the package body
  --     23/11/2011   Sanket Mittal     1.2     Updated put_prices_set to handle the historical replays
  --     30/12/2011   Sanket Mittal     1.3     Added the change for reporting_date
  --     14/06/2012   Mark Gornicki     1.4     Amended to cope with the breaking change of switching to use Nozomi v4 contract.
  --                                            This involves the acquisition and storage of multiple embedded data arrays
  --                                            for each price symbol.  This has been structured such that NRG will pass each
  --                                            embedded array as a separate table parameter containing the corresponding data for
  --                                            all price symbols i.e. a complete set ready to be processed.
  --                                            In addition many price fields are now obsolete and will no longer be passed,
  --                                            however the underlying tables will still retain these columns for historical purposes.
  --     14/02/2013   Sanket Mittal     1.5     Updated prices structures to reflect version 4.1 nazomi
  --     24/04/2013   Sanket Mittal     1.6     Updated the business date and reporting date logic to calculate on ODS
  --     19/05/2014   Adam Krasnicki    1.7     New procedure put_latest_prices for latest prices only
  --     21/07/2014   Adam Krasnicki    1.8     Business_date and reporting_date added to put_latest_prices procedure
  --     23/07/2014   Adam Krasnicki    1.8     Business_date and reporting_date added to put_latest_prices procedure (set by ODS)
  --     30/10/2014   Adam Krasnicki    1.8     New procedure added - put_anytime_prices
  --     28/07/2015   Adam Krasnicki    1.9     all put% procedure rebuilt, the logic changed--> the old prices go to history, the new are inserted
  --                                            to current table, if the old proces are requested they should go directly to history. The current
  --                                            table should contain only data for the recent business_date
  --     13/05/2016   Sanket Mittal     2.0     Updated prices and prices_h to load all snapshots into prices table
  --     19/12/2016   Sanket Mittal     2.1     BER-3222 NRG_PRICES - deprecated attributes
  --     14/11/2016   Sakina Kinkhabwala 2.2     Updated put_anytime_prices proc to ignore prices with instrument code is '%/CAR'
  --     19/01/2017   Sanket Mittal      2.3     BER-3241 Furai Pricing Contract attributes to be added in new table
  --     09/02/2018   Deepak Rajurkar    2.4     BER-4125 NRG_PRICES - New attribute num_roll_days
  --     19/03/2018   Deepak Rajurkar    2.4     BER-4354:NRG_PRICES - put_anytime_prices - business_date/reporting_date logic
  --     22/04/2020   Patrick Dinwiddy   2.5     JCS-12710
  --     23/06/2020   Patrick Dinwiddy   2.6     JCS-13234
  --     10/07/2020   Patrick Dinwiddy   2.7     JCS-13345
  --     21/08/2020   Patrick Dinwiddy   2.8     JCS-13500 and JCS-13514
  -- ===================================================================================

  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '2.8';
  gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
  gc_true               CONSTANT PLS_INTEGER := 1;
  gc_false              CONSTANT PLS_INTEGER := 0;
  gc_default_timestamp  CONSTANT TIMESTAMP   := TO_TIMESTAMP('01-Jan-1970', 'DD-Mon-YYYY');

  -- ===================================================================================
  -- PRIVATE MODULES
  -- ===================================================================================


  PROCEDURE resync_history_data(p_business_date                 prices.business_date%TYPE,
                                p_reporting_date                prices.reporting_date%TYPE,
                                p_effective_start_timestamp     prices.effective_start_timestamp%TYPE) IS

    lv_snapshot_found      NUMBER := 0;
  BEGIN
    BEGIN
      SELECT 1
      INTO lv_snapshot_found
      FROM prices
      WHERE business_date = p_business_date AND
            reporting_date = p_reporting_date AND
            rownum = 1;
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        lv_snapshot_found := 0;
    END;

    CASE
      WHEN lv_snapshot_found = 0 THEN
        --
        --If snapshot not found then no need to resync
        --
        NULL;
      WHEN lv_snapshot_found = 1 THEN
        --
        --If old snapshot found then clean up child and main tables and prepare them for write
        --

        --
        --price_ask_volumes_h
        --
        --
        --clean up existing history
        --

        DELETE price_ask_volumes_h
        WHERE business_date = p_business_date AND
              reporting_date = p_reporting_date;

        INSERT INTO price_ask_volumes_h(price_symbol,
                                        price_level,
                                        instrument_code,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        effective_end_timestamp,
                                        action,
                                        action_timestamp,
                                        business_date,
                                        reporting_date,
                                        platform,
                                        ask_volume)
                            SELECT  price_symbol,
                                    price_level,
                                    instrument_code,
                                    logical_load_timestamp,
                                    created_by,
                                    create_timestamp,
                                    updated_by,
                                    update_timestamp,
                                    effective_start_timestamp,
                                    p_effective_start_timestamp,
                                    'U',
                                    SYSTIMESTAMP,
                                    business_date,
                                    reporting_date,
                                    platform,
                                    ask_volume
                            FROM  price_ask_volumes
                            WHERE business_date = p_business_date AND
                                  reporting_date = p_reporting_date;

        DELETE price_ask_volumes
        WHERE business_date = p_business_date AND
              reporting_date = p_reporting_date;


        --
        --price_bid_volumes_h
        --

        DELETE price_bid_volumes_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO price_bid_volumes_h (price_symbol,
                                        price_level,
                                        instrument_code,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        effective_end_timestamp,
                                        action,
                                        action_timestamp,
                                        business_date,
                                        reporting_date,
                                        platform,
                                        bid_volume)
        SELECT t.price_symbol,
             t.price_level,
             t.instrument_code,
             t.logical_load_timestamp,
             t.created_by,
             t.create_timestamp,
             t.updated_by,
             t.update_timestamp,
             t.effective_start_timestamp,
             p_effective_start_timestamp,
             'U',
             SYSTIMESTAMP,
             t.business_date,
             t.reporting_date,
             t.platform,
             t.bid_volume
         FROM price_bid_volumes t
         WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        DELETE price_bid_volumes t
         WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        --
        --price_core_ask_prices_h
        --

        DELETE price_core_ask_prices_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO price_core_ask_prices_h(price_symbol,
                                            price_level,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            effective_end_timestamp,
                                            action,
                                            action_timestamp,
                                            business_date,
                                            reporting_date,
                                            platform,
                                            ask_price,
                                            instrument_code)
        SELECT t.price_symbol,
             t.price_level,
             t.logical_load_timestamp,
             t.created_by,
             t.create_timestamp,
             t.updated_by,
             t.update_timestamp,
             t.effective_start_timestamp,
             p_effective_start_timestamp,
             'D',
             SYSTIMESTAMP,
             t.business_date,
             t.reporting_date,
             t.platform,
             t.ask_price,
             t.instrument_code
         FROM price_core_ask_prices t
         WHERE business_date = p_business_date AND
               reporting_date = p_reporting_date;

        DELETE price_core_ask_prices t
         WHERE business_date = p_business_date AND
               reporting_date = p_reporting_date;

        --
        --price_core_bid_prices_h
        --

        DELETE price_core_bid_prices_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO price_core_bid_prices_h(price_symbol,
                                            price_level,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            effective_end_timestamp,
                                            action,
                                            action_timestamp,
                                            business_date,
                                            reporting_date,
                                            platform,
                                            bid_price,
                                            instrument_code)
        SELECT t.price_symbol,
                 t.price_level,
                 t.logical_load_timestamp,
                 t.created_by,
                 t.create_timestamp,
                 t.updated_by,
                 t.update_timestamp,
                 t.effective_start_timestamp,
                 p_effective_start_timestamp,
                 'D',
                 SYSTIMESTAMP,
                 t.business_date,
                 t.reporting_date,
                 t.platform,
                 t.bid_price,
                 t.instrument_code
             FROM price_core_bid_prices t
             WHERE business_date = p_business_date AND
                   reporting_date = p_reporting_date;

        DELETE price_core_bid_prices t
        WHERE business_date = p_business_date AND
              reporting_date = p_reporting_date;

        --
        --price_core_ohlc_h
        --

        DELETE price_core_ohlc_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO price_core_ohlc_h (price_symbol,
                                       ohlc_type,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       effective_end_timestamp,
                                       action,
                                       action_timestamp,
                                       business_date,
                                       reporting_date,
                                       platform,
                                       price,
                                       instrument_code)
            SELECT t.price_symbol,
                   t.ohlc_type,
                   t.logical_load_timestamp,
                   t.created_by,
                   t.create_timestamp,
                   t.updated_by,
                   t.update_timestamp,
                   t.effective_start_timestamp,
                   p_effective_start_timestamp,
                   'D',
                   SYSTIMESTAMP,
                   t.business_date,
                   t.reporting_date,
                   t.platform,
                   t.price,
                   t.instrument_code
            FROM price_core_ohlc t
            WHERE business_date = p_business_date AND
                  reporting_date = p_reporting_date;

        DELETE price_core_ohlc
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        --
        --price_ask_offsets_h
        --

        DELETE price_ask_offsets_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO price_ask_offsets_h(price_symbol,
                                        bucket,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        effective_end_timestamp,
                                        action,
                                        action_timestamp,
                                        business_date,
                                        reporting_date,
                                        platform,
                                        offset_ask_price,
                                        instrument_code)
        SELECT t.price_symbol,
                   t.bucket,
                   t.logical_load_timestamp,
                   t.created_by,
                   t.create_timestamp,
                   t.updated_by,
                   t.update_timestamp,
                   t.effective_start_timestamp,
                   p_effective_start_timestamp,
                   'D',
                   SYSTIMESTAMP,
                   t.business_date,
                   t.reporting_date,
                   t.platform,
                   t.offset_ask_price,
                   t.instrument_code
        FROM price_ask_offsets t
        WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        DELETE price_ask_offsets
        WHERE business_date = p_business_date AND
              reporting_date = p_reporting_date;

        --
        --price_bid_offsets_h
        --

        DELETE price_bid_offsets_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO price_bid_offsets_h(price_symbol,
                                        bucket,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        effective_end_timestamp,
                                        action,
                                        action_timestamp,
                                        business_date,
                                        reporting_date,
                                        platform,
                                        offset_bid_price,
                                        instrument_code)
        SELECT  t.price_symbol,
                 t.bucket,
                 t.logical_load_timestamp,
                 t.created_by,
                 t.create_timestamp,
                 t.updated_by,
                 t.update_timestamp,
                 t.effective_start_timestamp,
                 p_effective_start_timestamp,
                 'D',
                 SYSTIMESTAMP,
                 t.business_date,
                 t.reporting_date,
                 t.platform,
                 t.offset_bid_price,
                 t.instrument_code
             FROM price_bid_offsets t
             WHERE business_date = p_business_date AND
                   reporting_date = p_reporting_date;

        DELETE price_bid_offsets
             WHERE business_date = p_business_date AND
                   reporting_date = p_reporting_date;

        --
        --Prices
        --

        DELETE prices_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO prices_h(price_symbol,
                             logical_load_timestamp,
                             created_by,
                             create_timestamp,
                             updated_by,
                             update_timestamp,
                             effective_start_timestamp,
                             effective_end_timestamp,
                             action,
                             action_timestamp,
                             instrument_code,
                             request_time,
                             business_date,
                             reporting_date,
                             platform,
                             instrument,
                             instrument_generation,
                             quote_time,
                             quote_id,
                             quote_sequence_id,
                             quote_status,
                             source_price_time,
                             is_tradable,
                             is_corporate_actions,
                             is_first_good_in_continuous,
                             is_increasing_long_allowed,
                             is_increasing_short_allowed,
                             bid_price,
                             ask_price,
                             mm_instrument_id,
                             is_tradable_instrument,
                             mid_price,
                             price_exponent,
                             underlying_bid_price,
                             underlying_ask_price,
                             rollover_bid_spread,
                             rollover_ask_spread,
                             volatility,
                             performance,
                             settlement_ask_price,
                             settlement_bid_price,
                             num_roll_days,
                             instrument_code_ps,
                             stream_code,
                             stream_type_code,
                             primary_stream_code,
                             is_primary_stream,
                             mid_px_adjustment_ids,
                             mid_px_adjustments)
        SELECT new_version.price_symbol,
               new_version.logical_load_timestamp,
               new_version.created_by,
               new_version.create_timestamp,
               new_version.updated_by,
               new_version.update_timestamp,
               new_version.effective_start_timestamp,
               p_effective_start_timestamp,
               'U',
               SYSTIMESTAMP,
               new_version.instrument_code,
               new_version.request_time,
               new_version.business_date,
               new_version.reporting_date,
               new_version.platform,
               new_version.instrument,
               new_version.instrument_generation,
               new_version.quote_time,
               new_version.quote_id,
               new_version.quote_sequence_id,
               new_version.quote_status,
               new_version.source_price_time,
               new_version.is_tradable,
               new_version.is_corporate_actions,
               new_version.is_first_good_in_continuous,
               new_version.is_increasing_long_allowed,
               new_version.is_increasing_short_allowed,
               new_version.bid_price,
               new_version.ask_price,
               new_version.mm_instrument_id,
               new_version.is_tradable_instrument,
               new_version.mid_price,
               new_version.price_exponent,
               new_version.underlying_bid_price,
               new_version.underlying_ask_price,
               new_version.rollover_bid_spread,
               new_version.rollover_ask_spread,
               new_version.volatility,
               new_version.performance,
               new_version.settlement_ask_price,
               new_version.settlement_bid_price,
               new_version.num_roll_days,
               new_version.instrument_code_ps,
               new_version.stream_code,
               new_version.stream_type_code,
               new_version.primary_stream_code,
               new_version.is_primary_stream,
               new_version.mid_px_adjustment_ids,
               new_version.mid_px_adjustments
        FROM prices new_version
        WHERE business_date = p_business_date AND
              reporting_date = p_reporting_date;

        DELETE prices
        WHERE business_date = p_business_date AND
              reporting_date = p_reporting_date;

        ----------------
        -- prices_fxr --
        ----------------

        DELETE prices_fxr_h
          WHERE business_date = p_business_date AND
                reporting_date = p_reporting_date;

        INSERT INTO prices_fxr_h
                    (price_symbol,
                     logical_load_timestamp,
                     created_by,
                     create_timestamp,
                     updated_by,
                     update_timestamp,
                     effective_start_timestamp,
                     effective_end_timestamp,
                     action,
                     action_timestamp,
                     instrument_code,
                     business_date,
                     reporting_date,
                     from_ccy,
                     to_ccy,
                     band,
                     bid_price,
                     mid_price,
                     ask_price,
                     reval_rate)
              SELECT price_symbol,
                     logical_load_timestamp,
                     created_by,
                     create_timestamp,
                     updated_by,
                     update_timestamp,
                     effective_start_timestamp,
                     p_effective_start_timestamp,
                     'D',
                     SYSTIMESTAMP,
                     instrument_code,
                     business_date,
                     reporting_date,
                     from_ccy,
                     to_ccy,
                     band,
                     bid_price,
                     mid_price,
                     ask_price,
                     reval_rate
                FROM prices_fxr
               WHERE business_date = p_business_date
                 AND reporting_date = p_reporting_date;

              DELETE prices_fxr
               WHERE business_date = p_business_date
                 AND reporting_date = p_reporting_date;

    END CASE;

  END;

    -- ===================================================================================
  -- put_prices_fxr
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the FXR reval rates into the PRICES_FXR ODS table
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     PRICES_FXR
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_prices_fxr (p_user                          IN prices_fxr.created_by%TYPE,
                            p_effective_start_timestamp     IN prices_fxr.effective_start_timestamp%TYPE,
                            p_business_date                 IN prices_fxr.business_date%TYPE,
                            p_reporting_date                IN prices_fxr.reporting_date%TYPE) IS

  BEGIN

    INSERT INTO prices_fxr
                (price_symbol,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp,
                 instrument_code,
                 business_date,
                 reporting_date,
                 from_ccy,
                 to_ccy,
                 band,
                 bid_price,
                 mid_price,
                 ask_price,
                 reval_rate)
         (SELECT price_symbol,
                 systimestamp                         AS logical_load_timestamp,
                 p_user                               AS created_by,
                 SYSTIMESTAMP                         AS create_timestamp,
                 p_user                               AS updated_by,
                 SYSTIMESTAMP                         AS update_timestamp,
                 p_effective_start_timestamp          AS effective_start_timestamp,
                 instrument_code,
                 business_date,
                 reporting_date,
                 from_ccy,
                 to_ccy,
                 band,
                 bid_price,
                 mid_price,
                 ask_price,
                 reval_rate
            FROM (WITH
                  w_dates AS
                  (
                  SELECT /*snapshot_time AS snapshot_time,
                               TRUNC(snapshot_time) AS business_date,
                               bi_utils.transform_functions_pkg.get_reporting_date(p_date_utc => TRUNC(snapshot_time)) AS reporting_date
                          FROM (SELECT to_timestamp('18-APR-2020 21:00:00','dd-mon-yyyy hh24:mi:ss') AS snapshot_time FROM dual)*/
                        /* p_effective_start_timestamp AS snapshot_time,
                               p_business_date             AS business_date,
                               p_reporting_date            AS reporting_date  FROM dual*/
                               snapshot_time AS snapshot_time,
                               TRUNC(snapshot_time) AS business_date,
                               nrg_common.get_reporting_date(p_date_utc => TRUNC(snapshot_time)) AS reporting_date
                          FROM (SELECT p_effective_start_timestamp AS snapshot_time FROM dual)

                  ),
                  w_ifs_history AS
                  (
                        SELECT ifs.instrument_code, ifs.feed_symbol, ifs.feed_type, ifs.effective_start_timestamp, SYSDATE+1 AS effective_end_timestamp
                          FROM bi_ods.instrument_feed_settings ifs
                         WHERE ifs.feed_type = 'FxRevaluationRate'
                           AND ifs.is_deleted = 'NO'
                     UNION ALL
                        SELECT ifs.instrument_code, ifs.feed_symbol, ifs.feed_type, ifs.effective_start_timestamp, ifs.effective_end_timestamp
                          FROM bi_ods.instrument_feed_settings_h ifs
                         WHERE ifs.feed_type = 'FxRevaluationRate'
                           AND ifs.is_deleted = 'NO'
                  ),
                  w_eod_core_reval_sub AS
                  (
                        SELECT pr.business_date,
                               pr.reporting_date,
                               d.snapshot_time,
                               pr.price_symbol,
                               pr.instrument_code,
                               i.pair_currency,
                               i.currency,
                               bands.band,
                               pr.bid_price,
                               pr.ask_price
                          FROM w_ifs_history ifs
                          JOIN w_dates d
                            ON d.snapshot_time >= ifs.effective_start_timestamp
                           AND d.snapshot_time <  ifs.effective_end_timestamp
                          JOIN bi_ods.instruments i
                            ON ifs.instrument_code = i.instrument_code
                          JOIN bi_ods.prices pr
                            ON ifs.feed_symbol = pr.price_symbol
                          JOIN (  SELECT 'Band'||to_char(ROWNUM) AS band, ROWNUM AS band_sort_order FROM dual CONNECT BY ROWNUM <= 10
                                   UNION ALL
                                  SELECT 'CMC-Standard'  AS band, 0 AS band_sort_order FROM dual
                               ) bands
                            ON 1=1
                         WHERE i.feed_symbol IS NOT NULL
                           AND i.pair_currency IS NOT NULL
                           AND pr.business_date = d.business_date
                           AND pr.reporting_date = d.reporting_date
                  ),
                  w_eod_core_revals AS
                  (
                        SELECT r.business_date,
                               r.reporting_date,
                               r.snapshot_time AS effective_start_timestamp,
                               r.price_symbol,
                               r.instrument_code,
                               r.pair_currency as from_ccy,
                               r.currency as to_ccy,
                               r.band,
                               NVL(r.bid_price + pbo.offset_bid_price,r.bid_price) AS bid_price,
                               (NVL(r.bid_price + pbo.offset_bid_price,r.bid_price) + NVL(r.ask_price + pao.offset_ask_price,r.ask_price)) / 2 AS mid_price,
                               NVL(r.ask_price + pao.offset_ask_price,r.ask_price) AS ask_price,
                               (NVL(r.bid_price + pbo.offset_bid_price,r.bid_price) + NVL(r.ask_price + pao.offset_ask_price,r.ask_price)) / 2 AS reval_rate
                          FROM w_eod_core_reval_sub r
                     LEFT JOIN bi_ods.price_bid_offsets pbo
                            ON r.reporting_date = pbo.reporting_date
                           AND r.business_date = pbo.business_date
                           AND r.price_symbol = pbo.price_symbol
                           AND r.instrument_code = pbo.instrument_code
                           AND r.band = 'Band'||to_char(pbo.bucket)
                     LEFT JOIN bi_ods.price_ask_offsets pao
                            ON r.reporting_date = pao.reporting_date
                           AND r.business_date = pao.business_date
                           AND r.price_symbol = pao.price_symbol
                           AND r.instrument_code = pao.instrument_code
                           AND r.band = 'Band'||to_char(pao.bucket)
                  )
                        SELECT * FROM w_eod_core_revals)) ;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_prices_fxr;

  -- ===================================================================================
  -- put_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the NG-QUOTE prices array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     PRICES
  --     PRICES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_prices                         Table of Prices for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_prices (p_user                          IN prices.created_by%TYPE,
                        p_logical_load_timestamp        IN prices.logical_load_timestamp%TYPE,
                        p_effective_start_timestamp     IN prices.effective_start_timestamp%TYPE,
                        p_prices                        IN price_tab,
                        p_business_date                 IN prices.business_date%TYPE,
                        p_reporting_date                IN prices.reporting_date%TYPE
                       )
  IS

    TYPE ltyp_old_prices IS TABLE OF prices%ROWTYPE;
    lv_old_prices                     ltyp_old_prices;
    lv_insert_update_price            price_tab;
    lv_effective_start_timestamp      prices.effective_start_timestamp%TYPE;
    lv_max_business_date              prices.Business_Date%TYPE;
    lv_current_business_date          prices.Business_Date%TYPE;

    lv_snapshot_found                 NUMBER := 0;
  BEGIN
    logger.logger.set_module('put_prices');

    --
    --Create Instrument Stubs
    --

    FOR lc_instruments IN (SELECT DISTINCT instrument_code
                           FROM TABLE(CAST(p_prices AS price_tab))) LOOP
      nrg_products.create_instrument_stub (p_user                       => p_user,
                                           p_logical_load_timestamp     => p_logical_load_timestamp,
                                           p_effective_start_timestamp  => p_effective_start_timestamp,
                                           p_instrument_code            => lc_instruments.instrument_code,
                                           p_product_platform           => NULL,
                                           p_product_wrapper            => NULL,
                                           p_mm_instrument_id           => NULL);

    END LOOP;

    --SELECT MAX(business_date)
    --INTO   lv_max_business_date
    --FROM  prices;



    INSERT INTO prices(price_symbol,
                       logical_load_timestamp,
                       created_by,
                       create_timestamp,
                       updated_by,
                       update_timestamp,
                       effective_start_timestamp,
                       instrument_code,
                       request_time,
                       business_date,
                       reporting_date,
                       platform,
                       instrument,
                       instrument_generation,
                       quote_time,
                       quote_id,
                       quote_sequence_id,
                       quote_status,
                       source_price_time,
                       is_tradable,
                       is_corporate_actions,
                       is_first_good_in_continuous,
                       is_increasing_long_allowed,
                       is_increasing_short_allowed,
                       bid_price,
                       ask_price,
                       mm_instrument_id,
                       is_tradable_instrument,
                       mid_price,
                       price_exponent,
                       underlying_bid_price,
                       underlying_ask_price,
                       rollover_bid_spread,
                       rollover_ask_spread,
                       volatility,
                       performance,
                       settlement_ask_price,
                       settlement_bid_price,
                       num_roll_days,
                       instrument_code_ps,
                       stream_code,
                       stream_type_code,
                       primary_stream_code,
                       is_primary_stream,
                       mid_px_adjustment_ids,
                       mid_px_adjustments)
        SELECT table_snapshot.price_symbol          AS price_symbol ,
                 p_logical_load_timestamp             AS logical_load_timestamp ,
                 p_user                               AS created_by,
                 SYSTIMESTAMP                         AS create_timestamp,
                 p_user                               AS updated_by,
                 SYSTIMESTAMP                         AS update_timestamp,
                 p_effective_start_timestamp          AS effective_start_timestamp,
                 table_snapshot.instrument_code       AS instrument_code,
                 table_snapshot.request_time          AS request_time,
                 trunc(p_effective_start_timestamp)   AS business_date,
                 nrg_common.get_reporting_date(trunc(p_effective_start_timestamp)) AS reporting_date,
                 table_snapshot.platform              AS platform,
                 table_snapshot.instrument            AS instrument,
                 table_snapshot.instrument_generation AS instrument_generation,
                 table_snapshot.quote_time            AS quote_time,
                 table_snapshot.quote_id              AS quote_id,
                 table_snapshot.quote_sequence_id     AS quote_sequence_id,
                 table_snapshot.quote_status          AS quote_status,
                 table_snapshot.source_price_time     AS source_price_time,
                 table_snapshot.is_tradable           AS is_tradable,
                 table_snapshot.is_ca_quote           AS is_corporate_actions,
                 table_snapshot.is_first_good_in_continuous AS is_first_good_in_continuous,
                 table_snapshot.is_increasing_long_allowed  AS is_increasing_long_allowed,
                 table_snapshot.is_increasing_short_allowed AS is_increasing_short_allowed,
                 table_snapshot.bid_price                   AS bid_price,
                 table_snapshot.ask_price                   AS ask_price,
                 table_snapshot.mm_instrument_id            AS mm_instrument_id,
                 table_snapshot.is_tradable_instrument      AS is_tradable_instrument,
                 table_snapshot.mid_price                   AS mid_price,
                 table_snapshot.price_exponent              AS price_exponent,
                 table_snapshot.underlying_bid_price        AS underlying_bid_price,
                 table_snapshot.underlying_ask_price        AS underlying_ask_price,
                 table_snapshot.rollover_bid_spread         AS rollover_bid_spread,
                 table_snapshot.rollover_ask_spread         AS rollover_ask_spread,
                 table_snapshot.volatility                  AS volatility,
                 table_snapshot.performance                 AS performance,
                 table_snapshot.settlement_ask_price        AS settlement_ask_price,
                 table_snapshot.settlement_bid_price        AS settlement_bid_price,
                 table_snapshot.num_roll_days               AS num_roll_days,
                 table_snapshot.instrument_code_ps          AS instrument_code_ps,
                 table_snapshot.stream_code                 AS stream_code,
                 table_snapshot.stream_type_code            AS stream_type_code,
                 table_snapshot.primary_stream_code         AS primary_stream_code,
                 table_snapshot.is_primary_stream           AS is_primary_stream,
                 table_snapshot.mid_px_adjustment_ids       AS mid_px_adjustment_ids,
                 table_snapshot.mid_px_adjustments          AS mid_px_adjustments

        FROM TABLE(CAST(p_prices AS price_tab)) table_snapshot;



  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_prices;

  -- ===================================================================================
  -- put_ask_volumes
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_ask_volumes array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_ask_volumes
  --     price_ask_volumes_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_ask_volumes              Table of Price Ask Volumes for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_ask_volumes (p_user                          IN price_ask_volumes.created_by%TYPE,
                             p_logical_load_timestamp        IN price_ask_volumes.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp     IN price_ask_volumes.effective_start_timestamp%TYPE,
                             p_ask_volumes                   IN price_array_tab,
                             p_business_date                 IN price_ask_volumes.business_date%TYPE,
                             p_reporting_date                IN price_ask_volumes.reporting_date%TYPE)
  IS

    lv_found                    NUMBER := 0;

  BEGIN
    logger.logger.set_module('put_ask_volumes');


    INSERT INTO price_ask_volumes
                 (price_symbol,
                  price_level,
                  instrument_code,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  business_date,
                  reporting_date,
                  platform,
                  ask_volume)
          SELECT new_version.price_symbol,
                 new_version.level_id,
                 new_version.instrument_code,
                 p_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp,
                 p_business_date,
                 p_reporting_date,
                 new_version.platform,
                 new_version.value
          FROM TABLE(CAST(p_ask_volumes AS price_array_tab)) new_version;


  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_ask_volumes;

  -- ===================================================================================
  -- put_bid_volumes
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_bid_volumes array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_bid_volumes
  --     price_bid_volumes_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_bid_volumes              Table of Price Bid Volumes for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_bid_volumes (p_user                          IN price_bid_volumes.created_by%TYPE,
                             p_logical_load_timestamp        IN price_bid_volumes.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp     IN price_bid_volumes.effective_start_timestamp%TYPE,
                             p_bid_volumes                   IN price_array_tab,
                             p_business_date                 IN price_bid_volumes.business_date%TYPE,
                             p_reporting_date                IN price_bid_volumes.reporting_date%TYPE)
  IS

    lv_found        NUMBER := 0;

  BEGIN
    logger.logger.set_module('put_bid_volumes');


    INSERT INTO price_bid_volumes (price_symbol,
                                   instrument_code,
                                   price_level,
                                   logical_load_timestamp,
                                   created_by,
                                   create_timestamp,
                                   updated_by,
                                   update_timestamp,
                                   effective_start_timestamp,
                                   business_date,
                                   reporting_date,
                                   platform,
                                   bid_volume)
                            SELECT new_version.price_symbol,
                                   new_version.instrument_code,
                                   new_version.level_id,
                                   p_logical_load_timestamp,
                                   p_user,
                                   SYSTIMESTAMP,
                                   p_user,
                                   SYSTIMESTAMP,
                                   p_effective_start_timestamp,
                                   p_business_date,
                                   p_reporting_date,
                                   new_version.platform,
                                   new_version.value
                            FROM TABLE(CAST(p_bid_volumes AS price_array_tab)) new_version;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_bid_volumes;

  -- ===================================================================================
  -- put_core_ask_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_rask_price_levels array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_core_ask_prices
  --     price_core_ask_prices_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_core_ask_prices        Table of Ask Retail Price Levels for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_core_ask_prices (p_user                          IN price_core_ask_prices.created_by%TYPE,
                                 p_logical_load_timestamp        IN price_core_ask_prices.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp     IN price_core_ask_prices.effective_start_timestamp%TYPE,
                                 p_core_ask_prices               IN price_array_tab,
                                 p_business_date                 IN price_core_ask_prices.business_date%TYPE,
                                 p_reporting_date                IN price_core_ask_prices.reporting_date%TYPE)
  IS

    lv_found      NUMBER := 0;
  BEGIN
    logger.logger.set_module('put_core_ask_prices');


    INSERT INTO price_core_ask_prices (price_symbol,
                                       instrument_code,
                                       price_level,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       business_date,
                                       reporting_date,
                                       platform,
                                       ask_price)
                               SELECT  new_version.price_symbol,
                                       new_version.instrument_code,
                                       new_version.level_id,
                                       p_logical_load_timestamp,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_effective_start_timestamp,
                                       p_business_date,
                                       p_reporting_date,
                                       new_version.platform,
                                       new_version.value
                                FROM TABLE(CAST(p_core_ask_prices AS price_array_tab)) new_version;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_core_ask_prices;

  -- ===================================================================================
  -- put_core_bid_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_rbid_price_levels array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_core_bid_prices
  --     price_core_bid_prices_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_core_bid_prices        Table of Bid Retail Price Levels for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_core_bid_prices (p_user                          IN price_core_bid_prices.created_by%TYPE,
                                 p_logical_load_timestamp        IN price_core_bid_prices.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp     IN price_core_bid_prices.effective_start_timestamp%TYPE,
                                 p_core_bid_prices               IN price_array_tab,
                                 p_business_date                 IN price_core_bid_prices.business_date%TYPE,
                                 p_reporting_date                IN price_core_bid_prices.reporting_date%TYPE)
  IS

    lv_found      NUMBER := 0;

  BEGIN
    logger.logger.set_module('put_core_bid_prices');


    INSERT INTO price_core_bid_prices(price_symbol,
                                      instrument_code,
                                      price_level,
                                      logical_load_timestamp,
                                      created_by,
                                      create_timestamp,
                                      updated_by,
                                      update_timestamp,
                                      effective_start_timestamp,
                                      business_date,
                                      reporting_date,
                                      platform,
                                      bid_price)
                               SELECT new_version.price_symbol,
                                       new_version.instrument_code,
                                       new_version.level_id,
                                       p_logical_load_timestamp,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_effective_start_timestamp,
                                       p_business_date,
                                       p_reporting_date,
                                       new_version.platform,
                                       new_version.value
                              FROM TABLE(CAST(p_core_bid_prices AS price_array_tab)) new_version;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_core_bid_prices;

  -- ===================================================================================
  -- put_core_ohlc
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_retail_ohlc array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_core_ohlc
  --     price_core_ohlc_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_core_ohlc              Table of Retail Prices OpenHighLowClose Values for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_core_ohlc (p_user                          IN price_core_ohlc.created_by%TYPE,
                           p_logical_load_timestamp        IN price_core_ohlc.logical_load_timestamp%TYPE,
                           p_effective_start_timestamp     IN price_core_ohlc.effective_start_timestamp%TYPE,
                           p_core_ohlc                     IN price_array_tab,
                           p_business_date                 IN price_core_ohlc.business_date%TYPE,
                           p_reporting_date                IN price_core_ohlc.reporting_date%TYPE)
  IS

    lv_found    NUMBER := 0;

  BEGIN
    logger.logger.set_module('put_core_ohlc');


    INSERT INTO price_core_ohlc(price_symbol,
                                instrument_code,
                                ohlc_type,
                                logical_load_timestamp,
                                created_by,
                                create_timestamp,
                                updated_by,
                                update_timestamp,
                                effective_start_timestamp,
                                business_date,
                                reporting_date,
                                platform,
                                price)
                         SELECT new_version.price_symbol,
                                 new_version.instrument_code,
                                 new_version.level_desc,
                                 p_logical_load_timestamp,
                                 p_user,
                                 SYSTIMESTAMP,
                                 p_user,
                                 SYSTIMESTAMP,
                                 p_effective_start_timestamp,
                                 p_business_date,
                                 p_reporting_date,
                                 new_version.platform,
                                 new_version.value
                          FROM TABLE(CAST(p_core_ohlc AS price_array_tab)) new_version;


  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_core_ohlc;

  -- ===================================================================================
  -- put_ask_offsets
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_ask_offsets array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_ask_offsets
  --     price_ask_offsets_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_ask_offsets              Table of Price Ask Offsets for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_ask_offsets (p_user                          IN price_ask_offsets.created_by%TYPE,
                             p_logical_load_timestamp        IN price_ask_offsets.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp     IN price_ask_offsets.effective_start_timestamp%TYPE,
                             p_ask_offsets                   IN price_array_tab,
                             p_business_date                 IN price_ask_offsets.business_date%TYPE,
                             p_reporting_date                IN price_ask_offsets.reporting_date%TYPE)
  IS

    lv_found    NUMBER := 0;

  BEGIN
    logger.logger.set_module('put_ask_offsets');


    INSERT INTO price_ask_offsets(price_symbol,
                                  instrument_code,
                                  bucket,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  effective_start_timestamp,
                                  business_date,
                                  reporting_date,
                                  platform,
                                  offset_ask_price)
                           SELECT new_version.price_symbol,
                                  new_version.instrument_code,
                                  new_version.level_id,
                                  p_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_effective_start_timestamp,
                                  p_business_date,
                                  p_reporting_date,
                                  new_version.platform,
                                  new_version.value
                          FROM TABLE(CAST(p_ask_offsets AS price_array_tab)) new_version;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_ask_offsets;

  -- ===================================================================================
  -- put_bid_offsets
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the price_bid_offsets array into the corresponding ODS tables
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_bid_offsets
  --     price_bid_offsets_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_bid_offsets              Table of Price Bid Volumes for MM and NG
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_bid_offsets (p_user                          IN price_bid_offsets.created_by%TYPE,
                             p_logical_load_timestamp        IN price_bid_offsets.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp     IN price_bid_offsets.effective_start_timestamp%TYPE,
                             p_bid_offsets                   IN price_array_tab,
                             p_business_date                 IN price_bid_offsets.business_date%TYPE,
                             p_reporting_date                IN price_bid_offsets.reporting_date%TYPE)
  IS

    lv_found    NUMBER := 0;

  BEGIN
    logger.logger.set_module('put_bid_offsets');


    INSERT INTO price_bid_offsets(price_symbol,
                                  instrument_code,
                                  bucket,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  effective_start_timestamp,
                                  business_date,
                                  reporting_date,
                                  platform,
                                  offset_bid_price)
                           SELECT new_version.price_symbol,
                                  new_version.instrument_code,
                                  new_version.level_id,
                                  p_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_effective_start_timestamp,
                                  p_business_date,
                                  p_reporting_date,
                                  new_version.platform,
                                  new_version.value
                          FROM TABLE(CAST(p_bid_offsets AS price_array_tab)) new_version;

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_bid_offsets;


  -- ===================================================================================
  -- PUBLIC MODULES
  -- ===================================================================================


  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------
   FUNCTION version
      RETURN VARCHAR2 DETERMINISTIC IS

  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END version;

  -- ===================================================================================
  -- put_price_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price stub
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     PRICES
  --
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_price_symbol                   Price Symbol
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_price_stub    (p_user                          IN prices.created_by%TYPE,
                               p_effective_start_timestamp     IN prices.effective_start_timestamp%TYPE,
                               p_logical_load_timestamp        IN prices.logical_load_timestamp%TYPE,
                               p_price_symbol                  IN prices.price_symbol%TYPE
                               )
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN

    INSERT INTO prices (price_symbol,
                        logical_load_timestamp,
                        created_by,
                        create_timestamp,
                        updated_by,
                        update_timestamp,
                        effective_start_timestamp)
                VALUES (p_price_symbol,
                        p_logical_load_timestamp,
                        p_user,
                        SYSTIMESTAMP,
                        p_user,
                        SYSTIMESTAMP,
                        gc_default_timestamp);
    COMMIT;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      --
      --Since the price already exists no need to insert the stub
      --
      NULL;
  END put_price_stub;

  -- ===================================================================================
  -- put_price_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     PRICES
  --     PRICES_H
  --     price_ask_offsets
  --     price_ask_offsets_H
  --     price_ask_volumes
  --     price_ask_volumes_H
  --     price_bid_offsets
  --     price_bid_offsets_H
  --     price_bid_volumes
  --     price_bid_volumes_H
  --     price_core_ask_prices
  --     price_core_ask_prices_H
  --     price_core_bid_prices
  --     price_core_bid_prices_H
  --     price_core_ohlc
  --     price_core_ohlc_H
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_prices                         Table of Prices for MM and NG
  --     p_ask_volumes              Table of Core Order Book Ask Volumes
  --     p_bid_volumes              Table of Core Order Book Bid Volumes
  --     p_core_ask_prices        Table of Core Order Book Retail Ask Price Levels
  --     p_core_bid_prices        Table of Core Order Book Retail Bid Price Levels
  --     p_core_ohlc              Table of Core Order Book Retail Open High Low Close Prices
  --     p_ask_offsets              Table of Ask Price Offsets, 5 buckets supported. First entry = SB, others prepared for partners
  --     p_bid_offsets              Table of Ask Price Offsets, 5 buckets supported. First entry = SB, others prepared for partners
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_prices_set(p_user                          IN prices.created_by%TYPE,
                           p_effective_start_timestamp     IN prices.effective_start_timestamp%TYPE,
                           p_prices                        IN price_tab,
                           p_ask_volumes                   IN price_array_tab,
                           p_bid_volumes                   IN price_array_tab,
                           p_core_ask_prices               IN price_array_tab,
                           p_core_bid_prices               IN price_array_tab,
                           p_core_ohlc                     IN price_array_tab,
                           p_ask_offsets                   IN price_array_tab,
                           p_bid_offsets                   IN price_array_tab)
  IS

    lv_logical_load_timestamp prices.logical_load_timestamp%TYPE;
    lv_business_date          prices.business_date%TYPE;
    lv_reporting_date         prices.reporting_date%TYPE;

    lv_has_snapshot_changed     NUMBER;

    lex_no_changes_found        EXCEPTION;

  BEGIN
    logger.logger.set_module('put_price_set');

    -- set the logical load timestamp to now
    lv_logical_load_timestamp := SYSTIMESTAMP;

    --
    --Get Business date
    --

    lv_business_date := trunc(p_effective_start_timestamp);

    --
    --Get Reporting Date
    --

    IF UPPER(TRIM(TO_CHAR(lv_business_date, 'DAY'))) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF UPPER(TRIM(TO_CHAR(lv_business_date, 'DAY'))) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

    --
    --Compare existing sanpshot and new incoming snapshot
    --

    SELECT count(*)
    INTO lv_has_snapshot_changed
    FROM (SELECT *
          FROM prices
          WHERE business_date = lv_business_date AND
                reporting_date = lv_reporting_date)existing FULL OUTER JOIN
         TABLE(CAST(p_prices AS price_tab)) NEW ON (existing.price_symbol = NEW.price_symbol AND
                                                    existing.instrument_code = NEW.instrument_code)
    WHERE (nrg_common.has_value_changed(existing.request_time,NEW.request_time)                               = 1 OR
           nrg_common.has_value_changed(existing.platform,NEW.platform)                                       = 1 OR
           nrg_common.has_value_changed(existing.instrument,NEW.instrument)                                   = 1 OR
           nrg_common.has_value_changed(existing.instrument_generation,NEW.instrument_generation)             = 1 OR
           nrg_common.has_value_changed(existing.quote_time,NEW.quote_time)                                   = 1 OR
           nrg_common.has_value_changed(existing.quote_id,NEW.quote_id)                                       = 1 OR
           nrg_common.has_value_changed(existing.quote_sequence_id,NEW.quote_sequence_id)                     = 1 OR
           nrg_common.has_value_changed(existing.quote_status,NEW.quote_status)                               = 1 OR
           nrg_common.has_value_changed(existing.source_price_time,NEW.source_price_time)                     = 1 OR
           nrg_common.has_value_changed(existing.is_tradable,NEW.is_tradable)                                 = 1 OR
           nrg_common.has_value_changed(existing.is_corporate_actions,NEW.is_ca_quote)                        = 1 OR
           nrg_common.has_value_changed(existing.is_first_good_in_continuous,NEW.is_first_good_in_continuous) = 1 OR
           nrg_common.has_value_changed(existing.is_increasing_long_allowed,NEW.is_increasing_long_allowed)   = 1 OR
           nrg_common.has_value_changed(existing.is_increasing_short_allowed,NEW.is_increasing_short_allowed) = 1 OR
           nrg_common.has_value_changed(existing.bid_price,NEW.bid_price)                                     = 1 OR
           nrg_common.has_value_changed(existing.ask_price,NEW.ask_price)                                     = 1 OR
           nrg_common.has_value_changed(existing.mm_instrument_id,NEW.mm_instrument_id)                       = 1 OR
           nrg_common.has_value_changed(existing.is_tradable_instrument,NEW.is_tradable_instrument)           = 1 OR
           nrg_common.has_value_changed(existing.mid_price,NEW.mid_price)                                     = 1 OR
           nrg_common.has_value_changed(existing.price_exponent,NEW.price_exponent)                           = 1 OR
           nrg_common.has_value_changed(existing.underlying_bid_price,NEW.underlying_bid_price)               = 1 OR
           nrg_common.has_value_changed(existing.underlying_ask_price,NEW.underlying_ask_price)               = 1 OR
           nrg_common.has_value_changed(existing.rollover_bid_spread,NEW.rollover_bid_spread)                 = 1 OR
           nrg_common.has_value_changed(existing.rollover_ask_spread,NEW.rollover_ask_spread)                 = 1 OR
           nrg_common.has_value_changed(existing.volatility,NEW.volatility)                                   = 1 OR
           nrg_common.has_value_changed(existing.performance,NEW.performance)                                 = 1 OR
           nrg_common.has_value_changed(existing.settlement_ask_price,NEW.settlement_ask_price)               = 1 OR
           nrg_common.has_value_changed(existing.settlement_bid_price,NEW.settlement_bid_price)               = 1 OR
           nrg_common.has_value_changed(existing.num_roll_days,NEW.num_roll_days)                             = 1 OR
           nrg_common.has_value_changed(existing.instrument_code_ps,NEW.instrument_code_ps)                   = 1 OR
           nrg_common.has_value_changed(existing.stream_code,NEW.stream_code)                                 = 1 OR
           nrg_common.has_value_changed(existing.stream_type_code,NEW.stream_type_code)                       = 1 OR
           nrg_common.has_value_changed(existing.primary_stream_code,NEW.primary_stream_code)                 = 1 OR
           nrg_common.has_value_changed(existing.is_primary_stream,NEW.is_primary_stream)                     = 1 OR
           nrg_common.has_value_changed(existing.mid_px_adjustment_ids,NEW.mid_px_adjustment_ids)             = 1 OR
           nrg_common.has_value_changed(existing.mid_px_adjustments,NEW.mid_px_adjustments)                   = 1 );
    IF lv_has_snapshot_changed = 0 THEN
      RAISE lex_no_changes_found;
    END IF;

    resync_history_data(p_business_date                 => lv_business_date,
                        p_reporting_date                => lv_reporting_date,
                        p_effective_start_timestamp     => p_effective_start_timestamp);

    -- Call Put_Prices
    put_prices (p_user                          => p_user,
                p_logical_load_timestamp        => lv_logical_load_timestamp,
                p_effective_start_timestamp     => p_effective_start_timestamp,
                p_prices                        => p_prices,
                p_business_date                 => lv_business_date,
                p_reporting_date                => lv_reporting_date);

    -- Call Child Arrays
    put_ask_volumes (p_user                          => p_user,
                     p_logical_load_timestamp        => lv_logical_load_timestamp,
                     p_effective_start_timestamp     => p_effective_start_timestamp,
                     p_ask_volumes                   => p_ask_volumes,
                     p_business_date                 => lv_business_date,
                     p_reporting_date                => lv_reporting_date);

    put_bid_volumes (p_user                          => p_user,
                     p_logical_load_timestamp        => lv_logical_load_timestamp,
                     p_effective_start_timestamp     => p_effective_start_timestamp,
                     p_bid_volumes                   => p_bid_volumes,
                     p_business_date                 => lv_business_date,
                     p_reporting_date                => lv_reporting_date);

    put_core_ask_prices(p_user                          => p_user,
                        p_logical_load_timestamp        => lv_logical_load_timestamp,
                        p_effective_start_timestamp     => p_effective_start_timestamp,
                        p_core_ask_prices               => p_core_ask_prices,
                        p_business_date                 => lv_business_date,
                        p_reporting_date                => lv_reporting_date);

    put_core_bid_prices(p_user                          => p_user,
                        p_logical_load_timestamp        => lv_logical_load_timestamp,
                        p_effective_start_timestamp     => p_effective_start_timestamp,
                        p_core_bid_prices               => p_core_bid_prices,
                        p_business_date                 => lv_business_date,
                        p_reporting_date                => lv_reporting_date);

    put_core_ohlc (p_user                          => p_user,
                   p_logical_load_timestamp        => lv_logical_load_timestamp,
                   p_effective_start_timestamp     => p_effective_start_timestamp,
                   p_core_ohlc                     => p_core_ohlc,
                   p_business_date                 => lv_business_date,
                   p_reporting_date                => lv_reporting_date);

    put_ask_offsets (p_user                          => p_user,
                     p_logical_load_timestamp        => lv_logical_load_timestamp,
                     p_effective_start_timestamp     => p_effective_start_timestamp,
                     p_ask_offsets                   => p_ask_offsets,
                     p_business_date                 => lv_business_date,
                     p_reporting_date                => lv_reporting_date);

    put_bid_offsets (p_user                          => p_user,
                     p_logical_load_timestamp        => lv_logical_load_timestamp,
                     p_effective_start_timestamp     => p_effective_start_timestamp,
                     p_bid_offsets                   => p_bid_offsets,
                     p_business_date                 => lv_business_date,
                     p_reporting_date                => lv_reporting_date);

    put_prices_fxr (p_user                          => p_user,
                    p_effective_start_timestamp     => p_effective_start_timestamp,
                    p_business_date                 => lv_business_date,
                    p_reporting_date                => lv_reporting_date);
  /* Workaround for ITSM-268624 */
  DELETE FROM bi_ods.price_ask_offsets     WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.price_ask_volumes     WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.price_bid_offsets     WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.price_bid_volumes     WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.price_core_ask_prices WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.price_core_bid_prices WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.price_core_ohlc       WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;
  DELETE FROM bi_ods.prices                WHERE price_symbol LIKE '%_API%.BC' AND reporting_date = lv_reporting_date;

   --due to ref integ. delete old prices from prices table as a last
   --DELETE prices p WHERE p.Business_Date < lv_business_date;

  EXCEPTION
    WHEN lex_no_changes_found THEN
      NULL;
      --
      --Since prices has no changes nothing to do
      --
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_prices_set;

  -- ===================================================================================
  -- put_latest_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     latest_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_latest_prices                  Table of Latest Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
 PROCEDURE put_latest_prices (p_user                          IN latest_prices.created_by%TYPE,
                              p_effective_start_timestamp     IN latest_prices.effective_start_timestamp%TYPE,
                              p_latest_prices                 IN latest_prices_tab)
  IS

    lv_logical_load_timestamp latest_prices.logical_load_timestamp%TYPE;

    TYPE l_typ_price_symbol  IS TABLE OF latest_prices.Price_Symbol%TYPE;
    lv_changed_latest_prices l_typ_price_symbol;

  BEGIN
    logger.logger.set_module('put_latest_prices');


    -- set the logical load timestamp to now
    lv_logical_load_timestamp := SYSTIMESTAMP;


   SELECT old_version.price_symbol
      BULK COLLECT INTO lv_changed_latest_prices
     FROM latest_prices old_version,
              (SELECT * FROM
                  (SELECT /*+ cardinality(t 2500)*/ t.*
                          ,row_number() OVER (PARTITION BY  instrument_code ORDER BY quote_time DESC) rank
                     FROM TABLE(CAST(p_latest_prices AS latest_prices_tab)) t
                WHERE t.instrument_code IS NOT NULL AND t.price_symbol IS NOT NULL
                AND t.platform ='NG') WHERE rank=1) new_version
    WHERE old_version.instrument_code = new_version.instrument_code
     FOR UPDATE OF old_version.instrument_code;

   MERGE /*+ cardinality(new_version 2500)*/ INTO latest_prices old_version
        USING  (SELECT * FROM
                  (SELECT /*+ cardinality(t 2500)*/ t.*
                          ,row_number() OVER (PARTITION BY instrument_code ORDER BY quote_time DESC) rank
                     FROM TABLE(CAST(p_latest_prices AS latest_prices_tab)) t
                WHERE t.instrument_code IS NOT NULL AND t.price_symbol IS NOT NULL
                AND t.platform ='NG') WHERE rank=1) new_version
        ON (new_version.instrument_code = old_version.instrument_code)
        WHEN MATCHED THEN
          UPDATE
               SET logical_load_timestamp         =     lv_logical_load_timestamp
                  ,updated_by                     =     p_user
                  ,update_timestamp               =     systimestamp
                  ,effective_start_timestamp      =     p_effective_start_timestamp
                  ,platform                       =     new_version.platform
                  ,ask_price                      =     new_version.ask_price
                  ,ask_price_offset               =     new_version.ask_price_offset
                  ,ask_prices                     =     new_version.ask_prices
                  ,ask_volumes                    =     new_version.ask_volumes
                  ,bid_price                      =     new_version.bid_price
                  ,bid_price_offset               =     new_version.bid_price_offset
                  ,bid_prices                     =     new_version.bid_prices
                  ,bid_volumes                    =     new_version.bid_volumes
                  ,close                          =     new_version.close
                  ,generation                     =     new_version.generation
                  ,good_for                       =     new_version.good_for
                  ,high                           =     new_version.high
                  ,instrument                     =     new_version.instrument
                  ,is_corporate_actions           =     new_version.is_corporate_actions
                  ,is_first_good_in_continuous    =     new_version.is_first_good_in_continuous
                  ,is_increasing_long_allowed     =     new_version.is_increasing_long_allowed
                  ,is_increasing_short_allowed    =     new_version.is_increasing_short_allowed
                  ,instrument_found_in_master     =     new_version.instrument_found_in_master
                  ,is_tradable                    =     new_version.is_tradable
                  ,is_tradable_instrument         =     new_version.is_tradable_instrument
                  ,low                            =     new_version.low
                  ,mid_price                      =     new_version.mid_price
                  ,mm_instrument_id               =     new_version.mm_instrument_id
                  ,open                           =     new_version.open
                  ,price_exponent                 =     new_version.price_exponent
                  ,quote_id                       =     new_version.quote_id
                  ,quote_time                     =     new_version.quote_time
                  ,request_time                   =     new_version.request_time
                  ,rollover_ask_spread            =     new_version.rollover_ask_spread
                  ,rollover_bid_spread            =     new_version.rollover_bid_spread
                  ,sequence_id                    =     new_version.sequence_id
                  ,settlement_price               =     new_version.settlement_price
                  ,source_time                    =     new_version.source_time
                  ,status                         =     new_version.status
                  ,subject                        =     new_version.subject
                  ,underlying_ask_price           =     new_version.underlying_ask_price
                  ,underlying_bid_price           =     new_version.underlying_bid_price
                  ,business_date                  =     nrg_common.get_business_date(SYSDATE)
                  ,reporting_date                 =     nrg_common.get_reporting_date(SYSDATE)
                  ,instrument_code_ps             =     new_version.instrument_code_ps
                  ,stream_code                    =     new_version.stream_code
                  ,stream_type_code               =     new_version.stream_type_code
                  ,primary_stream_code            =     new_version.primary_stream_code
                  ,is_primary_stream              =     new_version.is_primary_stream
                  ,mid_px_adjustment_ids          =     new_version.mid_px_adjustment_ids
                  ,mid_px_adjustments             =     new_version.mid_px_adjustments
        WHEN NOT MATCHED THEN
          INSERT ( price_symbol
                  ,logical_load_timestamp
                  ,created_by
                  ,create_timestamp
                  ,updated_by
                  ,update_timestamp
                  ,effective_start_timestamp
                  ,ask_price
                  ,ask_price_offset
                  ,ask_prices
                  ,ask_volumes
                  ,bid_price
                  ,bid_price_offset
                  ,bid_prices
                  ,bid_volumes
                  ,close
                  ,generation
                  ,good_for
                  ,high
                  ,instrument
                  ,instrument_code
                  ,is_corporate_actions
                  ,is_first_good_in_continuous
                  ,is_increasing_long_allowed
                  ,is_increasing_short_allowed
                  ,instrument_found_in_master
                  ,is_tradable
                  ,is_tradable_instrument
                  ,low
                  ,mid_price
                  ,mm_instrument_id
                  ,open
                  ,platform
                  ,price_exponent
                  ,quote_id
                  ,quote_time
                  ,request_time
                  ,rollover_ask_spread
                  ,rollover_bid_spread
                  ,sequence_id
                  ,settlement_price
                  ,source_time
                  ,status
                  ,subject
                  ,underlying_ask_price
                  ,underlying_bid_price
                  ,business_date
                  ,reporting_date
                  ,instrument_code_ps
                  ,stream_code
                  ,stream_type_code
                  ,primary_stream_code
                  ,is_primary_stream
                  ,mid_px_adjustment_ids
                  ,mid_px_adjustments
                  )
           VALUES ( new_version.price_symbol
                   ,lv_logical_load_timestamp
                   ,p_user
                   ,SYSTIMESTAMP
                   ,p_user
                   ,SYSTIMESTAMP
                   ,p_effective_start_timestamp
                   ,new_version.ask_price
                   ,new_version.ask_price_offset
                   ,new_version.ask_prices
                   ,new_version.ask_volumes
                   ,new_version.bid_price
                   ,new_version.bid_price_offset
                   ,new_version.bid_prices
                   ,new_version.bid_volumes
                   ,new_version.close
                   ,new_version.generation
                   ,new_version.good_for
                   ,new_version.high
                   ,new_version.instrument
                   ,new_version.instrument_code
                   ,new_version.is_corporate_actions
                   ,new_version.is_first_good_in_continuous
                   ,new_version.is_increasing_long_allowed
                   ,new_version.is_increasing_short_allowed
                   ,new_version.instrument_found_in_master
                   ,new_version.is_tradable
                   ,new_version.is_tradable_instrument
                   ,new_version.low
                   ,new_version.mid_price
                   ,new_version.mm_instrument_id
                   ,new_version.open
                   ,new_version.platform
                   ,new_version.price_exponent
                   ,new_version.quote_id
                   ,new_version.quote_time
                   ,new_version.request_time
                   ,new_version.rollover_ask_spread
                   ,new_version.rollover_bid_spread
                   ,new_version.sequence_id
                   ,new_version.settlement_price
                   ,new_version.source_time
                   ,new_version.status
                   ,new_version.subject
                   ,new_version.underlying_ask_price
                   ,new_version.underlying_bid_price
                   ,nrg_common.get_business_date(SYSDATE)
                   ,nrg_common.get_reporting_date(SYSDATE)
                   ,new_version.instrument_code_ps
                   ,new_version.stream_code
                   ,new_version.stream_type_code
                   ,new_version.primary_stream_code
                   ,new_version.is_primary_stream
                   ,new_version.mid_px_adjustment_ids
                   ,new_version.mid_px_adjustments
                   );

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_latest_prices;

  -- ===================================================================================
  -- put_furai_latest_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a furai price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     furai_latest_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_furai_latest_prices            Table of Furai Latest Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
 PROCEDURE put_furai_latest_prices (p_user                          IN furai_latest_prices.created_by%TYPE,
                                    p_effective_start_timestamp     IN furai_latest_prices.effective_start_timestamp%TYPE,
                                    p_furai_latest_prices           IN furai_latest_price_tab) IS

    lv_logical_load_timestamp     TIMESTAMP(6);

  BEGIN

    logger.logger.set_module('put_furai_latest_prices');

    lv_logical_load_timestamp := SYSTIMESTAMP;

    MERGE INTO furai_latest_prices old_version
    USING (SELECT a.*,
                  p_user created_by,
                  p_user updated_by,
                  SYSTIMESTAMP create_timestamp,
                  SYSTIMESTAMP update_timestamp,
                  lv_logical_load_timestamp logical_load_timestamp,
                  p_effective_start_timestamp effective_start_timestamp
           FROM TABLE(CAST(p_furai_latest_prices AS  furai_latest_price_tab)) a)new_version
    ON (old_version.instrument_code = new_version.instrument_code AND
        old_version.price_symbol = new_version.price_symbol)
    WHEN MATCHED THEN
    UPDATE
    SET logical_load_timestamp = new_version.logical_load_timestamp,
        updated_by = new_version.updated_by,
        update_timestamp = new_version.update_timestamp,
        effective_start_timestamp = new_version.effective_start_timestamp,
        status = new_version.status,
        price_exponent = new_version.price_exponent,
        quote_id = new_version.quote_id,
        quote_time = new_version.quote_time,
        source_time = new_version.source_time,
        is_high_volatility_period = new_version.is_high_volatility_period,
        volatility = new_version.volatility
    WHEN NOT MATCHED THEN
    INSERT(old_version.instrument_code,
           old_version.price_symbol,
           old_version.logical_load_timestamp,
           old_version.created_by,
           old_version.create_timestamp,
           old_version.updated_by,
           old_version.update_timestamp,
           old_version.effective_start_timestamp,
           old_version.status,
           old_version.price_exponent,
           old_version.quote_id,
           old_version.quote_time,
           old_version.source_time,
           old_version.is_high_volatility_period,
           old_version.volatility)
     VALUES(new_version.instrument_code,
           new_version.price_symbol,
           new_version.logical_load_timestamp,
           new_version.created_by,
           new_version.create_timestamp,
           new_version.updated_by,
           new_version.update_timestamp,
           new_version.effective_start_timestamp,
           new_version.status,
           new_version.price_exponent,
           new_version.quote_id,
           new_version.quote_time,
           new_version.source_time,
           new_version.is_high_volatility_period,
           new_version.volatility);
  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);

  END;

  -- ===================================================================================
  -- put_anytime_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     anytime_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_anytime_prices                 Table of Anytime Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
PROCEDURE put_anytime_prices (p_user                          IN anytime_prices.created_by%TYPE,
                              p_effective_start_timestamp     IN anytime_prices.effective_start_timestamp%TYPE,
                              p_anytime_prices                IN anytime_prices_tab)
IS
lv_business_date  anytime_prices.Business_Date%TYPE;
lv_reporting_date anytime_prices.Reporting_Date%TYPE;
lv_logical_load_timestamp Anytime_Prices.Logical_Load_Timestamp%TYPE;
BEGIN

lv_business_date := Nrg_Common.get_snapshot_business_date(p_effective_start_timestamp);
lv_reporting_date := Nrg_Common.get_snapshot_reporting_date(p_effective_start_timestamp);
lv_logical_load_timestamp := systimestamp;

  DELETE FROM Anytime_prices ap
    WHERE ap.Reporting_Date = lv_reporting_date
      AND ap.Effective_Start_Timestamp = p_effective_start_timestamp;


  INSERT INTO Anytime_prices
               (price_symbol,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                business_date,
                reporting_date,
                instrument_code,
                requested_snapshot_time,
                platform,
                quote_id,
                quote_time,
                source_price_time,
                bid_volumes,
                ask_volumes,
                bid_price,
                ask_price,
                mid_price,
                price_exponent,
                core_bid_prices,
                core_ask_prices,
                underlying_bid_price,
                underlying_ask_price,
                bid_price_offsets,
                ask_price_offsets,
                rollover_bid_spread,
                rollover_ask_spread,
                subject,
                alternate_symbol,
                settlement_bid_price,
                settlement_ask_price,
                underlying_bid_volume,
                underlying_ask_volume,
                num_roll_days,
                instrument_code_ps,
                stream_code,
                stream_type_code,
                primary_stream_code,
                is_primary_stream,
                mid_px_adjustment_ids,
                mid_px_adjustments)
             SELECT new_version.price_symbol           AS price_symbol
                   ,lv_logical_load_timestamp          AS logical_load_timestamp
                   ,p_user                             AS created_by
                   ,lv_logical_load_timestamp          AS create_timestamp
                   ,p_user                             AS updated_by
                   ,lv_logical_load_timestamp          AS update_timestamp
                   ,p_effective_start_timestamp        AS effective_start_timestamp
                   ,lv_business_date                   AS business_date
                   ,lv_reporting_date                  AS reporting_date
                   ,new_version.instrument_code        AS instrument_code
                   ,p_effective_start_timestamp        AS requested_snapshot_time
                   ,'NG'                               AS platform
                   ,new_version.quote_id               AS quote_id
                   ,new_version.quote_time             AS quote_time
                   ,new_version.source_price_time      AS source_price_time
                   ,new_version.bid_volumes            AS bid_volumes
                   ,new_version.ask_volumes            AS ask_volumes
                   ,new_version.bid_price              AS bid_price
                   ,new_version.ask_price              AS ask_price
                   ,new_version.mid_price              AS mid_price
                   ,new_version.price_exponent         AS price_exponent
                   ,new_version.core_bid_prices        AS core_bid_prices
                   ,new_version.core_ask_prices        AS core_ask_prices
                   ,new_version.underlying_bid_price   AS underlying_bid_price
                   ,new_version.underlying_ask_price   AS underlying_ask_price
                   ,new_version.bid_price_offsets      AS bid_price_offsets
                   ,new_version.ask_price_offsets      AS ask_price_offsets
                   ,new_version.rollover_bid_spread    AS rollover_bid_spread
                   ,new_version.rollover_ask_spread    AS rollover_ask_spread
                   ,new_version.subject                AS subject
                   ,new_version.alternate_symbol       AS alternate_symbol
                   ,new_version.settlement_bid_price   AS settlement_bid_price
                   ,new_version.settlement_ask_price   AS settlement_ask_price
                   ,new_version.underlying_bid_volume  AS underlying_bid_volume
                   ,new_version.underlying_ask_volume  AS underlying_ask_volume
                   ,new_version.num_roll_days          AS num_roll_days
                   ,new_version.instrument_code_ps     AS instrument_code_ps
                   ,new_version.stream_code            AS stream_code
                   ,new_version.stream_type_code       AS stream_type_code
                   ,new_version.primary_stream_code    AS primary_stream_code
                   ,new_version.is_primary_stream      AS is_primary_stream
                   ,new_version.mid_px_adjustment_ids  AS mid_px_adjustment_ids
                   ,new_version.mid_px_adjustments     AS mid_px_adjustments
        FROM TABLE(CAST(p_anytime_prices AS anytime_prices_tab)        ) new_version
        WHERE new_version.instrument_code not like '%/CAR';

  DELETE FROM bi_ods.anytime_prices
   WHERE price_symbol LIKE '%_API%.BC'
     AND reporting_date = lv_reporting_date;



END put_anytime_prices;

  -- ===================================================================================
  -- put_price_stream_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_stream_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_price_stream_prices                 Table of Anytime Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_price_stream_prices (p_user                          IN price_stream_prices.created_by%TYPE,
                                     p_effective_start_timestamp     IN price_stream_prices.effective_start_timestamp%TYPE,
                                     p_price_stream_prices           IN anytime_prices_tab)
  IS

    lv_business_date  price_stream_prices.Business_Date%TYPE;
    lv_reporting_date price_stream_prices.Reporting_Date%TYPE;
    lv_logical_load_timestamp price_stream_Prices.Logical_Load_Timestamp%TYPE;
    
  BEGIN

    lv_business_date := Nrg_Common.get_snapshot_business_date(p_effective_start_timestamp);
    lv_reporting_date := Nrg_Common.get_snapshot_reporting_date(p_effective_start_timestamp);
    lv_logical_load_timestamp := systimestamp;

    DELETE price_stream_prices_h
     WHERE business_date = lv_business_date 
       AND reporting_date = lv_reporting_date;

    INSERT INTO price_stream_prices_h
               (stream_code              ,
                logical_load_timestamp   ,
                created_by               ,
                create_timestamp         ,
                updated_by               ,
                update_timestamp         ,
                effective_start_timestamp,
                effective_end_timestamp  ,
                action                   ,
                action_timestamp         ,
                business_date            ,
                reporting_date           ,
                instrument_code          ,
                requested_snapshot_time  ,
                platform                 ,
                price_symbol             ,
                quote_id                 ,
                quote_time               ,
                quote_status             ,
                source_price_time        ,
                bid_volumes              ,
                ask_volumes              ,
                bid_price                ,
                ask_price                ,
                mid_price                ,
                price_exponent           ,
                core_bid_prices          ,
                core_ask_prices          ,
                underlying_bid_price     ,
                underlying_ask_price     ,
                bid_price_offsets        ,
                ask_price_offsets        ,
                rollover_bid_spread      ,
                rollover_ask_spread      ,
                subject                  ,
                alternate_symbol         ,
                settlement_bid_price     ,
                settlement_ask_price     ,
                underlying_bid_volume    ,
                underlying_ask_volume    ,
                num_roll_days            ,
                instrument_code_ps       ,
                stream_type_code         ,
                primary_stream_code      ,
                is_primary_stream        ,
                mid_px_adjustment_ids    ,
                mid_px_adjustments       ,
                value_date
                )
         SELECT new_version.stream_code,
                new_version.logical_load_timestamp,
                new_version.created_by,
                new_version.create_timestamp,
                new_version.updated_by,
                new_version.update_timestamp,
                new_version.effective_start_timestamp,
                p_effective_start_timestamp,
                'U',
                SYSTIMESTAMP, 
                new_version.business_date            ,
                new_version.reporting_date           ,
                new_version.instrument_code          ,
                new_version.requested_snapshot_time  ,
                new_version.platform                 ,
                new_version.price_symbol             ,
                new_version.quote_id                 ,
                new_version.quote_time               ,
                new_version.quote_status             ,
                new_version.source_price_time        ,
                new_version.bid_volumes              ,
                new_version.ask_volumes              ,
                new_version.bid_price                ,
                new_version.ask_price                ,
                new_version.mid_price                ,
                new_version.price_exponent           ,
                new_version.core_bid_prices          ,
                new_version.core_ask_prices          ,
                new_version.underlying_bid_price     ,
                new_version.underlying_ask_price     ,
                new_version.bid_price_offsets        ,
                new_version.ask_price_offsets        ,
                new_version.rollover_bid_spread      ,
                new_version.rollover_ask_spread      ,
                new_version.subject                  ,
                new_version.alternate_symbol         ,
                new_version.settlement_bid_price     ,
                new_version.settlement_ask_price     ,
                new_version.underlying_bid_volume    ,
                new_version.underlying_ask_volume    ,
                new_version.num_roll_days            ,
                new_version.instrument_code_ps       ,
                new_version.stream_type_code         ,
                new_version.primary_stream_code      ,
                new_version.is_primary_stream        ,
                new_version.mid_px_adjustment_ids    ,
                new_version.mid_px_adjustments       ,
                new_version.value_date
      FROM price_stream_prices new_version
     WHERE business_date = lv_business_date 
       AND reporting_date = lv_reporting_date;

    DELETE price_stream_prices
     WHERE business_date = lv_business_date 
       AND reporting_date = lv_reporting_date;

    INSERT INTO price_stream_prices
               (stream_code              ,
                logical_load_timestamp   ,
                created_by               ,
                create_timestamp         ,
                updated_by               ,
                update_timestamp         ,
                effective_start_timestamp,                
                business_date            ,
                reporting_date           ,
                instrument_code          ,
                requested_snapshot_time  ,
                platform                 ,
                price_symbol             ,
                quote_id                 ,
                quote_time               ,
                quote_status             ,
                source_price_time        ,
                bid_volumes              ,
                ask_volumes              ,
                bid_price                ,
                ask_price                ,
                mid_price                ,
                price_exponent           ,
                core_bid_prices          ,
                core_ask_prices          ,
                underlying_bid_price     ,
                underlying_ask_price     ,
                bid_price_offsets        ,
                ask_price_offsets        ,
                rollover_bid_spread      ,
                rollover_ask_spread      ,
                subject                  ,
                alternate_symbol         ,
                settlement_bid_price     ,
                settlement_ask_price     ,
                underlying_bid_volume    ,
                underlying_ask_volume    ,
                num_roll_days            ,
                instrument_code_ps       ,
                stream_type_code         ,
                primary_stream_code      ,
                is_primary_stream        ,
                mid_px_adjustment_ids    ,
                mid_px_adjustments       ,
                value_date
                )
         SELECT new_version.stream_code            AS stream_code
               ,lv_logical_load_timestamp          AS logical_load_timestamp
               ,p_user                             AS created_by
               ,lv_logical_load_timestamp          AS create_timestamp
               ,p_user                             AS updated_by
               ,lv_logical_load_timestamp          AS update_timestamp
               ,p_effective_start_timestamp        AS effective_start_timestamp
               ,lv_business_date                   AS business_date
               ,lv_reporting_date                  AS reporting_date
               ,new_version.instrument_code        AS instrument_code
               ,p_effective_start_timestamp        AS requested_snapshot_time
               ,'NG'                               AS platform
               ,new_version.price_symbol           AS price_symbol
               ,new_version.quote_id               AS quote_id
               ,new_version.quote_time             AS quote_time
               ,new_version.quote_status           AS quote_status
               ,new_version.source_price_time      AS source_price_time
               ,new_version.bid_volumes            AS bid_volumes
               ,new_version.ask_volumes            AS ask_volumes
               ,new_version.bid_price              AS bid_price
               ,new_version.ask_price              AS ask_price
               ,new_version.mid_price              AS mid_price
               ,new_version.price_exponent         AS price_exponent
               ,new_version.core_bid_prices        AS core_bid_prices
               ,new_version.core_ask_prices        AS core_ask_prices
               ,new_version.underlying_bid_price   AS underlying_bid_price
               ,new_version.underlying_ask_price   AS underlying_ask_price
               ,new_version.bid_price_offsets      AS bid_price_offsets
               ,new_version.ask_price_offsets      AS ask_price_offsets
               ,new_version.rollover_bid_spread    AS rollover_bid_spread
               ,new_version.rollover_ask_spread    AS rollover_ask_spread
               ,new_version.subject                AS subject
               ,new_version.alternate_symbol       AS alternate_symbol
               ,new_version.settlement_bid_price   AS settlement_bid_price
               ,new_version.settlement_ask_price   AS settlement_ask_price
               ,new_version.underlying_bid_volume  AS underlying_bid_volume
               ,new_version.underlying_ask_volume  AS underlying_ask_volume
               ,new_version.num_roll_days          AS num_roll_days
               ,new_version.instrument_code_ps     AS instrument_code_ps
               ,new_version.stream_type_code       AS stream_type_code
               ,new_version.primary_stream_code    AS primary_stream_code
               ,new_version.is_primary_stream      AS is_primary_stream
               ,new_version.mid_px_adjustment_ids  AS mid_px_adjustment_ids
               ,new_version.mid_px_adjustments     AS mid_px_adjustments
               ,new_version.value_date             AS value_date
          FROM TABLE(CAST(p_price_stream_prices AS anytime_prices_tab)) new_version;

    DELETE FROM bi_ods.price_stream_prices
     WHERE price_symbol LIKE '%_API%.BC'
       AND reporting_date = lv_reporting_date;

  END put_price_stream_prices;

    -- ===================================================================================
    -- put_price_stream_anytime_prices
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a price collection
    --
    -- Notes:
    -- ------
    --
    --     Tables populated:
    --
    --     price_stream_anytime_prices
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_price_stream_anytime_prices                 Table of Anytime Prices
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_price_stream_anytime_prices (p_user                          IN price_stream_anytime_prices.created_by%TYPE,
                                             p_effective_start_timestamp     IN price_stream_anytime_prices.effective_start_timestamp%TYPE,
                                             p_price_stream_anytime_prices   IN anytime_prices_tab)
  IS
  
    lv_business_date  price_stream_anytime_prices.Business_Date%TYPE;
    lv_reporting_date price_stream_anytime_prices.Reporting_Date%TYPE;
    lv_logical_load_timestamp price_stream_anytime_prices.Logical_Load_Timestamp%TYPE;
    
  BEGIN

    lv_business_date          := Nrg_Common.get_snapshot_business_date(p_effective_start_timestamp);
    lv_reporting_date         := Nrg_Common.get_snapshot_reporting_date(p_effective_start_timestamp);
    lv_logical_load_timestamp := systimestamp;

    DELETE FROM price_stream_anytime_prices ap
     WHERE ap.Reporting_Date = lv_reporting_date
       AND ap.Effective_Start_Timestamp = p_effective_start_timestamp;

    INSERT INTO price_stream_anytime_prices
                 (price_symbol,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  business_date,
                  reporting_date,
                  instrument_code,
                  requested_snapshot_time,
                  platform,
                  quote_id,
                  quote_time,
                  source_price_time,
                  bid_volumes,
                  ask_volumes,
                  bid_price,
                  ask_price,
                  mid_price,
                  price_exponent,
                  core_bid_prices,
                  core_ask_prices,
                  underlying_bid_price,
                  underlying_ask_price,
                  bid_price_offsets,
                  ask_price_offsets,
                  rollover_bid_spread,
                  rollover_ask_spread,
                  subject,
                  alternate_symbol,
                  settlement_bid_price,
                  settlement_ask_price,
                  underlying_bid_volume,
                  underlying_ask_volume,
                  num_roll_days,
                  instrument_code_ps,
                  stream_code,
                  stream_type_code,
                  primary_stream_code,
                  is_primary_stream,
                  mid_px_adjustment_ids,
                  mid_px_adjustments,
                  value_date)
               SELECT new_version.price_symbol           AS price_symbol
                     ,lv_logical_load_timestamp          AS logical_load_timestamp
                     ,p_user                             AS created_by
                     ,lv_logical_load_timestamp          AS create_timestamp
                     ,p_user                             AS updated_by
                     ,lv_logical_load_timestamp          AS update_timestamp
                     ,p_effective_start_timestamp        AS effective_start_timestamp
                     ,lv_business_date                   AS business_date
                     ,lv_reporting_date                  AS reporting_date
                     ,new_version.instrument_code        AS instrument_code
                     ,p_effective_start_timestamp        AS requested_snapshot_time
                     ,'NG'                               AS platform
                     ,new_version.quote_id               AS quote_id
                     ,new_version.quote_time             AS quote_time
                     ,new_version.source_price_time      AS source_price_time
                     ,new_version.bid_volumes            AS bid_volumes
                     ,new_version.ask_volumes            AS ask_volumes
                     ,new_version.bid_price              AS bid_price
                     ,new_version.ask_price              AS ask_price
                     ,new_version.mid_price              AS mid_price
                     ,new_version.price_exponent         AS price_exponent
                     ,new_version.core_bid_prices        AS core_bid_prices
                     ,new_version.core_ask_prices        AS core_ask_prices
                     ,new_version.underlying_bid_price   AS underlying_bid_price
                     ,new_version.underlying_ask_price   AS underlying_ask_price
                     ,new_version.bid_price_offsets      AS bid_price_offsets
                     ,new_version.ask_price_offsets      AS ask_price_offsets
                     ,new_version.rollover_bid_spread    AS rollover_bid_spread
                     ,new_version.rollover_ask_spread    AS rollover_ask_spread
                     ,new_version.subject                AS subject
                     ,new_version.alternate_symbol       AS alternate_symbol
                     ,new_version.settlement_bid_price   AS settlement_bid_price
                     ,new_version.settlement_ask_price   AS settlement_ask_price
                     ,new_version.underlying_bid_volume  AS underlying_bid_volume
                     ,new_version.underlying_ask_volume  AS underlying_ask_volume
                     ,new_version.num_roll_days          AS num_roll_days
                     ,new_version.instrument_code_ps     AS instrument_code_ps
                     ,new_version.stream_code            AS stream_code
                     ,new_version.stream_type_code       AS stream_type_code
                     ,new_version.primary_stream_code    AS primary_stream_code
                     ,new_version.is_primary_stream      AS is_primary_stream
                     ,new_version.mid_px_adjustment_ids  AS mid_px_adjustment_ids
                     ,new_version.mid_px_adjustments     AS mid_px_adjustments
                     ,new_version.value_date             AS value_date
          FROM TABLE(CAST(p_price_stream_anytime_prices AS anytime_prices_tab)) new_version;

    DELETE FROM bi_ods.price_stream_anytime_prices
     WHERE price_symbol LIKE '%_API%.BC'
       AND reporting_date = lv_reporting_date;

  END put_price_stream_anytime_prices;

  -- ===================================================================================
  -- put_price_stream_latest_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     price_stream_latest_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_price_stream_latest_prices                  Table of Latest Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
 PROCEDURE put_price_stream_latest_prices(p_user                          IN price_stream_latest_prices.created_by%TYPE,
                                          p_effective_start_timestamp     IN price_stream_latest_prices.effective_start_timestamp%TYPE,
                                          p_price_stream_latest_prices    IN anytime_prices_tab)
  IS

    lv_logical_load_timestamp price_stream_latest_prices.logical_load_timestamp%TYPE;

    TYPE l_typ_price_symbol  IS TABLE OF price_stream_latest_prices.Price_Symbol%TYPE;
    lv_changed_price_stream_latest_prices l_typ_price_symbol;

  BEGIN
    logger.logger.set_module('put_price_stream_latest_prices');

    -- set the logical load timestamp to now
    lv_logical_load_timestamp := SYSTIMESTAMP;

    SELECT old_version.price_symbol
      BULK COLLECT INTO lv_changed_price_stream_latest_prices
      FROM price_stream_latest_prices old_version,
           (SELECT * FROM
                  (SELECT /*+ cardinality(t 2500)*/ t.*
                          ,row_number() OVER (PARTITION BY stream_code,instrument_code ORDER BY quote_time DESC) rank
                     FROM TABLE(CAST(p_price_stream_latest_prices AS anytime_prices_tab)) t
             WHERE t.stream_code IS NOT NULL AND t.instrument_code IS NOT NULL AND t.price_symbol IS NOT NULL) WHERE rank=1) new_version
     WHERE old_version.stream_code     = new_version.stream_code
       AND old_version.instrument_code = new_version.instrument_code
       FOR UPDATE OF old_version.instrument_code;

   MERGE /*+ cardinality(new_version 2500)*/ INTO price_stream_latest_prices old_version
        USING  (SELECT * FROM
                  (SELECT /*+ cardinality(t 2500)*/ t.*
                          ,row_number() OVER (PARTITION BY stream_code,instrument_code ORDER BY quote_time DESC) rank
                     FROM TABLE(CAST(p_price_stream_latest_prices AS anytime_prices_tab)) t
                WHERE t.stream_code IS NOT NULL AND t.instrument_code IS NOT NULL AND t.price_symbol IS NOT NULL) WHERE rank=1) new_version
        ON (new_version.stream_code     = old_version.stream_code
        AND new_version.instrument_code = old_version.instrument_code)
        WHEN MATCHED THEN
          UPDATE
               SET logical_load_timestamp         = lv_logical_load_timestamp
                  ,updated_by                     = p_user
                  ,update_timestamp               = systimestamp
                  ,effective_start_timestamp      = p_effective_start_timestamp
                  ,business_date                  = nrg_common.get_business_date(SYSDATE)
                  ,reporting_date                 = nrg_common.get_reporting_date(SYSDATE)
                  ,platform                       = 'NG'
                  ,price_symbol                   = new_version.price_symbol    
                  ,quote_id                       = new_version.quote_id
                  ,quote_time                     = new_version.quote_time      
                  ,quote_status                   = new_version.quote_status    
                  ,source_price_time              = new_version.source_price_time
                  ,bid_volumes                    = new_version.bid_volumes      
                  ,ask_volumes                    = new_version.ask_volumes      
                  ,bid_price                      = new_version.bid_price
                  ,ask_price                      = new_version.ask_price
                  ,mid_price                      = new_version.mid_price
                  ,price_exponent                 = new_version.price_exponent  
                  ,core_bid_prices                = new_version.core_bid_prices  
                  ,core_ask_prices                = new_version.core_ask_prices  
                  ,underlying_bid_price           = new_version.underlying_bid_price      
                  ,underlying_ask_price           = new_version.underlying_ask_price      
                  ,bid_price_offsets              = new_version.bid_price_offsets
                  ,ask_price_offsets              = new_version.ask_price_offsets
                  ,rollover_bid_spread            = new_version.rollover_bid_spread        
                  ,rollover_ask_spread            = new_version.rollover_ask_spread        
                  ,subject                        = new_version.subject  
                  ,alternate_symbol               = new_version.alternate_symbol
                  ,settlement_bid_price           = new_version.settlement_bid_price      
                  ,settlement_ask_price           = new_version.settlement_ask_price      
                  ,underlying_bid_volume          = new_version.underlying_bid_volume      
                  ,underlying_ask_volume          = new_version.underlying_ask_volume      
                  ,num_roll_days                  = new_version.num_roll_days    
                  ,instrument_code_ps             = new_version.instrument_code_ps        
                  ,stream_type_code               = new_version.stream_type_code
                  ,primary_stream_code            = new_version.primary_stream_code        
                  ,is_primary_stream              = new_version.is_primary_stream
                  ,mid_px_adjustment_ids          = new_version.mid_px_adjustment_ids      
                  ,mid_px_adjustments             = new_version.mid_px_adjustments        
                  ,value_date                     = new_version.value_date      
                  ,open                           = new_version.open    
                  ,high                           = new_version.high    
                  ,low                            = new_version.low      
                  ,close                          = new_version.close    

        WHEN NOT MATCHED THEN
          INSERT (stream_code                ,
                  logical_load_timestamp    ,
                  created_by                ,
                  create_timestamp          ,
                  updated_by                ,
                  update_timestamp          ,
                  effective_start_timestamp  ,
                  business_date              ,
                  reporting_date            ,
                  instrument_code            ,
                  platform                  ,
                  price_symbol              ,
                  quote_id                  ,
                  quote_time                ,
                  quote_status              ,
                  source_price_time          ,
                  bid_volumes                ,
                  ask_volumes                ,
                  bid_price                  ,
                  ask_price                  ,
                  mid_price                  ,
                  price_exponent            ,
                  core_bid_prices            ,
                  core_ask_prices            ,
                  underlying_bid_price      ,
                  underlying_ask_price      ,
                  bid_price_offsets          ,
                  ask_price_offsets          ,
                  rollover_bid_spread        ,
                  rollover_ask_spread        ,
                  subject                    ,
                  alternate_symbol          ,
                  settlement_bid_price      ,
                  settlement_ask_price      ,
                  underlying_bid_volume      ,
                  underlying_ask_volume      ,
                  num_roll_days              ,
                  instrument_code_ps        ,
                  stream_type_code          ,
                  primary_stream_code        ,
                  is_primary_stream          ,
                  mid_px_adjustment_ids      ,
                  mid_px_adjustments        ,
                  value_date                ,
                  open                      ,
                  high                      ,
                  low                        ,
                  close    
                  )
           VALUES ( new_version.stream_code
                   ,lv_logical_load_timestamp
                   ,p_user
                   ,SYSTIMESTAMP
                   ,p_user
                   ,SYSTIMESTAMP
                   ,p_effective_start_timestamp
                   ,nrg_common.get_business_date(SYSDATE)
                   ,nrg_common.get_reporting_date(SYSDATE)
                   ,new_version.instrument_code  
                   ,'NG'
                   ,new_version.price_symbol    
                   ,new_version.quote_id
                   ,new_version.quote_time      
                   ,new_version.quote_status    
                   ,new_version.source_price_time
                   ,new_version.bid_volumes      
                   ,new_version.ask_volumes      
                   ,new_version.bid_price
                   ,new_version.ask_price
                   ,new_version.mid_price
                   ,new_version.price_exponent  
                   ,new_version.core_bid_prices  
                   ,new_version.core_ask_prices  
                   ,new_version.underlying_bid_price      
                   ,new_version.underlying_ask_price      
                   ,new_version.bid_price_offsets
                   ,new_version.ask_price_offsets
                   ,new_version.rollover_bid_spread        
                   ,new_version.rollover_ask_spread        
                   ,new_version.subject  
                   ,new_version.alternate_symbol
                   ,new_version.settlement_bid_price      
                   ,new_version.settlement_ask_price      
                   ,new_version.underlying_bid_volume      
                   ,new_version.underlying_ask_volume      
                   ,new_version.num_roll_days    
                   ,new_version.instrument_code_ps        
                   ,new_version.stream_type_code
                   ,new_version.primary_stream_code        
                   ,new_version.is_primary_stream
                   ,new_version.mid_px_adjustment_ids      
                   ,new_version.mid_px_adjustments        
                   ,new_version.value_date      
                   ,new_version.open    
                   ,new_version.high    
                   ,new_version.low      
                   ,new_version.close    
                   );

  EXCEPTION
    WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_price_stream_latest_prices;

END nrg_prices;
/