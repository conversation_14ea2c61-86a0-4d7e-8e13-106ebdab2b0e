create or replace PACKAGE nrg_customer
AS
    -- ===================================================================================
    -- NRG_CUSTOMER
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the customers model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     05/09/2011   Sanket Mittal      1.0    Creation
    --     24/10/2011   Manoj Kumar        1.1    Modified the put_customer method after removal of triggers
    --     08/11/2011   Manoj Kumar        1.2    Modified the package for writing data into history tables.
    --     25/11/2011   Sanket Mittal      1.3    Modified to add the new field trading_account_type on trading account customers link
    --     28/11/2011   Mark Gornicki      1.4.   Added the parameter p_trading_account_type to the call of
    --                                            create_trading_account_stub, and other related fixes
    --     31/01/2012   Mark Gornicki      1.7    Added the function get_stubbed_ids
    --     27/07/2012   Sanket Mittal      2.0    Added new fields Assigned_agent_identity, Assigned_sales_trader_identity and bi_segmentation
    --     11/08/2012   Sanket Mittal      2.1    Added new fields Is_Potential_Premium, Trust_Type, Lead_Office
    --     25/06/2013   Prachi Shah        2.2    Added new field Assigned_Agent_Identity_Date
    --     22/07/2013   Prachi Shah        2.3    Added new fields last_remediation_date, next_remediation_date,next_remediation_alert_date, online_delcaration
    --     24/09/2013   Ravi Shankar       2.4    Added logic to include 'BLOCK_MARKETING' column in put_customer and put_history for customer table
    --     16/10/2013   Ravi Shankar       2.5    Removed referrence to the current seggregation flags and included logic to consume the newly added column is_customer_segregated
    --     17/10/2013   Ravi Shankar       2.5    Added put_referrer functionality and included logic to consume newly added column referrer_reference
    --     13/01/2013   Adam Krasnicki     2.6    p_full_remediation_required  parameter added to put_customer procedure
    --     04/03/2014   Adam Krasnicki     2.7    introducer_type, partner_id added to customer_refererrer[_h] tables
    --     13/06/2014   Adam Krasnicki     2.7    Two parameters to put_customer added (fatca%)
    --     08/10/2014   Adam Krasnicki     2.7    apprtns_assessment_date column added to customers table, put procedure changed
    --     22/07/2015   Adam Krasnicki     2.7    put_customers: 4 new columns added
    --     18/12/2015   Sanket Mittal      2.8    Additional Attributes added for customers - IS_BINARY_OPT_OUT, BINARY_OPT_OUT_STATUS_DATE, PERM_AGENT_IDENTITY, PERM_AGENT_IDENTITY_DATE
    --     03/03/2016   Sanket Mittal      2.9    Changed for BER-2388 - New columns added RECORD_SOURCE and SOURCE_CUSTOMER_ID
    --     17/06/2016   Sanket Mittal      3.0    Changed for BER-2673 - Integrate updated Introducer data contract - Knockouts - Added additional columns For customer referrer
    --     01/12/2016   Sanket Mittal      3.1    BER-3172 ODS - Integrate updated Customer registrations Data Contract
    --     05/01/2017   Sanket Mittal      3.2    BER-3243 ODS - Integrate updated Customer data contract
    --     18/04/2017   Sanket Mittal      3.4    BER-3533 NRG_CUSTOMER - Data contract changes
    --     19/03/2018   Sakina Kinkhabwala 3.3    BER-4239 NRG_CUSTOMER -put_customer/p_customer_apprprtnss_info - add new attributes
    --     03/05/2019   Patrick Dinwiddy   3.8    JCS-10583 new German Tax objects
    --     11/07/2019   Patrick Dinwiddy   3.9    JCS-11149 new segregation and reg classification entities
    --     29/09/2019   Patrick Dinwiddy   4.0    JCS-11398 new approp attribute and JCS-11508 new opt-up entities
    --     12/06/2020   Patrick Dinwiddy   4.6    JCS-13088
    --     15/03/2021   Patrick Dinwiddy   4.8    JCS-14433
    --     21/09/2021   Patrick Dinwiddy   5.0    JCS-14974
    --     21/02/2022   Patrick Dinwiddy   5.1    JCS-15142,15481
    -- ===================================================================================
    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;
    -- ===================================================================================
    -- create_customer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer stub in case the customer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id table
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_customer_stub (p_user                        IN customers.created_by%TYPE,
                                    p_logical_load_timestamp      IN customers.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   IN customers.effective_start_timestamp%TYPE,
                                    p_customer_id                 IN customer_id_tab);
    -- ===================================================================================
    -- create_customer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer stub in case the customer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_customer_stub (p_user                        IN customers.created_by%TYPE,
                                    p_logical_load_timestamp      IN customers.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   IN customers.effective_start_timestamp%TYPE,
                                    p_customer_id                 IN customers.customer_id%TYPE);
    -- ===================================================================================
    -- create_customer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer stub in case the customer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id
    --     p_person_id                      Person Id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_customer_stub (p_user                        IN customers.created_by%TYPE,
                                    p_logical_load_timestamp      IN customers.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   IN customers.effective_start_timestamp%TYPE,
                                    p_customer_id                 IN customers.customer_id%TYPE,
                                    p_person_id                   IN customers.person_id%TYPE);
    /***** Begin Modifiction for V2.5 - BER671 *****/
    -- ===================================================================================
    -- create_referrer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer referrer stub in case the referrer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_referrer_reference             Referrer Reference
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_referrer_stub (p_user                          IN customer_referrer.created_by%TYPE,
                                    p_logical_load_timestamp        IN customer_referrer.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp     IN customer_referrer.effective_start_timestamp%TYPE,
                                    p_referrer_reference            IN customer_referrer.referrer_reference%TYPE);
    -- ===================================================================================
    -- put_customer_referrer
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer referrer
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMER_REFERRER
    --     CUSTOMER_REFERRER_H
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_id                             Referrer Id
    --     p_referrer_reference             Referrer Reference
    --     p_notes                          Notes
    --     p_description                    description
    --     p_introducer_type
    --     p_partner_id
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_customer_referrer ( p_user                            IN  customer_referrer.created_by%TYPE,
                                      p_effective_start_timestamp       IN  customer_referrer.effective_start_timestamp%TYPE,
                                      p_id                              IN  customer_referrer.id%TYPE,
                                      p_referrer_reference              IN  customer_referrer.referrer_reference%TYPE,
                                      p_notes                           IN  customer_referrer.notes%TYPE,
                                      p_description                     IN  customer_referrer.description%TYPE,
                                      p_introducer_type                 IN  customer_referrer.Introducer_Type%TYPE,
                                      p_partner_id                      IN  Customer_Referrer.Partner_Id%TYPE,
                                      p_is_deleted                      IN  customer_referrer.is_deleted%TYPE,
                                      p_assigned_agent_identity         IN  customer_referrer.assigned_agent_identity%TYPE);
    /***** End Modifiction for V2.5 *****/
    -- ===================================================================================
    -- put_customer
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --     CUSTOMER_REGISTRATIONS
    --     TRDNG_ACCNTS_CUSTOMERS_LINK
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id
    --     p_customer_version               Customer Version
    --     p_customer_type                  Customer Type
    --     p_customer_sub_type              Customer Sub Type
    --     p_customer_status                Customer Status
    --     p_start_date                     Start Date
    --     p_is_politically_exposed         Is Politically Exposed
    --     p_probability_of_default         Probability Of Default
    --     p_risk_level                     Risk Level
    --     p_regulatory_classification      Regulatory Classification
    --     p_is_sales_trader_managed        Is Sales Trader Managed
    --     p_is_do_not_contact              Is Do Not Contact
    --     p_is_out_of_area                 Is Out Of Area
    --     p_is_chargeback_requested        Is Chargeback Requested
    --     p_risk_level_change_reason       Risk Level Change Reason
    --     p_marketing_data_id              Marketing Data Id
    --     p_marketing_data_version         Marketing Data Version
    --     p_allow_email                    Allow Email
    --     p_allow_mail                     Allow Mail
    --     p_allow_telephone                Allow Telephone
    --     p_allow_sms                      Allow Sms
    --     p_segregation_profile_id         Segregation Profile Id
    --     p_segregation_profile_version    Segregation Profile Version
    --     p_is_margin_segregation          Is Margin Segregation
    --     p_is_fr_total_equity_segregation Is Fr Total Equity Segregation
    --     p_is_rlsd_p_and_l_segregation    Is Realised P and L Segregation
    --     p_is_unrlsd_p_and_l_segregation  Is Unrealised P and L Segregation
    --     p_tax_profile_id                 Tax Profile Id
    --     p_tax_profile_version            Tax Profile Version
    --     p_tax_profile_name               Tax Profile Name
    --     p_is_tax_exempt                  Is Tax Exempt
    --     p_tax_residency_code             Tax Residency Code
    --     p_withholding_tax_rate           Withholding Tax Rate
    --     p_withholding_tax_exemption_code Withholding Tax Exemption Code
    --     p_appropriateness_id             Appropriateness Id
    --     p_is_appropriate                 Is Aprropriate
    --     p_appropriateness_reason         Appropriateness Reason
    --     p_agreed_to_proceed              Agreed To Proceed
    --     p_financial_suitability_score    Financial Suitability Score
    --     p_knowledge_experience_score     Knowledge Experience Score
    --     p_company_id                     Comapny Id
    --     p_person_id                      Person Id
    --     p_trading_account_ids            Trading Account Ids
    --     p_customer_registrations         Customer Registrations,
    --     p_is_deleted                     Is Deleted
    --     p_assigned_agent_identity        Assigned Agent Identity
    --     p_assigned_sales_trdr_identity   Assigned Sales Trader Identity
    --     p_bi_segmentation                BI Segmentation
    --     p_is_potential_premium           Is Petential Premium
    --     p_trust_type                     Trust Type
    --     p_lead_office                    Lead Office
    --     p_ASSIGNED_AGENT_IDENTITY_DATE   Assigned Agent Identity Date
    --     p_last_remediation_Date          Last Remediation Date
    --     p_next_remediation_date          Next Remediation Date
    --     p_next_remediation_alert_date    Next Remediation Alert Date
    --     p_online_declaration             Online Declaration
    /***** Begin Modification for V2.4 Ber-623 *****/
    --     p_block_marketing                Block Marketing
    /***** End Modification for V2.4 *****/
    /***** Begin Modification for V2.5 BER-669 *****/
    --     p_is_customer_segregated         Is Customer Segregated Flag
    /***** End Modification for V2.5 *****/
    --     p_is_binaries_opt_out            Is binaries opt out
    --     p_binaries_opt_out_status_date   Binaries opt out status date
    --     p_perm_agent_identity            Perm agenti dentity
    --     p_perm_agent_identity_date       Perm agent identity date
    --     p_record_source                  Record Source
    --     p_source_customer_id             Source Customer ID
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_customer      ( p_user                            IN customers.created_by%TYPE,
                                  p_effective_start_timestamp       IN customers.effective_start_timestamp%TYPE,
                                  p_customer_id                     IN customers.customer_id%TYPE,
                                  p_customer_version                IN customers.customer_version%TYPE,
                                  p_customer_type                   IN customers.customer_type%TYPE,
                                  p_customer_sub_type               IN customers.customer_sub_type%TYPE,
                                  p_customer_status                 IN customers.customer_status%TYPE,
                                  p_start_date                      IN customers.start_date%TYPE,
                                  p_is_politically_exposed          IN customers.is_politically_exposed%TYPE,
                                  p_probability_of_default          IN customers.probability_of_default%TYPE,
                                  p_risk_level                      IN customers.risk_level%TYPE,
                                  p_regulatory_classification       IN customers.regulatory_classification%TYPE,
                                  p_is_sales_trader_managed         IN customers.is_sales_trader_managed%TYPE,
                                  p_is_do_not_contact               IN customers.is_do_not_contact%TYPE,
                                  p_is_out_of_area                  IN customers.is_out_of_area%TYPE,
                                  p_is_chargeback_requested         IN customers.is_chargeback_requested%TYPE,
                                  p_risk_level_change_reason        IN customers.risk_level_change_reason%TYPE,
                                  p_marketing_data_id               IN customers.marketing_data_id%TYPE,
                                  p_marketing_data_version          IN customers.marketing_data_version%TYPE,
                                  p_allow_email                     IN customers.allow_email%TYPE,
                                  p_allow_mail                      IN customers.allow_mail%TYPE,
                                  p_allow_telephone                 IN customers.allow_telephone%TYPE,
                                  p_allow_sms                       IN customers.allow_sms%TYPE,
                                  p_tax_profile_id                  IN customers.tax_profile_id%TYPE,
                                  p_tax_profile_version             IN customers.tax_profile_version%TYPE,
                                  p_is_tax_exempt                   IN customers.is_tax_exempt%TYPE,
                                  p_tax_residency_code              IN customers.tax_residency_code%TYPE,
                                  p_withholding_tax_rate            IN customers.withholding_tax_rate%TYPE,
                                  p_withholding_tax_exption_cde     IN customers.withholding_tax_exemption_code%TYPE,
                                  p_company_id                      IN customers.company_id%TYPE,
                                  p_person_id                       IN customers.person_id%TYPE,
                                  p_trading_account_ids             IN trading_account_id_tab,
                                  p_customer_registrations          IN customer_registrations_tab,
                                  p_is_deleted                      IN customers.is_deleted%TYPE,
                                  p_assigned_agent_identity         IN customers.assigned_agent_identity%TYPE,
                                  p_assigned_sales_trdr_identity    IN customers.assigned_sales_trader_identity%TYPE,
                                  p_bi_segmentation                 IN customers.bi_segmentation%TYPE,
                                  p_is_potential_premium            IN customers.is_potential_premium%TYPE,
                                  p_trust_type                      IN customers.trust_type%TYPE,
                                  p_lead_office                     IN customers.lead_office%TYPE,
                                  p_assigned_agent_identity_date    IN customers.assigned_agent_identity_date%TYPE,
                                  p_last_remediation_date           IN customers.last_remediation_date%TYPE,
                                  p_next_remediation_date           IN customers.next_remediation_date%TYPE,
                                  p_next_remediation_alert_date     IN customers.next_remediation_alert_date%TYPE,
                                  p_online_declaration              IN customers.online_declaration%TYPE
                                  ,p_block_marketing                IN customers.block_marketing%TYPE
                                  ,p_is_customer_segregated         IN customers.is_customer_segregated%TYPE
                                  ,p_referrer_reference             IN customers.referrer_reference%TYPE
                                  ,p_full_remediation_required      IN customers.Full_Remediation_Required%TYPE
                                  ,p_fatca_status                   IN customers.fatca_status%TYPE
                                  ,p_fatca_assessment_date          IN customers.fatca_assessment_date%TYPE
                                  ,p_remediation_enabled            IN customers.Remediation_Enabled%TYPE
                                  ,p_market_countrpart_partner_id   IN customers.Market_Counterparty_Partner_Id%TYPE
                                  ,p_speedbet_opt_out               IN customers.Speedbet_Opt_Out%TYPE
                                  ,p_speedbet_opt_out_status_date   IN customers.Speedbet_Opt_Out_Status_Date%TYPE
                                  ,p_is_binaries_opt_out            IN customers.is_binaries_opt_out%TYPE
                                  ,p_binaries_opt_out_status_date   IN customers.binaries_opt_out_status_date%TYPE
                                  ,p_perm_agent_identity            IN customers.perm_agent_identity%TYPE
                                  ,p_perm_agent_identity_date       IN customers.perm_agent_identity_date%TYPE
                                  ,p_record_source                  IN customers.record_source%TYPE DEFAULT 'CM'
                                  ,p_source_customer_id             IN customers.source_customer_id%TYPE DEFAULT NULL
                                  ,p_is_cfd_provider                IN customers.is_cfd_provider%TYPE
                                  ,p_tax_declarations               IN tax_declaration_tab
                                  ,p_customer_apprprtnss_info       IN customer_apprprtnss_info_tab
                                  ,p_customer_enquiries             IN customer_enquiry_tab
								                  ,p_lead_profile_country           IN customers.lead_profile_country%TYPE
								                  ,p_is_profiling_allowed		        IN customers.is_profiling_allowed%TYPE
                                  ,p_lifecycle_status               IN customers.lifecycle_status%TYPE
                                  ,p_customer_tax_germany           IN customer_tax_germany_tab
                                  ,p_customer_segregations          IN customer_segregations_tab
                                  ,p_customer_reg_clsfctns          IN customer_reg_clsfctns_tab
                                  ,p_customer_pro_opt_up            IN customer_pro_opt_up_tab
                                  ,p_last_trade_attestation_date    IN customers.last_trade_attestation_date%TYPE
                                  ,p_customer_reg_experience        IN customer_reg_experience_tab
                                  ,p_customer_email_preferences     IN customer_email_preferences_tab
                                  ,p_customer_link_id               IN customers.customer_link_id%TYPE
                                  ,p_customer_status_value          IN customers.customer_status_value%TYPE
                                  ,p_customer_status_update_time    IN customers.customer_status_update_time%TYPE
                                  ,p_customer_loyalty_schemes       IN customer_loyalty_schemes_tab
                                  ,p_customer_ddo_history           IN customer_ddo_history_tab
                                  ,p_customer_isa_funding_allowance IN customer_isa_funding_allowance_tab
                                  ,p_customer_marketing_preferences IN customer_marketing_preferences_tab
                                  ,p_is_vip_client                  IN customers.is_vip_client%TYPE
                                  ,p_is_client_staff                IN customers.is_client_staff%TYPE
                                  ,p_is_tax_id_required             IN customers.is_tax_id_required%TYPE
                                  ,p_customer_vulnerability         IN customer_vulnerability_tab
                                  ,p_customer_knowledge_assessments IN customer_knowledge_assessment_tab
								);
    -- ===================================================================================
    -- get_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of stubbed id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed id's to return (Optional - not set returns all)
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_ids (p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR;
    /***** Begin Modification for V2.5 Ber-671 *****/
    -- ===================================================================================
    -- get_refr_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of Referrer stubbed id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed id's to return (Optional - not set returns all)
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    FUNCTION get_refr_stubbed_ids (p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR;
    /***** End Modification for V2.5 *****/

    PROCEDURE put_dioceses(p_user                      IN german_dioceses.created_by%TYPE,
                           p_effective_start_timestamp IN german_dioceses.effective_start_timestamp%TYPE,
                           p_german_dioceses           IN german_dioceses_tab);


END nrg_customer;
/