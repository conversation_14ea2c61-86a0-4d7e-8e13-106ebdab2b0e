DROP TYPE ct_corporate_action_cash_dividend_tab FORCE;
DROP TYPE ct_corporate_action_cash_dividend_obj FORCE;


CREATE OR REPLACE TYPE ct_corporate_action_cash_dividend_obj IS OBJECT (
cash_dividend_id                          NUMBER,
instrument_code                           VARCHAR2(50),
instrument_currency                       VARCHAR2(3),
instrument_currency_fractional_part_ratio NUMBER,
product_wrapper_code                      VARCHAR2(50),
corporate_action_code                     VARCHAR2(50),
corporate_action_execution_type           VARCHAR2(100),
corporate_action_execution_date           DATE,
payment_date                              DATE,
dividend_gross_amount                     NUMBER,
dividend_amount_currency                  VARCHAR2(3),
dividend_amount_fractional_part_ratio     NUMBER,
trading_account_id                        NUMBER,
source_amount                             NUMBER,
source_currency                           VARCHAR2(3),
target_amount_core                        NUMBER,
target_currency                           VARCHAR2(3),
fx_rate_core                              NUMBER,
fx_banding                                NUMBER,
conversion_fee                            NUMBER,
conversion_fee_currency                   VARCHAR2(3),
quantity                                  NUMBER,
original_dividend_amount_currency         VARCHAR2(3),
original_source_gross_amount              NUMBER,
corporate_action_official_type            VARCHAR2(200),
dividend_tax_name                         VARCHAR2(100),
tax_country                               VARCHAR2(100),
us_withholding_tax_reduction              VARCHAR2(100),
is_tax_rate_residency_dependent           VARCHAR2(3),
dividend_tax_rate                         NUMBER,
source_gross_amount                       NUMBER,
tax_amount                                NUMBER,
period                                    VARCHAR2(50),
target_amount_customer                    NUMBER,
fx_rate_customer                          NUMBER,
stock_dividend_rate                       NUMBER,
stock_dividend_price                      NUMBER,
trading_account_type                      VARCHAR2(20),
custodian_tax_rate                        NUMBER,
custodian_amount                          NUMBER,
mutual_funds_class                        VARCHAR2(50),
dividend_amount_per_unit                  NUMBER,
total_amount                              NUMBER,
total_distribution_amount                 NUMBER,
total_equalisation_amount                 NUMBER,
total_group2_quantity                     NUMBER,
total_group1_quantity                     NUMBER,
dividend_period_startdate                 DATE,
dividend_period_enddate                   DATE,
dividend_period_days                      NUMBER,
is_accumulation_unit                      VARCHAR2(3)
);
/