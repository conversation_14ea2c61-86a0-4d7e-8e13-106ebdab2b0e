CREATE OR REPLACE PACKAGE BODY nrg_price_plan AS

  gc_version CONSTANT VARCHAR2(3) := '1.0';
  gc_cr      CONSTANT VARCHAR2(1) := chr(10);
  gc_true    CONSTANT PLS_INTEGER := 1;
  gc_false   CONSTANT PLS_INTEGER := 0;
  gc_default_timestamp TIMESTAMP := to_timestamp('01-Jan-1970',
                                                 'DD-Mon-YYYY');

  -- ===================================================================================
  --
  --     procedure to customer price plan to table
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_price_plan(p_customer_price_plan IN customer_price_plan_obj) IS
    lv_row_exists          NUMBER;
    lv_insert_row          NUMBER;
    lv_same_id_pending_row NUMBER;
    lv_same_id             NUMBER;
  BEGIN
    logger.logger.set_module('put_customer_price_plan');

    -- if this plan is an active plan
    --    if there is an active plan there (and ids dont match) wipe the status and move to history
    --    if it was previously a pending plan then update

    -- if this is a pending plan
    --    if there is a pending plan already (and ids dont match) wipe the status and move to history
    --
    -- notice that if there is a pending plan, you may not be able to insert a record

    lv_insert_row          := 0;
    lv_same_id_pending_row := 0;

    SELECT COUNT(customer_plan_id)
      INTO lv_row_exists
      FROM customer_price_plans
     WHERE (customer_id = p_customer_price_plan.customer_id)
       AND (legal_entity = p_customer_price_plan.legal_entity OR
           legal_entity IS NULL)
       AND price_plan_type = p_customer_price_plan.price_plan_type
       AND customer_plan_id != p_customer_price_plan.customer_plan_id;

    SELECT COUNT(customer_plan_id)
      INTO lv_same_id_pending_row
      FROM customer_price_plans
     WHERE customer_plan_id = p_customer_price_plan.customer_plan_id
       AND price_plan_type = 'PENDING_PLAN';

    SELECT COUNT(customer_plan_id)
      INTO lv_same_id
      FROM customer_price_plans
     WHERE customer_plan_id = p_customer_price_plan.customer_plan_id;
     --  AND price_plan_type != 'PENDING_PLAN';

    IF (lv_row_exists IS NOT NULL AND lv_row_exists != 0) THEN
      IF (lv_same_id_pending_row = 0) THEN
        -- no pending plan
        INSERT INTO customer_price_plans_h
          SELECT customer_plan_id,
                 customer_id,
                 legal_entity,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp,
                 price_plan_name,
                 price_plan_type,
                 tier_id,
                 tier_name,
                 pending_plan_reason,
                 fee_override,
                 currency_override,
                 plan_request_time,
                 plan_active_time,
                 p_customer_price_plan.plan_end_time AS plan_end_time,
                 work_item_id,
                 SYSDATE                             AS effective_end_timestamp
            FROM customer_price_plans
           WHERE (customer_id = p_customer_price_plan.customer_id)
             AND (legal_entity = p_customer_price_plan.legal_entity OR
                 legal_entity IS NULL)
             AND price_plan_type = p_customer_price_plan.price_plan_type
             AND customer_plan_id != p_customer_price_plan.customer_plan_id;

        -- should insert a new record in main table since there is no pending plan
        IF (lv_same_id IS NULL OR lv_same_id = 0) THEN
          lv_insert_row := 1;
        END IF;
      ELSE
        -- has pending plan
        IF (p_customer_price_plan.price_plan_type = 'ACTIVE_PLAN') THEN
          -- if it is an active plan, then the historical should have two records
          INSERT INTO customer_price_plans_h
            SELECT customer_plan_id,
                   customer_id,
                   legal_entity,
                   logical_load_timestamp,
                   created_by,
                   create_timestamp,
                   updated_by,
                   update_timestamp,
                   effective_start_timestamp,
                   price_plan_name,
                   price_plan_type,
                   tier_id,
                   tier_name,
                   pending_plan_reason,
                   fee_override,
                   currency_override,
                   plan_request_time,
                   plan_active_time,
                   p_customer_price_plan.plan_end_time AS plan_end_time,
                   work_item_id,
                   SYSDATE                             AS effective_end_timestamp
              FROM customer_price_plans
             WHERE (customer_id = p_customer_price_plan.customer_id OR
                   customer_id IS NULL)
               AND (legal_entity = p_customer_price_plan.legal_entity OR
                   legal_entity IS NULL)
               AND ((price_plan_type = 'ACTIVE_PLAN' AND
                   customer_plan_id !=
                   p_customer_price_plan.customer_plan_id AND
                   customer_id = p_customer_price_plan.customer_id) OR
                   (price_plan_type = 'PENDING_PLAN' AND
                   customer_plan_id =
                   p_customer_price_plan.customer_plan_id));

          -- mark the current pending plan as an old one
          UPDATE customer_price_plans
             SET update_timestamp       = SYSDATE,
                 logical_load_timestamp = p_customer_price_plan.logical_load_timestamp,
                 price_plan_type        = 'ACTIVE_PLAN',
                 plan_active_time       = p_customer_price_plan.plan_active_time
           WHERE customer_plan_id = p_customer_price_plan.customer_plan_id;
        ELSE
          -- mark the current pending plan as an old one, which means cancellation
          -- in this case, you cannot active a plan without cancelling the pending plan first
          -- if it is a pending plan, then the historical should have only one record

          INSERT INTO customer_price_plans_h
            SELECT customer_plan_id,
                   customer_id,
                   legal_entity,
                   logical_load_timestamp,
                   created_by,
                   create_timestamp,
                   updated_by,
                   update_timestamp,
                   effective_start_timestamp,
                   price_plan_name,
                   price_plan_type,
                   tier_id,
                   tier_name,
                   pending_plan_reason,
                   fee_override,
                   currency_override,
                   plan_request_time,
                   plan_active_time,
                   p_customer_price_plan.plan_end_time,
                   work_item_id,
                   SYSDATE
              FROM customer_price_plans
             WHERE (customer_id = p_customer_price_plan.customer_id OR
                   customer_id IS NULL)
               AND (legal_entity = p_customer_price_plan.legal_entity OR
                   legal_entity IS NULL)
               AND price_plan_type = 'PENDING_PLAN'
               AND customer_plan_id =
                   p_customer_price_plan.customer_plan_id;

          UPDATE customer_price_plans
             SET price_plan_type           = 'OLD_' || price_plan_type,
                 effective_start_timestamp = SYSDATE,
                 plan_end_time             = SYSDATE
           WHERE (customer_id = p_customer_price_plan.customer_id OR
                 customer_id IS NULL)
             AND (legal_entity = p_customer_price_plan.legal_entity OR
                 legal_entity IS NULL)
             AND price_plan_type = p_customer_price_plan.price_plan_type
             AND customer_plan_id = p_customer_price_plan.customer_plan_id;
        END IF;
      END IF;
      -- mark the current active plan as an old one
      UPDATE customer_price_plans
         SET price_plan_type           = 'OLD_' || price_plan_type,
             effective_start_timestamp = SYSDATE,
             plan_end_time             = SYSDATE
       WHERE (customer_id = p_customer_price_plan.customer_id)
         AND (legal_entity = p_customer_price_plan.legal_entity OR
             legal_entity IS NULL)
         AND price_plan_type = p_customer_price_plan.price_plan_type
         AND customer_plan_id != p_customer_price_plan.customer_plan_id;
    ELSE
      -- should have a new record
      IF (lv_same_id IS NULL OR lv_same_id = 0) THEN
        lv_insert_row := 1;
      END IF;
    END IF;

    IF (lv_insert_row = 1) THEN
      INSERT INTO customer_price_plans
        (customer_plan_id,
         customer_id,
         legal_entity,
         logical_load_timestamp,
         created_by,
         create_timestamp,
         updated_by,
         update_timestamp,
         effective_start_timestamp,
         price_plan_name,
         price_plan_type,
         tier_id,
         tier_name,
         pending_plan_reason,
         fee_override,
         currency_override,
         plan_request_time,
         plan_active_time,
         plan_end_time,
         work_item_id)
      VALUES
        (p_customer_price_plan.customer_plan_id,
         p_customer_price_plan.customer_id,
         p_customer_price_plan.legal_entity,
         p_customer_price_plan.logical_load_timestamp,
         p_customer_price_plan.created_by,
         SYSDATE,
         p_customer_price_plan.updated_by,
         SYSDATE,
         p_customer_price_plan.effective_start_timestamp,
         p_customer_price_plan.price_plan_name,
         p_customer_price_plan.price_plan_type,
         p_customer_price_plan.tier_id,
         p_customer_price_plan.tier_name,
         p_customer_price_plan.pending_plan_reason,
         p_customer_price_plan.fee_override,
         p_customer_price_plan.currency_override,
         p_customer_price_plan.plan_request_time,
         p_customer_price_plan.plan_active_time,
         p_customer_price_plan.plan_end_time,
         p_customer_price_plan.work_item_id);
    END IF;

    logger.logger.set_module(NULL);
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004,
                              logger.logger.error_backtrace);
  END put_customer_price_plan;

  -- ===================================================================================
  --
  --     procedure to add multiple customer price plan to table
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_price_plans(p_customer_price_plans IN customer_price_plan_tab) IS

  BEGIN
    logger.logger.set_module('put_customer_price_plans');
    FOR i IN p_customer_price_plans.first .. p_customer_price_plans.last LOOP
      put_customer_price_plan(p_customer_price_plans(i));
    END LOOP;
    logger.logger.set_module(NULL);
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004,
                              logger.logger.error_backtrace);

  END put_customer_price_plans;

END nrg_price_plan;
/