CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_order
AS
    -- ===================================================================================
    -- NRG_ORDER
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the orders model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     05/09/2011   Sanket Mittal      1.0    Creation
    --     14/10/2011   Manoj Kumar        1.1    Modified the put_order method
    --     19/10/2011   Manoj Kumar        1.2    Modified the put_order method for concurrent writes
    --     21/10/2011   Sanket Mittal      1.3    Modified to remove the business rules for enums
    --     21/10/2011   Sanket Mittal      1.4    Included the new field order_type
    --     02/11/2011   Paul Flynn         1.5    Added MM Account ID Attribute
    --     04/11/2011   Manoj Kumar        1.6    Updated the comments and variables,removed remove_order method
    --     04/11/2011   Patrick Dinwiddy   1.7    Rename use_custom_code to use_custom_quote, applied UPPER() to controlled_order_type
    --     04/11/2011   Sanket Mittal      1.8    Included the call to populate order_type and controlled_order_type
    --     17/11/2011   Mark Gornicki      1.9    Changed the parameter name p_trading_account_code to p_trading_account_id
    --                                            and renamed column limit_is_trailing to is_limit_trailing.
    --                                            and added else clause to order type case statement
    --                                            bringing it into line with Trades
    --     18/11/2011   Mark Gornicki      2.0    Fixed bug in section for direct insert into history where the same parameter value
    --                                            p_is_ta_sspn_fr_trd_rvsl_ignrd was being inserted into 2 target columns by mistake.
    --                                            Also fixed spelling mistake on p_activation_time
    --     24/11/2011   Sanket Mittal      2.1    Included normalised_quantity
    --     25/11/2011   Sanket Mittal      2.2    Amended create_trade_stub to set BUSINESS_DATE to the gc_default_timestamp
    --                                            to ensure the column has a value for partitioning otherwise it will fail.
    --                                            Amended Put_Trade and Put_History to handle the processing for internal hedge trades.
    --                                            This is for MarketMaker data such that it would have no trading_account_id set and
    --                                            we must perform a lookup based on MarketMaker source ids, so set the correct
    --                                            Trading Account ID/Type combination for referential integrity
    --     29/11/2011   Sanket Mittal      2.3    Updated the business rule for normalised quantity
    --     01/12/2011   Sanket Mittal      2.4    Added logic to get the currency for TIQ instruments from MM using ref table
    --     08/12/2011   Patrick Dinwiddy   2.5    Included the call to populate quantity_designator
    --     08/12/2011   Sanket Mittal      2.6    Added the new parameter for trading_account_type
    --     12/12/2011   Sanket Mittal      2.7    Changed for requested, traded and normalised quantity
    --     13/12/2011   Sanket Mittal      2.8    Included two new paramters margin_requirement and margin_type
    --     20/12/2011   Sanket Mittal      2.9    Added the call to write positions and updated the chnage for trade time to make it static
    --     29/12/2011   Sanket Mittal      3.0    Added the parameter for Reporting Date on put_order
    --     04/12/2012   Sanket Mittal      3.1    Added the method get_order_ids
    --     26/01/2012   Mark Gornicki      3.2    The SB code for used to determine lv_normalised_quantity_ccy was misspelt
    --                                            resulting in null normalised quantity currency.'A-EVOH'  should be 'A-EOVH'
    --                                            Updated the calculation for normalised quantity
    --     27/01/2012   Mark Gornicki      3.3    Amended the calculation to default to the trading account currency if lv_trade_quantity_currency is null.
    --                                            populate new column normalised price
    --     15/02/2012   Manoj Kumar        3.4    Included the calculation for product_point_multiplier and product_fractional_part_ratio
    --     06/03/2012   Sanket Mittal      3.5    Added the fix for position deadlocking issues
    --     08/03/2012   Sanket Mittal      3.6    Updated to remove the fileds changed character, change reason code, change reason codifier, event time and publish time from the comparision for two order records
    --     10/04/2012   Sanket Mittal      3.7    Updated the lookup for trading accounts using the source account id where trading account id is passed as NULL
    --     16/05/2012   Mark Gornicki      3.8     Added code to create identity stubs
    --     06/11/2012   Sanket Mittal      3.9    Added Contingent Orders
    --     09/11/2012   Shanavaz Malayodu  4.0    Modified the Contingent Orders Section to remove the NOT IN Clause
    --     19/11/2012   Shanavaz Malayodu  4.1    Added parameters to put_order procedure
    --     03/01/2013   Sanket Mittal      4.2    Added new fields to put_orders
    --     15/01/2013   Sanket Mittal      4.3    Added sub routine to rec the stub id's and cleanup_contingent_orders to clean the history for contingent orders
    --     25/01/2013   Sanket Mittal      4.4    Updated the order type population for NG orders on put_order. Code was added back as NRG contingent order fix was delayed
    --     12/03/2013   Sanket Mittal      4.5    Updated the spec for P2 changes
    --     24/06/2013   Prachi Shah        4.6    Removed the comment on Normalized Quantity Currency. Updated the cod as per the new data lineage.
    --     19/07/2013   Prachi Shah        4.7    Updated get_order_id function to include version_number also as a return
    --     20/08/2013   Prachi Shah        4.8    Updated cleanup_contingent_orders to include the condition order_state  executed .
    --     21/08/2013   Ravi Shankar Gopal 4.8    Included Update statement to update Parent_order_statu of the Child orders in Contingency orders table.
    --     13/09/2013   Ravi Shankar Gopal 4.9    Removed NVL function from lcuv_result cursor in GET_ORDER_IDS Function.
    --     20/09/2013   Ravi Shankar Gopal 5.0    BER-627: Added new filter to update the Parent_Order_state of Child Orders
    --     14/11/2013   Adam Krasnicki     5.1    BER-721: Instrument type changed to 'Shares' from 'Companies'
    --     31/12/2013   Ravi Shankar Gopal 5.2    BER-781: Modified nrg_position.put_position procedure call
    --     04/02/2014   Adam Krasnicki     5.3    Shrink space statements commented in the cleanup_contingent_orders procedure due to locking issue
    --     19/02/2014   Adam Krasnicki     5.4    Added missing else statement in the case in put order procedure to deal with null or 'others' values of quantity designator
    --     05/03/2014   Adam Krasnicki     5.5    get_closed_executed_order_ids procedure added
    --     14/03/2014   Adam Krasnicki     5.6    get_stoploss_order_ids function added BER-907
    --     25/03/2014   Adam Krasnicki     5.7    Changes in put_order (order_type) BER-934
    --     15/05/2014   Adam Krasnicki     5.8    In the insert statement in the put_contingent_order procedure p_requested_quantity is inserted
    --                                            and 4 identities column added to contitingent_orders table
    --     15/05/2014   Adam Krasnicki     5.8    NULLIF function added to avoid division by zero
    --     10/06/2014   Adam Krasnicki     5.8    Prevent update of reporting_date in history table
    --     21/07/2014   Adam Krasnicki     5.8    New parameter to put_position added and set to YES
    --     28/07/2014   Adam Krasnicki     5.8    Handle incorrect values for Normalised CCY in MMCFD Orders (if > than 3 characters then set to NULL)
    --     05/09/2014   Adam Krasnicki     5.8    3 new columns added to ORDERS[_H] tables
    --     08/09/2014   Adam Krasnicki     5.8    Some minor correction done to enable move data to orders_h (SQL%ROWCOUNT removed)
    --     27/10/2014   Adam Krasnicki     5.9    Put_positions call for MM removed from package (it is called from put_trade procedure)
    --     29/01/2015   Adam Krasnicki     5.9    4 new cols added and one renamed in orders[_h] table, 4 nuew param in put_order proc
    --     12/03/2015   Adam Krasnicki     6.0    Prevent putting orders to history with the same version number
    --     13/03/2015   Adam Krasnicki     6.0    order_id_short_form and parent_order_id_short_form added to orders[_h]
    --     08/05/2015   Adam Krasnicki     6.1    put_order: two new parameters p_execution_type, p_assigned_to
    --     13/05/2015   Adam Krasnicki     6.1    func get_stubbed_ids, new condition added to exclude SPEEDBET stubs
    --     19/05/2015   Adam Krasnicki     6.1    put_order: record_source assigned to p_platform
    --     25/05/2015   Adam Krasnicki     6.1    func: get_stoploss_order_ids, get_closed_executed_order_ids modified to exclude SPEEDBET
    --     29/07/2015   Adam Krasnicki     6.2    put_orders: new parameter p_close_sub_reason
    --     30/09/2015   Sanket Mittal      6.3    put_orders: new parameters p_trading_scope, p_allocation_order_id, p_allocation_trading_risk_schema and p_managed_order_info. Added new procedure put_orders_managed_info
    --     23/12/2015   Sanket Mittal      6.4    put_orders: Updated primary keys for order managed information BER-2244
    --     20/01/2016   Sanket Mittal      6.5    put_orders: Added new parameters for binary p_bo_type, p_bo_settle_time, p_bo_tenor, p_bo_strike_price, p_bo_strike_price_additional
    --     07/04/2016   Sanket Mittal      6.6    put_contingent_order: Removed the stored procedure and migrated the logic to nrg_contingent_order package
    --     07/04/2016   Sanket Mittal      6.7    put_contingent_order: Renamed NRG_CONTINGENT_ORDERS to ODS_CONTINGENT_ORDERS
    --     08/06/2016   Sanket Mittal      6.8    put_orders new attributes added
    --     26/07/2016   Sanket Mittal      6.9    Added additional parameter for PUT_CONTINGENT_ORDER BER-2770
    --     28/07/2016   Sanket Mittal      7.0    create_session_stub to include channel id BER-2778
    --     22/08/2016   Sanket Mittal      7.1    BER-2828 New Attribute: OrderType
    --     22/08/2016   Sanket Mittal      7.2    BER-2844 Knockouts Normalisation - NRG_ORDER - put_order
    --     18/10/2016   Sanket Mittal      7.3    BER-2894 Changed the get_order_type Function
    --     19/12/2016   Sanket Mittal      7.4    BER-3156 SWS 36 - BI_ODS.ORDERS - New Attributes
    --     16/03/2017   Sanket Mittal      7.5    BER-3432 SWS 37 - BI_ODS.ORDERS - New Attribute
    --     18/07/2017   Sanket Mittal      7.6    BER-3781 NRG_ORDER - bi_ods.orders - changes
    --     16/05/2018   Patrick Dinwiddy   7.7    BER-4595 New order close comment attributes
    --     30/05/2018   Deepak Rajurkar    7.7    BER-4435  - BI_ODS.ORDERS - New Attribute
    --     04/07/2018   Patrick Dinwiddy   7.8    BER-4735  - BI_ODS.ORDERS - New Attribute migrated_order_id
    --     20/11/2018   Patrick Dinwiddy   7.9    BER-4910  - BI_ODS.ORDERS - New Attribute client_party_id
    --     29/04/2019   Patrick Dinwiddy   8.0    JCS-10569 - BI_ODS.ORDERS - New Attributes
    -- ===================================================================================
    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================

    gc_version            CONSTANT VARCHAR2(3) := '8.0';
    gc_cr                 CONSTANT VARCHAR2(1) := CHR(10);
    gc_true               CONSTANT PLS_INTEGER := 1;
    gc_false              CONSTANT PLS_INTEGER := 0;
    gc_default_timestamp  CONSTANT TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the order_trade_to_close record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_trade                     This is the old version of the order trade to close
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_history (p_old_trade               order_trades_to_close%ROWTYPE,
                           p_effective_end_timestamp order_trades_to_close_h.effective_end_timestamp%TYPE,
                           p_action                  order_trades_to_close_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO order_trades_to_close_h(platform,
                                         order_id,
                                         open_trade_to_close_order_id,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp)
                                  VALUES(p_old_trade.platform,
                                         p_old_trade.order_id,
                                         p_old_trade.open_trade_to_close_order_id,
                                         p_old_trade.logical_load_timestamp,
                                         p_old_trade.created_by,
                                         p_old_trade.create_timestamp,
                                         p_old_trade.updated_by,
                                         p_old_trade.update_timestamp,
                                         p_old_trade.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE order_trades_to_close_h
        SET logical_load_timestamp = p_old_trade.logical_load_timestamp,
            created_by = p_old_trade.created_by,
            create_timestamp = p_old_trade.create_timestamp,
            updated_by = p_old_trade.updated_by,
            update_timestamp = p_old_trade.update_timestamp,
            effective_end_timestamp = p_effective_end_timestamp,
            action = p_action,
            action_timestamp = SYSTIMESTAMP
        WHERE platform = p_old_trade.platform AND
              order_id = p_old_trade.order_id AND
              open_trade_to_close_order_id = p_old_trade.open_trade_to_close_order_id AND
              effective_start_timestamp = p_old_trade.effective_start_timestamp;
    END;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the order_trade_to_close record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_managed_order_info            This is the old version of the managed order info
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_history (p_old_order_managed_info               orders_managed_information%ROWTYPE,
                           p_effective_end_timestamp              orders_managed_information_h.effective_end_timestamp%TYPE,
                           p_action                               orders_managed_information_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO orders_managed_information_h(platform,
                                               order_id,
                                               managed_order_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               effective_end_timestamp,
                                               action,
                                               action_timestamp,
                                               trading_account_id,
                                               trading_account_type,
                                               requested_quantity,
                                               execution_price,
                                               close_reason)
                                        VALUES(p_old_order_managed_info.platform,
                                               p_old_order_managed_info.order_id,
                                               p_old_order_managed_info.managed_order_id,
                                               p_old_order_managed_info.logical_load_timestamp,
                                               p_old_order_managed_info.created_by,
                                               p_old_order_managed_info.create_timestamp,
                                               p_old_order_managed_info.updated_by,
                                               p_old_order_managed_info.update_timestamp,
                                               p_old_order_managed_info.effective_start_timestamp,
                                               p_effective_end_timestamp,
                                               p_action,
                                               SYSTIMESTAMP,
                                               p_old_order_managed_info.trading_account_id,
                                               p_old_order_managed_info.trading_account_type,
                                               p_old_order_managed_info.requested_quantity,
                                               p_old_order_managed_info.execution_price,
                                               p_old_order_managed_info.close_reason);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE orders_managed_information_h
        SET logical_load_timestamp = p_old_order_managed_info.logical_load_timestamp,
            created_by = p_old_order_managed_info.created_by,
            create_timestamp = p_old_order_managed_info.create_timestamp,
            updated_by = p_old_order_managed_info.updated_by,
            update_timestamp = p_old_order_managed_info.update_timestamp,
            effective_start_timestamp = p_old_order_managed_info.effective_start_timestamp,
            effective_end_timestamp = p_effective_end_timestamp,
            action = p_action,
            action_timestamp = SYSTIMESTAMP,
            managed_order_id = p_old_order_managed_info.managed_order_id,
            requested_quantity = p_old_order_managed_info.requested_quantity,
            execution_price = p_old_order_managed_info.execution_price
        WHERE platform = p_old_order_managed_info.platform AND
              order_id = p_old_order_managed_info.order_id AND
              trading_account_id = p_old_order_managed_info.trading_account_id AND
              trading_account_type = p_old_order_managed_info.trading_account_type AND
              effective_start_timestamp = p_old_order_managed_info.effective_start_timestamp AND
              close_reason = p_old_order_managed_info.close_reason;
    END;

    -- ===================================================================================
    -- put_open_trade_to_close
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put the order trade to close
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_logical_load_timestamp        Logical Load Timestamp
    --      p_user                          User
    --      p_effective_start_timestamp     Effective_start_timestamp
    --      p_old_effctv_strt_tmstmp        Old Effective start timestamp
    --      p_platform                      Platform
    --      p_order_id                      Order id
    --      p_rqustd_open_trade_to_close    Trade ids
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_open_trade_to_close(p_logical_load_timestamp      IN order_trades_to_close.logical_load_timestamp%TYPE,
                                      p_user                        IN order_trades_to_close.created_by%TYPE,
                                      p_effective_start_timestamp   IN order_trades_to_close.effective_start_timestamp%TYPE,
                                      p_old_effctv_strt_tmstmp      IN order_trades_to_close.effective_start_timestamp%TYPE,
                                      p_platform                    IN order_trades_to_close.platform%TYPE,
                                      p_order_id                    IN order_trades_to_close.order_id%TYPE,
                                      p_rqustd_open_trade_to_close  IN trade_id_tab) IS

      TYPE trade_to_close_tab IS TABLE OF order_trades_to_close%ROWTYPE;

      lv_deleted_trades trade_to_close_tab;

    BEGIN
      logger.logger.set_module('nrg_order.put_open_trade_to_close');

      CASE
        WHEN p_old_effctv_strt_tmstmp IS NULL OR p_old_effctv_strt_tmstmp <= p_effective_start_timestamp THEN
          FOR lv_cnt IN 1..p_rqustd_open_trade_to_close.COUNT LOOP

            BEGIN
              INSERT INTO order_trades_to_close(platform,
                                               order_id,
                                               open_trade_to_close_order_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp)
                                        VALUES(p_platform,
                                               p_order_id,
                                               p_rqustd_open_trade_to_close(lv_cnt).trade_id,
                                               p_logical_load_timestamp,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_effective_start_timestamp);

            EXCEPTION
              WHEN DUP_VAL_ON_INDEX THEN
                --
                --Since the trade is already linked to the order no need to update it
                --

                NULL;
            END;

          END LOOP;

          --
          --Find out if there is any trade that has been deleted from the list
          --
          SELECT *
          BULK COLLECT INTO lv_deleted_trades
          FROM order_trades_to_close
          WHERE platform = p_platform AND
                order_id = p_order_id AND
                open_trade_to_close_order_id NOT IN (SELECT trade_id
                                                     FROM TABLE(CAST(p_rqustd_open_trade_to_close AS trade_id_tab)));

          IF lv_deleted_trades IS NOT NULL THEN

            FOR lv_cnt IN 1..lv_deleted_trades.COUNT LOOP
              put_history(p_old_trade               => lv_deleted_trades(lv_cnt),
                          p_effective_end_timestamp => p_effective_start_timestamp,
                          p_action                  => 'D');
            END LOOP;

            FORALL lv_cnt IN 1..lv_deleted_trades.COUNT
              DELETE order_trades_to_close
              WHERE platform = lv_deleted_trades(lv_cnt).platform AND
                    order_id = lv_deleted_trades(lv_cnt).order_id AND
                    open_trade_to_close_order_id = lv_deleted_trades(lv_cnt).open_trade_to_close_order_id;

          END IF;

        WHEN p_old_effctv_strt_tmstmp > p_effective_start_timestamp THEN

          --
          --Check for the trade to close if they were created before the previous message
          --

          FORALL lv_cnt IN 1..p_rqustd_open_trade_to_close.COUNT
            UPDATE order_trades_to_close
            SET logical_load_timestamp = p_logical_load_timestamp,
                updated_by = p_user,
                update_timestamp = SYSTIMESTAMP,
                effective_start_timestamp = p_effective_start_timestamp
            WHERE order_id = p_order_id AND
                  platform = p_platform AND
                  open_trade_to_close_order_id = p_rqustd_open_trade_to_close(lv_cnt).trade_id AND
                  effective_start_timestamp > p_effective_start_timestamp;

          --
          --Select and put the trades which are in the object but not in the current table to history
          --

          SELECT p_platform platform,
                 p_order_id order_id,
                 trade_id,
                 p_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp
          BULK COLLECT INTO lv_deleted_trades
          FROM TABLE(CAST(p_rqustd_open_trade_to_close AS trade_id_tab))
          WHERE trade_id NOT IN (SELECT open_trade_to_close_order_id
                                 FROM order_trades_to_close
                                 WHERE platform = p_platform AND
                                       order_id = p_order_id);

          IF lv_deleted_trades IS NOT NULL THEN
            FOR lv_cnt IN 1..lv_deleted_trades.COUNT LOOP
              put_history(p_old_trade               => lv_deleted_trades(lv_cnt),
                          p_effective_end_timestamp => p_effective_start_timestamp,
                          p_action                  => 'I');
            END LOOP;
          END IF;
      END CASE;

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- put_orders_managed_info
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put the orders managed information
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_logical_load_timestamp        Logical Load Timestamp
    --      p_user                          User
    --      p_effective_start_timestamp     Effective_start_timestamp
    --      p_old_effctv_strt_tmstmp        Old Effective start timestamp
    --      p_platform                      Platform
    --      p_order_id                      Order id
    --      p_managed_orders                Managed Orders Information
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_orders_managed_info(p_logical_load_timestamp      IN orders_managed_information.logical_load_timestamp%TYPE,
                                      p_user                        IN orders_managed_information.created_by%TYPE,
                                      p_effective_start_timestamp   IN orders_managed_information.effective_start_timestamp%TYPE,
                                      p_old_effctv_strt_tmstmp      IN orders_managed_information.effective_start_timestamp%TYPE,
                                      p_platform                    IN orders_managed_information.platform%TYPE,
                                      p_order_id                    IN orders_managed_information.order_id%TYPE,
                                      p_order_managed_info          IN orders_managed_info_tab,
                                      p_operation_type              VARCHAR2)
    IS

      TYPE order_managed_info_tab IS TABLE OF orders_managed_information%ROWTYPE;

      lv_deleted_managed_info order_managed_info_tab;

      lv_updated_managed_info order_managed_info_tab;

      lv_old_update_records order_managed_info_tab;
      lv_old_delete_records order_managed_info_tab;

    BEGIN
      logger.logger.set_module('nrg_order.put_orders_managed_info');

      --
      --Create Stubs
      --

      FOR lv_cnt IN 1..p_order_managed_info.COUNT LOOP
        nrg_trading_account.create_trading_account_stub(p_trading_account_id => p_order_managed_info(lv_cnt).trading_account_id,
                                                        p_trading_account_type => p_order_managed_info(lv_cnt).trading_account_type,
                                                        p_user => p_user,
                                                        p_effective_start_timestamp => p_effective_start_timestamp,
                                                        p_logical_load_timestamp => p_logical_load_timestamp);

        IF p_order_managed_info(lv_cnt).managed_order_id IS NOT NULL THEN
          nrg_order.create_order_stub(p_user => p_user,
                                      p_platform => p_platform,
                                      p_order_id => p_order_managed_info(lv_cnt).managed_order_id,
                                      p_effective_start_timestamp => p_effective_start_timestamp,
                                      p_logical_load_timestamp => p_logical_load_timestamp);
        END IF;
      END LOOP;

      CASE
        WHEN p_operation_type = 'I' THEN

          FOR lv_cnt IN 1..p_order_managed_info.COUNT LOOP

            BEGIN
              INSERT INTO orders_managed_information(platform,
                                                     order_id,
                                                     trading_account_id,
                                                     trading_account_type,
                                                     logical_load_timestamp,
                                                     created_by,
                                                     create_timestamp,
                                                     updated_by,
                                                     update_timestamp,
                                                     effective_start_timestamp,
                                                     managed_order_id,
                                                     requested_quantity,
                                                     execution_price,
                                                     close_reason)
                                              VALUES(p_platform,
                                                     p_order_id,
                                                     p_order_managed_info(lv_cnt).trading_account_id,
                                                     p_order_managed_info(lv_cnt).trading_account_type,
                                                     p_logical_load_timestamp,
                                                     p_user,
                                                     SYSTIMESTAMP,
                                                     p_user,
                                                     SYSTIMESTAMP,
                                                     p_effective_start_timestamp,
                                                     p_order_managed_info(lv_cnt).managed_order_id,
                                                     p_order_managed_info(lv_cnt).requested_quantity,
                                                     p_order_managed_info(lv_cnt).execution_price,
                                                     nvl(p_order_managed_info(lv_cnt).close_reason,'NONE'));

            EXCEPTION
              WHEN DUP_VAL_ON_INDEX THEN

                NULL;

            END;

          END LOOP;


        WHEN p_operation_type = 'U' THEN

          --
          --Identify old records which have been updated
          --

          SELECT a.*
          BULK COLLECT INTO lv_updated_managed_info
          FROM orders_managed_information a,
              TABLE(CAST(p_order_managed_info AS orders_managed_info_tab)) b
          WHERE a.order_id = p_order_id AND
                a.trading_account_id = b.trading_account_id AND
                a.trading_account_type = b.trading_account_type AND
                a.platform = p_platform AND
                a.close_reason = nvl(b.close_reason,'NONE') AND
                (nrg_common.has_value_changed(b.managed_order_id, a.managed_order_id) = 1 OR
                 nrg_common.has_value_changed(b.requested_quantity, a.requested_quantity) = 1 OR
                 nrg_common.has_value_changed(b.execution_price, a.execution_price) = 1);

          --
          --Write old records to history
          --

          FOR lv_cnt IN 1..lv_updated_managed_info.COUNT LOOP
            put_history(p_old_order_managed_info               => lv_updated_managed_info(lv_cnt),
                        p_effective_end_timestamp              => p_effective_start_timestamp,
                        p_action                               => 'U');
          END LOOP;

          --
          --Identify records which have been deleted
          --

          SELECT a.*
          BULK COLLECT INTO lv_deleted_managed_info
          FROM orders_managed_information a,
              TABLE(CAST(p_order_managed_info AS orders_managed_info_tab)) b
          WHERE a.order_id = p_order_id AND
                a.trading_account_id = b.trading_account_id (+) AND
                a.trading_account_type = b.trading_account_type (+) AND
                a.platform = p_platform AND
                a.close_reason = nvl(b.close_reason,'NONE') AND
                b.trading_account_id IS NULL;

          --
          --Write deleted records to history
          --

          FOR lv_cnt IN 1..lv_deleted_managed_info.COUNT LOOP
            put_history(p_old_order_managed_info               => lv_deleted_managed_info(lv_cnt),
                        p_effective_end_timestamp              => p_effective_start_timestamp,
                        p_action                               => 'D');

            --
            --Delete records
            --

            DELETE orders_managed_information
            WHERE order_id = p_order_id AND
                  platform = p_platform AND
                  trading_account_id = lv_deleted_managed_info(lv_cnt).trading_account_id AND
                  trading_account_type = lv_deleted_managed_info(lv_cnt).trading_account_type AND
                  close_reason = lv_deleted_managed_info(lv_cnt).close_reason;

          END LOOP;

          --
          --Update/Insert Records
          --

          MERGE INTO orders_managed_information old_version
          USING (SELECT * FROM TABLE(CAST(p_order_managed_info AS orders_managed_info_tab))) new_version
          ON (old_version.order_id = p_order_id AND
              old_version.platform = p_platform AND
              old_version.trading_account_id = new_version.trading_account_id AND
              old_version.trading_account_type = new_version.trading_account_type AND
              old_version.close_reason = nvl(new_version.close_reason, 'NONE'))
          WHEN MATCHED THEN
            UPDATE
            SET old_version.logical_load_timestamp = p_logical_load_timestamp,
                old_version.update_timestamp = SYSTIMESTAMP,
                old_version.updated_by = p_user,
                old_version.effective_start_timestamp = p_effective_start_timestamp,
                old_version.managed_order_id = new_version.managed_order_id,
                old_version.requested_quantity = new_version.requested_quantity,
                old_version.execution_price = new_version.execution_price
          WHEN NOT MATCHED THEN
            INSERT (platform,
                    order_id,
                    trading_account_id,
                    trading_account_type,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    effective_start_timestamp,
                    managed_order_id,
                    requested_quantity,
                    execution_price,
                    close_reason )
            VALUES (p_platform,
                    p_order_id,
                    new_version.trading_account_id,
                    new_version.trading_account_type,
                    p_logical_load_timestamp,
                    p_user,
                    SYSTIMESTAMP,
                    p_user,
                    SYSTIMESTAMP,
                    p_effective_start_timestamp,
                    new_version.managed_order_id,
                    new_version.requested_quantity,
                    new_version.execution_price,
                    nvl(new_version.close_reason, 'NONE'));

          NULL;

        WHEN p_operation_type = 'OU' THEN

          --
          --Identify reords which should be written to history
          --

          SELECT p_platform,
                 p_order_id,
                 b.trading_account_id,
                 b.trading_account_type,
                 p_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp,
                 b.managed_order_id,
                 b.requested_quantity,
                 b.execution_price,
                 b.close_reason
          BULK COLLECT INTO lv_updated_managed_info
          FROM orders_managed_information a,
              TABLE(CAST(p_order_managed_info AS orders_managed_info_tab)) b
          WHERE a.order_id = p_order_id AND
                a.managed_order_id = b.managed_order_id AND
                a.platform = p_platform AND
                a.close_reason = nvl(b.close_reason,'NONE') AND
                (nrg_common.has_value_changed(b.trading_account_id, a.trading_account_id) = 1 OR
                 nrg_common.has_value_changed(b.trading_account_type, a.trading_account_type) = 1 OR
                 nrg_common.has_value_changed(b.requested_quantity, a.requested_quantity) = 1 OR
                 nrg_common.has_value_changed(b.execution_price, a.execution_price) = 1);

          --
          --Write to history table
          --

          FOR lv_cnt IN 1..lv_updated_managed_info.COUNT LOOP
            put_history(p_old_order_managed_info               => lv_updated_managed_info(lv_cnt),
                        p_effective_end_timestamp              => p_effective_start_timestamp,
                        p_action                               => 'U');
          END LOOP;

          --
          --Identify records existing in old message but are missing on the current table
          --
          SELECT p_platform,
                 p_order_id,
                 b.trading_account_id,
                 b.trading_account_type,
                 p_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp,
                 b.managed_order_id,
                 b.requested_quantity,
                 b.execution_price,
                 nvl(b.close_reason, 'NONE')
          BULK COLLECT INTO lv_deleted_managed_info
          FROM orders_managed_information a,
              TABLE(CAST(p_order_managed_info AS orders_managed_info_tab)) b
          WHERE a.order_id = p_order_id AND
                a.trading_account_id = b.trading_account_id (+) AND
                a.trading_account_type = b.trading_account_type (+) AND
                a.platform = p_platform AND
                a.close_reason = nvl(b.close_reason, 'NONE') AND
                a.trading_account_id IS NULL;

          --
          --Write deleted records to history
          --

          FOR lv_cnt IN 1..lv_deleted_managed_info.COUNT LOOP
            put_history(p_old_order_managed_info               => lv_deleted_managed_info(lv_cnt),
                        p_effective_end_timestamp              => p_effective_start_timestamp,
                        p_action                               => 'D');
          END LOOP;

      END CASE;

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;


    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the orders record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_order_record              This is the old version of the order record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_order_record         orders%ROWTYPE,
                           p_effective_end_timestamp  orders_h.effective_end_timestamp%TYPE,
                           p_action                   orders_h.action%TYPE)
    IS

    BEGIN
        INSERT INTO orders_h (platform,
                              order_id,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              effective_start_timestamp,
                              effective_end_timestamp,
                              action,
                              action_timestamp,
                              trading_account_id,
                              trading_account_type,
                              product_instrument_code,
                              session_key,
                              business_date,
                              reporting_date,
                              product_wrapper_code,
                              mm_instrument_id,
                              product_schema_code,
                              publish_time,
                              trading_account_function,
                              trading_account_codifier,
                              changed_character,
                              changed_reason_code,
                              changed_reason_codifier,
                              version_number,
                              state,
                              creation_time,
                              update_time,
                              order_type,
                              related_parent_order_id,
                              related_initiating_order_id,
                              open_trade_id,
                              controlled_order_type,
                              related_child_order_type,
                              visit_id,
                              protection_type,
                              trading_accnt_primary_currency,
                              product_feed_symbol,
                              product_generation,
                              product_prophet_symbol,
                              product_currency,
                              product_point_multiplier,
                              product_financing_ratio_max,
                              product_fractional_part_ratio,
                              channel_id,
                              request_id,
                              is_deleted,
                              creation_identity_token,
                              crtn_on_bhlf_of_idntty_tkn,
                              update_identity_token,
                              updt_on_bhlf_of_idntty_tkn,
                              requested_direction,
                              requested_quantity,
                              requested_financing_ratio,
                              requested_valid_till,
                              requested_trade_close_oder_id,
                              quantity_designator,
                              quantity_matchable,
                              quantity_match,
                              quantity_remaining,
                              quantity_currency,
                              activation_time,
                              actual_valid_till,
                              is_mandatory,
                              execution_condition,
                              limit_price,
                              limit_price_condition,
                              is_limit_trailing,
                              limit_trailing_distance,
                              limit_trailing_best_price,
                              limit_trailing_best_price_time,
                              guaranteed_trade_price,
                              gurntd_trd_prc_cndtn,
                              open_trade_quantity,
                              normalised_quantity,
                              normalised_quantity_currency,
                              open_trade_price,
                              open_trade_time,
                              open_trade_amount,
                              open_trade_amount_currency,
                              open_trade_margin,
                              open_trade_margin_type,
                              open_trade_margin_currency,
                              open_trade_margin_requirement,
                              open_trade_margin_fx_rate_bid,
                              open_trade_margin_fx_rate_ask,
                              opn_trd_amnt_in_ta_prm_crncy,
                              opn_trd_mrgn_in_ta_prm_crncy,
                              open_trade_last_modified_time,
                              client_state_request_time,
                              client_state_quote_l1_bid,
                              client_state_quote_l1_ask,
                              client_state_quote_bid,
                              client_state_quote_ask,
                              open_trade_financing_ratio,
                              open_trade_qntty_fx_rate_bid,
                              open_trade_qntty_fx_rate_ask,
                              close_time,
                              close_comment,
                              close_reason,
                              close_sub_reason,
                              custom_fx_rate_ask,
                              custom_fx_rate_bid,
                              custom_fx_rate_id,
                              cstm_info_vrtl_prtfl_cd,
                              custom_info,
                              custom_quote_ask,
                              custom_quote_bid,
                              custom_quote_id,
                              event_time,
                              is_late_deal,
                              is_primary,
                              cntrld_ordr_comment,
                              is_clrng_done_unconditionally,
                              is_crrncy_in_fractional_parts,
                              is_fnncng_ratio_chck_ignrd,
                              is_mtch_rslt_in_pstn_inc_ignrd,
                              is_mtch_rslt_in_shrt_trd_ignrd,
                              is_order_sz_grtr_max_ignrd,
                              is_product_generation_check,
                              is_ta_sspndd_fr_trd_rvsl_ignrd,
                              is_trdng_enbld_fr_prdct_ignrd,
                              is_trdng_pssbl_fr_ta_ignrd,
                              is_trdng_sspndd_fr_prdct_ignrd,
                              is_mtchng_use_lfo_opn_trd_cmpr,
                              point_multiplier,
                              trd_reversal_pl_reverse,
                              trd_reversal_trd_id_to_reverse,
                              use_custom_quote,
                              mm_account_id,
                              logical_update_time,
                              normalised_trade_price,
                              client_state_quote_id,
                              triggering_quote_id,
                              triggering_time,
                              triggering_execution_price,
                              triggering_level1_price,
                              boundary_price,
                              triggering_side,
                              is_funds_check_ignored,
                              custom_amount_fx_rate,
                              client_state_parameter_info,
                              amnt_to_chrg_in_comm_ccy,
                              reinstated_order_id,
                              is_position_auto_rolled,
                              rolled_order_id,
                              client_amount,
                              parent_order_state,
                              commission_currency,
                              triggering_price_offset_index,
                              queued_quote_id,
                              activation_quantity,
                              gslo_premium_per_unit,
                              gslo_premium_fxreval_rate,
                              gslo_premium_refund_percentage,
                              Order_Id_Short_Form,
                              parent_order_id_short_form,
                              Execution_Type,
                              Assigned_To,
                              cd_price_adjustment,
                              cd_tenor,
                              cd_opening_booking_id,
                              cd_win_payout,
                              cd_draw_payout,
                              cd_opening_quote_id,
                              cd_opening_speed_bet_quote_id ,
                              cd_client_state_sbet_quote_id ,
                              cd_client_state_strike_price,
                              cd_client_state_price_adjust,
                              cd_client_state_mid_price,
                              cd_result,
                              cd_settle_price,
                              cd_settle_quote_receive_time,
                              cd_settle_quote_id,
                              cd_settle_amount,
                              cd_settle_booking_id,
                              cd_failed_settle_quote_id,
                              cd_failed_settle_quote_receive,
                              cd_cancellation_open_booking,
                              cd_cancellation_settle_booking,
                              record_source,
                              trading_scope,
                              allocation_order_id,
                              alloc_trading_risk_schema,
                              binary_type,
                              settle_time,
                              tenor,
                              strike_price,
                              strike_price_additional,
                              alloc_instrument_schema,
                              guaranteed_limit_price,
                              was_guaranteed_executed,
                              source_order_id,
                              source_trade_id,
                              client_order_id,
                              limit_distance,
                              guaranteed_limit_distance,
                              metatrader_order_type,
                              client_limit_price ,
                              order_chngd_chrg_cmsn,
                              order_chngd_prev_state,
                              migrated_order_id,
                              client_party_id,
                              liquidation_id,
                              is_give_up,
                              exclude_from_turnover_report
                              )
                           VALUES (p_old_order_record.platform,
                              p_old_order_record.order_id,
                              p_old_order_record.logical_load_timestamp,
                              p_old_order_record.created_by,
                              p_old_order_record.create_timestamp,
                              p_old_order_record.updated_by,
                              p_old_order_record.update_timestamp,
                              p_old_order_record.effective_start_timestamp,
                              p_effective_end_timestamp,
                              p_action,
                              SYSTIMESTAMP,
                              p_old_order_record.trading_account_id,
                              p_old_order_record.trading_account_type,
                              p_old_order_record.product_instrument_code,
                              p_old_order_record.session_key,
                              p_old_order_record.business_date,
                              p_old_order_record.reporting_date,
                              p_old_order_record.product_wrapper_code,
                              p_old_order_record.mm_instrument_id,
                              p_old_order_record.product_schema_code,
                              p_old_order_record.publish_time,
                              p_old_order_record.trading_account_function,
                              p_old_order_record.trading_account_codifier,
                              p_old_order_record.changed_character,
                              p_old_order_record.changed_reason_code,
                              p_old_order_record.changed_reason_codifier,
                              p_old_order_record.version_number,
                              p_old_order_record.state,
                              p_old_order_record.creation_time,
                              p_old_order_record.update_time,
                              p_old_order_record.order_type,
                              p_old_order_record.related_parent_order_id,
                              p_old_order_record.related_initiating_order_id,
                              p_old_order_record.open_trade_id,
                              upper(p_old_order_record.controlled_order_type),
                              p_old_order_record.related_child_order_type,
                              p_old_order_record.visit_id,
                              p_old_order_record.protection_type,
                              p_old_order_record.trading_accnt_primary_currency,
                              p_old_order_record.product_feed_symbol,
                              p_old_order_record.product_generation,
                              p_old_order_record.product_prophet_symbol,
                              p_old_order_record.product_currency,
                              p_old_order_record.product_point_multiplier,
                              p_old_order_record.product_financing_ratio_max,
                              p_old_order_record.product_fractional_part_ratio,
                              p_old_order_record.channel_id,
                              p_old_order_record.request_id,
                              p_old_order_record.is_deleted,
                              p_old_order_record.creation_identity_token,
                              p_old_order_record.crtn_on_bhlf_of_idntty_tkn,
                              p_old_order_record.update_identity_token,
                              p_old_order_record.updt_on_bhlf_of_idntty_tkn,
                              p_old_order_record.requested_direction,
                              p_old_order_record.requested_quantity,
                              p_old_order_record.requested_financing_ratio,
                              p_old_order_record.requested_valid_till,
                              p_old_order_record.requested_trade_close_oder_id,
                              p_old_order_record.quantity_designator,
                              p_old_order_record.quantity_matchable,
                              p_old_order_record.quantity_match,
                              p_old_order_record.quantity_remaining,
                              p_old_order_record.quantity_currency,
                              p_old_order_record.activation_time,
                              p_old_order_record.actual_valid_till,
                              p_old_order_record.is_mandatory,
                              p_old_order_record.execution_condition,
                              p_old_order_record.limit_price,
                              p_old_order_record.limit_price_condition,
                              p_old_order_record.is_limit_trailing,
                              p_old_order_record.limit_trailing_distance,
                              p_old_order_record.limit_trailing_best_price,
                              p_old_order_record.limit_trailing_best_price_time,
                              p_old_order_record.guaranteed_trade_price,
                              p_old_order_record.gurntd_trd_prc_cndtn,
                              p_old_order_record.open_trade_quantity,
                              p_old_order_record.normalised_quantity,
                              p_old_order_record.normalised_quantity_currency,
                              p_old_order_record.open_trade_price,
                              p_old_order_record.open_trade_time,
                              p_old_order_record.open_trade_amount,
                              p_old_order_record.open_trade_amount_currency,
                              p_old_order_record.open_trade_margin,
                              p_old_order_record.open_trade_margin_type,
                              p_old_order_record.open_trade_margin_currency,
                              p_old_order_record.open_trade_margin_requirement,
                              p_old_order_record.open_trade_margin_fx_rate_bid,
                              p_old_order_record.open_trade_margin_fx_rate_ask,
                              p_old_order_record.opn_trd_amnt_in_ta_prm_crncy,
                              p_old_order_record.opn_trd_mrgn_in_ta_prm_crncy,
                              p_old_order_record.open_trade_last_modified_time,
                              p_old_order_record.client_state_request_time,
                              p_old_order_record.client_state_quote_l1_bid,
                              p_old_order_record.client_state_quote_l1_ask,
                              p_old_order_record.client_state_quote_bid,
                              p_old_order_record.client_state_quote_ask,
                              p_old_order_record.open_trade_financing_ratio,
                              p_old_order_record.open_trade_qntty_fx_rate_bid,
                              p_old_order_record.open_trade_qntty_fx_rate_ask,
                              p_old_order_record.close_time,
                              p_old_order_record.close_comment,
                              p_old_order_record.close_reason,
                              p_old_order_record.close_sub_reason,
                              p_old_order_record.custom_fx_rate_ask,
                              p_old_order_record.custom_fx_rate_bid,
                              p_old_order_record.custom_fx_rate_id,
                              p_old_order_record.cstm_info_vrtl_prtfl_cd,
                              p_old_order_record.custom_info,
                              p_old_order_record.custom_quote_ask,
                              p_old_order_record.custom_quote_bid,
                              p_old_order_record.custom_quote_id,
                              p_old_order_record.event_time,
                              p_old_order_record.is_late_deal,
                              p_old_order_record.is_primary,
                              p_old_order_record.cntrld_ordr_comment,
                              p_old_order_record.is_clrng_done_unconditionally,
                              p_old_order_record.is_crrncy_in_fractional_parts,
                              p_old_order_record.is_fnncng_ratio_chck_ignrd,
                              p_old_order_record.is_mtch_rslt_in_pstn_inc_ignrd,
                              p_old_order_record.is_mtch_rslt_in_shrt_trd_ignrd,
                              p_old_order_record.is_order_sz_grtr_max_ignrd,
                              p_old_order_record.is_product_generation_check,
                              p_old_order_record.is_ta_sspndd_fr_trd_rvsl_ignrd,
                              p_old_order_record.is_trdng_enbld_fr_prdct_ignrd,
                              p_old_order_record.is_trdng_pssbl_fr_ta_ignrd,
                              p_old_order_record.is_trdng_sspndd_fr_prdct_ignrd,
                              p_old_order_record.is_mtchng_use_lfo_opn_trd_cmpr,
                              p_old_order_record.point_multiplier,
                              p_old_order_record.trd_reversal_pl_reverse,
                              p_old_order_record.trd_reversal_trd_id_to_reverse,
                              p_old_order_record.use_custom_quote,
                              p_old_order_record.mm_account_id,
                              p_old_order_record.logical_update_time,
                              p_old_order_record.normalised_trade_price,
                              p_old_order_record.client_state_quote_id,
                              p_old_order_record.triggering_quote_id,
                              p_old_order_record.triggering_time,
                              p_old_order_record.triggering_execution_price,
                              p_old_order_record.triggering_level1_price,
                              p_old_order_record.boundary_price,
                              p_old_order_record.triggering_side,
                              p_old_order_record.is_funds_check_ignored,
                              p_old_order_record.custom_amount_fx_rate,
                              p_old_order_record.client_state_parameter_info,
                              p_old_order_record.amnt_to_chrg_in_comm_ccy,
                              p_old_order_record.reinstated_order_id,
                              p_old_order_record.is_position_auto_rolled,
                              p_old_order_record.rolled_order_id,
                              p_old_order_record.client_amount,
                              p_old_order_record.parent_order_state,
                              p_old_order_record.commission_currency,
                              p_old_order_record.triggering_price_offset_index,
                              p_old_order_record.queued_quote_id,
                              p_old_order_record.activation_quantity,
                              p_old_order_record.gslo_premium_per_unit,
                              p_old_order_record.gslo_premium_fxreval_rate,
                              p_old_order_record.gslo_premium_refund_percentage,
                              p_old_order_record.Order_Id_Short_Form,
                              p_old_order_record.Parent_Order_Id_Short_Form,
                              p_old_order_record.Execution_Type,
                              p_old_order_record.Assigned_To,
                              p_old_order_record.cd_price_adjustment,
                              p_old_order_record.cd_tenor,
                              p_old_order_record.cd_opening_booking_id,
                              p_old_order_record.cd_win_payout,
                              p_old_order_record.cd_draw_payout,
                              p_old_order_record.cd_opening_quote_id,
                              p_old_order_record.cd_opening_speed_bet_quote_id ,
                              p_old_order_record.cd_client_state_sbet_quote_id ,
                              p_old_order_record.cd_client_state_strike_price,
                              p_old_order_record.cd_client_state_price_adjust,
                              p_old_order_record.cd_client_state_mid_price,
                              p_old_order_record.cd_result,
                              p_old_order_record.cd_settle_price,
                              p_old_order_record.cd_settle_quote_receive_time,
                              p_old_order_record.cd_settle_quote_id,
                              p_old_order_record.cd_settle_amount,
                              p_old_order_record.cd_settle_booking_id,
                              p_old_order_record.cd_failed_settle_quote_id,
                              p_old_order_record.cd_failed_settle_quote_receive,
                              p_old_order_record.cd_cancellation_open_booking,
                              p_old_order_record.cd_cancellation_settle_booking,
                              p_old_order_record.record_source,
                              p_old_order_record.trading_scope,
                              p_old_order_record.allocation_order_id,
                              p_old_order_record.alloc_trading_risk_schema,
                              p_old_order_record.binary_type,
                              p_old_order_record.settle_time,
                              p_old_order_record.tenor,
                              p_old_order_record.strike_price,
                              p_old_order_record.strike_price_additional,
                              p_old_order_record.alloc_instrument_schema,
                              p_old_order_record.guaranteed_limit_price,
                              p_old_order_record.was_guaranteed_executed,
                              p_old_order_record.source_order_id,
                              p_old_order_record.source_trade_id,
                              p_old_order_record.client_order_id,
                              p_old_order_record.limit_distance,
                              p_old_order_record.guaranteed_limit_distance,
                              p_old_order_record.metatrader_order_type,
                              p_old_order_record.client_limit_price,
                              p_old_order_record.order_chngd_chrg_cmsn,
                              p_old_order_record.order_chngd_prev_state,
                              p_old_order_record.migrated_order_id,
                              p_old_order_record.client_party_id,
                              p_old_order_record.liquidation_id,
                              p_old_order_record.is_give_up,
                              p_old_order_record.exclude_from_turnover_report
                              );
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --
        -- This would occur in the case when the same message is replayed
        --
        -- Possible ways in which message gets replayed
        -- Request Response
        -- Upstream publishes for manual updates
        -- Message replayed
        --
        UPDATE orders_h
        SET   logical_load_timestamp           = p_old_order_record.logical_load_timestamp,
              created_by                       = p_old_order_record.created_by,
              create_timestamp                 = p_old_order_record.create_timestamp,
              updated_by                       = p_old_order_record.updated_by,
              update_timestamp                 = p_old_order_record.update_timestamp,
              effective_end_timestamp          = p_effective_end_timestamp,
              action                           = p_action,
              action_timestamp                 = SYSTIMESTAMP,
              trading_account_id               = p_old_order_record.trading_account_id,
              trading_account_type             = p_old_order_record.trading_account_type,
              product_instrument_code          = p_old_order_record.product_instrument_code,
              session_key                      = p_old_order_record.session_key,
              business_date                    = p_old_order_record.business_date,
              reporting_date                   = p_old_order_record.reporting_date,
              product_wrapper_code             = p_old_order_record.product_wrapper_code,
              mm_instrument_id                 = p_old_order_record.mm_instrument_id,
              product_schema_code              = p_old_order_record.product_schema_code,
              publish_time                     = p_old_order_record.publish_time,
              trading_account_function         = p_old_order_record.trading_account_function,
              trading_account_codifier         = p_old_order_record.trading_account_codifier,
              changed_character                = p_old_order_record.changed_character,
              changed_reason_code              = p_old_order_record.changed_reason_code,
              changed_reason_codifier          = p_old_order_record.changed_reason_codifier,
              version_number                   = p_old_order_record.version_number,
              state                            = p_old_order_record.state,
              creation_time                    = p_old_order_record.creation_time,
              update_time                      = p_old_order_record.update_time,
              order_type                       = p_old_order_record.order_type,
              related_parent_order_id          = p_old_order_record.related_parent_order_id,
              related_initiating_order_id      = p_old_order_record.related_initiating_order_id,
              open_trade_id                    = p_old_order_record.open_trade_id,
              controlled_order_type            = upper(p_old_order_record.controlled_order_type),
              related_child_order_type         = p_old_order_record.related_child_order_type,
              visit_id                         = p_old_order_record.visit_id,
              protection_type                  = p_old_order_record.protection_type,
              trading_accnt_primary_currency   = p_old_order_record.trading_accnt_primary_currency,
              product_feed_symbol              = p_old_order_record.product_feed_symbol,
              product_generation               = p_old_order_record.product_generation,
              product_prophet_symbol           = p_old_order_record.product_prophet_symbol,
              product_currency                 = p_old_order_record.product_currency,
              product_point_multiplier         = p_old_order_record.product_point_multiplier,
              product_financing_ratio_max      = p_old_order_record.product_financing_ratio_max,
              product_fractional_part_ratio    = p_old_order_record.product_fractional_part_ratio,
              channel_id                       = p_old_order_record.channel_id,
              request_id                       = p_old_order_record.request_id,
              is_deleted                       = p_old_order_record.is_deleted,
              creation_identity_token          = p_old_order_record.creation_identity_token,
              crtn_on_bhlf_of_idntty_tkn       = p_old_order_record.crtn_on_bhlf_of_idntty_tkn,
              update_identity_token            = p_old_order_record.update_identity_token,
              updt_on_bhlf_of_idntty_tkn       = p_old_order_record.updt_on_bhlf_of_idntty_tkn,
              requested_direction              = p_old_order_record.requested_direction,
              requested_quantity               = p_old_order_record.requested_quantity,
              requested_financing_ratio        = p_old_order_record.requested_financing_ratio,
              requested_valid_till             = p_old_order_record.requested_valid_till,
              requested_trade_close_oder_id    = p_old_order_record.requested_trade_close_oder_id,
              quantity_designator              = p_old_order_record.quantity_designator,
              quantity_matchable               = p_old_order_record.quantity_matchable,
              quantity_match                   = p_old_order_record.quantity_match,
              quantity_remaining               = p_old_order_record.quantity_remaining,
              quantity_currency                = p_old_order_record.quantity_currency,
              activation_time                  = p_old_order_record.activation_time,
              actual_valid_till                = p_old_order_record.actual_valid_till,
              is_mandatory                     = p_old_order_record.is_mandatory,
              execution_condition              = p_old_order_record.execution_condition,
              limit_price                      = p_old_order_record.limit_price,
              limit_price_condition            = p_old_order_record.limit_price_condition,
              is_limit_trailing                = p_old_order_record.is_limit_trailing,
              limit_trailing_distance          = p_old_order_record.limit_trailing_distance,
              limit_trailing_best_price        = p_old_order_record.limit_trailing_best_price,
              limit_trailing_best_price_time   = p_old_order_record.limit_trailing_best_price_time,
              guaranteed_trade_price           = p_old_order_record.guaranteed_trade_price,
              gurntd_trd_prc_cndtn             = p_old_order_record.gurntd_trd_prc_cndtn,
              open_trade_quantity              = p_old_order_record.open_trade_quantity,
              normalised_quantity              = p_old_order_record.normalised_quantity,
              normalised_quantity_currency     = p_old_order_record.normalised_quantity_currency,
              open_trade_price                 = p_old_order_record.open_trade_price,
              open_trade_time                  = p_old_order_record.open_trade_time,
              open_trade_amount                = p_old_order_record.open_trade_amount,
              open_trade_amount_currency       = p_old_order_record.open_trade_amount_currency,
              open_trade_margin                = p_old_order_record.open_trade_margin,
              open_trade_margin_type           = p_old_order_record.open_trade_margin_type,
              open_trade_margin_currency       = p_old_order_record.open_trade_margin_currency,
              open_trade_margin_requirement    = p_old_order_record.open_trade_margin_requirement,
              open_trade_margin_fx_rate_bid    = p_old_order_record.open_trade_margin_fx_rate_bid,
              open_trade_margin_fx_rate_ask    = p_old_order_record.open_trade_margin_fx_rate_ask,
              opn_trd_amnt_in_ta_prm_crncy     = p_old_order_record.opn_trd_amnt_in_ta_prm_crncy,
              opn_trd_mrgn_in_ta_prm_crncy     = p_old_order_record.opn_trd_mrgn_in_ta_prm_crncy,
              open_trade_last_modified_time    = p_old_order_record.open_trade_last_modified_time,
              client_state_request_time        = p_old_order_record.client_state_request_time,
              client_state_quote_l1_bid        = p_old_order_record.client_state_quote_l1_bid,
              client_state_quote_l1_ask        = p_old_order_record.client_state_quote_l1_ask,
              client_state_quote_bid           = p_old_order_record.client_state_quote_bid,
              client_state_quote_ask           = p_old_order_record.client_state_quote_ask,
              open_trade_financing_ratio       = p_old_order_record.open_trade_financing_ratio,
              open_trade_qntty_fx_rate_bid     = p_old_order_record.open_trade_qntty_fx_rate_bid,
              open_trade_qntty_fx_rate_ask     = p_old_order_record.open_trade_qntty_fx_rate_ask,
              close_time                       = p_old_order_record.close_time,
              close_comment                    = p_old_order_record.close_comment,
              close_reason                     = p_old_order_record.close_reason,
              close_sub_reason                 = p_old_order_record.close_sub_reason,
              custom_fx_rate_ask               = p_old_order_record.custom_fx_rate_ask,
              custom_fx_rate_bid               = p_old_order_record.custom_fx_rate_bid,
              custom_fx_rate_id                = p_old_order_record.custom_fx_rate_id,
              cstm_info_vrtl_prtfl_cd          = p_old_order_record.cstm_info_vrtl_prtfl_cd,
              custom_info                      = p_old_order_record.custom_info,
              custom_quote_ask                 = p_old_order_record.custom_quote_ask,
              custom_quote_bid                 = p_old_order_record.custom_quote_bid,
              custom_quote_id                  = p_old_order_record.custom_quote_id,
              event_time                       = p_old_order_record.event_time,
              is_late_deal                     = p_old_order_record.is_late_deal,
              is_primary                       = p_old_order_record.is_primary,
              cntrld_ordr_comment              = p_old_order_record.cntrld_ordr_comment,
              is_clrng_done_unconditionally    = p_old_order_record.is_clrng_done_unconditionally,
              is_crrncy_in_fractional_parts    = p_old_order_record.is_crrncy_in_fractional_parts,
              is_fnncng_ratio_chck_ignrd       = p_old_order_record.is_fnncng_ratio_chck_ignrd,
              is_mtch_rslt_in_pstn_inc_ignrd   = p_old_order_record.is_mtch_rslt_in_pstn_inc_ignrd,
              is_mtch_rslt_in_shrt_trd_ignrd   = p_old_order_record.is_mtch_rslt_in_shrt_trd_ignrd,
              is_order_sz_grtr_max_ignrd    = p_old_order_record.is_order_sz_grtr_max_ignrd,
              is_product_generation_check      = p_old_order_record.is_product_generation_check,
              is_ta_sspndd_fr_trd_rvsl_ignrd   = p_old_order_record.is_ta_sspndd_fr_trd_rvsl_ignrd,
              is_trdng_enbld_fr_prdct_ignrd    = p_old_order_record.is_trdng_enbld_fr_prdct_ignrd,
              is_trdng_pssbl_fr_ta_ignrd       = p_old_order_record.is_trdng_pssbl_fr_ta_ignrd,
              is_trdng_sspndd_fr_prdct_ignrd   = p_old_order_record.is_trdng_sspndd_fr_prdct_ignrd,
              is_mtchng_use_lfo_opn_trd_cmpr   = p_old_order_record.is_mtchng_use_lfo_opn_trd_cmpr,
              point_multiplier                 = p_old_order_record.point_multiplier,
              trd_reversal_pl_reverse          = p_old_order_record.trd_reversal_pl_reverse,
              trd_reversal_trd_id_to_reverse   = p_old_order_record.trd_reversal_trd_id_to_reverse,
              use_custom_quote                 = p_old_order_record.use_custom_quote,
              mm_account_id                    = p_old_order_record.mm_account_id,
              logical_update_time              = p_old_order_record.logical_update_time,
              normalised_trade_price           = p_old_order_record.normalised_trade_price,
              client_state_quote_id            = p_old_order_record.client_state_quote_id,
              triggering_quote_id              = p_old_order_record.triggering_quote_id,
              triggering_time                  = p_old_order_record.triggering_time,
              triggering_execution_price       = p_old_order_record.triggering_execution_price,
              triggering_level1_price          = p_old_order_record.triggering_level1_price,
              boundary_price                   = p_old_order_record.boundary_price,
              triggering_side                  = p_old_order_record.triggering_side,
              is_funds_check_ignored           = p_old_order_record.is_funds_check_ignored,
              custom_amount_fx_rate            = p_old_order_record.custom_amount_fx_rate,
              client_state_parameter_info      = p_old_order_record.client_state_parameter_info,
              amnt_to_chrg_in_comm_ccy         = p_old_order_record.amnt_to_chrg_in_comm_ccy,
              reinstated_order_id              = p_old_order_record.reinstated_order_id,
              is_position_auto_rolled          = p_old_order_record.is_position_auto_rolled,
              rolled_order_id                  = p_old_order_record.rolled_order_id,
              client_amount                    = p_old_order_record.client_amount,
              parent_order_state               = p_old_order_record.parent_order_state,
              commission_currency              = p_old_order_record.commission_currency,
              triggering_price_offset_index    = p_old_order_record.triggering_price_offset_index,
              queued_quote_id                  = p_old_order_record.queued_quote_id,
              activation_quantity              = p_old_order_record.activation_quantity,
              gslo_premium_per_unit            = p_old_order_record.gslo_premium_per_unit,
              gslo_premium_fxreval_rate        = p_old_order_record.gslo_premium_fxreval_rate,
              gslo_premium_refund_percentage   = p_old_order_record.gslo_premium_refund_percentage,
              order_id_short_form              = p_old_order_record.Order_Id_Short_Form,
              parent_order_id_short_form       = p_old_order_record.parent_Order_Id_Short_Form,
              Execution_Type                   = p_old_order_record.Execution_Type,
              Assigned_To                      = p_old_order_record.Assigned_To,
              cd_price_adjustment              = p_old_order_record.cd_price_adjustment,
              cd_tenor                         = p_old_order_record.cd_tenor,
              cd_opening_booking_id            = p_old_order_record.cd_opening_booking_id,
              cd_win_payout                    = p_old_order_record.cd_win_payout,
              cd_draw_payout                   = p_old_order_record.cd_draw_payout,
              cd_opening_quote_id              = p_old_order_record.cd_opening_quote_id,
              cd_opening_speed_bet_quote_id    = p_old_order_record.cd_opening_speed_bet_quote_id ,
              cd_client_state_sbet_quote_id    = p_old_order_record.cd_client_state_sbet_quote_id ,
              cd_client_state_strike_price     = p_old_order_record.cd_client_state_strike_price,
              cd_client_state_price_adjust     = p_old_order_record.cd_client_state_price_adjust,
              cd_client_state_mid_price        = p_old_order_record.cd_client_state_mid_price,
              cd_result                        = p_old_order_record.cd_result,
              cd_settle_price                  = p_old_order_record.cd_settle_price,
              cd_settle_quote_receive_time     = p_old_order_record.cd_settle_quote_receive_time,
              cd_settle_quote_id               = p_old_order_record.cd_settle_quote_id,
              cd_settle_amount                 = p_old_order_record.cd_settle_amount,
              cd_settle_booking_id             = p_old_order_record.cd_settle_booking_id,
              cd_failed_settle_quote_id        = p_old_order_record.cd_failed_settle_quote_id,
              cd_failed_settle_quote_receive   = p_old_order_record.cd_failed_settle_quote_receive,
              cd_cancellation_open_booking     = p_old_order_record.cd_cancellation_open_booking,
              cd_cancellation_settle_booking   = p_old_order_record.cd_cancellation_settle_booking,
              record_source                    = p_old_order_record.record_source,
              trading_scope                    = p_old_order_record.trading_scope,
              allocation_order_id              = p_old_order_record.allocation_order_id,
              alloc_trading_risk_schema        = p_old_order_record.alloc_trading_risk_schema,
              binary_type                      = p_old_order_record.binary_type,
              settle_time                      = p_old_order_record.settle_time,
              tenor                            = p_old_order_record.tenor,
              strike_price                     = p_old_order_record.strike_price,
              strike_price_additional          = p_old_order_record.strike_price_additional,
              alloc_instrument_schema          = p_old_order_record.alloc_instrument_schema,
              guaranteed_limit_price           = p_old_order_record.guaranteed_limit_price,
              was_guaranteed_executed          = p_old_order_record.was_guaranteed_executed,
              source_order_id                  = p_old_order_record.source_order_id,
              source_trade_id                  = p_old_order_record.source_trade_id,
              client_order_id                  = p_old_order_record.client_order_id,
              limit_distance                   = p_old_order_record.limit_distance,
              guaranteed_limit_distance        = p_old_order_record.guaranteed_limit_distance,
              metatrader_order_type            = p_old_order_record.metatrader_order_type,
              client_limit_price               = p_old_order_record.client_limit_price,
              order_chngd_chrg_cmsn            = p_old_order_record.order_chngd_chrg_cmsn,
              order_chngd_prev_state           = p_old_order_record.order_chngd_prev_state,
              migrated_order_id                = p_old_order_record.migrated_order_id,
              client_party_id                  = p_old_order_record.client_party_id,
              liquidation_id                   = p_old_order_record.liquidation_id,
              is_give_up                       = p_old_order_record.is_give_up,
              exclude_from_turnover_report     = p_old_order_record.exclude_from_turnover_report
            WHERE order_id                     = p_old_order_record.order_id AND
                  platform                     = p_old_order_record.platform AND
                  effective_start_timestamp    = p_old_order_record.effective_start_timestamp;
    END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the orders record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_order_record              This is the old version of the order record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_record               orders_close_comment%ROWTYPE,
                           p_effective_end_timestamp  orders_close_comment_h.effective_end_timestamp%TYPE,
                           p_action                   orders_close_comment_h.action%TYPE) IS

    BEGIN
      INSERT INTO orders_close_comment_h (order_id,
                                          platform,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          effective_start_timestamp,
                                          effective_end_timestamp,
                                          action,
                                          action_timestamp,
                                          custom,
                                          fx_rate_symbol,
                                          is_quote_id_set,
                                          quote_id,
                                          existing_order_id,
                                          underlying_position_size,
                                          underlying_max_position_size,
                                          prdct_undrlng_mx_pstn_sz,
                                          account_proportion,
                                          order_strike_price,
                                          strike_price_additional,
                                          binary_trading_status,
                                          is_is_target_above_set,
                                          is_target_above,
                                          quote_strike_price,
                                          underlying_instrument,
                                          evaluation_price,
                                          crrnt_open_trade_amount,
                                          crrnt_open_trade_amount_ccy,
                                          max_open_trade_amount,
                                          max_open_trade_amount_ccy,
                                          evaluation_fx_reval_rate,
                                          amount_fx_reval_rate_bid,
                                          amount_fx_reval_rate_ask,
                                          pnl_fx_reval_rate_bid,
                                          pnl_fx_reval_rate_ask,
                                          pnl_fx_reval_rate_mid,
                                          market_price,
                                          min_distance,
                                          parent_target_price,
                                          take_profit_price,
                                          position_size,
                                          max_position_size,
                                          quantity,
                                          max_quantity,
                                          product_max_position_size,
                                          product_trade_qty_maximum,
                                          quote_proportion,
                                          margin,
                                          equity,
                                          equity_change,
                                          margin_change,
                                          spread_loss,
                                          premium,
                                          stop_loss_price,
                                          stop_loss_trailing_distance,
                                          guaranteed_stop_loss_price,
                                          level_1_bid,
                                          level_1_ask,
                                          is_price_offset_index_set,
                                          price_offset_index,
                                          loss_amount,
                                          current_profit_loss_amount,
                                          loss_limit_amount,
                                          period_type,
                                          period,
                                          swsp_timeout_at,
                                          risk_reason,
                                          is_queue_count_set,
                                          queue_count,
                                          is_executions_per_period_set,
                                          executions_per_period,
                                          is_exctns_prd_in_mllscnds_set,
                                          exctns_prd_in_mllscnds,
                                          execution_price,
                                          aggregated_quantity,
                                          opposite_price,
                                          exposure,
                                          max_exposure,
                                          is_product_max_exposure_set,
                                          product_max_exposure,
                                          total_exposure,
                                          max_total_exposure,
                                          is_prdct_mx_ttl_expsr_set,
                                          prdct_mx_ttl_expsr,
                                          expiry_time,
                                          good_for_time,
                                          execution_time,
                                          utc_now,
                                          start_time,
                                          is_timeout_ms_set,
                                          timeout_ms,
                                          max_stake_size_individual,
                                          min_stake_size,
                                          concurrent_stake,
                                          max_concurrent_stake,
                                          prdct_mx_stk_sz_cncrrnt,
                                          stop_loss_distance,
                                          guaranteed_stop_loss_distance,
                                          currency,
                                          currency_type,
                                          take_profit_distance,
                                          order_type_maximum,
                                          quote_received_time,
                                          max_opn_pstn_amnt_grss,
                                          max_opn_pstn_amnt_grss_ccy,
                                          crrnt_opn_pstn_amnt_grss,
                                          crrnt_opn_pstn_amnt_grss_ccy,
                                          max_opn_pstn_amnt_net,
                                          max_opn_pstn_amnt_net_ccy,
                                          crrnt_opn_pstn_amnt_net,
                                          crrnt_opn_pstn_amnt_net_ccy,
                                          evaltn_fxrevalrate_insttousd,
                                          evaltn_fxrevalrate_pairtousd
                                          )
                                  VALUES (p_old_record.order_id,
                                          p_old_record.platform,
                                          p_old_record.logical_load_timestamp,
                                          p_old_record.created_by,
                                          p_old_record.create_timestamp,
                                          p_old_record.updated_by,
                                          p_old_record.update_timestamp,
                                          p_old_record.effective_start_timestamp,
                                          p_effective_end_timestamp,
                                          p_action,
                                          SYSTIMESTAMP,
                                          p_old_record.custom,
                                          p_old_record.fx_rate_symbol,
                                          p_old_record.is_quote_id_set,
                                          p_old_record.quote_id,
                                          p_old_record.existing_order_id,
                                          p_old_record.underlying_position_size,
                                          p_old_record.underlying_max_position_size,
                                          p_old_record.prdct_undrlng_mx_pstn_sz,
                                          p_old_record.account_proportion,
                                          p_old_record.order_strike_price,
                                          p_old_record.strike_price_additional,
                                          p_old_record.binary_trading_status,
                                          p_old_record.is_is_target_above_set,
                                          p_old_record.is_target_above,
                                          p_old_record.quote_strike_price,
                                          p_old_record.underlying_instrument,
                                          p_old_record.evaluation_price,
                                          p_old_record.crrnt_open_trade_amount,
                                          p_old_record.crrnt_open_trade_amount_ccy,
                                          p_old_record.max_open_trade_amount,
                                          p_old_record.max_open_trade_amount_ccy,
                                          p_old_record.evaluation_fx_reval_rate,
                                          p_old_record.amount_fx_reval_rate_bid,
                                          p_old_record.amount_fx_reval_rate_ask,
                                          p_old_record.pnl_fx_reval_rate_bid,
                                          p_old_record.pnl_fx_reval_rate_ask,
                                          p_old_record.pnl_fx_reval_rate_mid,
                                          p_old_record.market_price,
                                          p_old_record.min_distance,
                                          p_old_record.parent_target_price,
                                          p_old_record.take_profit_price,
                                          p_old_record.position_size,
                                          p_old_record.max_position_size,
                                          p_old_record.quantity,
                                          p_old_record.max_quantity,
                                          p_old_record.product_max_position_size,
                                          p_old_record.product_trade_qty_maximum,
                                          p_old_record.quote_proportion,
                                          p_old_record.margin,
                                          p_old_record.equity,
                                          p_old_record.equity_change,
                                          p_old_record.margin_change,
                                          p_old_record.spread_loss,
                                          p_old_record.premium,
                                          p_old_record.stop_loss_price,
                                          p_old_record.stop_loss_trailing_distance,
                                          p_old_record.guaranteed_stop_loss_price,
                                          p_old_record.level_1_bid,
                                          p_old_record.level_1_ask,
                                          p_old_record.is_price_offset_index_set,
                                          p_old_record.price_offset_index,
                                          p_old_record.loss_amount,
                                          p_old_record.current_profit_loss_amount,
                                          p_old_record.loss_limit_amount,
                                          p_old_record.period_type,
                                          p_old_record.period,
                                          p_old_record.swsp_timeout_at,
                                          p_old_record.risk_reason,
                                          p_old_record.is_queue_count_set,
                                          p_old_record.queue_count,
                                          p_old_record.is_executions_per_period_set,
                                          p_old_record.executions_per_period,
                                          p_old_record.is_exctns_prd_in_mllscnds_set,
                                          p_old_record.exctns_prd_in_mllscnds,
                                          p_old_record.execution_price,
                                          p_old_record.aggregated_quantity,
                                          p_old_record.opposite_price,
                                          p_old_record.exposure,
                                          p_old_record.max_exposure,
                                          p_old_record.is_product_max_exposure_set,
                                          p_old_record.product_max_exposure,
                                          p_old_record.total_exposure,
                                          p_old_record.max_total_exposure,
                                          p_old_record.is_prdct_mx_ttl_expsr_set,
                                          p_old_record.prdct_mx_ttl_expsr,
                                          p_old_record.expiry_time,
                                          p_old_record.good_for_time,
                                          p_old_record.execution_time,
                                          p_old_record.utc_now,
                                          p_old_record.start_time,
                                          p_old_record.is_timeout_ms_set,
                                          p_old_record.timeout_ms,
                                          p_old_record.max_stake_size_individual,
                                          p_old_record.min_stake_size,
                                          p_old_record.concurrent_stake,
                                          p_old_record.max_concurrent_stake,
                                          p_old_record.prdct_mx_stk_sz_cncrrnt,
                                          p_old_record.stop_loss_distance,
                                          p_old_record.guaranteed_stop_loss_distance,
                                          p_old_record.currency,
                                          p_old_record.currency_type,
                                          p_old_record.take_profit_distance,
                                          p_old_record.order_type_maximum,
                                          p_old_record.quote_received_time,
                                          p_old_record.max_opn_pstn_amnt_grss,
                                          p_old_record.max_opn_pstn_amnt_grss_ccy,
                                          p_old_record.crrnt_opn_pstn_amnt_grss,
                                          p_old_record.crrnt_opn_pstn_amnt_grss_ccy,
                                          p_old_record.max_opn_pstn_amnt_net,
                                          p_old_record.max_opn_pstn_amnt_net_ccy,
                                          p_old_record.crrnt_opn_pstn_amnt_net,
                                          p_old_record.crrnt_opn_pstn_amnt_net_ccy,
                                          p_old_record.evaltn_fxrevalrate_insttousd,
                                          p_old_record.evaltn_fxrevalrate_pairtousd
                                          );
      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          UPDATE orders_close_comment_h
          SET logical_load_timestamp = p_old_record.logical_load_timestamp,
              created_by = p_old_record.created_by,
              create_timestamp = p_old_record.create_timestamp,
              updated_by = p_old_record.updated_by,
              update_timestamp = p_old_record.update_timestamp,
              effective_end_timestamp = p_effective_end_timestamp,
              action = p_action,
              action_timestamp = SYSTIMESTAMP,
              custom = p_old_record.custom,
              fx_rate_symbol = p_old_record.fx_rate_symbol,
              is_quote_id_set = p_old_record.is_quote_id_set,
              quote_id = p_old_record.quote_id,
              existing_order_id = p_old_record.existing_order_id,
              underlying_position_size = p_old_record.underlying_position_size,
              underlying_max_position_size = p_old_record.underlying_max_position_size,
              prdct_undrlng_mx_pstn_sz = p_old_record.prdct_undrlng_mx_pstn_sz,
              account_proportion = p_old_record.account_proportion,
              order_strike_price = p_old_record.order_strike_price,
              strike_price_additional = p_old_record.strike_price_additional,
              binary_trading_status = p_old_record.binary_trading_status,
              is_is_target_above_set = p_old_record.is_is_target_above_set,
              is_target_above = p_old_record.is_target_above,
              quote_strike_price = p_old_record.quote_strike_price,
              underlying_instrument = p_old_record.underlying_instrument,
              evaluation_price = p_old_record.evaluation_price,
              crrnt_open_trade_amount = p_old_record.crrnt_open_trade_amount,
              crrnt_open_trade_amount_ccy = p_old_record.crrnt_open_trade_amount_ccy,
              max_open_trade_amount = p_old_record.max_open_trade_amount,
              max_open_trade_amount_ccy = p_old_record.max_open_trade_amount_ccy,
              evaluation_fx_reval_rate = p_old_record.evaluation_fx_reval_rate,
              amount_fx_reval_rate_bid = p_old_record.amount_fx_reval_rate_bid,
              amount_fx_reval_rate_ask = p_old_record.amount_fx_reval_rate_ask,
              pnl_fx_reval_rate_bid = p_old_record.pnl_fx_reval_rate_bid,
              pnl_fx_reval_rate_ask = p_old_record.pnl_fx_reval_rate_ask,
              pnl_fx_reval_rate_mid = p_old_record.pnl_fx_reval_rate_mid,
              market_price = p_old_record.market_price,
              min_distance = p_old_record.min_distance,
              parent_target_price = p_old_record.parent_target_price,
              take_profit_price = p_old_record.take_profit_price,
              position_size = p_old_record.position_size,
              max_position_size = p_old_record.max_position_size,
              quantity = p_old_record.quantity,
              max_quantity = p_old_record.max_quantity,
              product_max_position_size = p_old_record.product_max_position_size,
              product_trade_qty_maximum = p_old_record.product_trade_qty_maximum,
              quote_proportion = p_old_record.quote_proportion,
              margin = p_old_record.margin,
              equity = p_old_record.equity,
              equity_change = p_old_record.equity_change,
              margin_change = p_old_record.margin_change,
              spread_loss = p_old_record.spread_loss,
              premium = p_old_record.premium,
              stop_loss_price = p_old_record.stop_loss_price,
              stop_loss_trailing_distance = p_old_record.stop_loss_trailing_distance,
              guaranteed_stop_loss_price = p_old_record.guaranteed_stop_loss_price,
              level_1_bid = p_old_record.level_1_bid,
              level_1_ask = p_old_record.level_1_ask,
              is_price_offset_index_set = p_old_record.is_price_offset_index_set,
              price_offset_index = p_old_record.price_offset_index,
              loss_amount = p_old_record.loss_amount,
              current_profit_loss_amount = p_old_record.current_profit_loss_amount,
              loss_limit_amount = p_old_record.loss_limit_amount,
              period_type = p_old_record.period_type,
              period = p_old_record.period,
              swsp_timeout_at = p_old_record.swsp_timeout_at,
              risk_reason = p_old_record.risk_reason,
              is_queue_count_set = p_old_record.is_queue_count_set,
              queue_count = p_old_record.queue_count,
              is_executions_per_period_set = p_old_record.is_executions_per_period_set,
              executions_per_period = p_old_record.executions_per_period,
              is_exctns_prd_in_mllscnds_set = p_old_record.is_exctns_prd_in_mllscnds_set,
              exctns_prd_in_mllscnds = p_old_record.exctns_prd_in_mllscnds,
              execution_price = p_old_record.execution_price,
              aggregated_quantity = p_old_record.aggregated_quantity,
              opposite_price = p_old_record.opposite_price,
              exposure = p_old_record.exposure,
              max_exposure = p_old_record.max_exposure,
              is_product_max_exposure_set = p_old_record.is_product_max_exposure_set,
              product_max_exposure = p_old_record.product_max_exposure,
              total_exposure = p_old_record.total_exposure,
              max_total_exposure = p_old_record.max_total_exposure,
              is_prdct_mx_ttl_expsr_set = p_old_record.is_prdct_mx_ttl_expsr_set,
              prdct_mx_ttl_expsr = p_old_record.prdct_mx_ttl_expsr,
              expiry_time = p_old_record.expiry_time,
              good_for_time = p_old_record.good_for_time,
              execution_time = p_old_record.execution_time,
              utc_now = p_old_record.utc_now,
              start_time = p_old_record.start_time,
              is_timeout_ms_set = p_old_record.is_timeout_ms_set,
              timeout_ms = p_old_record.timeout_ms,
              max_stake_size_individual = p_old_record.max_stake_size_individual,
              min_stake_size = p_old_record.min_stake_size,
              concurrent_stake = p_old_record.concurrent_stake,
              max_concurrent_stake = p_old_record.max_concurrent_stake,
              prdct_mx_stk_sz_cncrrnt = p_old_record.prdct_mx_stk_sz_cncrrnt,
              stop_loss_distance = p_old_record.stop_loss_distance,
              guaranteed_stop_loss_distance = p_old_record.guaranteed_stop_loss_distance,
              currency = p_old_record.currency,
              currency_type = p_old_record.currency_type,
              take_profit_distance = p_old_record.take_profit_distance,
              order_type_maximum = p_old_record.order_type_maximum,
              quote_received_time = p_old_record.quote_received_time,
              max_opn_pstn_amnt_grss = p_old_record.max_opn_pstn_amnt_grss,
              max_opn_pstn_amnt_grss_ccy = p_old_record.max_opn_pstn_amnt_grss_ccy,
              crrnt_opn_pstn_amnt_grss = p_old_record.crrnt_opn_pstn_amnt_grss,
              crrnt_opn_pstn_amnt_grss_ccy = p_old_record.crrnt_opn_pstn_amnt_grss_ccy,
              max_opn_pstn_amnt_net = p_old_record.max_opn_pstn_amnt_net,
              max_opn_pstn_amnt_net_ccy = p_old_record.max_opn_pstn_amnt_net_ccy,
              crrnt_opn_pstn_amnt_net = p_old_record.crrnt_opn_pstn_amnt_net,
              crrnt_opn_pstn_amnt_net_ccy = p_old_record.crrnt_opn_pstn_amnt_net_ccy,
              evaltn_fxrevalrate_insttousd = p_old_record.evaltn_fxrevalrate_insttousd,
              evaltn_fxrevalrate_pairtousd = p_old_record.evaltn_fxrevalrate_pairtousd
        WHERE order_id = p_old_record.order_id AND
              platform = p_old_record.platform AND
              effective_start_timestamp = p_old_record.effective_start_timestamp;

    END;

    -- ===================================================================================
    -- PUBLIC MODULES
    -- ===================================================================================
    --

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC
    IS

    BEGIN
        logger.logger.set_module('version');
        RETURN gc_version;
    EXCEPTION
        WHEN OTHERS THEN
            logger.logger.severe(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            RAISE;
    END version;

    -- ===================================================================================
    -- create_order_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a order stub in case the order is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     ORDERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_order_id                       Order Id
    --     p_platform                       Platform
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_order_stub (p_user                       IN orders.created_by%TYPE,
                                 p_logical_load_timestamp     IN orders.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp  IN orders.effective_start_timestamp%TYPE,
                                 p_order_id                   in orders.order_id%TYPE,
                                 p_platform                   IN orders.platform%TYPE)
    IS

      PRAGMA AUTONOMOUS_TRANSACTION;

    BEGIN
      INSERT INTO orders (order_id,
                          platform,
                          logical_load_timestamp,
                          created_by,
                          create_timestamp,
                          updated_by,
                          update_timestamp,
                          effective_start_timestamp,
                          business_date,
                          reporting_date)
                  VALUES( p_order_id,
                          p_platform,
                          p_logical_load_timestamp,
                          p_user,
                          SYSTIMESTAMP,
                          p_user,
                          SYSTIMESTAMP,
                          gc_default_timestamp,
                          gc_default_timestamp,
                          gc_default_timestamp
                         );
      COMMIT;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the order already exists no need to create the stub
        --
        NULL;
    END create_order_stub;

  PROCEDURE put_order_type  (p_user                         IN order_types.created_by%TYPE,
                             p_effective_start_timestamp    IN order_types.effective_start_timestamp%TYPE,
                             p_logical_load_timestamp       IN order_types.logical_load_timestamp%TYPE,
                             p_order_type                   IN order_types.order_type%TYPE) IS

  BEGIN
    logger.logger.set_module('put_order_type');

    BEGIN
      INSERT INTO order_types (order_type,
                               logical_load_timestamp,
                               created_by,
                               create_timestamp,
                               updated_by,
                               update_timestamp,
                               effective_start_timestamp)
                        VALUES (p_order_type,
                                p_logical_load_timestamp,
                                p_user,
                                SYSTIMESTAMP,
                                p_user,
                                SYSTIMESTAMP,
                                p_effective_start_timestamp);
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        -- Order Type already exists so no need to insert
        --
        NULL;
    END;

    logger.logger.set_module(NULL);
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
    -- put_order_close_comment
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write Order Close Comment
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_order_id                      Order Id
    --      p_platform                      Platform
    --      p_close_comment                 Close Comment Details
    --      p_user                          User
    --      p_effective_start_timestamp     Effective Start Timestamp
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_order_close_comment (p_order_id                   IN orders_close_comment.order_id%TYPE,
                                       p_platform                   IN orders_close_comment.platform%TYPE,
                                       p_close_comment              IN order_close_comment_obj,
                                       p_user                       IN orders_close_comment.created_by%TYPE,
                                       p_effective_start_timestamp  IN orders_close_comment.effective_start_timestamp%TYPE,
                                       p_old_effctv_strt_timestamp  IN orders_close_comment.effective_start_timestamp%TYPE,
                                       p_logical_load_timestamp     IN orders_close_comment.logical_load_timestamp%TYPE) IS

      lv_logical_load_timestamp TIMESTAMP(6);

      lv_found NUMBER(1);

      lv_old_record orders_close_comment%ROWTYPE;

    BEGIN

      lv_logical_load_timestamp := p_logical_load_timestamp;

      lv_found := 0;

      --
      --Try inserting the message
      --

      BEGIN
        INSERT INTO orders_close_comment(order_id ,
                                         platform ,
                                         logical_load_timestamp ,
                                         created_by ,
                                         create_timestamp ,
                                         updated_by ,
                                         update_timestamp ,
                                         effective_start_timestamp ,
                                         custom ,
                                         fx_rate_symbol ,
                                         is_quote_id_set ,
                                         quote_id ,
                                         existing_order_id ,
                                         underlying_position_size ,
                                         underlying_max_position_size ,
                                         prdct_undrlng_mx_pstn_sz ,
                                         account_proportion ,
                                         order_strike_price ,
                                         strike_price_additional ,
                                         binary_trading_status ,
                                         is_is_target_above_set ,
                                         is_target_above ,
                                         quote_strike_price ,
                                         underlying_instrument ,
                                         evaluation_price ,
                                         crrnt_open_trade_amount ,
                                         crrnt_open_trade_amount_ccy ,
                                         max_open_trade_amount ,
                                         max_open_trade_amount_ccy ,
                                         evaluation_fx_reval_rate ,
                                         amount_fx_reval_rate_bid ,
                                         amount_fx_reval_rate_ask ,
                                         pnl_fx_reval_rate_bid ,
                                         pnl_fx_reval_rate_ask ,
                                         pnl_fx_reval_rate_mid ,
                                         market_price ,
                                         min_distance ,
                                         parent_target_price ,
                                         take_profit_price ,
                                         position_size ,
                                         max_position_size ,
                                         quantity ,
                                         max_quantity ,
                                         product_max_position_size ,
                                         product_trade_qty_maximum ,
                                         quote_proportion ,
                                         margin ,
                                         equity ,
                                         equity_change ,
                                         margin_change ,
                                         spread_loss ,
                                         premium ,
                                         stop_loss_price ,
                                         stop_loss_trailing_distance ,
                                         guaranteed_stop_loss_price ,
                                         level_1_bid ,
                                         level_1_ask ,
                                         is_price_offset_index_set ,
                                         price_offset_index ,
                                         loss_amount ,
                                         current_profit_loss_amount ,
                                         loss_limit_amount ,
                                         period_type ,
                                         period ,
                                         swsp_timeout_at ,
                                         risk_reason ,
                                         is_queue_count_set ,
                                         queue_count ,
                                         is_executions_per_period_set ,
                                         executions_per_period ,
                                         is_exctns_prd_in_mllscnds_set ,
                                         exctns_prd_in_mllscnds ,
                                         execution_price ,
                                         aggregated_quantity ,
                                         opposite_price ,
                                         exposure ,
                                         max_exposure ,
                                         is_product_max_exposure_set ,
                                         product_max_exposure ,
                                         total_exposure ,
                                         max_total_exposure ,
                                         is_prdct_mx_ttl_expsr_set ,
                                         prdct_mx_ttl_expsr ,
                                         expiry_time ,
                                         good_for_time ,
                                         execution_time ,
                                         utc_now ,
                                         start_time ,
                                         is_timeout_ms_set ,
                                         timeout_ms ,
                                         max_stake_size_individual ,
                                         min_stake_size ,
                                         concurrent_stake ,
                                         max_concurrent_stake,
                                         prdct_mx_stk_sz_cncrrnt,
                                         stop_loss_distance,
                                         guaranteed_stop_loss_distance,
                                         currency,
                                         currency_type,
                                         take_profit_distance,
                                         order_type_maximum,
                                         quote_received_time,
                                         max_opn_pstn_amnt_grss,
                                         max_opn_pstn_amnt_grss_ccy,
                                         crrnt_opn_pstn_amnt_grss,
                                         crrnt_opn_pstn_amnt_grss_ccy,
                                         max_opn_pstn_amnt_net,
                                         max_opn_pstn_amnt_net_ccy,
                                         crrnt_opn_pstn_amnt_net,
                                         crrnt_opn_pstn_amnt_net_ccy,
                                         evaltn_fxrevalrate_insttousd,
                                         evaltn_fxrevalrate_pairtousd
                                         )
                                 VALUES (p_order_id,
                                         p_platform,
                                         lv_logical_load_timestamp,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_effective_start_timestamp,
                                         p_close_comment.custom,
                                         p_close_comment.fx_rate_symbol,
                                         p_close_comment.is_quote_id_set,
                                         p_close_comment.quote_id,
                                         p_close_comment.existing_order_id,
                                         p_close_comment.underlying_position_size,
                                         p_close_comment.underlying_max_position_size,
                                         p_close_comment.prdct_undrlng_mx_pstn_sz,
                                         p_close_comment.account_proportion,
                                         p_close_comment.order_strike_price,
                                         p_close_comment.strike_price_additional,
                                         p_close_comment.binary_trading_status,
                                         p_close_comment.is_is_target_above_set,
                                         p_close_comment.is_target_above,
                                         p_close_comment.quote_strike_price,
                                         p_close_comment.underlying_instrument,
                                         p_close_comment.evaluation_price,
                                         p_close_comment.crrnt_open_trade_amount,
                                         p_close_comment.crrnt_open_trade_amount_ccy,
                                         p_close_comment.max_open_trade_amount,
                                         p_close_comment.max_open_trade_amount_ccy,
                                         p_close_comment.evaluation_fx_reval_rate,
                                         p_close_comment.amount_fx_reval_rate_bid,
                                         p_close_comment.amount_fx_reval_rate_ask,
                                         p_close_comment.pnl_fx_reval_rate_bid,
                                         p_close_comment.pnl_fx_reval_rate_ask,
                                         p_close_comment.pnl_fx_reval_rate_mid,
                                         p_close_comment.market_price,
                                         p_close_comment.min_distance,
                                         p_close_comment.parent_target_price,
                                         p_close_comment.take_profit_price,
                                         p_close_comment.position_size,
                                         p_close_comment.max_position_size,
                                         p_close_comment.quantity,
                                         p_close_comment.max_quantity,
                                         p_close_comment.product_max_position_size,
                                         p_close_comment.product_trade_qty_maximum,
                                         p_close_comment.quote_proportion,
                                         p_close_comment.margin,
                                         p_close_comment.equity,
                                         p_close_comment.equity_change,
                                         p_close_comment.margin_change,
                                         p_close_comment.spread_loss,
                                         p_close_comment.premium,
                                         p_close_comment.stop_loss_price,
                                         p_close_comment.stop_loss_trailing_distance,
                                         p_close_comment.guaranteed_stop_loss_price,
                                         p_close_comment.level_1_bid,
                                         p_close_comment.level_1_ask,
                                         p_close_comment.is_price_offset_index_set,
                                         p_close_comment.price_offset_index,
                                         p_close_comment.loss_amount,
                                         p_close_comment.current_profit_loss_amount,
                                         p_close_comment.loss_limit_amount,
                                         p_close_comment.period_type,
                                         p_close_comment.period,
                                         p_close_comment.swsp_timeout_at,
                                         p_close_comment.risk_reason,
                                         p_close_comment.is_queue_count_set,
                                         p_close_comment.queue_count,
                                         p_close_comment.is_executions_per_period_set,
                                         p_close_comment.executions_per_period,
                                         p_close_comment.is_exctns_prd_in_mllscnds_set,
                                         p_close_comment.exctns_prd_in_mllscnds,
                                         p_close_comment.execution_price,
                                         p_close_comment.aggregated_quantity,
                                         p_close_comment.opposite_price,
                                         p_close_comment.exposure,
                                         p_close_comment.max_exposure,
                                         p_close_comment.is_product_max_exposure_set,
                                         p_close_comment.product_max_exposure,
                                         p_close_comment.total_exposure,
                                         p_close_comment.max_total_exposure,
                                         p_close_comment.is_prdct_mx_ttl_expsr_set,
                                         p_close_comment.prdct_mx_ttl_expsr,
                                         p_close_comment.expiry_time,
                                         p_close_comment.good_for_time,
                                         p_close_comment.execution_time,
                                         p_close_comment.utc_now,
                                         p_close_comment.start_time,
                                         p_close_comment.is_timeout_ms_set,
                                         p_close_comment.timeout_ms,
                                         p_close_comment.max_stake_size_individual,
                                         p_close_comment.min_stake_size,
                                         p_close_comment.concurrent_stake,
                                         p_close_comment.max_concurrent_stake,
                                         p_close_comment.prdct_mx_stk_sz_cncrrnt,
                                         p_close_comment.stop_loss_distance,
                                         p_close_comment.guaranteed_stop_loss_distance,
                                         p_close_comment.currency,
                                         p_close_comment.currency_type,
                                         p_close_comment.take_profit_distance,
                                         p_close_comment.order_type_maximum,
                                         p_close_comment.quote_received_time,
                                         p_close_comment.max_opn_pstn_amnt_grss,
                                         p_close_comment.max_opn_pstn_amnt_grss_ccy,
                                         p_close_comment.crrnt_opn_pstn_amnt_grss,
                                         p_close_comment.crrnt_opn_pstn_amnt_grss_ccy,
                                         p_close_comment.max_opn_pstn_amnt_net,
                                         p_close_comment.max_opn_pstn_amnt_net_ccy,
                                         p_close_comment.crrnt_opn_pstn_amnt_net,
                                         p_close_comment.crrnt_opn_pstn_amnt_net_ccy,
                                         p_close_comment.evaltn_fxrevalrate_insttousd,
                                         p_close_comment.evaltn_fxrevalrate_pairtousd
                                         );
      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          lv_found := 1;

          SELECT *
          INTO lv_old_record
          FROM orders_close_comment
          WHERE order_id = p_order_id AND
                platform = p_platform;
      END;

      CASE
        WHEN lv_found = 0 THEN
          --
          --Nothing to do. Record already inserted
          --
          NULL;
        WHEN lv_found = 1 AND p_effective_start_timestamp >= p_old_effctv_strt_timestamp THEN
          UPDATE orders_close_comment
          SET logical_load_timestamp = lv_logical_load_timestamp,
              updated_by = p_user,
              update_timestamp = SYSTIMESTAMP,
              effective_start_timestamp = p_effective_start_timestamp,
              custom = p_close_comment.custom,
              fx_rate_symbol = p_close_comment.fx_rate_symbol,
              is_quote_id_set = p_close_comment.is_quote_id_set,
              quote_id = p_close_comment.quote_id,
              existing_order_id = p_close_comment.existing_order_id,
              underlying_position_size = p_close_comment.underlying_position_size,
              underlying_max_position_size = p_close_comment.underlying_max_position_size,
              prdct_undrlng_mx_pstn_sz = p_close_comment.prdct_undrlng_mx_pstn_sz,
              account_proportion = p_close_comment.account_proportion,
              order_strike_price = p_close_comment.order_strike_price,
              strike_price_additional = p_close_comment.strike_price_additional,
              binary_trading_status = p_close_comment.binary_trading_status,
              is_is_target_above_set = p_close_comment.is_is_target_above_set,
              is_target_above = p_close_comment.is_target_above,
              quote_strike_price = p_close_comment.quote_strike_price,
              underlying_instrument = p_close_comment.underlying_instrument,
              evaluation_price = p_close_comment.evaluation_price,
              crrnt_open_trade_amount = p_close_comment.crrnt_open_trade_amount,
              crrnt_open_trade_amount_ccy = p_close_comment.crrnt_open_trade_amount_ccy,
              max_open_trade_amount = p_close_comment.max_open_trade_amount,
              max_open_trade_amount_ccy = p_close_comment.max_open_trade_amount_ccy,
              evaluation_fx_reval_rate = p_close_comment.evaluation_fx_reval_rate,
              amount_fx_reval_rate_bid = p_close_comment.amount_fx_reval_rate_bid,
              amount_fx_reval_rate_ask = p_close_comment.amount_fx_reval_rate_ask,
              pnl_fx_reval_rate_bid = p_close_comment.pnl_fx_reval_rate_bid,
              pnl_fx_reval_rate_ask = p_close_comment.pnl_fx_reval_rate_ask,
              pnl_fx_reval_rate_mid = p_close_comment.pnl_fx_reval_rate_mid,
              market_price = p_close_comment.market_price,
              min_distance = p_close_comment.min_distance,
              parent_target_price = p_close_comment.parent_target_price,
              take_profit_price = p_close_comment.take_profit_price,
              position_size = p_close_comment.position_size,
              max_position_size = p_close_comment.max_position_size,
              quantity = p_close_comment.quantity,
              max_quantity = p_close_comment.max_quantity,
              product_max_position_size = p_close_comment.product_max_position_size,
              product_trade_qty_maximum = p_close_comment.product_trade_qty_maximum,
              quote_proportion = p_close_comment.quote_proportion,
              margin = p_close_comment.margin,
              equity = p_close_comment.equity,
              equity_change = p_close_comment.equity_change,
              margin_change = p_close_comment.margin_change,
              spread_loss = p_close_comment.spread_loss,
              premium = p_close_comment.premium,
              stop_loss_price = p_close_comment.stop_loss_price,
              stop_loss_trailing_distance = p_close_comment.stop_loss_trailing_distance,
              guaranteed_stop_loss_price = p_close_comment.guaranteed_stop_loss_price,
              level_1_bid = p_close_comment.level_1_bid,
              level_1_ask = p_close_comment.level_1_ask,
              is_price_offset_index_set = p_close_comment.is_price_offset_index_set,
              price_offset_index = p_close_comment.price_offset_index,
              loss_amount = p_close_comment.loss_amount,
              current_profit_loss_amount = p_close_comment.current_profit_loss_amount,
              loss_limit_amount = p_close_comment.loss_limit_amount,
              period_type = p_close_comment.period_type,
              period = p_close_comment.period,
              swsp_timeout_at = p_close_comment.swsp_timeout_at,
              risk_reason = p_close_comment.risk_reason,
              is_queue_count_set = p_close_comment.is_queue_count_set,
              queue_count = p_close_comment.queue_count,
              is_executions_per_period_set = p_close_comment.is_executions_per_period_set,
              executions_per_period = p_close_comment.executions_per_period,
              is_exctns_prd_in_mllscnds_set = p_close_comment.is_exctns_prd_in_mllscnds_set,
              exctns_prd_in_mllscnds = p_close_comment.exctns_prd_in_mllscnds,
              execution_price = p_close_comment.execution_price,
              aggregated_quantity = p_close_comment.aggregated_quantity,
              opposite_price = p_close_comment.opposite_price,
              exposure = p_close_comment.exposure,
              max_exposure = p_close_comment.max_exposure,
              is_product_max_exposure_set = p_close_comment.is_product_max_exposure_set,
              product_max_exposure = p_close_comment.product_max_exposure,
              total_exposure = p_close_comment.total_exposure,
              max_total_exposure = p_close_comment.max_total_exposure,
              is_prdct_mx_ttl_expsr_set = p_close_comment.is_prdct_mx_ttl_expsr_set,
              prdct_mx_ttl_expsr = p_close_comment.prdct_mx_ttl_expsr,
              expiry_time = p_close_comment.expiry_time,
              good_for_time = p_close_comment.good_for_time,
              execution_time = p_close_comment.execution_time,
              utc_now = p_close_comment.utc_now,
              start_time = p_close_comment.start_time,
              is_timeout_ms_set = p_close_comment.is_timeout_ms_set,
              timeout_ms = p_close_comment.timeout_ms,
              max_stake_size_individual = p_close_comment.max_stake_size_individual,
              min_stake_size = p_close_comment.min_stake_size,
              concurrent_stake = p_close_comment.concurrent_stake,
              max_concurrent_stake = p_close_comment.max_concurrent_stake,
              prdct_mx_stk_sz_cncrrnt = p_close_comment.prdct_mx_stk_sz_cncrrnt,
              stop_loss_distance = p_close_comment.stop_loss_distance,
              guaranteed_stop_loss_distance = p_close_comment.guaranteed_stop_loss_distance,
              currency = p_close_comment.currency,
              currency_type = p_close_comment.currency_type,
              take_profit_distance = p_close_comment.take_profit_distance,
              order_type_maximum = p_close_comment.order_type_maximum,
              quote_received_time = p_close_comment.quote_received_time,
              max_opn_pstn_amnt_grss = p_close_comment.max_opn_pstn_amnt_grss,
              max_opn_pstn_amnt_grss_ccy = p_close_comment.max_opn_pstn_amnt_grss_ccy,
              crrnt_opn_pstn_amnt_grss = p_close_comment.crrnt_opn_pstn_amnt_grss,
              crrnt_opn_pstn_amnt_grss_ccy = p_close_comment.crrnt_opn_pstn_amnt_grss_ccy,
              max_opn_pstn_amnt_net = p_close_comment.max_opn_pstn_amnt_net,
              max_opn_pstn_amnt_net_ccy = p_close_comment.max_opn_pstn_amnt_net_ccy,
              crrnt_opn_pstn_amnt_net = p_close_comment.crrnt_opn_pstn_amnt_net,
              crrnt_opn_pstn_amnt_net_ccy = p_close_comment.crrnt_opn_pstn_amnt_net_ccy,
              evaltn_fxrevalrate_insttousd = p_close_comment.evaltn_fxrevalrate_insttousd,
              evaltn_fxrevalrate_pairtousd = p_close_comment.evaltn_fxrevalrate_pairtousd
          WHERE order_id = p_order_id AND
                platform = p_platform AND
                (nrg_common.has_value_changed(custom, p_close_comment.custom) = 1 OR
                 nrg_common.has_value_changed(fx_rate_symbol, p_close_comment.fx_rate_symbol) = 1 OR
                 nrg_common.has_value_changed(is_quote_id_set, p_close_comment.is_quote_id_set) = 1 OR
                 nrg_common.has_value_changed(quote_id, p_close_comment.quote_id) = 1 OR
                 nrg_common.has_value_changed(existing_order_id, p_close_comment.existing_order_id) = 1 OR
                 nrg_common.has_value_changed(underlying_position_size, p_close_comment.underlying_position_size) = 1 OR
                 nrg_common.has_value_changed(underlying_max_position_size, p_close_comment.underlying_max_position_size) = 1 OR
                 nrg_common.has_value_changed(prdct_undrlng_mx_pstn_sz, p_close_comment.prdct_undrlng_mx_pstn_sz) = 1 OR
                 nrg_common.has_value_changed(account_proportion, p_close_comment.account_proportion) = 1 OR
                 nrg_common.has_value_changed(order_strike_price, p_close_comment.order_strike_price) = 1 OR
                 nrg_common.has_value_changed(strike_price_additional, p_close_comment.strike_price_additional) = 1 OR
                 nrg_common.has_value_changed(binary_trading_status, p_close_comment.binary_trading_status) = 1 OR
                 nrg_common.has_value_changed(is_is_target_above_set, p_close_comment.is_is_target_above_set) = 1 OR
                 nrg_common.has_value_changed(is_target_above, p_close_comment.is_target_above) = 1 OR
                 nrg_common.has_value_changed(quote_strike_price, p_close_comment.quote_strike_price) = 1 OR
                 nrg_common.has_value_changed(underlying_instrument, p_close_comment.underlying_instrument) = 1 OR
                 nrg_common.has_value_changed(evaluation_price, p_close_comment.evaluation_price) = 1 OR
                 nrg_common.has_value_changed(crrnt_open_trade_amount, p_close_comment.crrnt_open_trade_amount) = 1 OR
                 nrg_common.has_value_changed(crrnt_open_trade_amount_ccy, p_close_comment.crrnt_open_trade_amount_ccy) = 1 OR
                 nrg_common.has_value_changed(max_open_trade_amount, p_close_comment.max_open_trade_amount) = 1 OR
                 nrg_common.has_value_changed(max_open_trade_amount_ccy, p_close_comment.max_open_trade_amount_ccy) = 1 OR
                 nrg_common.has_value_changed(evaluation_fx_reval_rate, p_close_comment.evaluation_fx_reval_rate) = 1 OR
                 nrg_common.has_value_changed(amount_fx_reval_rate_bid, p_close_comment.amount_fx_reval_rate_bid) = 1 OR
                 nrg_common.has_value_changed(amount_fx_reval_rate_ask, p_close_comment.amount_fx_reval_rate_ask) = 1 OR
                 nrg_common.has_value_changed(pnl_fx_reval_rate_bid, p_close_comment.pnl_fx_reval_rate_bid) = 1 OR
                 nrg_common.has_value_changed(pnl_fx_reval_rate_ask, p_close_comment.pnl_fx_reval_rate_ask) = 1 OR
                 nrg_common.has_value_changed(pnl_fx_reval_rate_mid, p_close_comment.pnl_fx_reval_rate_mid) = 1 OR
                 nrg_common.has_value_changed(market_price, p_close_comment.market_price) = 1 OR
                 nrg_common.has_value_changed(min_distance, p_close_comment.min_distance) = 1 OR
                 nrg_common.has_value_changed(parent_target_price, p_close_comment.parent_target_price) = 1 OR
                 nrg_common.has_value_changed(take_profit_price, p_close_comment.take_profit_price) = 1 OR
                 nrg_common.has_value_changed(position_size, p_close_comment.position_size) = 1 OR
                 nrg_common.has_value_changed(max_position_size, p_close_comment.max_position_size) = 1 OR
                 nrg_common.has_value_changed(quantity, p_close_comment.quantity) = 1 OR
                 nrg_common.has_value_changed(max_quantity, p_close_comment.max_quantity) = 1 OR
                 nrg_common.has_value_changed(product_max_position_size, p_close_comment.product_max_position_size) = 1 OR
                 nrg_common.has_value_changed(product_trade_qty_maximum, p_close_comment.product_trade_qty_maximum) = 1 OR
                 nrg_common.has_value_changed(quote_proportion, p_close_comment.quote_proportion) = 1 OR
                 nrg_common.has_value_changed(margin, p_close_comment.margin) = 1 OR
                 nrg_common.has_value_changed(equity, p_close_comment.equity) = 1 OR
                 nrg_common.has_value_changed(equity_change, p_close_comment.equity_change) = 1 OR
                 nrg_common.has_value_changed(margin_change, p_close_comment.margin_change) = 1 OR
                 nrg_common.has_value_changed(spread_loss, p_close_comment.spread_loss) = 1 OR
                 nrg_common.has_value_changed(premium, p_close_comment.premium) = 1 OR
                 nrg_common.has_value_changed(stop_loss_price, p_close_comment.stop_loss_price) = 1 OR
                 nrg_common.has_value_changed(stop_loss_trailing_distance, p_close_comment.stop_loss_trailing_distance) = 1 OR
                 nrg_common.has_value_changed(guaranteed_stop_loss_price, p_close_comment.guaranteed_stop_loss_price) = 1 OR
                 nrg_common.has_value_changed(level_1_bid, p_close_comment.level_1_bid) = 1 OR
                 nrg_common.has_value_changed(level_1_ask, p_close_comment.level_1_ask) = 1 OR
                 nrg_common.has_value_changed(is_price_offset_index_set, p_close_comment.is_price_offset_index_set) = 1 OR
                 nrg_common.has_value_changed(price_offset_index, p_close_comment.price_offset_index) = 1 OR
                 nrg_common.has_value_changed(loss_amount, p_close_comment.loss_amount) = 1 OR
                 nrg_common.has_value_changed(current_profit_loss_amount, p_close_comment.current_profit_loss_amount) = 1 OR
                 nrg_common.has_value_changed(loss_limit_amount, p_close_comment.loss_limit_amount) = 1 OR
                 nrg_common.has_value_changed(period_type, p_close_comment.period_type) = 1 OR
                 nrg_common.has_value_changed(period, p_close_comment.period) = 1 OR
                 nrg_common.has_value_changed(swsp_timeout_at, p_close_comment.swsp_timeout_at) = 1 OR
                 nrg_common.has_value_changed(risk_reason, p_close_comment.risk_reason) = 1 OR
                 nrg_common.has_value_changed(is_queue_count_set, p_close_comment.is_queue_count_set) = 1 OR
                 nrg_common.has_value_changed(queue_count, p_close_comment.queue_count) = 1 OR
                 nrg_common.has_value_changed(is_executions_per_period_set, p_close_comment.is_executions_per_period_set) = 1 OR
                 nrg_common.has_value_changed(executions_per_period, p_close_comment.executions_per_period) = 1 OR
                 nrg_common.has_value_changed(is_exctns_prd_in_mllscnds_set, p_close_comment.is_exctns_prd_in_mllscnds_set) = 1 OR
                 nrg_common.has_value_changed(exctns_prd_in_mllscnds, p_close_comment.exctns_prd_in_mllscnds) = 1 OR
                 nrg_common.has_value_changed(execution_price, p_close_comment.execution_price) = 1 OR
                 nrg_common.has_value_changed(aggregated_quantity, p_close_comment.aggregated_quantity) = 1 OR
                 nrg_common.has_value_changed(opposite_price, p_close_comment.opposite_price) = 1 OR
                 nrg_common.has_value_changed(exposure, p_close_comment.exposure) = 1 OR
                 nrg_common.has_value_changed(max_exposure, p_close_comment.max_exposure) = 1 OR
                 nrg_common.has_value_changed(is_product_max_exposure_set, p_close_comment.is_product_max_exposure_set) = 1 OR
                 nrg_common.has_value_changed(product_max_exposure, p_close_comment.product_max_exposure) = 1 OR
                 nrg_common.has_value_changed(total_exposure, p_close_comment.total_exposure) = 1 OR
                 nrg_common.has_value_changed(max_total_exposure, p_close_comment.max_total_exposure) = 1 OR
                 nrg_common.has_value_changed(is_prdct_mx_ttl_expsr_set, p_close_comment.is_prdct_mx_ttl_expsr_set) = 1 OR
                 nrg_common.has_value_changed(prdct_mx_ttl_expsr, p_close_comment.prdct_mx_ttl_expsr) = 1 OR
                 nrg_common.has_value_changed(expiry_time, p_close_comment.expiry_time) = 1 OR
                 nrg_common.has_value_changed(good_for_time, p_close_comment.good_for_time) = 1 OR
                 nrg_common.has_value_changed(execution_time, p_close_comment.execution_time) = 1 OR
                 nrg_common.has_value_changed(utc_now, p_close_comment.utc_now) = 1 OR
                 nrg_common.has_value_changed(start_time, p_close_comment.start_time) = 1 OR
                 nrg_common.has_value_changed(is_timeout_ms_set, p_close_comment.is_timeout_ms_set) = 1 OR
                 nrg_common.has_value_changed(timeout_ms, p_close_comment.timeout_ms) = 1 OR
                 nrg_common.has_value_changed(max_stake_size_individual, p_close_comment.max_stake_size_individual) = 1 OR
                 nrg_common.has_value_changed(min_stake_size, p_close_comment.min_stake_size) = 1 OR
                 nrg_common.has_value_changed(concurrent_stake, p_close_comment.concurrent_stake) = 1 OR
                 nrg_common.has_value_changed(max_concurrent_stake, p_close_comment.max_concurrent_stake) = 1 OR
                 nrg_common.has_value_changed(prdct_mx_stk_sz_cncrrnt, p_close_comment.prdct_mx_stk_sz_cncrrnt) = 1 OR
                 nrg_common.has_value_changed(stop_loss_distance, p_close_comment.stop_loss_distance) = 1 OR
                 nrg_common.has_value_changed(guaranteed_stop_loss_distance, p_close_comment.guaranteed_stop_loss_distance) = 1 OR
                 nrg_common.has_value_changed(currency, p_close_comment.currency) = 1 OR
                 nrg_common.has_value_changed(currency_type, p_close_comment.currency_type) = 1 OR
                 nrg_common.has_value_changed(take_profit_distance, p_close_comment.take_profit_distance) = 1 OR
                 nrg_common.has_value_changed(order_type_maximum, p_close_comment.order_type_maximum) = 1 OR
                 nrg_common.has_value_changed(quote_received_time, p_close_comment.quote_received_time) = 1 OR
                 nrg_common.has_value_changed(max_opn_pstn_amnt_grss, p_close_comment.max_opn_pstn_amnt_grss) = 1 OR
                 nrg_common.has_value_changed(max_opn_pstn_amnt_grss_ccy, p_close_comment.max_opn_pstn_amnt_grss_ccy) = 1 OR
                 nrg_common.has_value_changed(crrnt_opn_pstn_amnt_grss, p_close_comment.crrnt_opn_pstn_amnt_grss) = 1 OR
                 nrg_common.has_value_changed(crrnt_opn_pstn_amnt_grss_ccy, p_close_comment.crrnt_opn_pstn_amnt_grss_ccy) = 1 OR
                 nrg_common.has_value_changed(max_opn_pstn_amnt_net, p_close_comment.max_opn_pstn_amnt_net) = 1 OR
                 nrg_common.has_value_changed(max_opn_pstn_amnt_net_ccy, p_close_comment.max_opn_pstn_amnt_net_ccy) = 1 OR
                 nrg_common.has_value_changed(crrnt_opn_pstn_amnt_net, p_close_comment.crrnt_opn_pstn_amnt_net) = 1 OR
                 nrg_common.has_value_changed(crrnt_opn_pstn_amnt_net_ccy, p_close_comment.crrnt_opn_pstn_amnt_net_ccy) = 1 OR
                 nrg_common.has_value_changed(evaltn_fxrevalrate_insttousd, p_close_comment.evaltn_fxrevalrate_insttousd) = 1 OR
                 nrg_common.has_value_changed(evaltn_fxrevalrate_pairtousd, p_close_comment.evaltn_fxrevalrate_pairtousd) = 1
                 );

          IF SQL%ROWCOUNT > 0 THEN
            put_history (p_old_record               => lv_old_record,
                         p_effective_end_timestamp  => p_effective_start_timestamp,
                         p_action                   => 'U');
          END IF;

        WHEN lv_found = 1 AND p_effective_start_timestamp >= p_old_effctv_strt_timestamp THEN
          SELECT p_order_id,
                 p_platform,
                 lv_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp,
                 p_close_comment.custom,
                 p_close_comment.fx_rate_symbol,
                 p_close_comment.is_quote_id_set,
                 p_close_comment.quote_id,
                 p_close_comment.existing_order_id,
                 p_close_comment.underlying_position_size,
                 p_close_comment.underlying_max_position_size,
                 p_close_comment.prdct_undrlng_mx_pstn_sz,
                 p_close_comment.account_proportion,
                 p_close_comment.order_strike_price,
                 p_close_comment.strike_price_additional,
                 p_close_comment.binary_trading_status,
                 p_close_comment.is_is_target_above_set,
                 p_close_comment.is_target_above,
                 p_close_comment.quote_strike_price,
                 p_close_comment.underlying_instrument,
                 p_close_comment.evaluation_price,
                 p_close_comment.crrnt_open_trade_amount,
                 p_close_comment.crrnt_open_trade_amount_ccy,
                 p_close_comment.max_open_trade_amount,
                 p_close_comment.max_open_trade_amount_ccy,
                 p_close_comment.evaluation_fx_reval_rate,
                 p_close_comment.amount_fx_reval_rate_bid,
                 p_close_comment.amount_fx_reval_rate_ask,
                 p_close_comment.pnl_fx_reval_rate_bid,
                 p_close_comment.pnl_fx_reval_rate_ask,
                 p_close_comment.pnl_fx_reval_rate_mid,
                 p_close_comment.market_price,
                 p_close_comment.min_distance,
                 p_close_comment.parent_target_price,
                 p_close_comment.take_profit_price,
                 p_close_comment.position_size,
                 p_close_comment.max_position_size,
                 p_close_comment.quantity,
                 p_close_comment.max_quantity,
                 p_close_comment.product_max_position_size,
                 p_close_comment.product_trade_qty_maximum,
                 p_close_comment.quote_proportion,
                 p_close_comment.margin,
                 p_close_comment.equity,
                 p_close_comment.equity_change,
                 p_close_comment.margin_change,
                 p_close_comment.spread_loss,
                 p_close_comment.premium,
                 p_close_comment.stop_loss_price,
                 p_close_comment.stop_loss_trailing_distance,
                 p_close_comment.guaranteed_stop_loss_price,
                 p_close_comment.level_1_bid,
                 p_close_comment.level_1_ask,
                 p_close_comment.is_price_offset_index_set,
                 p_close_comment.price_offset_index,
                 p_close_comment.loss_amount,
                 p_close_comment.current_profit_loss_amount,
                 p_close_comment.loss_limit_amount,
                 p_close_comment.period_type,
                 p_close_comment.period,
                 p_close_comment.swsp_timeout_at,
                 p_close_comment.risk_reason,
                 p_close_comment.is_queue_count_set,
                 p_close_comment.queue_count,
                 p_close_comment.is_executions_per_period_set,
                 p_close_comment.executions_per_period,
                 p_close_comment.is_exctns_prd_in_mllscnds_set,
                 p_close_comment.exctns_prd_in_mllscnds,
                 p_close_comment.execution_price,
                 p_close_comment.aggregated_quantity,
                 p_close_comment.opposite_price,
                 p_close_comment.exposure,
                 p_close_comment.max_exposure,
                 p_close_comment.is_product_max_exposure_set,
                 p_close_comment.product_max_exposure,
                 p_close_comment.total_exposure,
                 p_close_comment.max_total_exposure,
                 p_close_comment.is_prdct_mx_ttl_expsr_set,
                 p_close_comment.prdct_mx_ttl_expsr,
                 p_close_comment.expiry_time,
                 p_close_comment.good_for_time,
                 p_close_comment.execution_time,
                 p_close_comment.utc_now,
                 p_close_comment.start_time,
                 p_close_comment.is_timeout_ms_set,
                 p_close_comment.timeout_ms,
                 p_close_comment.max_stake_size_individual,
                 p_close_comment.min_stake_size,
                 p_close_comment.concurrent_stake,
                 p_close_comment.max_concurrent_stake,
                 p_close_comment.prdct_mx_stk_sz_cncrrnt,
                 p_close_comment.stop_loss_distance,
                 p_close_comment.guaranteed_stop_loss_distance,
                 p_close_comment.currency,
                 p_close_comment.currency_type,
                 p_close_comment.take_profit_distance,
                 p_close_comment.order_type_maximum,
                 p_close_comment.quote_received_time,
                 p_close_comment.max_opn_pstn_amnt_grss,
                 p_close_comment.max_opn_pstn_amnt_grss_ccy,
                 p_close_comment.crrnt_opn_pstn_amnt_grss,
                 p_close_comment.crrnt_opn_pstn_amnt_grss_ccy,
                 p_close_comment.max_opn_pstn_amnt_net,
                 p_close_comment.max_opn_pstn_amnt_net_ccy,
                 p_close_comment.crrnt_opn_pstn_amnt_net,
                 p_close_comment.crrnt_opn_pstn_amnt_net_ccy,
                 p_close_comment.evaltn_fxrevalrate_insttousd,
                 p_close_comment.evaltn_fxrevalrate_pairtousd
            INTO lv_old_record
            FROM dual;

            put_history (p_old_record               => lv_old_record,
                         p_effective_end_timestamp  => p_effective_start_timestamp,
                         p_action                   => 'I');
      END CASE;
    END;

    -- ===================================================================================
    -- put_order
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put an order
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     ORDERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                            Description
    --     ---------------------------------   ----------------------------------------------
    --      p_user                                User
    --      p_effective_start_timestamp           Effective Start Timestamp
    --      p_business_date                       Business Date
    --      p_reporting_date                      Reporting Date
    --      p_publish_time                        Publish Time
    --      p_event_time                          Event Time
    --      p_changed_character                   Changed Character
    --      p_changed_reason_code                 Changed Reason code
    --      p_changed_reason_codifier             Changed Reason Codifier
    --      p_order_id                            Order Id
    --      p_version_number                      Version Number
    --      p_is_deleted                          Is Deleted
    --      p_creation_time                       Creation Time
    --      p_creation_identity_token             Creation Identity Token
    --      p_crtn_on_bhlf_of_idntty_tkn          Creation On Behalf Of Identity Token
    --      p_update_time                         Update Time
    --      p_update_identity_token               Update Identity Tokan
    --      p_updt_on_bhlf_of_idntty_tkn          Update On Behalf Of Identity Token
    --      p_product_instrument_code             Product Instrument Code
    --      p_product_wrapper_code                Product Wrapper Code
    --      p_feed_symbol                         Feed Symbol
    --      p_prophet_symbol                      Prophet Symbol
    --      p_mm_instrument_id                    MM Instrument Id
    --      p_product_fractional_part_rat         Product Fractional Part Ratio
    --      p_is_crrncy_in_fractnal_parts         Is Currency In Fractional Parts
    --      p_product_generation                  Product Generation
    --      p_product_point_multiplier            Product Point Multiplier
    --      p_product_currency                    Product Currency
    --      p_product_financing_ratio_max         Product Financing Ration Maximum
    --      p_product_schema_code                 Product Schema Code
    --      p_trading_account_id                  Trading Account Id
    --      p_trading_account_codifier            Trading Account Codifier
    --      p_trading_accnt_primary_curncy        Trading Account Primary Currency
    --      p_trading_account_function            Trading Account Function
    --      p_channel_id                          Channel Id
    --      p_request_id                          Request Id
    --      p_session_id                          Session Id
    --      p_requested_direction                 Requested Direction
    --      p_requested_financing_ratio           Requested Financing Ratio
    --      p_requested_quantity                  Requested Quantity
    --      p_requested_valid_till                Requested Valid Till
    --      p_reqsted_trade_close_oder_id         Requested Trade Close Order Id
    --      p_customer_info                       Custom Info
    --      p_cstm_info_vrtl_prtfl_cd             Custom Info Virtual Prtfl Code
    --      p_logical_update_time                 Logical Update Time
    --      p_visit_id                            Visit Id
    --      p_state                               State
    --      p_activation_time                     Activation Time
    --      p_actual_valid_till                   Actual Valid Till
    --      p_is_mandatory                        Is Mandatory
    --      p_execution_condition                 Execution Condition
    --      p_close_time                          Close Time
    --      p_close_comment                       Close Comment
    --      p_close_reason                        Close Reason
    --      p_close_sub_reason                    Close Sub Reason
    --      p_quantity_designator                 Quantity Designator
    --      p_quantity_matchable                  Quantity  Matchable
    --      p_quantity_match                      Quantity Match
    --      p_quantity_remaining                  Quantity Remaining
    --      p_quantity_currency                   Quantity Currency
    --      p_limit_price                         Limit Price
    --      p_limit_price_condition               Limit Price Condition
    --      p_is_limit_trailing                   Limit Is Trailing
    --      p_limit_trailing_distance             Limit Trailing Distance
    --      p_limit_trailing_best_price           Limit Trailing Best Price
    --      p_limit_traling_best_price_tme        Limit Trailing Best Price Time
    --      p_guaranteed_trade_price              Guaranteed Trade Price
    --      p_gurntd_trd_prc_cndtn                Guaranteed Trade Price Condition
    --      p_related_child_order_type            Related Child Order Type
    --      p_related_parent_order_id             Related Parent Order Id
    --      p_related_initiating_order_id         Related Initiating Order Id
    --      p_protection_type                     Protection Type
    --      p_open_trade_id                       Open Trade Id
    --      p_open_trade_quantity                 Open Trade Quantity
    --      p_open_trade_price                    Open Trade Price
    --      p_open_trade_time                     Open Trade Time
    --      p_open_trade_amount                   Open Trade Amount
    --      p_open_trade_amount_currency          Open Trade Amount Currency
    --      p_open_trade_margin                   Open Trade Margin
    --      p_open_trade_margin_currency          Open Trade Margin Currency
    --      p_open_trade_mrn_fx_rate_bid          Open Trade Margin Fx Rate Bid
    --      p_open_trade_mrn_fx_rate_ask          Open Trade Margin Fx rate Ask
    --      p_opn_trd_amnt_in_ta_prm_crncy        Open Trade Amount In Trading Account Primary Currency
    --      p_opn_trd_mrgn_in_ta_prm_crncy        Open Trade Margin In Trading Account Primary Currency
    --      p_open_trd_last_modified_time         Open Trade Last Modified Time
    --      p_client_state_request_time           Client State Request Time
    --      p_client_state_quote_l1_bid           Client State Quote L1 Bid
    --      p_client_state_quote_l1_ask           Client State Quote L1 Ask
    --      p_client_state_quote_bid              Client State Quote Bid
    --      p_client_state_quote_ask              Client State Quote Ask
    --      p_open_trade_financing_ratio          Open Trade Financing Ratio
    --      p_open_trade_qntty_fx_rate_bid        Open Trade Quantity Fx Rate Bid
    --      p_open_trade_qntty_fx_rate_ask        Open Trade Quantity Fx Rate Ask
    --      p_is_late_deal                        Is Late Deal
    --      p_is_primary                          Is Primary
    --      p_controlled_order_type               Controlled Order Type
    --      p_is_trdng_enbld_fr_prdt_ignrd        Is Trading Enabled For Product Ignored
    --      p_is_trdng_pssbl_fr_ta_ignrd          Is Trading Possible For Trading Account Ignored
    --      p_is_clrng_done_unconditionlly        Is Clearing Done Unconditionaaly
    --      p_is_ta_sspn_fr_trd_rvsl_ignrd        Is Trading Account Suspended For Trade Revrsal Ignored
    --      p_use_custom_quote                    Use Custom Quote
    --      p_custom_quote_id                     Custom Quote Id
    --      p_custom_quote_bid                    Custom Quote Bid
    --      p_custom_quote_ask                    Custom Quote Ask
    --      p_custom_fx_rate_id                   Custom Fx Rate Id
    --      p_custom_fx_rate_bid                  Custom Fx Rate Bid
    --      p_custom_fx_rate_ask                  Custom Fx Rate Ask
    --      p_comment                             Comment
    --      p_trd_reversal_trd_id_to_rvrse        Trdae Reversal Trade Id To reverse
    --      p_trd_reversal_pl_reverse             Trade Reversal P1 Reverse
    --      p_is_mtchg_use_lfo_opn_trd_cpr        Is Matching Use Lfo Open Trade Cpr
    --      p_is_mtc_rslt_in_shrt_trd_igrd        Is Matching Result In Short Trade Ignored
    --      p_is_mtch_rslt_in_psn_inc_igrd        Is Matching Result In Position Inc Ignored
    --      p_is_order_sz_grtr_max_ignrd        Is Matching Trade Size Greater Max Ignored
    --      p_is_trdng_sspnd_fr_prdct_igrd        Is Trading Suspended For Prodcut Ignored
    --      p_is_fnncng_ratio_chck_ignrd          Is Finacing Ration Check Ignored
    --      p_is_product_generation_check         Is Product Generation Check
    --      p_point_multiplier                    Point Multiplier
    --      p_platform                            Platform
    --      p_order_type                          Order_type
    --      p_mm_account_id                       Market Maker Account ID
    --      p_trading_account_type                Trading Account Type(CUSTOMER/INTERNAL)
    --      p_margin_requirement                  Margin Requirement
    --      p_margin_type                         Margin Type
    --      p_parent_order_type                   Parent Order Type
    --      p_parent_is_limit_trailing            Parent Is Limit Trailing
    --      p_parent_limit_price                  Parent Limit Price
    --      p_parent_open_trade_price             Parent Open Trade Price
    --      p_parent_open_trade_time              Parent Open Trade Time
    --      p_parent_lmt_trlng_best_price         Parent Limit Trailing Best Price
    --      p_parent_limit_trlng_distance         Parent Limit Trailing Distance
    --      p_parent_requested_direction          Parent Requested Direction
    --      p_client_state_quote_id               Client State Quote Id
    --      p_triggering_quote_id                 Triggering Quote Id
    --      p_triggering_time                     Triggering Time
    --      p_triggering_execution_price          Triggering Execution Price
    --      p_triggering_level1_price             Triggering Level 1 Price
    --      p_boundary_price                      Boundary Price
    --      p_trading_scope                       Trading Scope
    --      p_allocation_order_id                 Allocation Order Id
    --      p_alloc_trading_risk_schema           Allocation Trading Risk Schema
    --      p_order_managed_info                  List of Managed Orders
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Order Deleted Before Update and After Insert
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------


    PROCEDURE put_order(p_user                                                      IN orders.created_by%TYPE,
                        p_effective_start_timestamp                                 IN orders.effective_start_timestamp%TYPE,
                        --p_business_date                                             IN orders.business_date%TYPE,
                        --p_reporting_date                                            IN orders.reporting_date%TYPE,
                        p_publish_time                                              IN orders.publish_time%TYPE,
                        p_event_time                                                IN orders.event_time%TYPE,
                        p_changed_character                                         IN orders.changed_character%TYPE,
                        p_changed_reason_code                                       IN orders.changed_reason_code%TYPE,
                        p_changed_reason_codifier                                   IN orders.changed_reason_codifier%TYPE,
                        p_order_id                                                  IN orders.order_id%TYPE,
                        p_version_number                                            IN orders.version_number%TYPE,
                        p_is_deleted                                                IN orders.is_deleted%TYPE,
                        p_creation_time                                             IN orders.creation_time%TYPE,
                        p_creation_identity_token                                   IN orders.creation_identity_token%TYPE,
                        p_crtn_on_bhlf_of_idntty_tkn                                IN orders.crtn_on_bhlf_of_idntty_tkn%TYPE,
                        p_update_time                                               IN orders.update_time%TYPE,
                        p_update_identity_token                                     IN orders.update_identity_token%TYPE,
                        p_updt_on_bhlf_of_idntty_tkn                                IN orders.updt_on_bhlf_of_idntty_tkn%TYPE,
                        p_product_instrument_code                                   IN orders.product_instrument_code%TYPE,
                        p_product_wrapper_code                                      IN orders.product_wrapper_code%TYPE,
                        p_feed_symbol                                               IN orders.product_feed_symbol%TYPE,
                        p_prophet_symbol                                            IN orders.product_prophet_symbol%TYPE,
                        p_mm_instrument_id                                          IN orders.mm_instrument_id%TYPE,
                        p_product_fractional_part_rat                               IN orders.product_fractional_part_ratio%TYPE,
                        p_is_crrncy_in_fractnal_parts                               IN orders.is_crrncy_in_fractional_parts%TYPE,
                        p_product_generation                                        IN orders.product_generation%TYPE,
                        p_product_point_multiplier                                  IN orders.product_point_multiplier%TYPE,
                        p_product_currency                                          IN orders.product_currency%TYPE,
                        --p_product_financing_ratio_max                               IN orders.product_financing_ratio_max%TYPE,
                        --p_product_schema_code                                       IN orders.product_schema_code%TYPE,
                        p_trading_account_id                                        IN orders.trading_account_id%TYPE,
                        p_trading_account_codifier                                  IN orders.trading_account_codifier%TYPE,
                        p_trading_accnt_primary_curncy                              IN orders.trading_accnt_primary_currency%TYPE,
                        p_trading_account_function                                  IN orders.trading_account_function%TYPE,
                        p_channel_id                                                IN orders.channel_id%TYPE,
                        p_request_id                                                IN orders.request_id%TYPE,
                        p_session_id                                                IN orders.session_key%TYPE,
                        p_requested_direction                                       IN orders.requested_direction%TYPE,
                        --p_requested_financing_ratio                                 IN orders.requested_financing_ratio%TYPE,
                        p_requested_quantity                                        IN orders.requested_quantity%TYPE,
                        p_requested_valid_till                                      IN orders.requested_valid_till%TYPE,
                        --p_reqsted_trade_close_oder_id                               IN orders.requested_trade_close_oder_id%TYPE,
                        p_customer_info                                             IN orders.custom_info%TYPE,
                        --p_cstm_info_vrtl_prtfl_cd                                   IN orders.cstm_info_vrtl_prtfl_cd%TYPE,
                        --p_logical_update_time                                       IN orders.logical_update_time%TYPE,
                        --p_visit_id                                                  IN orders.visit_id%TYPE,
                        p_state                                                     IN orders.state%TYPE,
                        --p_activation_time                                           IN orders.activation_time%TYPE,
                        p_actual_valid_till                                         IN orders.actual_valid_till%TYPE,
                        --p_is_mandatory                                              IN orders.is_mandatory%TYPE,
                        --p_execution_condition                                       IN orders.execution_condition%TYPE,
                        p_close_time                                                IN orders.close_time%TYPE,
                        --p_close_comment                                             IN orders.close_comment%TYPE,
                        p_close_reason                                              IN orders.close_reason%TYPE,
                        --p_close_sub_reason                                          IN orders.close_sub_reason%TYPE,
                        p_quantity_designator                                       IN orders.quantity_designator%TYPE,
                        --p_quantity_matchable                                        IN orders.quantity_matchable%TYPE,
                        p_quantity_match                                            IN orders.quantity_match%TYPE,
                        --p_quantity_remaining                                        IN orders.quantity_remaining%TYPE,
                        --p_quantity_currency                                         IN orders.quantity_currency%TYPE,
                        p_limit_price                                               IN orders.limit_price%TYPE,
                        p_limit_price_condition                                     IN orders.limit_price_condition%TYPE,
                        p_is_limit_trailing                                         IN orders.is_limit_trailing%TYPE,
                        p_limit_trailing_distance                                   IN orders.limit_trailing_distance%TYPE,
                        p_limit_trailing_best_price                                 IN orders.limit_trailing_best_price%TYPE,
                        p_limit_traling_best_price_tme                              IN orders.limit_trailing_best_price_time%TYPE,
                        --p_guaranteed_trade_price                                    IN orders.guaranteed_trade_price%TYPE,
                        --p_gurntd_trd_prc_cndtn                                      IN orders.gurntd_trd_prc_cndtn%TYPE,
                        p_related_child_order_type                                  IN orders.related_child_order_type%TYPE,
                        p_related_parent_order_id                                   IN orders.related_parent_order_id%TYPE,
                        --p_related_initiating_order_id                               IN orders.related_initiating_order_id%TYPE,
                        --p_protection_type                                           IN orders.protection_type%TYPE,
                        --p_open_trade_id                                             IN orders.open_trade_id%TYPE,
                        --p_open_trade_quantity                                       IN orders.open_trade_quantity%TYPE,
                        --p_open_trade_price                                          IN orders.open_trade_price%TYPE,
                        --p_open_trade_time                                           IN orders.open_trade_time%TYPE,
                        --p_open_trade_amount                                         IN orders.open_trade_amount%TYPE,
                        --p_open_trade_amount_currency                                IN orders.open_trade_amount_currency%TYPE,
                        --p_open_trade_margin                                         IN orders.open_trade_margin%TYPE,
                        --p_open_trade_margin_currency                                IN orders.open_trade_margin_currency%TYPE,
                        --p_open_trade_mrn_fx_rate_bid                                IN orders.open_trade_margin_fx_rate_bid%TYPE,
                        --p_open_trade_mrn_fx_rate_ask                                IN orders.open_trade_margin_fx_rate_ask%TYPE,
                        --p_opn_trd_amnt_in_ta_prm_crncy                              IN orders.opn_trd_amnt_in_ta_prm_crncy%TYPE,
                        --p_opn_trd_mrgn_in_ta_prm_crncy                              IN orders.opn_trd_mrgn_in_ta_prm_crncy%TYPE,
                        --p_open_trd_last_modified_time                               IN orders.open_trade_last_modified_time%TYPE,
                        p_client_state_request_time                                 IN orders.client_state_request_time%TYPE,
                        p_client_state_quote_l1_bid                                 IN orders.client_state_quote_l1_bid%TYPE,
                        p_client_state_quote_l1_ask                                 IN orders.client_state_quote_l1_ask%TYPE,
                        p_client_state_quote_bid                                    IN orders.client_state_quote_bid%TYPE,
                        p_client_state_quote_ask                                    IN orders.client_state_quote_ask%TYPE,
                        --p_open_trade_financing_ratio                                IN orders.open_trade_financing_ratio%TYPE,
                        --p_open_trade_qntty_fx_rate_bid                              IN orders.open_trade_qntty_fx_rate_bid%TYPE,
                        --p_open_trade_qntty_fx_rate_ask                              IN orders.open_trade_qntty_fx_rate_ask%TYPE,
                        p_is_late_deal                                              IN orders.is_late_deal%TYPE,
                        p_is_primary                                                IN orders.is_primary%TYPE,
                        p_controlled_order_type                                     IN orders.controlled_order_type%TYPE,
                        p_is_trdng_enbld_fr_prdt_ignrd                              IN orders.is_trdng_enbld_fr_prdct_ignrd%TYPE,
                        p_is_trdng_pssbl_fr_ta_ignrd                                IN orders.is_trdng_pssbl_fr_ta_ignrd%TYPE,
                        --p_is_clrng_done_unconditionlly                              IN orders.is_clrng_done_unconditionally%TYPE,
                        p_is_ta_sspn_fr_trd_rvsl_ignrd                              IN orders.is_ta_sspndd_fr_trd_rvsl_ignrd%TYPE,
                        p_use_custom_quote                                          IN orders.use_custom_quote%TYPE,
                        p_custom_quote_id                                           IN orders.custom_quote_id%TYPE,
                        p_custom_quote_bid                                          IN orders.custom_quote_bid%TYPE,
                        p_custom_quote_ask                                          IN orders.custom_quote_ask%TYPE,
                        p_custom_fx_rate_id                                         IN orders.custom_fx_rate_id%TYPE,
                        --p_custom_fx_rate_bid                                        IN orders.custom_fx_rate_bid%TYPE,
                        --p_custom_fx_rate_ask                                        IN orders.custom_fx_rate_ask%TYPE,
                        p_comment                                                   IN orders.cntrld_ordr_comment%TYPE,
                        p_trd_reversal_trd_id_to_rvrse                              IN orders.trd_reversal_trd_id_to_reverse%TYPE,
                        p_trd_reversal_pl_reverse                                   IN orders.trd_reversal_pl_reverse%TYPE,
                        p_is_mtchg_use_lfo_opn_trd_cpr                              IN orders.is_mtchng_use_lfo_opn_trd_cmpr%TYPE,
                        p_is_mtc_rslt_in_shrt_trd_igrd                              IN orders.is_mtch_rslt_in_shrt_trd_ignrd%TYPE,
                        p_is_mtch_rslt_in_psn_inc_igrd                              IN orders.is_mtch_rslt_in_pstn_inc_ignrd%TYPE,
                        p_is_order_sz_grtr_max_ignrd                              IN orders.is_order_sz_grtr_max_ignrd%TYPE,
                        p_is_trdng_sspnd_fr_prdct_igrd                              IN orders.is_trdng_sspndd_fr_prdct_ignrd%TYPE,
                        --p_is_fnncng_ratio_chck_ignrd                                IN orders.is_fnncng_ratio_chck_ignrd%TYPE,
                        p_is_product_generation_check                               IN orders.is_product_generation_check%TYPE,
                        p_point_multiplier                                          IN orders.point_multiplier%TYPE,
                        p_platform                                                  IN orders.platform%TYPE,
                        p_order_type                                                IN orders.order_type%TYPE,
                        p_mm_account_id                                             IN orders.mm_account_id%TYPE,
                        p_trading_account_type                                      IN orders.trading_account_type%TYPE,
                        --p_margin_requirement                                        IN orders.open_trade_margin_requirement%TYPE,
                        --p_margin_type                                               IN orders.open_trade_margin_type%TYPE,
                        p_parent_order_type                                         IN orders.order_type%TYPE,
                        p_parent_is_limit_trailing                                  IN orders.is_limit_trailing%TYPE,
                        p_parent_limit_price                                        IN orders.limit_price%TYPE,
                        p_parent_open_trade_price                                   IN orders.open_trade_price%TYPE,
                        p_parent_open_trade_time                                    IN orders.open_trade_time%TYPE,
                        p_parent_lmt_trlng_best_price                               IN orders.limit_trailing_best_price%TYPE,
                        p_parent_limit_trlng_distance                               IN orders.limit_trailing_distance%TYPE,
                        p_parent_requested_direction                                IN orders.requested_direction%TYPE,
                        p_client_state_quote_id                                     IN orders.client_state_quote_id%TYPE,
                        p_triggering_quote_id                                       IN orders.triggering_quote_id%TYPE,
                        p_triggering_time                                           IN orders.triggering_time%TYPE,
                        p_triggering_execution_price                                IN orders.triggering_execution_price%TYPE,
                        p_triggering_level1_price                                   IN orders.triggering_level1_price%TYPE,
                        p_boundary_price                                            IN orders.boundary_price%TYPE,
                        p_rqustd_open_trade_to_close                                IN trade_id_tab,
                        p_triggering_side                                           IN orders.triggering_side%TYPE,
                        p_is_funds_check_ignored                                    IN orders.is_funds_check_ignored%TYPE,
                        p_custom_amount_fx_rate                                     IN orders.custom_amount_fx_rate%TYPE,
                        p_client_state_parameter_info                               IN orders.client_state_parameter_info%TYPE,
                        p_amnt_to_chrg_in_comm_ccy                                  IN orders.amnt_to_chrg_in_comm_ccy%TYPE,
                        p_reinstated_order_id                                       IN orders.reinstated_order_id%TYPE,
                        p_is_position_auto_rolled                                   IN orders.is_position_auto_rolled%TYPE,
                        p_rolled_order_id                                           IN orders.rolled_order_id%TYPE,
                        P_CLIENT_AMOUNT                                             IN ORDERS.CLIENT_AMOUNT%TYPE,
                        P_CHILD_ORDER_IDS                                           IN ORDER_ID_TAB,
                        p_parent_order_state                                        IN orders.parent_order_state%TYPE,
                        p_commission_currency                                       IN orders.Commission_Currency%TYPE,
                        p_triggering_price_offset_idx                               IN orders.Triggering_Price_Offset_Index%TYPE,
                        p_queued_quote_id                                           IN orders.Queued_Quote_Id%TYPE,
                        p_activation_quantity                                       IN orders.activation_quantity%TYPE,
                        p_gslo_premium_per_unit                                     IN orders.gslo_premium_per_unit%TYPE,
                        p_gslo_premium_fxreval_rate                                 IN orders.gslo_premium_fxreval_rate%TYPE,
                        p_gslo_premium_refund_percentg                              IN orders.gslo_premium_refund_percentage%TYPE,
                        p_execution_type                                            IN orders.Execution_Type%TYPE,
                        p_assigned_to                                               IN orders.Assigned_To%TYPE,
                        p_close_sub_reason                                          IN orders.close_sub_reason%TYPE,
                        p_trading_scope                                             IN orders.trading_scope%TYPE,
                        p_allocation_order_id                                       IN orders.allocation_order_id%TYPE,
                        --p_alloc_trading_risk_schema                                 IN orders.alloc_trading_risk_schema%TYPE,
                        p_order_managed_info                                        IN orders_managed_info_tab,
                        p_binary_type                                               IN orders.binary_type%TYPE,
                        p_settle_time                                               IN orders.settle_time%TYPE,
                        p_tenor                                                     IN orders.tenor%TYPE,
                        p_strike_price                                              IN orders.strike_price%TYPE,
                        p_strike_price_additional                                   IN orders.strike_price_additional%TYPE,
                        p_alloc_instrument_schema                                   IN orders.alloc_instrument_schema%TYPE,
                        p_guaranteed_limit_price                                    IN orders.guaranteed_limit_price%TYPE,
                        p_was_guaranteed_executed                                   IN orders.was_guaranteed_executed%TYPE,
                        p_source_order_id                                           IN orders.source_order_id%TYPE,
                        p_source_trade_id                                           IN orders.source_trade_id%TYPE,
                        p_client_order_id                                           IN orders.client_order_id%TYPE,
                        p_limit_distance                                            IN orders.limit_distance%TYPE,
                        p_guaranteed_limit_distance                                 IN orders.guaranteed_limit_distance%TYPE,
                        p_metatrader_order_type                                     IN orders.metatrader_order_type%TYPE,
                        p_client_limit_price                                        IN orders.client_limit_price%TYPE,
                        p_order_chngd_chrg_cmsn                                     IN orders.order_chngd_chrg_cmsn%TYPE,
                        p_order_chngd_prev_state                                    IN orders.order_chngd_prev_state%TYPE,
                        p_order_close_comment                                       IN order_close_comment_obj,
                        p_migrated_order_id                                         IN orders.migrated_order_id%TYPE,
                        p_client_party_id                                           IN orders.client_party_id%TYPE,
                        p_liquidation_id                                            IN orders.liquidation_id%TYPE,
                        p_is_give_up                                                IN orders.is_give_up%TYPE,
                        p_exclude_from_turnover_report                              IN orders.exclude_from_turnover_report%TYPE)

    IS
      lv_effective_start_time         orders.effective_start_timestamp%TYPE := NULL;
      lv_logical_load_timestamp       orders.logical_load_timestamp%TYPE;

      lv_order_type                   orders.order_type%TYPE;
      lv_order_sub_type               orders.controlled_order_type%TYPE;
      lv_normalised_quantity          orders.normalised_quantity%TYPE;
      lv_normalised_quantity_ccy      orders.normalised_quantity_currency%TYPE;
      lv_normalised_trade_price       orders.normalised_trade_price%TYPE;
      lv_requested_quantity           orders.requested_quantity%TYPE;
      --lv_open_trade_quantity          orders.open_trade_quantity%TYPE;
      lv_trading_account_type         orders.trading_account_type%TYPE;
      lv_internal_account_id          orders.trading_account_id%TYPE := NULL;
      lv_fractional_part_ratio        orders.product_fractional_part_ratio%TYPE := NULL;
      lv_product_currency             orders.product_currency%TYPE;
      lv_old_order                    orders%ROWTYPE;
      lv_open_trade_time              orders.open_trade_time%TYPE;
      lv_product_point_multiplier     orders.product_point_multiplier%TYPE := NULL;
      lv_nrmlsd_opn_value             latest_open_trades.nrmlsd_opn_value_in_trdng_ccy%TYPE := NULL;
      lv_cfd_multiplier               mm_products.cfd_multiplier%TYPE;
      lv_open_trade_price             orders.open_trade_price%TYPE;

      lv_normalised_trading_ccy       latest_open_trades.normalised_trading_currency%TYPE;

      ltab_identity_id                identity_id_tab;

      lex_unknown_operation_type       EXCEPTION;
      lex_order_not_found              EXCEPTION;

      lv_business_date                 DATE;
      lv_reporting_date                DATE;

      lv_order_id_short_form           orders.Order_Id_Short_Form%TYPE;
      lv_parent_order_id_short_form    orders.Parent_Order_Id_Short_Form%TYPE;

    BEGIN
      logger.logger.set_module('put_order');

      -- set the logical load timestamp to now

      lv_logical_load_timestamp := SYSTIMESTAMP;

      --lv_fractional_part_ratio := nvl(p_product_fractional_part_rat, 1);
      --
      --Calculate the transformed values for MM and NG
      --

      --
      --Requested Quantity
      --
      --The Requested quantity should always be +ve for NG and MM
      --If the direction needs to be added then multiply the quantity to
      --direction multiplier
      --

      lv_requested_quantity := abs(p_requested_quantity);

      --
      --Open Trade Quantity
      --
      --The traded quantity should always be +ve for NG and MM
      --If the direction needs to be added then multiply the quantity to
      --direction multiplier
      --

      --lv_open_trade_quantity := abs(p_open_trade_quantity);

      --
      --Get Business Date
      --

      lv_business_date := nrg_common.get_business_date(p_creation_time);

      --
      --Get Reporting Date
      --

      lv_reporting_date := nrg_common.get_reporting_date(p_creation_time);


      CASE
        WHEN p_platform = 'MMCFD' OR p_platform = 'MMSB' THEN

          --
          -- Product Point Multiplier
          --

          lv_product_point_multiplier := p_product_point_multiplier;
          --
          --Normalised open value
          --

          /*CASE
            WHEN p_platform = 'MMSB' THEN
              lv_nrmlsd_opn_value := abs(p_open_trade_quantity * p_open_trade_price * nvl(lv_product_point_multiplier, 1));
            WHEN p_platform = 'MMCFD' AND p_is_primary = 'NO' THEN
              IF p_open_trade_price = 0 THEN
                lv_open_trade_price := 1;
              END IF;
              lv_nrmlsd_opn_value := abs(p_open_trade_quantity / lv_open_trade_price);
            WHEN p_platform = 'MMCFD' AND p_is_primary = 'YES' THEN
              BEGIN
                SELECT nvl(cfd_multiplier,1)
                INTO   lv_cfd_multiplier
                FROM mm_products
                WHERE mm_instrument_id = mm_instrument_id AND
                      platform = p_platform AND
                      ROWNUM = 1;
              EXCEPTION
                WHEN NO_DATA_FOUND THEN
                  lv_cfd_multiplier := 1;
              END;

              lv_nrmlsd_opn_value := abs(p_open_trade_quantity * p_open_trade_price * nvl(lv_product_point_multiplier, 1) * lv_cfd_multiplier);

            ELSE
              lv_nrmlsd_opn_value := NULL;
          END CASE;*/

          --
          --Product Currency
          --

          IF p_product_currency IS NOT NULL AND LENGTH(p_product_currency) > 3 THEN
            --
            --This scenario occurs for the TIQ instruments. For these instruments the product currency
            --does not comes in as ISO3 code. In order to identify the primary currency for such instruments
            --we need to lookup on the reference table and identify the actual product currency
            --
            BEGIN
              SELECT DECODE(UPPER(p_is_primary), 'YES', primary_currency, secondary_currency)
              INTO lv_product_currency
              FROM bi_ref.mm_instrument_currencies
              WHERE mm_instrument_id = p_mm_instrument_id;
            EXCEPTION
              WHEN TOO_MANY_ROWS THEN
                --
                --Since the product currency is more than 3 characters and we can not find the mapping for
                --it on the reference table we will have to leave it as it is on the source so that we can
                --identify it and then later it can be fixed.
                --
                lv_product_currency := p_product_currency;
              WHEN NO_DATA_FOUND THEN
                --
                --Since the product currency is more than 3 characters and we can not find the mapping for
                --it on the reference table we will have to leave it as it is on the source so that we can
                --identify it and then later it can be fixed
                --
                lv_product_currency := p_product_currency;
            END;
          ELSE
            lv_product_currency := p_product_currency;
          END IF;

          --
          --Order Type
          --
          --lv_order_type := p_order_type;
          /*CASE
            WHEN upper(p_order_type) = 'MARKET' THEN
                CASE
                  WHEN upper(p_controlled_order_type) = 'ROLLOVER'  THEN
                      lv_order_type := 'ROLLOVER';
                  ELSE
                      lv_order_type := 'MARKET';
                END CASE;
            WHEN upper(p_order_type) = 'LIMIT' THEN
              lv_order_type := 'LIMIT';
            WHEN upper(p_order_type) = 'STOP' THEN
              lv_order_type := 'STOP';
            WHEN upper(p_order_type) = 'CASH' THEN
              lv_order_type := 'CASH';
            ELSE
              lv_order_type := 'UNKNOWN';
          END CASE;*/

          ---BER-934 begin
      lv_order_type := nrg_common.get_order_type(p_platform                       => p_platform,
                                                 p_controlled_order_type          => p_controlled_order_type,
                                                 p_order_type                     => p_order_type);


          ---BER-934 end

          --
          --Sub Order Type/Controlled Order Type
          --
          CASE
            WHEN upper(p_controlled_order_type) = 'LIQUIDATION' THEN
              lv_order_sub_type := 'POSITIONCLOSEOUT';
            WHEN upper(p_controlled_order_type) = 'TRADECANCEL' THEN
              lv_order_sub_type := 'TRADEREVERSAL';
            ELSE
              lv_order_sub_type := 'OTHER';
          END CASE;

          --
          --Product fractional part ratio
          --
          CASE
            WHEN p_platform = 'MMSB' THEN
              lv_fractional_part_ratio := NULL;
            WHEN p_platform = 'MMCFD' THEN
              BEGIN
                SELECT 0.01
                INTO lv_fractional_part_ratio
                FROM instruments, countries
                WHERE countries.country_code = instruments.country_code AND
                      countries.iso3 = 'GBR' AND
                      instrument_code = p_product_instrument_code AND
                      instrument_type = 'Shares';
              EXCEPTION
                WHEN NO_DATA_FOUND THEN
                  lv_fractional_part_ratio := NULL;
              END;
            ELSE
              lv_fractional_part_ratio := NULL;
          END CASE;

          lv_normalised_quantity := abs(round(p_requested_quantity, 0)/(1/nvl(nullif(lv_product_point_multiplier,0), 1)) * nvl(nullif(lv_fractional_part_ratio,0),1));

        WHEN p_platform = 'NG' THEN
          --
          -- Product Point Multiplier
          --

          CASE
            WHEN p_quantity_designator = 'UNITS'  THEN
              lv_product_point_multiplier := NULL;
            WHEN p_quantity_designator = 'AMOUNT' THEN
              lv_product_point_multiplier := NULL;
            WHEN P_QUANTITY_DESIGNATOR = 'AMOUNTPERPOINT'  THEN --2
              lv_product_point_multiplier := p_product_point_multiplier;
            WHEN p_quantity_designator = 'KOUNITS' THEN
              lv_product_point_multiplier := p_product_point_multiplier;
            ELSE
              lv_product_point_multiplier := NULL;
          END CASE;

          --
          -- Product fractional part ratio
          --

          CASE
            WHEN p_quantity_designator = 'UNITS'  THEN
              CASE
                WHEN p_is_crrncy_in_fractnal_parts = 'YES'  THEN
                  lv_fractional_part_ratio := p_product_fractional_part_rat;
                WHEN p_is_crrncy_in_fractnal_parts = 'NO'  THEN
                  lv_fractional_part_ratio := NULL;
                ELSE
                  lv_fractional_part_ratio := NULL;
              END CASE;
            WHEN p_quantity_designator = 'AMOUNT' THEN
              lv_fractional_part_ratio := NULL;
            WHEN p_quantity_designator = 'AMOUNTPERPOINT'  THEN --2
              lv_fractional_part_ratio := NULL;
            WHEN p_quantity_designator = 'KOUNITS' THEN
              lv_fractional_part_ratio := NULL;
            ELSE
              lv_fractional_part_ratio := NULL;
          end case;

          --
          --Normalised open value
          --

          /*CASE
            WHEN p_quantity_designator = 'AMOUNTPERPOINT' THEN
              lv_nrmlsd_opn_value := p_open_trade_amount;
            WHEN p_quantity_designator = 'UNITS' THEN
              CASE
                WHEN p_is_crrncy_in_fractnal_parts = 'NO' THEN
                  lv_nrmlsd_opn_value := p_open_trade_quantity * p_open_trade_price;
              ELSE
                lv_nrmlsd_opn_value := p_open_trade_quantity * nvl(lv_fractional_part_ratio,1) * p_open_trade_price;
              END CASE;
            WHEN p_quantity_designator = 'AMOUNT' THEN
              lv_nrmlsd_opn_value := p_open_trade_amount;
            ELSE
              lv_nrmlsd_opn_value := p_open_trade_amount;
            END CASE;*/

          --
          --Product Currency
          --

          lv_product_currency := p_product_currency;

          --
          --Sub Order Type/Controlled Order Type
          --
          lv_order_sub_type := p_controlled_order_type;

          --
          --Order Type
          --

          /*CASE
            WHEN upper(p_related_child_order_type) = 'STOPLOSS' AND p_is_mandatory = 'NO' THEN
              lv_order_type := 'STOPLOSS (REGULAR)';
            WHEN upper(p_related_child_order_type) = 'STOPLOSS' AND p_is_mandatory = 'YES' THEN
              lv_order_type := 'STOPLOSS (REGULAR, MANDATORY)';
            WHEN upper(p_related_child_order_type) = 'TRAILINGSTOPLOSS' AND p_is_mandatory = 'NO' THEN
              lv_order_type := 'STOPLOSS (TRAILING)';
            WHEN upper(p_related_child_order_type) = 'TRAILINGSTOPLOSS' AND p_is_mandatory = 'YES' THEN
              lv_order_type := 'STOPLOSS (TRAILING, MANDATORY)';
            WHEN upper(p_related_child_order_type) = 'TAKEPROFIT' THEN
              lv_order_type := 'TAKEPROFIT';
            WHEN p_reqsted_trade_close_oder_id != '-1' THEN
              lv_order_type := 'TRADECLOSEORDER';
            WHEN upper(p_is_mandatory) = 'NO' AND p_limit_price_condition = 'NONE' THEN
              lv_order_type := 'MARKET';
            WHEN upper(p_limit_price_condition) != 'NONE' AND p_related_parent_order_id = '-1' THEN
              CASE
                WHEN upper(p_requested_direction) = 'BUY' THEN
                  CASE
                    WHEN upper(p_limit_price_condition) = 'GREATEROREQUAL' THEN
                      lv_order_type := 'MARKET IF TOUCHED';
                    ELSE
                      lv_order_type := 'LIMIT';
                  END CASE;
                ELSE
                  CASE
                    WHEN upper(p_limit_price_condition) = 'LESSOREQUAL' THEN
                      lv_order_type := 'MARKET IF TOUCHED';
                    ELSE
                      lv_order_type := 'LIMIT';
                  END CASE;
              END CASE;
          ELSE
            lv_order_type := NULL;
          END CASE;*/

        --
        --Normalised Quantity
        --

        CASE
            WHEN p_product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
              lv_normalised_quantity := NULL;
            WHEN UPPER(P_QUANTITY_DESIGNATOR) = 'AMOUNTPERPOINT' THEN
              lv_normalised_quantity := p_requested_quantity * nvl(lv_product_point_multiplier,1);
            WHEN UPPER(P_QUANTITY_DESIGNATOR) = 'UNITS' AND UPPER(P_IS_CRRNCY_IN_FRACTNAL_PARTS) = 'YES' THEN
              lv_normalised_quantity := p_requested_quantity * nvl(lv_fractional_part_ratio,1);
            WHEN upper(p_quantity_designator) = 'UNITS' AND upper(p_is_crrncy_in_fractnal_parts) = 'NO' THEN
              lv_normalised_quantity := p_requested_quantity;
            WHEN upper(p_quantity_designator) = 'AMOUNT' THEN
              CASE
                WHEN p_requested_direction = 'BUY' THEN
                  lv_normalised_quantity := p_requested_quantity/nullif(p_client_state_quote_ask,0);
                ELSE
                  lv_normalised_quantity := p_requested_quantity/nullif(p_client_state_quote_bid,0);
              END CASE;
            WHEN p_quantity_designator = 'KOUNITS' THEN
              lv_normalised_quantity := p_requested_quantity * nvl(lv_product_point_multiplier,1);
            ELSE
              lv_normalised_quantity := NULL;
        END CASE;

      END CASE;

      --
      --Normalised Quantity Currency
      --
      CASE
      WHEN p_product_wrapper_code = 'A-EOVH' THEN
        lv_normalised_quantity_ccy := p_trading_accnt_primary_curncy;
      WHEN p_product_wrapper_code = 'X-A' THEN
        lv_normalised_quantity_ccy := (CASE WHEN LENGTH(lv_product_currency) > 3 THEN NULL ELSE lv_product_currency END);
      WHEN p_product_wrapper_code = 'X-QOQH' THEN
        lv_normalised_quantity_ccy := p_trading_accnt_primary_curncy;
      ELSE
        lv_normalised_quantity_ccy := NULL;
      END CASE;

    --
    -- Normalised Trade Price
    --
    /*IF p_is_primary = 'NO' AND NVL(p_open_trade_price,0) != 0 THEN
         lv_normalised_trade_price := 1/p_open_trade_price;
    ELSE
     lv_normalised_trade_price := p_open_trade_price;
    END IF;*/

    --
    -- Normalised Trading Currency
    --

    /*CASE
      WHEN p_platform = 'MMSB' THEN
        lv_normalised_trading_ccy := p_open_trade_amount_currency;
      WHEN p_platform = 'MMCFD' THEN
        IF p_is_primary = 'YES' THEN
          lv_normalised_trading_ccy := p_open_trade_amount_currency;
        ELSE
          lv_normalised_trading_ccy := p_quantity_currency;
        END IF;
      ELSE
        lv_normalised_trading_ccy := NULL;
    END CASE;*/

      --
      --Order Type
      --

      lv_order_type := nrg_common.get_order_type(p_platform                       => p_platform,
                                                 p_controlled_order_type          => p_controlled_order_type,
                                                 p_order_type                     => p_order_type);

      --
      -- This is to ensure that the trading account record exists for referential integrity
      --
      IF p_trading_account_id IS NOT NULL THEN
        lv_trading_account_type := p_trading_account_type;
        nrg_trading_account.create_trading_account_stub (p_user                       => p_user,
                                                         p_logical_load_timestamp     => lv_logical_load_timestamp,
                                                         p_effective_start_timestamp  => p_effective_start_timestamp,
                                                         p_trading_account_id         => p_trading_account_id,
                                                         p_trading_account_type       => lv_trading_account_type);
      ELSE
        BEGIN
          SELECT trading_account_id, trading_account_type
          INTO   lv_internal_account_id, lv_trading_account_type
          FROM   trading_accounts
          WHERE  source_account_id = to_char(p_mm_account_id) AND
                 trading_account_type <> 'CUSTOMER';

        EXCEPTION
          WHEN TOO_MANY_ROWS THEN
            --
            --If we get more than 1 row then leave the trading account id as null
            --
            lv_internal_account_id := NULL;
            lv_trading_account_type := NULL;
          WHEN NO_DATA_FOUND THEN
            --
            --If the internal account id does not exist then leave the trading account id null
            --
            lv_trading_account_type := NULL;
        END;
      END IF;

      --
      -- This is to ensure that the session record exists for the referential integrity
      --
      IF p_session_id IS NOT NULL THEN
        nrg_session.create_session_stub(p_user                       => p_user,
                                        p_logical_load_timestamp     => lv_logical_load_timestamp,
                                        p_effective_start_timestamp  => p_effective_start_timestamp,
                                        p_session_key                => p_session_id,
                                        p_channel_id                 => p_channel_id,
                                        p_identity_id                => p_creation_identity_token,
                                        p_on_behalf_of_identity_id   => p_crtn_on_bhlf_of_idntty_tkn);
      END IF;

      IF p_product_wrapper_code IS NOT NULL AND p_product_instrument_code IS NOT NULL THEN
        nrg_products.create_instrument_stub (p_user                       => p_user,
                                             p_logical_load_timestamp     => lv_logical_load_timestamp,
                                             p_effective_start_timestamp  => p_effective_start_timestamp,
                                             p_instrument_code            => p_product_instrument_code,
                                             p_product_platform           => p_platform,
                                             p_product_wrapper            => p_product_wrapper_code,
                                             p_mm_instrument_id           => p_mm_instrument_id);
      END IF;

      --
      --Insert the controlled order type
      --
      IF lv_order_sub_type IS NOT NULL THEN
        nrg_controlled_order_type.put_controlled_order_type (p_user                         => p_user,
                                                             p_effective_start_timestamp    => p_effective_start_timestamp,
                                                             p_logical_load_timestamp       => lv_logical_load_timestamp,
                                                             p_controlled_order_type        => upper(lv_order_sub_type));
      END IF;

      --
      --Insert the order type
      --
      IF lv_order_type IS NOT NULL THEN
        nrg_order_type.put_order_type (p_user                         => p_user,
                                       p_effective_start_timestamp    => p_effective_start_timestamp,
                                       p_logical_load_timestamp       => lv_logical_load_timestamp,
                                       p_order_type                   => lv_order_type);
      END IF;

      --
      --Insert the quantity designator
      --
      IF p_quantity_designator IS NOT NULL THEN
        nrg_quantity_designator.put_quantity_designator (p_user                         => p_user,
                                                         p_effective_start_timestamp    => p_effective_start_timestamp,
                                                         p_logical_load_timestamp       => lv_logical_load_timestamp,
                                                         p_quantity_designator          => p_quantity_designator);
      END IF;

      --
      --Create Order Stubs for child orders
      --

      IF p_child_order_ids IS NOT NULL THEN
        FOR lv_cnt IN 1..p_child_order_ids.COUNT LOOP
          create_order_stub(p_user                       => p_user,
                            p_logical_load_timestamp     => lv_logical_load_timestamp,
                            p_effective_start_timestamp  => p_effective_start_timestamp,
                            p_order_id                   => p_child_order_ids(lv_cnt),
                            p_platform                   => p_platform);
        END LOOP;
      END IF;

      --
      --Put Trade Stubs
      --

      IF p_rqustd_open_trade_to_close IS NOT NULL AND p_rqustd_open_trade_to_close.COUNT > 0 THEN
        FOR lv_cnt IN 1..p_rqustd_open_trade_to_close.COUNT LOOP
          create_order_stub(p_user                       => p_user,
                            p_logical_load_timestamp     => lv_logical_load_timestamp,
                            p_effective_start_timestamp  => p_effective_start_timestamp,
                            p_order_id                   => p_rqustd_open_trade_to_close(lv_cnt).trade_id,
                            p_platform                   => p_platform);
        END LOOP;
      END IF;


    --
      -- Identity Stubs for referential integrity
      --
    -- Build array of ids
    --
      select  identity_id_obj(identity_id)
      bulk collect into ltab_identity_id
      from
         (select distinct t.identity_id
          from
            (select p_creation_identity_token as identity_id
             from dual
             union
             select p_crtn_on_bhlf_of_idntty_tkn
             from dual
             union
             select p_update_identity_token
             from dual
             union
             select p_updt_on_bhlf_of_idntty_tkn
             from dual
            ) t
          where t.identity_id is not null
         );
    --
    -- Create Identity Stubs
    --
    IF ltab_identity_id.COUNT <> 0 THEN
       nrg_identity.create_identity_stub (p_user                       => p_user,
                                            p_logical_load_timestamp     => lv_logical_load_timestamp,
                                            p_effective_start_timestamp  => p_effective_start_timestamp,
                                            p_identity_id                => ltab_identity_id);
    END IF;


    --
    -- Order_id_short_form
    --
    IF p_platform = 'NG'
      THEN
         lv_order_id_short_form :=
          CASE WHEN p_trading_account_type = 'HEDGE' THEN
               NULL
          ELSE
               SUBSTR(p_order_id,1,1)||LTRIM(SUBSTR(p_order_id,2,2),'0')||'-'||LTRIM(SUBSTR(p_order_id,4,4),'0')||'-'||LTRIM(SUBSTR(p_order_id,8,13),'0')
          END;

        lv_parent_order_id_short_form :=
          CASE WHEN p_related_parent_order_id IS NOT NULL AND length(p_related_parent_order_id)>10 AND p_trading_account_type != 'HEDGE' THEN
              SUBSTR(p_related_parent_order_id,1,1)||LTRIM(SUBSTR(p_related_parent_order_id,2,2),'0')||'-'||LTRIM(SUBSTR(p_related_parent_order_id,4,4),'0')||'-'||LTRIM(SUBSTR(p_related_parent_order_id,8,13),'0')
          ELSE
            NULL
          END;

    END IF;

      --
      --Insert the new order on the table
      --

      BEGIN
        INSERT INTO orders (order_id,
                            platform,
                            logical_load_timestamp,
                            created_by,
                            create_timestamp,
                            updated_by,
                            update_timestamp,
                            effective_start_timestamp,
                            business_date,
                            reporting_date,
                            trading_account_id,
                            trading_account_type,
                            session_key,
                            product_instrument_code,
                            product_wrapper_code,
                            --product_schema_code,
                            trading_account_function,
                            trading_account_codifier,
                            changed_character,
                            changed_reason_code,
                            changed_reason_codifier,
                            version_number,
                            state,
                            creation_time,
                            update_time,
                            order_type,
                            related_parent_order_id,
                            --related_initiating_order_id,
                            --open_trade_id,
                            controlled_order_type,
                            related_child_order_type,
                            --visit_id,
                            --protection_type,
                            trading_accnt_primary_currency,
                            product_feed_symbol,
                            product_generation,
                            product_prophet_symbol,
                            product_currency,
                            product_point_multiplier,
                            --product_financing_ratio_max,
                            product_fractional_part_ratio,
                            channel_id,
                            request_id,
                            is_deleted,
                            creation_identity_token,
                            crtn_on_bhlf_of_idntty_tkn,
                            update_identity_token,
                            updt_on_bhlf_of_idntty_tkn,
                            requested_direction,
                            requested_quantity,
                            --requested_financing_ratio,
                            requested_valid_till,
                            --requested_trade_close_oder_id,
                            quantity_designator,
                            --quantity_matchable,
                            quantity_match,
                            --quantity_remaining,
                            --quantity_currency,
                            --activation_time,
                            actual_valid_till,
                            --is_mandatory,
                            --execution_condition,
                            limit_price,
                            limit_price_condition,
                            is_limit_trailing,
                            limit_trailing_distance,
                            limit_trailing_best_price,
                            limit_trailing_best_price_time,
                            --guaranteed_trade_price,
                            --gurntd_trd_prc_cndtn,
                            --open_trade_quantity,
                            --open_trade_price,
                            --open_trade_time,
                            --open_trade_amount,
                            --open_trade_amount_currency,
                            --open_trade_margin,
                            --open_trade_margin_currency,
                            --open_trade_margin_fx_rate_bid,
                            --open_trade_margin_fx_rate_ask,
                            --opn_trd_amnt_in_ta_prm_crncy,
                            --opn_trd_mrgn_in_ta_prm_crncy,
                            --open_trade_last_modified_time,
                            client_state_request_time,
                            client_state_quote_l1_bid,
                            client_state_quote_l1_ask,
                            client_state_quote_bid,
                            client_state_quote_ask,
                            --open_trade_financing_ratio,
                            --open_trade_qntty_fx_rate_bid,
                            --open_trade_qntty_fx_rate_ask,
                            close_time,
                            --close_comment,
                            close_reason,
                            close_sub_reason,
                            --custom_fx_rate_ask,
                            --custom_fx_rate_bid,
                            custom_fx_rate_id,
                            --cstm_info_vrtl_prtfl_cd,
                            custom_info,
                            custom_quote_ask,
                            custom_quote_bid,
                            custom_quote_id,
                            event_time,
                            is_late_deal,
                            is_primary,
                            cntrld_ordr_comment,
                            --is_clrng_done_unconditionally,
                            is_crrncy_in_fractional_parts,
                            --is_fnncng_ratio_chck_ignrd,
                            is_mtch_rslt_in_pstn_inc_ignrd,
                            is_mtch_rslt_in_shrt_trd_ignrd,
                            is_order_sz_grtr_max_ignrd,
                            is_product_generation_check,
                            is_ta_sspndd_fr_trd_rvsl_ignrd,
                            is_trdng_enbld_fr_prdct_ignrd,
                            is_trdng_pssbl_fr_ta_ignrd,
                            is_trdng_sspndd_fr_prdct_ignrd,
                            is_mtchng_use_lfo_opn_trd_cmpr,
                            mm_instrument_id,
                            point_multiplier,
                            publish_time,
                            trd_reversal_pl_reverse,
                            trd_reversal_trd_id_to_reverse,
                            use_custom_quote,
                            mm_account_id,
                            --logical_update_time,
                            normalised_quantity,
                            --open_trade_margin_requirement,
                            --open_trade_margin_type,
                            normalised_quantity_currency,
                            normalised_trade_price,
                            client_state_quote_id,
                            triggering_quote_id,
                            triggering_time,
                            triggering_execution_price,
                            triggering_level1_price,
                            boundary_price,
                            triggering_side,
                            is_funds_check_ignored,
                            custom_amount_fx_rate,
                            client_state_parameter_info,
                            amnt_to_chrg_in_comm_ccy,
                            reinstated_order_id,
                            is_position_auto_rolled,
                            ROLLED_ORDER_ID,
                            client_amount,
                            commission_currency,
                            triggering_price_offset_index,
                            Queued_Quote_Id,
                            activation_quantity,
                            gslo_premium_per_unit,
                            gslo_premium_fxreval_rate,
                            gslo_premium_refund_percentage,
                            order_id_short_form,
                            parent_order_id_short_form,
                            execution_type,
                            assigned_to,
                            Record_Source,
                            trading_scope,
                            allocation_order_id,
                            alloc_trading_risk_schema,
                            binary_type,
                            settle_time,
                            tenor,
                            strike_price,
                            strike_price_additional,
                            alloc_instrument_schema,
                            guaranteed_limit_price,
                            was_guaranteed_executed,
                            source_order_id,
                            source_trade_id,
                            client_order_id,
                            limit_distance,
                            guaranteed_limit_distance,
                            metatrader_order_type,
                            client_limit_price ,
                            order_chngd_chrg_cmsn,
                            order_chngd_prev_state,
                            migrated_order_id,
                            client_party_id,
                            liquidation_id,
                            is_give_up,
                            exclude_from_turnover_report

                           )
                   VALUES (
                            p_order_id,
                            p_platform,
                            lv_logical_load_timestamp,
                            p_user,
                            SYSTIMESTAMP,
                            p_user,
                            SYSTIMESTAMP,
                            p_effective_start_timestamp,
                            lv_business_date,
                            lv_reporting_date,
                            NVL(p_trading_account_id, lv_internal_account_id),
                            lv_trading_account_type,
                            p_session_id,
                            p_product_instrument_code,
                            p_product_wrapper_code,
                            --p_product_schema_code,
                            p_trading_account_function,
                            p_trading_account_codifier,
                            p_changed_character,
                            p_changed_reason_code,
                            p_changed_reason_codifier,
                            p_version_number,
                            p_state,
                            p_creation_time,
                            p_update_time,
                            lv_order_type,
                            p_related_parent_order_id,
                            --p_related_initiating_order_id,
                            --p_open_trade_id,
                            upper(lv_order_sub_type),
                            p_related_child_order_type,
                            --p_visit_id,
                            --p_protection_type,
                            p_trading_accnt_primary_curncy,
                            p_feed_symbol,
                            p_product_generation,
                            p_prophet_symbol,
                            lv_product_currency,
                            p_product_point_multiplier,
                            --p_product_financing_ratio_max,
                            p_product_fractional_part_rat,
                            p_channel_id,
                            p_request_id,
                            p_is_deleted,
                            p_creation_identity_token,
                            p_crtn_on_bhlf_of_idntty_tkn,
                            p_update_identity_token,
                            p_updt_on_bhlf_of_idntty_tkn,
                            p_requested_direction,
                            lv_requested_quantity,
                            --p_requested_financing_ratio,
                            p_requested_valid_till,
                            --p_reqsted_trade_close_oder_id,
                            p_quantity_designator,
                            --p_quantity_matchable,
                            p_quantity_match,
                            --p_quantity_remaining,
                            --p_quantity_currency,
                            --p_activation_time,
                            p_actual_valid_till,
                            --p_is_mandatory,
                            --p_execution_condition,
                            p_limit_price,
                            p_limit_price_condition,
                            p_is_limit_trailing,
                            p_limit_trailing_distance,
                            p_limit_trailing_best_price,
                            p_limit_traling_best_price_tme,
                            --p_guaranteed_trade_price,
                            --p_gurntd_trd_prc_cndtn,
                            --lv_open_trade_quantity,
                            --p_open_trade_price,
                            --p_open_trade_time,
                            --p_open_trade_amount,
                            --p_open_trade_amount_currency,
                            --p_open_trade_margin,
                            --p_open_trade_margin_currency,
                            --p_open_trade_mrn_fx_rate_bid,
                            --p_open_trade_mrn_fx_rate_ask,
                            --p_opn_trd_amnt_in_ta_prm_crncy,
                            --p_opn_trd_mrgn_in_ta_prm_crncy,
                            --p_open_trd_last_modified_time,
                            p_client_state_request_time,
                            p_client_state_quote_l1_bid,
                            p_client_state_quote_l1_ask,
                            p_client_state_quote_bid,
                            p_client_state_quote_ask,
                            --p_open_trade_financing_ratio,
                            --p_open_trade_qntty_fx_rate_bid,
                            --p_open_trade_qntty_fx_rate_ask,
                            p_close_time,
                            --p_close_comment,
                            p_close_reason,
                            p_close_sub_reason,
                            --p_custom_fx_rate_ask,
                            --p_custom_fx_rate_bid,
                            p_custom_fx_rate_id,
                            --p_cstm_info_vrtl_prtfl_cd,
                            p_customer_info,
                            p_custom_quote_ask,
                            p_custom_quote_bid,
                            p_custom_quote_id,
                            p_event_time,
                            p_is_late_deal,
                            p_is_primary,
                            p_comment,
                            --p_is_clrng_done_unconditionlly,
                            p_is_crrncy_in_fractnal_parts,
                            --p_is_fnncng_ratio_chck_ignrd,
                            p_is_mtch_rslt_in_psn_inc_igrd,
                            p_is_mtc_rslt_in_shrt_trd_igrd,
                            p_is_order_sz_grtr_max_ignrd,
                            p_is_product_generation_check,
                            p_is_ta_sspn_fr_trd_rvsl_ignrd,
                            p_is_trdng_enbld_fr_prdt_ignrd,
                            p_is_trdng_pssbl_fr_ta_ignrd,
                            p_is_trdng_sspnd_fr_prdct_igrd,
                            p_is_mtchg_use_lfo_opn_trd_cpr,
                            p_mm_instrument_id,
                            p_point_multiplier,
                            p_publish_time,
                            p_trd_reversal_pl_reverse,
                            p_trd_reversal_trd_id_to_rvrse,
                            p_use_custom_quote,
                            p_mm_account_id,
                            --p_logical_update_time,
                            lv_normalised_quantity,
                            --p_margin_requirement,
                            --p_margin_type,
                            lv_normalised_quantity_ccy,
                            lv_normalised_trade_price,
                            p_client_state_quote_id,
                            p_triggering_quote_id,
                            p_triggering_time,
                            p_triggering_execution_price,
                            p_triggering_level1_price,
                            p_boundary_price,
                            p_triggering_side,
                            p_is_funds_check_ignored,
                            p_custom_amount_fx_rate,
                            p_client_state_parameter_info,
                            p_amnt_to_chrg_in_comm_ccy,
                            p_reinstated_order_id,
                            p_is_position_auto_rolled,
                            p_rolled_order_id,
                            P_CLIENT_AMOUNT,
                           -- p_parent_order_state
                            p_commission_currency,
                            p_triggering_price_offset_idx,
                            p_queued_quote_id,
                            p_activation_quantity,
                            p_gslo_premium_per_unit,
                            p_gslo_premium_fxreval_rate,
                            p_gslo_premium_refund_percentg,
                            lv_order_id_short_form,
                            lv_parent_order_id_short_form,
                            p_execution_type,
                            p_assigned_to,
                            p_platform,
                            p_trading_scope,
                            p_allocation_order_id,
                            NULL,--p_alloc_trading_risk_schema,
                            p_binary_type,
                            p_settle_time,
                            p_tenor,
                            p_strike_price,
                            p_strike_price_additional,
                            p_alloc_instrument_schema,
                            p_guaranteed_limit_price,
                            p_was_guaranteed_executed,
                            p_source_order_id,
                            p_source_trade_id,
                            p_client_order_id,
                            p_limit_distance,
                            p_guaranteed_limit_distance,
                            p_metatrader_order_type,
                            p_client_limit_price,
                            p_order_chngd_chrg_cmsn,
                            p_order_chngd_prev_state,
                            p_migrated_order_id,
                            p_client_party_id,
                            p_liquidation_id,
                            p_is_give_up,
                            p_exclude_from_turnover_report
                           );


          IF p_order_managed_info IS NOT NULL THEN
            put_orders_managed_info(p_logical_load_timestamp      => lv_logical_load_timestamp,
                                    p_user                        => p_user,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_old_effctv_strt_tmstmp      => NULL,
                                    p_platform                    => p_platform,
                                    p_order_id                    => p_order_id,
                                    p_order_managed_info          => p_order_managed_info,
                                    p_operation_type              => 'I');
         END IF;


        --
        --Contingent Orders Write
        --

          ods_contingent_order.put_contingent_order ( p_user                              => p_user,
                                                      p_effective_start_timestamp         => p_effective_start_timestamp,
                                                      p_effective_end_timestamp           => p_effective_start_timestamp,
                                                      p_logical_load_timestamp            => lv_logical_load_timestamp,
                                                      p_order_id                          => p_order_id,
                                                      p_trading_account_id                => NVL(p_trading_account_id, lv_internal_account_id),
                                                      p_platform                          => p_platform,
                                                      p_product_wrapper_code              => p_product_wrapper_code,
                                                      p_product_instrument_code           => p_product_instrument_code,
                                                      p_mm_instrument_id                  => p_mm_instrument_id,
                                                      p_business_date                     => lv_business_date,
                                                      p_reporting_date                    => lv_reporting_date,
                                                      p_state                             => p_state,
                                                      p_quantity_designator               => p_quantity_designator,
                                                      p_order_type                        => lv_order_type,
                                                      p_is_primary                        => p_is_primary,
                                                      p_is_crrncy_in_frctnl_parts         => p_is_crrncy_in_fractnal_parts,
                                                      p_is_limit_trailing                 => p_is_limit_trailing,
                                                      p_limit_price                       => p_limit_price,
                                                      p_limit_price_condition             => p_limit_price_condition,
                                                      p_limit_trailing_distance           => p_limit_trailing_distance,
                                                      p_limit_trailing_best_price         => p_limit_trailing_best_price,
                                                      p_limit_trlng_best_prc_time         => p_limit_traling_best_price_tme,
                                                      p_parent_order_id                   => p_related_parent_order_id,
                                                      p_product_point_multiplier          => p_product_point_multiplier,
                                                      p_product_frctnl_part_ratio         => p_product_fractional_part_rat,
                                                      p_requested_direction               => p_requested_direction,
                                                      p_requested_quantity                => lv_requested_quantity,
                                                      p_requested_valid_till              => p_requested_valid_till,
                                                      p_normalised_quantity               => lv_normalised_quantity,
                                                      p_normalised_quantity_currency      => lv_normalised_quantity_ccy,
                                                      p_parent_order_type                 => nvl(p_parent_order_type,'Unspecified'),
                                                      p_parent_is_limit_trailing          => p_parent_is_limit_trailing,
                                                      p_parent_limit_price                => p_parent_limit_price,
                                                      --p_parent_open_trade_price           => p_parent_open_trade_price,
                                                      --p_parent_open_trade_time            => p_parent_open_trade_time,
                                                      p_parent_lmt_trlng_best_price       => p_parent_lmt_trlng_best_price,
                                                      p_parent_limit_trlng_distance       => p_parent_limit_trlng_distance,
                                                      p_parent_requested_direction        => nvl(p_parent_requested_direction,'Unspecified'),
                                                      --p_open_trade_time                   => p_open_trade_time,
                                                      --p_requested_financing_ratio         => p_requested_financing_ratio,
                                                      p_related_parent_order_id           => p_related_parent_order_id,
                                                      p_parent_order_state                => p_parent_order_state,
                                                      p_trading_account_type              => p_trading_account_type,
                                                      p_creation_identity_token           => p_creation_identity_token,
                                                      p_crtn_on_bhlf_of_idntty_tkn        => p_crtn_on_bhlf_of_idntty_tkn,
                                                      p_update_identity_token             => p_update_identity_token,
                                                      p_updt_on_bhlf_of_idntty_tkn        => p_updt_on_bhlf_of_idntty_tkn,
                                                      p_record_source                     => p_platform,
                                                      p_trading_account_function          => p_trading_account_function,
                                                      p_trading_accnt_primary_ccy         => p_trading_accnt_primary_curncy,
                                                      p_trading_scope                     => p_trading_scope,
                                                      p_event_time                        => p_event_time,
                                                      p_order_id_short_form               => lv_order_id_short_form,
                                                      p_parent_order_id_short_form        => lv_parent_order_id_short_form,
                                                      p_activation_quantity               => p_activation_quantity,
                                                      p_gslo_premium_per_unit             => p_gslo_premium_per_unit,
                                                      p_gslo_premium_fxreval_rate         => p_gslo_premium_fxreval_rate,
                                                      p_gslo_premium_refund_pct           => p_gslo_premium_refund_percentg,
                                                      p_guaranteed_limit_price            => p_guaranteed_limit_price
                                                      );

        --
        --Put Latest Position
        --
/*        IF p_platform <> 'NG' THEN
          nrg_position.put_position(p_user                              => p_user,
                                    p_effective_start_timestamp         => p_effective_start_timestamp,
                                    p_order_id                          => p_order_id,
                                    p_state                             => p_state,
                                    p_platform                          => p_platform,
                                    p_trading_account_id                => NVL(p_trading_account_id, lv_internal_account_id),
                                    p_trading_account_type              => lv_trading_account_type,
                                    p_trading_account_codifier          => p_trading_account_codifier,
                                    p_trading_account_function          => p_trading_account_function,
                                    p_mm_account_id                     => p_mm_account_id,
                                    p_product_instrument_code           => p_product_instrument_code,
                                    p_product_wrapper_code              => p_product_wrapper_code,
                                    p_product_point_multiplier          => p_product_point_multiplier,
                                    p_product_financing_ratio_max       => NULL,
                                    p_product_generation                => p_product_generation,
                                    p_is_crncy_in_frctnl_prts           => p_is_crrncy_in_fractnal_parts,
                                    p_fractional_part_ratio             => p_product_fractional_part_rat,
                                    p_mm_instrument_id                  => p_mm_instrument_id,
                                    p_requested_direction               => p_requested_direction,
                                    p_creation_time                     => p_creation_time,
                                    p_trade_id                          => NULL,
                                    p_trade_time                        => NULL,
                                    p_is_primary                        => p_is_primary,
                                    p_trdng_accnt_prmry_crrncy          => p_trading_accnt_primary_curncy,
                                    p_product_currency                  => lv_product_currency,
                                    p_trade_quantity                    => NULL,
                                    p_trade_quantity_currency           => NULL,
                                    p_nrmlsd_opn_qty_in_trdng_ccy       => lv_normalised_quantity,
                                    p_trade_price                       => NULL,
                                    p_normalised_open_price             => lv_normalised_trade_price,
                                    p_open_trade_financing_ratio        => NULL,
                                    p_trade_amount                      => NULL,
                                    p_trade_amount_currency             => NULL,
                                    p_amt_in_trdng_accnt_prmry_ccy      => NULL,
                                    p_nrmlsd_opn_vle_in_trdng_ccy       => lv_nrmlsd_opn_value,
                                    p_margin_type                       => NULL,
                                    p_normalised_margin_type            => NULL,
                                    p_normalised_margin_req             => NULL,
                                    p_margin                            => NULL,
                                    p_mrgn_trdng_accnt_prmry_ccy        => NULL,
                                    p_margin_currency                   => NULL,
                                    p_margin_secondary                  => NULL,
                                    p_margin_secondary_currency         => NULL,
                                    p_margin_fx_rate                    => NULL,
                                    p_open_trade_qntty_fx_rate          => NULL,
                                    p_cstm_info_vrtl_prtfl_cd           => NULL,
                                    p_quantity_designator               => p_quantity_designator,
                                    p_mm_backoffice_ref                 => NULL,
                                    p_mm_value_date                     => NULL,
                                    p_normalised_trading_currency       => lv_normalised_trading_ccy,
                                    p_last_modified_time                => p_update_time,
                                    p_old_effective_start_time          => NULL,
                                    p_logical_load_timestamp            => lv_logical_load_timestamp
                                    \***** Begin Modification for V5.2 - BER781 *****\
                                   ,p_record_source                     => NULL
                                    \***** End Modification for V5.2 *****\
                                   ,p_load_latest_positions             => 'YES');

          END IF;*/
      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          --Get the effective start time of the previous version of the order

          BEGIN
            SELECT ord.*
            INTO lv_old_order
            from orders ord
            WHERE order_id = p_order_id AND
                  platform = p_platform
            FOR UPDATE;

            --
            -- Open Trade Time
            -- In order to keep the trade time slways static we will never update
            -- the open trade time for MM. i.e. the trade time will always stay static
            -- once we have got the executed order. Hence to achive this we will use this local
            -- variable to store the trade time and ensure that it never gets updated once written
            -- For NG we will update the trade time as and when passed by NRG
            --

            /*IF p_platform = 'NG' THEN
              lv_open_trade_time := p_open_trade_time;
            ELSE
              lv_open_trade_time := lv_old_order.open_trade_time;
            END IF;*/

            lv_effective_start_time := lv_old_order.effective_start_timestamp;


          EXCEPTION
            WHEN no_data_found THEN
              RAISE lex_order_not_found;
          END;
      END;

      CASE
        WHEN lv_effective_start_time IS NULL THEN
          --
          --Since the order is already inserted no operation will be performed
          --

          NULL;
        WHEN lv_effective_start_time IS NOT NULL AND ((p_platform = 'NG' and (nvl(lv_old_order.version_number, 0) <= p_version_number) ) OR (p_platform <> 'NG' and lv_old_order.effective_start_timestamp <= p_effective_start_timestamp)) THEN

          --
          --In case when the effective start time of the incoming record is greater than the effective start time
          --of the order on the orders table then this specifies the update of the order
          --Update order with the latest version
          --

          UPDATE orders
          SET logical_load_timestamp          = lv_logical_load_timestamp,
              updated_by                      = p_user,
              update_timestamp                = SYSTIMESTAMP,
              effective_start_timestamp       = p_effective_start_timestamp,
              business_date                   = lv_business_date,
              reporting_date                  = lv_reporting_date,
              trading_account_id              = NVL(p_trading_account_id, lv_internal_account_id),
              trading_account_type            = lv_trading_account_type,
              session_key                     = p_session_id,
              product_instrument_code         = p_product_instrument_code,
              product_wrapper_code            = p_product_wrapper_code,
              --product_schema_code             = p_product_schema_code,
              trading_account_function        = p_trading_account_function,
              trading_account_codifier        = p_trading_account_codifier,
              changed_character               = p_changed_character,
              changed_reason_code             = p_changed_reason_code,
              changed_reason_codifier         = p_changed_reason_codifier,
              version_number                  = p_version_number,
              state                           = p_state,
              creation_time                   = p_creation_time,
              update_time                     = p_update_time,
              order_type                      = lv_order_type,
              related_parent_order_id         = p_related_parent_order_id,
              --related_initiating_order_id     = p_related_initiating_order_id,
              --open_trade_id                   = p_open_trade_id,
              controlled_order_type           = upper(lv_order_sub_type),
              related_child_order_type        = p_related_child_order_type,
              --visit_id                        = p_visit_id,
              --protection_type                 = p_protection_type,
              trading_accnt_primary_currency  = p_trading_accnt_primary_curncy,
              product_feed_symbol             = p_feed_symbol,
              product_generation              = p_product_generation,
              product_prophet_symbol          = p_prophet_symbol,
              product_currency                = lv_product_currency,
              product_point_multiplier        = p_product_point_multiplier,
              --product_financing_ratio_max     = p_product_financing_ratio_max,
              product_fractional_part_ratio   = p_product_fractional_part_rat,
              channel_id                      = p_channel_id,
              request_id                      = p_request_id,
              is_deleted                      = p_is_deleted,
              creation_identity_token         = p_creation_identity_token,
              crtn_on_bhlf_of_idntty_tkn      = p_crtn_on_bhlf_of_idntty_tkn,
              update_identity_token           = p_update_identity_token,
              updt_on_bhlf_of_idntty_tkn      = p_updt_on_bhlf_of_idntty_tkn,
              requested_direction             = p_requested_direction,
              requested_quantity              = lv_requested_quantity,
              --requested_financing_ratio       = p_requested_financing_ratio,
              requested_valid_till            = p_requested_valid_till,
              --requested_trade_close_oder_id   = p_reqsted_trade_close_oder_id,
              quantity_designator             = p_quantity_designator,
              --quantity_matchable              = p_quantity_matchable,
              quantity_match                  = p_quantity_match,
              --quantity_remaining              = p_quantity_remaining,
              --quantity_currency               = p_quantity_currency,
              --activation_time                 = p_activation_time,
              actual_valid_till               = p_actual_valid_till,
              --is_mandatory                    = p_is_mandatory,
              --execution_condition             = p_execution_condition,
              limit_price                     = p_limit_price,
              limit_price_condition           = p_limit_price_condition,
              is_limit_trailing               = p_is_limit_trailing,
              limit_trailing_distance         = p_limit_trailing_distance,
              limit_trailing_best_price       = p_limit_trailing_best_price,
              limit_trailing_best_price_time  = p_limit_traling_best_price_tme,
              --guaranteed_trade_price          = p_guaranteed_trade_price,
              --gurntd_trd_prc_cndtn            = p_gurntd_trd_prc_cndtn,
              --open_trade_quantity             = lv_open_trade_quantity,
              --open_trade_price                = p_open_trade_price,
              --open_trade_time                 = lv_open_trade_time,
              --open_trade_amount               = p_open_trade_amount,
              --open_trade_amount_currency      = p_open_trade_amount_currency,
              --open_trade_margin               = p_open_trade_margin,
              --open_trade_margin_currency      = p_open_trade_margin_currency,
              --open_trade_margin_fx_rate_bid   = p_open_trade_mrn_fx_rate_bid,
              --open_trade_margin_fx_rate_ask   = p_open_trade_mrn_fx_rate_ask,
              --opn_trd_amnt_in_ta_prm_crncy    = p_opn_trd_amnt_in_ta_prm_crncy,
              --opn_trd_mrgn_in_ta_prm_crncy    = p_opn_trd_mrgn_in_ta_prm_crncy,
              --open_trade_last_modified_time   = p_open_trd_last_modified_time,
              client_state_request_time       = p_client_state_request_time,
              client_state_quote_l1_bid       = p_client_state_quote_l1_bid,
              client_state_quote_l1_ask       = p_client_state_quote_l1_ask,
              client_state_quote_bid          = p_client_state_quote_bid,
              client_state_quote_ask          = p_client_state_quote_ask,
              --open_trade_financing_ratio      = p_open_trade_financing_ratio,
              --open_trade_qntty_fx_rate_bid    = p_open_trade_qntty_fx_rate_bid,
              --open_trade_qntty_fx_rate_ask    = p_open_trade_qntty_fx_rate_ask,
              close_time                      = p_close_time,
              --close_comment                   = p_close_comment,
              close_reason                    = p_close_reason,
              close_sub_reason                = p_close_sub_reason,
              --custom_fx_rate_ask              = p_custom_fx_rate_ask,
              --custom_fx_rate_bid              = p_custom_fx_rate_bid,
              custom_fx_rate_id               = p_custom_fx_rate_id,
              --cstm_info_vrtl_prtfl_cd         = p_cstm_info_vrtl_prtfl_cd,
              custom_info                     = p_customer_info,
              custom_quote_ask                = p_custom_quote_ask,
              custom_quote_bid                = p_custom_quote_bid,
              custom_quote_id                 = p_custom_quote_id,
              event_time                      = p_event_time,
              is_late_deal                    = p_is_late_deal,
              is_primary                      = p_is_primary,
              cntrld_ordr_comment             = p_comment,
              --is_clrng_done_unconditionally   = p_is_clrng_done_unconditionlly,
              is_crrncy_in_fractional_parts   = p_is_crrncy_in_fractnal_parts,
              --is_fnncng_ratio_chck_ignrd      = p_is_fnncng_ratio_chck_ignrd,
              is_mtch_rslt_in_pstn_inc_ignrd  = p_is_mtch_rslt_in_psn_inc_igrd,
              is_mtch_rslt_in_shrt_trd_ignrd  = p_is_mtc_rslt_in_shrt_trd_igrd,
              is_order_sz_grtr_max_ignrd   = p_is_order_sz_grtr_max_ignrd,
              is_product_generation_check     = p_is_product_generation_check,
              is_ta_sspndd_fr_trd_rvsl_ignrd  = p_is_ta_sspn_fr_trd_rvsl_ignrd,
              is_trdng_enbld_fr_prdct_ignrd   = p_is_trdng_enbld_fr_prdt_ignrd,
              is_trdng_pssbl_fr_ta_ignrd      = p_is_trdng_pssbl_fr_ta_ignrd,
              is_trdng_sspndd_fr_prdct_ignrd  = p_is_trdng_sspnd_fr_prdct_igrd,
              is_mtchng_use_lfo_opn_trd_cmpr  = p_is_mtchg_use_lfo_opn_trd_cpr,
              mm_instrument_id                = p_mm_instrument_id,
              point_multiplier                = p_point_multiplier,
              publish_time                    = p_publish_time,
              trd_reversal_pl_reverse         = p_trd_reversal_pl_reverse,
              trd_reversal_trd_id_to_reverse  = p_trd_reversal_trd_id_to_rvrse,
              use_custom_quote                = p_use_custom_quote,
              mm_account_id                   = p_mm_account_id,
              --logical_update_time             = p_logical_update_time,
              normalised_quantity             = lv_normalised_quantity,
              --open_trade_margin_requirement   = p_margin_requirement,
              --open_trade_margin_type          = p_margin_type,
              normalised_quantity_currency    = lv_normalised_quantity_ccy,
              normalised_trade_price          = lv_normalised_trade_price,
              client_state_quote_id           = p_client_state_quote_id,
              triggering_quote_id             = p_triggering_quote_id,
              triggering_time                 = p_triggering_time,
              triggering_execution_price      = p_triggering_execution_price,
              triggering_level1_price         = p_triggering_level1_price,
              boundary_price                  = p_boundary_price,
              triggering_side                 = p_triggering_side,
              is_funds_check_ignored          = p_is_funds_check_ignored,
              custom_amount_fx_rate           = p_custom_amount_fx_rate,
              client_state_parameter_info     = p_client_state_parameter_info,
              amnt_to_chrg_in_comm_ccy        = p_amnt_to_chrg_in_comm_ccy,
              reinstated_order_id             = p_reinstated_order_id,
              is_position_auto_rolled         = p_is_position_auto_rolled,
              rolled_order_id                 = p_rolled_order_id,
              CLIENT_AMOUNT                   = P_CLIENT_AMOUNT,
              commission_currency             = p_commission_currency,
              triggering_price_offset_index   = p_triggering_price_offset_idx,
              queued_quote_id                 = p_queued_quote_id,
              activation_quantity             = p_activation_quantity,
              gslo_premium_per_unit           = p_gslo_premium_per_unit,
              gslo_premium_fxreval_rate       = p_gslo_premium_fxreval_rate,
              gslo_premium_refund_percentage  = p_gslo_premium_refund_percentg,
              order_id_short_form             = lv_order_id_short_form,
              parent_order_id_short_form      = lv_parent_order_id_short_form,
              execution_type                  = p_execution_type,
              assigned_to                     = p_assigned_to,
              record_source                   = p_platform,
              trading_scope                   = p_trading_scope,
              allocation_order_id             = p_allocation_order_id,
              --alloc_trading_risk_schema       = p_alloc_trading_risk_schema,
              binary_type                     = p_binary_type,
              settle_time                     = p_settle_time,
              tenor                           = p_tenor,
              strike_price                    = p_strike_price,
              strike_price_additional         = p_strike_price_additional,
              alloc_instrument_schema         = p_alloc_instrument_schema,
              guaranteed_limit_price          = p_guaranteed_limit_price,
              was_guaranteed_executed         = p_was_guaranteed_executed,
              source_order_id                 = p_source_order_id,
              source_trade_id                 = p_source_trade_id,
              client_order_id                 = p_client_order_id,
              limit_distance                  = p_limit_distance,
              guaranteed_limit_distance       = p_guaranteed_limit_distance,
              metatrader_order_type           = p_metatrader_order_type,
              client_limit_price              = p_client_limit_price,
              order_chngd_chrg_cmsn           = p_order_chngd_chrg_cmsn,
              order_chngd_prev_state          = p_order_chngd_prev_state,
              migrated_order_id               = p_migrated_order_id,
              client_party_id                 = p_client_party_id,
              liquidation_id                  = p_liquidation_id,
              is_give_up                      = p_is_give_up,
              exclude_from_turnover_report    = p_exclude_from_turnover_report
          WHERE order_id                      = p_order_id AND
                platform                      = p_platform AND
                ( nrg_common.has_value_changed(business_date,lv_business_date) = 1 OR
                  nrg_common.has_value_changed(reporting_date, lv_reporting_date) = 1 OR
                  nrg_common.has_value_changed(trading_account_id,NVL(p_trading_account_id, lv_internal_account_id))= 1 OR
                  nrg_common.has_value_changed(trading_account_type, lv_trading_account_type)= 1 OR
                  nrg_common.has_value_changed(session_key,p_session_id)= 1 OR
                  nrg_common.has_value_changed(product_instrument_code, p_product_instrument_code)= 1 OR
                  nrg_common.has_value_changed(product_wrapper_code,p_product_wrapper_code)= 1 OR
                  --nrg_common.has_value_changed(product_schema_code,p_product_schema_code)= 1 OR
                  nrg_common.has_value_changed(trading_account_function,p_trading_account_function)= 1 OR
                  nrg_common.has_value_changed(trading_account_codifier,p_trading_account_codifier)= 1 OR
                  nrg_common.has_value_changed(version_number,p_version_number)= 1 OR
                  nrg_common.has_value_changed(state,p_state)= 1 OR
                  nrg_common.has_value_changed(creation_time,p_creation_time)= 1 OR
                  nrg_common.has_value_changed(update_time,p_update_time)= 1 OR
                  nrg_common.has_value_changed(order_type,lv_order_type)= 1 OR
                  nrg_common.has_value_changed(related_parent_order_id,p_related_parent_order_id)= 1 OR
                  --nrg_common.has_value_changed(related_initiating_order_id,p_related_initiating_order_id)= 1 OR
                  --nrg_common.has_value_changed(open_trade_id,p_open_trade_id)= 1 OR
                  nrg_common.has_value_changed(controlled_order_type,lv_order_sub_type)= 1 OR
                  nrg_common.has_value_changed(related_child_order_type,p_related_child_order_type)= 1 OR
                  --nrg_common.has_value_changed(visit_id,p_visit_id)= 1 OR
                  --nrg_common.has_value_changed(protection_type,p_protection_type)= 1 OR
                  nrg_common.has_value_changed(trading_accnt_primary_currency,p_trading_accnt_primary_curncy)= 1 OR
                  nrg_common.has_value_changed(product_feed_symbol,p_feed_symbol)= 1 OR
                  nrg_common.has_value_changed(product_generation,p_product_generation)= 1 OR
                  nrg_common.has_value_changed(product_prophet_symbol,p_prophet_symbol)= 1 OR
                  nrg_common.has_value_changed(product_currency,lv_product_currency)= 1 OR
                  nrg_common.has_value_changed(product_point_multiplier,p_product_point_multiplier)= 1 OR
                  --nrg_common.has_value_changed(product_financing_ratio_max,p_product_financing_ratio_max)= 1 OR
                  nrg_common.has_value_changed(product_fractional_part_ratio,p_product_fractional_part_rat)= 1 OR
                  nrg_common.has_value_changed(channel_id,p_channel_id)= 1 OR
                  nrg_common.has_value_changed(request_id,p_request_id)= 1 OR
                  nrg_common.has_value_changed(is_deleted,p_is_deleted)= 1 OR
                  nrg_common.has_value_changed(creation_identity_token,p_creation_identity_token)= 1 OR
                  nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_crtn_on_bhlf_of_idntty_tkn)= 1 OR
                  nrg_common.has_value_changed(update_identity_token,p_update_identity_token)= 1 OR
                  nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_updt_on_bhlf_of_idntty_tkn)= 1 OR
                  nrg_common.has_value_changed(requested_direction,p_requested_direction)= 1 OR
                  nrg_common.has_value_changed(requested_quantity,lv_requested_quantity)= 1 OR
                  --nrg_common.has_value_changed(requested_financing_ratio,p_requested_financing_ratio)= 1 OR
                  nrg_common.has_value_changed(requested_valid_till,p_requested_valid_till)= 1 OR
                  --nrg_common.has_value_changed(requested_trade_close_oder_id,p_reqsted_trade_close_oder_id)= 1 OR
                  nrg_common.has_value_changed(quantity_designator,p_quantity_designator)= 1 OR
                  --nrg_common.has_value_changed(quantity_matchable,p_quantity_matchable)= 1 OR
                  nrg_common.has_value_changed(quantity_match,p_quantity_match)= 1 OR
                  --nrg_common.has_value_changed(quantity_remaining,p_quantity_remaining)= 1 OR
                  --nrg_common.has_value_changed(quantity_currency,p_quantity_currency)= 1 OR
                  --nrg_common.has_value_changed(activation_time,p_activation_time)= 1 or
                  nrg_common.has_value_changed(actual_valid_till,p_actual_valid_till)= 1 or
                  --nrg_common.has_value_changed(is_mandatory,p_is_mandatory)= 1 OR
                  --nrg_common.has_value_changed(execution_condition,p_execution_condition)= 1 OR
                  nrg_common.has_value_changed(limit_price,p_limit_price)= 1 OR
                  nrg_common.has_value_changed(limit_price_condition,p_limit_price_condition)= 1 OR
                  nrg_common.has_value_changed(is_limit_trailing,p_is_limit_trailing)= 1 OR
                  nrg_common.has_value_changed(limit_trailing_distance,p_limit_trailing_distance)= 1 OR
                  nrg_common.has_value_changed(limit_trailing_best_price,p_limit_trailing_best_price)= 1 OR
                  nrg_common.has_value_changed(limit_trailing_best_price_time,p_limit_traling_best_price_tme)= 1 OR
                  --nrg_common.has_value_changed(guaranteed_trade_price,p_guaranteed_trade_price)= 1 OR
                  --nrg_common.has_value_changed(gurntd_trd_prc_cndtn,p_gurntd_trd_prc_cndtn)= 1 OR
                  --nrg_common.has_value_changed(open_trade_quantity,lv_open_trade_quantity)= 1 OR
                  --nrg_common.has_value_changed(open_trade_price,p_open_trade_price)= 1 OR
                  --nrg_common.has_value_changed(open_trade_amount,p_open_trade_amount)= 1 OR
                  --nrg_common.has_value_changed(open_trade_amount_currency,p_open_trade_amount_currency)= 1 OR
                  --nrg_common.has_value_changed(open_trade_margin,p_open_trade_margin)= 1 OR
                  --nrg_common.has_value_changed(open_trade_margin_currency,p_open_trade_margin_currency)= 1 OR
                  --nrg_common.has_value_changed(open_trade_margin_fx_rate_bid,p_open_trade_mrn_fx_rate_bid)= 1 OR
                  --nrg_common.has_value_changed(open_trade_margin_fx_rate_ask,p_open_trade_mrn_fx_rate_ask)= 1 OR
                  --nrg_common.has_value_changed(opn_trd_amnt_in_ta_prm_crncy,p_opn_trd_amnt_in_ta_prm_crncy)= 1 OR
                  --nrg_common.has_value_changed(opn_trd_mrgn_in_ta_prm_crncy,p_opn_trd_mrgn_in_ta_prm_crncy)= 1 OR
                  --nrg_common.has_value_changed(open_trade_last_modified_time,p_open_trd_last_modified_time)= 1 OR
                  nrg_common.has_value_changed(client_state_request_time,p_client_state_request_time)= 1 OR
                  nrg_common.has_value_changed(client_state_quote_l1_bid,p_client_state_quote_l1_bid)= 1 OR
                  nrg_common.has_value_changed(client_state_quote_l1_ask,p_client_state_quote_l1_ask)= 1 OR
                  nrg_common.has_value_changed(client_state_quote_bid,p_client_state_quote_bid)= 1 OR
                  nrg_common.has_value_changed(client_state_quote_ask,p_client_state_quote_ask)= 1 OR
                  --nrg_common.has_value_changed(open_trade_financing_ratio,p_open_trade_financing_ratio)= 1 OR
                  --nrg_common.has_value_changed(open_trade_qntty_fx_rate_bid,p_open_trade_qntty_fx_rate_bid)= 1 OR
                  --nrg_common.has_value_changed(open_trade_qntty_fx_rate_ask,p_open_trade_qntty_fx_rate_ask)= 1 OR
                  nrg_common.has_value_changed(close_time,p_close_time)= 1 OR
                  --nrg_common.has_value_changed(close_comment,p_close_comment)= 1 OR
                  nrg_common.has_value_changed(close_reason,p_close_reason)= 1 OR
                  nrg_common.has_value_changed(close_sub_reason,p_close_sub_reason)= 1 OR
                  --nrg_common.has_value_changed(custom_fx_rate_ask,p_custom_fx_rate_ask)= 1 OR
                  --nrg_common.has_value_changed(custom_fx_rate_bid,p_custom_fx_rate_bid)= 1 OR
                  nrg_common.has_value_changed(custom_fx_rate_id,p_custom_fx_rate_id)= 1 OR
                  --nrg_common.has_value_changed(cstm_info_vrtl_prtfl_cd,p_cstm_info_vrtl_prtfl_cd)= 1 or
                  nrg_common.has_value_changed(custom_info,p_customer_info)= 1 OR
                  nrg_common.has_value_changed(custom_quote_ask,p_custom_quote_ask)= 1 OR
                  nrg_common.has_value_changed(custom_quote_bid,p_custom_quote_bid)= 1 OR
                  nrg_common.has_value_changed(custom_quote_id,p_custom_quote_id)= 1 OR
                  nrg_common.has_value_changed(is_late_deal,p_is_late_deal)= 1 OR
                  nrg_common.has_value_changed(is_primary,p_is_primary)= 1 OR
                  nrg_common.has_value_changed(cntrld_ordr_comment,p_comment)= 1 or
                  --nrg_common.has_value_changed(is_clrng_done_unconditionally,p_is_clrng_done_unconditionlly)= 1 OR
                  nrg_common.has_value_changed(is_crrncy_in_fractional_parts,p_is_crrncy_in_fractnal_parts)= 1 OR
                  --nrg_common.has_value_changed(is_fnncng_ratio_chck_ignrd,p_is_fnncng_ratio_chck_ignrd)= 1 OR
                  nrg_common.has_value_changed(is_mtch_rslt_in_pstn_inc_ignrd,p_is_mtch_rslt_in_psn_inc_igrd)= 1 OR
                  nrg_common.has_value_changed(is_mtch_rslt_in_shrt_trd_ignrd,p_is_mtc_rslt_in_shrt_trd_igrd)= 1 OR
                  nrg_common.has_value_changed(is_order_sz_grtr_max_ignrd,p_is_order_sz_grtr_max_ignrd)= 1 OR
                  nrg_common.has_value_changed(is_product_generation_check,p_is_product_generation_check)= 1 OR
                  nrg_common.has_value_changed(is_ta_sspndd_fr_trd_rvsl_ignrd,p_is_ta_sspn_fr_trd_rvsl_ignrd)= 1 OR
                  nrg_common.has_value_changed(is_trdng_enbld_fr_prdct_ignrd,p_is_trdng_enbld_fr_prdt_ignrd)= 1 OR
                  nrg_common.has_value_changed(is_trdng_pssbl_fr_ta_ignrd,p_is_trdng_pssbl_fr_ta_ignrd)= 1 or
                  nrg_common.has_value_changed(is_trdng_sspndd_fr_prdct_ignrd,p_is_trdng_sspnd_fr_prdct_igrd)= 1 or
                  nrg_common.has_value_changed(is_mtchng_use_lfo_opn_trd_cmpr,p_is_mtchg_use_lfo_opn_trd_cpr)= 1 OR
                  nrg_common.has_value_changed(mm_instrument_id,p_mm_instrument_id)= 1 OR
                  nrg_common.has_value_changed(point_multiplier,p_point_multiplier)= 1 OR
                  nrg_common.has_value_changed(trd_reversal_pl_reverse,p_trd_reversal_pl_reverse)= 1 OR
                  nrg_common.has_value_changed(trd_reversal_trd_id_to_reverse,p_trd_reversal_trd_id_to_rvrse)= 1 OR
                  nrg_common.has_value_changed(use_custom_quote,p_use_custom_quote) =1 OR
                  nrg_common.has_value_changed(mm_account_id,p_mm_account_id) =1 OR
                  --nrg_common.has_value_changed(logical_update_time, p_logical_update_time) = 1 OR
                  nrg_common.has_value_changed(normalised_quantity, lv_normalised_quantity) = 1 OR
                  --nrg_common.has_value_changed(open_trade_margin_requirement, p_margin_requirement) = 1 OR
                  --nrg_common.has_value_changed(open_trade_margin_type, p_margin_type) = 1 OR
                  nrg_common.has_value_changed(normalised_quantity_currency, lv_normalised_quantity_ccy) = 1 OR
                  nrg_common.has_value_changed(normalised_trade_price,lv_normalised_trade_price) = 1 OR
                  nrg_common.has_value_changed(client_state_quote_id, p_client_state_quote_id) = 1 OR
                  nrg_common.has_value_changed(triggering_quote_id, p_triggering_quote_id) = 1 OR
                  nrg_common.has_value_changed(triggering_time, p_triggering_time) = 1 OR
                  nrg_common.has_value_changed(triggering_execution_price, p_triggering_execution_price) = 1 OR
                  nrg_common.has_value_changed(triggering_level1_price, p_triggering_level1_price) = 1 OR
                  nrg_common.has_value_changed(boundary_price, p_boundary_price) = 1 OR
                  nrg_common.has_value_changed(triggering_side, p_triggering_side) = 1 OR
                  nrg_common.has_value_changed(is_funds_check_ignored, p_is_funds_check_ignored) = 1 OR
                  nrg_common.has_value_changed(custom_amount_fx_rate, p_custom_amount_fx_rate) = 1 OR
                  nrg_common.has_value_changed(client_state_parameter_info, p_client_state_parameter_info) = 1 OR
                  nrg_common.has_value_changed(amnt_to_chrg_in_comm_ccy, p_amnt_to_chrg_in_comm_ccy) = 1 OR
                  nrg_common.has_value_changed(reinstated_order_id, p_reinstated_order_id) = 1 OR
                  nrg_common.has_value_changed(is_position_auto_rolled, p_is_position_auto_rolled) = 1 OR
                  nrg_common.has_value_changed(rolled_order_id, p_rolled_order_id) = 1 OR
                  NRG_COMMON.HAS_VALUE_CHANGED(CLIENT_AMOUNT, P_CLIENT_AMOUNT) = 1 OR
                  nrg_common.has_value_changed(commission_currency, p_commission_currency) = 1 OR
                  nrg_common.has_value_changed(triggering_price_offset_index, p_triggering_price_offset_idx) = 1 OR
                  nrg_common.has_value_changed(queued_quote_id, p_queued_quote_id) = 1 OR
                  nrg_common.has_value_changed(activation_quantity, p_activation_quantity) = 1 OR
                  nrg_common.has_value_changed(gslo_premium_per_unit, p_gslo_premium_per_unit) = 1 OR
                  nrg_common.has_value_changed(gslo_premium_fxreval_rate, p_gslo_premium_fxreval_rate) = 1 OR
                  nrg_common.has_value_changed(gslo_premium_refund_percentage, p_gslo_premium_refund_percentg) = 1 OR
                  nrg_common.has_value_changed(order_id_short_form, lv_order_id_short_form) = 1 OR
                  nrg_common.has_value_changed(parent_order_id_short_form, lv_parent_order_id_short_form) = 1 OR
                  nrg_common.has_value_changed(execution_type, p_execution_type) = 1 OR
                  nrg_common.has_value_changed(assigned_to, p_assigned_to) = 1 OR
                  nrg_common.has_value_changed(record_source, p_platform) = 1 OR
                  nrg_common.has_value_changed(trading_scope, p_trading_scope) = 1 OR
                  nrg_common.has_value_changed(allocation_order_id, p_allocation_order_id) = 1 OR
                  --nrg_common.has_value_changed(alloc_trading_risk_schema, p_alloc_trading_risk_schema) = 1 OR
                  nrg_common.has_value_changed(binary_type, p_binary_type) = 1 OR
                  nrg_common.has_value_changed(settle_time, p_settle_time) = 1 OR
                  nrg_common.has_value_changed(tenor, p_tenor) = 1 OR
                  nrg_common.has_value_changed(strike_price, p_strike_price) = 1 OR
                  nrg_common.has_value_changed(strike_price_additional, p_strike_price_additional) = 1 OR
                  nrg_common.has_value_changed(p_alloc_instrument_schema, alloc_instrument_schema) = 1 OR
                  nrg_common.has_value_changed(p_guaranteed_limit_price, guaranteed_limit_price) = 1 OR
                  nrg_common.has_value_changed(p_was_guaranteed_executed, was_guaranteed_executed) = 1 OR
                  nrg_common.has_value_changed(source_order_id, p_source_order_id) = 1 OR
                  nrg_common.has_value_changed(source_trade_id, p_source_trade_id) = 1 OR
                  nrg_common.has_value_changed(client_order_id, p_client_order_id) = 1 OR
                  nrg_common.has_value_changed(limit_distance, p_limit_distance) = 1 OR
                  nrg_common.has_value_changed(guaranteed_limit_distance, p_guaranteed_limit_distance) = 1 OR
                  nrg_common.has_value_changed(metatrader_order_type, p_metatrader_order_type) = 1 OR
                  nrg_common.has_value_changed(client_limit_price, p_client_limit_price) = 1 OR
                  nrg_common.has_value_changed(order_chngd_chrg_cmsn, p_order_chngd_chrg_cmsn) = 1 OR
                  nrg_common.has_value_changed(order_chngd_prev_state, p_order_chngd_prev_state) = 1 OR
                  nrg_common.has_value_changed(migrated_order_id, p_migrated_order_id) = 1 OR
                  nrg_common.has_value_changed(client_party_id, p_client_party_id) = 1 OR
                  nrg_common.has_value_changed(liquidation_id, p_liquidation_id) = 1 OR
                  nrg_common.has_value_changed(is_give_up, p_is_give_up) = 1 OR
                  nrg_common.has_value_changed(exclude_from_turnover_report, p_exclude_from_turnover_report) = 1
                  );


          IF p_order_managed_info IS NOT NULL THEN
            put_orders_managed_info(p_logical_load_timestamp      => lv_logical_load_timestamp,
                                    p_user                        => p_user,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_old_effctv_strt_tmstmp      => lv_effective_start_time,
                                    p_platform                    => p_platform,
                                    p_order_id                    => p_order_id,
                                    p_order_managed_info          => p_order_managed_info,
                                    p_operation_type              => 'U');
          END IF;
          --
          --Contingent Orders Write
          --

            ods_contingent_order.put_contingent_order ( p_user                              => p_user,
                                                        p_effective_start_timestamp         => p_effective_start_timestamp,
                                                        p_effective_end_timestamp           => p_effective_start_timestamp,
                                                        p_logical_load_timestamp            => lv_logical_load_timestamp,
                                                        p_order_id                          => p_order_id,
                                                        p_trading_account_id                => NVL(p_trading_account_id, lv_internal_account_id),
                                                        p_platform                          => p_platform,
                                                        p_product_wrapper_code              => p_product_wrapper_code,
                                                        p_product_instrument_code           => p_product_instrument_code,
                                                        p_mm_instrument_id                  => p_mm_instrument_id,
                                                        p_business_date                     => lv_business_date,
                                                        p_reporting_date                    => lv_reporting_date,
                                                        p_state                             => p_state,
                                                        p_quantity_designator               => p_quantity_designator,
                                                        p_order_type                        => lv_order_type,
                                                        p_is_primary                        => p_is_primary,
                                                        p_is_crrncy_in_frctnl_parts         => p_is_crrncy_in_fractnal_parts,
                                                        p_is_limit_trailing                 => p_is_limit_trailing,
                                                        p_limit_price                       => p_limit_price,
                                                        p_limit_price_condition             => p_limit_price_condition,
                                                        p_limit_trailing_distance           => p_limit_trailing_distance,
                                                        p_limit_trailing_best_price         => p_limit_trailing_best_price,
                                                        p_limit_trlng_best_prc_time         => p_limit_traling_best_price_tme,
                                                        p_parent_order_id                   => p_related_parent_order_id,
                                                        p_product_point_multiplier          => p_product_point_multiplier,
                                                        p_product_frctnl_part_ratio         => p_product_fractional_part_rat,
                                                        p_requested_direction               => p_requested_direction,
                                                        p_requested_quantity                => lv_requested_quantity,
                                                        p_requested_valid_till              => p_requested_valid_till,
                                                        p_normalised_quantity               => lv_normalised_quantity,
                                                        p_normalised_quantity_currency      => lv_normalised_quantity_ccy,
                                                        p_parent_order_type                 => nvl(p_parent_order_type,'Unspecified'),
                                                        p_parent_is_limit_trailing          => p_parent_is_limit_trailing,
                                                        p_parent_limit_price                => p_parent_limit_price,
                                                        --p_parent_open_trade_price           => p_parent_open_trade_price,
                                                        --p_parent_open_trade_time            => p_parent_open_trade_time,
                                                        p_parent_lmt_trlng_best_price       => p_parent_lmt_trlng_best_price,
                                                        p_parent_limit_trlng_distance       => p_parent_limit_trlng_distance,
                                                        p_parent_requested_direction        => nvl(p_parent_requested_direction,'Unspecified'),
                                                        --p_open_trade_time                   => p_open_trade_time,
                                                        --p_requested_financing_ratio         => p_requested_financing_ratio
                                                        p_related_parent_order_id           => p_related_parent_order_id,
                                                        p_parent_order_state                => p_parent_order_state,
                                                        p_trading_account_type              => p_trading_account_type,
                                                        p_creation_identity_token           => p_creation_identity_token,
                                                        p_crtn_on_bhlf_of_idntty_tkn        => p_crtn_on_bhlf_of_idntty_tkn,
                                                        p_update_identity_token             => p_update_identity_token,
                                                        p_updt_on_bhlf_of_idntty_tkn        => p_updt_on_bhlf_of_idntty_tkn,
                                                        p_record_source                     => p_platform,
                                                        p_trading_account_function          => p_trading_account_function,
                                                        p_trading_accnt_primary_ccy         => p_trading_accnt_primary_curncy,
                                                        p_trading_scope                     => p_trading_scope,
                                                        p_event_time                        => p_event_time,
                                                        p_order_id_short_form               => lv_order_id_short_form,
                                                        p_parent_order_id_short_form        => lv_parent_order_id_short_form,
                                                        p_activation_quantity               => p_activation_quantity,
                                                        p_gslo_premium_per_unit             => p_gslo_premium_per_unit,
                                                        p_gslo_premium_fxreval_rate         => p_gslo_premium_fxreval_rate,
                                                        p_gslo_premium_refund_pct           => p_gslo_premium_refund_percentg,
                                                        p_guaranteed_limit_price            => p_guaranteed_limit_price
                                                        );
          --
          --Write To History
          --


          IF (lv_old_order.effective_start_timestamp <> gc_default_timestamp AND nvl(lv_old_order.version_number, 0) < p_version_number ) THEN
             --
             --If the existing order is not a stub record and the same record has not been replayed
             --then put the old record in the history
             --

            put_history (p_old_order_record         => lv_old_order,
                         p_effective_end_timestamp  => p_effective_start_timestamp,
                         p_action                   => 'U');
          END IF;

          lv_old_order := NULL;

          --
          --Put Latest Position
          --
        /*  IF p_platform <> 'NG' THEN
            nrg_position.put_position(p_user                              => p_user,
                                      p_effective_start_timestamp         => p_effective_start_timestamp,
                                      p_order_id                          => p_order_id,
                                      p_state                             => p_state,
                                      p_platform                          => p_platform,
                                      p_trading_account_id                => NVL(p_trading_account_id, lv_internal_account_id),
                                      p_trading_account_type              => lv_trading_account_type,
                                      p_trading_account_codifier          => p_trading_account_codifier,
                                      p_trading_account_function          => p_trading_account_function,
                                      p_mm_account_id                     => p_mm_account_id,
                                      p_product_instrument_code           => p_product_instrument_code,
                                      p_product_wrapper_code              => p_product_wrapper_code,
                                      p_product_point_multiplier          => p_product_point_multiplier,
                                      p_product_financing_ratio_max       => NULL,
                                      p_product_generation                => p_product_generation,
                                      p_is_crncy_in_frctnl_prts           => p_is_crrncy_in_fractnal_parts,
                                      p_fractional_part_ratio             => p_product_fractional_part_rat,
                                      p_mm_instrument_id                  => p_mm_instrument_id,
                                      p_requested_direction               => p_requested_direction,
                                      p_creation_time                     => p_creation_time,
                                      p_trade_id                          => NULL,
                                      p_trade_time                        => NULL,
                                      p_is_primary                        => p_is_primary,
                                      p_trdng_accnt_prmry_crrncy          => p_trading_accnt_primary_curncy,
                                      p_product_currency                  => lv_product_currency,
                                      p_trade_quantity                    => NULL,
                                      p_trade_quantity_currency           => NULL,
                                      p_nrmlsd_opn_qty_in_trdng_ccy       => lv_normalised_quantity,
                                      p_trade_price                       => NULL,
                                      p_normalised_open_price             => lv_normalised_trade_price,
                                      p_open_trade_financing_ratio        => NULL,
                                      p_trade_amount                      => NULL,
                                      p_trade_amount_currency             => NULL,
                                      p_amt_in_trdng_accnt_prmry_ccy      => NULL,
                                      p_nrmlsd_opn_vle_in_trdng_ccy       => lv_nrmlsd_opn_value,
                                      p_margin_type                       => NULL,
                                      p_normalised_margin_type            => NULL,
                                      p_normalised_margin_req             => NULL,
                                      p_margin                            => NULL,
                                      p_mrgn_trdng_accnt_prmry_ccy        => NULL,
                                      p_margin_currency                   => NULL,
                                      p_margin_secondary                  => NULL,
                                      p_margin_secondary_currency         => NULL,
                                      p_margin_fx_rate                    => NULL,
                                      p_open_trade_qntty_fx_rate          => NULL,
                                      p_cstm_info_vrtl_prtfl_cd           => NULL,
                                      p_quantity_designator               => p_quantity_designator,
                                      p_mm_backoffice_ref                 => NULL,
                                      p_mm_value_date                     => NULL,
                                      p_normalised_trading_currency       => lv_normalised_trading_ccy,
                                      p_last_modified_time                => p_update_time,
                                      p_old_effective_start_time          => lv_effective_start_time,
                                      p_logical_load_timestamp            => lv_logical_load_timestamp
                                      \***** Begin Modification for V5.2 - BER781 *****\
                                     ,p_record_source                     => NULL
                                      \***** End Modification for V5.2 *****\
                                     ,p_load_latest_positions             => 'YES');

          END IF;
*/
        WHEN lv_effective_start_time IS NOT NULL AND ((p_platform = 'NG' and lv_old_order.version_number > p_version_number ) OR (p_platform <> 'NG' and lv_old_order.effective_start_timestamp > p_effective_start_timestamp)) THEN
          --
          --When the effective start time of the incoming record is smaller than the effective start time of the
          --order on the orders table then it signifies old message is being replayed.
          --Write to history table directly
          --
          SELECT  p_order_id,
                  p_platform,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  lv_business_date,
                  lv_reporting_date,
                  NVL(p_trading_account_id, lv_internal_account_id),
                  lv_trading_account_type,
                  p_session_id,
                  p_product_instrument_code,
                  p_product_wrapper_code,
                  --p_product_schema_code,
                  p_trading_account_function,
                  p_trading_account_codifier,
                  p_changed_character,
                  p_changed_reason_code,
                  p_changed_reason_codifier,
                  p_version_number,
                  p_state,
                  p_creation_time,
                  p_update_time,
                  lv_order_type,
                  p_related_parent_order_id,
                  --p_related_initiating_order_id,
                  --p_open_trade_id,
                  lv_order_sub_type,
                  p_related_child_order_type,
                  --p_visit_id,
                  --p_protection_type,
                  p_trading_accnt_primary_curncy,
                  p_feed_symbol,
                  p_product_generation,
                  p_prophet_symbol,
                  lv_product_currency,
                  p_product_point_multiplier,
                  --p_product_financing_ratio_max,
                  p_product_fractional_part_rat,
                  p_channel_id,
                  p_request_id,
                  p_is_deleted,
                  p_creation_identity_token,
                  p_crtn_on_bhlf_of_idntty_tkn,
                  p_update_identity_token,
                  p_updt_on_bhlf_of_idntty_tkn,
                  p_requested_direction,
                  lv_requested_quantity,
                  --p_requested_financing_ratio,
                  p_requested_valid_till,
                  --p_reqsted_trade_close_oder_id,
                  p_quantity_designator,
                  --p_quantity_matchable,
                  p_quantity_match,
                  --p_quantity_remaining,
                  --p_quantity_currency,
                  --p_activation_time,
                  p_actual_valid_till,
                  --p_is_mandatory,
                  --p_execution_condition,
                  p_limit_price,
                  p_limit_price_condition,
                  p_is_limit_trailing,
                  p_limit_trailing_distance,
                  p_limit_trailing_best_price,
                  p_limit_traling_best_price_tme,
                  --p_guaranteed_trade_price,
                  --p_gurntd_trd_prc_cndtn,
                  --lv_open_trade_quantity,
                  --p_open_trade_price,
                  --lv_open_trade_time,
                  --p_open_trade_amount,
                  --p_open_trade_amount_currency,
                  --p_open_trade_margin,
                  --p_open_trade_margin_currency,
                  --p_open_trade_mrn_fx_rate_bid,
                  --p_open_trade_mrn_fx_rate_ask,
                  --p_opn_trd_amnt_in_ta_prm_crncy,
                  --p_opn_trd_mrgn_in_ta_prm_crncy,
                  --p_open_trd_last_modified_time,
                  p_client_state_request_time,
                  p_client_state_quote_l1_bid,
                  p_client_state_quote_l1_ask,
                  p_client_state_quote_bid,
                  p_client_state_quote_ask,
                  --p_open_trade_financing_ratio,
                  --p_open_trade_qntty_fx_rate_bid,
                  --p_open_trade_qntty_fx_rate_ask,
                  p_close_time,
                  NULL, --p_close_comment,
                  p_close_reason,
                  p_close_sub_reason,
                  --p_custom_fx_rate_ask,
                  --p_custom_fx_rate_bid,
                  p_custom_fx_rate_id,
                  --p_cstm_info_vrtl_prtfl_cd,
                  p_customer_info,
                  p_custom_quote_ask,
                  p_custom_quote_bid,
                  p_custom_quote_id,
                  p_event_time,
                  p_is_late_deal,
                  p_is_primary,
                  p_comment,
                  --p_is_clrng_done_unconditionlly,
                  p_is_crrncy_in_fractnal_parts,
                  --p_is_fnncng_ratio_chck_ignrd,
                  p_is_mtch_rslt_in_psn_inc_igrd,
                  p_is_mtc_rslt_in_shrt_trd_igrd,
                  p_is_order_sz_grtr_max_ignrd,
                  p_is_product_generation_check,
                  p_is_ta_sspn_fr_trd_rvsl_ignrd,
                  p_is_trdng_enbld_fr_prdt_ignrd,
                  p_is_trdng_pssbl_fr_ta_ignrd,
                  p_is_trdng_sspnd_fr_prdct_igrd,
                  p_is_mtchg_use_lfo_opn_trd_cpr,
                  p_mm_instrument_id,
                  p_point_multiplier,
                  p_publish_time,
                  p_trd_reversal_pl_reverse,
                  p_trd_reversal_trd_id_to_rvrse,
                  p_use_custom_quote,
                  p_mm_account_id,
                  --p_logical_update_time,
                  lv_normalised_quantity,
                  --p_margin_requirement,
                  --p_margin_type,
                  lv_normalised_quantity_ccy,
                  lv_normalised_trade_price,
                  p_client_state_quote_id,
                  p_triggering_quote_id,
                  p_triggering_time,
                  p_triggering_execution_price,
                  p_triggering_level1_price,
                  p_boundary_price,
                  p_triggering_side,
                  p_is_funds_check_ignored,
                  p_custom_amount_fx_rate,
                  p_client_state_parameter_info,
                  p_amnt_to_chrg_in_comm_ccy,
                  p_reinstated_order_id,
                  p_is_position_auto_rolled,
                  p_rolled_order_id,
                  P_CLIENT_AMOUNT,
                  p_commission_currency,
                  p_triggering_price_offset_idx,
                  p_queued_quote_id,
                  p_activation_quantity,
                  p_gslo_premium_per_unit,
                  p_gslo_premium_fxreval_rate,
                  p_gslo_premium_refund_percentg,
                  lv_order_id_short_form,
                  lv_parent_order_id_short_form,
                  p_execution_type,
                  p_assigned_to,
                  p_platform,
                  p_trading_scope,
                  p_allocation_order_id,
                  NULL,--p_alloc_trading_risk_schema,
                  p_binary_type,
                  p_settle_time,
                  p_tenor,
                  p_strike_price,
                  p_strike_price_additional,
                  p_alloc_instrument_schema,
                  p_guaranteed_limit_price,
                  p_was_guaranteed_executed,
                  p_source_order_id,
                  p_source_trade_id,
                  p_client_order_id,
                  p_limit_distance,
                  p_guaranteed_limit_distance
            INTO  lv_old_order.order_id,
                  lv_old_order.platform,
                  lv_old_order.logical_load_timestamp,
                  lv_old_order.created_by,
                  lv_old_order.create_timestamp,
                  lv_old_order.updated_by,
                  lv_old_order.update_timestamp,
                  lv_old_order.effective_start_timestamp,
                  lv_old_order.business_date,
                  lv_old_order.reporting_date,
                  lv_old_order.trading_account_id,
                  lv_old_order.trading_account_type,
                  lv_old_order.session_key,
                  lv_old_order.product_instrument_code,
                  lv_old_order.product_wrapper_code,
                  --lv_old_order.product_schema_code,
                  lv_old_order.trading_account_function,
                  lv_old_order.trading_account_codifier,
                  lv_old_order.changed_character,
                  lv_old_order.changed_reason_code,
                  lv_old_order.changed_reason_codifier,
                  lv_old_order.version_number,
                  lv_old_order.state,
                  lv_old_order.creation_time,
                  lv_old_order.update_time,
                  lv_old_order.order_type,
                  lv_old_order.related_parent_order_id,
                  --lv_old_order.related_initiating_order_id,
                  --lv_old_order.open_trade_id,
                  lv_old_order.controlled_order_type,
                  lv_old_order.related_child_order_type,
                  --lv_old_order.visit_id,
                  --lv_old_order.protection_type,
                  lv_old_order.trading_accnt_primary_currency,
                  lv_old_order.product_feed_symbol,
                  lv_old_order.product_generation,
                  lv_old_order.product_prophet_symbol,
                  lv_old_order.product_currency,
                  lv_old_order.product_point_multiplier,
                  --lv_old_order.product_financing_ratio_max,
                  lv_old_order.product_fractional_part_ratio,
                  lv_old_order.channel_id,
                  lv_old_order.request_id,
                  lv_old_order.is_deleted,
                  lv_old_order.creation_identity_token,
                  lv_old_order.crtn_on_bhlf_of_idntty_tkn,
                  lv_old_order.update_identity_token,
                  lv_old_order.updt_on_bhlf_of_idntty_tkn,
                  lv_old_order.requested_direction,
                  lv_old_order.requested_quantity,
                  --lv_old_order.requested_financing_ratio,
                  lv_old_order.requested_valid_till,
                  --lv_old_order.requested_trade_close_oder_id,
                  lv_old_order.quantity_designator,
                  --lv_old_order.quantity_matchable,
                  lv_old_order.quantity_match,
                  --lv_old_order.quantity_remaining,
                  --lv_old_order.quantity_currency,
                  --lv_old_order.activation_time,
                  lv_old_order.actual_valid_till,
                  --lv_old_order.is_mandatory,
                  --lv_old_order.execution_condition,
                  lv_old_order.limit_price,
                  lv_old_order.limit_price_condition,
                  lv_old_order.is_limit_trailing,
                  lv_old_order.limit_trailing_distance,
                  lv_old_order.limit_trailing_best_price,
                  lv_old_order.limit_trailing_best_price_time,
                  --lv_old_order.guaranteed_trade_price,
                  --lv_old_order.gurntd_trd_prc_cndtn,
                  --lv_old_order.open_trade_quantity,
                  --lv_old_order.open_trade_price,
                  --lv_old_order.open_trade_time,
                  --lv_old_order.open_trade_amount,
                  --lv_old_order.open_trade_amount_currency,
                  --lv_old_order.open_trade_margin,
                  --lv_old_order.open_trade_margin_currency,
                  --lv_old_order.open_trade_margin_fx_rate_bid,
                  --lv_old_order.open_trade_margin_fx_rate_ask,
                  --lv_old_order.opn_trd_amnt_in_ta_prm_crncy,
                  --lv_old_order.opn_trd_mrgn_in_ta_prm_crncy,
                  --lv_old_order.open_trade_last_modified_time,
                  lv_old_order.client_state_request_time,
                  lv_old_order.client_state_quote_l1_bid,
                  lv_old_order.client_state_quote_l1_ask,
                  lv_old_order.client_state_quote_bid,
                  lv_old_order.client_state_quote_ask,
                  --lv_old_order.open_trade_financing_ratio,
                  --lv_old_order.open_trade_qntty_fx_rate_bid,
                  --lv_old_order.open_trade_qntty_fx_rate_ask,
                  lv_old_order.close_time,
                  lv_old_order.close_comment,
                  lv_old_order.close_reason,
                  lv_old_order.close_sub_reason,
                  --lv_old_order.custom_fx_rate_ask,
                  --lv_old_order.custom_fx_rate_bid,
                  lv_old_order.custom_fx_rate_id,
                  --lv_old_order.cstm_info_vrtl_prtfl_cd,
                  lv_old_order.custom_info,
                  lv_old_order.custom_quote_ask,
                  lv_old_order.custom_quote_bid,
                  lv_old_order.custom_quote_id,
                  lv_old_order.event_time,
                  lv_old_order.is_late_deal,
                  lv_old_order.is_primary,
                  lv_old_order.cntrld_ordr_comment,
                  --lv_old_order.is_clrng_done_unconditionally,
                  lv_old_order.is_crrncy_in_fractional_parts,
                  --lv_old_order.is_fnncng_ratio_chck_ignrd,
                  lv_old_order.is_mtch_rslt_in_pstn_inc_ignrd,
                  lv_old_order.is_mtch_rslt_in_shrt_trd_ignrd,
                  lv_old_order.is_order_sz_grtr_max_ignrd,
                  lv_old_order.is_product_generation_check,
                  lv_old_order.is_ta_sspndd_fr_trd_rvsl_ignrd,
                  lv_old_order.is_trdng_enbld_fr_prdct_ignrd,
                  lv_old_order.is_trdng_pssbl_fr_ta_ignrd,
                  lv_old_order.is_trdng_sspndd_fr_prdct_ignrd,
                  lv_old_order.is_mtchng_use_lfo_opn_trd_cmpr,
                  lv_old_order.mm_instrument_id,
                  lv_old_order.point_multiplier,
                  lv_old_order.publish_time,
                  lv_old_order.trd_reversal_pl_reverse,
                  lv_old_order.trd_reversal_trd_id_to_reverse,
                  lv_old_order.use_custom_quote,
                  lv_old_order.mm_account_id,
                  --lv_old_order.logical_update_time,
                  lv_old_order.normalised_quantity,
                  --lv_old_order.open_trade_margin_requirement,
                  --lv_old_order.open_trade_margin_type,
                  lv_old_order.normalised_quantity_currency,
                  lv_old_order.normalised_trade_price,
                  lv_old_order.client_state_quote_id,
                  lv_old_order.triggering_quote_id,
                  lv_old_order.triggering_time,
                  lv_old_order.triggering_execution_price,
                  lv_old_order.triggering_level1_price,
                  lv_old_order.boundary_price,
                  lv_old_order.triggering_side,
                  lv_old_order.is_funds_check_ignored,
                  lv_old_order.custom_amount_fx_rate,
                  lv_old_order.client_state_parameter_info,
                  lv_old_order.amnt_to_chrg_in_comm_ccy,
                  lv_old_order.reinstated_order_id,
                  lv_old_order.is_position_auto_rolled,
                  lv_old_order.rolled_order_id,
                  LV_OLD_ORDER.CLIENT_AMOUNT,
                  LV_OLD_ORDER.Commission_Currency,
                  LV_OLD_ORDER.Triggering_Price_Offset_Index,
                  LV_OLD_ORDER.Queued_Quote_Id,
                  lv_old_order.activation_quantity,
                  lv_old_order.gslo_premium_per_unit,
                  lv_old_order.gslo_premium_fxreval_rate,
                  lv_old_order.gslo_premium_refund_percentage,
                  lv_old_order.order_id_short_form,
                  lv_old_order.Parent_Order_Id_Short_Form,
                  lv_old_order.execution_type,
                  lv_old_order.assigned_to,
                  lv_old_order.Record_Source,
                  lv_old_order.trading_scope,
                  lv_old_order.allocation_order_id,
                  lv_old_order.alloc_trading_risk_schema,
                  lv_old_order.binary_type,
                  lv_old_order.settle_time,
                  lv_old_order.tenor,
                  lv_old_order.strike_price,
                  lv_old_order.strike_price_additional,
                  lv_old_order.alloc_instrument_schema,
                  lv_old_order.guaranteed_limit_price,
                  lv_old_order.was_guaranteed_executed,
                  lv_old_order.source_order_id,
                  lv_old_order.source_trade_id,
                  lv_old_order.client_order_id,
                  lv_old_order.limit_distance,
                  lv_old_order.guaranteed_limit_distance
          FROM DUAL;

          put_history (p_old_order_record         => lv_old_order,
                       p_effective_end_timestamp  => p_effective_start_timestamp,
                       p_action                   => 'I');

          lv_old_order := NULL;

          IF p_order_managed_info IS NOT NULL THEN
            put_orders_managed_info(p_logical_load_timestamp      => lv_logical_load_timestamp,
                                    p_user                        => p_user,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_old_effctv_strt_tmstmp      => lv_effective_start_time,
                                    p_platform                    => p_platform,
                                    p_order_id                    => p_order_id,
                                    p_order_managed_info          => p_order_managed_info,
                                    p_operation_type              => 'OU');
          END IF;

        ELSE
          RAISE lex_unknown_operation_type;
      END CASE;

      IF p_rqustd_open_trade_to_close IS NOT NULL THEN
        put_open_trade_to_close(p_logical_load_timestamp      => lv_logical_load_timestamp,
                                p_user                        => p_user,
                                p_effective_start_timestamp   => p_effective_start_timestamp,
                                p_old_effctv_strt_tmstmp      => lv_effective_start_time,
                                p_order_id                    => p_order_id,
                                p_platform                    => p_platform,
                                p_rqustd_open_trade_to_close  => p_rqustd_open_trade_to_close);
      END IF;

      IF p_order_close_comment IS NOT NULL THEN
        put_order_close_comment (p_order_id                   => p_order_id,
                                 p_platform                   => p_platform,
                                 p_close_comment              => p_order_close_comment,
                                 p_user                       => p_user,
                                 p_effective_start_timestamp  => p_effective_start_timestamp,
                                 p_old_effctv_strt_timestamp  => lv_effective_start_time,
                                 p_logical_load_timestamp     => lv_logical_load_timestamp);
      END IF;


    EXCEPTION
        WHEN lex_order_not_found THEN
          logger.logger.severe('Order Deleted Before Update and After Insert');
          logger.logger.set_module(NULL);
          raise_application_error(-20003, 'Order Deleted Before Update and After Insert');
        WHEN lex_unknown_operation_type THEN
          logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
          logger.logger.set_module(NULL);
          raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END put_order;

  -- ===================================================================================
  -- get_order_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of order_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform MMCFD or MMSB or NG
  --     p_order_time_from                 order time from - Time order was created at source
  --     p_order_time_to                   order time to - Time order was created at source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
  FUNCTION get_order_ids(p_platform         IN orders.platform%TYPE DEFAULT NULL,
                         p_order_time_from  IN orders.creation_time%TYPE DEFAULT TRUNC(SYSDATE),
                         p_order_time_to    IN orders.creation_time%TYPE DEFAULT TRUNC(SYSDATE)) RETURN SYS_REFCURSOR

  IS
    lcuv_result           SYS_REFCURSOR;
  BEGIN
    logger.logger.set_module('get_order_ids');
    OPEN lcuv_result FOR
                        SELECT platform, order_id, version_number
                        FROM   orders
                        WHERE  (creation_time BETWEEN p_order_time_from AND p_order_time_to OR
                                update_time BETWEEN p_order_time_from AND p_order_time_to) AND
                                (record_source IS NULL OR record_source!='SPEEDBET') AND
                                effective_start_timestamp <> gc_default_timestamp AND
                               (p_platform IS NULL OR platform = p_platform);

    logger.logger.set_module(NULL);

    RETURN lcuv_result;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END get_order_ids;

    -- ===================================================================================
    -- get_closed_executed_order_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of order_id attributes -BER-890
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_order_update_time_from         order update time from
    --     p_order_update_time_to           order update time to
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --      -20005    Invalid Platform Name. Expected Platform names are NG
    --      -20004    Dafault Exception
    -- -----------------------------------------------------------------------------------
 FUNCTION get_closed_executed_order_ids(p_order_update_time_from  IN orders.Update_Time%TYPE,
                                        p_order_update_time_to    IN orders.Update_Time%TYPE) RETURN SYS_REFCURSOR
    IS
      lcuv_result           SYS_REFCURSOR;

   BEGIN
      logger.logger.set_module('get_closed_executed_order_ids');

     OPEN lcuv_result FOR
       SELECT order_id
              FROM   orders
              WHERE  update_time >= p_order_update_time_from AND
                     update_time < p_order_update_time_to AND
                     effective_start_timestamp <> gc_default_timestamp AND
                     platform = 'NG' AND
                     (record_source != 'SPEEDBET'  OR record_source IS NULL) AND
                     state IN ('CLOSED','EXECUTED');

      logger.logger.set_module(NULL);

      RETURN lcuv_result;

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
 END get_closed_executed_order_ids;

    -- ===================================================================================
    -- get_stoploss_order_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of order_id attributes BER-907
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_order_update_time_from         order update time from
    --     p_order_update_time_to           order update time to
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --      -20005    Invalid Platform Name. Expected Platform names are NG
    --      -20004    Dafault Exception
    -- -----------------------------------------------------------------------------------
 FUNCTION get_stoploss_order_ids(p_order_update_time_from  IN orders.Update_Time%TYPE,
                                 p_order_update_time_to    IN orders.Update_Time%TYPE) RETURN SYS_REFCURSOR
    IS
      lcuv_result           SYS_REFCURSOR;

   BEGIN
      logger.logger.set_module('get_stoploss_order_ids');

         OPEN lcuv_result
          FOR  SELECT order_id
                FROM bi_ods.orders
                WHERE platform = 'NG'
                 AND state IN ('CLOSED','EXECUTED')
                 AND order_type in ('STOPLOSS (TRAILING)', 'STOPLOSS (TRAILING, MANDATORY)')
                 AND (record_source != 'SPEEDBET' OR record_source IS NULL)
                 AND effective_start_timestamp <> gc_default_timestamp
                 AND update_time >= p_order_update_time_from
                 AND update_time < p_order_update_time_to;


      logger.logger.set_module(NULL);

      RETURN lcuv_result;

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
 END get_stoploss_order_ids;
  -- ===================================================================================
  -- get_stubbed_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed cash account id's
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
  --     p_platform                       Cash Account Platform
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
  FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER,
                           p_platform     IN VARCHAR2) RETURN SYS_REFCURSOR IS

    lcuv_result           SYS_REFCURSOR;

    lex_unknown_platform  EXCEPTION;

  BEGIN
    logger.logger.set_module('get_stubbed_ids');

    IF p_platform IS NULL OR p_platform NOT IN ('NG','MMSB','MMCFD') THEN
      RAISE lex_unknown_platform;
    END IF;

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
                 SELECT order_id
                   FROM orders
                  WHERE effective_start_timestamp = gc_default_timestamp
                    AND platform = p_platform
                    AND (record_source != 'SPEEDBET' OR record_source IS NULL)
                    AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
                 SELECT order_id
                   FROM orders
                  WHERE effective_start_timestamp = gc_default_timestamp
                    AND platform = p_platform
                    AND (record_source != 'SPEEDBET' OR record_source IS NULL);

    END CASE;

    logger.logger.set_module(NULL);

    RETURN lcuv_result;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END get_stubbed_ids;

  -- ===================================================================================
  -- cleanup_contingent_orders
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure is to delete the contingent orders history so that only 1 day worth of contingent orders are stored
  --     This procedure is scheduled using the dbms scheduler to clear the contingent orders at regular intervals
  --     This is done to restrict the size of data on contingent orders
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
  --     p_platform                       Cash Account Platform
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------

  PROCEDURE cleanup_contingent_orders IS

    n_commit_freequency NUMBER := 2000;
    n_row_count         NUMBER := 0;
    CURSOR cur_old_contingent_orders IS (SELECT ROWID row_id
                                         FROM CONTINGENT_ORDERS
                                         WHERE (current_flg = 'N' OR ORDER_STATE='EXECUTED') AND
                                               business_date < nrg_common.get_business_date(SYSDATE));
  BEGIN

    n_row_count := 0;

    FOR rec_contingent_orders IN cur_old_contingent_orders LOOP
      DELETE contingent_orders
      WHERE rowid = rec_contingent_orders.row_id;

      n_row_count := n_row_count + SQL%ROWCOUNT;

      IF (MOD(n_row_count, n_commit_freequency) = 0) THEN
        COMMIT;
      END IF;
    END LOOP;

    COMMIT;

  /*  EXECUTE IMMEDIATE 'ALTER TABLE contingent_orders ENABLE ROW MOVEMENT';
    EXECUTE IMMEDIATE 'ALTER TABLE contingent_orders SHRINK SPACE';*/

    BEGIN
       dbms_stats.gather_table_stats(ownname=>'BI_ODS',
                                     tabname=>'CONTINGENT_ORDERS',
                                     CASCADE=>TRUE,
                                    DEGREE=>4);


     /*  dbms_stats.gather_table_stats(ownname=>'BI_ODS',
                                     tabname=>'CONTINGENT_ORDERS',
                                     estimate_percent=>dbms_stats.auto_sample_size,
                                     method_opt=>'for all columns size skewonly',
                                     CASCADE=>TRUE,
                                     DEGREE=>7);*/
    END;
  END;
END nrg_order;
/