CREATE OR REPLACE TYPE options_xcode_obj
IS
    OBJECT (
        logical_load_timestamp    timestamp(6),
        created_by                varchar2(50),
        create_timestamp          timestamp(6),
        updated_by                varchar2(50),
        update_timestamp          timestamp(6),
        effective_start_timestamp timestamp(6),
        option_code               varchar2(50),
        listing_source            varchar2(50),
        subscription_symbol       varchar2(50),
        nozomi_output_symbol      varchar2(100),
        instrument_code           varchar2(50),
        trading_class_code        varchar2(50),
        trading_class_name        varchar2(100),
        tick_size_list_code       number,
        expiration_date           timestamp(6),
        option_type               varchar2(50),
        strike                    number,
        last_trading_date         timestamp(6),
        order_book_id             varchar2(100),
        trading_currency_code     varchar2(50),
        instrument_name           varchar2(50),
        strike_exponent           varchar2(50)
    );
/