------------------------------
-- JCS-15901 TERMS OF BUSINESS
------------------------------
DROP TABLE terms_of_business;
DROP TABLE terms_of_business_h;

CREATE TABLE terms_of_business (
  tob_reference                    VARCHAR2(200),
  tob_version                      NUMBER,
  logical_load_timestamp           TIMESTAMP(6),
  created_by                       VARCHAR2(50),
  create_timestamp                 TIMESTAMP(6),
  updated_by                       VARCHAR2(50),
  update_timestamp                 TIMESTAMP(6),
  effective_start_timestamp        TIMESTAMP(6),
  brand                            VARCHAR2(50),
  region                           VARCHAR2(50),
  account_type                     VARCHAR2(50),
  legal_entity                     VARCHAR2(50),
  reg_classification               VARCHAR2(50),
  sales_trader_managed             VARCHAR2(50),
  tob_status                       VARCHAR2(50),
  effective_from                   TIMESTAMP(6),
  notification_from                TIMESTAMP(6),
  unsigned_tobs_trading_impact     VARCHAR2(50),
  tenant_template                  VARCHAR2(100)
)  
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.terms_of_business  
add 
 constraint terms_of_business_pk primary key (tob_reference,tob_version,brand,region,account_type,legal_entity,reg_classification,sales_trader_managed,tenant_template)
  using index 
  tablespace ODS_IND;

CREATE TABLE terms_of_business_h (
  tob_reference                    VARCHAR2(200),
  tob_version                      NUMBER,
  logical_load_timestamp           TIMESTAMP(6),
  created_by                       VARCHAR2(50),
  create_timestamp                 TIMESTAMP(6),
  updated_by                       VARCHAR2(50),
  update_timestamp                 TIMESTAMP(6),
  effective_start_timestamp        TIMESTAMP(6),
  effective_end_timestamp          timestamp(6),
  action                           varchar2(1),
  action_timestamp                 timestamp(6),
  brand                            VARCHAR2(50),
  region                           VARCHAR2(50),
  account_type                     VARCHAR2(50),
  legal_entity                     VARCHAR2(50),
  reg_classification               VARCHAR2(50),
  sales_trader_managed             VARCHAR2(50),
  tob_status                       VARCHAR2(50),
  effective_from                   TIMESTAMP(6),
  notification_from                TIMESTAMP(6),
  unsigned_tobs_trading_impact     VARCHAR2(50),  
  tenant_template                  VARCHAR2(100)
)  
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.terms_of_business_h  
add
 constraint terms_of_business_h_pk primary key (tob_reference,tob_version,brand,region,account_type,legal_entity,reg_classification,sales_trader_managed,tenant_template,effective_start_timestamp)
  using index 
  tablespace ODS_IND;