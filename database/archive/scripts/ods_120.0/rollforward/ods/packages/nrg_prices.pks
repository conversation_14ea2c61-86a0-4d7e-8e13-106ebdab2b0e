create or replace PACKAGE        nrg_prices
as
    -- ===================================================================================
    -- NRG_PRICES
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the prices model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    --  Technical Scenarios to handle data for prices :-
    --
    --  Scenario 1 - The current price snapshot is received - In such a scenario we need to find out the prices that have got updated
    --                                                      and move them to history table and update the base table with the latest
    --                                                      prices. Any price that does not exist on the snapshot and exist on the base table
    --                                                      need to be left on the base table as they are the latest price
    --
    --  Scenario 2 - An older snapshot is received - In case when a older snapshot is received we need to check if the
    --                                            base table contains the price symbol for an older effective start timestamp.
    --                                            If such prices exists then we need to move these prices into history and then update the
    --                                            base table.
    --
    --                                            In case if there are prices on the snapshot that do not exist on the base table
    --                                            then insert the missing prices to the base table to ensure that the latest prices are
    --                                            always there in the base table
    --
    --                                            The rest of the prices on the snapshot which exists on the base table and are
    --                                            older than the ones on the base table needs to be written to the history directly
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     11/11/2011   Paul Flynn        1.0     Creation
    --     23/11/2011   Sanket Mittal     1.2     Updated put_prices_set to handle the historical replays
    --     30/12/2011   Sanket Mittal     1.3     Added the change for reporting_date
    --     14/06/2012   Mark Gornicki     1.4     Amended to cope with the breaking change of switching to use Nozomi v4 contract.
    --                                            This involves the acquisition and storage of multiple embedded data arrays
    --                                            for each price symbol.  This has been structured such that NRG will pass each
    --                                            embedded array as a separate table parameter containing the corresponding data for
    --                                            all price symbols i.e. a complete set ready to be processed.
    --                                            In addition many price fields are now obsolete and will no longer be passed,
    --                                            however the underlying tables will still retain these columns for historical purposes.
    --     14/02/2013   Sanket Mittal     1.5     Updated prices structures to reflect version 4.1 nazomi
    --     19/05/2014   Adam Krasnicki    1.7     New procedure put_latest_prices for latest prices only
    --     30/10/2014   Adam Krasnicki    1.8     New procedure added - put_anytime_prices
    --     13/05/2016   Sanket Mittal     2.0     Updated prices and prices_h to load all snapshots into prices table
    --     19/12/2016   Sanket Mittal     2.1     BER-3222 NRG_PRICES - deprecated attributes
    --     19/01/2017   Sanket Mittal     2.3     BER-3241 Furai Pricing Contract attributes to be added in new table
    --     10/07/2020   Patrick Dinwiddy  2.7     JCS-13345
    --     21/08/2020   Patrick Dinwiddy  2.8     JCS-13500 and JCS-13514
    --     12/04/2023   Adam Davies       2.9     JCS-16676 - put_price_options_contracts
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

    -- ===================================================================================
    -- put_price_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a price stub
    --
    -- Notes:
    -- ------
    --
    --     Tables populated:
    --
    --     PRICES
    --
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_price_symbol                   Price Symbol
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_price_stub    (p_user                          IN prices.created_by%TYPE,
                                 p_effective_start_timestamp     IN prices.effective_start_timestamp%TYPE,
                                 p_logical_load_timestamp        IN prices.logical_load_timestamp%TYPE,
                                 p_price_symbol                  IN prices.price_symbol%TYPE
                                 );
    -- ===================================================================================
    -- put_price_set
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a price collection
    --
    -- Notes:
    -- ------
    --
    --     Tables populated:
    --
    --     PRICES
    --     PRICES_H
    --     PRICES_ASK_OFFSETS
    --     PRICES_ASK_OFFSETS_H
    --     PRICES_ASK_VOLUMES
    --     PRICES_ASK_VOLUMES_H
    --     PRICES_BID_OFFSETS
    --     PRICES_BID_OFFSETS_H
    --     PRICES_BID_VOLUMES
    --     PRICES_BID_VOLUMES_H
    --     PRICES_RASK_PRICE_LEVELS
    --     PRICES_RASK_PRICE_LEVELS_H
    --     PRICES_RBID_PRICE_LEVELS
    --     PRICES_RBID_PRICE_LEVELS_H
    --     PRICES_RETAIL_OHLC
    --     PRICES_RETAIL_OHLC_H
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_prices                         Table of Prices for MM and NG
    --     p_price_ask_volumes              Table of Core Order Book Ask Volumes
    --     p_price_bid_volumes              Table of Core Order Book Bid Volumes
    --     p_price_rask_price_levels        Table of Core Order Book Retail Ask Price Levels
    --     p_price_rbid_price_levels        Table of Core Order Book Retail Bid Price Levels
    --     p_price_retail_ohlc              Table of Core Order Book Retail Open High Low Close Prices
    --     p_price_ask_offsets              Table of Ask Price Offsets, 5 buckets supported. First entry = SB, others prepared for partners
    --     p_price_bid_offsets              Table of Ask Price Offsets, 5 buckets supported. First entry = SB, others prepared for partners
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_prices_set    (p_user                          IN prices.created_by%TYPE,
                                p_effective_start_timestamp     IN prices.effective_start_timestamp%TYPE,
                                p_prices                        IN price_tab,
                                p_ask_volumes                   IN price_array_tab,
                                p_bid_volumes                   IN price_array_tab,
                                p_core_ask_prices               IN price_array_tab,
                                p_core_bid_prices               IN price_array_tab,
                                p_core_ohlc                     IN price_array_tab,
                                p_ask_offsets                   IN price_array_tab,
                                p_bid_offsets                   IN price_array_tab);

  -- ===================================================================================
  -- put_furai_latest_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a furai price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     furai_latest_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_furai_latest_prices            Table of Furai Latest Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
 PROCEDURE put_furai_latest_prices (p_user                          IN furai_latest_prices.created_by%TYPE,
                                    p_effective_start_timestamp     IN furai_latest_prices.effective_start_timestamp%TYPE,
                                    p_furai_latest_prices           IN furai_latest_price_tab);

  -- ===================================================================================
  -- put_latest_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     latest_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_latest_prices                  Table of Latest Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
 PROCEDURE put_latest_prices (p_user                          IN latest_prices.created_by%TYPE,
                              p_effective_start_timestamp     IN latest_prices.effective_start_timestamp%TYPE,
                              p_latest_prices                 IN latest_prices_tab);

  -- ===================================================================================
  -- put_anytime_prices
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price collection
  --
  -- Notes:
  -- ------
  --
  --     Tables populated:
  --
  --     anytime_prices
  --
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_anytime_prices                 Table of Anytime Prices
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
 PROCEDURE put_anytime_prices (p_user                          IN anytime_prices.created_by%TYPE,
                               p_effective_start_timestamp     IN anytime_prices.effective_start_timestamp%TYPE,
                               p_anytime_prices                IN anytime_prices_tab);

 PROCEDURE put_price_stream_prices (p_user                          IN price_stream_prices.created_by%TYPE,
                                    p_effective_start_timestamp     IN price_stream_prices.effective_start_timestamp%TYPE,
                                    p_price_stream_prices           IN anytime_prices_tab);

 PROCEDURE put_price_stream_anytime_prices (p_user                          IN price_stream_anytime_prices.created_by%TYPE,
                                            p_effective_start_timestamp     IN price_stream_anytime_prices.effective_start_timestamp%TYPE,
                                            p_price_stream_anytime_prices   IN anytime_prices_tab);

 PROCEDURE put_price_stream_latest_prices (p_user                          IN price_stream_latest_prices.created_by%TYPE,
                                           p_effective_start_timestamp     IN price_stream_latest_prices.effective_start_timestamp%TYPE,
                                           p_price_stream_latest_prices    IN anytime_prices_tab);

 PROCEDURE put_swap_points (p_user                          IN swap_points.created_by%TYPE,
                            p_effective_start_timestamp     IN swap_points.effective_start_timestamp%TYPE,
                            p_swap_points                   IN swap_points_tab);

 PROCEDURE put_anytime_swap_points (p_user                          IN anytime_swap_points.created_by%TYPE,
                                    p_effective_start_timestamp     IN anytime_swap_points.effective_start_timestamp%TYPE,
                                    p_anytime_swap_points           IN swap_points_tab);

 PROCEDURE put_price_option_contracts (p_user                          IN price_option_contracts.created_by%TYPE,
                                       p_effective_start_timestamp     IN price_option_contracts.effective_start_timestamp%TYPE,
                                       p_price_option_contracts        IN price_option_contracts_tab);

 PROCEDURE put_option_contract_anytime_prices (p_user                           IN option_contract_anytime_prices.created_by%TYPE,
                                               p_effective_start_timestamp      IN option_contract_anytime_prices.effective_start_timestamp%TYPE,
                                               p_option_contract_anytime_prices  IN price_option_contracts_tab);

END nrg_prices;
/