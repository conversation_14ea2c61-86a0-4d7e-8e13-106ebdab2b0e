CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_customer
AS
    -- ===================================================================================
    -- NRG_CUSTOMER
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the customers model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Customer Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     05/09/2011   Sanket Mittal      1.0    Creation
    --     24/10/2011   Manoj Kumar        1.1    Modified the put_customer method after removal of triggers
    --     08/11/2011   Manoj Kumar        1.2    Modified the package for writing data into history tables.
    --     25/11/2011   Sanket Mittal      1.3    Modified to add the new field trading_account_type on trading account customers link
    --     28/11/2011   Mark Gornicki      1.4    Added the parameter p_trading_account_type to the call of
    --                                            create_trading_account_stub, and other related fixes
    --     18/01/2012   Mark Gornicki      1.5    Moved trading_account stub creation to the start of the put_customer to minimise
    --                                            deadlock issues between trading account and customers
    --     24/01/2012   Mark Gornicki      1.6    Fix in 1.5 did not resolve the issue, where accounts/customers already existed
    --                                            the attempt to create stubs did not lock the rows when using the array version due to not exists clause
    --                                            Changing the array processing to call the single value stub procedure instead
    --     31/01/2012   Mark Gornicki      1.7    Added the function get_stubbed_ids
    --     02/02/2012   Manoj Kumar        1.8    Modified the insert for put_trading_account_ids as individual insert using for loop
    --     15/02/2012   Manoj Kumar        1.9    Included the Cardinality Hint
    --     27/07/2012   Sanket Mittal      2.0    Added new fields Assigned_agent_identity, Assigned_sales_trader_identity and bi_segmentation
    --     11/08/2012   Sanket Mittal      2.1    Added new fields Is_Potential_Premium, Trust_Type, Lead_Office
    --     25/06/2013   Prachi Shah        2.2    Added new field Assigned_Agent_Identity_Date
    --     22/07/2013   Prachi Shah        2.3    Added new fields last_remediation_date, next_remediation_date,next_remediation_alert_date
    --     19/09/2013   Ravi Shankar       2.4    Added logic to check if trdng_accnts_customers_link record already exists before inserting
    --     24/09/2013   Ravi Shankar       2.4    Added logic to include 'BLOCK_MARKETING' column in put_customer and put_history for customer table
    --     09/10/2013   Ravi Shankar       2.5    Included missed out logic to check if trdng_accnts_customers_link record already exists before inserting at line 655
    --     16/10/2013   Ravi Shankar       2.5    Removed referrence to the current seggregation flags and included logic to consume the newly added column is_customer_segregated
    --     17/10/2013   Ravi Shankar       2.5    Added put_referrer functionality and included logic to consume newly added column referrer_reference
    --     06/11/2013   Adam Krasnicki     2.6    Changed put_history procedure (direct upsert to TA_CUST_LINK_TA_FEED_H), and
    --     13/01/2013   Adam Krasnicki     2.6    p_full_remediation_required  parameter added to put_customer procedure
    --     04/03/2014   Adam Krasnicki     2.7    introducer_type, partner_id added to customer_refererrer[_h] tables
    --     09/05/2014   Adam Krasnicki     2.7    Some amendmends done to deal with negative customer_id (needed for EDW trading_account_dim)
    --     13/06/2014   Adam Krasnicki     2.7    Two parameters to put_customer added (fatca%)
    --     11/09/2014   Adam Krasnicki     2.7    Trading_account_id and trading_account_type added to customer registration
    --     08/10/2014   Adam Krasnicki     2.7    apprtns_assessment_date column added to customers table, put procedure changed
    --     22/07/2015   Adam Krasnicki     2.7    put_customer: 4 new parameters
    --     27/08/2015   Adam Krasnicki     2.7    visitor_id added to customer_registration_obj, put proc changed
    --     18/12/2015   Sanket Mittal      2.8    Additional Attributes added for customers - IS_BINARY_OPT_OUT, BINARY_OPT_OUT_STATUS_DATE, PERM_AGENT_IDENTITY, PERM_AGENT_IDENTITY_DATE
    --     03/03/2016   Sanket Mittal      2.9    Changed for BER-2388 - New columns added RECORD_SOURCE and SOURCE_CUSTOMER_ID
    --     17/06/2016   Sanket Mittal      3.0    Changed for BER-2673 - Integrate updated Introducer data contract - Knockouts - Added additional columns For customer referrer
    --     01/12/2016   Sanket Mittal      3.1    BER-3172 ODS - Integrate updated Customer registrations Data Contract
    --     05/01/2017   Sanket Mittal      3.2    BER-3243 ODS - Integrate updated Customer data contract
    --     26/01/2017   Sanket Mittal      3.3    BER-3334 ODS - CUSTOMER - Integrate updated customer data contract
    --     18/04/2017   Sanket Mittal      3.4    BER-3533 NRG_CUSTOMER - Data contract changes
    --     19/03/2018   Sakina Kinkhabwala 3.5    BER-4239 NRG_CUSTOMER -put_customer/p_customer_apprprtnss_info - add new attributes
    --     22/05/2018   Sakina Kinkhabwala 3.6    BER-4636 NRG_CUSTOMER -put_customer - add new attributes and revert changes for is_appropriate
    --     19/03/2019   Patrick Dinwiddy   3.7    BER-5010 call to put customers into TA_CUST_LINK_TA_FEED
    --     03/05/2019   Patrick Dinwiddy   3.8    JCS-10583 new German Tax objects
    --     11/07/2019   Patrick Dinwiddy   3.9    JCS-11149 new segregation and reg classification entities
	--     29/09/2019   Patrick Dinwiddy   4.0    JCS-11398 new approp attribute and JCS-11508 new opt-up entities
    --     22/10/2019   Patrick Dinwiddy   4.1    JCS-11724 new last trade attestation date attribute and new reg experience entities
	--     11/11/2019   Patrick Dinwiddy   4.2    JCS-11825 new is_deleted attribute on church tax rate
    --     05/12/2019   Patrick Dinwiddy   4.3    JCS-12050 new entity customer_email_preferences
    --     06/02/2020   Patrick Dinwiddy   4.4    JCS-12381 perf tuning for put customers
    --     27/02/2020   Patrick Dinwiddy   4.5    JCS-12374 German tax roll losses new entity
    -- ===================================================================================
    --
    -- ===================================================================================
    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================
    gc_version            CONSTANT VARCHAR2(3) := 4.5;
    gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
    gc_true               CONSTANT PLS_INTEGER := 1;
    gc_false              CONSTANT PLS_INTEGER := 0;
    gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');
    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the customer record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_customers_record          This is the old version of the customer record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_customers_record     customers%ROWTYPE,
                           p_effective_end_timestamp  customers_h.effective_end_timestamp%TYPE,
                           p_action                   customers_h.action%TYPE)
    IS
    BEGIN
         INSERT INTO customers_h (created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  customer_id,
                                  customer_version,
                                  customer_type,
                                  customer_sub_type,
                                  customer_status,
                                  start_date,
                                  is_politically_exposed,
                                  probability_of_default,
                                  risk_level,
                                  regulatory_classification,
                                  is_sales_trader_managed,
                                  is_do_not_contact,
                                  is_out_of_area,
                                  is_chargeback_requested,
                                  risk_level_change_reason,
                                  marketing_data_id,
                                  marketing_data_version,
                                  allow_email,
                                  allow_mail,
                                  allow_telephone,
                                  allow_sms,
                                  tax_profile_id,
                                  tax_profile_version,
                                  is_tax_exempt,
                                  tax_residency_code,
                                  withholding_tax_rate,
                                  withholding_tax_exemption_code,
                                  --appropriateness_id,
                                  --is_appropriate,
                                  --appropriateness_reason,
                                  --agreed_to_proceed,
                                  --financial_suitability_score,
                                  --knowledge_experience_score,
                                  person_id,
                                  company_id,
                                  is_deleted,
                                  assigned_agent_identity,
                                  assigned_sales_trader_identity,
                                  bi_segmentation,
                                  assigned_agent_identity_date,
                                  last_remediation_date,
                                  next_remediation_date,
                                  next_remediation_alert_date,
                                  online_declaration
                                  ,block_marketing
                                  ,is_customer_segregated
                                  ,referrer_reference
                                  ,full_remediation_required
                                  ,fatca_status
                                  ,fatca_assessment_date
                                  --,apprtns_assessment_date
                                  ,remediation_enabled
                                  ,market_counterparty_partner_id
                                  ,speedbet_opt_out
                                  ,speedbet_opt_out_status_date
                                  ,is_binaries_opt_out
                                  ,binaries_opt_out_status_date
                                  ,perm_agent_identity
                                  ,perm_agent_identity_date
                                  ,record_source
                                  ,source_customer_id
                                  ,is_cfd_provider
                                  ,lead_profile_country
                                  ,is_profiling_allowed
                                  ,lifecycle_status
                                  ,last_trade_attestation_date)
                              VALUES
                              (
                                  p_old_customers_record.created_by,
                                  p_old_customers_record.create_timestamp,
                                  p_old_customers_record.updated_by,
                                  p_old_customers_record.update_timestamp,
                                  p_old_customers_record.logical_load_timestamp,
                                  p_old_customers_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_customers_record.customer_id,
                                  p_old_customers_record.customer_version,
                                  p_old_customers_record.customer_type,
                                  p_old_customers_record.customer_sub_type,
                                  p_old_customers_record.customer_status,
                                  p_old_customers_record.start_date,
                                  p_old_customers_record.is_politically_exposed,
                                  p_old_customers_record.probability_of_default,
                                  p_old_customers_record.risk_level,
                                  p_old_customers_record.regulatory_classification,
                                  p_old_customers_record.is_sales_trader_managed,
                                  p_old_customers_record.is_do_not_contact,
                                  p_old_customers_record.is_out_of_area,
                                  p_old_customers_record.is_chargeback_requested,
                                  p_old_customers_record.risk_level_change_reason,
                                  p_old_customers_record.marketing_data_id,
                                  p_old_customers_record.marketing_data_version,
                                  p_old_customers_record.allow_email,
                                  p_old_customers_record.allow_mail,
                                  p_old_customers_record.allow_telephone,
                                  p_old_customers_record.allow_sms,
                                  p_old_customers_record.tax_profile_id,
                                  p_old_customers_record.tax_profile_version,
                                  p_old_customers_record.is_tax_exempt,
                                  p_old_customers_record.tax_residency_code,
                                  p_old_customers_record.withholding_tax_rate,
                                  p_old_customers_record.withholding_tax_exemption_code,
                                  --p_old_customers_record.appropriateness_id,
                                  --p_old_customers_record.is_appropriate,
                                  --p_old_customers_record.appropriateness_reason,
                                  --p_old_customers_record.agreed_to_proceed,
                                  --p_old_customers_record.financial_suitability_score,
                                  --p_old_customers_record.knowledge_experience_score,
                                  p_old_customers_record.person_id,
                                  p_old_customers_record.company_id,
                                  p_old_customers_record.is_deleted,
                                  p_old_customers_record.assigned_agent_identity,
                                  p_old_customers_record.assigned_sales_trader_identity,
                                  p_old_customers_record.bi_segmentation,
                                  p_old_customers_record.assigned_agent_identity_date,
                                  p_old_customers_record.last_remediation_date,
                                  p_old_customers_record.next_remediation_date,
                                  p_old_customers_record.next_remediation_alert_date,
                                  p_old_customers_record.online_declaration
                                  ,p_old_customers_record.block_marketing
                                  ,p_old_customers_record.is_customer_segregated
                                  ,p_old_customers_record.referrer_reference
                                  ,p_old_customers_record.Full_Remediation_Required
                                  ,p_old_customers_record.fatca_status
                                  ,p_old_customers_record.fatca_assessment_date
                                  --,p_old_customers_record.apprtns_assessment_date
                                  ,p_old_customers_record.remediation_enabled
                                  ,p_old_customers_record.market_counterparty_partner_id
                                  ,p_old_customers_record.speedbet_opt_out
                                  ,p_old_customers_record.speedbet_opt_out_status_date
                                  ,p_old_customers_record.is_binaries_opt_out
                                  ,p_old_customers_record.binaries_opt_out_status_date
                                  ,p_old_customers_record.perm_agent_identity
                                  ,p_old_customers_record.perm_agent_identity_date
                                  ,p_old_customers_record.record_source
                                  ,p_old_customers_record.source_customer_id
                                  ,p_old_customers_record.is_cfd_provider
                                  ,p_old_customers_record.lead_profile_country
                                  ,p_old_customers_record.is_profiling_allowed
                                  ,p_old_customers_record.lifecycle_status
                                  ,p_old_customers_record.last_trade_attestation_date);
    EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
            UPDATE customers_h
                SET updated_by                    = p_old_customers_record.updated_by,
                    action                        = p_action,
                    action_timestamp              = SYSTIMESTAMP,
                    update_timestamp              = p_old_customers_record.update_timestamp,
                    logical_load_timestamp        = p_old_customers_record.logical_load_timestamp,
                    customer_version              = p_old_customers_record.customer_version,
                    customer_type                 = p_old_customers_record.customer_type,
                    customer_sub_type             = p_old_customers_record.customer_sub_type,
                    customer_status               = p_old_customers_record.customer_status,
                    start_date                    = p_old_customers_record.start_date,
                    is_politically_exposed        = p_old_customers_record.is_politically_exposed,
                    probability_of_default        = p_old_customers_record.probability_of_default,
                    risk_level                    = p_old_customers_record.risk_level,
                    regulatory_classification     = p_old_customers_record.regulatory_classification,
                    is_sales_trader_managed       = p_old_customers_record.is_sales_trader_managed,
                    is_do_not_contact             = p_old_customers_record.is_do_not_contact,
                    is_out_of_area                = p_old_customers_record.is_out_of_area,
                    is_chargeback_requested       = p_old_customers_record.is_chargeback_requested,
                    risk_level_change_reason      = p_old_customers_record.risk_level_change_reason,
                    marketing_data_id             = p_old_customers_record.marketing_data_id,
                    marketing_data_version        = p_old_customers_record.marketing_data_version,
                    allow_email                   = p_old_customers_record.allow_email,
                    allow_mail                    = p_old_customers_record.allow_mail,
                    allow_telephone               = p_old_customers_record.allow_telephone,
                    allow_sms                     = p_old_customers_record.allow_sms,
                    tax_profile_id                = p_old_customers_record.tax_profile_id,
                    tax_profile_version           = p_old_customers_record.tax_profile_version,
                    is_tax_exempt                 = p_old_customers_record.is_tax_exempt,
                    tax_residency_code            = p_old_customers_record.tax_residency_code,
                    withholding_tax_rate          = p_old_customers_record.withholding_tax_rate,
                    withholding_tax_exemption_code= p_old_customers_record.withholding_tax_exemption_code,
                    --appropriateness_id            = p_old_customers_record.appropriateness_id,
                    --is_appropriate                = p_old_customers_record.is_appropriate,
                    --appropriateness_reason        = p_old_customers_record.appropriateness_reason,
                    --agreed_to_proceed             = p_old_customers_record.agreed_to_proceed,
                    --financial_suitability_score   = p_old_customers_record.financial_suitability_score,
                    --knowledge_experience_score    = p_old_customers_record.knowledge_experience_score,
                    person_id                     = p_old_customers_record.person_id,
                    company_id                    = p_old_customers_record.company_id,
                    is_deleted                    = p_old_customers_record.is_deleted,
                    assigned_agent_identity       = p_old_customers_record.assigned_agent_identity,
                    assigned_sales_trader_identity= p_old_customers_record.assigned_sales_trader_identity,
                    bi_segmentation               = p_old_customers_record.bi_segmentation,
                    assigned_agent_identity_date  = p_old_customers_record.assigned_agent_identity_date,
                    last_remediation_date         = p_old_customers_record.last_remediation_date,
                    next_remediation_date         = p_old_customers_record.next_remediation_date,
                    next_remediation_alert_date   = p_old_customers_record.next_remediation_alert_date,
                    online_declaration            = p_old_customers_record.online_declaration
                    ,block_marketing              = p_old_customers_record.block_marketing
                    ,is_customer_segregated       = p_old_customers_record.is_customer_segregated
                    ,referrer_reference           = p_old_customers_record.referrer_reference
                    ,Full_Remediation_Required    = p_old_customers_record.Full_Remediation_Required
                    ,fatca_status                 = p_old_customers_record.Fatca_Status
                    ,Fatca_Assessment_Date        = p_old_customers_record.Fatca_Assessment_Date
                    --,apprtns_assessment_date      = p_old_customers_record.apprtns_assessment_date
                    ,remediation_enabled          = p_old_customers_record.remediation_enabled
                    ,market_counterparty_partner_id = p_old_customers_record.market_counterparty_partner_id
                    ,speedbet_opt_out             = p_old_customers_record.speedbet_opt_out
                    ,speedbet_opt_out_status_date = p_old_customers_record.speedbet_opt_out_status_date
                    ,is_binaries_opt_out          = p_old_customers_record.is_binaries_opt_out
                    ,binaries_opt_out_status_date = p_old_customers_record.binaries_opt_out_status_date
                    ,perm_agent_identity          = p_old_customers_record.perm_agent_identity
                    ,perm_agent_identity_date     = p_old_customers_record.perm_agent_identity_date
                    ,record_source                = p_old_customers_record.record_source
                    ,source_customer_id           = p_old_customers_record.source_customer_id
                    ,is_cfd_provider              = p_old_customers_record.is_cfd_provider
                    ,lead_profile_country         = p_old_customers_record.lead_profile_country
                    ,is_profiling_allowed         = p_old_customers_record.is_profiling_allowed
                    ,lifecycle_status             = p_old_customers_record.lifecycle_status
                    ,last_trade_attestation_date  = p_old_customers_record.last_trade_attestation_date
              WHERE customer_id                   = p_old_customers_record.customer_id AND
                    effective_start_timestamp     = p_old_customers_record.effective_start_timestamp AND
                    (/* only where there have been for data changes */
                    nrg_common.has_value_changed(customer_version,p_old_customers_record.customer_version) = 1 OR
                    nrg_common.has_value_changed(customer_type,p_old_customers_record.customer_type) = 1 OR
                    nrg_common.has_value_changed(customer_sub_type,p_old_customers_record.customer_sub_type) = 1 OR
                    nrg_common.has_value_changed(customer_status,p_old_customers_record.customer_status) = 1 OR
                    nrg_common.has_value_changed(start_date,p_old_customers_record.start_date) = 1 OR
                    nrg_common.has_value_changed(is_politically_exposed,p_old_customers_record.is_politically_exposed) = 1 OR
                    nrg_common.has_value_changed(probability_of_default,p_old_customers_record.probability_of_default) = 1 OR
                    nrg_common.has_value_changed(risk_level,p_old_customers_record.risk_level) = 1 OR
                    nrg_common.has_value_changed(regulatory_classification,p_old_customers_record.regulatory_classification) = 1 OR
                    nrg_common.has_value_changed(is_sales_trader_managed,p_old_customers_record.is_sales_trader_managed) = 1 OR
                    nrg_common.has_value_changed(is_do_not_contact,p_old_customers_record.is_do_not_contact) = 1 OR
                    nrg_common.has_value_changed(is_out_of_area,p_old_customers_record.is_out_of_area) = 1 OR
                    nrg_common.has_value_changed(is_chargeback_requested,p_old_customers_record.is_chargeback_requested) = 1 OR
                    nrg_common.has_value_changed(risk_level_change_reason,p_old_customers_record.risk_level_change_reason) = 1 OR
                    nrg_common.has_value_changed(marketing_data_id,p_old_customers_record.marketing_data_id) = 1 OR
                    nrg_common.has_value_changed(marketing_data_version,p_old_customers_record.marketing_data_version) = 1 OR
                    nrg_common.has_value_changed(allow_email,p_old_customers_record.allow_email) = 1 OR
                    nrg_common.has_value_changed(allow_mail,p_old_customers_record.allow_mail) = 1 OR
                    nrg_common.has_value_changed(allow_telephone,p_old_customers_record.allow_telephone) = 1 OR
                    nrg_common.has_value_changed(allow_sms,p_old_customers_record.allow_sms) = 1 OR
                    nrg_common.has_value_changed(tax_profile_id,p_old_customers_record.tax_profile_id) = 1 OR
                    nrg_common.has_value_changed(tax_profile_version,p_old_customers_record.tax_profile_version) = 1 OR
                    nrg_common.has_value_changed(is_tax_exempt,p_old_customers_record.is_tax_exempt) = 1 OR
                    nrg_common.has_value_changed(tax_residency_code,p_old_customers_record.tax_residency_code) = 1 OR
                    nrg_common.has_value_changed(withholding_tax_rate,p_old_customers_record.withholding_tax_rate) = 1 OR
                    nrg_common.has_value_changed(withholding_tax_exemption_code,p_old_customers_record.withholding_tax_exemption_code) = 1 OR
                    --nrg_common.has_value_changed(appropriateness_id,p_old_customers_record.appropriateness_id) = 1 OR
                    --nrg_common.has_value_changed(is_appropriate,p_old_customers_record.is_appropriate) = 1 OR
                    --nrg_common.has_value_changed(appropriateness_reason,p_old_customers_record.appropriateness_reason) = 1 OR
                    --nrg_common.has_value_changed(agreed_to_proceed,p_old_customers_record.agreed_to_proceed) = 1 OR
                    --nrg_common.has_value_changed(financial_suitability_score,p_old_customers_record.financial_suitability_score) = 1 OR
                    --nrg_common.has_value_changed(knowledge_experience_score,p_old_customers_record.knowledge_experience_score) = 1 OR
                    nrg_common.has_value_changed(person_id,p_old_customers_record.person_id) = 1 OR
                    nrg_common.has_value_changed(company_id,p_old_customers_record.company_id) = 1 OR
                    nrg_common.has_value_changed(is_deleted,p_old_customers_record.is_deleted) = 1 OR
                    nrg_common.has_value_changed(assigned_agent_identity,p_old_customers_record.assigned_agent_identity) = 1 OR
                    nrg_common.has_value_changed(assigned_sales_trader_identity,p_old_customers_record.assigned_sales_trader_identity) = 1 OR
                    nrg_common.has_value_changed(bi_segmentation,p_old_customers_record.bi_segmentation) = 1 OR
                    nrg_common.has_value_changed(assigned_agent_identity_date,p_old_customers_record.assigned_agent_identity_date) = 1 OR
                    nrg_common.has_value_changed(last_remediation_date,p_old_customers_record.last_remediation_date) =1 OR
                    nrg_common.has_value_changed(next_remediation_date,p_old_customers_record.next_remediation_date) = 1 OR
                    nrg_common.has_value_changed(next_remediation_alert_date, p_old_customers_record.next_remediation_alert_date)= 1 OR
                    nrg_common.has_value_changed(block_marketing, p_old_customers_record.block_marketing)= 1 OR
                    nrg_common.has_value_changed(is_customer_segregated, p_old_customers_record.is_customer_segregated)= 1 OR
                    nrg_common.has_value_changed(referrer_reference, p_old_customers_record.referrer_reference)= 1 OR
                    nrg_common.has_value_changed(online_declaration, p_old_customers_record.online_declaration) = 1 OR
                    nrg_common.has_value_changed(Full_Remediation_Required, p_old_customers_record.Full_Remediation_Required) = 1 OR
                    nrg_common.has_value_changed(fatca_status, p_old_customers_record.fatca_status) = 1 OR
                    nrg_common.has_value_changed(Fatca_Assessment_Date, p_old_customers_record.Fatca_Assessment_Date) = 1 OR
                    --nrg_common.has_value_changed(apprtns_assessment_date, p_old_customers_record.apprtns_assessment_date) = 1 OR
                    nrg_common.has_value_changed(remediation_enabled, p_old_customers_record.remediation_enabled) = 1 OR
                    nrg_common.has_value_changed(market_counterparty_partner_id, p_old_customers_record.market_counterparty_partner_id) = 1 OR
                    nrg_common.has_value_changed(speedbet_opt_out, p_old_customers_record.speedbet_opt_out) = 1 OR
                    nrg_common.has_value_changed(speedbet_opt_out_status_date, p_old_customers_record.speedbet_opt_out_status_date) = 1 OR
                    nrg_common.has_value_changed(is_binaries_opt_out, p_old_customers_record.is_binaries_opt_out) = 1 OR
                    nrg_common.has_value_changed(binaries_opt_out_status_date, p_old_customers_record.binaries_opt_out_status_date) = 1 OR
                    nrg_common.has_value_changed(perm_agent_identity, p_old_customers_record.perm_agent_identity) = 1 OR
                    nrg_common.has_value_changed(perm_agent_identity_date, p_old_customers_record.perm_agent_identity_date) = 1 OR
                    nrg_common.has_value_changed(record_source, p_old_customers_record.record_source) = 1 OR
                    nrg_common.has_value_changed(source_customer_id, p_old_customers_record.source_customer_id) = 1 OR
                    nrg_common.has_value_changed(is_cfd_provider, p_old_customers_record.is_cfd_provider) = 1 OR
                    nrg_common.has_value_changed(lead_profile_country, p_old_customers_record.lead_profile_country) = 1 OR
                    nrg_common.has_value_changed(is_profiling_allowed, p_old_customers_record.is_profiling_allowed) = 1 OR
                    nrg_common.has_value_changed(lifecycle_status, p_old_customers_record.lifecycle_status) = 1 OR
                    nrg_common.has_value_changed(last_trade_attestation_date, p_old_customers_record.last_trade_attestation_date) = 1
      );
    END put_history;
  --
  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer appropriateness infos
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_apprprtnss_infos_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_apprprtnss_infos_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_apprprtnss_infos_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_apprprtnss_infos%ROWTYPE) IS
  BEGIN
    INSERT INTO customer_apprprtnss_infos_h(customer_id,
                                            appropriateness_id,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            effective_end_timestamp,
                                            action,
                                            action_timestamp,
                                            is_appropriate,
                                            reason,
                                            is_agreed_to_proceed,
                                            financial_suitability_score,
                                            knowledge_and_experience_score,
                                            assessment_date,
                                            is_reassessment,
                                            appropriateness,
                                            passed_knowledge_assessment,
                                            legal_entity,
                                            reference)
                                    VALUES (p_old_record.customer_id,
                                            p_old_record.appropriateness_id,
                                            p_old_record.logical_load_timestamp,
                                            p_old_record.created_by,
                                            p_old_record.create_timestamp,
                                            p_old_record.updated_by,
                                            p_old_record.update_timestamp,
                                            p_old_record.effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_record.is_appropriate,
                                            p_old_record.reason,
                                            p_old_record.is_agreed_to_proceed,
                                            p_old_record.financial_suitability_score,
                                            p_old_record.knowledge_and_experience_score,
                                            p_old_record.assessment_date,
                                            p_old_record.is_reassessment,
                                            p_old_record.appropriateness,
                                            p_old_record.passed_knowledge_assessment,
                                            p_old_record.legal_entity,
                                            p_old_record.reference);

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE customer_apprprtnss_infos_h
      SET logical_load_timestamp = p_old_record.logical_load_timestamp,
          created_by = p_old_record.created_by,
          create_timestamp = p_old_record.create_timestamp,
          updated_by = p_old_record.updated_by,
          update_timestamp = p_old_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          is_appropriate = p_old_record.is_appropriate,
          reason = p_old_record.reason,
          is_agreed_to_proceed = p_old_record.is_agreed_to_proceed,
          financial_suitability_score = p_old_record.financial_suitability_score,
          knowledge_and_experience_score = p_old_record.knowledge_and_experience_score,
          assessment_date = p_old_record.assessment_date,
          is_reassessment = p_old_record.is_reassessment,
          appropriateness = p_old_record.appropriateness,
          passed_knowledge_assessment = p_old_record.passed_knowledge_assessment,
          legal_entity = p_old_record.legal_entity,
          reference = p_old_record.reference
    WHERE customer_id = p_old_record.customer_id AND
          appropriateness_id = p_old_record.appropriateness_id AND
          effective_start_timestamp = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer_segregations
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_tax_germany_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history_seg(p_action                    IN customer_segregations_h.action%TYPE,
                            p_effective_end_timestamp   IN customer_segregations_h.effective_end_timestamp%TYPE,
                            p_old_record                IN customer_segregations%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_segregations_h (segregation_id,
                                         customer_id,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         legal_entity,
                                         is_segregated)
                                 VALUES (p_old_record.segregation_id,
                                         p_old_record.customer_id,
                                         p_old_record.logical_load_timestamp,
                                         p_old_record.created_by,
                                         p_old_record.create_timestamp,
                                         p_old_record.updated_by,
                                         p_old_record.update_timestamp,
                                         p_old_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_record.legal_entity,
                                         p_old_record.is_segregated);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_segregations_h
       SET logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           legal_entity                    = p_old_record.legal_entity,
           is_segregated                   = p_old_record.is_segregated
     WHERE customer_id                     = p_old_record.customer_id AND
           segregation_id                  = p_old_record.segregation_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer_reg_clsfctns
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_tax_germany_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_reg_clsfctns_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_reg_clsfctns_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_reg_clsfctns%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_reg_clsfctns_h (regulatory_classification_id,
                                         customer_id,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         legal_entity,
                                         regulatory_classification)
                                 VALUES (p_old_record.regulatory_classification_id,
                                         p_old_record.customer_id,
                                         p_old_record.logical_load_timestamp,
                                         p_old_record.created_by,
                                         p_old_record.create_timestamp,
                                         p_old_record.updated_by,
                                         p_old_record.update_timestamp,
                                         p_old_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_record.legal_entity,
                                         p_old_record.regulatory_classification);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_reg_clsfctns_h
       SET logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           legal_entity                    = p_old_record.legal_entity,
           regulatory_classification                   = p_old_record.regulatory_classification
     WHERE customer_id                     = p_old_record.customer_id AND
           regulatory_classification_id                  = p_old_record.regulatory_classification_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer_pro_opt_up
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_tax_germany_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_pro_opt_up_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_pro_opt_up_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_pro_opt_up%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_pro_opt_up_h (pro_opt_up_id,
                                       customer_id,
                                       legal_entity,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       effective_end_timestamp,
                                       action,
                                       action_timestamp,
                                       application_date,
                                       is_reapplication,
                                       decision_date,
                                       decision,
                                       reference,
                                       route)
                               VALUES (p_old_record.pro_opt_up_id,
                                       p_old_record.customer_id,
                                       p_old_record.legal_entity,
                                       p_old_record.logical_load_timestamp,
                                       p_old_record.created_by,
                                       p_old_record.create_timestamp,
                                       p_old_record.updated_by,
                                       p_old_record.update_timestamp,
                                       p_old_record.effective_start_timestamp,
                                       p_effective_end_timestamp,
                                       p_action,
                                       SYSTIMESTAMP,
                                       p_old_record.application_date,
                                       p_old_record.is_reapplication,
                                       p_old_record.decision_date,
                                       p_old_record.decision,
                                       p_old_record.reference,
                                       p_old_record.route);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_pro_opt_up_h
       SET legal_entity                    = p_old_record.legal_entity,
           logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           application_date                = p_old_record.application_date,
           is_reapplication                = p_old_record.is_reapplication,
           decision_date                   = p_old_record.decision_date,
           decision                        = p_old_record.decision,
           reference                       = p_old_record.reference,
           route                           = p_old_record.route
     WHERE customer_id                     = p_old_record.customer_id AND
           pro_opt_up_id                   = p_old_record.pro_opt_up_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer_reg_experience
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_reg_experience_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_reg_experience_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_reg_experience_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_reg_experience%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_reg_experience_h (regulatory_experience_id,
                                           customer_id,
                                           legal_entity,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           assessment_date,
                                           expiry_date,
                                           reg_experience_status)
                                   VALUES (p_old_record.regulatory_experience_id,
                                           p_old_record.customer_id,
                                           p_old_record.legal_entity,
                                           p_old_record.logical_load_timestamp,
                                           p_old_record.created_by,
                                           p_old_record.create_timestamp,
                                           p_old_record.updated_by,
                                           p_old_record.update_timestamp,
                                           p_old_record.effective_start_timestamp,
                                           p_effective_end_timestamp,
                                           p_action,
                                           SYSTIMESTAMP,
                                           p_old_record.assessment_date,
                                           p_old_record.expiry_date,
                                           p_old_record.reg_experience_status);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_reg_experience_h
       SET legal_entity                    = p_old_record.legal_entity,
           logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           assessment_date                 = p_old_record.assessment_date,
           expiry_date                     = p_old_record.expiry_date,
           reg_experience_status           = p_old_record.reg_experience_status
     WHERE customer_id                     = p_old_record.customer_id AND
           regulatory_experience_id        = p_old_record.regulatory_experience_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  --
  --
  --
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the tax declarations
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for partner_tax_declarations
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_tax_declarations_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_tax_declarations_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_tax_declarations%ROWTYPE) IS
  BEGIN
    INSERT INTO customer_tax_declarations_h(customer_id,
                                           legal_entity,
                                           reporting_country,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           declaration_form_name,
                                           country_of_residence,
                                           signature_date,
                                           is_valid,
                                           --is_exempt,
                                           is_opt_out,
                                           withholding_tax_agree_type)
                                    VALUES (p_old_record.customer_id,
                                            p_old_record.legal_entity,
                                            p_old_record.reporting_country,
                                            p_old_record.logical_load_timestamp,
                                            p_old_record.created_by,
                                            p_old_record.create_timestamp,
                                            p_old_record.updated_by,
                                            p_old_record.update_timestamp,
                                            p_old_record.effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_record.declaration_form_name,
                                            p_old_record.country_of_residence,
                                            p_old_record.signature_date,
                                            p_old_record.is_valid,
                                            --p_old_record.is_exempt,
                                            p_old_record.is_opt_out,
                                            p_old_record.withholding_tax_agree_type);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE customer_tax_declarations_h
      SET
          logical_load_timestamp = p_old_record.logical_load_timestamp,
          created_by = p_old_record.created_by,
          create_timestamp = p_old_record.create_timestamp,
          updated_by = p_old_record.updated_by,
          update_timestamp = p_old_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          declaration_form_name = p_old_record.declaration_form_name,
          country_of_residence = p_old_record.country_of_residence,
          signature_date = p_old_record.signature_date,
          is_valid = p_old_record.is_valid,
          --is_exempt = p_old_record.is_exempt,
          is_opt_out = p_old_record.is_opt_out,
          withholding_tax_agree_type = p_old_record.withholding_tax_agree_type
      WHERE customer_id = p_old_record.customer_id AND
            legal_entity = p_old_record.legal_entity AND
            reporting_country = p_old_record.reporting_country AND
            effective_start_timestamp = p_old_record.effective_start_timestamp;
  END;
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the Customer Registration records
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_cust_reg                  This is the old version of the Customer Registration record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_cust_reg             customer_registrations%ROWTYPE,
                           p_effective_end_timestamp  customer_registrations_h.effective_end_timestamp%TYPE,
                           p_action                   customer_registrations_h.action%TYPE)
    IS
    BEGIN
      INSERT INTO customer_registrations_h
                                (
                                    created_by,
                                    create_timestamp,
                                    updated_by,
                                    update_timestamp,
                                    logical_load_timestamp,
                                    effective_start_timestamp,
                                    effective_end_timestamp,
                                    action,
                                    action_timestamp,
                                    customer_id,
                                    registration_id,
                                    registration_version,
                                    sales_channel,
                                    application_file_reference,
                                    ip_address,
                                    device_id,
                                    application_channel,
                                    trading_account_id,
                                    trading_account_type,
                                    visitor_id,
                                    straight_through_processing,
                                    click_id,
                                    is_metatrader_2fa_requested
                                    )
                              VALUES
                                    (
                                        p_old_cust_reg.created_by,
                                        p_old_cust_reg.create_timestamp,
                                        p_old_cust_reg.updated_by,
                                        p_old_cust_reg.update_timestamp,
                                        p_old_cust_reg.logical_load_timestamp,
                                        p_old_cust_reg.effective_start_timestamp,
                                        p_effective_end_timestamp,
                                        p_action,
                                        SYSTIMESTAMP,
                                        p_old_cust_reg.customer_id,
                                        p_old_cust_reg.registration_id,
                                        p_old_cust_reg.registration_version,
                                        p_old_cust_reg.sales_channel,
                                        p_old_cust_reg.application_file_reference,
                                        p_old_cust_reg.ip_address,
                                        p_old_cust_reg.device_id,
                                        p_old_cust_reg.application_channel,
                                        p_old_cust_reg.trading_account_id,
                                        p_old_cust_reg.Trading_Account_Type,
                                        p_old_cust_reg.visitor_id,
                                        p_old_cust_reg.straight_through_processing,
                                        p_old_cust_reg.click_id,
                                        p_old_cust_reg.is_metatrader_2fa_requested
                                    );
    EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
            UPDATE customer_registrations_h
                SET updated_by                  = p_old_cust_reg.updated_by,
                    action                      = p_action,
                    action_timestamp            = SYSTIMESTAMP,
                    update_timestamp            = p_old_cust_reg.update_timestamp,
                    logical_load_timestamp      = p_old_cust_reg.logical_load_timestamp,
                    registration_version        = p_old_cust_reg.registration_version,
                    sales_channel               = p_old_cust_reg.sales_channel,
                    application_file_reference  = p_old_cust_reg.application_file_reference,
                    ip_address                  = p_old_cust_reg.ip_address,
                    device_id                   = p_old_cust_reg.device_id,
                    application_channel         = p_old_cust_reg.application_channel,
                    trading_account_id          = p_old_cust_reg.Trading_Account_Id,
                    trading_account_type        = p_old_cust_reg.Trading_Account_type,
                    visitor_id                  = p_old_cust_reg.Visitor_Id ,
                    straight_through_processing = p_old_cust_reg.straight_through_processing,
                    click_id                    = p_old_cust_reg.click_id,
                    is_metatrader_2fa_requested = p_old_cust_reg.is_metatrader_2fa_requested
              WHERE registration_id             = p_old_cust_reg.registration_id AND
                    effective_start_timestamp   = p_old_cust_reg.effective_start_timestamp AND
                    (/* only where there have been for data changes */
                    nrg_common.has_value_changed(registration_version , p_old_cust_reg.registration_version) = 1 OR
                    nrg_common.has_value_changed(sales_channel , p_old_cust_reg.sales_channel) = 1 OR
                    nrg_common.has_value_changed(application_file_reference , p_old_cust_reg.application_file_reference) = 1 OR
                    nrg_common.has_value_changed(ip_address , p_old_cust_reg.ip_address) = 1 OR
                    nrg_common.has_value_changed(device_id , p_old_cust_reg.device_id) = 1 OR
                    nrg_common.has_value_changed(application_channel , p_old_cust_reg.application_channel) = 1 OR
                    nrg_common.has_value_changed(trading_account_id , p_old_cust_reg.trading_account_id) = 1 OR
                    nrg_common.has_value_changed(trading_account_type , p_old_cust_reg.trading_account_type) = 1 OR
                    nrg_common.has_value_changed(visitor_id , p_old_cust_reg.visitor_id) = 1 OR
                    nrg_common.has_value_changed(straight_through_processing, p_old_cust_reg.straight_through_processing) = 1 OR
                    nrg_common.has_value_changed(click_id, p_old_cust_reg.click_id) = 1 OR
                    nrg_common.has_value_changed(is_metatrader_2fa_requested, p_old_cust_reg.is_metatrader_2fa_requested) = 1
                  );
    END put_history;
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the customer trading account link records
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_cust_trad                 This is the old version of the customer trading account link
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_cust_trad            TA_CUST_LINK_CUST_FEED%ROWTYPE,
                           p_effective_end_timestamp  TA_CUST_LINK_CUST_FEED_H.effective_end_timestamp%TYPE,
                           p_action                   TA_CUST_LINK_CUST_FEED_H.action%TYPE)
    IS
    BEGIN
      INSERT INTO TA_CUST_LINK_CUST_FEED_H
                                    (
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        logical_load_timestamp,
                                        effective_start_timestamp,
                                        effective_end_timestamp,
                                        action,
                                        action_timestamp,
                                        customer_id,
                                        trading_account_id,
                                        trading_account_type
                                        )
                              VALUES
                                    (
                                        p_old_cust_trad.created_by,
                                        p_old_cust_trad.create_timestamp,
                                        p_old_cust_trad.updated_by,
                                        p_old_cust_trad.update_timestamp,
                                        p_old_cust_trad.logical_load_timestamp,
                                        p_old_cust_trad.effective_start_timestamp,
                                        p_effective_end_timestamp,
                                        p_action,
                                        SYSTIMESTAMP,
                                        p_old_cust_trad.customer_id,
                                        p_old_cust_trad.trading_account_id,
                                        p_old_cust_trad.trading_account_type
                                    );
    EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          UPDATE TA_CUST_LINK_CUST_FEED_H
          SET    updated_by               = p_old_cust_trad.updated_by,
                 update_timestamp         = p_old_cust_trad.update_timestamp,
                 logical_load_timestamp   = p_old_cust_trad.logical_load_timestamp,
                 effective_end_timestamp  = p_effective_end_timestamp,
                 action                   = p_action,
                 action_timestamp         = SYSTIMESTAMP
          WHERE trading_account_id        = p_old_cust_trad.trading_account_id AND
                trading_account_type      = p_old_cust_trad.trading_account_type AND
                customer_id               = p_old_cust_trad.customer_id AND
                effective_start_timestamp = p_old_cust_trad.effective_start_timestamp;
    END  put_history;
    /***** Begin Modifiction for V2.5 - BER671 *****/
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to write the history table for all the old versions of the customer record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_customer_referrer_rec     This is the old version of the customer referrer record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_customer_referrer_rec  customer_referrer%ROWTYPE,
                           p_effective_end_timestamp    customer_referrer_h.effective_end_timestamp%TYPE,
                           p_action                     customer_referrer_h.action%TYPE)
    IS
    BEGIN
      INSERT INTO customer_referrer_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  id,
                                  referrer_reference,
                                  notes,
                                  description,
                                  introducer_type,
                                  partner_id,
                                  is_deleted,
                                  assigned_agent_identity
                               )
                              VALUES
                              (
                                  p_old_customer_referrer_rec.created_by,
                                  p_old_customer_referrer_rec.create_timestamp,
                                  p_old_customer_referrer_rec.updated_by,
                                  p_old_customer_referrer_rec.update_timestamp,
                                  p_old_customer_referrer_rec.logical_load_timestamp,
                                  p_old_customer_referrer_rec.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_customer_referrer_rec.id,
                                  p_old_customer_referrer_rec.referrer_reference,
                                  p_old_customer_referrer_rec.notes,
                                  p_old_customer_referrer_rec.description,
                                  p_old_customer_referrer_rec.Introducer_Type,
                                  p_old_customer_referrer_rec.Partner_Id,
                                  p_old_customer_referrer_rec.is_deleted,
                                  p_old_customer_referrer_rec.assigned_agent_identity
                              );
    EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
            UPDATE customer_referrer_h
                SET updated_by                    = p_old_customer_referrer_rec.updated_by,
                    action                        = p_action,
                    action_timestamp              = SYSTIMESTAMP,
                    update_timestamp              = p_old_customer_referrer_rec.update_timestamp,
                    logical_load_timestamp        = p_old_customer_referrer_rec.logical_load_timestamp,
                    id                            = p_old_customer_referrer_rec.id,
                    notes                         = p_old_customer_referrer_rec.notes,
                    description                   = p_old_customer_referrer_rec.description,
                    introducer_type               = p_old_customer_referrer_rec.Introducer_Type,
                    partner_id                    = p_old_customer_referrer_rec.Partner_Id,
                    is_deleted                    = p_old_customer_referrer_rec.is_deleted,
                    assigned_agent_identity       = p_old_customer_referrer_rec.assigned_agent_identity
              WHERE referrer_reference            = p_old_customer_referrer_rec.referrer_reference AND
                    effective_start_timestamp     = p_old_customer_referrer_rec.effective_start_timestamp AND
                    (/* only where there have been for data changes */
                    nrg_common.has_value_changed(id,p_old_customer_referrer_rec.id) = 1 OR
                    nrg_common.has_value_changed(notes,p_old_customer_referrer_rec.notes) = 1 OR
                    nrg_common.has_value_changed(description,p_old_customer_referrer_rec.description) = 1 OR
                    nrg_common.has_value_changed(introducer_type,p_old_customer_referrer_rec.introducer_type) = 1 OR
                    nrg_common.has_value_changed(partner_id,p_old_customer_referrer_rec.partner_id) = 1  OR
                    nrg_common.has_value_changed(is_deleted,p_old_customer_referrer_rec.is_deleted) = 1 OR
                    nrg_common.has_value_changed(assigned_agent_identity,p_old_customer_referrer_rec.assigned_agent_identity) = 1
                    );
    END put_history;
    /***** End Modifiction for V2.5 *****/
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer enquiry
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer enquiry
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_enquiries_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_enquiries_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_enquiries%ROWTYPE) IS
  BEGIN
    INSERT INTO customer_enquiries_h(enquiry_type,
                                    enquiry_date,
                                    customer_id,
                                    effective_start_timestamp,
                                    effective_end_timestamp,
                                    action,
                                    action_timestamp,
                                    logical_load_timestamp,
                                    created_by,
                                    create_timestamp,
                                    updated_by,
                                    update_timestamp,
                                    is_deleted)
                            VALUES (p_old_record.enquiry_type,
                                    p_old_record.enquiry_date,
                                    p_old_record.customer_id,
                                    p_old_record.effective_start_timestamp,
                                    p_effective_end_timestamp,
                                    p_action,
                                    SYSTIMESTAMP,
                                    p_old_record.logical_load_timestamp,
                                    p_old_record.created_by,
                                    p_old_record.create_timestamp,
                                    p_old_record.updated_by,
                                    p_old_record.update_timestamp,
                                    p_old_record.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE customer_enquiries_h
      SET
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          logical_load_timestamp = p_old_record.logical_load_timestamp,
          created_by = p_old_record.created_by,
          create_timestamp = p_old_record.create_timestamp,
          updated_by = p_old_record.updated_by,
          update_timestamp = p_old_record.update_timestamp,
          is_deleted = p_old_record.is_deleted
      WHERE enquiry_type = p_old_record.enquiry_type AND
            enquiry_date = p_old_record.enquiry_date AND
            customer_id = p_old_record.customer_id AND
            effective_start_timestamp = p_old_record.effective_start_timestamp;
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer appropriateness infos
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_tax_germany_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
PROCEDURE put_history(p_action                    IN customer_tax_germany_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_tax_germany_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_tax_germany%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_tax_germany_h (customer_tax_germany_id,
                                       customer_id,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       effective_end_timestamp,
                                       action,
                                       action_timestamp,
                                       subject_to_capital_gains_tax,
                                       roll_losses_at_end_of_year,
                                       tax_identification_number,
                                       state)
                               VALUES (p_old_record.customer_tax_germany_id,
                                       p_old_record.customer_id,
                                       p_old_record.logical_load_timestamp,
                                       p_old_record.created_by,
                                       p_old_record.create_timestamp,
                                       p_old_record.updated_by,
                                       p_old_record.update_timestamp,
                                       p_old_record.effective_start_timestamp,
                                       p_effective_end_timestamp,
                                       p_action,
                                       SYSTIMESTAMP,
                                       p_old_record.subject_to_capital_gains_tax,
                                       p_old_record.roll_losses_at_end_of_year,
                                       p_old_record.tax_identification_number,
                                       p_old_record.state);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_tax_germany_h
       SET logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           subject_to_capital_gains_tax    = p_old_record.subject_to_capital_gains_tax,
           roll_losses_at_end_of_year      = p_old_record.roll_losses_at_end_of_year,
           tax_identification_number       = p_old_record.tax_identification_number,
           state                           = p_old_record.state
     WHERE customer_id                     = p_old_record.customer_id AND
           customer_tax_germany_id         = p_old_record.customer_tax_germany_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer_tax_church_rates
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_tax_church_rates_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_tax_church_rates_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_tax_church_rates_h.effective_end_timestamp%TYPE,
                        p_old_record_ch             IN customer_tax_church_rates%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_tax_church_rates_h (church_rates_id,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             customer_tax_germany_id,
                                             tax_year,
                                             tax_rate,
                                             diocese,
                                             is_deleted)
                                     VALUES (p_old_record_ch.church_rates_id,
                                             p_old_record_ch.logical_load_timestamp,
                                             p_old_record_ch.created_by,
                                             p_old_record_ch.create_timestamp,
                                             p_old_record_ch.updated_by,
                                             p_old_record_ch.update_timestamp,
                                             p_old_record_ch.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_old_record_ch.customer_tax_germany_id,
                                             p_old_record_ch.tax_year,
                                             p_old_record_ch.tax_rate,
                                             p_old_record_ch.diocese,
                                             p_old_record_ch.is_deleted);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_tax_church_rates_h
       SET logical_load_timestamp          = p_old_record_ch.logical_load_timestamp,
           created_by                      = p_old_record_ch.created_by,
           create_timestamp                = p_old_record_ch.create_timestamp,
           updated_by                      = p_old_record_ch.updated_by,
           update_timestamp                = p_old_record_ch.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           customer_tax_germany_id         = p_old_record_ch.customer_tax_germany_id,
           tax_year                        = p_old_record_ch.tax_year,
           tax_rate                        = p_old_record_ch.tax_rate,
           diocese                         = p_old_record_ch.diocese,
           is_deleted                      = p_old_record_ch.is_deleted
     WHERE church_rates_id                 = p_old_record_ch.church_rates_id AND
           effective_start_timestamp       = p_old_record_ch.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the german_tax_roll_losses
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for german_tax_roll_losses_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN german_tax_roll_losses_h.action%TYPE,
                        p_effective_end_timestamp   IN german_tax_roll_losses_h.effective_end_timestamp%TYPE,
                        p_old_record_ch             IN german_tax_roll_losses%ROWTYPE) IS

  BEGIN

    INSERT INTO german_tax_roll_losses_h(roll_losses_id,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         customer_tax_germany_id,
                                         tax_year,
                                         roll_losses_at_end_of_year)
                                 VALUES (p_old_record_ch.roll_losses_id,
                                         p_old_record_ch.logical_load_timestamp,
                                         p_old_record_ch.created_by,
                                         p_old_record_ch.create_timestamp,
                                         p_old_record_ch.updated_by,
                                         p_old_record_ch.update_timestamp,
                                         p_old_record_ch.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_record_ch.customer_tax_germany_id,
                                         p_old_record_ch.tax_year,
                                         p_old_record_ch.roll_losses_at_end_of_year);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE german_tax_roll_losses_h
       SET logical_load_timestamp          = p_old_record_ch.logical_load_timestamp,
           created_by                      = p_old_record_ch.created_by,
           create_timestamp                = p_old_record_ch.create_timestamp,
           updated_by                      = p_old_record_ch.updated_by,
           update_timestamp                = p_old_record_ch.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           customer_tax_germany_id         = p_old_record_ch.customer_tax_germany_id,
           tax_year                        = p_old_record_ch.tax_year,
           roll_losses_at_end_of_year      = p_old_record_ch.roll_losses_at_end_of_year
     WHERE roll_losses_id                  = p_old_record_ch.roll_losses_id AND
           effective_start_timestamp       = p_old_record_ch.effective_start_timestamp;

  END;
  
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the cst_tx_ger_psl_tx_fr_allw
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for cst_tx_ger_psl_tx_fr_allw_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history_psl(p_action                    IN cst_tx_ger_psl_tx_fr_allw_h.action%TYPE,
                            p_effective_end_timestamp   IN cst_tx_ger_psl_tx_fr_allw_h.effective_end_timestamp%TYPE,
                            p_old_record                IN cst_tx_ger_psl_tx_fr_allw%ROWTYPE) IS

  BEGIN

    INSERT INTO cst_tx_ger_psl_tx_fr_allw_h (personal_tax_free_allow_id,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             customer_tax_germany_id,
                                             tax_year,
                                             tax_free_allowance,
                                             is_auto_roll,
                                             is_deleted)
                                     VALUES (p_old_record.personal_tax_free_allow_id,
                                             p_old_record.logical_load_timestamp,
                                             p_old_record.created_by,
                                             p_old_record.create_timestamp,
                                             p_old_record.updated_by,
                                             p_old_record.update_timestamp,
                                             p_old_record.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_old_record.customer_tax_germany_id,
                                             p_old_record.tax_year,
                                             p_old_record.tax_free_allowance,
                                             p_old_record.is_auto_roll,
                                             p_old_record.is_deleted);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE cst_tx_ger_psl_tx_fr_allw_h
       SET logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           customer_tax_germany_id         = p_old_record.customer_tax_germany_id,
           tax_year                        = p_old_record.tax_year,
           tax_free_allowance              = p_old_record.tax_free_allowance,
           is_auto_roll                    = p_old_record.is_auto_roll,
           is_deleted                      = p_old_record.is_deleted
     WHERE personal_tax_free_allow_id      = p_old_record.personal_tax_free_allow_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the cst_tx_ger_cpl_tx_fr_allw
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for cst_tx_ger_cpl_tx_fr_allw_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN cst_tx_ger_cpl_tx_fr_allw_h.action%TYPE,
                        p_effective_end_timestamp   IN cst_tx_ger_cpl_tx_fr_allw_h.effective_end_timestamp%TYPE,
                        p_old_record                IN cst_tx_ger_cpl_tx_fr_allw%ROWTYPE) IS

  BEGIN

    INSERT INTO cst_tx_ger_cpl_tx_fr_allw_h (couples_tax_free_allow_id,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             customer_tax_germany_id,
                                             tax_year,
                                             tax_free_allowance,
                                             is_auto_roll,
                                             is_deleted)
                                     VALUES (p_old_record.couples_tax_free_allow_id,
                                             p_old_record.logical_load_timestamp,
                                             p_old_record.created_by,
                                             p_old_record.create_timestamp,
                                             p_old_record.updated_by,
                                             p_old_record.update_timestamp,
                                             p_old_record.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_old_record.customer_tax_germany_id,
                                             p_old_record.tax_year,
                                             p_old_record.tax_free_allowance,
                                             p_old_record.is_auto_roll,
                                             p_old_record.is_deleted);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE cst_tx_ger_cpl_tx_fr_allw_h
       SET logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           customer_tax_germany_id         = p_old_record.customer_tax_germany_id,
           tax_year                        = p_old_record.tax_year,
           tax_free_allowance              = p_old_record.tax_free_allowance,
           is_auto_roll                    = p_old_record.is_auto_roll,
           is_deleted                      = p_old_record.is_deleted
     WHERE couples_tax_free_allow_id      = p_old_record.couples_tax_free_allow_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the german_tax_couples_rel
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for german_tax_couples_rel_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN german_tax_couples_rel_h.action%TYPE,
                        p_effective_end_timestamp   IN german_tax_couples_rel_h.effective_end_timestamp%TYPE,
                        p_old_record                IN german_tax_couples_rel%ROWTYPE) IS

  BEGIN

    INSERT INTO german_tax_couples_rel_h(couples_tax_free_allow_id,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         customer_id)
                                 VALUES (p_old_record.couples_tax_free_allow_id,
                                         p_old_record.logical_load_timestamp,
                                         p_old_record.created_by,
                                         p_old_record.create_timestamp,
                                         p_old_record.updated_by,
                                         p_old_record.update_timestamp,
                                         p_old_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_record.customer_id);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

   NULL; --No other attributes at present

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the customer_email_preferences
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_email_preferences_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN customer_email_preferences_h.action%TYPE,
                        p_effective_end_timestamp   IN customer_email_preferences_h.effective_end_timestamp%TYPE,
                        p_old_record                IN customer_email_preferences%ROWTYPE) IS

  BEGIN

    INSERT INTO customer_email_preferences_h(email_preference_id,
                                             customer_id,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             topic,
                                             is_subscribed)
                                     VALUES (p_old_record.email_preference_id,
                                             p_old_record.customer_id,
                                             p_old_record.logical_load_timestamp,
                                             p_old_record.created_by,
                                             p_old_record.create_timestamp,
                                             p_old_record.updated_by,
                                             p_old_record.update_timestamp,
                                             p_old_record.effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_old_record.topic,
                                             p_old_record.is_subscribed);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE customer_email_preferences_h
       SET topic                           = p_old_record.topic,
           logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           is_subscribed                   = p_old_record.is_subscribed
     WHERE customer_id                     = p_old_record.customer_id AND
           email_preference_id             = p_old_record.email_preference_id AND
           effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_dioceses
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put German Dioceses
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     german_dioceses
  --     german_dioceses_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_german_dioceses                german_dioceses
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
PROCEDURE put_dioceses(p_user                      IN german_dioceses.created_by%TYPE,
                       p_effective_start_timestamp IN german_dioceses.effective_start_timestamp%TYPE,
                       p_german_dioceses           IN german_dioceses_tab) IS

  lv_logical_load_timestamp TIMESTAMP(6) := systimestamp;


BEGIN

   MERGE INTO german_dioceses_h old_version
        USING (SELECT old_version.*
                 FROM german_dioceses old_version
                 JOIN TABLE(CAST(p_german_dioceses AS german_dioceses_tab)) gd
                   ON gd.diocese_number = old_version.diocese_number
                WHERE (nrg_common.has_value_changed(old_version.diocese_name,gd.diocese_name) = 1 OR
                       nrg_common.has_value_changed(old_version.diocese_id,gd.diocese_id) = 1)) new_version
           ON (old_version.diocese_number = new_version.diocese_number)
         WHEN MATCHED THEN UPDATE
          SET old_version.updated_by             = new_version.updated_by,
              old_version.action                 = 'U',
              old_version.action_timestamp       = systimestamp,
              old_version.update_timestamp       = new_version.update_timestamp,
              old_version.logical_load_timestamp = new_version.logical_load_timestamp,
              old_version.diocese_name           = new_version.diocese_name,
              old_version.diocese_id             = new_version.diocese_id
         WHEN NOT MATCHED THEN INSERT
             (diocese_number,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              effective_end_timestamp,
              action,
              action_timestamp,
              diocese_name,
              diocese_id)
      VALUES (new_version.diocese_number,
              new_version.logical_load_timestamp,
              new_version.created_by,
              new_version.create_timestamp,
              new_version.updated_by,
              new_version.update_timestamp,
              new_version.effective_start_timestamp,
              p_effective_start_timestamp,
              'U',
              systimestamp,
              new_version.diocese_name,
              new_version.diocese_id);

   MERGE INTO german_dioceses old_version
        USING (SELECT DISTINCT gd.diocese_id     AS diocese_id,
                               gd.diocese_name   AS diocese_name,
                               gd.diocese_number AS diocese_number
                 FROM TABLE(CAST(p_german_dioceses AS german_dioceses_tab)) gd) new_version
           ON (old_version.diocese_number = new_version.diocese_number)
         WHEN MATCHED THEN UPDATE
          SET old_version.logical_load_timestamp    = lv_logical_load_timestamp,
              old_version.updated_by                = p_user,
              old_version.update_timestamp          = systimestamp,
              old_version.effective_start_timestamp = p_effective_start_timestamp,
              old_version.diocese_name              = new_version.diocese_name,
              old_version.diocese_id                = new_version.diocese_id
       WHERE (nrg_common.has_value_changed(old_version.diocese_name,new_version.diocese_name) = 1 OR
              nrg_common.has_value_changed(old_version.diocese_id,new_version.diocese_id) = 1)
        WHEN NOT MATCHED THEN INSERT
             (diocese_number,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              diocese_name,
              diocese_id)
      VALUES (new_version.diocese_number,
              lv_logical_load_timestamp,
              p_user,
              systimestamp,
              p_user,
              systimestamp,
              p_effective_start_timestamp,
              new_version.diocese_name,
              new_version.diocese_id);

END;


  -- ===================================================================================
  -- put_customer_enquiries
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Customer enquires
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_enquiries
  --     customer_enquiries_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version            Account Version of the existing record on ODS
  --     p_new_customer_version            Account Version of the new record that is received
  --     p_customer_Apprprtnss_info       Customer enquiries
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_enquiries(p_user                        customer_enquiries.created_by%TYPE,
                                   p_logical_load_timestamp      customer_enquiries.logical_load_timestamp%TYPE,
                                   p_effective_start_timestamp   customer_enquiries.effective_start_timestamp%TYPE,
                                   p_customer_id                 customer_enquiries.customer_id%TYPE,
                                   p_old_customer_version        customers.customer_version%TYPE,
                                   p_new_customer_version        customers.customer_version%TYPE,
                                   p_customer_enquiries          customer_enquiry_tab) IS
    TYPE ltab_customer_enquiries IS TABLE OF customer_enquiries%ROWTYPE;
    lv_old_records ltab_customer_enquiries;
  BEGIN
    CASE
      WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN
        SELECT old_recs.*
        BULK COLLECT INTO lv_old_records
        FROM customer_enquiries old_recs JOIN
             TABLE(CAST(p_customer_enquiries AS customer_enquiry_tab)) new_recs ON (old_recs.enquiry_type = new_recs.enquiry_type AND
                                                                                    old_recs.enquiry_date = new_recs.enquiry_date AND
                                                                                    old_recs.customer_id = p_customer_id)
        WHERE nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1;
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action => 'U',
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_old_record => lv_old_records(lv_cnt));
        END LOOP;
        MERGE INTO customer_enquiries old_recs
        USING (SELECT * FROM TABLE(CAST(p_customer_enquiries AS customer_enquiry_tab))) new_recs
        ON (old_recs.enquiry_type = new_recs.enquiry_type AND
            old_recs.enquiry_date = new_recs.enquiry_date AND
            old_recs.customer_id = p_customer_id)
        WHEN MATCHED THEN
          UPDATE
          SET effective_start_timestamp = p_effective_start_timestamp,
              logical_load_timestamp = p_logical_load_timestamp,
              updated_by = p_user,
              update_timestamp = SYSTIMESTAMP,
              is_deleted = new_recs.is_deleted
          WHERE nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1
        WHEN NOT MATCHED THEN
          INSERT (enquiry_type,
                  enquiry_date,
                  customer_id,
                  effective_start_timestamp,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  is_deleted)
           VALUES(new_recs.enquiry_type,
                  new_recs.enquiry_date,
                  p_customer_id,
                  p_effective_start_timestamp,
                  p_logical_load_timestamp,
                  p_user,
                  SYSDATE,
                  p_user,
                  SYSDATE,
                  new_recs.is_deleted);
      WHEN p_old_customer_version > p_new_customer_version THEN
        SELECT enquiry_type,
               enquiry_date,
               p_customer_id,
               p_effective_start_timestamp,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               is_deleted
        BULK COLLECT INTO lv_old_records
        FROM TABLE(CAST(p_customer_enquiries AS customer_enquiry_tab)) new_recs
        WHERE (is_deleted) NOT IN (SELECT is_deleted
                                   FROM customer_enquiries a
                                   WHERE a.enquiry_type = new_recs.enquiry_type AND
                                         a.enquiry_date = new_recs.enquiry_date AND
                                         a.customer_id = p_customer_id
                                   UNION ALL
                                   SELECT is_deleted
                                   FROM customer_enquiries_h b
                                   WHERE b.enquiry_type = new_recs.enquiry_type AND
                                         b.enquiry_date = new_recs.enquiry_date AND
                                         b.customer_id = p_customer_id);
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action => 'I',
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_old_record => lv_old_records(lv_cnt));
        END LOOP;
    END CASE;
  END;
    -- ===================================================================================
    -- put_customer_Apprprtnss_infos
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Appropriateness Infos
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     customer_Apprprtnss_info
    --     customer_Apprprtnss_info_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_customer_Apprprtnss_info       Customer Appropriateness Infos
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_customer_apprprtnss_infos(p_user                        customer_apprprtnss_infos.created_by%TYPE,
                                            p_logical_load_timestamp      customer_apprprtnss_infos.logical_load_timestamp%TYPE,
                                            p_effective_start_timestamp   customer_apprprtnss_infos.effective_start_timestamp%TYPE,
                                            p_customer_id                 customers.customer_id%TYPE,
                                            p_old_customer_version        customers.customer_version%TYPE,
                                            p_new_customer_version        customers.customer_version%TYPE,
                                            p_customer_apprprtnss_info    customer_apprprtnss_info_tab) IS
      TYPE l_old_records_tab IS TABLE OF customer_apprprtnss_infos%ROWTYPE;
      lv_old_records l_old_records_tab;
    BEGIN
      logger.logger.set_module('put_customer_Apprprtnss_infos');
      CASE
        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN
        --
        --Incase of new accounts and updated account information
        --
        --
        --Identify updated records
        --
        SELECT old_records.*
        BULK COLLECT INTO lv_old_records
        FROM TABLE(CAST(p_customer_apprprtnss_info AS customer_apprprtnss_info_tab)) new_records,
             customer_apprprtnss_infos old_records
        WHERE old_records.customer_id = p_customer_id AND
              old_records.appropriateness_id = new_records.appropriateness_id AND
              (nrg_common.has_value_changed(old_records.is_appropriate, new_records.is_appropriate) = 1 OR
               nrg_common.has_value_changed(old_records.reason, new_records.reason) = 1 OR
               nrg_common.has_value_changed(old_records.is_agreed_to_proceed, new_records.is_agreed_to_proceed) = 1 OR
               nrg_common.has_value_changed(old_records.financial_suitability_score, new_records.financial_suitability_score) = 1 OR
               nrg_common.has_value_changed(old_records.knowledge_and_experience_score, new_records.knowledge_and_experience_score) = 1 OR
               nrg_common.has_value_changed(old_records.assessment_date, new_records.assessment_date) = 1 OR
               nrg_common.has_value_changed(old_records.is_reassessment, new_records.is_reassessment) = 1 OR
               nrg_common.has_value_changed(old_records.appropriateness, new_records.appropriateness) = 1 OR
               nrg_common.has_value_changed(old_records.passed_knowledge_assessment, new_records.passed_knowledge_assessment) = 1 OR
               nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
               nrg_common.has_value_changed(old_records.reference, new_records.reference) = 1);
        --
        --Write updated records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'U',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;
        --
        --Identify Deleted Records
        --
        SELECT old_records.*
        BULK COLLECT INTO lv_old_records
        FROM customer_apprprtnss_infos old_records
        WHERE customer_id = p_customer_id AND
              appropriateness_id NOT IN (SELECT appropriateness_id
                                         FROM TABLE(CAST(p_customer_apprprtnss_info AS customer_apprprtnss_info_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'D',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_apprprtnss_infos old_records
        WHERE customer_id = p_customer_id AND
              appropriateness_id NOT IN (SELECT appropriateness_id
                                         FROM TABLE(CAST(p_customer_apprprtnss_info AS customer_apprprtnss_info_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_apprprtnss_infos old_records
        USING (SELECT p_customer_id customer_id,
                      p_user created_by,
                      SYSTIMESTAMP message_time,
                      p_effective_start_timestamp effective_start_timestamp,
                      appropriateness_id,
                      is_appropriate,
                      reason,
                      is_agreed_to_proceed,
                      financial_suitability_score,
                      knowledge_and_experience_score,
                      assessment_date,
                      is_reassessment,
                      appropriateness,
                      passed_knowledge_assessment,
                      legal_entity,
                      reference
              FROM TABLE(CAST(p_customer_apprprtnss_info AS customer_apprprtnss_info_tab)) aa) new_records
        ON (old_records.customer_id = new_records.customer_id AND
            old_records.appropriateness_id = new_records.appropriateness_id)
        WHEN
          MATCHED THEN
            UPDATE
            SET old_records.updated_by = new_records.created_by,
                old_records.update_timestamp = new_records.message_time,
                old_records.logical_load_timestamp = SYSTIMESTAMP,
                old_records.effective_start_timestamp = new_records.effective_start_timestamp,
                old_records.is_appropriate = new_records.is_appropriate,
                old_records.reason = new_records.reason,
                old_records.is_agreed_to_proceed = new_records.is_agreed_to_proceed,
                old_records.financial_suitability_score = new_records.financial_suitability_score,
                old_records.knowledge_and_experience_score = new_records.knowledge_and_experience_score,
                old_records.assessment_date = new_records.assessment_date,
                old_records.is_reassessment = new_records.is_reassessment,
                old_records.appropriateness = new_records.appropriateness,
                old_records.passed_knowledge_assessment = new_records.passed_knowledge_assessment,
                old_records.legal_entity = new_records.legal_entity,
                old_records.reference = new_records.reference
            WHERE (nrg_common.has_value_changed(old_records.is_appropriate, new_records.is_appropriate) = 1 OR
                   nrg_common.has_value_changed(old_records.reason, new_records.reason) = 1 OR
                   nrg_common.has_value_changed(old_records.is_agreed_to_proceed, new_records.is_agreed_to_proceed) = 1 OR
                   nrg_common.has_value_changed(old_records.financial_suitability_score, new_records.financial_suitability_score) = 1 OR
                   nrg_common.has_value_changed(old_records.knowledge_and_experience_score, new_records.knowledge_and_experience_score) = 1 OR
                   nrg_common.has_value_changed(old_records.assessment_date, new_records.assessment_date) = 1 OR
                   nrg_common.has_value_changed(old_records.is_reassessment, new_records.is_reassessment) = 1 OR
                   nrg_common.has_value_changed(old_records.appropriateness, new_records.appropriateness) = 1 OR
                   nrg_common.has_value_changed(old_records.passed_knowledge_assessment, new_records.passed_knowledge_assessment) = 1 OR
                   nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1) OR
                   nrg_common.has_value_changed(old_records.reference, new_records.reference) = 1
        WHEN
          NOT MATCHED THEN
            INSERT (customer_id,
                    appropriateness_id,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    effective_start_timestamp,
                    is_appropriate,
                    reason,
                    is_agreed_to_proceed,
                    financial_suitability_score,
                    knowledge_and_experience_score,
                    assessment_date,
                    is_reassessment,
                    appropriateness,
                    passed_knowledge_assessment,
                    legal_entity,
                    reference)
            VALUES (new_records.customer_id,
                    new_records.appropriateness_id,
                    SYSTIMESTAMP,
                    new_records.created_by,
                    new_records.message_time,
                    new_records.created_by,
                    new_records.message_time,
                    new_records.effective_start_timestamp,
                    new_records.is_appropriate,
                    new_records.reason,
                    new_records.is_agreed_to_proceed,
                    new_records.financial_suitability_score,
                    new_records.knowledge_and_experience_score,
                    new_records.assessment_date,
                    new_records.is_reassessment,
                    new_records.appropriateness,
                    new_records.passed_knowledge_assessment,
                    new_records.legal_entity,
                    new_records.reference);
        WHEN p_old_customer_version > p_new_customer_version THEN
          SELECT p_customer_id,
                 appropriateness_id,
                 SYSTIMESTAMP logical_load_timestamp,
                 p_user created_by,
                 SYSTIMESTAMP create_timestamp,
                 p_user updated_by,
                 SYSTIMESTAMP update_timestamp,
                 p_effective_start_timestamp,
                 is_appropriate,
                 reason,
                 is_agreed_to_proceed,
                 financial_suitability_score,
                 knowledge_and_experience_score,
                 assessment_date,
                 is_reassessment,
                 appropriateness,
                 passed_knowledge_assessment,
                 legal_entity,
                 reference
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_customer_apprprtnss_info AS customer_apprprtnss_info_tab))
          WHERE (appropriateness_id,
                 is_appropriate,
                 reason,
                 is_agreed_to_proceed,
                 financial_suitability_score,
                 knowledge_and_experience_score,
                 assessment_date,
                 is_reassessment,
                 appropriateness,
                 passed_knowledge_assessment,
                 legal_entity,
                 reference) NOT IN (SELECT appropriateness_id,
                                              is_appropriate,
                                              reason,
                                              is_agreed_to_proceed,
                                              financial_suitability_score,
                                              knowledge_and_experience_score,
                                              assessment_date,
                                              is_reassessment,
                                              appropriateness,
                                              passed_knowledge_assessment,
                                              legal_entity,
                                              reference
                                         FROM customer_apprprtnss_infos
                                        WHERE customer_id = p_customer_id
                                        UNION ALL
                                       SELECT appropriateness_id,
                                              is_appropriate,
                                              reason,
                                              is_agreed_to_proceed,
                                              financial_suitability_score,
                                              knowledge_and_experience_score,
                                              assessment_date,
                                              is_reassessment,
                                              appropriateness,
                                              passed_knowledge_assessment,
                                              legal_entity,
                                              reference
                                         FROM customer_apprprtnss_infos_h
                                        WHERE customer_id = p_customer_id);
          --
          --Write missing records to history
          --
          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;
      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;
    --
    --
    --
    -- ===================================================================================
    -- put_tax_declaration
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put tax declarations
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMER_TAX_DECLARATIONS
    --     CUSTOMER_TAX_DECLARATIONS_H
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_tax_declarations               Tax declarations
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_tax_declaration(p_user                        customers.created_by%TYPE,
                                  p_logical_load_timestamp      customers.logical_load_timestamp%TYPE,
                                  p_effective_start_timestamp   customers.effective_start_timestamp%TYPE,
                                  p_customer_id                 customers.customer_id%TYPE,
                                  p_old_customer_version        customers.customer_version%TYPE,
                                  p_new_customer_version        customers.customer_version%TYPE,
                                  p_tax_declarations            tax_declaration_tab) IS
      TYPE l_old_records_tab IS TABLE OF customer_tax_declarations%ROWTYPE;
      lv_old_records l_old_records_tab;
    BEGIN
      logger.logger.set_module('put_tax_declaration');
      CASE
        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN
        --
        --Incase of new accounts and updated account information
        --
        --
        --Identify updated records
        --
        SELECT old_records.*
        BULK COLLECT INTO lv_old_records
        FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)) new_records,
             customer_tax_declarations old_records
        WHERE old_records.customer_id = p_customer_id AND
              old_records.legal_entity = new_records.legal_entity AND
              old_records.reporting_country = new_records.reporting_country AND
              (nrg_common.has_value_changed(old_records.declaration_form_name, new_records.declaration_form_name) = 1 OR
               nrg_common.has_value_changed(old_records.country_of_residence, new_records.country_of_residence) = 1 OR
               nrg_common.has_value_changed(old_records.signature_date, new_records.signature_date) = 1 OR
               nrg_common.has_value_changed(old_records.is_valid, new_records.is_valid) = 1 OR
               --nrg_common.has_value_changed(old_records.is_exempt, new_records.is_exempt) = 1 OR
               nrg_common.has_value_changed(old_records.is_opt_out, new_records.is_opt_out) = 1 OR
               nrg_common.has_value_changed(old_records.withholding_tax_agree_type, new_records.withholding_tax_agree_type) = 1);
        --
        --Write updated records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'U',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;
        --
        --Identify Deleted Records
        --
        SELECT old_records.*
        BULK COLLECT INTO lv_old_records
        FROM customer_tax_declarations old_records
        WHERE customer_id = p_customer_id AND
              (legal_entity,
               reporting_country) NOT IN (SELECT legal_entity,
                                                 reporting_country
                                          FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'D',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_tax_declarations old_records
        WHERE customer_id = p_customer_id AND
              (legal_entity,
               reporting_country) NOT IN (SELECT legal_entity,
                                                 reporting_country
                                          FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_tax_declarations old_records
        USING (SELECT p_customer_id customer_id,
                      p_user created_by,
                      SYSTIMESTAMP message_time,
                      p_effective_start_timestamp effective_start_timestamp,
                      legal_entity,
                      reporting_country,
                      declaration_form_name,
                      country_of_residence,
                      signature_date,
                      is_valid,
                      --is_exempt,
                      is_opt_out,
                      withholding_tax_agree_type
              FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)) aa) new_records
        ON (old_records.customer_id = new_records.customer_id AND
            old_records.legal_entity = new_records.legal_entity AND
            old_records.reporting_country = new_records.reporting_country)
        WHEN
          MATCHED THEN
            UPDATE
            SET old_records.updated_by = new_records.created_by,
                old_records.update_timestamp = new_records.message_time,
                old_records.logical_load_timestamp = SYSTIMESTAMP,
                old_records.effective_start_timestamp = new_records.effective_start_timestamp,
                old_records.declaration_form_name = new_records.declaration_form_name,
                old_records.country_of_residence = new_records.country_of_residence,
                old_records.signature_date = new_records.signature_date,
                old_records.is_valid = new_records.is_valid,
                --old_records.is_exempt = new_records.is_exempt,
                old_records.is_opt_out = new_records.is_opt_out,
                old_records.withholding_tax_agree_type = new_records.withholding_tax_agree_type
            WHERE (nrg_common.has_value_changed(old_records.declaration_form_name, new_records.declaration_form_name) = 1 OR
                   nrg_common.has_value_changed(old_records.country_of_residence, new_records.country_of_residence) = 1 OR
                   nrg_common.has_value_changed(old_records.signature_date, new_records.signature_date) = 1 OR
                   nrg_common.has_value_changed(old_records.is_valid, new_records.is_valid) = 1 OR
                   --nrg_common.has_value_changed(old_records.is_exempt, new_records.is_exempt) = 1 OR
                   nrg_common.has_value_changed(old_records.is_opt_out, new_records.is_opt_out) = 1 OR
                   nrg_common.has_value_changed(old_records.withholding_tax_agree_type, new_records.withholding_tax_agree_type) = 1)
        WHEN
          NOT MATCHED THEN
            INSERT (customer_id,
                    legal_entity,
                    reporting_country,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    effective_start_timestamp,
                    declaration_form_name,
                    country_of_residence,
                    signature_date,
                    is_valid,
                    --is_exempt,
                    is_opt_out,
                    withholding_tax_agree_type)
            VALUES (new_records.customer_id,
                    new_records.legal_entity,
                    new_records.reporting_country,
                    SYSTIMESTAMP,
                    new_records.created_by,
                    new_records.message_time,
                    new_records.created_by,
                    new_records.message_time,
                    new_records.effective_start_timestamp,
                    new_records.declaration_form_name,
                    new_records.country_of_residence,
                    new_records.signature_date,
                    new_records.is_valid,
                    --new_records.is_exempt,
                    new_records.is_opt_out,
                    new_records.withholding_tax_agree_type);
        WHEN p_old_customer_version > p_new_customer_version THEN
          SELECT p_customer_id,
                 legal_entity,
                 reporting_country,
                 SYSTIMESTAMP logical_load_timestamp,
                 p_user created_by,
                 SYSTIMESTAMP create_timestamp,
                 p_user updated_by,
                 SYSTIMESTAMP update_timestamp,
                 p_effective_start_timestamp,
                 declaration_form_name,
                 country_of_residence,
                 signature_date,
                 is_valid,
                 NULL is_exempt,
                 is_opt_out,
                 withholding_tax_agree_type
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab))
          WHERE (legal_entity,
                 reporting_country,
                 declaration_form_name,
                 country_of_residence,
                 signature_date,
                 is_valid,
                 --is_exempt,
                 is_opt_out,
                 withholding_tax_agree_type) NOT IN (SELECT legal_entity,
                                           reporting_country,
                                           declaration_form_name,
                                           country_of_residence,
                                           signature_date,
                                           is_valid,
                                           --is_exempt,
                                           is_opt_out,
                                           withholding_tax_agree_type
                                    FROM customer_tax_declarations
                                    WHERE customer_id = p_customer_id
                                    UNION ALL
                                    SELECT legal_entity,
                                           reporting_country,
                                           declaration_form_name,
                                           country_of_residence,
                                           signature_date,
                                           is_valid,
                                           --is_exempt,
                                           is_opt_out,
                                           withholding_tax_agree_type
                                    FROM customer_tax_declarations_h
                                    WHERE customer_id = p_customer_id);
          --
          --Write missing records to history
          --
          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;
      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;
    -- ===================================================================================
    -- put_trading_account_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Trading Account Id's
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     TA_CUST_LINK_CUST_FEED
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_old_effective_start_time       Effective start timestamp of the existing record if present
    --     p_customer_id                    Customer id
    --     p_trading_account_ids            Trading Account Ids
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_trading_account_ids (p_user                         customers.created_by%TYPE,
                                       p_logical_load_timestamp       customers.logical_load_timestamp%TYPE,
                                       p_effective_start_timestamp    customers.effective_start_timestamp%TYPE,
                                       p_old_effective_start_time     customers.effective_start_timestamp%TYPE,
                                       p_customer_id                  customers.customer_id%TYPE,
                                       p_trading_account_ids           trading_account_id_tab,
                                       p_trading_account_type         trading_accounts.Trading_Account_Type%TYPE)
    IS
      TYPE ltyp_trading_acc_link IS TABLE OF TA_CUST_LINK_CUST_FEED%ROWTYPE;
      ltab_trad_acc_link       ltyp_trading_acc_link;
      ltab_trad_acc_link_old   ltyp_trading_acc_link;
      /***** Begin Modification for V2.4 - BER625 *****/
      lv_record_exists         NUMBER:=0;
      /***** End Modification for V2.4 *****/
      TYPE ltyp_trading_account_ids IS TABLE OF NUMBER
      INDEX BY BINARY_INTEGER;
      ltab_trading_account_ids        ltyp_trading_account_ids;
      ltab_customer_ids               customer_id_tab := customer_id_tab(customer_id_obj(p_customer_id));

    BEGIN

    --
    -- Note stubs have already been created if required
    --
      CASE
      WHEN p_old_effective_start_time IS NULL THEN
        IF p_trading_account_ids IS NOT NULL THEN
          FOR i IN 1.. p_trading_account_ids.COUNT LOOP
            BEGIN
              /***** Begin Modification for V2.4 - BER625 *****/
              SELECT   1
                  INTO lv_record_exists
                  FROM TA_CUST_LINK_CUST_FEED
                  WHERE trading_account_id  = p_trading_account_ids(i).trading_account_id
                    AND trading_account_type=p_trading_account_type
                    AND customer_id         = p_customer_id;
            EXCEPTION
            WHEN NO_DATA_FOUND THEN
              lv_record_exists := 0;
            END;
            IF lv_record_exists = 0 THEN
            /***** End Modification for V2.4 *****/
              INSERT INTO TA_CUST_LINK_CUST_FEED
                            (
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              logical_load_timestamp,
                              effective_start_timestamp,
                              customer_id,
                              trading_account_id,
                              trading_account_type
                            )
                            VALUES( p_user,
                              SYSTIMESTAMP,
                              p_user,
                              SYSTIMESTAMP,
                              p_logical_load_timestamp,
                              p_effective_start_timestamp,
                              p_customer_id,
                              p_trading_account_ids(i).trading_account_id,
                              p_trading_account_type);
            /***** Begin Modification for V2.4 - BER625 *****/
            END IF;
            --EXCEPTION
            --  WHEN DUP_VAL_ON_INDEX THEN
            --    NULL;
                --The value already present , so no action
            --END;
            /***** End Modification for V2.4 *****/
          END LOOP;
        END IF;
        WHEN p_effective_start_timestamp >= p_old_effective_start_time THEN
            --
            --Insert the new customer trading account links
            --
          IF p_trading_account_ids IS NOT NULL THEN
            FOR i IN 1.. p_trading_account_ids.COUNT LOOP
              BEGIN
              /***** Begin Modification for V2.5 - BER631 *****/
              SELECT   1
                  INTO lv_record_exists
                  FROM TA_CUST_LINK_CUST_FEED
                  WHERE trading_account_id  = p_trading_account_ids(i).trading_account_id
                    AND trading_account_type=p_trading_account_type
                    AND customer_id         = p_customer_id;
            EXCEPTION
            WHEN NO_DATA_FOUND THEN
              lv_record_exists := 0;
            END;
            IF lv_record_exists = 0 THEN
            /***** End Modification for V2.5 *****/
                INSERT INTO TA_CUST_LINK_CUST_FEED
                          (
                            created_by,
                            create_timestamp,
                            updated_by,
                            update_timestamp,
                            logical_load_timestamp,
                            effective_start_timestamp,
                            customer_id,
                            trading_account_id,
                            trading_account_type
                          )
                      values(  p_user,
                          SYSTIMESTAMP,
                          p_user,
                          SYSTIMESTAMP,
                          p_logical_load_timestamp,
                          p_effective_start_timestamp,
                          p_customer_id,
                          p_trading_account_ids(i).trading_account_id,
                          p_trading_account_type);
            /***** Begin Modification for V2.5 - BER631 *****/
            END IF;
            --EXCEPTION
            --  WHEN DUP_VAL_ON_INDEX THEN
            --    NULL;
                --The value already present , so no action
            --END;
            /***** End Modification for V2.5 *****/
        END LOOP;
      END IF;
            --
            -- Bulk Delete the data that has got deleted from the source
            -- including the scenario where all values have been removed i.e. empty
            --
            -- Lock the row(s) with the for update
            --
            SELECT *
            BULK COLLECT INTO ltab_trad_acc_link_old
            FROM TA_CUST_LINK_CUST_FEED t
      WHERE  t.customer_id = p_customer_id
              AND (t.trading_account_id, t.trading_account_type) NOT IN (SELECT trading_account_id, p_trading_account_type
                                                                          FROM   TABLE(CAST(p_trading_account_ids AS trading_account_id_tab)) trad )
            FOR UPDATE;
            FOR lv_count IN 1..ltab_trad_acc_link_old.COUNT LOOP
                put_history(p_old_cust_trad           => ltab_trad_acc_link_old(lv_count),
                            p_effective_end_timestamp => p_effective_start_timestamp,
                            p_action                  => 'D');
            END LOOP;
        DELETE TA_CUST_LINK_CUST_FEED
        WHERE (trading_account_id, trading_account_type) NOT IN (SELECT trading_account_id, p_trading_account_type
                                                                     FROM   TABLE(CAST(p_trading_account_ids AS trading_account_id_tab)))
        AND customer_id = p_customer_id;
        WHEN p_effective_start_timestamp < p_old_effective_start_time THEN
        --
          -- Process out of sequence data/replayed messages
          --
          SELECT trad.trading_account_id,
               p_trading_account_type,
               p_customer_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp
          BULK COLLECT INTO ltab_trad_acc_link
          FROM   TABLE(CAST(p_trading_account_ids AS trading_account_id_tab)) trad
          WHERE
                --
                -- We need to exclude any data that entering the history where the non-key columns
                -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
           -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
          -- and replaying exising messages
          --
               trading_account_id NOT IN (SELECT trading_account_id
                                             FROM TA_CUST_LINK_CUST_FEED
                                             WHERE customer_id = p_customer_id AND
                                                   effective_start_timestamp <= p_effective_start_timestamp
                                         UNION ALL
                                         SELECT trading_account_id
                                            FROM TA_CUST_LINK_CUST_FEED_H
                                            WHERE customer_id = p_customer_id AND
                                                  effective_start_timestamp <= p_effective_start_timestamp AND
                                                 effective_end_timestamp > p_effective_start_timestamp);
          FOR lv_count IN 1 ..ltab_trad_acc_link.COUNT LOOP
            put_history(p_old_cust_trad           => ltab_trad_acc_link(lv_count),
                        p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action                  => 'I');
          END LOOP;
      END CASE;

    -------------------------------------------------------------------------------------------------------------------------
    ---- For PARTNER_CASH trading accounts only, put into TA_CUST_LINK_TA_FEED.
    -------------------------------------------------------------------------------------------------------------------------

      IF p_trading_account_type = 'PARTNER_CASH' THEN

        FOR lv_count IN 1..p_trading_account_ids.COUNT LOOP

          nrg_trading_account.put_customer_ids(p_user                        => p_user,
                                               p_logical_load_timestamp      => p_logical_load_timestamp,
                                               p_effective_start_timestamp   => p_effective_start_timestamp,
                                               p_trading_account_id          => p_trading_account_ids(lv_count).trading_account_id,
                                               p_customer_ids                => ltab_customer_ids,
                                               p_old_account_version         => NULL,
                                               p_new_account_version         => 1,
                                               p_trading_account_type        => p_trading_account_type);
        END LOOP;
      END IF;

    -------------------------------------------------------------------------------------------------------------------------
    ---- Check if for requested customers exist in TA_CUST_LINK_TA_FEED different trading_account_id than in this message
    -------------------------------------------------------------------------------------------------------------------------
        SELECT tal.trading_account_id
         BULK COLLECT INTO ltab_trading_account_ids
          FROM ta_cust_link_ta_feed tal
           WHERE tal.customer_id = p_customer_id
            AND tal.Trading_Account_Type = 'CUSTOMER'
            AND tal.trading_account_id NOT IN
              (SELECT trad.trading_account_id
                FROM TABLE(CAST(p_trading_account_ids AS trading_account_id_tab)) trad);
     FOR lv_count IN 1..ltab_trading_account_ids.COUNT LOOP
        nrg_request.put_trading_account_request(p_trading_account_id => ltab_trading_account_ids(lv_count));
     END LOOP;
    END put_trading_account_ids;
    -- ===================================================================================
    -- put_customer_registrations
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Registrations
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMER_REGISTRATIONS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_old_effective_start_time       Effective start timestamp of old record if exists
    --     p_customer_id                    Customer Ids
    --     p_customer_registrations         Customer Registrations
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_customer_registrations   (p_user customers.created_by%TYPE,
                                            p_logical_load_timestamp customer_registrations.logical_load_timestamp%TYPE,
                                            p_effective_start_timestamp customer_registrations.effective_start_timestamp%TYPE,
                                            p_old_effective_start_time customer_registrations.effective_start_timestamp%TYPE,
                                            p_customer_id customers.customer_id%TYPE,
                                            p_customer_registrations customer_registrations_tab)
    IS
      TYPE ltyp_updated_cust_reg IS TABLE OF customer_registrations%ROWTYPE;
      ltab_updated_cust_reg ltyp_updated_cust_reg;
      ltab_updated_cust_reg_old ltyp_updated_cust_reg;
      ltab_deleted_cust_reg ltyp_updated_cust_reg;
    BEGIN
    FOR I IN (SELECT trading_account_id
                FROM (SELECT cust_reg_tab.trading_account_id trading_account_id
                        FROM TABLE(CAST(p_customer_registrations AS customer_registrations_tab)) cust_reg_tab
                       WHERE trading_account_id NOT IN (SELECT trading_account_id
                                                          FROM trading_accounts t
                                                         WHERE t.Trading_Account_Type = 'CUSTOMER')
                                                           AND TRADING_ACCOUNT_ID IS NOT NULL))
     LOOP
         nrg_trading_account.create_trading_account_stub (p_user                        => p_user,
                                                          p_logical_load_timestamp      => p_logical_load_timestamp,
                                                          p_effective_start_timestamp   => p_effective_start_timestamp,
                                                          p_trading_account_id          => i.trading_account_id,
                                                          p_trading_account_type        => 'CUSTOMER');
     END LOOP;
      CASE
      WHEN p_old_effective_start_time IS NULL THEN
        INSERT INTO customer_registrations
                        (
                          created_by,
                        create_timestamp,
                        updated_by,
                        update_timestamp,
                        logical_load_timestamp,
                        effective_start_timestamp,
                        customer_id,
                        registration_id,
                        registration_version,
                        sales_channel,
                        application_file_reference,
                        ip_address,
                        device_id,
                        application_channel,
                        trading_account_id,
                        trading_account_type,
                        visitor_id,
                        straight_through_processing,
                        click_id,
                        is_metatrader_2fa_requested
                      )
                    SELECT /*+ CARDINALITY (cust_reg_tab 3) */  p_user,
                        SYSTIMESTAMP,
                        p_user,
                        SYSTIMESTAMP,
                        p_logical_load_timestamp,
                        p_effective_start_timestamp,
                        p_customer_id,
                        cust_reg_tab.registration_id,
                        cust_reg_tab.registration_version,
                        cust_reg_tab.sales_channel,
                        cust_reg_tab.application_file_reference,
                        cust_reg_tab.ip_address,
                        cust_reg_tab.device_id,
                        cust_reg_tab.application_channel,
                        cust_reg_tab.trading_account_id,
                        'CUSTOMER',
                        cust_reg_tab.visitor_id,
                        cust_reg_tab.straight_through_processing,
                        cust_reg_tab.click_id,
                        cust_reg_tab.is_metatrader_2fa_requested
                    FROM   TABLE(CAST(p_customer_registrations AS customer_registrations_tab)) cust_reg_tab
                    WHERE  NOT EXISTS (SELECT 1
                              FROM customer_registrations
                              WHERE registration_id = cust_reg_tab.registration_id);
      WHEN p_effective_start_timestamp >= p_old_effective_start_time THEN
        --
        -- Update the data that has got updated as compared to the previous version
      --
      SELECT /*+ CARDINALITY (new_version 3) */old_version.registration_id,
               old_version.logical_load_timestamp,
               old_version.created_by,
               old_version.create_timestamp,
               old_version.updated_by,
               old_version.update_timestamp,
               old_version.effective_start_timestamp,
               old_version.customer_id,
               old_version.registration_version,
               old_version.sales_channel,
               old_version.application_file_reference,
               old_version.ip_address,
               old_version.device_id,
               old_version.application_channel,
               old_version.Trading_Account_Id,
               old_version.Trading_Account_Type,
               old_version.Visitor_Id,
               old_version.straight_through_processing,
               old_version.click_id,
               old_version.is_metatrader_2fa_requested
      BULK COLLECT INTO ltab_updated_cust_reg_old
      FROM    customer_registrations old_version,
               TABLE(CAST(p_customer_registrations AS customer_registrations_tab)) new_version
      WHERE  old_version.registration_id = new_version.registration_id AND
         ( nrg_common.has_value_changed(old_version.registration_version,new_version.registration_version) =1 OR
                 nrg_common.has_value_changed(old_version.sales_channel,new_version.sales_channel) =1 OR
                 nrg_common.has_value_changed(old_version.application_file_reference,new_version.application_file_reference) =1 OR
                 nrg_common.has_value_changed(old_version.ip_address,new_version.ip_address) =1 OR
                 nrg_common.has_value_changed(old_version.device_id,new_version.device_id) =1 or
                 nrg_common.has_value_changed(old_version.application_channel,new_version.application_channel) =1 OR
                 nrg_common.has_value_changed(old_version.customer_id,p_customer_id) =1 OR
                 nrg_common.has_value_changed(old_version.Trading_Account_Id,new_version.Trading_Account_Id) =1  OR
                 nrg_common.has_value_changed(old_version.trading_account_type, 'CUSTOMER') =1 OR
                 nrg_common.has_value_changed(old_version.Visitor_Id, new_version.Visitor_Id) = 1 OR
                 nrg_common.has_value_changed(old_version.straight_through_processing, new_version.straight_through_processing) = 1 OR
                 nrg_common.has_value_changed(old_version.click_id, new_version.click_id) = 1 OR
                 nrg_common.has_value_changed(old_version.is_metatrader_2fa_requested, new_version.is_metatrader_2fa_requested) = 1)
            FOR UPDATE OF old_version.registration_id;
            --
            -- The data that has got updated, move it to the history
            --
      FOR lv_count IN 1..ltab_updated_cust_reg_old.COUNT LOOP
                put_history(p_old_cust_reg               => ltab_updated_cust_reg_old(lv_count),
                            p_effective_end_timestamp    => p_effective_start_timestamp,
                            p_action                     => 'U');
            END LOOP;
       --
            --Insert/Update the new data
            --
            MERGE /*+ CARDINALITY (new_version 3) */ INTO customer_registrations old_version
            USING (SELECT *
                   FROM   TABLE(CAST(p_customer_registrations AS customer_registrations_tab))) new_version
            ON (old_version.registration_id = new_version.registration_id)
            WHEN MATCHED THEN
              UPDATE
              SET
                 updated_by                 = p_user,
                 update_timestamp           = SYSTIMESTAMP,
                 logical_load_timestamp     = p_logical_load_timestamp,
                 effective_start_timestamp  = p_effective_start_timestamp,
                 registration_version       = new_version.registration_version,
                 sales_channel              = new_version.sales_channel,
                 application_file_reference = new_version.application_file_reference,
                 ip_address                 = new_version.ip_address,
                 device_id                  = new_version.device_id,
                 application_channel        = new_version.application_channel,
                 customer_id                = p_customer_id,
                 trading_account_id         = new_version.trading_account_id,
                 trading_account_type       = 'CUSTOMER',
                 visitor_id                 = new_version.visitor_id,
                 straight_through_processing = new_version.straight_through_processing,
                 click_id                    = new_version.click_id,
                 is_metatrader_2fa_requested = new_version.is_metatrader_2fa_requested
              WHERE ( nrg_common.has_value_changed(old_version.registration_version,new_version.registration_version) =1 OR
                      nrg_common.has_value_changed(old_version.sales_channel,new_version.sales_channel) =1 OR
                      nrg_common.has_value_changed(old_version.application_file_reference,new_version.application_file_reference) =1 OR
                      nrg_common.has_value_changed(old_version.ip_address,new_version.ip_address) =1 OR
                      nrg_common.has_value_changed(old_version.device_id,new_version.device_id) =1 OR
                      nrg_common.has_value_changed(old_version.application_channel,new_version.application_channel) =1 OR
                      nrg_common.has_value_changed(old_version.customer_id,p_customer_id) =1  OR
                      nrg_common.has_value_changed(old_version.trading_account_id,old_version.trading_account_id) =1  OR
                      nrg_common.has_value_changed(old_version.trading_account_type, 'CUSTOMER') = 1 OR
                      nrg_common.has_value_changed(old_version.visitor_id, new_version.visitor_id) = 1 OR
                      nrg_common.has_value_changed(old_version.straight_through_processing, new_version.straight_through_processing) = 1 OR
                      nrg_common.has_value_changed(old_version.click_id, new_version.click_id) = 1 OR
                      nrg_common.has_value_changed(old_version.is_metatrader_2fa_requested, new_version.is_metatrader_2fa_requested) = 1
                      )
            WHEN NOT MATCHED THEN
              INSERT
                    (
                        created_by,
                        create_timestamp,
                        updated_by,
                        update_timestamp,
                        logical_load_timestamp,
                        effective_start_timestamp,
                        customer_id,
                        registration_id,
                        registration_version,
                        sales_channel,
                        application_file_reference,
                        ip_address,
                        device_id,
                        application_channel,
                        trading_account_id,
                        trading_account_type,
                        visitor_id,
                        straight_through_processing,
                        click_id,
                        is_metatrader_2fa_requested
                    )
              VALUES(
                  p_user,
                        SYSTIMESTAMP,
                        p_user,
                        SYSTIMESTAMP,
                        p_logical_load_timestamp,
                        p_effective_start_timestamp,
                        p_customer_id,
                        new_version.registration_id,
                        new_version.registration_version,
                        new_version.sales_channel,
                        new_version.application_file_reference,
                        new_version.ip_address,
                        new_version.device_id,
                        new_version.application_channel,
                        new_version.trading_account_id,
                        'CUSTOMER',
                        new_version.visitor_id,
                        new_version.straight_through_processing,
                        new_version.click_id,
                        new_version.is_metatrader_2fa_requested
          );
            --
            -- Delete the data that has got deleted in this version as compare to the previous version
            --
            SELECT *
            BULK COLLECT INTO ltab_deleted_cust_reg
            FROM customer_registrations
            WHERE customer_id = p_customer_id AND
                  registration_id NOT IN (SELECT registration_id
                                          FROM   TABLE(CAST(p_customer_registrations AS customer_registrations_tab)))
            FOR UPDATE;
            FOR lv_count IN 1..ltab_deleted_cust_reg.COUNT LOOP
                put_history(p_old_cust_reg            => ltab_deleted_cust_reg(lv_count),
                            p_effective_end_timestamp => p_effective_start_timestamp,
                            p_action                  => 'D');
            END LOOP;
            FORALL lv_count IN 1..ltab_deleted_cust_reg.COUNT
                DELETE customer_registrations
                WHERE registration_id = ltab_deleted_cust_reg(lv_count).registration_id;
        WHEN p_effective_start_timestamp < p_old_effective_start_time THEN
          --
            -- Process out of sequence data/replayed messages
            --
            SELECT/*+ CARDINALITY (cust_reg_tab 3) */
               registration_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_customer_id,
               registration_version,
               sales_channel,
               application_file_reference,
               ip_address,
               device_id,
               application_channel,
               trading_account_id,
               'CUSTOMER',
               visitor_id,
               straight_through_processing,
               click_id,
               is_metatrader_2fa_requested
            BULK COLLECT INTO ltab_updated_cust_reg
            FROM   TABLE(CAST(p_customer_registrations AS customer_registrations_tab)) cust_reg_tab
            WHERE
         --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
                 (registration_version,
                  sales_channel,
                  application_file_reference,
                  ip_address,
                  device_id,
                  application_channel,
                  trading_account_id,
                  visitor_id,
                  straight_through_processing,
                  click_id,
                  is_metatrader_2fa_requested) NOT IN (SELECT registration_version,
                                                              sales_channel,
                                                              application_file_reference,
                                                              ip_address, device_id,
                                                              application_channel,
                                                              trading_account_id,
                                                              visitor_id,
                                                              straight_through_processing,
                                                              click_id,
                                                              is_metatrader_2fa_requested
                                                         FROM customer_registrations
                                                        WHERE customer_id = p_customer_id AND
                                                              effective_start_timestamp <= p_effective_start_timestamp
                                                     UNION ALL
                                                       SELECT registration_version,
                                                              sales_channel,
                                                              application_file_reference,
                                                              ip_address, device_id,
                                                              application_channel,
                                                              trading_account_id,
                                                              visitor_id,
                                                              straight_through_processing,
                                                              click_id,
                                                              is_metatrader_2fa_requested
                                                         FROM customer_registrations_h
                                                        WHERE customer_id = p_customer_id AND
                                                              effective_start_timestamp <= p_effective_start_timestamp AND
                                                              effective_end_timestamp > p_effective_start_timestamp);

            FOR lv_cnt IN 1..ltab_updated_cust_reg.COUNT LOOP
                put_history(p_old_cust_reg            => ltab_updated_cust_reg(lv_cnt),
                            p_effective_end_timestamp => p_effective_start_timestamp,
                            p_action                  => 'I');
            END LOOP;
      END CASE;
  END put_customer_registrations;

    -- ===================================================================================
    -- put_german_tax_church_rates
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Appropriateness Infos
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     customer_Apprprtnss_info
    --     customer_Apprprtnss_info_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_customer_Apprprtnss_info       Customer Appropriateness Infos
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_german_tax_church_rates(p_user                        customer_tax_church_rates.created_by%TYPE,
                                          p_logical_load_timestamp      customer_tax_church_rates.logical_load_timestamp%TYPE,
                                          p_effective_start_timestamp   customer_tax_church_rates.effective_start_timestamp%TYPE,
                                          p_customer_tax_germany_id     customer_tax_church_rates.customer_tax_germany_id%TYPE,
                                          p_old_customer_version        customers.customer_version%TYPE,
                                          p_new_customer_version        customers.customer_version%TYPE,
                                          p_customer_tax_church_rates   customer_tax_church_rates_tab) IS

      TYPE l_old_records_tab IS TABLE OF customer_tax_church_rates%ROWTYPE;
      lv_old_records l_old_records_tab;
      lv_diocese_exists NUMBER;
      ltab_dioceses german_dioceses_tab;

    BEGIN

      logger.logger.set_module('put_german_tax');

      CASE

        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM TABLE(CAST(p_customer_tax_church_rates AS customer_tax_church_rates_tab)) new_records,
                 customer_tax_church_rates old_records
           WHERE old_records.customer_tax_germany_id   = p_customer_tax_germany_id   AND
                 old_records.church_rates_id           = new_records.church_rates_id AND
                (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                 nrg_common.has_value_changed(old_records.tax_rate, new_records.tax_rate) = 1 OR
                 nrg_common.has_value_changed(old_records.diocese, new_records.diocese) = 1 OR
                 nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'U',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record_ch             => lv_old_records(lv_cnt));
          END LOOP;

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM customer_tax_church_rates old_records
           WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                 church_rates_id NOT IN (SELECT church_rates_id
                                         FROM TABLE(CAST(p_customer_tax_church_rates AS customer_tax_church_rates_tab)));

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'D',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record_ch             => lv_old_records(lv_cnt));
          END LOOP;

          DELETE customer_tax_church_rates old_records
          WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                church_rates_id NOT IN (SELECT church_rates_id
                                        FROM TABLE(CAST(p_customer_tax_church_rates AS customer_tax_church_rates_tab)));

          IF p_customer_tax_church_rates IS NOT NULL THEN
            FOR lv_count in 1.. p_customer_tax_church_rates.count LOOP
              IF p_customer_tax_church_rates(lv_count).diocese IS NOT NULL THEN

                SELECT CASE WHEN EXISTS (SELECT 1
                                           FROM german_dioceses
                                          WHERE diocese_number = p_customer_tax_church_rates(lv_count).diocese)
                       THEN 1 ELSE 0 END
                  INTO lv_diocese_exists
                  FROM dual;

                IF lv_diocese_exists = 0 THEN

                  SELECT german_dioceses_obj(null,
                                             null,
                                             p_customer_tax_church_rates(lv_count).diocese)
                    BULK COLLECT INTO ltab_dioceses
                    FROM dual;

                    put_dioceses(p_user                      => 'NRG',
                                 p_effective_start_timestamp => p_effective_start_timestamp,
                                 p_german_dioceses           => ltab_dioceses);

                END IF;
              END IF;
            END LOOP;
          END IF;

          MERGE INTO customer_tax_church_rates old_records USING
               (SELECT church_rates_id,
                       p_user                         created_by,
                       SYSTIMESTAMP                   message_time,
                       p_effective_start_timestamp    effective_start_timestamp,
                       p_customer_tax_germany_id      customer_tax_germany_id,
                       tax_year,
                       tax_rate,
                       diocese,
                       is_deleted
                  FROM TABLE(CAST(p_customer_tax_church_rates AS customer_tax_church_rates_tab)) aa) new_records
              ON (old_records.customer_tax_germany_id   = new_records.customer_tax_germany_id AND
                  old_records.church_rates_id           = new_records.church_rates_id)
          WHEN MATCHED THEN UPDATE
              SET old_records.updated_by                    = new_records.created_by,
                  old_records.update_timestamp              = new_records.message_time,
                  old_records.logical_load_timestamp        = SYSTIMESTAMP,
                  old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                  old_records.tax_year                      = new_records.tax_year,
                  old_records.tax_rate                      = new_records.tax_rate,
                  old_records.diocese                       = new_records.diocese,
                  old_records.is_deleted                    = new_records.is_deleted
           WHERE (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                  nrg_common.has_value_changed(old_records.tax_rate, new_records.tax_rate) = 1 OR
                  nrg_common.has_value_changed(old_records.diocese, new_records.diocese) = 1 OR
                  nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1)
          WHEN NOT MATCHED THEN INSERT
                 (church_rates_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  customer_tax_germany_id,
                  tax_year,
                  tax_rate,
                  diocese,
                  is_deleted)
          VALUES (new_records.church_rates_id,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.customer_tax_germany_id,
                  new_records.tax_year,
                  new_records.tax_rate,
                  new_records.diocese,
                  new_records.is_deleted);

        WHEN p_old_customer_version > p_new_customer_version THEN

           SELECT church_rates_id,
                  SYSTIMESTAMP logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  p_effective_start_timestamp,
                  p_customer_tax_germany_id,
                  tax_year,
                  tax_rate,
                  diocese,
                  is_deleted
             BULK COLLECT INTO lv_old_records
             FROM TABLE(CAST(p_customer_tax_church_rates AS customer_tax_church_rates_tab))
            WHERE (church_rates_id,
                   tax_year,
                   tax_rate,
                   diocese,
                   is_deleted) NOT IN (SELECT church_rates_id,
                                           tax_year,
                                           tax_rate,
                                           diocese,
                                           is_deleted
                                      FROM customer_tax_church_rates
                                     WHERE customer_tax_germany_id = p_customer_tax_germany_id
                                     UNION ALL
                                    SELECT church_rates_id,
                                           tax_year,
                                           tax_rate,
                                           diocese,
                                           is_deleted
                                      FROM customer_tax_church_rates_h
                                     WHERE customer_tax_germany_id = p_customer_tax_germany_id);
          --
          --Write missing records to history
          --
          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record_ch             => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;
      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- put_german_tax_church_rates
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Appropriateness Infos
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     customer_Apprprtnss_info
    --     customer_Apprprtnss_info_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_customer_Apprprtnss_info       Customer Appropriateness Infos
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_german_tax_couples_rel(p_user                        german_tax_couples_rel.created_by%TYPE,
                                         p_logical_load_timestamp      german_tax_couples_rel.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp   german_tax_couples_rel.effective_start_timestamp%TYPE,
                                         p_couples_tax_free_allow_id   german_tax_couples_rel.couples_tax_free_allow_id%TYPE,
                                         p_old_customer_version        customers.customer_version%TYPE,
                                         p_new_customer_version        customers.customer_version%TYPE,
                                         p_german_tax_couples_rel      german_tax_couples_rel_tab) IS

      TYPE l_old_records_tab IS TABLE OF german_tax_couples_rel%ROWTYPE;
      lv_old_records l_old_records_tab;

    BEGIN

      logger.logger.set_module('put_german_tax');

      CASE

        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM TABLE(CAST(p_german_tax_couples_rel AS german_tax_couples_rel_tab)) new_records,
                 german_tax_couples_rel old_records
           WHERE old_records.couples_tax_free_allow_id = p_couples_tax_free_allow_id AND
                 old_records.customer_id               = new_records.customer_id;

        /*FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'U',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;*/

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM german_tax_couples_rel old_records
           WHERE old_records.couples_tax_free_allow_id = p_couples_tax_free_allow_id AND
                 customer_id NOT IN (SELECT customer_id
                                       FROM TABLE(CAST(p_german_tax_couples_rel AS german_tax_couples_rel_tab)));

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'D',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;

          DELETE german_tax_couples_rel old_records
           WHERE old_records.couples_tax_free_allow_id = p_couples_tax_free_allow_id AND
                 customer_id NOT IN (SELECT customer_id
                                       FROM TABLE(CAST(p_german_tax_couples_rel AS german_tax_couples_rel_tab)));

          MERGE INTO german_tax_couples_rel old_records USING
               (SELECT DISTINCT p_couples_tax_free_allow_id    couples_tax_free_allow_id,
                       p_user                         created_by,
                       SYSTIMESTAMP                   message_time,
                       p_effective_start_timestamp    effective_start_timestamp,
                       customer_id                    customer_id
                  FROM TABLE(CAST(p_german_tax_couples_rel AS german_tax_couples_rel_tab)) aa) new_records
              ON (old_records.customer_id                  = new_records.customer_id AND
                  old_records.couples_tax_free_allow_id    = p_couples_tax_free_allow_id)
          WHEN NOT MATCHED THEN INSERT
                 (couples_tax_free_allow_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  customer_id)
          VALUES (p_couples_tax_free_allow_id,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.customer_id);

        WHEN p_old_customer_version > p_new_customer_version THEN

           SELECT p_couples_tax_free_allow_id,
                  customer_id,
                  SYSTIMESTAMP logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  p_effective_start_timestamp
             BULK COLLECT INTO lv_old_records
             FROM TABLE(CAST(p_german_tax_couples_rel AS german_tax_couples_rel_tab))
            WHERE (customer_id) NOT IN (SELECT customer_id
                                          FROM german_tax_couples_rel
                                         WHERE couples_tax_free_allow_id = p_couples_tax_free_allow_id
                                         UNION ALL
                                        SELECT customer_id
                                          FROM german_tax_couples_rel_h
                                         WHERE couples_tax_free_allow_id = p_couples_tax_free_allow_id);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;
      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- put_german_tax_church_rates
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Appropriateness Infos
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     customer_Apprprtnss_info
    --     customer_Apprprtnss_info_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_customer_Apprprtnss_info       Customer Appropriateness Infos
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_german_tax_psl_allow(p_user                        cst_tx_ger_psl_tx_fr_allw.created_by%TYPE,
                                       p_logical_load_timestamp      cst_tx_ger_psl_tx_fr_allw.logical_load_timestamp%TYPE,
                                       p_effective_start_timestamp   cst_tx_ger_psl_tx_fr_allw.effective_start_timestamp%TYPE,
                                       p_customer_tax_germany_id     cst_tx_ger_psl_tx_fr_allw.customer_tax_germany_id%TYPE,
                                       p_old_customer_version        customers.customer_version%TYPE,
                                       p_new_customer_version        customers.customer_version%TYPE,
                                       p_cst_tx_ger_psl_tx_fr_allw   cst_tx_ger_psl_tx_fr_allw_tab) IS

      TYPE l_old_records_tab IS TABLE OF cst_tx_ger_psl_tx_fr_allw%ROWTYPE;
      lv_old_records l_old_records_tab;

    BEGIN

      logger.logger.set_module('put_german_tax');

      CASE

        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM TABLE(CAST(p_cst_tx_ger_psl_tx_fr_allw AS cst_tx_ger_psl_tx_fr_allw_tab)) new_records,
                 cst_tx_ger_psl_tx_fr_allw old_records
           WHERE old_records.customer_tax_germany_id    = p_customer_tax_germany_id   AND
                 old_records.personal_tax_free_allow_id = new_records.personal_tax_free_allow_id AND
                (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                 nrg_common.has_value_changed(old_records.tax_free_allowance, new_records.tax_free_allowance) = 1 OR
                 nrg_common.has_value_changed(old_records.is_auto_roll, new_records.is_auto_roll) = 1 OR
                 nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1 );

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history_psl(p_action                    => 'U',
                            p_effective_end_timestamp   => p_effective_start_timestamp,
                            p_old_record                => lv_old_records(lv_cnt));
          END LOOP;

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM cst_tx_ger_psl_tx_fr_allw old_records
           WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                 personal_tax_free_allow_id NOT IN (SELECT personal_tax_free_allow_id
                                         FROM TABLE(CAST(p_cst_tx_ger_psl_tx_fr_allw AS cst_tx_ger_psl_tx_fr_allw_tab)));

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history_psl(p_action                    => 'D',
                            p_effective_end_timestamp   => p_effective_start_timestamp,
                            p_old_record                => lv_old_records(lv_cnt));
          END LOOP;

          DELETE cst_tx_ger_psl_tx_fr_allw old_records
          WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                personal_tax_free_allow_id NOT IN (SELECT personal_tax_free_allow_id
                                        FROM TABLE(CAST(p_cst_tx_ger_psl_tx_fr_allw AS cst_tx_ger_psl_tx_fr_allw_tab)));

          MERGE INTO cst_tx_ger_psl_tx_fr_allw old_records USING
               (SELECT personal_tax_free_allow_id,
                       p_user                         created_by,
                       SYSTIMESTAMP                   message_time,
                       p_effective_start_timestamp    effective_start_timestamp,
                       p_customer_tax_germany_id      customer_tax_germany_id,
                       tax_year,
                       tax_free_allowance,
                       is_auto_roll,
                       is_deleted
                  FROM TABLE(CAST(p_cst_tx_ger_psl_tx_fr_allw AS cst_tx_ger_psl_tx_fr_allw_tab)) aa) new_records
              ON (old_records.customer_tax_germany_id    = new_records.customer_tax_germany_id AND
                  old_records.personal_tax_free_allow_id = new_records.personal_tax_free_allow_id)
          WHEN MATCHED THEN UPDATE
              SET old_records.updated_by                    = new_records.created_by,
                  old_records.update_timestamp              = new_records.message_time,
                  old_records.logical_load_timestamp        = SYSTIMESTAMP,
                  old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                  old_records.tax_year                      = new_records.tax_year,
                  old_records.tax_free_allowance            = new_records.tax_free_allowance,
                  old_records.is_auto_roll                  = new_records.is_auto_roll,
                  old_records.is_deleted                    = new_records.is_deleted
           WHERE (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                  nrg_common.has_value_changed(old_records.tax_free_allowance, new_records.tax_free_allowance) = 1 OR
                  nrg_common.has_value_changed(old_records.is_auto_roll, new_records.is_auto_roll) = 1 OR
                  nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1 )
          WHEN NOT MATCHED THEN INSERT
                 (personal_tax_free_allow_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  customer_tax_germany_id,
                  tax_year,
                  tax_free_allowance,
                  is_auto_roll,
                  is_deleted)
          VALUES (new_records.personal_tax_free_allow_id,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.customer_tax_germany_id,
                  new_records.tax_year,
                  new_records.tax_free_allowance,
                  new_records.is_auto_roll,
                  new_records.is_deleted);

        WHEN p_old_customer_version > p_new_customer_version THEN

           SELECT personal_tax_free_allow_id,
                  SYSTIMESTAMP logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  p_effective_start_timestamp,
                  p_customer_tax_germany_id,
                  tax_year,
                  tax_free_allowance,
                  is_auto_roll,
                  is_deleted
             BULK COLLECT INTO lv_old_records
             FROM TABLE(CAST(p_cst_tx_ger_psl_tx_fr_allw AS cst_tx_ger_psl_tx_fr_allw_tab))
            WHERE (personal_tax_free_allow_id,
                   tax_year,
                   tax_free_allowance,
                   is_auto_roll,
                   is_deleted) NOT IN (SELECT personal_tax_free_allow_id,
                                            tax_year,
                                            tax_free_allowance,
                                            is_auto_roll,
                                            is_deleted
                                       FROM cst_tx_ger_psl_tx_fr_allw
                                      WHERE customer_tax_germany_id = p_customer_tax_germany_id
                                      UNION ALL
                                     SELECT personal_tax_free_allow_id,
                                            tax_year,
                                            tax_free_allowance,
                                            is_auto_roll,
                                            is_deleted
                                       FROM cst_tx_ger_psl_tx_fr_allw_h
                                      WHERE customer_tax_germany_id = p_customer_tax_germany_id);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history_psl(p_action                    => 'I',
                            p_effective_end_timestamp   => p_effective_start_timestamp,
                            p_old_record                => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;
      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- put_german_tax_church_rates
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Appropriateness Infos
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     customer_Apprprtnss_info
    --     customer_Apprprtnss_info_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_customer_Apprprtnss_info       Customer Appropriateness Infos
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_german_tax_cpl_allow(p_user                        cst_tx_ger_cpl_tx_fr_allw.created_by%TYPE,
                                       p_logical_load_timestamp      cst_tx_ger_cpl_tx_fr_allw.logical_load_timestamp%TYPE,
                                       p_effective_start_timestamp   cst_tx_ger_cpl_tx_fr_allw.effective_start_timestamp%TYPE,
                                       p_customer_tax_germany_id     cst_tx_ger_cpl_tx_fr_allw.customer_tax_germany_id%TYPE,
                                       p_old_customer_version        customers.customer_version%TYPE,
                                       p_new_customer_version        customers.customer_version%TYPE,
                                       p_cst_tx_ger_cpl_tx_fr_allw   cst_tx_ger_cpl_tx_fr_allw_tab) IS

      TYPE l_old_records_tab IS TABLE OF cst_tx_ger_cpl_tx_fr_allw%ROWTYPE;
      lv_old_records l_old_records_tab;

    BEGIN

      logger.logger.set_module('put_german_tax');

      CASE

        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM TABLE(CAST(p_cst_tx_ger_cpl_tx_fr_allw AS cst_tx_ger_cpl_tx_fr_allw_tab)) new_records,
                 cst_tx_ger_cpl_tx_fr_allw old_records
           WHERE old_records.customer_tax_germany_id   = p_customer_tax_germany_id   AND
                 old_records.couples_tax_free_allow_id = new_records.couples_tax_free_allow_id AND
                (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                 nrg_common.has_value_changed(old_records.tax_free_allowance, new_records.tax_free_allowance) = 1 OR
                 nrg_common.has_value_changed(old_records.is_auto_roll, new_records.is_auto_roll) = 1 OR
                 nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1 );

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'U',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM cst_tx_ger_cpl_tx_fr_allw old_records
           WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                 couples_tax_free_allow_id NOT IN (SELECT couples_tax_free_allow_id
                                         FROM TABLE(CAST(p_cst_tx_ger_cpl_tx_fr_allw AS cst_tx_ger_cpl_tx_fr_allw_tab)));

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'D',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;

          DELETE cst_tx_ger_cpl_tx_fr_allw old_records
          WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                couples_tax_free_allow_id NOT IN (SELECT couples_tax_free_allow_id
                                        FROM TABLE(CAST(p_cst_tx_ger_cpl_tx_fr_allw AS cst_tx_ger_cpl_tx_fr_allw_tab)));

          MERGE INTO cst_tx_ger_cpl_tx_fr_allw old_records USING
               (SELECT DISTINCT couples_tax_free_allow_id,
                       p_user                         created_by,
                       SYSTIMESTAMP                   message_time,
                       p_effective_start_timestamp    effective_start_timestamp,
                       p_customer_tax_germany_id      customer_tax_germany_id,
                       tax_year,
                       tax_free_allowance,
                       is_auto_roll,
                       is_deleted
                  FROM TABLE(CAST(p_cst_tx_ger_cpl_tx_fr_allw AS cst_tx_ger_cpl_tx_fr_allw_tab)) aa) new_records
              ON (old_records.customer_tax_germany_id   = new_records.customer_tax_germany_id AND
                  old_records.couples_tax_free_allow_id = new_records.couples_tax_free_allow_id)
          WHEN MATCHED THEN UPDATE
              SET old_records.updated_by                    = new_records.created_by,
                  old_records.update_timestamp              = new_records.message_time,
                  old_records.logical_load_timestamp        = SYSTIMESTAMP,
                  old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                  old_records.tax_year                      = new_records.tax_year,
                  old_records.tax_free_allowance            = new_records.tax_free_allowance,
                  old_records.is_auto_roll                  = new_records.is_auto_roll,
                  old_records.is_deleted                    = new_records.is_deleted
           WHERE (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                  nrg_common.has_value_changed(old_records.tax_free_allowance, new_records.tax_free_allowance) = 1OR
                  nrg_common.has_value_changed(old_records.is_auto_roll, new_records.is_auto_roll) = 1 OR
                  nrg_common.has_value_changed(old_records.is_deleted, new_records.is_deleted) = 1 )
          WHEN NOT MATCHED THEN INSERT
                 (couples_tax_free_allow_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  customer_tax_germany_id,
                  tax_year,
                  tax_free_allowance,
                  is_auto_roll,
                  is_deleted)
          VALUES (new_records.couples_tax_free_allow_id,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.customer_tax_germany_id,
                  new_records.tax_year,
                  new_records.tax_free_allowance,
                  new_records.is_auto_roll,
                  new_records.is_deleted);

        WHEN p_old_customer_version > p_new_customer_version THEN

           SELECT couples_tax_free_allow_id,
                  SYSTIMESTAMP logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  p_effective_start_timestamp,
                  p_customer_tax_germany_id,
                  tax_year,
                  tax_free_allowance,
                  is_auto_roll,
                  is_deleted
             BULK COLLECT INTO lv_old_records
             FROM TABLE(CAST(p_cst_tx_ger_cpl_tx_fr_allw AS cst_tx_ger_cpl_tx_fr_allw_tab))
            WHERE (couples_tax_free_allow_id,
                   tax_year,
                   tax_free_allowance,
                   is_auto_roll,
                   is_deleted) NOT IN (SELECT couples_tax_free_allow_id,
                                                   tax_year,
                                                   tax_free_allowance,
                                                   is_auto_roll,
                                                   is_deleted
                                              FROM cst_tx_ger_cpl_tx_fr_allw
                                             WHERE customer_tax_germany_id = p_customer_tax_germany_id
                                             UNION ALL
                                            SELECT couples_tax_free_allow_id,
                                                   tax_year,
                                                   tax_free_allowance,
                                                   is_auto_roll,
                                                   is_deleted
                                              FROM cst_tx_ger_cpl_tx_fr_allw_h
                                             WHERE customer_tax_germany_id = p_customer_tax_germany_id);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;

      IF p_cst_tx_ger_cpl_tx_fr_allw IS NOT NULL THEN

        FOR lv_count in 1.. p_cst_tx_ger_cpl_tx_fr_allw.count LOOP

          put_german_tax_couples_rel (p_user                          => p_user,
                                      p_logical_load_timestamp        => p_logical_load_timestamp,
                                      p_effective_start_timestamp     => p_effective_start_timestamp,
                                      p_couples_tax_free_allow_id     => p_cst_tx_ger_cpl_tx_fr_allw(lv_count).couples_tax_free_allow_id,
                                      p_old_customer_version          => p_old_customer_version,
                                      p_new_customer_version          => p_new_customer_version,
                                      p_german_tax_couples_rel        => p_cst_tx_ger_cpl_tx_fr_allw(lv_count).german_tax_couples_rel);

        END LOOP;

      END IF;

      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

    -- ===================================================================================
    -- put_german_tax_roll_losses
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer german_tax_roll_losses 
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     german_tax_roll_losses
    --     german_tax_roll_losses_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version           Account Version of the existing record on ODS
    --     p_new_customer_version           Account Version of the new record that is received
    --     p_german_tax_roll_losses         german_tax_roll_losses
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_german_tax_roll_losses (p_user                        german_tax_roll_losses.created_by%TYPE,
                                          p_logical_load_timestamp      german_tax_roll_losses.logical_load_timestamp%TYPE,
                                          p_effective_start_timestamp   german_tax_roll_losses.effective_start_timestamp%TYPE,
                                          p_customer_tax_germany_id     german_tax_roll_losses.customer_tax_germany_id%TYPE,
                                          p_old_customer_version        customers.customer_version%TYPE,
                                          p_new_customer_version        customers.customer_version%TYPE,
                                          p_german_tax_roll_losses      german_tax_roll_losses_tab) IS

      TYPE l_old_records_tab IS TABLE OF german_tax_roll_losses%ROWTYPE;
      lv_old_records l_old_records_tab;
      lv_diocese_exists NUMBER;
      ltab_dioceses german_dioceses_tab;

    BEGIN

      logger.logger.set_module('put_german_tax');

      CASE

        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM TABLE(CAST(p_german_tax_roll_losses AS german_tax_roll_losses_tab)) new_records,
                 german_tax_roll_losses old_records
           WHERE old_records.customer_tax_germany_id   = p_customer_tax_germany_id   AND
                 old_records.roll_losses_id           = new_records.roll_losses_id AND
                (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                 nrg_common.has_value_changed(old_records.roll_losses_at_end_of_year, new_records.roll_losses_at_end_of_year) = 1);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'U',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record_ch             => lv_old_records(lv_cnt));
          END LOOP;

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM german_tax_roll_losses old_records
           WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                 roll_losses_id NOT IN (SELECT roll_losses_id
                                         FROM TABLE(CAST(p_german_tax_roll_losses AS german_tax_roll_losses_tab)));

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'D',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record_ch             => lv_old_records(lv_cnt));
          END LOOP;

          DELETE german_tax_roll_losses old_records
          WHERE customer_tax_germany_id = p_customer_tax_germany_id AND
                roll_losses_id NOT IN (SELECT roll_losses_id
                                        FROM TABLE(CAST(p_german_tax_roll_losses AS german_tax_roll_losses_tab)));

          MERGE INTO german_tax_roll_losses old_records USING
               (SELECT roll_losses_id,
                       p_user                         created_by,
                       SYSTIMESTAMP                   message_time,
                       p_effective_start_timestamp    effective_start_timestamp,
                       p_customer_tax_germany_id      customer_tax_germany_id,
                       tax_year,
                       roll_losses_at_end_of_year
                  FROM TABLE(CAST(p_german_tax_roll_losses AS german_tax_roll_losses_tab)) aa) new_records
              ON (old_records.customer_tax_germany_id   = new_records.customer_tax_germany_id AND
                  old_records.roll_losses_id           = new_records.roll_losses_id)
          WHEN MATCHED THEN UPDATE
              SET old_records.updated_by                    = new_records.created_by,
                  old_records.update_timestamp              = new_records.message_time,
                  old_records.logical_load_timestamp        = SYSTIMESTAMP,
                  old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                  old_records.tax_year                      = new_records.tax_year,
                  old_records.roll_losses_at_end_of_year    = new_records.roll_losses_at_end_of_year
           WHERE (nrg_common.has_value_changed(old_records.tax_year, new_records.tax_year) = 1 OR
                  nrg_common.has_value_changed(old_records.roll_losses_at_end_of_year, new_records.roll_losses_at_end_of_year) = 1)
          WHEN NOT MATCHED THEN INSERT
                 (roll_losses_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  customer_tax_germany_id,
                  tax_year,
                  roll_losses_at_end_of_year)
          VALUES (new_records.roll_losses_id,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.customer_tax_germany_id,
                  new_records.tax_year,
                  new_records.roll_losses_at_end_of_year);

        WHEN p_old_customer_version > p_new_customer_version THEN

           SELECT roll_losses_id,
                  SYSTIMESTAMP logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  p_effective_start_timestamp,
                  p_customer_tax_germany_id,
                  tax_year,
                  roll_losses_at_end_of_year
             BULK COLLECT INTO lv_old_records
             FROM TABLE(CAST(p_german_tax_roll_losses AS german_tax_roll_losses_tab))
            WHERE (roll_losses_id,
                   tax_year,
                   roll_losses_at_end_of_year) NOT IN (SELECT roll_losses_id,
                                                              tax_year,
                                                              roll_losses_at_end_of_year
                                                         FROM german_tax_roll_losses
                                                        WHERE customer_tax_germany_id = p_customer_tax_germany_id
                                                        UNION ALL
                                                       SELECT roll_losses_id,
                                                              tax_year,
                                                              roll_losses_at_end_of_year
                                                         FROM german_tax_roll_losses_h
                                                        WHERE customer_tax_germany_id = p_customer_tax_germany_id);
          --
          --Write missing records to history
          --
          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record_ch             => lv_old_records(lv_cnt));
          END LOOP;
      END CASE;
      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;
    
    -- ===================================================================================
    -- put_german_tax
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put Customer Appropriateness Infos
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     customer_Apprprtnss_info
    --     customer_Apprprtnss_info_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    customer id
    --     p_old_customer_version            Account Version of the existing record on ODS
    --     p_new_customer_version            Account Version of the new record that is received
    --     p_customer_Apprprtnss_info       Customer Appropriateness Infos
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_german_tax (p_user                        customer_tax_germany.created_by%TYPE,
                              p_logical_load_timestamp      customer_tax_germany.logical_load_timestamp%TYPE,
                              p_effective_start_timestamp   customer_tax_germany.effective_start_timestamp%TYPE,
                              p_customer_id                 customers.customer_id%TYPE,
                              p_old_customer_version        customers.customer_version%TYPE,
                              p_new_customer_version        customers.customer_version%TYPE,
                              p_customer_tax_germany        customer_tax_germany_tab) IS

      TYPE l_old_records_tab IS TABLE OF customer_tax_germany%ROWTYPE;
      lv_old_records l_old_records_tab;

    BEGIN

      logger.logger.set_module('put_german_tax');

      CASE

        WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM TABLE(CAST(p_customer_tax_germany AS customer_tax_germany_tab)) new_records,
                 customer_tax_germany old_records
           WHERE old_records.customer_id   = p_customer_id AND
                 old_records.customer_tax_germany_id = new_records.customer_tax_germany_id AND
                (nrg_common.has_value_changed(old_records.subject_to_capital_gains_tax, new_records.subject_to_capital_gains_tax) = 1 OR
                 nrg_common.has_value_changed(old_records.roll_losses_at_end_of_year, new_records.roll_losses_at_end_of_year) = 1 OR
                 nrg_common.has_value_changed(old_records.tax_identification_number, new_records.tax_identification_number) = 1 OR
                 nrg_common.has_value_changed(old_records.state, new_records.state) = 1);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
            put_history(p_action                    => 'U',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));
          END LOOP;

          SELECT old_records.*
            BULK COLLECT INTO lv_old_records
            FROM customer_tax_germany old_records
           WHERE customer_id = p_customer_id AND
                 customer_tax_germany_id NOT IN (SELECT customer_tax_germany_id
                                                   FROM TABLE(CAST(p_customer_tax_germany AS customer_tax_germany_tab)));
          --
          --Write deleted records to history
          --
          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

            put_history(p_action                    => 'D',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));

          END LOOP;
          --
          --Delete reocrds from base table
          --
          DELETE customer_tax_germany old_records
           WHERE customer_id = p_customer_id AND
                 customer_tax_germany_id NOT IN (SELECT customer_tax_germany_id
                                                   FROM TABLE(CAST(p_customer_tax_germany AS customer_tax_germany_tab)));
          --
          --Write New records, updated records to base table
          --
          MERGE INTO customer_tax_germany old_records USING
               (SELECT customer_tax_germany_id,
                       p_customer_id                  customer_id,
                       p_user                         created_by,
                       SYSTIMESTAMP                   message_time,
                       p_effective_start_timestamp    effective_start_timestamp,
                       subject_to_capital_gains_tax,
                       roll_losses_at_end_of_year,
                       tax_identification_number,
                       state
                  FROM TABLE(CAST(p_customer_tax_germany AS customer_tax_germany_tab)) aa) new_records
              ON (old_records.customer_id   = new_records.customer_id AND
                  old_records.customer_tax_germany_id = new_records.customer_tax_germany_id)
          WHEN MATCHED THEN UPDATE
              SET old_records.updated_by                    = new_records.created_by,
                  old_records.update_timestamp              = new_records.message_time,
                  old_records.logical_load_timestamp        = SYSTIMESTAMP,
                  old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                  old_records.subject_to_capital_gains_tax  = new_records.subject_to_capital_gains_tax,
                  old_records.roll_losses_at_end_of_year    = new_records.roll_losses_at_end_of_year,
                  old_records.tax_identification_number     = new_records.tax_identification_number,
                  old_records.state                         = new_records.state
           WHERE (nrg_common.has_value_changed(old_records.subject_to_capital_gains_tax, new_records.subject_to_capital_gains_tax) = 1 OR
                  nrg_common.has_value_changed(old_records.roll_losses_at_end_of_year, new_records.roll_losses_at_end_of_year) = 1 OR
                  nrg_common.has_value_changed(old_records.tax_identification_number, new_records.tax_identification_number) = 1 OR
                  nrg_common.has_value_changed(old_records.state, new_records.state) = 1)
          WHEN NOT MATCHED THEN INSERT
                 (customer_tax_germany_id,
                  customer_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  subject_to_capital_gains_tax,
                  roll_losses_at_end_of_year,
                  tax_identification_number,
                  state)
          VALUES (new_records.customer_tax_germany_id,
                  new_records.customer_id,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.subject_to_capital_gains_tax,
                  new_records.roll_losses_at_end_of_year,
                  new_records.tax_identification_number,
                  new_records.state);

        WHEN p_old_customer_version > p_new_customer_version THEN

           SELECT customer_tax_germany_id,
                  p_customer_id,
                  SYSTIMESTAMP logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  p_effective_start_timestamp,
                  subject_to_capital_gains_tax,
                  roll_losses_at_end_of_year,
                  tax_identification_number,
                  state
             BULK COLLECT INTO lv_old_records
             FROM TABLE(CAST(p_customer_tax_germany AS customer_tax_germany_tab))
            WHERE (customer_tax_germany_id,
                   subject_to_capital_gains_tax,
                   roll_losses_at_end_of_year,
                   tax_identification_number,
                   state) NOT IN (SELECT customer_tax_germany_id,
                                         subject_to_capital_gains_tax,
                                         roll_losses_at_end_of_year,
                                         tax_identification_number,
                                         state
                                    FROM customer_tax_germany
                                   WHERE customer_id = p_customer_id
                                   UNION ALL
                                  SELECT customer_tax_germany_id,
                                         subject_to_capital_gains_tax,
                                         roll_losses_at_end_of_year,
                                         tax_identification_number,
                                         state
                                    FROM customer_tax_germany_h
                                   WHERE customer_id = p_customer_id);

          FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

            put_history(p_action                    => 'I',
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_old_record                => lv_old_records(lv_cnt));

          END LOOP;

      END CASE;

      IF p_customer_tax_germany IS NOT NULL THEN

        FOR lv_count in 1.. p_customer_tax_germany.count LOOP

          put_german_tax_church_rates(p_user                          => p_user,
                                      p_logical_load_timestamp        => p_logical_load_timestamp,
                                      p_effective_start_timestamp     => p_effective_start_timestamp,
                                      p_customer_tax_germany_id       => p_customer_tax_germany(lv_count).customer_tax_germany_id,
                                      p_old_customer_version          => p_old_customer_version,
                                      p_new_customer_version          => p_new_customer_version,
                                      p_customer_tax_church_rates     => p_customer_tax_germany(lv_count).customer_tax_church_rates);

          put_german_tax_psl_allow   (p_user                          => p_user,
                                      p_logical_load_timestamp        => p_logical_load_timestamp,
                                      p_effective_start_timestamp     => p_effective_start_timestamp,
                                      p_customer_tax_germany_id       => p_customer_tax_germany(lv_count).customer_tax_germany_id,
                                      p_old_customer_version          => p_old_customer_version,
                                      p_new_customer_version          => p_new_customer_version,
                                      p_cst_tx_ger_psl_tx_fr_allw     => p_customer_tax_germany(lv_count).cst_tx_ger_psl_tx_fr_allw);

          put_german_tax_cpl_allow   (p_user                          => p_user,
                                      p_logical_load_timestamp        => p_logical_load_timestamp,
                                      p_effective_start_timestamp     => p_effective_start_timestamp,
                                      p_customer_tax_germany_id       => p_customer_tax_germany(lv_count).customer_tax_germany_id,
                                      p_old_customer_version          => p_old_customer_version,
                                      p_new_customer_version          => p_new_customer_version,
                                      p_cst_tx_ger_cpl_tx_fr_allw     => p_customer_tax_germany(lv_count).cst_tx_ger_cpl_tx_fr_allw);

          put_german_tax_roll_losses (p_user                          => p_user,
                                      p_logical_load_timestamp        => p_logical_load_timestamp,
                                      p_effective_start_timestamp     => p_effective_start_timestamp,
                                      p_customer_tax_germany_id       => p_customer_tax_germany(lv_count).customer_tax_germany_id,
                                      p_old_customer_version          => p_old_customer_version,
                                      p_new_customer_version          => p_new_customer_version,
                                      p_german_tax_roll_losses        => p_customer_tax_germany(lv_count).german_tax_roll_losses);
        END LOOP;

      END IF;
      logger.logger.set_module(NULL);

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

  -- ===================================================================================
  -- put_customer_segregations
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put customer_segregations
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_segregations
  --     customer_segregations_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version           Account Version of the existing record on ODS
  --     p_new_customer_version           Account Version of the new record that is received
  --     p_customer_segregations          customer_segregations
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_segregations(p_user                        customer_segregations.created_by%TYPE,
                                      p_logical_load_timestamp      customer_segregations.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp   customer_segregations.effective_start_timestamp%TYPE,
                                      p_customer_id                 customers.customer_id%TYPE,
                                      p_old_customer_version        customers.customer_version%TYPE,
                                      p_new_customer_version        customers.customer_version%TYPE,
                                      p_customer_segregations       customer_segregations_tab) IS

    TYPE l_old_records_tab IS TABLE OF customer_segregations%ROWTYPE;
    lv_old_records l_old_records_tab;

  BEGIN

    logger.logger.set_module('put_customer_segregations');

    CASE

      WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_customer_segregations AS customer_segregations_tab)) new_records,
               customer_segregations old_records
         WHERE old_records.customer_id     = p_customer_id AND
               old_records.segregation_id  = new_records.segregation_id AND
              (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
               nrg_common.has_value_changed(old_records.is_segregated, new_records.is_segregated) = 1);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history_seg(p_action                    => 'U',
                          p_effective_end_timestamp   => p_effective_start_timestamp,
                          p_old_record                => lv_old_records(lv_cnt));
        END LOOP;

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM customer_segregations old_records
         WHERE customer_id = p_customer_id AND
               segregation_id NOT IN (SELECT segregation_id
                                        FROM TABLE(CAST(p_customer_segregations AS customer_segregations_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history_seg(p_action                    => 'D',
                          p_effective_end_timestamp   => p_effective_start_timestamp,
                          p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_segregations old_records
         WHERE customer_id = p_customer_id AND
               segregation_id NOT IN (SELECT segregation_id
                                        FROM TABLE(CAST(p_customer_segregations AS customer_segregations_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_segregations old_records USING
             (SELECT /*+ CARDINALITY(AA 1) */
                     segregation_id,
                     p_customer_id                  customer_id,
                     p_user                         created_by,
                     SYSTIMESTAMP                   message_time,
                     p_effective_start_timestamp    effective_start_timestamp,
                     legal_entity,
                     is_segregated
                FROM TABLE(CAST(p_customer_segregations AS customer_segregations_tab)) aa) new_records
            ON (old_records.customer_id    = new_records.customer_id AND
                old_records.segregation_id = new_records.segregation_id)
        WHEN MATCHED THEN UPDATE
            SET old_records.updated_by                    = new_records.created_by,
                old_records.update_timestamp              = new_records.message_time,
                old_records.logical_load_timestamp        = SYSTIMESTAMP,
                old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                old_records.legal_entity                  = new_records.legal_entity,
                old_records.is_segregated                 = new_records.is_segregated
         WHERE (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
                nrg_common.has_value_changed(old_records.is_segregated, new_records.is_segregated) = 1)
        WHEN NOT MATCHED THEN INSERT
               (segregation_id,
                customer_id,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                legal_entity,
                is_segregated)
        VALUES (new_records.segregation_id,
                new_records.customer_id,
                SYSTIMESTAMP,
                new_records.created_by,
                new_records.message_time,
                new_records.created_by,
                new_records.message_time,
                new_records.effective_start_timestamp,
                new_records.legal_entity,
                new_records.is_segregated);

      WHEN p_old_customer_version > p_new_customer_version THEN

         SELECT segregation_id,
                p_customer_id,
                SYSTIMESTAMP logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp,
                legal_entity,
                is_segregated
           BULK COLLECT INTO lv_old_records
           FROM TABLE(CAST(p_customer_segregations AS customer_segregations_tab))
          WHERE (segregation_id,
                 legal_entity,
                 is_segregated) NOT IN (SELECT segregation_id,
                                       legal_entity,
                                       is_segregated
                                  FROM customer_segregations
                                 WHERE customer_id = p_customer_id
                                 UNION ALL
                                SELECT segregation_id,
                                       legal_entity,
                                       is_segregated
                                  FROM customer_segregations_h
                                 WHERE customer_id = p_customer_id);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history_seg(p_action                    => 'I',
                          p_effective_end_timestamp   => p_effective_start_timestamp,
                          p_old_record                => lv_old_records(lv_cnt));

        END LOOP;

    END CASE;

    logger.logger.set_module(NULL);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_customer_reg_clsfctns
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put customer_reg_clsfctns
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_reg_clsfctns
  --     customer_reg_clsfctns_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version           Account Version of the existing record on ODS
  --     p_new_customer_version           Account Version of the new record that is received
  --     p_customer_reg_clsfctns          customer_reg_clsfctns
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_reg_clsfctns(p_user                        customer_reg_clsfctns.created_by%TYPE,
                                      p_logical_load_timestamp      customer_reg_clsfctns.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp   customer_reg_clsfctns.effective_start_timestamp%TYPE,
                                      p_customer_id                 customers.customer_id%TYPE,
                                      p_old_customer_version        customers.customer_version%TYPE,
                                      p_new_customer_version        customers.customer_version%TYPE,
                                      p_customer_reg_clsfctns       customer_reg_clsfctns_tab) IS

    TYPE l_old_records_tab IS TABLE OF customer_reg_clsfctns%ROWTYPE;
    lv_old_records l_old_records_tab;

  BEGIN

    logger.logger.set_module('put_customer_reg_clsfctns');

    CASE

      WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_customer_reg_clsfctns AS customer_reg_clsfctns_tab)) new_records,
               customer_reg_clsfctns old_records
         WHERE old_records.customer_id     = p_customer_id AND
               old_records.regulatory_classification_id  = new_records.regulatory_classification_id AND
              (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
               nrg_common.has_value_changed(old_records.regulatory_classification, new_records.regulatory_classification) = 1);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'U',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM customer_reg_clsfctns old_records
         WHERE customer_id = p_customer_id AND
               regulatory_classification_id NOT IN (SELECT regulatory_classification_id
                                        FROM TABLE(CAST(p_customer_reg_clsfctns AS customer_reg_clsfctns_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'D',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_reg_clsfctns old_records
         WHERE customer_id = p_customer_id AND
               regulatory_classification_id NOT IN (SELECT regulatory_classification_id
                                        FROM TABLE(CAST(p_customer_reg_clsfctns AS customer_reg_clsfctns_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_reg_clsfctns old_records USING
             (SELECT /*+ CARDINALITY(AA 1) */
                     regulatory_classification_id,
                     p_customer_id                  customer_id,
                     p_user                         created_by,
                     SYSTIMESTAMP                   message_time,
                     p_effective_start_timestamp    effective_start_timestamp,
                     legal_entity,
                     regulatory_classification
                FROM TABLE(CAST(p_customer_reg_clsfctns AS customer_reg_clsfctns_tab)) aa) new_records
            ON (old_records.customer_id    = new_records.customer_id AND
                old_records.regulatory_classification_id = new_records.regulatory_classification_id)
        WHEN MATCHED THEN UPDATE
            SET old_records.updated_by                    = new_records.created_by,
                old_records.update_timestamp              = new_records.message_time,
                old_records.logical_load_timestamp        = SYSTIMESTAMP,
                old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                old_records.legal_entity                  = new_records.legal_entity,
                old_records.regulatory_classification                 = new_records.regulatory_classification
         WHERE (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
                nrg_common.has_value_changed(old_records.regulatory_classification, new_records.regulatory_classification) = 1)
        WHEN NOT MATCHED THEN INSERT
               (regulatory_classification_id,
                customer_id,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                legal_entity,
                regulatory_classification)
        VALUES (new_records.regulatory_classification_id,
                new_records.customer_id,
                SYSTIMESTAMP,
                new_records.created_by,
                new_records.message_time,
                new_records.created_by,
                new_records.message_time,
                new_records.effective_start_timestamp,
                new_records.legal_entity,
                new_records.regulatory_classification);

      WHEN p_old_customer_version > p_new_customer_version THEN

         SELECT regulatory_classification_id,
                p_customer_id,
                SYSTIMESTAMP logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp,
                legal_entity,
                regulatory_classification
           BULK COLLECT INTO lv_old_records
           FROM TABLE(CAST(p_customer_reg_clsfctns AS customer_reg_clsfctns_tab))
          WHERE (regulatory_classification_id,
                 legal_entity,
                 regulatory_classification) NOT IN (SELECT regulatory_classification_id,
                                       legal_entity,
                                       regulatory_classification
                                  FROM customer_reg_clsfctns
                                 WHERE customer_id = p_customer_id
                                 UNION ALL
                                SELECT regulatory_classification_id,
                                       legal_entity,
                                       regulatory_classification
                                  FROM customer_reg_clsfctns_h
                                 WHERE customer_id = p_customer_id);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'I',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;

    END CASE;

    logger.logger.set_module(NULL);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_customer_pro_opt_up
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put customer_pro_opt_up
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_pro_opt_up
  --     customer_pro_opt_up_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version           Account Version of the existing record on ODS
  --     p_new_customer_version           Account Version of the new record that is received
  --     p_customer_pro_opt_up          customer_pro_opt_up
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_pro_opt_up(p_user                        customer_pro_opt_up.created_by%TYPE,
                                    p_logical_load_timestamp      customer_pro_opt_up.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   customer_pro_opt_up.effective_start_timestamp%TYPE,
                                    p_customer_id                 customers.customer_id%TYPE,
                                    p_old_customer_version        customers.customer_version%TYPE,
                                    p_new_customer_version        customers.customer_version%TYPE,
                                    p_customer_pro_opt_up         customer_pro_opt_up_tab) IS

    TYPE l_old_records_tab IS TABLE OF customer_pro_opt_up%ROWTYPE;
    lv_old_records l_old_records_tab;

  BEGIN

    logger.logger.set_module('put_customer_pro_opt_up');

    CASE

      WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_customer_pro_opt_up AS customer_pro_opt_up_tab)) new_records,
               customer_pro_opt_up old_records
         WHERE old_records.customer_id    = p_customer_id AND
               old_records.pro_opt_up_id  = new_records.pro_opt_up_id AND
              (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
               nrg_common.has_value_changed(old_records.application_date, new_records.application_date) = 1 OR
               nrg_common.has_value_changed(old_records.is_reapplication, new_records.is_reapplication) = 1 OR
               nrg_common.has_value_changed(old_records.decision_date, new_records.decision_date) = 1 OR
               nrg_common.has_value_changed(old_records.decision, new_records.decision) = 1 OR
               nrg_common.has_value_changed(old_records.reference, new_records.reference) = 1 OR
               nrg_common.has_value_changed(old_records.route, new_records.route) = 1);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'U',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM customer_pro_opt_up old_records
         WHERE customer_id = p_customer_id AND
               pro_opt_up_id NOT IN (SELECT pro_opt_up_id
                                        FROM TABLE(CAST(p_customer_pro_opt_up AS customer_pro_opt_up_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'D',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_pro_opt_up old_records
         WHERE customer_id = p_customer_id AND
               pro_opt_up_id NOT IN (SELECT pro_opt_up_id
                                        FROM TABLE(CAST(p_customer_pro_opt_up AS customer_pro_opt_up_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_pro_opt_up old_records USING
             (SELECT pro_opt_up_id,
                     p_customer_id                  customer_id,
                     legal_entity,
                     p_user                         created_by,
                     SYSTIMESTAMP                   message_time,
                     p_effective_start_timestamp    effective_start_timestamp,
                     application_date,
                     is_reapplication,
                     decision_date,
                     decision,
                     reference,
                     route
                FROM TABLE(CAST(p_customer_pro_opt_up AS customer_pro_opt_up_tab)) aa) new_records
            ON (old_records.customer_id   = new_records.customer_id AND
                old_records.pro_opt_up_id = new_records.pro_opt_up_id)
        WHEN MATCHED THEN UPDATE
            SET old_records.updated_by                    = new_records.created_by,
                old_records.update_timestamp              = new_records.message_time,
                old_records.logical_load_timestamp        = SYSTIMESTAMP,
                old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                old_records.legal_entity                  = new_records.legal_entity,
                old_records.application_date              = new_records.application_date,
                old_records.is_reapplication              = new_records.is_reapplication,
                old_records.decision_date                 = new_records.decision_date,
                old_records.decision                      = new_records.decision,
                old_records.reference                     = new_records.reference,
                old_records.route                         = new_records.route
         WHERE (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
                nrg_common.has_value_changed(old_records.application_date, new_records.application_date) = 1 OR
                nrg_common.has_value_changed(old_records.is_reapplication, new_records.is_reapplication) = 1 OR
                nrg_common.has_value_changed(old_records.decision_date, new_records.decision_date) = 1 OR
                nrg_common.has_value_changed(old_records.decision, new_records.decision) = 1 OR
                nrg_common.has_value_changed(old_records.reference, new_records.reference) = 1 OR
                nrg_common.has_value_changed(old_records.route, new_records.route) = 1)
        WHEN NOT MATCHED THEN INSERT
               (pro_opt_up_id,
                customer_id,
                legal_entity,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                application_date,
                is_reapplication,
                decision_date,
                decision,
                reference,
                route)
        VALUES (new_records.pro_opt_up_id,
                new_records.customer_id,
                new_records.legal_entity,
                SYSTIMESTAMP,
                new_records.created_by,
                new_records.message_time,
                new_records.created_by,
                new_records.message_time,
                new_records.effective_start_timestamp,
                new_records.application_date,
                new_records.is_reapplication,
                new_records.decision_date,
                new_records.decision,
                new_records.reference,
                new_records.route);

      WHEN p_old_customer_version > p_new_customer_version THEN

         SELECT pro_opt_up_id,
                p_customer_id,
                legal_entity,
                SYSTIMESTAMP logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp,
                application_date,
                is_reapplication,
                decision_date,
                decision,
                reference,
                route
           BULK COLLECT INTO lv_old_records
           FROM TABLE(CAST(p_customer_pro_opt_up AS customer_pro_opt_up_tab))
          WHERE (pro_opt_up_id,
                 legal_entity,
                 application_date,
                 is_reapplication,
                 decision_date,
                 decision,
                 reference,
                 route) NOT IN (SELECT pro_opt_up_id,
                                           legal_entity,
                                           application_date,
                                           is_reapplication,
                                           decision_date,
                                           decision,
                                           reference,
                                           route
                                      FROM customer_pro_opt_up
                                     WHERE customer_id = p_customer_id
                                     UNION ALL
                                    SELECT pro_opt_up_id,
                                           legal_entity,
                                           application_date,
                                           is_reapplication,
                                           decision_date,
                                           decision,
                                           reference,
                                           route
                                      FROM customer_pro_opt_up_h
                                     WHERE customer_id = p_customer_id);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'I',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;

    END CASE;

    logger.logger.set_module(NULL);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_customer_reg_experience
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put customer_reg_experience
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_reg_experience
  --     customer_reg_experience_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version           Account Version of the existing record on ODS
  --     p_new_customer_version           Account Version of the new record that is received
  --     p_customer_reg_experience        customer_reg_experience
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_reg_experience(p_user                        customer_reg_experience.created_by%TYPE,
                                        p_logical_load_timestamp      customer_reg_experience.logical_load_timestamp%TYPE,
                                        p_effective_start_timestamp   customer_reg_experience.effective_start_timestamp%TYPE,
                                        p_customer_id                 customers.customer_id%TYPE,
                                        p_old_customer_version        customers.customer_version%TYPE,
                                        p_new_customer_version        customers.customer_version%TYPE,
                                        p_customer_reg_experience     customer_reg_experience_tab) IS

    TYPE l_old_records_tab IS TABLE OF customer_reg_experience%ROWTYPE;
    lv_old_records l_old_records_tab;

  BEGIN

    logger.logger.set_module('put_customer_reg_experience');

    CASE

      WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_customer_reg_experience AS customer_reg_experience_tab)) new_records,
               customer_reg_experience old_records
         WHERE old_records.customer_id               = p_customer_id AND
               old_records.regulatory_experience_id  = new_records.regulatory_experience_id AND
              (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
               nrg_common.has_value_changed(old_records.assessment_date, new_records.assessment_date) = 1 OR
               nrg_common.has_value_changed(old_records.expiry_date, new_records.expiry_date) = 1 OR
               nrg_common.has_value_changed(old_records.reg_experience_status, new_records.reg_experience_status) = 1);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'U',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM customer_reg_experience old_records
         WHERE customer_id = p_customer_id AND
               regulatory_experience_id NOT IN (SELECT regulatory_experience_id
                                                  FROM TABLE(CAST(p_customer_reg_experience AS customer_reg_experience_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'D',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_reg_experience old_records
         WHERE customer_id = p_customer_id AND
               regulatory_experience_id NOT IN (SELECT regulatory_experience_id
                                                  FROM TABLE(CAST(p_customer_reg_experience AS customer_reg_experience_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_reg_experience old_records USING
             (SELECT regulatory_experience_id,
                     p_customer_id                  customer_id,
                     legal_entity,
                     p_user                         created_by,
                     SYSTIMESTAMP                   message_time,
                     p_effective_start_timestamp    effective_start_timestamp,
                     assessment_date,
                     expiry_date,
                     reg_experience_status
                FROM TABLE(CAST(p_customer_reg_experience AS customer_reg_experience_tab)) aa) new_records
            ON (old_records.customer_id              = new_records.customer_id AND
                old_records.regulatory_experience_id = new_records.regulatory_experience_id)
        WHEN MATCHED THEN UPDATE
            SET old_records.updated_by                    = new_records.created_by,
                old_records.update_timestamp              = new_records.message_time,
                old_records.logical_load_timestamp        = SYSTIMESTAMP,
                old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                old_records.legal_entity                  = new_records.legal_entity,
                old_records.assessment_date               = new_records.assessment_date,
                old_records.expiry_date                   = new_records.expiry_date,
                old_records.reg_experience_status         = new_records.reg_experience_status
         WHERE (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
                nrg_common.has_value_changed(old_records.assessment_date, new_records.assessment_date) = 1 OR
                nrg_common.has_value_changed(old_records.expiry_date, new_records.expiry_date) = 1 OR
                nrg_common.has_value_changed(old_records.reg_experience_status, new_records.reg_experience_status) = 1)
        WHEN NOT MATCHED THEN INSERT
               (regulatory_experience_id,
                customer_id,
                legal_entity,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                assessment_date,
                expiry_date,
                reg_experience_status)
        VALUES (new_records.regulatory_experience_id,
                new_records.customer_id,
                new_records.legal_entity,
                SYSTIMESTAMP,
                new_records.created_by,
                new_records.message_time,
                new_records.created_by,
                new_records.message_time,
                new_records.effective_start_timestamp,
                new_records.assessment_date,
                new_records.expiry_date,
                new_records.reg_experience_status);

      WHEN p_old_customer_version > p_new_customer_version THEN

         SELECT regulatory_experience_id,
                p_customer_id,
                legal_entity,
                SYSTIMESTAMP logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp,
                assessment_date,
                expiry_date,
                reg_experience_status
           BULK COLLECT INTO lv_old_records
           FROM TABLE(CAST(p_customer_reg_experience AS customer_reg_experience_tab))
          WHERE (regulatory_experience_id,
                 legal_entity,
                 assessment_date,
                 expiry_date,
                 reg_experience_status) NOT IN (SELECT regulatory_experience_id,
                                                       legal_entity,
                                                       assessment_date,
                                                       expiry_date,
                                                       reg_experience_status
                                                  FROM customer_reg_experience
                                                 WHERE customer_id = p_customer_id
                                                 UNION ALL
                                                SELECT regulatory_experience_id,
                                                       legal_entity,
                                                       assessment_date,
                                                       expiry_date,
                                                       reg_experience_status
                                                  FROM customer_reg_experience_h
                                                 WHERE customer_id = p_customer_id);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'I',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;

    END CASE;

    logger.logger.set_module(NULL);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_customer_email_preferences
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put customer_email_preferences
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_email_preferences
  --     customer_email_preferences_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version           Account Version of the existing record on ODS
  --     p_new_customer_version           Account Version of the new record that is received
  --     p_customer_email_preferences     customer_email_preferences
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_customer_email_preferences(p_user                        customer_email_preferences.created_by%TYPE,
                                           p_logical_load_timestamp      customer_email_preferences.logical_load_timestamp%TYPE,
                                           p_effective_start_timestamp   customer_email_preferences.effective_start_timestamp%TYPE,
                                           p_customer_id                 customers.customer_id%TYPE,
                                           p_old_customer_version        customers.customer_version%TYPE,
                                           p_new_customer_version        customers.customer_version%TYPE,
                                           p_customer_email_preferences  customer_email_preferences_tab) IS

    TYPE l_old_records_tab IS TABLE OF customer_email_preferences%ROWTYPE;
    lv_old_records l_old_records_tab;

  BEGIN

    logger.logger.set_module('put_customer_email_preferences');

    CASE

      WHEN p_old_customer_version IS NULL OR p_old_customer_version <= p_new_customer_version THEN

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_customer_email_preferences AS customer_email_preferences_tab)) new_records,
               customer_email_preferences old_records
         WHERE old_records.customer_id          = p_customer_id AND
               old_records.email_preference_id  = new_records.email_preference_id AND
              (nrg_common.has_value_changed(old_records.topic, new_records.topic) = 1 OR
               nrg_common.has_value_changed(old_records.is_subscribed, new_records.is_subscribed) = 1);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'U',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM customer_email_preferences old_records
         WHERE customer_id = p_customer_id AND
               email_preference_id NOT IN (SELECT email_preference_id
                                                  FROM TABLE(CAST(p_customer_email_preferences AS customer_email_preferences_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'D',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE customer_email_preferences old_records
         WHERE customer_id = p_customer_id AND
               email_preference_id NOT IN (SELECT email_preference_id
                                                  FROM TABLE(CAST(p_customer_email_preferences AS customer_email_preferences_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO customer_email_preferences old_records USING
             (SELECT email_preference_id,
                     p_customer_id                  customer_id,
                     topic,
                     p_user                         created_by,
                     SYSTIMESTAMP                   message_time,
                     p_effective_start_timestamp    effective_start_timestamp,
                     is_subscribed
                FROM TABLE(CAST(p_customer_email_preferences AS customer_email_preferences_tab)) aa) new_records
            ON (old_records.customer_id         = new_records.customer_id AND
                old_records.email_preference_id = new_records.email_preference_id)
        WHEN MATCHED THEN UPDATE
            SET old_records.updated_by                    = new_records.created_by,
                old_records.update_timestamp              = new_records.message_time,
                old_records.logical_load_timestamp        = SYSTIMESTAMP,
                old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                old_records.topic                         = new_records.topic,
                old_records.is_subscribed                 = new_records.is_subscribed
         WHERE (nrg_common.has_value_changed(old_records.topic, new_records.topic) = 1 OR
                nrg_common.has_value_changed(old_records.is_subscribed, new_records.is_subscribed) = 1)
        WHEN NOT MATCHED THEN INSERT
               (email_preference_id,
                customer_id,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                topic,
                is_subscribed)
        VALUES (new_records.email_preference_id,
                new_records.customer_id,
                SYSTIMESTAMP,
                new_records.created_by,
                new_records.message_time,
                new_records.created_by,
                new_records.message_time,
                new_records.effective_start_timestamp,
                new_records.topic,
                new_records.is_subscribed);

      WHEN p_old_customer_version > p_new_customer_version THEN

         SELECT email_preference_id,
                p_customer_id,
                SYSTIMESTAMP logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp,
                topic,
                is_subscribed
           BULK COLLECT INTO lv_old_records
           FROM TABLE(CAST(p_customer_email_preferences AS customer_email_preferences_tab))
          WHERE (email_preference_id,
                 topic,
                 is_subscribed) NOT IN (SELECT email_preference_id,
                                               topic,
                                               is_subscribed
                                          FROM customer_email_preferences
                                         WHERE customer_id = p_customer_id
                                         UNION ALL
                                        SELECT email_preference_id,
                                               topic,
                                               is_subscribed
                                          FROM customer_email_preferences_h
                                         WHERE customer_id = p_customer_id);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history(p_action                    => 'I',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));

        END LOOP;

    END CASE;

    logger.logger.set_module(NULL);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

    -- ===================================================================================
    -- PUBLIC MODULES
    -- ===================================================================================
    --
    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION version
        RETURN VARCHAR2 deterministic
    IS
    BEGIN
        logger.logger.set_module('version');
        RETURN gc_version;
    EXCEPTION
        WHEN OTHERS THEN
            logger.logger.SEVERE(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            RAISE;
    END version;
    -- ===================================================================================
    -- create_customer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer stub in case the customer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_customer_stub (p_user                          IN customers.created_by%TYPE,
                                    p_logical_load_timestamp        IN customers.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp     IN customers.effective_start_timestamp%TYPE,
                                    p_customer_id                   IN customers.customer_id%TYPE)
    IS
      PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      INSERT INTO customers (customer_id,
                             logical_load_timestamp,
                             created_by,
                             create_timestamp,
                             updated_by,
                             update_timestamp,
                             effective_start_timestamp,
                             record_source)
                      VALUES(
                             p_customer_id,
                             p_logical_load_timestamp,
                             p_user,
                             SYSTIMESTAMP,
                             p_user,
                             SYSTIMESTAMP,
                             gc_default_timestamp,
                             'CM'
                             );
      COMMIT;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the customer record already exists no need to create stub
        --
        NULL;
    END create_customer_stub;
    -- ===================================================================================
    -- create_customer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer stub in case the customer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id
    --     p_person_id                      Person Id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_customer_stub (p_user                          IN customers.created_by%TYPE,
                                    p_logical_load_timestamp        IN customers.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp     IN customers.effective_start_timestamp%TYPE,
                                    p_customer_id                   IN customers.customer_id%TYPE,
                                    p_person_id                     IN customers.person_id%TYPE)
    IS
      PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      INSERT INTO customers (customer_id,
                             person_id,
                             logical_load_timestamp,
                             created_by,
                             create_timestamp,
                             updated_by,
                             update_timestamp,
                             effective_start_timestamp,
                             record_source)
                      VALUES(
                             p_customer_id,
                             p_person_id,
                             p_logical_load_timestamp,
                             p_user,
                             SYSTIMESTAMP,
                             p_user,
                             SYSTIMESTAMP,
                             gc_default_timestamp,
                             'CM'
                             );
      COMMIT;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the customer record already exists no need to create stub
        --
        NULL;
    END create_customer_stub;
    -- ===================================================================================
    -- create_customer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer stub in case the customer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id Table
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_customer_stub (p_user                        IN customers.created_by%TYPE,
                                    p_logical_load_timestamp      IN customers.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   IN customers.effective_start_timestamp%TYPE,
                                    p_customer_id                 IN customer_id_tab)
    IS
      -- PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      --
    -- Originally implemented as a single insert statement with the array casted and using a not exists.
    -- However, this along with a similar implementation within nrg_customer, allowed deadlocks to occur
    -- when both api's were being executed at the same time with corresponding linked data when a trading_account
    -- or conversely the customer already existed and was therefore not locked and processing held up until the the
    -- other api had its data committed.  This resulted in both APIs reaching the trading_account_link processing
    -- and deadlocking each other
    /*********************************** Original Code ****************************
      INSERT INTO customers (customer_id,
                             logical_load_timestamp,
                             created_by,
                             create_timestamp,
                             updated_by,
                             update_timestamp,
                             effective_start_timestamp)
                      SELECT customer_id,
                             p_logical_load_timestamp,
                             p_user,
                             SYSTIMESTAMP,
                             p_user,
                             SYSTIMESTAMP,
                             gc_default_timestamp
                        FROM TABLE(CAST(p_customer_id AS customer_id_tab)) cust
                        WHERE NOT EXISTS (SELECT 1
                                          FROM customers
                                          WHERE customer_id = cust.customer_id);
      COMMIT;
      ****************************************************************************************/
    --
    -- Loop through the array and call the create_stub individually,
    --
    IF p_customer_id IS NOT NULL THEN
     FOR i IN 1 .. p_customer_id.COUNT LOOP
         create_customer_stub (p_user                       => p_user,
                                 p_logical_load_timestamp     => p_logical_load_timestamp,
                                 p_effective_start_timestamp  => p_effective_start_timestamp,
                                 p_customer_id                => p_customer_id(i).customer_id
                                );
     END LOOP;
    END IF;
    END create_customer_stub;
    /***** Begin Modifiction for V2.5 - BER671 *****/
    -- ===================================================================================
    -- create_referrer_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer referrer stub in case the referrer is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_referrer_reference             Referrer Reference
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE create_referrer_stub (p_user                          IN customer_referrer.created_by%TYPE,
                                    p_logical_load_timestamp        IN customer_referrer.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp     IN customer_referrer.effective_start_timestamp%TYPE,
                                    p_referrer_reference            IN customer_referrer.referrer_reference%TYPE)
    IS
      PRAGMA AUTONOMOUS_TRANSACTION;
    BEGIN
      INSERT INTO customer_referrer (referrer_reference,
                                     logical_load_timestamp,
                                     created_by,
                                     create_timestamp,
                                     updated_by,
                                     update_timestamp,
                                     effective_start_timestamp)
                             VALUES (p_referrer_reference,
                                     p_logical_load_timestamp,
                                     p_user,
                                     SYSTIMESTAMP,
                                     p_user,
                                     SYSTIMESTAMP,
                                     gc_default_timestamp);
      COMMIT;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        --
        --Since the Referrer record already exists no need to create stub
        --
        NULL;
    END create_referrer_stub;
    -- ===================================================================================
    -- put_customer_referrer
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a customer referrer
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMER_REFERRER
    --     CUSTOMER_REFERRER_H
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_id                             Referrer Id
    --     p_referrer_reference             Referrer Reference
    --     p_notes                          Notes
    --     p_description                    description
    --     p_introducer_type
    --     p_partner_id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_customer_referrer ( p_user                            IN  customer_referrer.created_by%TYPE,
                                      p_effective_start_timestamp       IN  customer_referrer.effective_start_timestamp%TYPE,
                                      p_id                              IN  customer_referrer.id%TYPE,
                                      p_referrer_reference              IN  customer_referrer.referrer_reference%TYPE,
                                      p_notes                           IN  customer_referrer.notes%TYPE,
                                      p_description                     IN  customer_referrer.description%TYPE,
                                      p_introducer_type                 IN  customer_referrer.Introducer_Type%TYPE,
                                      p_partner_id                      IN  Customer_Referrer.Partner_Id%TYPE,
                                      p_is_deleted                      IN  customer_referrer.is_deleted%TYPE,
                                      p_assigned_agent_identity         IN  customer_referrer.assigned_agent_identity%TYPE)
    IS
      lv_logical_load_timestamp     customer_referrer.logical_load_timestamp%TYPE;
      lv_effective_start_timestamp  customer_referrer.effective_start_timestamp%TYPE;
      lv_old_customer_referrer_rec      customer_referrer%rowtype;
      l_count_partner    SMALLINT := 0;
      lex_referrer_not_found        EXCEPTION;
      lex_unknown_operation_type    EXCEPTION;
  BEGIN
    logger.logger.set_module('put_customer_referrer');
      -- set the logical load timestamp to now
      lv_logical_load_timestamp := SYSTIMESTAMP;
   -------
   --- Check if stub for partner_id is required
   -------
    SELECT COUNT(1)
      INTO l_count_partner
    FROM partners p
    WHERE partner_id = p_partner_id;
       -- create stub for partner
      IF (l_count_partner = 0) THEN
       nrg_partner.create_partner_stub (p_user                       => p_user,
                                        p_logical_load_timestamp     => lv_logical_load_timestamp,
                                        p_effective_start_timestamp  => p_effective_start_timestamp,
                                        p_partner_id                 => p_partner_id);
      END IF;
      BEGIN
        --
        --Insert the Customer Referrer Record
        --
        INSERT INTO customer_referrer
                (
                    id,
                    referrer_reference,
                    notes,
                    description,
                    introducer_type,
                    partner_id,
                    effective_start_timestamp,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    is_deleted,
                    assigned_agent_identity
                )
        VALUES
               (
                  p_id,
                  p_referrer_reference,
                  p_notes,
                  p_description,
                  p_introducer_type,
                  p_partner_id,
                  p_effective_start_timestamp,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_is_deleted,
                  p_assigned_agent_identity
                );
        lv_effective_start_timestamp := NULL;
      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          -- look up and lock a Customer Referrer by PK
          BEGIN
            SELECT cref.*
            INTO   lv_old_customer_referrer_rec
            FROM   customer_referrer cref
            WHERE  referrer_reference = p_referrer_reference
            FOR UPDATE;
          EXCEPTION
            WHEN NO_DATA_FOUND THEN
              RAISE lex_referrer_not_found;
          END;
        lv_effective_start_timestamp := lv_old_customer_referrer_rec.effective_start_timestamp;
      END;
      CASE
      WHEN lv_effective_start_timestamp IS NULL THEN
          --
          --Since the Referrer is already inserted no operation will be performed
          --
          NULL;
        WHEN lv_effective_start_timestamp IS NOT NULL AND lv_effective_start_timestamp <= p_effective_start_timestamp THEN
          -- the referrer exists and its effective start timestamp is
          -- earlier than the new effective start timestamp so update only
          -- where there has been some data change, otherwise do nothing
        UPDATE customer_referrer
              SET  updated_by                      = p_user,
                  update_timestamp                = SYSTIMESTAMP,
                  logical_load_timestamp          = lv_logical_load_timestamp,
                  effective_start_timestamp       = p_effective_start_timestamp,
                  id                              = p_id,
                  notes                           = p_notes,
                  description                     = p_description,
                  introducer_type                 = p_introducer_type,
                  partner_id                      = p_partner_id,
                  is_deleted                      = p_is_deleted,
                  assigned_agent_identity         = p_assigned_agent_identity
          WHERE   referrer_reference = p_referrer_reference AND
                 (/* only where there have been for data changes */
                  nrg_common.has_value_changed(id,p_id) = 1 OR
                  nrg_common.has_value_changed(notes,p_notes) = 1 OR
                  nrg_common.has_value_changed(description,p_description) = 1 OR
                  nrg_common.has_value_changed(introducer_type,p_introducer_type) = 1 OR
                  nrg_common.has_value_changed(partner_id,p_partner_id) = 1 OR
                  nrg_common.has_value_changed(is_deleted,p_is_deleted) = 1 OR
                  nrg_common.has_value_changed(assigned_agent_identity, p_assigned_agent_identity) = 1);
          --
          --If the row has got updated then write the history
          --
          IF lv_old_customer_referrer_rec.effective_start_timestamp <> gc_default_timestamp AND SQL%ROWCOUNT > 0 THEN
            put_history(p_old_customer_referrer_rec    => lv_old_customer_referrer_rec,
                        p_effective_end_timestamp      => p_effective_start_timestamp,
                        p_action                       => 'U');
          END IF;
      lv_old_customer_referrer_rec := NULL;
        WHEN lv_effective_start_timestamp IS NOT NULL AND lv_effective_start_timestamp > p_effective_start_timestamp THEN
      -- the referrer exists but its effective start timestamp is
          -- later than the new effective start timestamp so insert a history record
          SELECT
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               lv_logical_load_timestamp,
               p_effective_start_timestamp,
               p_referrer_reference,
               p_id,
               p_notes,
               p_description,
               p_introducer_type,
               p_partner_id,
               p_is_deleted,
               p_assigned_agent_identity
          INTO lv_old_customer_referrer_rec.created_by,
               lv_old_customer_referrer_rec.create_timestamp,
               lv_old_customer_referrer_rec.updated_by,
               lv_old_customer_referrer_rec.update_timestamp,
               lv_old_customer_referrer_rec.logical_load_timestamp,
               lv_old_customer_referrer_rec.effective_start_timestamp,
               lv_old_customer_referrer_rec.referrer_reference,
               lv_old_customer_referrer_rec.id,
               lv_old_customer_referrer_rec.notes,
               lv_old_customer_referrer_rec.description,
               lv_old_customer_referrer_rec.Introducer_Type,
               lv_old_customer_referrer_rec.Partner_Id,
               lv_old_customer_referrer_rec.is_deleted,
               lv_old_customer_referrer_rec.assigned_agent_identity
        FROM dual;
          put_history(p_old_customer_referrer_rec  => lv_old_customer_referrer_rec,
                      p_effective_end_timestamp    => p_effective_start_timestamp,
                      p_action                     => 'I');
          lv_old_customer_referrer_rec := NULL;
        ELSE
          RAISE lex_unknown_operation_type;
      END CASE;
    EXCEPTION
      WHEN lex_referrer_not_found THEN
        logger.logger.severe('Referrer Deleted Before Update and After Insert');
        logger.logger.set_module(NULL);
        raise_application_error(-20003, 'Referrer Deleted Before Update and After Insert');
      WHEN lex_unknown_operation_type THEN
        logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
        logger.logger.set_module(NULL);
        raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
  END put_customer_referrer;
    /***** End Modifiction for V2.5 *****/
    -- ===================================================================================
    -- put_customer
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a Customer
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     CUSTOMERS
    --     CUSTOMER_REGISTRATIONS
    --     TRDNG_ACCNTS_CUSTOMERS_LINK
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_customer_id                    Customer Id
    --     p_customer_version               Customer Version
    --     p_customer_type                  Customer Type
    --     p_customer_sub_type              Customer Sub Type
    --     p_customer_status                Customer Status
    --     p_start_date                     Start Date
    --     p_is_politically_exposed         Is Politically Exposed
    --     p_probability_of_default         Probability Of Default
    --     p_risk_level                     Risk Level
    --     p_regulatory_classification      Regulatory Classification
    --     p_is_sales_trader_managed        Is Sales Trader Managed
    --     p_is_do_not_contact              Is Do Not Contact
    --     p_is_out_of_area                 Is Out Of Area
    --     p_is_chargeback_requested        Is Chargeback Requested
    --     p_risk_level_change_reason       Risk Level Change Reason
    --     p_marketing_data_id              Marketing Data Id
    --     p_marketing_data_version         Marketing Data Version
    --     p_allow_email                    Allow Email
    --     p_allow_mail                     Allow Mail
    --     p_allow_telephone                Allow Telephone
    --     p_allow_sms                      Allow Sms
    --     p_segregation_profile_id         Segregation Profile Id
    --     p_segregation_profile_version    Segregation Profile Version
    --     p_is_margin_segregation          Is Margin Segregation
    --     p_is_fr_total_equity_segregation Is Fr Total Equity Segregation
    --     p_is_rlsd_p_and_l_segregation    Is Realised P and L Segregation
    --     p_is_unrlsd_p_and_l_segregation  Is Unrealised P and L Segregation
    --     p_tax_profile_id                 Tax Profile Id
    --     p_tax_profile_version            Tax Profile Version
    --     p_tax_profile_name               Tax Profile Name
    --     p_is_tax_exempt                  Is Tax Exempt
    --     p_tax_residency_code             Tax Residency Code
    --     p_withholding_tax_rate           Withholding Tax Rate
    --     p_withholding_tax_exemption_code Withholding Tax Exemption Code
    --     p_appropriateness_id             Appropriateness Id
    --     p_is_appropriate                 Is Aprropriate
    --     p_appropriateness_reason         Appropriateness Reason
    --     p_agreed_to_proceed              Agreed To Proceed
    --     p_financial_suitability_score    Financial Suitability Score
    --     p_knowledge_experience_score     Knowledge Experience Score
    --     p_company_id                     Comapny Id
    --     p_person_id                      Person Id
    --     p_trading_account_ids            Trading Account Ids
    --     p_customer_registrations         Customer Registrations,
    --     p_is_deleted                     Is Deleted
    --     p_assigned_agent_identity        Assigned Agent Identity
    --     p_assigned_sales_trdr_identity   Assigned Sales Trader Identity
    --     p_bi_segmentation                BI Segmentation
    --     p_is_potential_premium           Is Petential Premium
    --     p_trust_type                     Trust Type
    --     p_lead_office                    Lead Office
    /***** Begin Modification for V2.4 Ber-623 *****/
    --     p_block_marketing                Block Marketing
    /***** End Modification for V2.4 *****/
    /***** Begin Modification for V2.5 Ber-669 *****/
    --     p_is_customer_segregated         Is Customer Segregated Flag
    /***** End Modification for V2.5 *****/
    /***** Begin Modification for V2.5 Ber-671 *****/
    --     p_referrer_reference             Referrer Reference
    /***** End Modification for V2.5 *****/
    --     p_full_remedation_required       Full Remedation Required
    --     p_record_source                  Record Source
    --     p_source_customer_id             Source Customer Id
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Customer Deleted Before Update and After Insert
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_customer      ( p_user                            IN customers.created_by%TYPE,
                                  p_effective_start_timestamp       IN customers.effective_start_timestamp%TYPE,
                                  p_customer_id                     IN customers.customer_id%TYPE,
                                  p_customer_version                IN customers.customer_version%TYPE,
                                  p_customer_type                   IN customers.customer_type%TYPE,
                                  p_customer_sub_type               IN customers.customer_sub_type%TYPE,
                                  p_customer_status                 IN customers.customer_status%TYPE,
                                  p_start_date                      IN customers.start_date%TYPE,
                                  p_is_politically_exposed          IN customers.is_politically_exposed%TYPE,
                                  p_probability_of_default          IN customers.probability_of_default%TYPE,
                                  p_risk_level                      IN customers.risk_level%TYPE,
                                  p_regulatory_classification       IN customers.regulatory_classification%TYPE,
                                  p_is_sales_trader_managed         IN customers.is_sales_trader_managed%TYPE,
                                  p_is_do_not_contact               IN customers.is_do_not_contact%TYPE,
                                  p_is_out_of_area                  IN customers.is_out_of_area%TYPE,
                                  p_is_chargeback_requested         IN customers.is_chargeback_requested%TYPE,
                                  p_risk_level_change_reason        IN customers.risk_level_change_reason%TYPE,
                                  p_marketing_data_id               IN customers.marketing_data_id%TYPE,
                                  p_marketing_data_version          IN customers.marketing_data_version%TYPE,
                                  p_allow_email                     IN customers.allow_email%TYPE,
                                  p_allow_mail                      IN customers.allow_mail%TYPE,
                                  p_allow_telephone                 IN customers.allow_telephone%TYPE,
                                  p_allow_sms                       IN customers.allow_sms%TYPE,
                                  p_tax_profile_id                  IN customers.tax_profile_id%TYPE,
                                  p_tax_profile_version             IN customers.tax_profile_version%TYPE,
                                  p_is_tax_exempt                   IN customers.is_tax_exempt%TYPE,
                                  p_tax_residency_code              IN customers.tax_residency_code%TYPE,
                                  p_withholding_tax_rate            IN customers.withholding_tax_rate%TYPE,
                                  p_withholding_tax_exption_cde     IN customers.withholding_tax_exemption_code%TYPE,
                                  --p_appropriateness_id              IN customers.appropriateness_id%TYPE,
                                  --p_is_appropriate                  IN customers.is_appropriate%TYPE,
                                  --p_appropriateness_reason          IN customers.appropriateness_reason%TYPE,
                                  --p_agreed_to_proceed               IN customers.agreed_to_proceed%TYPE,
                                  --p_financial_suitability_score     IN customers.financial_suitability_score%TYPE,
                                  --p_knowledge_experience_score      IN customers.knowledge_experience_score%TYPE,
                                  p_company_id                      IN customers.company_id%TYPE,
                                  p_person_id                       IN customers.person_id%TYPE,
                                  p_trading_account_ids             IN trading_account_id_tab,
                                  p_customer_registrations          IN customer_registrations_tab,
                                  p_is_deleted                      IN customers.is_deleted%TYPE,
                                  p_assigned_agent_identity         IN customers.assigned_agent_identity%TYPE,
                                  p_assigned_sales_trdr_identity    IN customers.assigned_sales_trader_identity%TYPE,
                                  p_bi_segmentation                 IN customers.bi_segmentation%TYPE,
                                  p_is_potential_premium            IN customers.is_potential_premium%TYPE,
                                  p_trust_type                      IN customers.trust_type%TYPE,
                                  p_lead_office                     IN customers.lead_office%TYPE,
                                  p_assigned_agent_identity_date    IN customers.assigned_agent_identity_date%TYPE,
                                  p_last_remediation_date           IN customers.last_remediation_date%TYPE,
                                  p_next_remediation_date           IN customers.next_remediation_date%TYPE,
                                  p_next_remediation_alert_date     IN customers.next_remediation_alert_date%TYPE,
                                  p_online_declaration              IN customers.online_declaration%TYPE
                                  ,p_block_marketing                IN customers.block_marketing%TYPE
                                  ,p_is_customer_segregated         IN customers.is_customer_segregated%TYPE
                                  ,p_referrer_reference             IN customers.referrer_reference%TYPE
                                  ,p_full_remediation_required      IN customers.Full_Remediation_Required%TYPE
                                  ,p_fatca_status                   IN customers.fatca_status%TYPE
                                  ,p_fatca_assessment_date          IN customers.fatca_assessment_date%TYPE
                                  --,p_apprtns_assessment_date        IN customers.apprtns_assessment_date%TYPE
                                  ,p_remediation_enabled            IN customers.Remediation_Enabled%TYPE
                                  ,p_market_countrpart_partner_id   IN customers.Market_Counterparty_Partner_Id%TYPE
                                  ,p_speedbet_opt_out               IN customers.Speedbet_Opt_Out%TYPE
                                  ,p_speedbet_opt_out_status_date   IN customers.Speedbet_Opt_Out_Status_Date%TYPE
                                  ,p_is_binaries_opt_out            IN customers.is_binaries_opt_out%TYPE
                                  ,p_binaries_opt_out_status_date   IN customers.binaries_opt_out_status_date%TYPE
                                  ,p_perm_agent_identity            IN customers.perm_agent_identity%TYPE
                                  ,p_perm_agent_identity_date       IN customers.perm_agent_identity_date%TYPE
                                  ,p_record_source                  IN customers.record_source%TYPE DEFAULT 'CM'
                                  ,p_source_customer_id             IN customers.source_customer_id%TYPE DEFAULT NULL
                                  ,p_is_cfd_provider                IN customers.is_cfd_provider%TYPE
                                  ,p_tax_declarations               IN tax_declaration_tab
                                  ,p_customer_Apprprtnss_info       IN customer_Apprprtnss_info_tab
                                  ,p_customer_enquiries             IN customer_enquiry_tab
                                  ,p_lead_profile_country           IN customers.lead_profile_country%TYPE
                                  ,p_is_profiling_allowed           IN customers.is_profiling_allowed%TYPE
                                  ,p_lifecycle_status               IN customers.lifecycle_status%TYPE
                                  ,p_customer_tax_germany           IN customer_tax_germany_tab
                                  ,p_customer_segregations          IN customer_segregations_tab
                                  ,p_customer_reg_clsfctns          IN customer_reg_clsfctns_tab
                                  ,p_customer_pro_opt_up            IN customer_pro_opt_up_tab
                                  ,p_last_trade_attestation_date    IN customers.last_trade_attestation_date%TYPE
                                  ,p_customer_reg_experience        IN customer_reg_experience_tab
                                  ,p_customer_email_preferences     IN customer_email_preferences_tab)
    IS
      lv_logical_load_timestamp     customers.logical_load_timestamp%TYPE;
      lv_effective_start_timestamp  customers.effective_start_timestamp%TYPE;
      lv_old_customers              customers%ROWTYPE;
      lv_trading_account_type       trading_accounts.trading_account_type%TYPE;
      lex_customer_not_found        EXCEPTION;
      lex_unknown_operation_type    EXCEPTION;
  BEGIN
    logger.logger.set_module('put_customer');
      -- set the logical load timestamp to now
      lv_logical_load_timestamp := SYSTIMESTAMP;
      --
      --Identity Stubs for Assigned Agent and Assigned Trader Agent
      --
      IF p_assigned_agent_identity IS NOT NULL THEN
        nrg_identity.create_identity_stub(p_user                      => p_user,
                                          p_logical_load_timestamp    => lv_logical_load_timestamp,
                                          p_effective_start_timestamp => p_effective_start_timestamp,
                                          p_identity_id               => p_assigned_agent_identity);
      END IF;
      IF p_assigned_sales_trdr_identity IS NOT NULL THEN
        nrg_identity.create_identity_stub(p_user                      => p_user,
                                          p_logical_load_timestamp    => lv_logical_load_timestamp,
                                          p_effective_start_timestamp => p_effective_start_timestamp,
                                          p_identity_id               => p_assigned_sales_trdr_identity);
      END IF;
      --
      -- Trading Account Stubs for referential integrity
      --
      -- Note
      -- The version that handles an array of trading_account_id has internal set trading_account_type
      -- so no need to pass it
      --
      IF p_trading_account_ids IS NOT NULL AND p_customer_id > 0 THEN
         nrg_trading_account.create_trading_account_stub (p_user                        => p_user,
                                                          p_logical_load_timestamp      => lv_logical_load_timestamp,
                                                          p_effective_start_timestamp   => p_effective_start_timestamp,
                                                          p_trading_account_id          => p_trading_account_ids
                                 );
      END IF;
      --
      -- This is to ensure that the person record exists for referential integrity
      --
      IF p_person_id IS NOT NULL THEN
        nrg_person.create_person_stub (p_user                         => p_user,
                                       p_logical_load_timestamp     => lv_logical_load_timestamp,
                                       p_effective_start_timestamp  => p_effective_start_timestamp,
                                       P_PERSON_ID                  => P_PERSON_ID);
      END IF;
      --
      -- Company Stubs for referential integrity
      --
      IF p_company_id IS NOT NULL THEN
        nrg_company.create_company_stub(p_user                         => p_user,
                                        p_logical_load_timestamp       => lv_logical_load_timestamp,
                                        p_effective_start_timestamp    => p_effective_start_timestamp,
                                        p_company_id                   => p_company_id);
      END IF;
      /***** Begin Modification for V2.5 BER671 *****/
      --
      -- Referrer Stubs for referential integrity
      --
      IF p_referrer_reference IS NOT NULL THEN
        create_referrer_stub (p_user                          => p_user,
                              p_logical_load_timestamp        => lv_logical_load_timestamp,
                              p_effective_start_timestamp     => p_effective_start_timestamp,
                              p_referrer_reference            => p_referrer_reference);
      END IF;
      /***** End Modification for V2.5 *****/
      BEGIN
        --
        --Insert the Customer
        --
        INSERT INTO customers
                (
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    logical_load_timestamp,
                    effective_start_timestamp,
                    customer_id,
                    customer_version,
                    customer_type,
                    customer_sub_type,
                    customer_status,
                    start_date,
                    is_politically_exposed,
                    probability_of_default,
                    risk_level,
                    regulatory_classification,
                    is_sales_trader_managed,
                    is_do_not_contact,
                    is_out_of_area,
                    is_chargeback_requested,
                    risk_level_change_reason,
                    marketing_data_id,
                    marketing_data_version,
                    allow_email,
                    allow_mail,
                    allow_telephone,
                    allow_sms,
                    /***** Begin Modification for V2.5 BER-669 *****/
                    /***** Removed as per BER-669 *****/
                    --segregation_profile_id,
                    --segregation_profile_version,
                    --is_margin_segregation,
                    --is_fr_total_equity_segregation,
                    --is_rlsd_p_and_l_segregation,
                    --is_unrlsd_p_and_l_segregation,
                    /***** End Modification for V2.5 *****/
                    tax_profile_id,
                    tax_profile_version,
                    is_tax_exempt,
                    tax_residency_code,
                    withholding_tax_rate,
                    withholding_tax_exemption_code,
                    --appropriateness_id,
                    --is_appropriate,
                    --appropriateness_reason,
                    --agreed_to_proceed,
                    --financial_suitability_score,
                    ---knowledge_experience_score,
                    person_id,
                    company_id,
                    is_deleted,
                    assigned_agent_identity,
                    assigned_sales_trader_identity,
                    bi_segmentation,
                    is_potential_premium,
                    trust_type,
                    lead_office,
                    assigned_agent_identity_date,
                    last_remediation_date,
                    next_remediation_date,
                    next_remediation_alert_date,
                    online_declaration
                    ,block_marketing
                    ,is_customer_segregated
                    ,referrer_reference
                    ,full_remediation_required
                    ,fatca_status
                    ,fatca_assessment_date
                    --,apprtns_assessment_date
                    ,remediation_enabled
                    ,market_counterparty_partner_id
                    ,speedbet_opt_out
                    ,speedbet_opt_out_status_date
                    ,is_binaries_opt_out
                    ,binaries_opt_out_status_date
                    ,perm_agent_identity
                    ,perm_agent_identity_date
                    ,record_source
                    ,source_customer_id
                    ,is_cfd_provider
                    ,lead_profile_country
                    ,is_profiling_allowed
                    ,lifecycle_status
                    ,last_trade_attestation_date
                   )
        VALUES
               (
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  lv_logical_load_timestamp,
                  p_effective_start_timestamp,
                  p_customer_id,
                  p_customer_version,
                  p_customer_type,
                  p_customer_sub_type,
                  p_customer_status,
                  p_start_date,
                  p_is_politically_exposed,
                  p_probability_of_default,
                  p_risk_level,
                  p_regulatory_classification,
                  p_is_sales_trader_managed,
                  p_is_do_not_contact,
                  p_is_out_of_area,
                  p_is_chargeback_requested,
                  p_risk_level_change_reason,
                  p_marketing_data_id,
                  p_marketing_data_version,
                  p_allow_email,
                  p_allow_mail,
                  p_allow_telephone,
                  p_allow_sms,
                  /***** Begin Modification for V2.5 BER-669 *****/
                  /***** Removed as per BER-669 *****/
                  --p_segregation_profile_id,
                  --p_segregation_profile_version,
                  --p_is_margin_segregation,
                  --p_is_fr_total_equity_segregtn,
                  --p_is_rlsd_p_and_l_segregation,
                  --p_is_unrlsd_p_and_l_segregatn,
                  /***** End Modification for V2.5 *****/
                  p_tax_profile_id,
                  p_tax_profile_version,
                  p_is_tax_exempt,
                  p_tax_residency_code,
                  p_withholding_tax_rate,
                  p_withholding_tax_exption_cde,
                  --p_appropriateness_id,
                  --p_is_appropriate,
                  --p_appropriateness_reason,
                  --p_agreed_to_proceed,
                  --p_financial_suitability_score,
                  --p_knowledge_experience_score,
                  p_person_id,
                  p_company_id,
                  p_is_deleted,
                  p_assigned_agent_identity,
                  p_assigned_sales_trdr_identity,
                  p_bi_segmentation,
                  p_is_potential_premium,
                  p_trust_type,
                  p_lead_office,
                  p_assigned_agent_identity_date,
                  p_last_remediation_date,
                  p_next_remediation_date,
                  p_next_remediation_alert_date,
                  p_online_declaration
                  ,p_block_marketing
                  ,p_is_customer_segregated
                  ,p_referrer_reference
                  ,p_full_remediation_required
                  ,p_fatca_status
                  ,p_fatca_assessment_date
                  --,p_apprtns_assessment_date
                  ,p_remediation_enabled
                  ,p_market_countrpart_partner_id
                  ,p_speedbet_opt_out
                  ,p_speedbet_opt_out_status_date
                  ,p_is_binaries_opt_out
                  ,p_binaries_opt_out_status_date
                  ,p_perm_agent_identity
                  ,p_perm_agent_identity_date
                  ,p_record_source
                  ,p_source_customer_id
                  ,p_is_cfd_provider
                  ,p_lead_profile_country
                  ,p_is_profiling_allowed
                  ,p_lifecycle_status
                  ,P_last_trade_attestation_date
                );
        lv_effective_start_timestamp := NULL;
      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
          -- look up and lock a Customer by PK
          BEGIN
            SELECT cust.*
            INTO   lv_old_customers
            FROM   customers cust
            WHERE  customer_id = p_customer_id
            FOR UPDATE;
          EXCEPTION
            WHEN NO_DATA_FOUND THEN
              RAISE lex_customer_not_found;
          END;
        lv_effective_start_timestamp := lv_old_customers.effective_start_timestamp;
      END;
      CASE
      WHEN lv_effective_start_timestamp IS NULL THEN
          --
          --Since the customer is already inserted no operation will be performed
          --
          NULL;
        WHEN lv_effective_start_timestamp IS NOT NULL AND lv_effective_start_timestamp <= p_effective_start_timestamp THEN
          -- the person exists and its effective start timestamp is
          -- earlier than the new effective start timestamp so update only
          -- where there has been some data change, otherwise do nothing
        UPDATE customers
              SET  updated_by                      = p_user,
                  update_timestamp                = SYSTIMESTAMP,
                  logical_load_timestamp          = lv_logical_load_timestamp,
                  effective_start_timestamp       = p_effective_start_timestamp,
                  customer_version                = p_customer_version,
                  customer_type                   = p_customer_type,
                  customer_sub_type               = p_customer_sub_type,
                  customer_status                 = p_customer_status,
                  start_date                      = p_start_date,
                  is_politically_exposed          = p_is_politically_exposed,
                  probability_of_default          = p_probability_of_default,
                  risk_level                      = p_risk_level,
                  regulatory_classification       = p_regulatory_classification,
                  is_sales_trader_managed         = p_is_sales_trader_managed,
                  is_do_not_contact               = p_is_do_not_contact,
                  is_out_of_area                  = p_is_out_of_area,
                  is_chargeback_requested         = p_is_chargeback_requested,
                  risk_level_change_reason        = p_risk_level_change_reason,
                  marketing_data_id               = p_marketing_data_id,
                  marketing_data_version          = p_marketing_data_version,
                  allow_email                     = p_allow_email,
                  allow_mail                      = p_allow_mail,
                  allow_telephone                 = p_allow_telephone,
                  allow_sms                       = p_allow_sms,
                  tax_profile_id                  = p_tax_profile_id,
                  tax_profile_version             = p_tax_profile_version,
                  is_tax_exempt                   = p_is_tax_exempt,
                  tax_residency_code              = p_tax_residency_code,
                  withholding_tax_rate            = p_withholding_tax_rate,
                  withholding_tax_exemption_code  = p_withholding_tax_exption_cde,
                  --appropriateness_id              = p_appropriateness_id,
                  --is_appropriate                  = p_is_appropriate,
                  --appropriateness_reason          = p_appropriateness_reason,
                  --agreed_to_proceed               = p_agreed_to_proceed,
                  --financial_suitability_score     = p_financial_suitability_score,
                  --knowledge_experience_score      = p_knowledge_experience_score,
                  person_id                       = p_person_id,
                  company_id                      = p_company_id,
                  is_deleted                      = p_is_deleted,
                  assigned_agent_identity         = p_assigned_agent_identity,
                  assigned_sales_trader_identity  = p_assigned_sales_trdr_identity,
                  bi_segmentation                 = p_bi_segmentation,
                  is_potential_premium            = p_is_potential_premium,
                  trust_type                      = p_trust_type,
                  lead_office                     = p_lead_office,
                  assigned_agent_identity_date    = p_assigned_agent_identity_date,
                  last_remediation_date           = p_last_remediation_date,
                  next_remediation_date           = p_next_remediation_date,
                  next_remediation_alert_date     = p_next_remediation_alert_date,
                  online_declaration              = p_online_declaration
                  ,block_marketing                = p_block_marketing
                  ,is_customer_segregated         = p_is_customer_segregated
                  ,referrer_reference             = p_referrer_reference
                  ,full_remediation_required      = p_full_remediation_required
                  ,fatca_status                   = p_fatca_status
                  ,fatca_assessment_date          = p_fatca_assessment_date
                  --,apprtns_assessment_date        = p_apprtns_assessment_date
                  ,remediation_enabled            = p_remediation_enabled
                  ,market_counterparty_partner_id = p_market_countrpart_partner_id
                  ,speedbet_opt_out               = p_speedbet_opt_out
                  ,speedbet_opt_out_status_date   = p_speedbet_opt_out_status_date
                  ,is_binaries_opt_out            = p_is_binaries_opt_out
                  ,binaries_opt_out_status_date   = p_binaries_opt_out_status_date
                  ,perm_agent_identity            = p_perm_agent_identity
                  ,perm_agent_identity_date       = p_perm_agent_identity_date
                  ,record_source                  = p_record_source
                  ,source_customer_id             = p_source_customer_id
                  ,is_cfd_provider                = p_is_cfd_provider
                  ,lead_profile_country           = p_lead_profile_country
                  ,is_profiling_allowed           = p_is_profiling_allowed
                  ,lifecycle_status               = p_lifecycle_status
                  ,last_trade_attestation_date    = P_last_trade_attestation_date
          WHERE  customer_id = p_customer_id AND
                 (/* only where there have been for data changes */
                  nrg_common.has_value_changed(customer_version,p_customer_version) = 1 OR
                  nrg_common.has_value_changed(customer_type,p_customer_type) = 1 OR
                  nrg_common.has_value_changed(customer_sub_type,p_customer_sub_type) = 1 OR
                  nrg_common.has_value_changed(customer_status,p_customer_status) = 1 OR
                  nrg_common.has_value_changed(start_date,p_start_date) = 1 OR
                  nrg_common.has_value_changed(is_politically_exposed,p_is_politically_exposed) = 1 OR
                  nrg_common.has_value_changed(probability_of_default,p_probability_of_default) = 1 OR
                  nrg_common.has_value_changed(risk_level,p_risk_level) = 1 OR
                  nrg_common.has_value_changed(regulatory_classification,p_regulatory_classification) = 1 OR
                  nrg_common.has_value_changed(is_sales_trader_managed,p_is_sales_trader_managed) = 1 OR
                  nrg_common.has_value_changed(is_do_not_contact,p_is_do_not_contact) = 1 OR
                  nrg_common.has_value_changed(is_out_of_area,p_is_out_of_area) = 1 OR
                  nrg_common.has_value_changed(is_chargeback_requested,p_is_chargeback_requested) = 1 OR
                  nrg_common.has_value_changed(risk_level_change_reason,p_risk_level_change_reason) = 1 OR
                  nrg_common.has_value_changed(marketing_data_id,p_marketing_data_id) = 1 OR
                  nrg_common.has_value_changed(marketing_data_version,p_marketing_data_version) = 1 OR
                  nrg_common.has_value_changed(allow_email,p_allow_email) = 1 OR
                  nrg_common.has_value_changed(allow_mail,p_allow_mail) = 1 OR
                  nrg_common.has_value_changed(allow_telephone,p_allow_telephone) = 1 OR
                  nrg_common.has_value_changed(allow_sms,p_allow_sms) = 1 OR
                  nrg_common.has_value_changed(tax_profile_id,p_tax_profile_id) = 1 OR
                  nrg_common.has_value_changed(tax_profile_version,p_tax_profile_version) = 1 OR
                  nrg_common.has_value_changed(is_tax_exempt,p_is_tax_exempt) = 1 OR
                  nrg_common.has_value_changed(tax_residency_code,p_tax_residency_code) = 1 OR
                  nrg_common.has_value_changed(withholding_tax_rate,p_withholding_tax_rate) = 1 OR
                  nrg_common.has_value_changed(withholding_tax_exemption_code,p_withholding_tax_exption_cde) = 1 OR
                  --nrg_common.has_value_changed(appropriateness_id,p_appropriateness_id) = 1 OR
                  --nrg_common.has_value_changed(is_appropriate,p_is_appropriate) = 1 OR
                  --nrg_common.has_value_changed(appropriateness_reason,p_appropriateness_reason) = 1 OR
                  --nrg_common.has_value_changed(agreed_to_proceed,p_agreed_to_proceed) = 1 OR
                  --nrg_common.has_value_changed(financial_suitability_score,p_financial_suitability_score) = 1 OR
                  --nrg_common.has_value_changed(knowledge_experience_score,p_knowledge_experience_score) = 1 OR
                  nrg_common.has_value_changed(person_id,p_person_id) = 1 OR
                  nrg_common.has_value_changed(company_id,p_company_id) = 1 OR
                  nrg_common.has_value_changed(is_deleted,p_is_deleted) = 1 OR
                  nrg_common.has_value_changed(assigned_agent_identity,p_assigned_agent_identity) = 1 OR
                  nrg_common.has_value_changed(assigned_sales_trader_identity,p_assigned_sales_trdr_identity) = 1 OR
                  nrg_common.has_value_changed(bi_segmentation,p_bi_segmentation) = 1 OR
                  nrg_common.has_value_changed(is_potential_premium, p_is_potential_premium) = 1 OR
                  nrg_common.has_value_changed(trust_type, p_trust_type) = 1 OR
                  nrg_common.has_value_changed(lead_office, p_lead_office) = 1 OR
                  nrg_common.has_value_changed(assigned_agent_identity_date,p_assigned_agent_identity_date) = 1 OR
                  nrg_common.has_value_changed(last_remediation_date,p_last_remediation_date) =1 OR
                  nrg_common.has_value_changed(next_remediation_date,p_next_remediation_date) = 1 OR
                  nrg_common.has_value_changed(next_remediation_alert_date, p_next_remediation_alert_date)= 1 OR
                  nrg_common.has_value_changed(block_marketing, p_block_marketing) = 1 OR
                  nrg_common.has_value_changed(is_customer_segregated, p_is_customer_segregated) = 1 OR
                  nrg_common.has_value_changed(referrer_reference, p_referrer_reference) = 1 OR
                  nrg_common.has_value_changed(online_declaration, p_online_declaration)= 1  OR
                  nrg_common.has_value_changed(full_remediation_required, p_full_remediation_required)= 1  OR
                  nrg_common.has_value_changed(fatca_status, p_fatca_status)= 1   OR
                  nrg_common.has_value_changed(fatca_assessment_date, p_fatca_assessment_date)= 1  OR
                  --nrg_common.has_value_changed(apprtns_assessment_date, p_apprtns_assessment_date)= 1 OR
                  nrg_common.has_value_changed(remediation_enabled, p_remediation_enabled)= 1 OR
                  nrg_common.has_value_changed(market_counterparty_partner_id, p_market_countrpart_partner_id)= 1 OR
                  nrg_common.has_value_changed(speedbet_opt_out, p_speedbet_opt_out)= 1 OR
                  nrg_common.has_value_changed(speedbet_opt_out_status_date, p_speedbet_opt_out_status_date)= 1 OR
                  nrg_common.has_value_changed(is_binaries_opt_out, p_is_binaries_opt_out) = 1 OR
                  nrg_common.has_value_changed(binaries_opt_out_status_date, p_binaries_opt_out_status_date) = 1 OR
                  nrg_common.has_value_changed(perm_agent_identity, p_perm_agent_identity) = 1 OR
                  nrg_common.has_value_changed(perm_agent_identity_date, p_perm_agent_identity_date) = 1 OR
                  nrg_common.has_value_changed(record_source, p_record_source) = 1 OR
                  nrg_common.has_value_changed(source_customer_id, p_source_customer_id) = 1 OR
                  nrg_common.has_value_changed(is_cfd_provider, p_is_cfd_provider) = 1 OR
                  nrg_common.has_value_changed(lead_profile_country, p_lead_profile_country) = 1 OR
                  nrg_common.has_value_changed(is_profiling_allowed, p_is_profiling_allowed) = 1 OR
                  nrg_common.has_value_changed(lifecycle_status, p_lifecycle_status) = 1 OR
                  nrg_common.has_value_changed(last_trade_attestation_date, p_last_trade_attestation_date) = 1);
          --
          --If the row has got updated then write the history
          --
          IF lv_old_customers.effective_start_timestamp <> gc_default_timestamp AND SQL%ROWCOUNT > 0 THEN
            put_history(p_old_customers_record         => lv_old_customers,
                        p_effective_end_timestamp      => p_effective_start_timestamp,
                        p_action                       => 'U');
          END IF;
      lv_old_customers := NULL;
        WHEN lv_effective_start_timestamp IS NOT NULL AND lv_effective_start_timestamp > p_effective_start_timestamp THEN
      -- the customer exists but its effective start timestamp is
          -- later than the new effective start timestamp so insert a history record
          SELECT
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               lv_logical_load_timestamp,
               p_effective_start_timestamp,
               p_customer_id,
               p_customer_version,
               p_customer_type,
               p_customer_sub_type,
               p_customer_status,
               p_start_date,
               p_is_politically_exposed,
               p_probability_of_default,
               p_risk_level,
               p_regulatory_classification,
               p_is_sales_trader_managed,
               p_is_do_not_contact,
               p_is_out_of_area,
               p_is_chargeback_requested,
               p_risk_level_change_reason,
               p_marketing_data_id,
               p_marketing_data_version,
               p_allow_email,
               p_allow_mail,
               p_allow_telephone,
               p_allow_sms,
               p_tax_profile_id,
               p_tax_profile_version,
               p_is_tax_exempt,
               p_tax_residency_code,
               p_withholding_tax_rate,
               p_withholding_tax_exption_cde,
               --p_appropriateness_id,
               --p_is_appropriate,
               --p_appropriateness_reason,
               --p_agreed_to_proceed,
               --p_financial_suitability_score,
               --p_knowledge_experience_score,
               p_person_id,
               p_company_id,
               p_is_deleted,
               p_assigned_agent_identity,
               p_assigned_sales_trdr_identity,
               p_bi_segmentation,
               p_is_potential_premium,
               p_trust_type,
               p_lead_office,
               p_assigned_agent_identity_date,
               p_last_remediation_date,
               p_next_remediation_date,
               p_next_remediation_alert_date,
               p_online_declaration
               ,p_block_marketing
               ,p_is_customer_segregated
               ,p_referrer_reference
               ,p_full_remediation_required
               ,p_fatca_status
               ,p_fatca_assessment_date
               --,p_apprtns_assessment_date
               ,p_remediation_enabled
               ,p_market_countrpart_partner_id
               ,p_speedbet_opt_out
               ,p_speedbet_opt_out_status_date
               ,p_is_binaries_opt_out
               ,p_binaries_opt_out_status_date
               ,p_perm_agent_identity
               ,p_perm_agent_identity_date
               ,p_record_source
               ,p_source_customer_id
               ,p_is_cfd_provider
               ,p_lead_profile_country
               ,p_is_profiling_allowed
               ,p_lifecycle_status
               ,p_last_trade_attestation_date
          INTO lv_old_customers.created_by,
               lv_old_customers.create_timestamp,
               lv_old_customers.updated_by,
               lv_old_customers.update_timestamp,
               lv_old_customers.logical_load_timestamp,
               lv_old_customers.effective_start_timestamp,
               lv_old_customers.customer_id,
               lv_old_customers.customer_version,
               lv_old_customers.customer_type,
               lv_old_customers.customer_sub_type,
               lv_old_customers.customer_status,
               lv_old_customers.start_date,
               lv_old_customers.is_politically_exposed,
               lv_old_customers.probability_of_default,
               lv_old_customers.risk_level,
               lv_old_customers.regulatory_classification,
               lv_old_customers.is_sales_trader_managed,
               lv_old_customers.is_do_not_contact,
               lv_old_customers.is_out_of_area,
               lv_old_customers.is_chargeback_requested,
               lv_old_customers.risk_level_change_reason,
               lv_old_customers.marketing_data_id,
               lv_old_customers.marketing_data_version,
               lv_old_customers.allow_email,
               lv_old_customers.allow_mail,
               lv_old_customers.allow_telephone,
               lv_old_customers.allow_sms,
               lv_old_customers.tax_profile_id,
               lv_old_customers.tax_profile_version,
               lv_old_customers.is_tax_exempt,
               lv_old_customers.tax_residency_code,
               lv_old_customers.withholding_tax_rate,
               lv_old_customers.withholding_tax_exemption_code,
               --lv_old_customers.appropriateness_id,
               --lv_old_customers.is_appropriate,
               --lv_old_customers.appropriateness_reason,
               --lv_old_customers.agreed_to_proceed,
               --lv_old_customers.financial_suitability_score,
               --lv_old_customers.knowledge_experience_score,
               lv_old_customers.person_id,
               lv_old_customers.company_id,
               lv_old_customers.is_deleted,
               lv_old_customers.assigned_agent_identity,
               lv_old_customers.assigned_sales_trader_identity,
               lv_old_customers.bi_segmentation,
               lv_old_customers.is_potential_premium,
               lv_old_customers.trust_type,
               lv_old_customers.lead_office,
               lv_old_customers.assigned_agent_identity_date,
               lv_old_customers.last_remediation_date,
               lv_old_customers.next_remediation_date,
               lv_old_customers.next_remediation_alert_date,
               lv_old_customers.online_declaration
               ,lv_old_customers.block_marketing
               ,lv_old_customers.is_customer_segregated
               ,lv_old_customers.referrer_reference
               ,lv_old_customers.Full_Remediation_Required
               ,lv_old_customers.Fatca_Status
               ,lv_old_customers.Fatca_Assessment_Date
               --,lv_old_customers.Apprtns_Assessment_Date
               ,lv_old_customers.remediation_enabled
               ,lv_old_customers.market_counterparty_partner_id
               ,lv_old_customers.speedbet_opt_out
               ,lv_old_customers.speedbet_opt_out_status_date
               ,lv_old_customers.is_binaries_opt_out
               ,lv_old_customers.binaries_opt_out_status_date
               ,lv_old_customers.perm_agent_identity
               ,lv_old_customers.perm_agent_identity_date
               ,lv_old_customers.record_source
               ,lv_old_customers.source_customer_id
               ,lv_old_customers.is_cfd_provider
               ,lv_old_customers.lead_profile_country
               ,lv_old_customers.is_profiling_allowed
               ,lv_old_customers.lifecycle_status
               ,lv_old_customers.last_trade_attestation_date
        FROM dual;
          put_history(p_old_customers_record      => lv_old_customers,
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action                  => 'I');
          lv_old_customers := NULL;
        ELSE
          RAISE lex_unknown_operation_type;
      END CASE;
    --
    -- Create the links to trading accounts
    --
    IF (p_customer_id<0) THEN
       lv_trading_account_type := 'PARTNER_CASH';
    ELSE
       lv_trading_account_type := 'CUSTOMER';
    END IF;
      put_trading_account_ids (p_user                         => p_user,
                               p_logical_load_timestamp       => lv_logical_load_timestamp,
                               p_effective_start_timestamp    => p_effective_start_timestamp,
                               p_old_effective_start_time     => lv_effective_start_timestamp,
                               p_customer_id                  => p_customer_id,
                               p_trading_account_ids           => p_trading_account_ids,
                               p_trading_account_type         => lv_trading_account_type
                               );
      --
      -- Customer Registrations
      --
      put_customer_registrations(p_user                         => p_user,
                                 p_logical_load_timestamp       => lv_logical_load_timestamp,
                                 p_effective_start_timestamp    => p_effective_start_timestamp,
                                 p_old_effective_start_time     => lv_effective_start_timestamp,
                                 p_customer_id                  => p_customer_id,
                                 p_customer_registrations     => p_customer_registrations);
      put_tax_declaration(p_user                        => p_user,
                          p_logical_load_timestamp      => lv_logical_load_timestamp,
                          p_effective_start_timestamp   => p_effective_start_timestamp,
                          p_customer_id                 => p_customer_id,
                          p_old_customer_version        => lv_old_customers.customer_version,
                          p_new_customer_version        => p_customer_version,
                          p_tax_declarations            => p_tax_declarations);
      put_customer_apprprtnss_infos(p_user                        => p_user,
                                    p_logical_load_timestamp      => lv_logical_load_timestamp,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_customer_id                 => p_customer_id,
                                    p_old_customer_version        => lv_old_customers.customer_version,
                                    p_new_customer_version        => p_customer_version,
                                    p_customer_apprprtnss_info    => p_customer_apprprtnss_info);
      put_customer_enquiries(p_user                        => p_user,
                             p_logical_load_timestamp      => lv_logical_load_timestamp,
                             p_effective_start_timestamp   => p_effective_start_timestamp,
                             p_customer_id                 => p_customer_id,
                             p_old_customer_version        => lv_old_customers.customer_version,
                             p_new_customer_version        => p_customer_version,
                             p_customer_enquiries          => p_customer_enquiries);

      put_german_tax(p_user                        => p_user,
                     p_logical_load_timestamp      => lv_logical_load_timestamp,
                     p_effective_start_timestamp   => p_effective_start_timestamp,
                     p_customer_id                 => p_customer_id,
                     p_old_customer_version        => lv_old_customers.customer_version,
                     p_new_customer_version        => p_customer_version,
                     p_customer_tax_germany        => p_customer_tax_germany);

      put_customer_segregations(p_user                        => p_user,
                                p_logical_load_timestamp      => lv_logical_load_timestamp,
                                p_effective_start_timestamp   => p_effective_start_timestamp,
                                p_customer_id                 => p_customer_id,
                                p_old_customer_version        => lv_old_customers.customer_version,
                                p_new_customer_version        => p_customer_version,
                                p_customer_segregations       => p_customer_segregations);

      put_customer_reg_clsfctns(p_user                        => p_user,
                                p_logical_load_timestamp      => lv_logical_load_timestamp,
                                p_effective_start_timestamp   => p_effective_start_timestamp,
                                p_customer_id                 => p_customer_id,
                                p_old_customer_version        => lv_old_customers.customer_version,
                                p_new_customer_version        => p_customer_version,
                                p_customer_reg_clsfctns       => p_customer_reg_clsfctns);

      put_customer_pro_opt_up(p_user                        => p_user,
                              p_logical_load_timestamp      => lv_logical_load_timestamp,
                              p_effective_start_timestamp   => p_effective_start_timestamp,
                              p_customer_id                 => p_customer_id,
                              p_old_customer_version        => lv_old_customers.customer_version,
                              p_new_customer_version        => p_customer_version,
                              p_customer_pro_opt_up         => p_customer_pro_opt_up);

      put_customer_reg_experience(p_user                        => p_user,
                                  p_logical_load_timestamp      => lv_logical_load_timestamp,
                                  p_effective_start_timestamp   => p_effective_start_timestamp,
                                  p_customer_id                 => p_customer_id,
                                  p_old_customer_version        => lv_old_customers.customer_version,
                                  p_new_customer_version        => p_customer_version,
                                  p_customer_reg_experience     => p_customer_reg_experience);

      put_customer_email_preferences(p_user                        => p_user,
                                     p_logical_load_timestamp      => lv_logical_load_timestamp,
                                     p_effective_start_timestamp   => p_effective_start_timestamp,
                                     p_customer_id                 => p_customer_id,
                                     p_old_customer_version        => lv_old_customers.customer_version,
                                     p_new_customer_version        => p_customer_version,
                                     p_customer_email_preferences  => p_customer_email_preferences);

      logger.logger.set_module(NULL);
    EXCEPTION
      WHEN lex_customer_not_found THEN
        logger.logger.severe('Customer Deleted Before Update and After Insert');
        logger.logger.set_module(NULL);
        raise_application_error(-20003, 'Customer Deleted Before Update and After Insert');
      WHEN lex_unknown_operation_type THEN
        logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
        logger.logger.set_module(NULL);
        raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
  END put_customer;
    -- ===================================================================================
    -- get_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of stubbed id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed ids to return (Optional - not set returns all)
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR
    IS
        lcuv_result           SYS_REFCURSOR;
        lex_unknown_platform  EXCEPTION;
    BEGIN
        logger.logger.set_module('get_stubbed_ids');
    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT customer_id
               FROM   customers
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT customer_id
               FROM   customers
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;
        logger.logger.set_module(NULL);
        RETURN lcuv_result;
    EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_stubbed_ids;
    /***** Begin Modification for V2.5 Ber-671 *****/
    -- ===================================================================================
    -- get_refr_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of Referrer stubbed id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed id's to return (Optional - not set returns all)
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    FUNCTION get_refr_stubbed_ids (p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR
    IS
        lcuv_result           SYS_REFCURSOR;
        lex_unknown_platform  EXCEPTION;
    BEGIN
        logger.logger.set_module('get_refr_stubbed_ids');
    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT referrer_reference
               FROM   customer_referrer
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT referrer_reference
               FROM   customer_referrer
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;
        logger.logger.set_module(NULL);
        RETURN lcuv_result;
    EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_refr_stubbed_ids;
    /***** End Modification for V2.5 *****/
END nrg_customer;
/