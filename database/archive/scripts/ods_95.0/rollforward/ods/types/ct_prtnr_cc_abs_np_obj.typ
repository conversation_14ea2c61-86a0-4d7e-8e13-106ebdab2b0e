DROP TYPE ct_prtnr_cc_abs_np_tab FORCE;
DROP TYPE ct_prtnr_cc_abs_np_obj FORCE;

CREATE OR REPLACE TYPE bi_ods.ct_prtnr_cc_abs_np_obj IS OBJECT
 (instrument_code                VARCHAR2(100),
  open_trade_amount_currency     VARCHAR2(3),
  evltn_open_trade_amount        NUMBER,
  total_evltn_open_trade_amount  NUMBER,
  carrying_costs_rate            NUMBER,
  carrying_costs                 NUMBER,
  carrying_costs_fx_reval_rate   NUMBER,
  carrying_costs_in_account_ccy  NUMBER,
  carrying_costs_day_multiplier  NUMBER,
  open_trade_quantity            NUMBER,
  total_open_trade_quantity      NUMBER,
  evaluation_price               NUMBER    
  );
  /