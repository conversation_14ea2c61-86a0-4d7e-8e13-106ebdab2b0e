CREATE OR REPLACE PACKAGE bi_ods.nrg_trade
AS
  -- ===================================================================================
  -- NRG_TRADE
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the trades model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Trade Deleted Before Update and After Insert
  --     -20004    Default Exception
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     01/09/2011   Sanket Mittal      1.0    Creation
  --     27/09/2011   Sanket Mittal      1.1    Updated the trades entity
  --     10/10/2011   Sanket Mittal      1.2    Updated the Logic for concurrent writes
  --     02/11/2011   Paul Flynn         1.3    Updated to include insertion of MM Account ID and Trade Quantity
  --     04/11/2011   Manoj Kumar        1.4    Updated the comments and variable names
  --     07/11/2011   Mark Gornicki      1.5    Removed the code for the remove_trade method, since it is not required.
  --                                            Updated the trade_ids function such that if the platform is not
  --                                            specified all trade_ids within the date range are returned.
  --                                            Implemented by clause  platform = NVL(p_platform,platform).
  --                                            Also changed the parameters and the cursor query to use creation_time (source)
  --                                            and not create_timestamp (dwh audit column)
  --     08/11/2011   Mark Gornicki      1.6    Reordered the code which updates trades and copies previous version to history
  --                                            The put_history is now only executed if the update actually updates any rows.
  --     09/11/2011   Sanket Mittal      1.7    Renamed the function trade_ids to get_trade_ids
  --     11/11/2011   Mark Gornicki      1.9    In put_trade renamed parameter p_trading_platform to p_platform
  --     16/11/2011   Mark Gornicki      2.0   Added the parameter p_is_limit_trailing to put_trade to handle new column on underlying tables
  --     25/11/2011   Mark Gornicki      2.1    Amended create_trade_stub to set BUSINESS_DATE to the gc_default_timestamp
  --                                            to ensure the column has a value for partitioning otherwise it will fail.
  --                                            Amended Put_Trade and Put_History to handle the processing for internal hedge trades.
  --                                            This is for MarketMaker data such that it would have no trading_account_id set and
  --                                            we must perform a lookup based on MarketMaker source ids, so set the correct
  --                                            Trading Account ID/Type combination for referential integrity
  --     08/12/2011   Sanket Mittal      2.2    Added new parameters trading_account_type and cp2_trading_account_type
  --     08/12/2011   Patrick Dinwiddy   2.5    Added logic to populate quantity designator
  --     13/12/2011   Sanket Mittal      2.7    Updated to add a new fundtion get_order_ids
  --     20/12/2011   Sanket Mittal      2.8    Updated the logic to keep the trade time static once written
  --     23/12/2011   Sanket Mittal      2.9    Updated the logic to keep the business date static once written
  --     29/12/2011   Sanket Mittal      3.0    Added the column reporting Date to put_trade
  --     03/07/2012   Sanket Mittal      3.9    Added the column record_source and cash transaction seq
  --     11/07/2012   Sanket Mittal      4.0    Added the new field price_source
  --     15/01/2013   Sanket Mittal      4.2    Added the sub routine get_stubbed_ids
  --     06/03/2013   Sanket Mittal      4.3    Updated for P2 Changes
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added Logic to allow NRG to perform reconciliation of Hedge Trades
  --     19/12/2013   Ravi Shankar Gopal 4.5    BER-774: Added back p_trade_quantity_currency parameter which was commented out
  --     15/05/2014   Adam Krasnicki     4.6    BER-984: LIMIT_TRAILING_BEST_PRICE column dropped from TRADES[_H]
  --     07/05/2015   Adam Krasnicki     4.9    4 new parameters added to trades[_h] tables (IS_ROLLOVER_CLOSING,PRICE_OFFSET_INDEX,EXECUTION_STARTED_TIME..)
  --     26/06/2015   Adam Krasnicki     4.9    put_trade: new parameters: p_quote_depth_price
  --     07/10/2015   Sanket Mittal      5.0    put_trade - put_position procedure changed to add trading_scope parameter BER-2081
  --     20/01/2016   Sanket Mittal      5.2    put_trade - Added new parameters for binary:p_bo_type, p_bo_settle_time, p_bo_tenor, p_bo_strike_price_additional
  --     18/05/2016   Sanket Mittal      5.3    put_trade - Populate missing BI_ODS.TRADES columns for Binaries and Speedbets BER 2566
  --     10/06/2016    Sakina Kinkhabwala  5.4   put_trade - Added new parameters for countdowns:p_trade_instrument_amount, p_reference_trade_price
  --     22/08/2016   Sanket Mittal      5.6    BER-2830 New Attribute: LadderQuantitiesOverride
  --     12/10/2016   Sanket Mittal      5.9    BER-2967 add Knockout product wrapper to spread_l1 calcs
  --     12/10/2016   Sanket Mittal      5.9    BER-2957 New Hedge Attributes
  --     18/10/2016   Sanket Mittal      6.0    BER-2984 Change for order type function call
  --     16/03/2017   Sanket Mittal      6.1    BER-3434 SWS 37 - BI_ODS.TRADES - New Attribute
  --     14/07/2017   Sanket Mittal      6.2    BER-3786 NRG_TRADE - bi_ods.trades - Changes
  --     01/09/2017   Patrick Dinwiddy   6.3    BER-3905 New attribute
  --     15/01/2018   Sakina Kinkhabwala 6.4    BER-4127 put_trade/put_history - add new attributes
  --     10/05/2018   Sakina Kinkhabwala 6.5    BER-4593 put_trade/put_history - add new attributes
  --     22/04/2020   Patrick Dinwiddy   7.0    JCS-13086
  --     03/09/2020   Patrick Dinwiddy   7.1    JCS-13250
  --     01/12/2020   Patrick Dinwiddy   7.3    JCS-14060
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------
FUNCTION version
  RETURN VARCHAR2 DETERMINISTIC;
  -- ===================================================================================
  -- create_trade_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a trade stub in case the trade is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trade_id                       Trade Id
  --     p_platform                       Platform
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
PROCEDURE create_trade_stub(
    p_user                      IN trades.created_by%TYPE,
    p_logical_load_timestamp    IN trades.logical_load_timestamp%TYPE,
    p_effective_start_timestamp IN trades.effective_start_timestamp%TYPE,
    p_trade_id                  IN trades.trade_id%TYPE,
    p_platform                  IN trades.platform%TYPE);

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the trades record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_trade_record              This is the old version of the trade transaction
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
 PROCEDURE put_history (p_old_trade_record              IN trades%ROWTYPE,
                        p_effective_end_timestamp       IN trades_h.effective_end_timestamp%TYPE,
                        p_action                        IN trades_h.action%TYPE);
  -- ===================================================================================
  -- put_trade
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a trade
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trade_id                       Trade Id
  --     p_platform                       Platform
  --     p_trading_account_id             Trading Account Id
  --     p_trading_account_codifier       Trading Account Codifier
  --     p_order_id                       Order Id
  --     p_product_instrument_code        Product Instrument Code
  --     p_product_wrapper_code           Product Wrapper Code
  --     p_product_generation             Product Generation
  --     p_product_point_multiplier       Product Point Multiplier
  --     p_prdct_frctnl_prt_rto           Product Fractional Part Ratio
  --     p_product_currency               Product Currency
  --     p_product_schema_code            Product Schema Code
  --     p_prdct_financng_ratio_max       Product Financing Ratio Maximum
  --     p_session_key                    Session Key
  --     p_booking_number                 Booking Number
  --     p_cp1_cash_account_number        Counter Party 1 Cash Account Number
  --     p_cp2_cash_account_number        Counter Party 2 Cash Account Number
  --     p_publish_time                   Publish Time
  --     p_event_time                     Event Time
  --     p_creation_time                  Creation Time
  --     p_update_time                    Update Time
  --     p_creation_identity_token        Creation Identity Token
  --     p_crtn_on_bhlf_of_idntty_tkn     Creation On Behlaf Of Identity Token
  --     p_update_identity_token          Update Identity Token
  --     p_updt_on_bhlf_of_idntty_tkn     Update On Behalf Of Identity Token
  --     p_version_number                 Version Number
  --     p_is_deleted                     Is Deleted
  --     p_visit_id                       Visit Id
  --     p_channel_id                     Channel Id
  --     p_request_id                     Request Id
  --     p_reduced_trade_id               Reduced Trade Id
  --     p_reversed_trade_id              Reversed Trade Id
  --     p_business_date                  Business Date
  --     p_reporting_date                 Reporting Date
  --     p_trade_time                     Trade Time
  --     p_order_type                     Order Type
  --     p_controlled_order_type          Controlled Order Type
  --     p_trading_account_function       Trading Account Function
  --     p_trading_account_currency       Trading Account Currency
  --     p_product_point_multiplier       Product Point Multiplier
  --     p_product_frctonl_part_ratio     Product Functional Part Ratio
  --     p_product_currency               Product Currency
  --     p_feedsymbol                     Feed Symbol
  --     p_prophet_symbol                 Prophet Symbol
  --     p_is_primary                     Is Primay
  --     p_is_late_deal                   Is Late Deal
  --     p_mm_value_date                  MM Value Date
  --     p_direction                      Direction
  --     p_direction_multiplier           Direction Multiplier
  --     p_quoted_l1_ask_price            Quoted L1 Ask Price
  --     p_quoted_l1_bid_price            Quoted L1 Bid Price
  --     p_trade_price                    Trade Price
  --     p_price_designator               Price Designator
  --     p_quantity_designator            Quantity Designator
  --     p_trade_quantity                 Trade Quantity
  --     p_trade_quantity_currency        Trade Quantity Currency
  --     p_quantity_fx_rate_bid           Quantity FX Rate Bid
  --     p_quantity_fx_rate_ask           Quantity FX Rate Ask
  --     p_trade_amount                   Trade Amount
  --     p_trade_amount_currency          Trade Amount Currency
  --     p_trd_amt_in_trdng_accnt_crncy   Trade Amount In Trading Account Currency
  --     p_financing_ratio                Financing Ratio
  --     p_margin_type                    Margin Type
  --     p_margin_requirement             Margin Requirement
  --     p_margin_fx_rate_bid             Margin FX Rate Bid
  --     p_margin_fx_rate_ask             Margin FX Rate Ask
  --     p_trade_margin_amount            Trade Margin Amount
  --     p_trade_margin_currency          Trade Margin Currency
  --     p_trd_mrgn_in_trdng_accnt_crrncy Trade Margin In Trading Account Currnecy
  --     p_profit_loss_fx_rate_bid        Profit And Loss FX Rate Bid
  --     p_profit_loss_fx_rate_ask        Profit And Loss FX Rate Ask
  --     p_trade_profit_loss              Trade Profit And Loss
  --     p_trade_profit_loss_currency     Trade Profit And Loss Currency
  --     p_trd_pl_in_trdng_accnt_crrncy   Trade Profit And Loss In Trading Account Currency
  --     p_cp2_trading_account_id         Counter Party 2 Trading Account ID
  --     p_cp2_trading_account_codifier   Counter Party 2 Trading Account Codifier
  --     p_cp2_custom_info                Counter Party 2 Custom Info
  --     p_quote_id                       Quote ID
  --     p_quote_l1_bid                   Quote Level 1 Bid
  --     p_quote_l1_ask                   Quote Level 1 Ask
  --     p_quantity_fx_rate_id            Quantity FX Rate Id
  --     p_margin_fx_rate_id              Margin Fx Rate Id
  --     p_pl_fx_rate_id                  Profit And Loss FX Rate Id
  --     p_pl_fx_rate_bid                 Profit And Loss FX Rate Bid
  --     p_pl_fx_rate_ask                 Profit And Loss FX Rate Ask
  --     p_is_trd_of_cntrlld_ordr         Is Trade Of Controlled Order
  --     p_reversed_order_id              Reversed Order Id
  --     p_mm_instrument_id               MM Instrument Id
  --     p_related_child_order_type       Related Child Order Type
  --     p_is_mandatory                   Is Mandatory
  --     p_rqustd_trd_close_order_id      Requested Trade Close Order Id
  --     p_related_parent_order_id        Related Parent Order Id
  --     p_limit_price_condition          Limit Price Condition
  --     p_order_type                     Order Type
  --     p_limit_is_trailing_distance     Limit Trailing Distance
  --     p_limit_trailing_best_price      Limit Trailing Best Price
  --     p_limit_price                    Limit Price
  --     p_quote_ask_price                Quote Ask Price
  --     p_quote_bid_price                Quote Bid Price
  --     p_mm_account_id                  Market Maker Account ID
  --     p_is_limit_trailing              Is Trailing Limit
  --     p_trading_account_type           Type Of Trading Account (CUSTOMER/INTERNAL)
  --     p_cp2_trading_account_type       Type Of Trading Account (CUSTOMER/INTERNAL)
  --     p_record_source                  Record Source for the booking
  --     p_price_source                   Identifies the source of the TradePrice
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Trade Deleted Before Update and After Insert
  --     -20004    Default Exception
  --
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_trade (p_user                           IN trades.created_by%TYPE,
                         p_effective_start_timestamp      IN trades.effective_start_timestamp%TYPE,
                         p_trade_id                       IN trades.trade_id%TYPE,
                         p_platform                       IN trades.platform%TYPE,
                         p_trading_account_id             IN trades.trading_account_id%TYPE,
                         p_trading_account_codifier       IN trades.trading_account_codifier%TYPE,
                         p_order_id                       IN trades.order_id%TYPE,
                         p_product_instrument_code        IN trades.product_instrument_code%TYPE,
                         p_product_wrapper_code           IN trades.product_wrapper_code%TYPE,
                         p_product_generation             IN trades.product_generation%TYPE,
                         p_product_point_multiplier       IN trades.product_point_multiplier%TYPE,
                         p_prdct_frctnl_prt_rto           IN trades.product_fractional_part_ratio%TYPE,
                         p_product_currency               IN trades.product_currency%TYPE,
                         p_session_key                    IN trades.session_key%TYPE,
                         p_booking_number                 IN trades.booking_number%TYPE,
                         p_publish_time                   IN trades.publish_time%TYPE,
                         p_event_time                     IN trades.event_time%TYPE,
                         p_creation_time                  IN trades.creation_time%TYPE,
                         p_update_time                    IN trades.update_time%TYPE,
                         p_creation_identity_token        IN trades.creation_identity_token%TYPE,
                         p_crtn_on_bhlf_of_idntty_tkn     IN trades.crtn_on_bhlf_of_idntty_tkn%TYPE,
                         p_update_identity_token          IN trades.update_identity_token%TYPE,
                         p_updt_on_bhlf_of_idntty_tkn     IN trades.updt_on_bhlf_of_idntty_tkn%TYPE,
                         p_version_number                 IN trades.version_number%TYPE,
                         p_is_deleted                     IN trades.is_deleted%TYPE,
                         p_channel_id                     IN trades.channel_id%TYPE,
                         p_request_id                     IN trades.request_id%TYPE,
                         p_reduced_trade_id               IN trades.reduced_trade_id%TYPE,
                         p_reversed_trade_id              IN trades.reversed_trade_id%TYPE,
                         p_trade_time                     IN trades.trade_time%TYPE,
                         p_order_type                     IN trades.order_type%TYPE,
                         p_controlled_order_type          IN trades.controlled_order_type%TYPE,
                         p_trading_account_function       IN trades.trading_account_function%TYPE,
                         p_trading_account_currency       IN trades.trading_account_currency%TYPE,
                         p_is_primary                     IN trades.is_primary%TYPE,
                         p_is_late_deal                   IN trades.is_late_deal%TYPE,
                         p_mm_value_date                  IN trades.mm_value_date%TYPE,
                         p_direction                      IN trades.direction%TYPE,
                         p_direction_multiplier           IN trades.direction_multiplier%TYPE,
                         p_quoted_l1_ask_price            IN trades.quoted_l1_ask_price%TYPE,
                         p_quoted_l1_bid_price            IN trades.quoted_l1_bid_price%TYPE,
                         p_trade_price                    IN trades.trade_price%TYPE,
                         p_quantity_designator            IN trades.quantity_designator%TYPE,
                         p_trade_quantity                 IN trades.normalised_quantity%TYPE,
                         p_trade_quantity_currency        IN trades.normalised_quantity_currency%TYPE,
                         p_trade_amount                   IN trades.trade_amount%TYPE,
                         p_trade_amount_currency          IN trades.trade_amount_currency%TYPE,
                         p_trd_amnt_in_trdng_acnt_crncy   IN trades.trd_amnt_in_trdng_accnt_crrncy%TYPE,
                         p_margin_type                    IN trades.margin_type%TYPE,
                         p_margin_requirement             IN trades.margin_requirement%TYPE,
                         p_trade_profit_loss              IN trades.trade_profit_loss%TYPE,
                         p_trade_profit_loss_currency     IN trades.trade_profit_loss_currency%TYPE,
                         p_trd_pl_in_trdng_accnt_crrncy   IN trades.trd_pl_in_trdng_accnt_crrncy%TYPE,
                         p_trdng_accnt_custom_info        IN trades.trdng_accnt_custom_info%TYPE,
                         p_quote_id                       IN trades.quote_id%TYPE,
                         p_quote_l1_bid                   IN trades.quote_l1_bid%TYPE,
                         p_quote_l1_ask                   IN trades.quote_l1_ask%TYPE,
                         p_profit_loss_fx_rate_id         IN trades.profit_loss_fx_rate_id%TYPE,
                         p_profit_loss_fx_rate_bid        IN trades.profit_loss_fx_rate_bid%TYPE,
                         p_profit_loss_fx_rate_ask        IN trades.profit_loss_fx_rate_ask%TYPE,
                         p_is_trd_of_cntrlld_ordr         IN trades.is_trd_of_cntrlld_ordr%TYPE,
                         p_reversed_order_id              IN trades.reversed_order_id%TYPE,
                         p_mm_instrument_id               IN trades.mm_instrument_id%TYPE,
                         p_related_child_order_type       IN trades.related_child_order_type%TYPE,
                         p_is_mandatory                   IN trades.is_mandatory%TYPE,
                         p_rqustd_trd_close_order_id      IN trades.requested_trade_close_order_id%TYPE,
                         p_related_parent_order_id        IN trades.related_parent_order_id%TYPE,
                         p_limit_price_condition          IN trades.limit_price_condition%TYPE,
                         p_limit_trailing_distance        IN trades.limit_trailing_distance%TYPE,
                         p_limit_price                    IN trades.limit_price%TYPE,
                         p_is_prdct_crncy_in_frtnl_prts   IN trades.is_prdct_crrncy_in_frtnl_prts%TYPE,
                         p_quote_ask_price                IN trades.quote_ask_price%TYPE,
                         p_quote_bid_price                IN trades.quote_bid_price%TYPE,
                         p_mm_account_id                  IN trades.mm_account_id%TYPE,
                         p_is_limit_trailing              IN trades.is_limit_trailing%TYPE,
                         p_trading_account_type           IN trades.trading_account_type%TYPE,
                         p_record_source                  IN trades.record_source%TYPE,
                         p_quote_received_time            IN trades.quote_received_time%TYPE,
                         p_price_source                   IN trades.price_source%TYPE,
                         p_app_to_units                   IN trades.app_to_units%TYPE,
                         p_rollover_closing_trade_id      IN trades.rollover_closing_trade_id%TYPE,
                         p_price_level                    IN trades.price_level%TYPE,
                         p_amount_fx_rate_id              IN trades.amount_fx_rate_id%TYPE,
                         p_amount_fx_rate                 IN trades.amount_fx_rate%TYPE,
                         p_aggregated_price_quantity      IN trades.aggregated_price_quantity%TYPE,
                         p_rqustd_open_trade_to_close     IN trade_id_tab,
                         p_order_time                     IN orders.creation_time%TYPE
                         ,p_hdg_ext_trxn_id               IN trades.hdg_ext_trxn_id%TYPE,
                         p_hdg_milliways_seq_id           IN trades.hdg_milliways_seq_id%TYPE,
                         p_hdg_src                        IN trades.hdg_src%TYPE,
                         p_hdg_src_ref_id                 IN trades.hdg_src_ref_id%TYPE,
                         p_hdg_trxn_type                  IN trades.hdg_trxn_type%TYPE,
                         p_hdg_execn_type                 IN trades.hdg_execn_type%TYPE,
                         p_hdg_ext_trxn_time              IN trades.hdg_ext_trxn_time%TYPE,
                         p_hdg_trade_dt                   IN trades.hdg_trade_dt%TYPE,
                         p_hdg_ext_trade_dt               IN trades.hdg_ext_trade_dt%TYPE,
                         p_hdg_accnting_trade_dt          IN trades.hdg_accnting_trade_dt%TYPE,
                         p_hdg_exec_broker_accnt_num      IN trades.hdg_exec_broker_accnt_num%TYPE,
                         p_hdg_asset_class                IN trades.hdg_asset_class%TYPE,
                         p_hdg_instr_code_ext             IN trades.hdg_instr_code_ext%TYPE,
                         p_hdg_expy_month_code            IN trades.hdg_expy_month_code%TYPE,
                         p_hdg_risk_bucket                IN trades.hdg_risk_bucket%TYPE,
                         p_hdg_cmc_trader                 IN trades.hdg_cmc_trader%TYPE,
                         p_trade_comment                  IN trades.trade_comment%TYPE,
                         p_hdg_calc_commission            IN trades.hdg_calc_commission%TYPE,
                         p_hdg_commission                 IN trades.hdg_commission%TYPE,
                         p_hdg_is_risk_relevant           IN trades.hdg_is_risk_relevant%TYPE,
                         p_hdg_is_reg_repng_relvnt        IN trades.hdg_is_reg_repng_relvnt%TYPE,
                         p_hdg_cancln_ref                 IN trades.hdg_cancln_ref%TYPE,
                         p_hdg_correction_ref             IN trades.hdg_correction_ref%TYPE,
                         p_hdg_cross_ref                  IN trades.hdg_cross_ref%TYPE,
                         p_is_rollover_closing            IN trades.Is_Rollover_Closing%TYPE,
                         p_price_offset_index             IN trades.Price_Offset_Index%TYPE,
                         p_execution_started_time         IN trades.Execution_Started_Time%TYPE,
                         p_trade_instrument_price         IN trades.Trade_Instrument_Price%TYPE,
                         p_strike_price                   IN trades.Strike_Price%TYPE,
                         p_order_execution_type           IN trades.Order_Type%TYPE,
                         p_quote_depth_price              IN trades.Quote_Depth_Price%TYPE,
                         p_binary_type                    IN trades.binary_type%TYPE,
                         p_settle_time                    IN trades.settle_time%TYPE,
                         p_tenor                          IN trades.tenor%TYPE,
                         p_strike_price_additional        IN trades.strike_price_additional%TYPE,
                         p_trade_instrument_amount        IN trades.trade_instrument_amount%TYPE,
                         p_reference_trade_price          IN trades.reference_trade_price%TYPE,
                         p_ladder_quantities_override     IN trades.ladder_quantities_override%TYPE,
                         p_hdg_exec_broker_code           IN VARCHAR2,
                         p_hdg_execution_commission       IN trades.hdg_execution_commission%TYPE,
                         p_hdg_broker_code                IN VARCHAR2,
                         p_hdg_ext_broker_accnt_num       IN VARCHAR2,
                         p_cmc_trade_instrument_price     IN trades.cmc_trade_instrument_price%TYPE,
                         p_hdg_spot_price                 IN trades.hdg_spot_price%TYPE,
                         p_hdg_trade_settlement_date      IN trades.hdg_trade_settlement_date%TYPE,
                         p_hdg_redemption_date            IN trades.hdg_redemption_date%TYPE,
                         p_hdg_current_coupon_date        IN trades.hdg_current_coupon_date%TYPE,
                         p_hdg_accrued_interest_days      IN trades.hdg_accrued_interest_days%TYPE,
                         p_hdg_accrued_interest_amount    IN trades.hdg_accrued_interest_amount%TYPE,
                         p_hdg_effctv_intrst_rate         IN trades.hdg_effctv_intrst_rate%TYPE,
                         p_hdg_effctv_intrst_base_amnt    IN trades.hdg_effctv_intrst_base_amnt%TYPE,
                         p_hdg_afs_reserve_amount         IN trades.hdg_afs_reserve_amount%TYPE,
                         p_hdg_opening_reference          IN trades.hdg_opening_reference%TYPE,
                         p_hdg_client_order_link_id       IN trades.hdg_client_order_link_id%TYPE,
                         p_hdg_crrnt_coupon_pymnt_date    IN trades.hdg_crrnt_coupon_pymnt_date%TYPE,
                         p_hdg_redemption_payment_date    IN trades.hdg_redemption_payment_date%TYPE,
                         p_trade_time_2                   IN trades.trade_time_2%TYPE,
                         p_trade_time_confirmation        IN trades.trade_time_confirmation%TYPE,
                         p_hdg_report_source              IN trades.hdg_report_source%TYPE,
                         p_hdg_original_currency          IN trades.hdg_original_currency%TYPE,
                         p_hdg_original_amount            IN trades.hdg_original_amount%TYPE,
                         p_hdg_crypto_inst_ccy_fx_rate    IN trades.hdg_crypto_inst_ccy_fx_rate%TYPE,
                         p_trade_price_offset             IN trades.trade_price_offset%TYPE,
                         p_qt_is_frst_gd_in_cntns_trdng   IN trades.qt_is_frst_gd_in_cntns_trdng%TYPE,
                         p_hdg_trade_settlement_ref       IN trades.hdg_trade_settlement_ref%TYPE,
                         p_price_stream_code              IN trades.price_stream_code%TYPE,
                         p_capital_gains_in_tax_ccy       IN trades.capital_gains_in_tax_currency%TYPE,
                         p_is_pair_ccy_in_frctnl_prts     IN trades.is_pair_ccy_in_frctnl_prts%TYPE,
                         p_bid_price_offset               IN trades.bid_price_offset%TYPE,
                         p_ask_price_offset               IN trades.ask_price_offset%TYPE,
                         p_price_offset_type              IN trades.price_offset_type%TYPE,
                         p_value_date                     IN trades.value_date%TYPE,
                         p_spot_price                     IN trades.spot_price%TYPE,
                         p_pair_currency                  IN trades.pair_currency%TYPE,
                         p_primary_currency               IN trades.primary_currency%TYPE,
                         p_secondary_currency             IN trades.secondary_currency%TYPE,
                         p_primary_amount                 IN trades.primary_amount%TYPE,
                         p_secondary_amount               IN trades.secondary_amount%TYPE,
                         p_fx_tenor                       IN trades.fx_tenor%TYPE,
                         p_fx_trade_date                  IN trades.fx_trade_date%TYPE,
                         p_swap_quote_id                  IN trades.swap_quote_id%TYPE,
                         p_full_spread_trade_price        IN trades.full_spread_trade_price%TYPE
                         );

  -- ===================================================================================
  -- put_historic_annl_tx_amnts
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put historic annual tax amounts
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_historic_annl_tx_amnts(p_user                           IN historic_annl_tx_amnts.created_by%TYPE,
                                         p_effective_start_timestamp      IN historic_annl_tx_amnts.effective_start_timestamp%TYPE,
                                         p_historic_annl_tx_amnts         IN historic_annl_tx_amnts_tab
                                         );

  -- ===================================================================================
  -- get_order_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of order_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform MM or NG
  --     p_trade_time_from                 trade time from - Time trade was created at source
  --     p_trade_time_to                   trade time to - Time trade was created at source
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20005    Invalid Platform Name. Expected Platform names are NG or MMCFD or MMSB
  --      -20004    Dafault Exception
  -- -----------------------------------------------------------------------------------
FUNCTION get_order_ids(
    p_platform        IN trades.platform%TYPE,
    p_trade_time_from IN trades.creation_time%TYPE,
    p_trade_time_to   IN trades.creation_time%TYPE)
  RETURN SYS_REFCURSOR;
  /***** Begin Modification for V4.5 - BER774 *****/
  -- ===================================================================================
  -- get_hedge_trade_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of hedge trade_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform - NG
  --     p_trade_time_from                 trade time from - Time trade was created at source
  --     p_trade_time_to                   trade time to - Time trade was created at source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  --     -20005    Invalid Platform Name. Expected Platform names are NG or MMCFD or MMSB
  -- -----------------------------------------------------------------------------------
FUNCTION get_hedge_trade_ids(
    p_platform        IN trades.platform%TYPE,
    p_trade_time_from IN trades.creation_time%TYPE,
    p_trade_time_to   IN trades.creation_time%TYPE)
  RETURN SYS_REFCURSOR;
  /***** End Modification for V4.5 *****/
  -- ===================================================================================
  -- trade_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of trade_id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_platform                        platform - Trading Platform MMCFD or MMSB or NG
  --     p_trade_time_from                 trade time from - Time trade was created at source
  --     p_trade_time_to                   trade time to - Time trade was created at source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  --     -20005    Invalid Platform Name. Expected Platform names are NG or MMCFD or MMSB
  -- -----------------------------------------------------------------------------------
FUNCTION get_trade_ids(
    p_platform        IN trades.platform%TYPE,
    p_trade_time_from IN trades.creation_time%TYPE,
    p_trade_time_to   IN trades.creation_time%TYPE)
  RETURN SYS_REFCURSOR;
  -- ===================================================================================
  -- get_stubbed_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed cash account id's
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed identity ids to return (Optional - not set returns all)
  --     p_platform                       Cash Account Platform
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
FUNCTION get_stubbed_ids(
    p_batch_limit IN NUMBER,
    p_platform    IN VARCHAR2)
  RETURN SYS_REFCURSOR;
END nrg_trade;
/