CREATE OR REPLACE VIEW bi_views.NG_CASH_BALANCES AS
(
SELECT "PLATFORM","ACCOUNT_NUMBER","CURRENCY","CLEARED_ACCOUNT_BALANCE","UNCLEARED_ACCOUNT_BALANCE" FROM (
WITH w_current_date AS
--This returns the most recent eod_cash_position_snapshot and builds the required date parameters
 (SELECT /*+ MATERIALIZE */
   last_business_date,
   last_business_date + (CASE to_char(last_business_date, 'DY')
     WHEN 'SAT' THEN
      +2
     WHEN 'SUN' THEN
      +1
     ELSE
      0
   END) AS last_reporting_date,
   (last_business_date + 1) AS current_business_date,
   (last_business_date) + (CASE to_char(last_business_date, 'DY')
     WHEN 'FRI' THEN
      +3
     WHEN 'SAT' THEN
      +2
     ELSE
      1
   END) AS current_reporting_date
    FROM (SELECT MAX(business_date) AS last_business_date
            FROM bi_ods.eod_cash_position_snapshots
           WHERE platform = 'NG'))
--Cash Balance is the sum of the most recent EOD and the cash tranasction entries for business dates after the EOD. This can be more than 1 business day, especially when the EOD is running
SELECT r.platform,
       r.account_number,
       r.amount_currency AS currency,
       SUM(r.amount) cleared_account_balance,
       SUM(r.amount) uncleared_account_balance
  FROM (
        --Get the last eod cash positions
        SELECT eod.platform,
               eod.account_number,
               eod.amount,
               eod.amount_currency
          FROM bi_ods.eod_cash_positions eod
          JOIN w_current_date
            ON w_current_date.last_business_date = eod.business_date
           AND w_current_date.last_reporting_date = eod.reporting_date
         WHERE eod.platform = 'NG'
        UNION ALL
        --Get the cash transactions after the last eod date, this can be more than 1 day
        SELECT cte.platform,
               cte.account_number,
               cte.amount,
               cte.amount_ccy
          FROM bi_ods.cash_transaction_entries cte
          JOIN w_current_date
            ON cte.business_date >= w_current_date.current_business_date
           AND cte.reporting_date >= w_current_date.current_reporting_date
         WHERE cte.platform = 'NG') r
 GROUP BY r.platform, r.account_number, r.amount_currency));
