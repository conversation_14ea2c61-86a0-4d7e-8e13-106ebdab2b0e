CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_price_subscription AS
  -- ===================================================================================
  -- NRG_PRICE_SUBSCRIPTION
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the price subscription model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     03/09/2012   Sanket Mittal      1.0    Creation
  --     24/04/2013   Sanket Mittal      1.1    Removed the parameters business date
  --     16/12/2013   Adam Krasnicki     1.2    The procedure put_price_subscription_detail is rebuilt to avoid table locking
  --     28/03/2014   Adam Krasnicki     1.3    The procedure put_price_subscription_detail has been rebuilt to avoid table locking
  --     10/09/2014   Adam Krasnicki     1.4    History table not need, no updates on price_subscriptions table needed
  -- ===================================================================================

  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '1.1';

  gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
  gc_true               CONSTANT PLS_INTEGER := 1;
  gc_false              CONSTANT PLS_INTEGER := 0;
  gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

  --
  --
  -- ===================================================================================
  --  PRIVATE MODULES
  -- ===================================================================================
  --
  --

  --
  --
  -- ===================================================================================
  --  put_price_subscription_detail
  -- ===================================================================================
  --
  --
  PROCEDURE put_price_subscription_detail(p_user                           IN VARCHAR2,
                                          p_effective_start_timestamp      IN TIMESTAMP,
                                          p_server_ip_address              IN VARCHAR2,
                                          p_identity_id                    IN NUMBER,
                                          p_session_key                    IN VARCHAR2,
                                          --p_business_date                  IN price_subscriptions.business_date%TYPE,
                                          --p_reporting_date                 IN price_subscriptions.reporting_date%TYPE,
                                          p_logical_load_timestamp         IN TIMESTAMP,
                                          p_price_subscription_detail      IN price_subscription_detail_obj) IS

    lv_price_subscription_detail      price_subscriptions%ROWTYPE;

    lv_business_date                  price_subscriptions.business_date%TYPE;
    lv_reporting_date                 price_subscriptions.reporting_date%TYPE;

    lv_effective_start_timestamp      price_subscriptions.Effective_Start_Timestamp%TYPE;

  BEGIN

    --
    --Calculate business date
    --

    lv_business_date := nrg_common.get_business_date(p_price_subscription_detail.unsubscribe_time);

    --
    --Calculate reporting date
    --

    lv_reporting_date := nrg_common.get_reporting_date(p_price_subscription_detail.unsubscribe_time);

    logger.logger.set_module('put_price_subscription_detail');


  BEGIN
       INSERT INTO price_subscriptions
         (price_feed_symbol
         ,server_ip_address
         ,identity_id
         ,session_key
         ,logical_load_timestamp
         ,created_by
         ,create_timestamp
         ,updated_by
         ,update_timestamp
         ,effective_start_timestamp
         ,business_date
         ,reporting_date
         ,price_level
         ,subscribe_time
         ,unsubscribe_time
         ,total_subscribe_time)
        VALUES( p_price_subscription_detail.price_feed_symbol
         ,p_server_ip_address
         ,p_identity_id
         ,p_session_key
         ,p_logical_load_timestamp
         ,p_user
         ,systimestamp
         ,p_user
         ,systimestamp
         ,p_effective_start_timestamp
         ,lv_business_date
         ,lv_reporting_date
         ,p_price_subscription_detail.price_level
         ,p_price_subscription_detail.subscribe_time
         ,p_price_subscription_detail.unsubscribe_time
         ,p_price_subscription_detail.total_subscribe_time) ;
   EXCEPTION
     WHEN DUP_VAL_ON_INDEX THEN
         NULL;
     END;

 EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END;
  --
  --
  -- ===================================================================================
  --  PUBLIC MODULES
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC IS
    BEGIN
      NULL;
    END;

  -- ===================================================================================
  -- put_price_subscription
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a price subscriptions
  --
  -- Notes:
  -- ------
  --
  --    Tables potentially populated:
  --
  --      PUT_PRICE_SUBSCRIPTION
  --      PUT_PRICE_SUBSCRIPTION_H
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --    p_user                          User
  --    p_effective_start_timestamp     Effective Start Timestamp
  --    p_server_ip_address             Server IP Address
  --    p_identity_id                   Identity ID
  --    p_session_key                   User Session Key
  --    p_price_subscription_details    Price Subscription Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_price_subscription(p_user                           IN price_subscriptions.created_by%TYPE,
                                   p_effective_start_timestamp      IN price_subscriptions.effective_start_timestamp%TYPE,
                                   p_server_ip_address              IN price_subscriptions.server_ip_address%TYPE,
                                   p_identity_id                    IN price_subscriptions.identity_id%TYPE,
                                   p_session_key                    IN price_subscriptions.session_key%TYPE,
                                   --p_business_date                  IN price_subscriptions.business_date%TYPE,
                                   --p_reporting_date                 IN price_subscriptions.reporting_date%TYPE,
                                   p_price_subscription_details     IN price_subscription_detail_tab) IS

    lv_logical_load_timestamp       TIMESTAMP(6);


  BEGIN


    --
    --Initialize logger
    --

    logger.logger.set_module('put_price_subscription');

    --
    --Set the logical load timestamp
    --

    lv_logical_load_timestamp := SYSTIMESTAMP;

    --
    --Create Stubs
    --

    --
    --Identity Stub
    --

    nrg_identity.create_identity_stub (p_user                       => p_user,
                                       p_logical_load_timestamp     => lv_logical_load_timestamp,
                                       p_effective_start_timestamp  => p_effective_start_timestamp,
                                       p_identity_id                => p_identity_id);

    --
    --Session Stub
    --

    nrg_session.create_session_stub (p_user                           => p_user,
                                     p_logical_load_timestamp         => lv_logical_load_timestamp,
                                     p_effective_start_timestamp      => p_effective_start_timestamp,
                                     p_session_key                    => p_session_key);

    --
    --Now put all the price subscriptions
    --

    FOR lv_cnt IN 1..p_price_subscription_details.COUNT LOOP
      put_price_subscription_detail(p_user                      => p_user,
                                    p_effective_start_timestamp => p_effective_start_timestamp,
                                    p_server_ip_address         => p_server_ip_address,
                                    p_identity_id               => p_identity_id,
                                    p_session_key               => p_session_key,
                                    --p_business_date             => p_business_date,
                                    --p_reporting_date            => p_reporting_date,
                                    p_logical_load_timestamp    => lv_logical_load_timestamp,
                                    p_price_subscription_detail => p_price_subscription_details(lv_cnt));
    END LOOP;

  EXCEPTION
        /*WHEN lex_identity_not_found THEN
            logger.logger.severe('Price Subscription Deleted Before Update and After Insert');
            logger.logger.set_module(NULL);
            raise_application_error(-20003, 'Price Subscription Deleted Before Update and After Insert');
        WHEN lex_unknown_operation_type THEN
            logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
            logger.logger.set_module(NULL);
            raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');*/
        WHEN OTHERS THEN
            logger.logger.severe(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            raise_application_error(-20004, logger.logger.error_backtrace);
  END;
END nrg_price_subscription;
/