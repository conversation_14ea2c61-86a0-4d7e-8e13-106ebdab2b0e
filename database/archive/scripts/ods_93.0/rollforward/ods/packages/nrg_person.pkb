CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_person
AS
    -- ===================================================================================
      -- NRG_PERSON
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the person model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     05/09/2011   Manoj Kumar        1.0    Creation
    --     20/10/2011   Manoj Kumar        1.1    Modified for concurrent writes
    --     08/11/2011   Sanket Mittal      1.2    Modified as per review comments
    --     01/12/2011   Sanket Mittal      1.3    Modified the person Relationships method to prevent deadlocks
    --     31/01/2012   Mark Gornicki      1.4    Added the function get_stubbed_ids
    --     15/02/2012   Manoj Kumar        1.5    Inserted Cardinality Hints
    --     11/02/2013   Sanket Mittal      1.6    Updated the person financial details
    --     04/10/2013   Ravi Shankar       1.7    Updated to include additional fields in the persons_h table BER-640
    --     11/10/2013   Ravi Shankar Gopal 1.7    Removed Reference to persons.remediation_date BER-664
    --     10/01/2014   Adam Krasnicki     1.8    Update put_person procedure(new attribues added:
    --                                            person_trading_experince, person_trading_profiles), put_history (PERSONS_H)
    --     20/01/2014   Adam Krasnicki     1.81   Logic changed in put_trading_experience and put_trading_profiles procedures
    --     13/06/2015   Adam Krasnicki     1.81   us_citizen added
    --     16/06/2015   Adam Krasnicki     1.81   COUNTRY_OF_ISSUE, IS_DELETED added to PERSON_NATIONAL_IDENTIF_OBJ and Person_National_Identifiers
    --     22/07/2015   Adam Krasnicki     1.81   changes in person_relationship and employment details (current table not populated)
    --     02/09/2015   Adam Krasnicki     1.82   ne parameters in put_person procedure
    --     07/09/2015   Adam Krasnicki     1.82   put procedure for person_stmnt_email_address changed
    --     03/03/2016   Sanket Mittal      1.9    Updated for BER-2388 - Added new parameters record-source and source_person_id
    --     22/08/2016   Sanket Mittal      2.0    BER-2831 Integrate updated Person data contract
    --     08/02/2017   S Kinkhabwala      2.1    BER -3358  updated Person data contract
    --     17/04/2017   Sanket Mittal      2.2    BER-3537 NRG_PERSON - Data contract changes
    --     02/06/2017   Sanket Mittal      2.3    BER-3660 bi_ods.person_fnncl_dtl_srcs not logging is_deleted attribute correctly
    --     07/06/2017   Sanket Mittal      2.4    BER-3667 NRG_PERSON - New attributes
    --     24/08/2017   Patrick Dinwiddy   2.5    BER-3914 New attributes and child table
    --     30/04/2018   D. Rajurkar        2.6    PATCH for commenting alert preferences
    --     24/05/2018   Deepak Rajurkar    2.7    BER-4439 deprecated attributes related to trading profiles
    --     04/07/2018   Patrick Dinwiddy   2.8    BER-4725 new attributes for put_person
    --     22/04/2020   Patrick Dinwiddy   2.9    JCS-12344
    -- ===================================================================================
    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================

    gc_version            CONSTANT VARCHAR2(6) := '2.9';
    gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
    gc_true               CONSTANT PLS_INTEGER := 1;
    gc_false              CONSTANT PLS_INTEGER := 0;
    gc_default_timestamp  TIMESTAMP            := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');
    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================
    --
    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the persons record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_person_record             This is the old version of the person record
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history (p_old_person_record        persons%ROWTYPE,
                           p_effective_end_timestamp  persons_h.effective_end_timestamp%TYPE,
                           p_action                   persons_h.action%TYPE)
    IS

    BEGIN
      INSERT INTO persons_h
                              (
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  logical_load_timestamp,
                                  effective_start_timestamp,
                                  effective_end_timestamp,
                                  action,
                                  action_timestamp,
                                  person_id,
                                  person_version,
                                  title,
                                  first_name,
                                  last_name,
                                  gender,
                                  date_of_birth,
                                  nationality,
                                  spoken_language,
                                  exchange_data_classification,
                                  is_deleted
                                  ,financial_detail_id,
                                  origin_of_wealth,
                                  origin_of_wealth_detail,
                                  annual_income_range,
                                  annual_income_currency,
                                  val_svngs_invstmnt_range,
                                  val_svngs_invstmnt_currency,
                                  initial_lead_channel,
                                  us_citizen,
                                  marital_status,
                                  market_counterparty_partner_id,
                                  middle_name,
                                  is_politically_exposed,
                                  politically_exposed_details,
                                  financial_detail_version,
                                  investment_portfolio,
                                  investment_portfolio_currency,
                                  liquid_assets,
                                  liquid_assets_currency,
                                  cumulative_risk_limit,
                                  cumulative_risk_limit_currency,
                                  record_source,
                                  source_person_id,
                                  exchng_data_clssfctn_cnfrmd,
                                  is_accredited_investor,
                                  is_related_to_regulated_firm,
                                  is_insider_of_public_company,
                                  first_name_latin,
                                  last_name_latin,
                                  nationality_country_code,
                                  is_mifid_details_remediated,
                                  mkt_cpty_rgltry_clssfctn,
                                  country_of_birth
                                  )
                              VALUES
                              (
                                  p_old_person_record.created_by,
                                  p_old_person_record.create_timestamp,
                                  p_old_person_record.updated_by,
                                  p_old_person_record.update_timestamp,
                                  p_old_person_record.logical_load_timestamp,
                                  p_old_person_record.effective_start_timestamp,
                                  p_effective_end_timestamp,
                                  p_action,
                                  SYSTIMESTAMP,
                                  p_old_person_record.person_id,
                                  p_old_person_record.person_version,
                                  p_old_person_record.title,
                                  p_old_person_record.first_name,
                                  p_old_person_record.last_name,
                                  p_old_person_record.gender,
                                  p_old_person_record.date_of_birth,
                                  p_old_person_record.nationality,
                                  p_old_person_record.spoken_language,
                                  p_old_person_record.exchange_data_classification,
                                  p_old_person_record.is_deleted,
                                  p_old_person_record.financial_detail_id,
                                  p_old_person_record.origin_of_wealth,
                                  p_old_person_record.origin_of_wealth_detail,
                                  p_old_person_record.annual_income_range,
                                  p_old_person_record.annual_income_currency,
                                  p_old_person_record.val_svngs_invstmnt_range,
                                  p_old_person_record.val_svngs_invstmnt_currency,
                                  p_old_person_record.initial_lead_channel,
                                  p_old_person_record.Us_Citizen,
                                  p_old_person_record.marital_status,
                                  p_old_person_record.market_counterparty_partner_id,
                                  p_old_person_record.middle_name,
                                  p_old_person_record.is_politically_exposed,
                                  p_old_person_record.politically_exposed_details,
                                  p_old_person_record.financial_detail_version,
                                  p_old_person_record.investment_portfolio,
                                  p_old_person_record.investment_portfolio_currency,
                                  p_old_person_record.liquid_assets,
                                  p_old_person_record.liquid_assets_currency,
                                  p_old_person_record.cumulative_risk_limit,
                                  p_old_person_record.cumulative_risk_limit_currency,
                                  p_old_person_record.record_source,
                                  p_old_person_record.source_person_id,
                                  p_old_person_record.exchng_data_clssfctn_cnfrmd,
                                  p_old_person_record.is_accredited_investor,
                                  p_old_person_record.is_related_to_regulated_firm,
                                  p_old_person_record.is_insider_of_public_company,
                                  p_old_person_record.first_name_latin,
                                  p_old_person_record.last_name_latin,
                                  p_old_person_record.nationality_country_code,
                                  p_old_person_record.is_mifid_details_remediated,
                                  p_old_person_record.mkt_cpty_rgltry_clssfctn,
                                  p_old_person_record.country_of_birth
                              );
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
            UPDATE PERSONS_H
                SET updated_by                = p_old_person_record.updated_by,
                    action                    = p_action,
                    action_timestamp          = SYSTIMESTAMP,
                    update_timestamp          = p_old_person_record.update_timestamp,
                    logical_load_timestamp    = p_old_person_record.logical_load_timestamp,
                    person_version            = p_old_person_record.person_version,
                    title                     = p_old_person_record.title,
                    first_name                = p_old_person_record.first_name,
                    last_name                 = p_old_person_record.last_name,
                    gender                    = p_old_person_record.gender,
                    date_of_birth             = p_old_person_record.date_of_birth,
                    nationality               = p_old_person_record.nationality,
                    spoken_language           = p_old_person_record.spoken_language,
                    exchange_data_classification = p_old_person_record.exchange_data_classification,
                    is_deleted                = p_old_person_record.is_deleted
                    ,financial_detail_id        = p_old_person_record.financial_detail_id,
                    origin_of_wealth            = p_old_person_record.origin_of_wealth,
                    origin_of_wealth_detail     = p_old_person_record.origin_of_wealth_detail,
                    annual_income_range         = p_old_person_record.annual_income_range,
                    annual_income_currency      = p_old_person_record.annual_income_currency,
                    val_svngs_invstmnt_range    = p_old_person_record.val_svngs_invstmnt_range,
                    val_svngs_invstmnt_currency = p_old_person_record.val_svngs_invstmnt_currency,
                    initial_lead_channel        = p_old_person_record.initial_lead_channel,
                    us_citizen                  = p_old_person_record.Us_Citizen,
                    marital_status                 = p_old_person_record.Marital_Status,
                    market_counterparty_partner_id = p_old_person_record.Market_Counterparty_Partner_Id,
                    middle_name                    = p_old_person_record.Middle_Name,
                    is_politically_exposed         = p_old_person_record.Is_Politically_Exposed,
                    politically_exposed_details    = p_old_person_record.politically_exposed_details,
                    financial_detail_version       = p_old_person_record.financial_detail_version,
                    investment_portfolio           = p_old_person_record.investment_portfolio,
                    investment_portfolio_currency  = p_old_person_record.investment_portfolio_currency,
                    liquid_assets                  = p_old_person_record.liquid_assets,
                    liquid_assets_currency         = p_old_person_record.liquid_assets_currency,
                    cumulative_risk_limit          = p_old_person_record.cumulative_risk_limit,
                    cumulative_risk_limit_currency = p_old_person_record.cumulative_risk_limit_currency,
                    record_source                  = p_old_person_record.record_source,
                    source_person_id               = p_old_person_record.source_person_id,
                    exchng_data_clssfctn_cnfrmd    = p_old_person_record.exchng_data_clssfctn_cnfrmd,
                    is_accredited_investor         = p_old_person_record.is_accredited_investor,
                    is_related_to_regulated_firm   = p_old_person_record.is_related_to_regulated_firm,
                    is_insider_of_public_company   = p_old_person_record.is_insider_of_public_company,
                    first_name_latin               = p_old_person_record.first_name_latin,
                    last_name_latin                = p_old_person_record.last_name_latin,
                    nationality_country_code       = p_old_person_record.nationality_country_code,
                    is_mifid_details_remediated    = p_old_person_record.is_mifid_details_remediated,
                    mkt_cpty_rgltry_clssfctn       = p_old_person_record.mkt_cpty_rgltry_clssfctn,
                    country_of_birth               = p_old_person_record.country_of_birth
              WHERE person_id                 = p_old_person_record.person_id AND
                    effective_start_timestamp = p_old_person_record.effective_start_timestamp AND
                    (/* only where there have been for data changes */
                      nrg_common.has_value_changed(person_version,p_old_person_record.person_version) = 1 OR
                      nrg_common.has_value_changed(title,p_old_person_record.title) = 1 OR
                      nrg_common.has_value_changed(first_name,p_old_person_record.first_name) = 1 OR
                      nrg_common.has_value_changed(last_name,p_old_person_record.last_name) = 1 OR
                      nrg_common.has_value_changed(gender,p_old_person_record.gender) = 1 OR
                      nrg_common.has_value_changed(date_of_birth,p_old_person_record.date_of_birth) = 1 OR
                      nrg_common.has_value_changed(nationality,p_old_person_record.nationality) = 1 OR
                      nrg_common.has_value_changed(spoken_language,p_old_person_record.spoken_language) = 1 OR
                      nrg_common.has_value_changed(exchange_data_classification,p_old_person_record.exchange_data_classification) = 1 OR
                      nrg_common.has_value_changed(is_deleted,p_old_person_record.is_deleted) = 1 OR
                      nrg_common.has_value_changed(financial_detail_id, p_old_person_record.financial_detail_id) = 1 OR
                      nrg_common.has_value_changed(origin_of_wealth, p_old_person_record.origin_of_wealth) = 1 OR
                      nrg_common.has_value_changed(origin_of_wealth_detail, p_old_person_record.origin_of_wealth_detail) = 1 OR
                      nrg_common.has_value_changed(annual_income_range, p_old_person_record.annual_income_range) = 1 OR
                      nrg_common.has_value_changed(annual_income_currency, p_old_person_record.annual_income_currency) = 1 OR
                      nrg_common.has_value_changed(val_svngs_invstmnt_range, p_old_person_record.val_svngs_invstmnt_range) = 1 OR
                      nrg_common.has_value_changed(val_svngs_invstmnt_currency, p_old_person_record.val_svngs_invstmnt_currency) = 1 OR
                      nrg_common.has_value_changed(initial_lead_channel, p_old_person_record.initial_lead_channel) = 1 OR
                      nrg_common.has_value_changed(us_citizen, p_old_person_record.us_citizen) = 1 OR
                      nrg_common.has_value_changed(marital_status, p_old_person_record.Marital_Status) = 1 OR
                      nrg_common.has_value_changed(market_counterparty_partner_id, p_old_person_record.Market_Counterparty_Partner_Id) = 1 OR
                      nrg_common.has_value_changed(middle_name, p_old_person_record.Middle_Name) = 1 OR
                      nrg_common.has_value_changed(is_politically_exposed, p_old_person_record.Is_Politically_Exposed) = 1 OR
                      nrg_common.has_value_changed(politically_exposed_details, p_old_person_record.politically_exposed_details) = 1 OR
                      nrg_common.has_value_changed(financial_detail_version, p_old_person_record.financial_detail_version) = 1 OR
                      nrg_common.has_value_changed(investment_portfolio, p_old_person_record.investment_portfolio) = 1 OR
                      nrg_common.has_value_changed(investment_portfolio_currency, p_old_person_record.investment_portfolio_currency) = 1 OR
                      nrg_common.has_value_changed(liquid_assets, p_old_person_record.liquid_assets) = 1 OR
                      nrg_common.has_value_changed(liquid_assets_currency, p_old_person_record.liquid_assets_currency) = 1 OR
                      nrg_common.has_value_changed(cumulative_risk_limit, p_old_person_record.cumulative_risk_limit) = 1 OR
                      nrg_common.has_value_changed(cumulative_risk_limit_currency, p_old_person_record.cumulative_risk_limit_currency) = 1 OR
                      nrg_common.has_value_changed(record_source, p_old_person_record.record_source) = 1 OR
                      nrg_common.has_value_changed(source_person_id, p_old_person_record.source_person_id) = 1 OR
                      nrg_common.has_value_changed(exchng_data_clssfctn_cnfrmd, p_old_person_record.exchng_data_clssfctn_cnfrmd) = 1 OR
                      nrg_common.has_value_changed(is_accredited_investor, p_old_person_record.is_accredited_investor) = 1 OR
                      nrg_common.has_value_changed(is_related_to_regulated_firm, p_old_person_record.is_related_to_regulated_firm) = 1 OR
                      nrg_common.has_value_changed(is_insider_of_public_company, p_old_person_record.is_insider_of_public_company) = 1 OR
                      nrg_common.has_value_changed(first_name_latin, p_old_person_record.first_name_latin) = 1 OR
                      nrg_common.has_value_changed(last_name_latin, p_old_person_record.last_name_latin) = 1 OR
                      nrg_common.has_value_changed(nationality_country_code, p_old_person_record.nationality_country_code) = 1 OR
                      nrg_common.has_value_changed(is_mifid_details_remediated, p_old_person_record.is_mifid_details_remediated) = 1 OR
                      nrg_common.has_value_changed(mkt_cpty_rgltry_clssfctn, p_old_person_record.mkt_cpty_rgltry_clssfctn) = 1 OR
                      nrg_common.has_value_changed(country_of_birth, p_old_person_record.country_of_birth) = 1
                    );
    END put_history;

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the trading account record
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_old_person_address            This is the old version of the person address
    --      p_effective_end_timestamp       This is the end time for the record
    --      p_action                        Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_old_person_address person_addresses%ROWTYPE,
                        p_effective_end_timestamp person_addresses_h.effective_end_timestamp%TYPE,
                        p_action person_addresses_h.action%TYPE) IS

  BEGIN
    INSERT INTO person_addresses_h
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              address_id,
                                              address_version,
                                              address_type,
                                              address_line_1,
                                              address_line_2,
                                              address_line_3,
                                              address_line_4,
                                              address_line_5,
                                              post_code,
                                              country_code,
                                              is_primary,
                                              is_previous,
                                              years_at_address,
                                              is_deleted,
                                              employment_detail_id,
                                              record_source,
                                              source_address_id
                                          )
                                      VALUES(
                                              p_old_person_address.created_by,
                                              p_old_person_address.create_timestamp,
                                              p_old_person_address.updated_by,
                                              p_old_person_address.update_timestamp,
                                              p_old_person_address.logical_load_timestamp,
                                              p_old_person_address.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_address.person_id,
                                              p_old_person_address.address_id,
                                              p_old_person_address.address_version,
                                              p_old_person_address.address_type,
                                              p_old_person_address.address_line_1,
                                              p_old_person_address.address_line_2,
                                              p_old_person_address.address_line_3,
                                              p_old_person_address.address_line_4,
                                              p_old_person_address.address_line_5,
                                              p_old_person_address.post_code,
                                              p_old_person_address.country_code,
                                              p_old_person_address.is_primary,
                                              p_old_person_address.is_previous,
                                              p_old_person_address.years_at_address,
                                              p_old_person_address.is_deleted,
                                              p_old_person_address.employment_detail_id,
                                              p_old_person_address.record_source,
                                              p_old_person_address.source_address_id
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_addresses_h
        SET    created_by = p_old_person_address.created_by,
               create_timestamp = p_old_person_address.create_timestamp,
               updated_by = p_old_person_address.updated_by,
               update_timestamp = p_old_person_address.update_timestamp,
               logical_load_timestamp = p_old_person_address.logical_load_timestamp,
               effective_start_timestamp = p_old_person_address.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               address_version = p_old_person_address.address_version,
               address_type = p_old_person_address.address_type,
               address_line_1 = p_old_person_address.address_line_1,
               address_line_2 = p_old_person_address.address_line_2,
               address_line_3 = p_old_person_address.address_line_3,
               address_line_4 = p_old_person_address.address_line_4,
               address_line_5 = p_old_person_address.address_line_5,
               post_code = p_old_person_address.post_code,
               country_code = p_old_person_address.country_code,
               is_primary = p_old_person_address.is_primary,
               is_previous = p_old_person_address.is_previous,
               years_at_address = p_old_person_address.years_at_address,
               is_deleted = p_old_person_address.is_deleted,
               employment_detail_id = p_old_person_address.Employment_Detail_Id,
               record_source = p_old_person_address.record_source,
               source_address_id = p_old_person_address.source_address_id
      WHERE    address_id = p_old_person_address.address_id AND
                effective_start_timestamp = p_old_person_address.effective_start_timestamp AND
                person_id = p_old_person_address.person_id AND
                (nrg_common.has_value_changed(address_version,p_old_person_address.address_version ) =1 OR
                 nrg_common.has_value_changed(address_type,p_old_person_address.address_type ) =1 OR
                 nrg_common.has_value_changed(address_line_1,p_old_person_address.address_line_1 ) =1 OR
                 nrg_common.has_value_changed(address_line_2,p_old_person_address.address_line_2 ) =1 or
                 nrg_common.has_value_changed(address_line_3,p_old_person_address.address_line_3 ) =1 OR
                 nrg_common.has_value_changed(address_line_4,p_old_person_address.address_line_4 ) =1 OR
                 nrg_common.has_value_changed(address_line_5,p_old_person_address.address_line_5 ) =1 OR
                 nrg_common.has_value_changed(post_code,p_old_person_address.post_code ) =1 OR
                 nrg_common.has_value_changed(country_code,p_old_person_address.country_code ) =1 OR
                 nrg_common.has_value_changed(is_primary,p_old_person_address.is_primary ) =1 OR
                 nrg_common.has_value_changed(is_previous,p_old_person_address.is_previous ) =1 OR
                 nrg_common.has_value_changed(years_at_address,p_old_person_address.years_at_address )= 1 OR
                 nrg_common.has_value_changed(is_deleted,p_old_person_address.is_deleted) = 1 OR
                 nrg_common.has_value_changed(employment_detail_id, p_old_person_address.Employment_Detail_Id) =1 OR
                 nrg_common.has_value_changed(record_source, p_old_person_address.record_source) = 1 OR
                 nrg_common.has_value_changed(source_address_id, p_old_person_address.source_address_id) = 1);
  END put_history;

  PROCEDURE put_history(p_old_person_alias person_aliases%ROWTYPE,
                        p_effective_end_timestamp person_aliases_h.effective_end_timestamp%TYPE,
                        p_action person_aliases_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_aliases_h
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              alias_id,
                                              alias_version,
                                              alias_type,
                                              alias_name
                                          )
                                      values(
                                              p_old_person_alias.created_by,
                                              p_old_person_alias.create_timestamp,
                                              p_old_person_alias.updated_by,
                                              p_old_person_alias.update_timestamp,
                                              p_old_person_alias.logical_load_timestamp,
                                              p_old_person_alias.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_alias.person_id,
                                              p_old_person_alias.alias_id,
                                              p_old_person_alias.alias_version,
                                              p_old_person_alias.alias_type,
                                              p_old_person_alias.alias_name
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_aliases_h
        SET    created_by = p_old_person_alias.created_by,
               create_timestamp = p_old_person_alias.create_timestamp,
               updated_by = p_old_person_alias.updated_by,
               update_timestamp = p_old_person_alias.update_timestamp,
               logical_load_timestamp = p_old_person_alias.logical_load_timestamp,
               effective_start_timestamp = p_old_person_alias.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               alias_version = p_old_person_alias.alias_version,
               alias_type = p_old_person_alias.alias_type,
               alias_name = p_old_person_alias.alias_name
        WHERE  alias_id = p_old_person_alias.alias_id AND
               effective_start_timestamp = p_old_person_alias.effective_start_timestamp AND
               person_id = p_old_person_alias.person_id AND
               (nrg_common.has_value_changed(alias_version,p_old_person_alias.alias_version ) =1 OR
                nrg_common.has_value_changed(alias_name,p_old_person_alias.alias_name ) =1 or
                nrg_common.has_value_changed(alias_name,p_old_person_alias.alias_name ) =1 );
  END put_history;

  PROCEDURE put_history(p_old_person_email_add person_email_addresses%ROWTYPE,
                        p_effective_end_timestamp person_email_addresses_h.effective_end_timestamp%TYPE,
                        p_action person_email_addresses_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_email_addresses_h
                                            (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              email_address_id,
                                              email_version,
                                              email_type,
                                              email_address,
                                              is_primary,
                                              is_deleted,
                                              record_source,
                                              source_email_address_id
                                          )
                                      VALUES(
                                              p_old_person_email_add.created_by,
                                              p_old_person_email_add.create_timestamp,
                                              p_old_person_email_add.updated_by,
                                              p_old_person_email_add.update_timestamp,
                                              p_old_person_email_add.logical_load_timestamp,
                                              p_old_person_email_add.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_email_add.person_id,
                                              p_old_person_email_add.email_address_id,
                                              p_old_person_email_add.email_version,
                                              p_old_person_email_add.email_type,
                                              p_old_person_email_add.email_address,
                                              p_old_person_email_add.is_primary,
                                              p_old_person_email_add.is_deleted,
                                              p_old_person_email_add.record_source,
                                              p_old_person_email_add.source_email_address_id
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_email_addresses_h
        SET    created_by = p_old_person_email_add.created_by,
               create_timestamp = p_old_person_email_add.create_timestamp,
               updated_by = p_old_person_email_add.updated_by,
               update_timestamp = p_old_person_email_add.update_timestamp,
               logical_load_timestamp = p_old_person_email_add.logical_load_timestamp,
               effective_start_timestamp = p_old_person_email_add.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               email_version = p_old_person_email_add.email_version,
               email_type = p_old_person_email_add.email_type,
               email_address = p_old_person_email_add.email_address,
               is_primary = p_old_person_email_add.is_primary,
               is_deleted = p_old_person_email_add.is_deleted,
               record_source = p_old_person_email_add.record_source,
               source_email_address_id = p_old_person_email_add.source_email_address_id
      WHERE    email_address_id = p_old_person_email_add.email_address_id AND
               effective_start_timestamp = p_old_person_email_add.effective_start_timestamp AND
               person_id = p_old_person_email_add.person_id AND
                (nrg_common.has_value_changed(email_version,p_old_person_email_add.email_version ) =1 OR
                 nrg_common.has_value_changed(email_type,p_old_person_email_add.email_type ) =1 OR
                 nrg_common.has_value_changed(email_address,p_old_person_email_add.email_address ) =1 OR
                 nrg_common.has_value_changed(is_primary,p_old_person_email_add.is_primary ) =1 OR
                 nrg_common.has_value_changed(is_deleted,p_old_person_email_add.is_deleted ) =1 OR
                 nrg_common.has_value_changed(record_source, p_old_person_email_add.record_source) = 1 OR
                 nrg_common.has_value_changed(source_email_address_id, p_old_person_email_add.source_email_address_id) = 1);
  END put_history;

  PROCEDURE put_history(p_old_person_national_id person_national_identifiers%ROWTYPE,
                        p_effective_end_timestamp person_national_identifiers_h.effective_end_timestamp%TYPE,
                        p_action person_national_identifiers_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_national_identifiers_h
                                            (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              identification_id,
                                              identification_version,
                                              identification_type,
                                              national_identication,
                                              document_expiry_date,
                                              place_of_issue,
                                              country_of_issue,
                                              is_deleted
                                          )
                                      VALUES(
                                              p_old_person_national_id.created_by,
                                              p_old_person_national_id.create_timestamp,
                                              p_old_person_national_id.updated_by,
                                              p_old_person_national_id.update_timestamp,
                                              p_old_person_national_id.logical_load_timestamp,
                                              p_old_person_national_id.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_national_id.person_id,
                                              p_old_person_national_id.identification_id,
                                              p_old_person_national_id.identification_version,
                                              p_old_person_national_id.identification_type,
                                              p_old_person_national_id.national_identication,
                                              p_old_person_national_id.document_expiry_date,
                                              p_old_person_national_id.place_of_issue,
                                              p_old_person_national_id.country_of_issue,
                                              p_old_person_national_id.is_deleted
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_national_identifiers_h
        SET    created_by = p_old_person_national_id.created_by,
               create_timestamp = p_old_person_national_id.create_timestamp,
               updated_by = p_old_person_national_id.updated_by,
               update_timestamp = p_old_person_national_id.update_timestamp,
               logical_load_timestamp = p_old_person_national_id.logical_load_timestamp,
               effective_start_timestamp = p_old_person_national_id.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               identification_version = p_old_person_national_id.identification_version,
               identification_type = p_old_person_national_id.identification_type,
               national_identication = p_old_person_national_id.national_identication,
               document_expiry_date = p_old_person_national_id.document_expiry_date,
               place_of_issue = p_old_person_national_id.place_of_issue,
               country_of_issue = p_old_person_national_id.Country_Of_Issue,
               is_deleted = p_old_person_national_id.Is_Deleted
      WHERE    identification_id = p_old_person_national_id.identification_id AND
               effective_start_timestamp = p_old_person_national_id.effective_start_timestamp AND
               person_id = p_old_person_national_id.person_id AND
                (nrg_common.has_value_changed(identification_version,p_old_person_national_id.identification_version ) =1 OR
                 nrg_common.has_value_changed(identification_type,p_old_person_national_id.identification_type ) =1 OR
                 nrg_common.has_value_changed(national_identication,p_old_person_national_id.national_identication ) =1 OR
                 nrg_common.has_value_changed(document_expiry_date,p_old_person_national_id.document_expiry_date ) =1 OR
                 nrg_common.has_value_changed(place_of_issue,p_old_person_national_id.place_of_issue ) =1 OR
                 nrg_common.has_value_changed(country_of_issue,p_old_person_national_id.country_of_issue ) =1 OR
                 nrg_common.has_value_changed(is_deleted,p_old_person_national_id.is_deleted ) =1 );
  END put_history;

  PROCEDURE put_history(p_old_person_relationship person_relationships%ROWTYPE,
                        p_effective_end_timestamp person_relationships_h.effective_end_timestamp%TYPE,
                        p_action person_relationships_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_relationships_h
                                            (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              relationship_id,
                                              relationship_version,
                                              person_id_from,
                                              person_id_to,
                                              relationship_type,
                                              is_previous
                                          )
                                      VALUES(
                                              p_old_person_relationship.created_by,
                                              p_old_person_relationship.create_timestamp,
                                              p_old_person_relationship.updated_by,
                                              p_old_person_relationship.update_timestamp,
                                              p_old_person_relationship.logical_load_timestamp,
                                              p_old_person_relationship.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_relationship.relationship_id,
                                              p_old_person_relationship.relationship_version,
                                              p_old_person_relationship.person_id_from,
                                              p_old_person_relationship.person_id_to,
                                              p_old_person_relationship.relationship_type,
                                              p_old_person_relationship.is_previous
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_relationships_h
        SET    created_by = p_old_person_relationship.created_by,
               create_timestamp = p_old_person_relationship.create_timestamp,
               updated_by = p_old_person_relationship.updated_by,
               update_timestamp = p_old_person_relationship.update_timestamp,
               logical_load_timestamp = p_old_person_relationship.logical_load_timestamp,
               effective_start_timestamp = p_old_person_relationship.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               relationship_version = p_old_person_relationship.relationship_version,
               relationship_type = p_old_person_relationship.relationship_type,
               is_previous = p_old_person_relationship.is_previous
      WHERE    relationship_id = p_old_person_relationship.relationship_id AND
               effective_start_timestamp = p_old_person_relationship.effective_start_timestamp AND
                (nrg_common.has_value_changed(relationship_version,p_old_person_relationship.relationship_version ) =1 OR
                 nrg_common.has_value_changed(relationship_type,p_old_person_relationship.relationship_type ) =1 OR
                 nrg_common.has_value_changed(is_previous,p_old_person_relationship.is_previous ) =1 );
  END put_history;

  PROCEDURE put_history(p_old_person_telephone_num person_telephone_numbers%ROWTYPE,
                        p_effective_end_timestamp person_telephone_numbers_h.effective_end_timestamp%TYPE,
                        p_action person_telephone_numbers_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_telephone_numbers_h
                                            (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              telephone_id,
                                              telephone_version,
                                              telephone_type,
                                              telephone_number,
                                              is_primary,
                                              is_deleted,
                                              record_source,
                                              source_telephone_id
                                          )
                                      VALUES(
                                              p_old_person_telephone_num.created_by,
                                              p_old_person_telephone_num.create_timestamp,
                                              p_old_person_telephone_num.updated_by,
                                              p_old_person_telephone_num.update_timestamp,
                                              p_old_person_telephone_num.logical_load_timestamp,
                                              p_old_person_telephone_num.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_telephone_num.person_id,
                                              p_old_person_telephone_num.telephone_id,
                                              p_old_person_telephone_num.telephone_version,
                                              p_old_person_telephone_num.telephone_type,
                                              p_old_person_telephone_num.telephone_number,
                                              p_old_person_telephone_num.is_primary,
                                              p_old_person_telephone_num.is_deleted,
                                              p_old_person_telephone_num.record_source,
                                              p_old_person_telephone_num.source_telephone_id
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_telephone_numbers_h
        SET    created_by = p_old_person_telephone_num.created_by,
               create_timestamp = p_old_person_telephone_num.create_timestamp,
               updated_by = p_old_person_telephone_num.updated_by,
               update_timestamp = p_old_person_telephone_num.update_timestamp,
               logical_load_timestamp = p_old_person_telephone_num.logical_load_timestamp,
               effective_start_timestamp = p_old_person_telephone_num.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               telephone_version = p_old_person_telephone_num.telephone_version,
               telephone_type = p_old_person_telephone_num.telephone_type,
               telephone_number = p_old_person_telephone_num.telephone_number,
               is_primary = p_old_person_telephone_num.is_primary,
               is_deleted = p_old_person_telephone_num.is_deleted,
               record_source = p_old_person_telephone_num.record_source,
               source_telephone_id = p_old_person_telephone_num.source_telephone_id
        WHERE  telephone_id = p_old_person_telephone_num.telephone_id AND
               effective_start_timestamp = p_old_person_telephone_num.effective_start_timestamp AND
               person_id = p_old_person_telephone_num.person_id AND
               (nrg_common.has_value_changed(telephone_version,p_old_person_telephone_num.telephone_version ) =1 OR
                nrg_common.has_value_changed(telephone_type,p_old_person_telephone_num.telephone_type ) =1 OR
                nrg_common.has_value_changed(telephone_number,p_old_person_telephone_num.telephone_number ) =1 OR
                nrg_common.has_value_changed(is_primary,p_old_person_telephone_num.is_primary ) =1 OR
                nrg_common.has_value_changed(is_deleted,p_old_person_telephone_num.is_deleted ) =1 OR
                nrg_common.has_value_changed(record_source, p_old_person_telephone_num.record_source) = 1 OR
                nrg_common.has_value_changed(source_telephone_id, p_old_person_telephone_num.source_telephone_id) = 1);
  END put_history;

  PROCEDURE put_history(p_old_pte                 IN person_trading_experience%ROWTYPE,
                        p_effective_end_timestamp IN person_trading_experience_h.effective_end_timestamp%TYPE,
                        p_action                  IN person_trading_experience_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_trading_experience_h
                                            (asset_class
                                            ,logical_load_timestamp
                                            ,created_by
                                            ,create_timestamp
                                            ,updated_by
                                            ,update_timestamp
                                            ,effective_start_timestamp
                                            ,effective_end_timestamp
                                            ,action
                                            ,action_timestamp
                                            ,experience
                                            ,value
                                            ,person_id)
                                      VALUES( p_old_pte.asset_class
                                             ,p_old_pte.logical_load_timestamp
                                             ,p_old_pte.created_by
                                             ,p_old_pte.create_timestamp
                                             ,p_old_pte.updated_by
                                             ,p_old_pte.update_timestamp
                                             ,p_old_pte.effective_start_timestamp
                                             ,p_effective_end_timestamp
                                             ,p_action
                                             ,systimestamp
                                             ,p_old_pte.experience
                                             ,p_old_pte.value
                                             ,p_old_pte.person_id);
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_trading_experience_h
        SET    logical_load_timestamp = p_old_pte.logical_load_timestamp
              ,created_by = p_old_pte.created_by
              ,create_timestamp = p_old_pte.create_timestamp
              ,updated_by = p_old_pte.updated_by
              ,update_timestamp = p_old_pte.update_timestamp
              ,effective_start_timestamp = p_old_pte.effective_start_timestamp
              ,effective_end_timestamp = p_effective_end_timestamp
              ,action = p_action
              ,action_timestamp = SYSTIMESTAMP
              ,experience = p_old_pte.experience
              ,value = p_old_pte.value
      WHERE    asset_class = p_old_pte.asset_class AND
               effective_start_timestamp = p_old_pte.effective_start_timestamp AND
               person_id = p_old_pte.person_id AND
                (nrg_common.has_value_changed(experience,p_old_pte.experience) =1 OR
                 nrg_common.has_value_changed(value,p_old_pte.value) =1);
  END put_history;

  PROCEDURE put_history(p_old_pmi                 IN person_mifid_idntfctns%ROWTYPE,
                        p_effective_end_timestamp IN person_mifid_idntfctns_h.effective_end_timestamp%TYPE,
                        p_action                  IN person_mifid_idntfctns_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_mifid_idntfctns_h
                                            (person_id
                                            ,mifir_identification_type
                                            ,logical_load_timestamp
                                            ,created_by
                                            ,create_timestamp
                                            ,updated_by
                                            ,update_timestamp
                                            ,effective_start_timestamp
                                            ,effective_end_timestamp
                                            ,action
                                            ,action_timestamp
                                            ,identification
                                            ,document_expiry_date
                                            ,priority
                                            ,is_verified
                                            ,is_issued)
                                      VALUES( p_old_pmi.person_id
                                             ,p_old_pmi.mifir_identification_type
                                             ,p_old_pmi.logical_load_timestamp
                                             ,p_old_pmi.created_by
                                             ,p_old_pmi.create_timestamp
                                             ,p_old_pmi.updated_by
                                             ,p_old_pmi.update_timestamp
                                             ,p_old_pmi.effective_start_timestamp
                                             ,p_effective_end_timestamp
                                             ,p_action
                                             ,systimestamp
                                             ,p_old_pmi.identification
                                             ,p_old_pmi.document_expiry_date
                                             ,p_old_pmi.priority
                                             ,p_old_pmi.is_verified
                                             ,p_old_pmi.is_issued
                                             );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_mifid_idntfctns_h
        SET    logical_load_timestamp = p_old_pmi.logical_load_timestamp
              ,created_by = p_old_pmi.created_by
              ,create_timestamp = p_old_pmi.create_timestamp
              ,updated_by = p_old_pmi.updated_by
              ,update_timestamp = p_old_pmi.update_timestamp
              ,effective_start_timestamp = p_old_pmi.effective_start_timestamp
              ,effective_end_timestamp = p_effective_end_timestamp
              ,action = p_action
              ,action_timestamp = SYSTIMESTAMP
              ,identification = p_old_pmi.identification
              ,document_expiry_date = p_old_pmi.document_expiry_date
              ,priority = p_old_pmi.priority
              ,is_verified = p_old_pmi.is_verified
              ,is_issued = p_old_pmi.is_issued
      WHERE    mifir_identification_type = p_old_pmi.mifir_identification_type AND
               effective_start_timestamp = p_old_pmi.effective_start_timestamp AND
               person_id = p_old_pmi.person_id AND
               (nrg_common.has_value_changed(identification,p_old_pmi.identification) =1 OR
                nrg_common.has_value_changed(document_expiry_date,p_old_pmi.document_expiry_date) =1 OR
                nrg_common.has_value_changed(priority,p_old_pmi.priority) =1 OR
                nrg_common.has_value_changed(is_verified,p_old_pmi.is_verified) =1 OR
                nrg_common.has_value_changed(is_issued,p_old_pmi.is_issued) =1);
  END put_history;

  PROCEDURE put_history(p_old_ptp                 IN person_trading_profiles%ROWTYPE,
                        p_effective_end_timestamp IN person_trading_profiles_h.effective_end_timestamp%TYPE,
                        p_action                  IN person_trading_profiles_h.action%TYPE) IS

  BEGIN
        INSERT INTO person_trading_profiles_h
                                            (person_id
                                            ,trading_account_id
                                            ,logical_load_timestamp
                                            ,created_by
                                            ,create_timestamp
                                            ,updated_by
                                            ,update_timestamp
                                            ,effective_start_timestamp
                                            ,effective_end_timestamp
                                            ,action
                                            ,action_timestamp
                                            ,one_click_allowed
                                            ,one_click_enabled
                                            ,one_click_style
                                            ,daily_statement_requested
                                            ,monthly_statement_requested
                                            ,yearly_statement_requested
                                            ,is_trade_confirm_email_req
                                            ,is_suspend_all_alerts
                                            ,suspend_alerts_from
                                            ,suspend_alerts_to
                                            ,notification_email
                                            ,notification_sms)
                                      VALUES( p_old_ptp.person_id
                                             ,p_old_ptp.Trading_Account_Id
                                             ,p_old_ptp.logical_load_timestamp
                                             ,p_old_ptp.created_by
                                             ,p_old_ptp.create_timestamp
                                             ,p_old_ptp.updated_by
                                             ,p_old_ptp.update_timestamp
                                             ,p_old_ptp.effective_start_timestamp
                                             ,p_effective_end_timestamp
                                             ,p_action
                                             ,systimestamp
                                             ,p_old_ptp.one_click_allowed
                                             ,p_old_ptp.one_click_enabled
                                             ,p_old_ptp.one_click_style
                                             ,p_old_ptp.daily_statement_requested
                                             ,p_old_ptp.monthly_statement_requested
                                             ,p_old_ptp.yearly_statement_requested
                                             ,p_old_ptp.is_trade_confirm_email_req
                                             ,null
						,null
						,null
						/*
						,p_old_ptp.is_suspend_all_alerts
						,p_old_ptp.suspend_alerts_from
                                             ,p_old_ptp.suspend_alerts_to */ --DR

                                             ,p_old_ptp.notification_email
                                             ,p_old_ptp.notification_sms);
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_trading_profiles_h
        SET    logical_load_timestamp = p_old_ptp.logical_load_timestamp
              ,created_by = p_old_ptp.created_by
              ,create_timestamp = p_old_ptp.create_timestamp
              ,updated_by = p_old_ptp.updated_by
              ,update_timestamp = p_old_ptp.update_timestamp
              ,effective_start_timestamp = p_old_ptp.effective_start_timestamp
              ,effective_end_timestamp = p_effective_end_timestamp
              ,action = p_action
              ,action_timestamp = SYSTIMESTAMP
              ,one_click_allowed = p_old_ptp.one_click_allowed
              ,one_click_enabled = p_old_ptp.one_click_enabled
              ,one_click_style = p_old_ptp.one_click_style
              ,daily_statement_requested = p_old_ptp.daily_statement_requested
              ,monthly_statement_requested = p_old_ptp.monthly_statement_requested
              ,yearly_statement_requested = p_old_ptp.yearly_statement_requested
              ,is_trade_confirm_email_req = p_old_ptp.is_trade_confirm_email_req
               ,is_suspend_all_alerts = null
	       ,suspend_alerts_from = null
              ,suspend_alerts_to = null --DR
              ,notification_email = p_old_ptp.notification_email
              ,notification_sms = p_old_ptp.notification_sms
      WHERE    trading_account_id = p_old_ptp.trading_account_id AND
               effective_start_timestamp = p_old_ptp.effective_start_timestamp AND
               person_id = p_old_ptp.person_id AND
                (nrg_common.has_value_changed(one_click_allowed,p_old_ptp.one_click_allowed) =1 OR
                 nrg_common.has_value_changed(one_click_enabled,p_old_ptp.one_click_enabled) =1 OR
                 nrg_common.has_value_changed(one_click_style,p_old_ptp.one_click_style) =1 OR
                 nrg_common.has_value_changed(daily_statement_requested, p_old_ptp.daily_statement_requested) =1 OR
                 nrg_common.has_value_changed(monthly_statement_requested, p_old_ptp.monthly_statement_requested) =1 OR
                 nrg_common.has_value_changed(yearly_statement_requested, p_old_ptp.yearly_statement_requested) =1 OR
                 nrg_common.has_value_changed(is_trade_confirm_email_req, p_old_ptp.is_trade_confirm_email_req) = 1 OR
                /*
		nrg_common.has_value_changed(is_suspend_all_alerts, p_old_ptp.is_suspend_all_alerts) = 1 OR
		nrg_common.has_value_changed(suspend_alerts_from, p_old_ptp.suspend_alerts_from) = 1 OR
		nrg_common.has_value_changed(suspend_alerts_to, p_old_ptp.suspend_alerts_to) = 1 OR */ --DR
                 nrg_common.has_value_changed(notification_email, p_old_ptp.notification_email) = 1 OR
                 nrg_common.has_value_changed(notification_sms, p_old_ptp.notification_sms) = 1);
  END put_history;


/*  PROCEDURE put_history(p_old_pers_financial_details     IN person_financial_details%rowtype,
                        p_effective_end_timestamp        IN person_financial_details_h.effective_end_timestamp%TYPE,
                        p_action                         IN person_financial_details_h.action%TYPE) IS

  BEGIN
    INSERT INTO person_financial_details_h(financial_detail_id
                                           ,logical_load_timestamp
                                           ,created_by
                                           ,create_timestamp
                                           ,updated_by
                                           ,update_timestamp
                                           ,effective_start_timestamp
                                           ,effective_end_timestamp
                                           ,action
                                           ,action_timestamp
                                           ,person_id
                                           ,financial_detail_version
                                           ,annual_income_range
                                           ,annual_income_range_currency
                                           ,savings_and_investments_range
                                           ,savings_and_invest_range_ccy
                                           ,investment_portfolio
                                           ,investment_portfolio_currency
                                           ,liquid_assets
                                           ,liquid_assets_currency
                                           ,cumulative_risk_limit
                                           ,cumulative_risk_limit_currency)
                                      VALUES(p_old_pers_financial_details.financial_detail_id
                                           ,p_old_pers_financial_details.logical_load_timestamp
                                           ,p_old_pers_financial_details.created_by
                                           ,p_old_pers_financial_details.create_timestamp
                                           ,p_old_pers_financial_details.updated_by
                                           ,p_old_pers_financial_details.update_timestamp
                                           ,p_old_pers_financial_details.effective_start_timestamp
                                           ,p_effective_end_timestamp
                                           ,p_action
                                           ,systimestamp
                                           ,p_old_pers_financial_details.person_id
                                           ,p_old_pers_financial_details.financial_detail_version
                                           ,p_old_pers_financial_details.annual_income_range
                                           ,p_old_pers_financial_details.annual_income_range_currency
                                           ,p_old_pers_financial_details.savings_and_investments_range
                                           ,p_old_pers_financial_details.savings_and_invest_range_ccy
                                           ,p_old_pers_financial_details.investment_portfolio
                                           ,p_old_pers_financial_details.investment_portfolio_currency
                                           ,p_old_pers_financial_details.liquid_assets
                                           ,p_old_pers_financial_details.liquid_assets_currency
                                           ,p_old_pers_financial_details.cumulative_risk_limit
                                           ,p_old_pers_financial_details.cumulative_risk_limit_currency
                                           );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_financial_details_h
        SET    created_by                     = p_old_pers_financial_details.created_by
              ,create_timestamp               = p_old_pers_financial_details.create_timestamp
              ,updated_by                     = p_old_pers_financial_details.updated_by
              ,update_timestamp               = p_old_pers_financial_details.update_timestamp
              ,logical_load_timestamp         = p_old_pers_financial_details.logical_load_timestamp
              ,effective_start_timestamp      = p_old_pers_financial_details.effective_start_timestamp
              ,effective_end_timestamp        = p_effective_end_timestamp
              ,action                         = p_action
              ,action_timestamp               = SYSTIMESTAMP
              ,financial_detail_version       = p_old_pers_financial_details.financial_detail_version
              ,annual_income_range            = p_old_pers_financial_details.annual_income_range
              ,annual_income_range_currency   = p_old_pers_financial_details.annual_income_range_currency
              ,savings_and_investments_range  = p_old_pers_financial_details.savings_and_investments_range
              ,savings_and_invest_range_ccy   = p_old_pers_financial_details.savings_and_invest_range_ccy
              ,investment_portfolio           = p_old_pers_financial_details.investment_portfolio
              ,investment_portfolio_currency  = p_old_pers_financial_details.investment_portfolio_currency
              ,liquid_assets                  = p_old_pers_financial_details.liquid_assets
              ,liquid_assets_currency         = p_old_pers_financial_details.liquid_assets_currency
              ,cumulative_risk_limit          = p_old_pers_financial_details.cumulative_risk_limit
              ,cumulative_risk_limit_currency = p_old_pers_financial_details.cumulative_risk_limit_currency
      WHERE    financial_detail_id            = p_old_pers_financial_details.financial_detail_id AND
               effective_start_timestamp      = p_old_pers_financial_details.effective_start_timestamp AND
               person_id                      = p_old_pers_financial_details.person_id AND
               (nrg_common.has_value_changed(financial_detail_version    , p_old_pers_financial_details.financial_detail_version) = 1 OR
               nrg_common.has_value_changed(annual_income_range          , p_old_pers_financial_details.annual_income_range) = 1 OR
               nrg_common.has_value_changed(annual_income_range_currency , p_old_pers_financial_details.annual_income_range_currency ) = 1 OR
               nrg_common.has_value_changed(savings_and_investments_range, p_old_pers_financial_details.savings_and_investments_range) = 1 OR
               nrg_common.has_value_changed(savings_and_invest_range_ccy , p_old_pers_financial_details.savings_and_invest_range_ccy) = 1 OR
               nrg_common.has_value_changed(investment_portfolio         , p_old_pers_financial_details.investment_portfolio) = 1 OR
               nrg_common.has_value_changed(investment_portfolio_currency, p_old_pers_financial_details.investment_portfolio_currency ) = 1 OR
               nrg_common.has_value_changed(liquid_assets                , p_old_pers_financial_details.liquid_assets) = 1 OR
               nrg_common.has_value_changed(liquid_assets_currency       , p_old_pers_financial_details.liquid_assets_currency) = 1 OR
               nrg_common.has_value_changed(cumulative_risk_limit        , p_old_pers_financial_details.cumulative_risk_limit ) = 1 OR
               nrg_common.has_value_changed(cumulative_risk_limit_currency , p_old_pers_financial_details.cumulative_risk_limit_currency) = 1
               );
  END put_history;*/

  PROCEDURE put_history(p_old_person_employment_det person_employment_details%rowtype,
                        p_effective_end_timestamp person_employment_details_h.effective_end_timestamp%type,
                        p_action person_employment_details_h.action%TYPE) IS

  BEGIN
    INSERT INTO person_employment_details_h
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              employment_detail_id,
                                              employment_detail_version,
                                              employment_status,
                                              job_title,
                                              company_name,
                                              nature_of_business,
                                              is_fsa_regulated,
                                              company_registration_number,
                                              industry_further_details,
                                              industry_other_details,
                                              is_former_employment,
                                              educational_establishment,
                                              job_title_details,
                                              is_deleted
                                          )
                                      VALUES(
                                              p_old_person_employment_det.created_by,
                                              p_old_person_employment_det.create_timestamp,
                                              p_old_person_employment_det.updated_by,
                                              p_old_person_employment_det.update_timestamp,
                                              p_old_person_employment_det.logical_load_timestamp,
                                              p_old_person_employment_det.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_employment_det.person_id,
                                              p_old_person_employment_det.employment_detail_id,
                                              p_old_person_employment_det.employment_detail_version,
                                              p_old_person_employment_det.employment_status,
                                              p_old_person_employment_det.job_title,
                                              p_old_person_employment_det.company_name,
                                              p_old_person_employment_det.nature_of_business,
                                              p_old_person_employment_det.is_fsa_regulated,
                                              p_old_person_employment_det.company_registration_number,
                                              p_old_person_employment_det.industry_further_details,
                                              p_old_person_employment_det.industry_other_details,
                                              p_old_person_employment_det.is_former_employment,
                                              p_old_person_employment_det.educational_establishment,
                                              p_old_person_employment_det.job_title_details,
                                              p_old_person_employment_det.is_deleted
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE person_employment_details_h
        SET    created_by = p_old_person_employment_det.created_by,
               create_timestamp = p_old_person_employment_det.create_timestamp,
               updated_by = p_old_person_employment_det.updated_by,
               update_timestamp = p_old_person_employment_det.update_timestamp,
               logical_load_timestamp = p_old_person_employment_det.logical_load_timestamp,
               effective_start_timestamp = p_old_person_employment_det.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               employment_detail_version = p_old_person_employment_det.employment_detail_version,
               employment_status = p_old_person_employment_det.employment_status,
               job_title = p_old_person_employment_det.job_title,
               company_name = p_old_person_employment_det.company_name,
               nature_of_business = p_old_person_employment_det.nature_of_business,
               is_fsa_regulated = p_old_person_employment_det.is_fsa_regulated,
               company_registration_number = p_old_person_employment_det.company_registration_number,
               industry_further_details = p_old_person_employment_det.industry_further_details,
               industry_other_details = p_old_person_employment_det.industry_other_details,
               is_former_employment = p_old_person_employment_det.is_former_employment,
               educational_establishment = p_old_person_employment_det.educational_establishment,
               job_title_details = p_old_person_employment_det.job_title_details,
               is_deleted = p_old_person_employment_det.is_deleted
      WHERE    employment_detail_id = p_old_person_employment_det.employment_detail_id AND
               effective_start_timestamp = p_old_person_employment_det.effective_start_timestamp AND
               person_id = p_old_person_employment_det.person_id AND
                (nrg_common.has_value_changed(employment_detail_version,p_old_person_employment_det.employment_detail_version) =1 OR
                 nrg_common.has_value_changed(employment_status,p_old_person_employment_det.employment_status) =1 OR
                 nrg_common.has_value_changed(job_title,p_old_person_employment_det.job_title) =1 OR
                 nrg_common.has_value_changed(company_name,p_old_person_employment_det.company_name) =1 OR
                 nrg_common.has_value_changed(nature_of_business,p_old_person_employment_det.nature_of_business) =1 OR
                 nrg_common.has_value_changed(is_fsa_regulated,p_old_person_employment_det.is_fsa_regulated) =1 OR
                 nrg_common.has_value_changed(company_registration_number,p_old_person_employment_det.company_registration_number) =1 OR
                 nrg_common.has_value_changed(industry_further_details , p_old_person_employment_det.industry_further_details) =1 OR
                 nrg_common.has_value_changed(industry_other_details , p_old_person_employment_det.industry_other_details) =1 OR
                 nrg_common.has_value_changed(is_former_employment, p_old_person_employment_det.is_former_employment) =1 OR
                 nrg_common.has_value_changed(educational_establishment , p_old_person_employment_det.educational_establishment) =1 OR
                 nrg_common.has_value_changed(job_title_details , p_old_person_employment_det.job_title_details) =1 OR
                 nrg_common.has_value_changed(is_deleted , p_old_person_employment_det.is_deleted) =1 );
  END put_history;

  PROCEDURE put_history(p_old_person_cmplnc_det prsn_cmplnc_eml_addrsss%rowtype,
                        p_effective_end_timestamp prsn_cmplnc_eml_addrsss_h.effective_end_timestamp%type,
                        p_action prsn_cmplnc_eml_addrsss_h.action%TYPE) IS

  BEGIN
    INSERT INTO prsn_cmplnc_eml_addrsss_h
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              employment_detail_id,
                                              compliance_email_id,
                                              email_address,
                                              is_deleted
                                          )
                                      VALUES(
                                              p_old_person_cmplnc_det.created_by,
                                              p_old_person_cmplnc_det.create_timestamp,
                                              p_old_person_cmplnc_det.updated_by,
                                              p_old_person_cmplnc_det.update_timestamp,
                                              p_old_person_cmplnc_det.logical_load_timestamp,
                                              p_old_person_cmplnc_det.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_old_person_cmplnc_det.employment_detail_id,
                                              p_old_person_cmplnc_det.compliance_email_id,
                                              p_old_person_cmplnc_det.email_address,
                                              p_old_person_cmplnc_det.is_deleted
                                            );
    EXCEPTION
      WHEN dup_val_on_index THEN
        UPDATE prsn_cmplnc_eml_addrsss_h
        SET    created_by = p_old_person_cmplnc_det.created_by,
               create_timestamp = p_old_person_cmplnc_det.create_timestamp,
               updated_by = p_old_person_cmplnc_det.updated_by,
               update_timestamp = p_old_person_cmplnc_det.update_timestamp,
               logical_load_timestamp = p_old_person_cmplnc_det.logical_load_timestamp,
               effective_start_timestamp = p_old_person_cmplnc_det.effective_start_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               email_address = p_old_person_cmplnc_det.email_address,
               is_deleted = p_old_person_cmplnc_det.is_deleted
        WHERE  compliance_email_id = p_old_person_cmplnc_det.compliance_email_id AND
               effective_start_timestamp = p_old_person_cmplnc_det.effective_start_timestamp AND
               employment_detail_id = p_old_person_cmplnc_det.employment_detail_id AND
               (nrg_common.has_value_changed(email_address,p_old_person_cmplnc_det.email_address) =1 OR
                nrg_common.has_value_changed(is_deleted,p_old_person_cmplnc_det.is_deleted) =1 );
  END put_history;

  PROCEDURE put_history(p_old_source_of_funds       person_fnncl_dtl_srcs%rowtype,
                        p_effective_end_timestamp   person_fnncl_dtl_srcs_h.effective_end_timestamp%type,
                        p_action                    person_fnncl_dtl_srcs_h.action%TYPE) IS

  BEGIN
    INSERT INTO person_fnncl_dtl_srcs_h (person_id,
                                         source_id,
                                         type_of_fund,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action,
                                         action_timestamp,
                                         fund_source,
                                         source_details,
                                         is_deleted)
                                  VALUES(p_old_source_of_funds.person_id,
                                         p_old_source_of_funds.source_id,
                                         p_old_source_of_funds.type_of_fund,
                                         p_old_source_of_funds.logical_load_timestamp,
                                         p_old_source_of_funds.created_by,
                                         p_old_source_of_funds.create_timestamp,
                                         p_old_source_of_funds.updated_by,
                                         p_old_source_of_funds.update_timestamp,
                                         p_old_source_of_funds.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_old_source_of_funds.fund_source,
                                         p_old_source_of_funds.source_details,
                                         p_old_source_of_funds.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE person_fnncl_dtl_srcs_h
      SET    logical_load_timestamp = p_old_source_of_funds.logical_load_timestamp,
             updated_by = p_old_source_of_funds.updated_by,
             update_timestamp = p_old_source_of_funds.update_timestamp,
             effective_start_timestamp = p_old_source_of_funds.effective_start_timestamp,
             fund_source = p_old_source_of_funds.fund_source,
             source_details = p_old_source_of_funds.source_details,
             is_deleted = p_old_source_of_funds.is_deleted
      WHERE person_id = p_old_source_of_funds.person_id AND
            source_id = p_old_source_of_funds.source_id AND
            type_of_fund = p_old_source_of_funds.type_of_fund AND
            effective_start_timestamp = p_old_source_of_funds.effective_start_timestamp AND
            (nrg_common.has_value_changed(fund_source, p_old_source_of_funds.fund_source) = 1 OR
             nrg_common.has_value_changed(source_details, p_old_source_of_funds.source_details) = 1 OR
             nrg_common.has_value_changed(is_deleted, p_old_source_of_funds.is_deleted) = 1);
  END;



  PROCEDURE put_history(p_person_old_stmnt_email_addr IN  person_stmnt_email_address%rowtype,
                        p_effective_end_timestamp     IN  person_stmnt_email_address_h.effective_end_timestamp%type,
                        p_action                      IN  person_stmnt_email_address_h.action%TYPE) IS

  BEGIN
    INSERT INTO person_stmnt_email_address_h (stmnt_email_address_id,
                                              person_id,
                                              trading_account_id,
                                              trading_account_type,
                                              logical_load_timestamp,
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              email_address,
                                              is_deleted)
                                  VALUES(p_person_old_stmnt_email_addr.stmnt_email_address_id,
                                         p_person_old_stmnt_email_addr.person_id,
                                         p_person_old_stmnt_email_addr.trading_account_id,
                                         p_person_old_stmnt_email_addr.trading_account_type,
                                         p_person_old_stmnt_email_addr.logical_load_timestamp,
                                         p_person_old_stmnt_email_addr.created_by,
                                         p_person_old_stmnt_email_addr.create_timestamp,
                                         p_person_old_stmnt_email_addr.updated_by,
                                         p_person_old_stmnt_email_addr.update_timestamp,
                                         p_person_old_stmnt_email_addr.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_person_old_stmnt_email_addr.email_address,
                                         p_person_old_stmnt_email_addr.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE person_stmnt_email_address_h
      SET    logical_load_timestamp     = p_person_old_stmnt_email_addr.logical_load_timestamp,
             updated_by                 = p_person_old_stmnt_email_addr.updated_by,
             update_timestamp           = p_person_old_stmnt_email_addr.update_timestamp,
             effective_start_timestamp  = p_person_old_stmnt_email_addr.effective_start_timestamp,
             email_address              = p_person_old_stmnt_email_addr.email_address,
             is_deleted                 = p_person_old_stmnt_email_addr.is_deleted
      WHERE stmnt_email_address_id    = p_person_old_stmnt_email_addr.stmnt_email_address_id AND
            effective_start_timestamp = p_person_old_stmnt_email_addr.effective_start_timestamp AND
            (nrg_common.has_value_changed(is_deleted, p_person_old_stmnt_email_addr.is_deleted) = 1 OR
             nrg_common.has_value_changed(email_address, p_person_old_stmnt_email_addr.email_address) = 1);
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the trading account record
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --      p_old_person_address            This is the old version of the person address
  --      p_effective_end_timestamp       This is the end time for the record
  --      p_action                        Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_old_exctn_email_address prsn_trd_cnfrm_email_address%ROWTYPE,
                        p_effective_end_timestamp prsn_trd_cnfrm_email_address_h.effective_end_timestamp%TYPE,
                        p_action prsn_trd_cnfrm_email_address_h.action%TYPE) IS

  BEGIN
    INSERT INTO prsn_trd_cnfrm_email_address_h(trade_confirm_email_address_id,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           person_id,
                                           trading_account_id,
                                           trading_account_type,
                                           email_address,
                                           is_deleted)
                                    VALUES(p_old_exctn_email_address.trade_confirm_email_address_id,
                                           p_old_exctn_email_address.logical_load_timestamp,
                                           p_old_exctn_email_address.created_by,
                                           p_old_exctn_email_address.create_timestamp,
                                           p_old_exctn_email_address.updated_by,
                                           p_old_exctn_email_address.update_timestamp,
                                           p_old_exctn_email_address.effective_start_timestamp,
                                           p_effective_end_timestamp,
                                           p_action,
                                           SYSTIMESTAMP,
                                           p_old_exctn_email_address.person_id,
                                           p_old_exctn_email_address.trading_account_id,
                                           p_old_exctn_email_address.trading_account_type,
                                           p_old_exctn_email_address.email_address,
                                           p_old_exctn_email_address.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE prsn_trd_cnfrm_email_address_h
      SET logical_load_timestamp = p_old_exctn_email_address.logical_load_timestamp,
          created_by = p_old_exctn_email_address.created_by,
          create_timestamp = p_old_exctn_email_address.create_timestamp,
          updated_by = p_old_exctn_email_address.updated_by,
          update_timestamp = p_old_exctn_email_address.update_timestamp,
          effective_start_timestamp = p_old_exctn_email_address.effective_start_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          person_id = p_old_exctn_email_address.person_id,
          trading_account_id = p_old_exctn_email_address.trading_account_id,
          trading_account_type = p_old_exctn_email_address.trading_account_type,
          email_address = p_old_exctn_email_address.email_address,
          is_deleted = p_old_exctn_email_address.is_deleted
      WHERE trade_confirm_email_address_id = p_old_exctn_email_address.trade_confirm_email_address_id;
  END;


  /* --DR
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the trading account record
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --      p_old_person_address            This is the old version of the person address
  --      p_effective_end_timestamp       This is the end time for the record
  --      p_action                        Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_old_alert_preferences person_alert_preferences%ROWTYPE,
                        p_effective_end_timestamp person_alert_preferences_h.effective_end_timestamp%TYPE,
                        p_action person_alert_preferences_h.action%TYPE) IS

  BEGIN
    INSERT INTO person_alert_preferences_h(alert_channel,
                                           person_id,
                                           trading_account_id,
                                           trading_account_type,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           is_execution_alerts_enabled,
                                           is_price_alerts_enabled,
                                           is_calendar_alerts_enabled,
                                           is_insight_prce_alerts_enabled,
                                           is_insight_news_alerts_enabled,
                                           is_deleted)
                                    VALUES(p_old_alert_preferences.alert_channel,
                                           p_old_alert_preferences.person_id,
                                           p_old_alert_preferences.trading_account_id,
                                           p_old_alert_preferences.trading_account_type,
                                           p_old_alert_preferences.logical_load_timestamp,
                                           p_old_alert_preferences.created_by,
                                           p_old_alert_preferences.create_timestamp,
                                           p_old_alert_preferences.updated_by,
                                           p_old_alert_preferences.update_timestamp,
                                           p_old_alert_preferences.effective_start_timestamp,
                                           p_effective_end_timestamp,
                                           p_action,
                                           SYSTIMESTAMP,
                                           p_old_alert_preferences.is_execution_alerts_enabled,
                                           p_old_alert_preferences.is_price_alerts_enabled,
                                           p_old_alert_preferences.is_calendar_alerts_enabled,
                                           p_old_alert_preferences.is_insight_prce_alerts_enabled,
                                           p_old_alert_preferences.is_insight_news_alerts_enabled,
                                           p_old_alert_preferences.is_deleted);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE person_alert_preferences_h
      SET logical_load_timestamp = p_old_alert_preferences.logical_load_timestamp,
          created_by = p_old_alert_preferences.created_by,
          create_timestamp = p_old_alert_preferences.create_timestamp,
          updated_by = p_old_alert_preferences.updated_by,
          update_timestamp = p_old_alert_preferences.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          is_execution_alerts_enabled = p_old_alert_preferences.is_execution_alerts_enabled,
          is_price_alerts_enabled = p_old_alert_preferences.is_price_alerts_enabled,
          is_calendar_alerts_enabled = p_old_alert_preferences.is_calendar_alerts_enabled,
          is_insight_prce_alerts_enabled = p_old_alert_preferences.is_insight_prce_alerts_enabled,
          is_insight_news_alerts_enabled = p_old_alert_preferences.is_insight_news_alerts_enabled,
          is_deleted = p_old_alert_preferences.is_deleted
      WHERE alert_channel = p_old_alert_preferences.alert_channel AND
          person_id = p_old_alert_preferences.person_id AND
          trading_account_id = p_old_alert_preferences.trading_account_id AND
          trading_account_type = p_old_alert_preferences.trading_account_type AND
          effective_start_timestamp = p_old_alert_preferences.effective_start_timestamp;
  END;

  */
  /* --DR

  -- ===================================================================================
  -- put_alert_preferences
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put alert preferences
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_ALERT_PREFERENCES
  --     PERSON_ALERT_PREFERENCES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     p_alert_preferences              Person Alert Preferences
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_alert_preferences  (p_user                        persons.created_by%TYPE,
                                    p_logical_load_timestamp      person_alert_preferences.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   person_alert_preferences.effective_start_timestamp%TYPE,
                                    p_lv_effective_start_time     person_alert_preferences.effective_start_timestamp%TYPE,
                                    p_person_id                   person_alert_preferences.person_id%TYPE,
                                    p_trading_account_id          person_alert_preferences.trading_account_id%TYPE,
                                    p_trading_account_type        person_alert_preferences.trading_account_type%TYPE,
                                    p_alert_preferences           person_alert_preferences_tab) IS

    TYPE ltab_alert_preferences IS TABLE OF person_alert_preferences%ROWTYPE;

    lv_old_alert_preferences ltab_alert_preferences;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_alert_preferences(alert_channel,
                                             person_id,
                                             trading_account_id,
                                             trading_account_type,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             is_execution_alerts_enabled,
                                             is_price_alerts_enabled,
                                             is_calendar_alerts_enabled,
                                             is_insight_prce_alerts_enabled,
                                             is_insight_news_alerts_enabled,
                                             is_deleted)
                                      SELECT alert_channel,
                                             p_person_id,
                                             p_trading_account_id,
                                             p_trading_account_type,
                                             p_logical_load_timestamp,
                                             p_user,
                                             SYSTIMESTAMP,
                                             p_user,
                                             SYSTIMESTAMP,
                                             p_effective_start_timestamp,
                                             is_execution_alerts_enabled,
                                             is_price_alerts_enabled,
                                             is_calendar_alerts_enabled,
                                             is_insight_prce_alerts_enabled,
                                             is_insight_news_alerts_enabled,
                                             is_deleted
                                      FROM TABLE(CAST(p_alert_preferences AS person_alert_preferences_tab));
      WHEN p_lv_effective_start_time <= p_effective_start_timestamp THEN
        SELECT old_recs.*
        BULK COLLECT INTO lv_old_alert_preferences
        FROM person_alert_preferences old_recs JOIN
             TABLE(CAST(p_alert_preferences AS person_alert_preferences_tab))new_recs ON (old_recs.alert_channel = new_recs.alert_channel AND
                                                                                          old_recs.person_id = p_person_id AND
                                                                                          old_recs.trading_account_id = p_trading_account_id AND
                                                                                          old_recs.trading_account_type = p_trading_account_type)
        WHERE nrg_common.has_value_changed(old_recs.is_execution_alerts_enabled, new_recs.is_execution_alerts_enabled) = 1 OR
              nrg_common.has_value_changed(old_recs.is_price_alerts_enabled, new_recs.is_price_alerts_enabled) = 1 OR
              nrg_common.has_value_changed(old_recs.is_calendar_alerts_enabled, new_recs.is_calendar_alerts_enabled) = 1 OR
              nrg_common.has_value_changed(old_recs.is_insight_prce_alerts_enabled, new_recs.is_insight_prce_alerts_enabled) = 1 OR
              nrg_common.has_value_changed(old_recs.is_insight_news_alerts_enabled, new_recs.is_insight_news_alerts_enabled) = 1 OR
              nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1;

        FOR lv_cnt IN 1..lv_old_alert_preferences.COUNT LOOP
          put_history(p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U',
                      p_old_alert_preferences => lv_old_alert_preferences(lv_cnt));
        END LOOP;

        MERGE INTO person_alert_preferences old_recs
        USING (SELECT a.*
               FROM TABLE(CAST(p_alert_preferences AS person_alert_preferences_tab))a)new_recs
        ON (old_recs.alert_channel = new_recs.alert_channel AND
            old_recs.person_id = p_person_id AND
            old_recs.trading_account_id = p_trading_account_id AND
            old_recs.trading_account_type = p_trading_account_type)
        WHEN MATCHED THEN
          UPDATE
          SET logical_load_timestamp = p_logical_load_timestamp,
              updated_by = p_user,
              update_timestamp = SYSTIMESTAMP,
              effective_start_timestamp = p_effective_start_timestamp,
              is_execution_alerts_enabled = new_recs.is_execution_alerts_enabled,
              is_price_alerts_enabled = new_recs.is_price_alerts_enabled,
              is_calendar_alerts_enabled = new_recs.is_calendar_alerts_enabled,
              is_insight_prce_alerts_enabled = new_recs.is_insight_prce_alerts_enabled,
              is_insight_news_alerts_enabled = new_recs.is_insight_news_alerts_enabled,
              is_deleted = new_recs.is_deleted
          WHERE nrg_common.has_value_changed(old_recs.is_execution_alerts_enabled, new_recs.is_execution_alerts_enabled) = 1 OR
                nrg_common.has_value_changed(old_recs.is_price_alerts_enabled, new_recs.is_price_alerts_enabled) = 1 OR
                nrg_common.has_value_changed(old_recs.is_calendar_alerts_enabled, new_recs.is_calendar_alerts_enabled) = 1 OR
                nrg_common.has_value_changed(old_recs.is_insight_prce_alerts_enabled, new_recs.is_insight_prce_alerts_enabled) = 1 OR
                nrg_common.has_value_changed(old_recs.is_insight_news_alerts_enabled, new_recs.is_insight_news_alerts_enabled) = 1 OR
                nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1
        WHEN NOT MATCHED THEN
          INSERT (alert_channel,
                  person_id,
                  trading_account_id,
                  trading_account_type,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  is_execution_alerts_enabled,
                  is_price_alerts_enabled,
                  is_calendar_alerts_enabled,
                  is_insight_prce_alerts_enabled,
                  is_insight_news_alerts_enabled,
                  is_deleted)
          VALUES(new_recs.alert_channel,
                 p_person_id,
                 p_trading_account_id,
                 p_trading_account_type,
                 p_logical_load_timestamp,
                 p_user,
                 SYSTIMESTAMP,
                 p_user,
                 SYSTIMESTAMP,
                 p_effective_start_timestamp,
                 new_recs.is_execution_alerts_enabled,
                 new_recs.is_price_alerts_enabled,
                 new_recs.is_calendar_alerts_enabled,
                 new_recs.is_insight_prce_alerts_enabled,
                 new_recs.is_insight_news_alerts_enabled,
                 new_recs.is_deleted);
      WHEN p_lv_effective_start_time > p_effective_start_timestamp THEN
        SELECT new_recs.alert_channel,
               p_person_id,
               p_trading_account_id,
               p_trading_account_type,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               new_recs.is_execution_alerts_enabled,
               new_recs.is_price_alerts_enabled,
               new_recs.is_calendar_alerts_enabled,
               new_recs.is_insight_prce_alerts_enabled,
               new_recs.is_insight_news_alerts_enabled,
               new_recs.is_deleted
        BULK COLLECT INTO lv_old_alert_preferences
        FROM TABLE(CAST(p_alert_preferences AS person_alert_preferences_tab))new_recs
        WHERE (is_execution_alerts_enabled,
               is_price_alerts_enabled,
               is_calendar_alerts_enabled,
               is_insight_prce_alerts_enabled,
               is_insight_news_alerts_enabled,
               is_deleted) NOT IN (SELECT is_execution_alerts_enabled,
                                          is_price_alerts_enabled,
                                          is_calendar_alerts_enabled,
                                          is_insight_prce_alerts_enabled,
                                          is_insight_news_alerts_enabled,
                                          is_deleted
                                   FROM person_alert_preferences x
                                   WHERE x.alert_channel = new_recs.alert_channel AND
                                         x.person_id = p_person_id AND
                                         x.trading_account_id = p_trading_account_id AND
                                         x.trading_account_type = p_trading_account_type
                                   UNION ALL
                                   SELECT is_execution_alerts_enabled,
                                          is_price_alerts_enabled,
                                          is_calendar_alerts_enabled,
                                          is_insight_prce_alerts_enabled,
                                          is_insight_news_alerts_enabled,
                                          is_deleted
                                   FROM person_alert_preferences_h y
                                   WHERE y.alert_channel = new_recs.alert_channel AND
                                         y.person_id = p_person_id AND
                                         y.trading_account_id = p_trading_account_id AND
                                         y.trading_account_type = p_trading_account_type);

        FOR lv_cnt IN 1..lv_old_alert_preferences.COUNT LOOP
          put_history(p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I',
                      p_old_alert_preferences => lv_old_alert_preferences(lv_cnt));
        END LOOP;

    END CASE;
  END;

  */

  -- ===================================================================================
  -- put_trd_cnfrm_eml_addrss
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Exctn Email Address
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     prsn_trd_cnfrm_email_address
  --     prsn_trd_cnfrm_email_address_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     p_prsn_trd_cnfrm_email_address     Person Exctn Email Address
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_trd_cnfrm_eml_addrss(p_user                        persons.created_by%TYPE,
                                    p_logical_load_timestamp      prsn_trd_cnfrm_email_address.logical_load_timestamp%TYPE,
                                    p_effective_start_timestamp   prsn_trd_cnfrm_email_address.effective_start_timestamp%TYPE,
                                    p_lv_effective_start_time     prsn_trd_cnfrm_email_address.effective_start_timestamp%TYPE,
                                    p_person_id                   prsn_trd_cnfrm_email_address.person_id%TYPE,
                                    p_trading_account_id          prsn_trd_cnfrm_email_address.trading_account_id%TYPE,
                                    p_trading_account_type        prsn_trd_cnfrm_email_address.trading_account_type%TYPE,
                                    p_prsn_trd_cnfrm_email_address  prsn_trd_cnfrm_eml_addrss_tab) IS

    TYPE ltab_email IS TABLE OF prsn_trd_cnfrm_email_address%ROWTYPE;

    lv_old_records ltab_email;
  BEGIN

    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO prsn_trd_cnfrm_email_address(trade_confirm_email_address_id,
                                               logical_load_timestamp,
                                               created_by,
                                               create_timestamp,
                                               updated_by,
                                               update_timestamp,
                                               effective_start_timestamp,
                                               person_id,
                                               trading_account_id,
                                               trading_account_type,
                                               email_address,
                                               is_deleted)
                                       SELECT a.trade_confirm_email_address_id,
                                              p_logical_load_timestamp,
                                              p_user,
                                              SYSTIMESTAMP,
                                              p_user,
                                              SYSTIMESTAMP,
                                              p_effective_start_timestamp,
                                              p_person_id,
                                              p_trading_account_id,
                                              p_trading_account_type,
                                              a.email_address,
                                              a.is_deleted
                                        FROM TABLE(CAST(p_prsn_trd_cnfrm_email_address AS prsn_trd_cnfrm_eml_addrss_tab)) a;
      WHEN p_lv_effective_start_time <= p_effective_start_timestamp THEN
        SELECT old_recs.*
        BULK COLLECT INTO lv_old_records
        FROM TABLE(CAST(p_prsn_trd_cnfrm_email_address AS prsn_trd_cnfrm_eml_addrss_tab)) new_recs JOIN
             prsn_trd_cnfrm_email_address old_recs ON (old_recs.trade_confirm_email_address_id = new_recs.trade_confirm_email_address_id)
        WHERE nrg_common.has_value_changed(old_recs.person_id, p_person_id) = 1 OR
              nrg_common.has_value_changed(old_recs.trading_account_id, p_trading_account_id) = 1 OR
              nrg_common.has_value_changed(old_recs.trading_account_type, p_trading_account_type) = 1 OR
              nrg_common.has_value_changed(old_recs.email_address, new_recs.email_address) = 1 OR
              nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1;

        --
        --Put History
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_old_exctn_email_address => lv_old_records(lv_cnt),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');
        END LOOP;

        MERGE INTO prsn_trd_cnfrm_email_address old_recs
        USING (SELECT a.trade_confirm_email_address_id,
                      p_logical_load_timestamp logical_load_timestamp,
                      p_user change_user,
                      SYSTIMESTAMP change_date,
                      p_effective_start_timestamp effective_start_timestamp,
                      p_person_id person_id,
                      p_trading_account_id trading_account_id,
                      p_trading_account_type trading_account_type,
                      a.email_address email_address,
                      a.is_deleted is_deleted
               FROM TABLE(CAST(p_prsn_trd_cnfrm_email_address AS prsn_trd_cnfrm_eml_addrss_tab)) a) new_recs
        ON (old_recs.trade_confirm_email_address_id = new_recs.trade_confirm_email_address_id)
        WHEN MATCHED THEN
          UPDATE
          SET logical_load_timestamp = new_recs.logical_load_timestamp,
              updated_by = new_recs.change_user,
              update_timestamp = new_recs.change_date,
              effective_start_timestamp = new_recs.effective_start_timestamp,
              person_id = new_recs.person_id,
              trading_account_id = new_recs.trading_account_id,
              trading_account_type = new_recs.trading_account_type,
              email_address = new_recs.email_address,
              is_deleted = new_recs.is_deleted
          WHERE nrg_common.has_value_changed(old_recs.person_id, p_person_id) = 1 OR
                nrg_common.has_value_changed(old_recs.trading_account_id, new_recs.trading_account_id) = 1 OR
                nrg_common.has_value_changed(old_recs.trading_account_type, new_recs.trading_account_type) = 1 OR
                nrg_common.has_value_changed(old_recs.email_address, new_recs.email_address) = 1 OR
                nrg_common.has_value_changed(old_recs.is_deleted, new_recs.is_deleted) = 1
        WHEN NOT MATCHED THEN
          INSERT (trade_confirm_email_address_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  person_id,
                  trading_account_id,
                  trading_account_type,
                  email_address,
                  is_deleted)
           VALUES(new_recs.trade_confirm_email_address_id,
                  new_recs.logical_load_timestamp,
                  new_recs.change_user,
                  new_recs.change_date,
                  new_recs.change_user,
                  new_recs.change_date,
                  new_recs.effective_start_timestamp,
                  new_recs.person_id,
                  new_recs.trading_account_id,
                  new_recs.trading_account_type,
                  new_recs.email_address,
                  new_recs.is_deleted);

      WHEN p_lv_effective_start_time > p_effective_start_timestamp THEN
        SELECT a.trade_confirm_email_address_id,
               p_logical_load_timestamp logical_load_timestamp,
               p_user created_by,
               SYSTIMESTAMP create_timestamp,
               p_user updated_by,
               SYSTIMESTAMP update_timestamp,
               p_effective_start_timestamp effective_start_timestamp,
               p_person_id person_id,
               p_trading_account_id trading_account_id,
               p_trading_account_type trading_account_type,
               a.email_address email_address,
               a.is_deleted is_deleted
        BULK COLLECT INTO lv_old_records
        FROM TABLE(CAST(p_prsn_trd_cnfrm_email_address AS prsn_trd_cnfrm_eml_addrss_tab)) a
        WHERE (p_person_id, p_trading_account_id, p_trading_account_type, email_address, is_deleted) NOT IN (SELECT person_id,
                                                                                                                trading_account_id,
                                                                                                                trading_account_type,
                                                                                                                email_address,
                                                                                                                is_deleted
                                                                                                          FROM prsn_trd_cnfrm_email_address x
                                                                                                          WHERE x.trade_confirm_email_address_id = a.trade_confirm_email_address_id
                                                                                                          UNION ALL
                                                                                                          SELECT person_id,
                                                                                                                trading_account_id,
                                                                                                                trading_account_type,
                                                                                                                email_address,
                                                                                                                is_deleted
                                                                                                          FROM prsn_trd_cnfrm_email_address_h y
                                                                                                          WHERE y.trade_confirm_email_address_id = a.trade_confirm_email_address_id);
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_old_exctn_email_address => lv_old_records(lv_cnt),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I');
        END LOOP;
    END CASE;
  END;


  -- ===================================================================================
  -- put_source_of_funds
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person financial details
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_FINANCIAL_DETAILS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     p_financial_details              Person Financial Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_source_of_funds(p_user                        persons.created_by%TYPE,
                                p_logical_load_timestamp      person_fnncl_dtl_srcs.logical_load_timestamp%TYPE,
                                p_effective_start_timestamp   person_fnncl_dtl_srcs.effective_start_timestamp%TYPE,
                                p_lv_effective_start_time     person_fnncl_dtl_srcs.effective_start_timestamp%TYPE,
                                p_person_id                   persons.person_id%TYPE,
                                p_type_of_fund                person_fnncl_dtl_srcs.type_of_fund%TYPE,
                                p_source_of_funds             person_fnncl_stl_srcs_tab) IS

    TYPE ltab_source_of_funds IS TABLE OF person_fnncl_dtl_srcs%ROWTYPE;

    lv_source_of_funds ltab_source_of_funds;

  BEGIN

    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        --
        --This means that the new person record is being written
        --

        INSERT INTO person_fnncl_dtl_srcs(person_id,
                                          source_id,
                                          type_of_fund,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          effective_start_timestamp,
                                          fund_source,
                                          source_details,
                                          is_deleted)
                                   SELECT p_person_id person_id,
                                          source_id source_id,
                                          p_type_of_fund type_of_fund,
                                          p_logical_load_timestamp logical_load_timestamp,
                                          p_user created_by,
                                          SYSTIMESTAMP create_timestamp,
                                          p_user updated_by,
                                          SYSTIMESTAMP update_timestamp,
                                          p_effective_start_timestamp effective_start_timestamp,
                                          source_name fund_source,
                                          source_details source_details,
                                          nvl(is_deleted, 'NO') is_deleted
                                   FROM   TABLE(CAST(p_source_of_funds AS person_fnncl_stl_srcs_tab));
      WHEN p_lv_effective_start_time <= p_effective_start_timestamp THEN

        --
        --Get All the records which will be updated
        --

        SELECT old_recs.*
        BULK COLLECT INTO lv_source_of_funds
        FROM person_fnncl_dtl_srcs old_recs JOIN
             (SELECT *
             FROM TABLE(CAST(p_source_of_funds AS person_fnncl_stl_srcs_tab))) new_recs ON (person_id = p_person_id AND
                                                                                            old_recs.source_id = new_recs.source_id AND
                                                                                            type_of_fund = p_type_of_fund)
        WHERE nrg_common.has_value_changed(old_recs.fund_source, new_recs.source_name) = 1 OR
              nrg_common.has_value_changed(old_recs.source_details, new_recs.source_details) = 1 OR
              nrg_common.has_value_changed(old_recs.is_deleted, nvl(new_recs.is_deleted,'NO')) = 1;

        --
        --Put these records to hsitory
        --

        FOR lv_count IN 1..lv_source_of_funds.COUNT LOOP
          put_history(p_old_source_of_funds       => lv_source_of_funds(lv_count),
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'U');
        END LOOP;

        --
        --This is when an update to person has been received
        --

        MERGE INTO person_fnncl_dtl_srcs existing_record
        USING (SELECT p_person_id person_id,
                      source_id source_id,
                      p_type_of_fund type_of_fund,
                      p_logical_load_timestamp logical_load_timestamp,
                      p_user created_by,
                      SYSTIMESTAMP create_timestamp,
                      p_user updated_by,
                      SYSTIMESTAMP update_timestamp,
                      p_effective_start_timestamp effective_start_timestamp,
                      a.source_name fund_source,
                      a.source_details source_details,
                      nvl(a.is_deleted, 'NO') is_deleted
               FROM   TABLE(CAST(p_source_of_funds AS person_fnncl_stl_srcs_tab)) a) new_record
        ON (existing_record.person_id = new_record.person_id AND
            existing_record.source_id = new_record.source_id AND
            existing_record.type_of_fund = new_record.type_of_fund)
        WHEN MATCHED THEN
          UPDATE
          SET    logical_load_timestamp = new_record.logical_load_timestamp,
                 updated_by = new_record.updated_by,
                 update_timestamp = new_record.update_timestamp,
                 effective_start_timestamp = new_record.effective_start_timestamp,
                 fund_source = new_record.fund_source,
                 source_details = new_record.source_details,
                 is_deleted = new_record.is_deleted
          --WHERE (nrg_common.has_value_changed(fund_source, new_record.fund_source) = 1 OR
          --       nrg_common.has_value_changed(source_details, new_record.source_details) = 1 OR
          --       nrg_common.has_value_changed(is_deleted, new_record.is_deleted) = 1)
        WHEN NOT MATCHED THEN
          INSERT (person_id,
                  source_id,
                  type_of_fund,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  fund_source,
                  source_details,
                  is_deleted)
          VALUES  (new_record.person_id,
                  new_record.source_id,
                  new_record.type_of_fund,
                  new_record.logical_load_timestamp,
                  new_record.created_by,
                  new_record.create_timestamp,
                  new_record.updated_by,
                  new_record.update_timestamp,
                  new_record.effective_start_timestamp,
                  new_record.fund_source,
                  new_record.source_details,
                  new_record.is_deleted);

        --
        --To be on a safe side compare if there is any extra record that was there earlier in the list and is missing now
        --If such a record exists mark the is deleted flag as yes. This can be because of a missed message earlier
        --

        --
        --Put these records to hsitory
        --
        SELECT *
        BULK COLLECT INTO lv_source_of_funds
        FROM person_fnncl_dtl_srcs
        WHERE person_id = p_person_id AND
              type_of_fund = p_type_of_fund AND
              is_deleted = 'NO' AND
              source_id NOT IN (SELECT source_id
                                FROM TABLE(CAST(p_source_of_funds AS person_fnncl_stl_srcs_tab)));

        FOR lv_count IN 1..lv_source_of_funds.COUNT LOOP
          put_history(p_old_source_of_funds       => lv_source_of_funds(lv_count),
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'D');
        END LOOP;

        UPDATE person_fnncl_dtl_srcs
        SET    logical_load_timestamp = p_logical_load_timestamp,
               updated_by = p_user,
               update_timestamp = SYSTIMESTAMP,
               effective_start_timestamp = p_effective_start_timestamp,
               is_deleted = 'YES'
        WHERE  is_deleted = 'NO' AND
               person_id = p_person_id AND
               type_of_fund = p_type_of_fund AND
               source_id NOT IN (SELECT source_id
                                 FROM TABLE(CAST(p_source_of_funds AS person_fnncl_stl_srcs_tab)));
      WHEN p_lv_effective_start_time > p_effective_start_timestamp THEN

        --
        --An old update is received
        --

        SELECT  p_person_id person_id,
                source_id source_id,
                p_type_of_fund type_of_fund,
                p_logical_load_timestamp logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp effective_start_timestamp,
                source_name fund_source,
                source_details source_details,
                nvl(is_deleted, 'NO') is_deleted
        BULK COLLECT INTO lv_source_of_funds
        FROM   TABLE(CAST(p_source_of_funds AS person_fnncl_stl_srcs_tab))
        WHERE
               --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (p_person_id, source_id, p_type_of_fund, source_name, source_details, nvl(is_deleted, 'NO')) NOT IN (SELECT person_id, source_id, type_of_fund, fund_source, source_details, is_deleted
                                                                                                     FROM   person_fnncl_dtl_srcs
                                                                                                     WHERE  person_id = p_person_id AND
                                                                                                            effective_start_timestamp <= p_effective_start_timestamp
                                                                                                     UNION ALL
                                                                                                     SELECT person_id, source_id, type_of_fund, fund_source, source_details, is_deleted
                                                                                                     FROM   person_fnncl_dtl_srcs_h
                                                                                                     WHERE  person_id = p_person_id AND
                                                                                                            effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                                            effective_end_timestamp > p_effective_start_timestamp);

        --
        --Put History
        --

        FOR lv_count IN 1..lv_source_of_funds.COUNT LOOP
          put_history(p_old_source_of_funds       => lv_source_of_funds(lv_count),
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'I');
        END LOOP;

    END CASE;
  END;

    -- ===================================================================================
    -- put_person_financial_details
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person financial details
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PERSON_FINANCIAL_DETAILS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     p_financial_details              Person Financial Details
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

  --
  --Commented the put financial details in sprint 22
  --

    -- ===================================================================================
    -- put_prsn_cmplnc_eml_addrsss
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person complaince email adresses
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRSN CMPLNC EML ADDRSSS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     put_prsn_cmplnc_eml_addrssss     Person Complaince Email Address
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_prsn_cmplnc_eml_addrsss(p_user prsn_cmplnc_eml_addrsss.created_by%TYPE,
                                         p_logical_load_timestamp prsn_cmplnc_eml_addrsss.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp prsn_cmplnc_eml_addrsss.effective_start_timestamp%TYPE,
                                         p_lv_effective_start_time prsn_cmplnc_eml_addrsss.effective_start_timestamp%TYPE,
                                         p_employment_detail_id prsn_cmplnc_eml_addrsss.employment_detail_id%type,
                                         p_prsn_cmplnc_eml_addrsss prsn_cmplnc_eml_addrsss_tab)
  is
  TYPE ltyp_updated_per_cmp IS TABLE OF prsn_cmplnc_eml_addrsss%rowtype;

  ltab_updated_per_cmp ltyp_updated_per_cmp;
  ltab_updated_per_cmp_old ltyp_updated_per_cmp;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO prsn_cmplnc_eml_addrsss
                                    (
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        logical_load_timestamp,
                                        effective_start_timestamp,
                                        compliance_email_id,
                                        employment_detail_id,
                                        email_address,
                                        is_deleted
                                    )
                                    SELECT p_user,
                                           SYSTIMESTAMP,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_logical_load_timestamp,
                                           p_effective_start_timestamp,
                                           compliance_email_id,
                                           p_employment_detail_id,
                                           email_address,
                                           is_deleted
                                    FROM   TABLE(CAST(p_prsn_cmplnc_eml_addrsss AS prsn_cmplnc_eml_addrsss_tab)) per_cmp_tab
                                    WHERE  NOT EXISTS (SELECT 1
                                                       FROM prsn_cmplnc_eml_addrsss
                                                       WHERE compliance_email_id = per_cmp_tab.compliance_email_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT per_cmp.compliance_email_id,
               per_cmp.logical_load_timestamp,
               per_cmp.created_by,
               per_cmp.create_timestamp,
               per_cmp.updated_by,
               per_cmp.update_timestamp,
               per_cmp.effective_start_timestamp,
               per_cmp.employment_detail_id,
               per_cmp.email_address,
               per_cmp.is_deleted
        BULK COLLECT INTO ltab_updated_per_cmp_old
        FROM   prsn_cmplnc_eml_addrsss per_cmp, TABLE(CAST(p_prsn_cmplnc_eml_addrsss AS prsn_cmplnc_eml_addrsss_tab)) per_cmp_tab
        WHERE  per_cmp.employment_detail_id = p_employment_detail_id AND
               per_cmp.compliance_email_id = per_cmp_tab.compliance_email_id AND
               (nrg_common.has_value_changed(per_cmp.email_address,per_cmp_tab.email_address) = 1 OR
                nrg_common.has_value_changed(per_cmp.is_deleted,per_cmp_tab.is_deleted) = 1 )
        FOR UPDATE OF per_cmp.compliance_email_id;

        FOR lv_count IN 1..ltab_updated_per_cmp_old.COUNT LOOP
          put_history(p_old_person_cmplnc_det        => ltab_updated_per_cmp_old(lv_count),
                        p_effective_end_timestamp    => p_effective_start_timestamp,
                        p_action                     => 'U');
        END LOOP;

        MERGE INTO prsn_cmplnc_eml_addrsss old_version
        USING (SELECT *
               FROM TABLE(CAST(p_prsn_cmplnc_eml_addrsss AS prsn_cmplnc_eml_addrsss_tab)))new_version
        ON (old_version.compliance_email_id = new_version.compliance_email_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 email_address = new_version.email_address,
                 is_deleted = new_version.is_deleted,
                 employment_detail_id = p_employment_detail_id
          WHERE (nrg_common.has_value_changed(old_version.email_address,new_version.email_address) = 1 OR
                nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted) = 1 OR
                nrg_common.has_value_changed(old_version.employment_detail_id, p_employment_detail_id) = 1)
        WHEN NOT MATCHED THEN
          INSERT (compliance_email_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  employment_detail_id,
                  email_address,
                  is_deleted)
          VALUES (new_version.compliance_email_id,
                  p_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  p_employment_detail_id,
                  new_version.email_address,
                  new_version.is_deleted);
        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --

        SELECT *
        BULK COLLECT INTO ltab_updated_per_cmp
        FROM   prsn_cmplnc_eml_addrsss
        WHERE  employment_detail_id = p_employment_detail_id AND
               compliance_email_id NOT IN (SELECT compliance_email_id
                                  FROM   TABLE(CAST(p_prsn_cmplnc_eml_addrsss AS prsn_cmplnc_eml_addrsss_tab)))
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_per_cmp.COUNT LOOP
          put_history(p_old_person_cmplnc_det => ltab_updated_per_cmp(lv_count),
                        p_effective_end_timestamp    => p_effective_start_timestamp,
                        p_action                     => 'D');
        END LOOP;

        FORALL lv_count IN 1..ltab_updated_per_cmp.COUNT
          DELETE prsn_cmplnc_eml_addrsss
          WHERE  employment_detail_id = ltab_updated_per_cmp(lv_count).employment_detail_id AND
                 compliance_email_id = ltab_updated_per_cmp(lv_count).compliance_email_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT compliance_email_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_employment_detail_id,
               email_address,
               is_deleted
        BULK COLLECT INTO ltab_updated_per_cmp
        FROM   TABLE(CAST(p_prsn_cmplnc_eml_addrsss AS prsn_cmplnc_eml_addrsss_tab)) per_cmp_tab
        WHERE
                --
                -- We need to exclude any data that entering the history where the non-key columns
                -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
                -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
                -- and replaying exising messages
                --
               (email_address,is_deleted) NOT IN (SELECT email_address,is_deleted
                                                  FROM    prsn_cmplnc_eml_addrsss
                                                  WHERE   employment_detail_id = p_employment_detail_id AND
                                                         effective_start_timestamp <= p_effective_start_timestamp
                                                  UNION ALL
                                                  SELECT email_address,is_deleted
                                                  FROM    prsn_cmplnc_eml_addrsss_h
                                                  WHERE  employment_detail_id = p_employment_detail_id AND
                                                         effective_start_timestamp <= p_effective_start_timestamp AND
                                                         effective_end_timestamp > p_effective_start_timestamp);

        FOR lv_count IN 1..ltab_updated_per_cmp.COUNT LOOP
          put_history(p_old_person_cmplnc_det        => ltab_updated_per_cmp(lv_count),
                        p_effective_end_timestamp    => p_effective_start_timestamp,
                        p_action                     => 'I');
        END LOOP;
      END CASE;

    END put_prsn_cmplnc_eml_addrsss;

    -- ===================================================================================
    -- put_person_employment_details
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person Employment Details
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PERSON EMPLOYMENT DETAILS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     p_employment_details             Person Employment Details
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_employment_details(p_user persons.created_by%TYPE,
                                         p_logical_load_timestamp person_employment_details.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp person_employment_details.effective_start_timestamp%TYPE,
                                         p_lv_effective_start_time person_employment_details.effective_start_timestamp%TYPE,
                                         p_person_id persons.person_id%TYPE,
                                         p_person_employment_details employment_detail_tab)
  IS
    TYPE ltyp_updated_per_emp IS TABLE OF person_employment_details%rowtype;
    TYPE ltyp_deleted_cmp_eml_add IS TABLE OF prsn_cmplnc_eml_addrsss%ROWTYPE;

    ltab_updated_per_emp ltyp_updated_per_emp;
    ltab_updated_per_emp_old ltyp_updated_per_emp;

    ltab_cmplnc_email_del ltyp_deleted_cmp_eml_add;

    lv_effective_start_timestamp person_employment_details.effective_start_timestamp%TYPE := p_lv_effective_start_time;
  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_employment_details
                                    (
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        logical_load_timestamp,
                                        effective_start_timestamp,
                                        person_id,
                                        employment_detail_id,
                                        employment_detail_version,
                                        employment_status,
                                        job_title,
                                        company_name,
                                        nature_of_business,
                                        is_fsa_regulated,
                                        company_registration_number,
                                        industry_further_details,
                                        industry_other_details,
                                        is_former_employment,
                                        educational_establishment,
                                        job_title_details,
                                        is_deleted
                                    )
                                    SELECT /*+ CARDINALITY (per_emp_tab 3) */p_user,
                                           SYSTIMESTAMP,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_logical_load_timestamp,
                                           p_effective_start_timestamp,
                                           p_person_id,
                                           employment_detail_id,
                                           employment_detail_version,
                                           employment_status,
                                           job_title,
                                           company_name,
                                           nature_of_business,
                                           is_fsa_regulated,
                                           company_registration_number,
                                           industry_further_details,
                                           industry_other_details,
                                           is_former_employment,
                                           educational_establishment,
                                           job_title_details,
                                           is_deleted
                                    FROM   TABLE(CAST(p_person_employment_details AS employment_detail_tab)) per_emp_tab
                                    WHERE  NOT EXISTS (SELECT 1
                                                       FROM person_employment_details
                                                       WHERE employment_detail_id = per_emp_tab.employment_detail_id);



      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (per_emp_tab 3) */per_emp.employment_detail_id,
               per_emp.logical_load_timestamp,
               per_emp.created_by,
               per_emp.create_timestamp,
               per_emp.updated_by,
               per_emp.update_timestamp,
               per_emp.effective_start_timestamp,
               per_emp.person_id,
               per_emp.employment_detail_version,
               per_emp.employment_status,
               per_emp.job_title,
               per_emp.company_name,
               per_emp.nature_of_business,
               per_emp.is_fsa_regulated,
               per_emp.company_registration_number,
               per_emp.Industry_Further_Details,
               per_emp.industry_other_details,
               per_emp.is_former_employment,
               per_emp.educational_establishment,
               per_emp.job_title_details,
               per_emp.is_deleted
              BULK COLLECT INTO ltab_updated_per_emp_old
              FROM   person_employment_details per_emp, TABLE(CAST(p_person_employment_details AS employment_detail_tab)) per_emp_tab
              WHERE  per_emp.employment_detail_id = per_emp_tab.employment_detail_id AND
                     (nrg_common.has_value_changed(per_emp.employment_detail_version,per_emp_tab.employment_detail_version) =1 OR
                      nrg_common.has_value_changed(per_emp.employment_status,per_emp_tab.employment_status) =1 OR
                      nrg_common.has_value_changed(per_emp.job_title,per_emp_tab.job_title) =1 OR
                      nrg_common.has_value_changed(per_emp.company_name,per_emp_tab.company_name) =1 OR
                      nrg_common.has_value_changed(per_emp.nature_of_business,per_emp_tab.nature_of_business) =1 OR
                      nrg_common.has_value_changed(per_emp.is_fsa_regulated,per_emp_tab.is_fsa_regulated) =1 OR
                      nrg_common.has_value_changed(per_emp.company_registration_number,per_emp_tab.company_registration_number) =1 OR
                      nrg_common.has_value_changed(per_emp.Industry_Further_Details, per_emp_tab.Industry_Further_Details) = 1 OR
                      nrg_common.has_value_changed(per_emp.industry_other_details, per_emp_tab.industry_other_details) = 1 OR
                      nrg_common.has_value_changed(per_emp.is_former_employment, per_emp_tab.is_former_employment) = 1 OR
                      nrg_common.has_value_changed(per_emp.educational_establishment, per_emp_tab.educational_establishment) = 1 OR
                      nrg_common.has_value_changed(per_emp.job_title_details, per_emp_tab.job_title_details) = 1 OR
                      nrg_common.has_value_changed(per_emp.is_deleted, per_emp_tab.is_deleted) = 1 )
        FOR UPDATE OF per_emp.employment_detail_id;

        FOR lv_count in 1..ltab_updated_per_emp_old.COUNT LOOP
          put_history(p_old_person_employment_det   => ltab_updated_per_emp_old(lv_count),
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_action                    => 'U');
        END LOOP;

        MERGE /*+ CARDINALITY (new_version 3) */INTO person_employment_details old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_employment_details AS employment_detail_tab))) new_version
        ON (old_version.employment_detail_id = new_version.employment_detail_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 employment_detail_version = new_version.employment_detail_version,
                 employment_status = new_version.employment_status,
                 job_title = new_version.job_title,
                 company_name = new_version.company_name,
                 nature_of_business = new_version.nature_of_business,
                 is_fsa_regulated = new_version.is_fsa_regulated,
                 company_registration_number = new_version.company_registration_number,
                 Industry_Further_Details = new_version.Industry_Further_Details,
                 industry_other_details  = new_version.industry_other_details,
                 is_former_employment = new_version.is_former_employment,
                 educational_establishment = new_version.educational_establishment,
                 job_title_details = new_version.job_title_details,
                 is_deleted = new_version.is_deleted
          WHERE  (nrg_common.has_value_changed(old_version.employment_detail_version,new_version.employment_detail_version) = 1 OR
                  nrg_common.has_value_changed(old_version.employment_status,new_version.employment_status) =1 OR
                  nrg_common.has_value_changed(old_version.job_title,new_version.job_title) =1 OR
                  nrg_common.has_value_changed(old_version.company_name,new_version.company_name) =1 OR
                  nrg_common.has_value_changed(old_version.nature_of_business,new_version.nature_of_business) =1 OR
                  nrg_common.has_value_changed(old_version.is_fsa_regulated,new_version.is_fsa_regulated) =1 OR
                  nrg_common.has_value_changed(old_version.company_registration_number,new_version.company_registration_number) =1 OR
                  nrg_common.has_value_changed(old_version.Industry_Further_Details , new_version.Industry_Further_Details) = 1 OR
                  nrg_common.has_value_changed(old_version.industry_other_details  , new_version.industry_other_details) = 1 OR
                  nrg_common.has_value_changed(old_version.is_former_employment , new_version.is_former_employment) = 1 OR
                  nrg_common.has_value_changed(old_version.educational_establishment , new_version.educational_establishment) = 1 OR
                  nrg_common.has_value_changed(old_version.job_title_details , new_version.job_title_details) = 1 OR
                  nrg_common.has_value_changed(old_version.is_deleted , new_version.is_deleted) = 1
                  )
        WHEN NOT MATCHED THEN
          INSERT (employment_detail_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  person_id,
                  employment_detail_version,
                  employment_status,
                  job_title,
                  company_name,
                  nature_of_business,
                  is_fsa_regulated,
                  company_registration_number,
                  industry_further_details,
                  industry_other_details,
                  is_former_employment,
                  educational_establishment,
                  job_title_details,
                  is_deleted)
          VALUES (new_version.employment_detail_id,
                  p_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  p_person_id,
                  new_version.employment_detail_version,
                  new_version.employment_status,
                  new_version.job_title,
                  new_version.company_name,
                  new_version.nature_of_business,
                  new_version.is_fsa_regulated,
                  new_version.company_registration_number,
                  new_version.industry_further_details,
                  new_version.industry_other_details,
                  new_version.is_former_employment,
                  new_version.educational_establishment,
                  new_version.job_title_details,
                  new_version.is_deleted);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --

        SELECT *
        BULK COLLECT INTO ltab_updated_per_emp
        FROM   person_employment_details
        WHERE  person_id = p_person_id AND
               employment_detail_id NOT IN (SELECT employment_detail_id
                                            FROM   TABLE(CAST(p_person_employment_details AS employment_detail_tab)))
        FOR UPDATE;

         SELECT old_table.compliance_email_id,
                old_table.logical_load_timestamp,
                old_table.created_by,
                old_table.create_timestamp,
                old_table.updated_by,
                old_table.update_timestamp,
                old_table.effective_start_timestamp,
                old_table.employment_detail_id,
                old_table.email_address,
                old_table.is_deleted
        BULK COLLECT INTO ltab_cmplnc_email_del
        FROM prsn_cmplnc_eml_addrsss old_table,
           (SELECT employment_detail_id
            FROM TABLE(CAST(p_person_employment_details AS employment_detail_tab))) new_table
        WHERE old_table.employment_detail_id = new_table.employment_detail_id
         AND old_table.compliance_email_id NOT IN
                (SELECT compliance_email_id
                     FROM TABLE(CAST(p_person_employment_details AS employment_detail_tab)) main_table,
                      TABLE(main_table.compliance_email_addresses) sub_table);

        FOR lv_count IN 1..ltab_cmplnc_email_del.COUNT LOOP
          put_history(p_old_person_cmplnc_det     => ltab_cmplnc_email_del(lv_count),
                      p_effective_end_timestamp   => ltab_cmplnc_email_del(lv_count).effective_start_timestamp,
                      p_action                    => 'D');
        END LOOP;

        FOR lv_count IN 1..ltab_updated_per_emp.COUNT LOOP
          put_history(p_old_person_employment_det   => ltab_updated_per_emp(lv_count),
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'D');
        END LOOP;

        FORALL lv_count IN 1..ltab_cmplnc_email_del.COUNT
          DELETE prsn_cmplnc_eml_addrsss
          WHERE  compliance_email_id = ltab_cmplnc_email_del(lv_count).compliance_email_id;

        FORALL lv_count IN 1..ltab_updated_per_emp.COUNT
          DELETE person_employment_details
          WHERE  person_id = ltab_updated_per_emp(lv_count).person_id AND
                 employment_detail_id = ltab_updated_per_emp(lv_count).employment_detail_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (per_emp_tab 3) */employment_detail_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_person_id,
               employment_detail_version,
               employment_status,
               job_title,
               company_name,
               nature_of_business,
               is_fsa_regulated,
               company_registration_number,
               industry_further_details,
               industry_other_details,
               is_former_employment,
               educational_establishment,
               job_title_details,
               is_deleted
       BULK COLLECT INTO ltab_updated_per_emp
        FROM   TABLE(CAST(p_person_employment_details AS employment_detail_tab)) per_emp_tab
        WHERE
               --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (employment_detail_version,employment_status,job_title,company_name,
                nature_of_business,is_fsa_regulated,company_registration_number,industry_further_details,
               industry_other_details,
               is_former_employment,
               educational_establishment,
               job_title_details,
               is_deleted ) NOT IN (SELECT employment_detail_version,employment_status,job_title,company_name,
                                                                                                nature_of_business,is_fsa_regulated,company_registration_number,
                                                                                                industry_further_details, industry_other_details,
                                                                                                is_former_employment, educational_establishment,
                                                                                                job_title_details, is_deleted
                                                                                         FROM    person_employment_details
                                                                                         WHERE   person_id = p_person_id AND
                                                                                                 effective_start_timestamp <= p_effective_start_timestamp
                                                                                         UNION ALL
                                                                                         SELECT employment_detail_version,employment_status,job_title,company_name,
                                                                                                nature_of_business,is_fsa_regulated,company_registration_number,
                                                                                                industry_further_details, industry_other_details,
                                                                                                is_former_employment, educational_establishment,
                                                                                                job_title_details, is_deleted
                                                                                         FROM   person_employment_details_h
                                                                                         WHERE  person_id = p_person_id AND
                                                                                                effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                                effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_count IN 1..ltab_updated_per_emp.COUNT LOOP
          put_history(p_old_person_employment_det   => ltab_updated_per_emp(lv_count),
                        p_effective_end_timestamp   => p_effective_start_timestamp,
                        p_action                    => 'I');
        END LOOP;

      END CASE;

      -- prsn_cmplnc_eml_address
      IF p_person_employment_details IS NOT NULL THEN
        FOR lv_count IN 1..p_person_employment_details.COUNT LOOP
          put_prsn_cmplnc_eml_addrsss(p_user                        => p_user,
                                      p_logical_load_timestamp      => p_logical_load_timestamp,
                                      p_effective_start_timestamp   => p_effective_start_timestamp,
                                      p_lv_effective_start_time     => lv_effective_start_timestamp,
                                      p_employment_detail_id        => p_person_employment_details(lv_count).employment_detail_id,
                                      p_prsn_cmplnc_eml_addrsss     => p_person_employment_details(lv_count).compliance_email_addresses);
        END LOOP;
      END IF;

    END put_person_employment_details;

    -- ===================================================================================
    -- put_person_addresses
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person addresses
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PERSON_ADDRESSES
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     p_person_addresses               Person Addresses
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_addresses(p_user persons.created_by%TYPE,
                                 p_logical_load_timestamp person_addresses.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp person_addresses.effective_start_timestamp%TYPE,
                                 p_lv_effective_start_time person_addresses.effective_start_timestamp%TYPE,
                                 p_person_id persons.person_id%TYPE,
                                 p_person_addresses person_addresses_tab,
                                 p_record_source person_addresses.record_source%TYPE)
  IS
  TYPE ltyp_updated_per_add IS TABLE OF person_addresses%rowtype;

  ltab_updated_per_add ltyp_updated_per_add;
  ltab_updated_per_add_old ltyp_updated_per_add;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_addresses
                                    (
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        logical_load_timestamp,
                                        effective_start_timestamp,
                                        person_id,
                                        address_id,
                                        address_version,
                                        address_type,
                                        address_line_1,
                                        address_line_2,
                                        address_line_3,
                                        address_line_4,
                                        address_line_5,
                                        post_code,
                                        country_code,
                                        is_primary,
                                        is_previous,
                                        years_at_address,
                                        is_deleted,
                                        employment_detail_id,
                                        record_source,
                                        source_address_id
                                    )
                                    SELECT /*+ CARDINALITY (per_add_tab 3) */p_user,
                                           SYSTIMESTAMP,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_logical_load_timestamp,
                                           p_effective_start_timestamp,
                                           p_person_id,
                                           address_id,
                                           address_version,
                                           address_type,
                                           address_line_1,
                                           address_line_2,
                                           address_line_3,
                                           address_line_4,
                                           address_line_5,
                                           post_code,
                                           country_code,
                                           is_primary,
                                           is_previous,
                                           years_at_address,
                                           is_deleted,
                                           employment_detail_id,
                                           p_record_source,
                                           source_address_id
                                    FROM   TABLE(CAST(p_person_addresses AS person_addresses_tab)) per_add_tab
                                    WHERE  NOT EXISTS (SELECT 1
                                                       FROM person_addresses
                                                       WHERE address_id = per_add_tab.address_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        -- Insert the data which is new
        --
        SELECT /*+ CARDINALITY (mq 3) */per_add.address_id,
               per_add.logical_load_timestamp,
               per_add.created_by,
               per_add.create_timestamp,
               per_add.updated_by,
               per_add.update_timestamp,
               per_add.effective_start_timestamp,
               per_add.person_id,
               per_add.address_version,
               per_add.address_type,
               per_add.address_line_1,
               per_add.address_line_2,
               per_add.address_line_3,
               per_add.address_line_4,
               per_add.address_line_5,
               per_add.post_code,
               per_add.country_code,
               per_add.is_primary,
               per_add.is_previous,
               per_add.years_at_address,
               per_add.is_deleted,
               per_add.Employment_Detail_Id,
               per_add.record_source,
               per_add.source_address_id
        BULK COLLECT INTO ltab_updated_per_add_old
        FROM   person_addresses per_add, TABLE(CAST(p_person_addresses AS person_addresses_tab)) mq
        WHERE  per_add.address_id = mq.address_id AND
               (nrg_common.has_value_changed(per_add.address_version,mq.address_version ) =1 OR
                nrg_common.has_value_changed(per_add.address_type,mq.address_type ) =1 OR
                nrg_common.has_value_changed(per_add.address_line_1,mq.address_line_1 ) =1 OR
                nrg_common.has_value_changed(per_add.address_line_2,mq.address_line_2 ) =1 OR
                nrg_common.has_value_changed(per_add.address_line_3,mq.address_line_3 ) =1 OR
                nrg_common.has_value_changed(per_add.address_line_4,mq.address_line_4 ) =1 OR
                nrg_common.has_value_changed(per_add.address_line_5,mq.address_line_5 ) =1 OR
                nrg_common.has_value_changed(per_add.post_code,mq.post_code ) =1 OR
                nrg_common.has_value_changed(per_add.country_code,mq.country_code ) =1 OR
                nrg_common.has_value_changed(per_add.is_primary,mq.is_primary ) =1 OR
                nrg_common.has_value_changed(per_add.is_previous,mq.is_previous ) =1 OR
                nrg_common.has_value_changed(per_add.years_at_address,mq.years_at_address )= 1 OR
                nrg_common.has_value_changed(per_add.is_deleted,mq.is_deleted) = 1 OR
                nrg_common.has_value_changed(per_add.person_id,p_person_id) = 1 OR
                nrg_common.has_value_changed(per_add.Employment_Detail_Id, mq.Employment_Detail_Id) = 1 OR
                nrg_common.has_value_changed(per_add.record_source, p_record_source) = 1 OR
                nrg_common.has_value_changed(per_add.source_address_id, mq.source_address_id) = 1)
        FOR UPDATE OF per_add.address_id;

        --
        -- The data that has got updated, move it to the history
        --

        FOR lv_count IN 1..ltab_updated_per_add_old.COUNT LOOP
          put_history(p_old_person_address      => ltab_updated_per_add_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action                  => 'U');
        END LOOP;

        --
        --Insert/Update the new data
        --
        MERGE /*+ CARDINALITY (new_version 3) */INTO person_addresses old_version
        USING (SELECT *
               FROM   TABLE(CAST(p_person_addresses AS person_addresses_tab))) new_version
        ON (old_version.address_id = new_version.address_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 address_version = new_version.address_version,
                 address_type = new_version.address_type,
                 address_line_1 = new_version.address_line_1,
                 address_line_2 = new_version.address_line_2,
                 address_line_3 = new_version.address_line_3,
                 address_line_4 = new_version.address_line_4,
                 address_line_5 = new_version.address_line_5,
                 post_code = new_version.post_code,
                 country_code = new_version.country_code,
                 is_primary = new_version.is_primary,
                 is_previous = new_version.is_previous,
                 years_at_address = new_version.years_at_address,
                 is_deleted = new_version.is_deleted,
                 person_id = p_person_id,
                 employment_detail_id = new_version.employment_detail_id,
                 record_source = p_record_source,
                 source_address_id = new_version.source_address_id
          WHERE (nrg_common.has_value_changed(old_version.address_version,new_version.address_version ) =1 OR
                nrg_common.has_value_changed(old_version.address_type,new_version.address_type ) =1 OR
                nrg_common.has_value_changed(old_version.address_line_1,new_version.address_line_1 ) =1 OR
                nrg_common.has_value_changed(old_version.address_line_2,new_version.address_line_2 ) =1 OR
                nrg_common.has_value_changed(old_version.address_line_3,new_version.address_line_3 ) =1 OR
                nrg_common.has_value_changed(old_version.address_line_4,new_version.address_line_4 ) =1 OR
                nrg_common.has_value_changed(old_version.address_line_5,new_version.address_line_5 ) =1 OR
                nrg_common.has_value_changed(old_version.post_code,new_version.post_code ) =1 OR
                nrg_common.has_value_changed(old_version.country_code,new_version.country_code ) =1 OR
                nrg_common.has_value_changed(old_version.is_primary,new_version.is_primary ) =1 OR
                nrg_common.has_value_changed(old_version.is_previous,new_version.is_previous ) =1 OR
                nrg_common.has_value_changed(old_version.years_at_address,new_version.years_at_address )= 1 OR
                nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted) = 1 OR
                nrg_common.has_value_changed(old_version.person_id,p_person_id) = 1 OR
                nrg_common.has_value_changed(old_version.employment_detail_id, new_version.employment_detail_id) = 1 OR
                nrg_common.has_value_changed(old_version.record_source, p_record_source) = 1 OR
                nrg_common.has_value_changed(old_version.source_address_id, new_version.source_address_id) = 1
                )
        WHEN NOT MATCHED THEN
                           INSERT
                                        (
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            logical_load_timestamp,
                                            effective_start_timestamp,
                                            person_id,
                                            address_id,
                                            address_version,
                                            address_type,
                                            address_line_1,
                                            address_line_2,
                                            address_line_3,
                                            address_line_4,
                                            address_line_5,
                                            post_code,
                                            country_code,
                                            is_primary,
                                            is_previous,
                                            years_at_address,
                                            is_deleted,
                                            employment_detail_id,
                                            record_source,
                                            source_address_id
                                        )
                                    VALUES( p_user,
                                            SYSTIMESTAMP,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_logical_load_timestamp,
                                            p_effective_start_timestamp,
                                            p_person_id,
                                            new_version.address_id,
                                            new_version.address_version,
                                            new_version.address_type,
                                            new_version.address_line_1,
                                            new_version.address_line_2,
                                            new_version.address_line_3,
                                            new_version.address_line_4,
                                            new_version.address_line_5,
                                            new_version.post_code,
                                            new_version.country_code,
                                            new_version.is_primary,
                                            new_version.is_previous,
                                            new_version.years_at_address,
                                            new_version.is_deleted,
                                            new_version.employment_detail_id,
                                            p_record_source,
                                            new_version.source_address_id);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --

        SELECT *
        BULK COLLECT INTO ltab_updated_per_add
        FROM   person_addresses
        WHERE  person_id = p_person_id AND
               address_id NOT IN (SELECT address_id
                                  FROM   TABLE(CAST(p_person_addresses AS person_addresses_tab)))
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_per_add.COUNT LOOP
          put_history(p_old_person_address        => ltab_updated_per_add(lv_count),
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'D');
        END LOOP;

        FORALL lv_count IN 1..ltab_updated_per_add.COUNT
          DELETE person_addresses
          WHERE  person_id = ltab_updated_per_add(lv_count).person_id AND
                 address_id = ltab_updated_per_add(lv_count).address_id;

      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
      --
      -- Process out of sequence data/replayed messages
      --
        SELECT /*+ CARDINALITY (per_add_tab 3) */address_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_person_id,
               address_version,
               address_type,
               address_line_1,
               address_line_2,
               address_line_3,
               address_line_4,
               address_line_5,
               post_code,
               country_code,
               is_primary,
               is_previous,
               years_at_address,
               is_deleted,
               employment_detail_id,
               p_record_source,
               source_address_id
        BULK COLLECT INTO ltab_updated_per_add
        FROM   TABLE(CAST(p_person_addresses AS person_addresses_tab)) per_add_tab
        WHERE  --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (address_version, address_type, address_line_1, address_line_2, address_line_3, address_line_4,
                address_line_5, post_code, country_code, is_primary, is_previous, years_at_address, employment_detail_id,
                p_record_source, source_address_id) NOT IN (SELECT address_version, address_type, address_line_1, address_line_2, address_line_3, address_line_4,
                                                                 address_line_5, post_code, country_code, is_primary, is_previous, years_at_address, employment_detail_id ,
                                                                 record_source, source_address_id
                                                          FROM    person_addresses
                                                          WHERE   person_id = p_person_id AND
                                                                  effective_start_timestamp <= p_effective_start_timestamp
                                                          UNION ALL
                                                          SELECT address_version, address_type, address_line_1, address_line_2, address_line_3, address_line_4,
                                                                 address_line_5, post_code, country_code, is_primary, is_previous, years_at_address, employment_detail_id ,
                                                                 record_source, source_address_id
                                                          FROM   person_addresses_h
                                                          WHERE  person_id = p_person_id AND
                                                                 effective_start_timestamp <= p_effective_start_timestamp AND
                                                                 effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_cnt IN 1..ltab_updated_per_add.COUNT LOOP
          put_history(p_old_person_address        => ltab_updated_per_add(lv_cnt),
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'I');
        END LOOP;
      END CASE;
    END put_person_addresses;
    -- ===================================================================================
    -- put_person_addresses
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person addresses
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PERSON_ADDRESSES
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     p_Person_Financial_Details       Person Financial Details
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_stmt_email_address(p_user                           IN person_stmnt_email_address.created_by%TYPE,
                                          p_logical_load_timestamp         IN person_stmnt_email_address.logical_load_timestamp%TYPE,
                                          p_effective_start_timestamp      IN person_stmnt_email_address.effective_start_timestamp%TYPE,
                                          p_person_id                      IN person_stmnt_email_address.person_id%TYPE,
                                          p_trading_account_id             IN person_stmnt_email_address.Trading_Account_Id%TYPE,
                                          p_trading_account_type           IN person_stmnt_email_address.Trading_Account_Type%TYPE,
                                          p_person_stmnt_email_addr_tab    IN person_stmnt_email_address_tab)
  IS
  TYPE ltyp_person_stmnt_email_tab IS TABLE OF person_stmnt_email_address%ROWTYPE;

  ltab_person_stmnt_email ltyp_person_stmnt_email_tab;
  ltab_del_person_stmnt_email ltyp_person_stmnt_email_tab;

  BEGIN


  SELECT old_version.*
   BULK COLLECT INTO ltab_person_stmnt_email
   FROM person_stmnt_email_address old_version, TABLE(CAST(p_person_stmnt_email_addr_tab AS person_stmnt_email_address_tab)) new_version
        WHERE  old_version.stmnt_email_address_id = new_version.stmnt_email_address_id AND
               (nrg_common.has_value_changed(old_version.is_deleted, new_version.is_deleted) = 1 OR
                nrg_common.has_value_changed(old_version.Email_Address, new_version.email_address) = 1)
        FOR UPDATE OF old_version.stmnt_email_address_id;

    FOR lv_count IN 1..ltab_person_stmnt_email.COUNT LOOP
      put_history(p_person_old_stmnt_email_addr  => ltab_person_stmnt_email(lv_count),
                  p_effective_end_timestamp      => p_effective_start_timestamp,
                  p_action                       => 'U');
    END LOOP;


        MERGE INTO person_stmnt_email_address old_version
           USING  (SELECT /*+ CARDINALITY (t 3) */
                                            t.stmnt_email_address_id
                                           ,p_person_id AS person_id
                                           ,p_trading_account_id trading_account_id
                                           ,p_trading_account_type trading_account_type
                                           ,p_logical_load_timestamp AS logical_load_timestamp
                                           ,p_user AS  created_by
                                           ,SYSTIMESTAMP AS create_timestamp
                                           ,p_user AS updated_by
                                           ,SYSTIMESTAMP AS update_timestamp
                                           ,p_effective_start_timestamp AS effective_start_timestamp
                                           ,t.email_address AS email_address
                                           ,is_deleted AS is_deleted
                                    FROM TABLE(CAST(p_person_stmnt_email_addr_tab AS person_stmnt_email_address_tab)) t
                        ) new_version
            ON (old_version.Stmnt_Email_Address_Id = new_version.Stmnt_Email_Address_Id)
         WHEN MATCHED THEN UPDATE
             SET old_version.logical_load_timestamp         = new_version.logical_load_timestamp
                ,old_version.updated_by                     = new_version.updated_by
                ,old_version.update_timestamp               = new_version.update_timestamp
                ,old_version.is_deleted                     = new_version.is_deleted
                ,old_version.Email_Address                  = new_version.Email_Address
            WHERE (nrg_common.has_value_changed(old_version.Email_Address, new_version.email_address) = 1
                OR nrg_common.has_value_changed(old_version.is_deleted, new_version.is_deleted) = 1)
         WHEN NOT MATCHED THEN
           INSERT(Stmnt_Email_Address_Id,
                  person_id,
                  trading_account_id,
                  trading_account_type,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  email_address,
                  is_deleted)
               VALUES
                   (new_version.Stmnt_Email_Address_Id,
                    new_version.person_id,
                    new_version.trading_account_id,
                    new_version.trading_account_type,
                    new_version.logical_load_timestamp,
                    new_version.created_by,
                    new_version.create_timestamp,
                    new_version.updated_by,
                    new_version.update_timestamp,
                    new_version.effective_start_timestamp,
                    new_version.email_address,
                    new_version.is_deleted);


  ------------
  ---- Check if details are deleted
  ------------



  SELECT old_version.*
   BULK COLLECT INTO ltab_del_person_stmnt_email
   FROM person_stmnt_email_address old_version WHERE
        old_version.Person_Id = p_person_id AND
        old_version.trading_account_id = p_trading_account_id AND
        old_version.trading_account_type = p_trading_account_type AND NOT EXISTS
        (SELECT 1 FROM TABLE(CAST(p_person_stmnt_email_addr_tab AS person_stmnt_email_address_tab)) new_version
          WHERE old_version.Person_Id = p_Person_Id
            AND old_version.Trading_Account_Id = p_trading_account_id
            AND old_version.Trading_Account_Type = p_trading_account_type
            AND old_version.Stmnt_Email_Address_Id = new_version.Stmnt_Email_Address_Id)
        FOR UPDATE OF old_version.Person_Id;


        FOR lv_count IN 1..ltab_del_person_stmnt_email.COUNT LOOP
          put_history(p_person_old_stmnt_email_addr  => ltab_del_person_stmnt_email(lv_count),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_action                       => 'D');
        END LOOP;


        FORALL i IN 1..ltab_del_person_stmnt_email.COUNT
         DELETE FROM person_stmnt_email_address t
           WHERE t.Person_Id= ltab_del_person_stmnt_email(i).person_id
             AND t.Trading_Account_Id= ltab_del_person_stmnt_email(i).Trading_Account_Id
             AND t.Trading_Account_Type= ltab_del_person_stmnt_email(i).Trading_Account_Type
             AND t.Stmnt_Email_Address_Id= ltab_del_person_stmnt_email(i).Stmnt_Email_Address_Id;

    END put_person_stmt_email_address;

    -- ===================================================================================
    -- put_person_aliases
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person aliases
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PERSON_ALIASES
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     p_person_aliases                 Person Aliases
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_aliases (p_user persons.created_by%TYPE,
                                p_logical_load_timestamp person_aliases.logical_load_timestamp%TYPE,
                                p_effective_start_timestamp person_aliases.effective_start_timestamp%TYPE,
                                p_lv_effective_start_time person_aliases.effective_start_timestamp%TYPE,
                                p_person_id persons.person_id%TYPE,
                                p_person_aliases person_aliases_tab)
  IS
  TYPE ltyp_updated_per_als IS TABLE OF person_aliases%rowtype;

  ltab_updated_per_als ltyp_updated_per_als;
  ltab_updated_per_als_old ltyp_updated_per_als;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_aliases
                                  (
                                      created_by,
                                      create_timestamp,
                                      updated_by,
                                      update_timestamp,
                                      logical_load_timestamp,
                                      effective_start_timestamp,
                                      person_id,
                                      alias_id,
                                      alias_version,
                                      alias_type,
                                      alias_name
                                  )
                                  SELECT /*+ CARDINALITY (per_als_tab 1) */p_user,
                                         SYSTIMESTAMP,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_logical_load_timestamp,
                                         p_effective_start_timestamp,
                                         p_person_id,
                                         alias_id,
                                         alias_version,
                                         alias_type,
                                         alias_name
                                  FROM   TABLE(CAST(p_person_aliases AS person_aliases_tab)) per_als_tab
                                  WHERE  NOT EXISTS (SELECT 1
                                                    FROM person_aliases
                                                    WHERE alias_id = per_als_tab.alias_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        -- Insert the new data
        --

        SELECT /*+ CARDINALITY (per_als_tab 1) */per_als.alias_id,
               per_als.logical_load_timestamp,
               per_als.created_by,
               per_als.create_timestamp,
               per_als.updated_by,
               per_als.update_timestamp,
               per_als.effective_start_timestamp,
               per_als.person_id,
               per_als.alias_version,
               per_als.alias_type,
               per_als.alias_name
        BULK COLLECT INTO ltab_updated_per_als_old
        FROM   person_aliases per_als, TABLE(CAST(p_person_aliases AS person_aliases_tab)) per_als_tab
        WHERE  per_als.alias_id = per_als_tab.alias_id AND
               (nrg_common.has_value_changed(per_als.alias_version,per_als_tab.alias_version) = 1  OR
                nrg_common.has_value_changed(per_als.alias_type,per_als_tab.alias_type) = 1  OR
                nrg_common.has_value_changed(per_als.alias_name,per_als_tab.alias_name) = 1 OR
                nrg_common.has_value_changed(per_als.person_id,p_person_id) = 1)
        FOR UPDATE OF per_als.alias_id;

        FOR lv_count IN 1..ltab_updated_per_als_old.COUNT LOOP
          put_history(p_old_person_alias => ltab_updated_per_als_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');
        END LOOP;

        MERGE /*+ CARDINALITY (new_version 1) */INTO person_aliases old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_aliases AS person_aliases_tab))) new_version
        ON (old_version.alias_id = new_version.alias_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 alias_version = new_version.alias_version,
                 alias_type = new_version.alias_type,
                 alias_name = new_version.alias_name,
                 person_id = p_person_id
          WHERE (nrg_common.has_value_changed(old_version.alias_version,new_version.alias_version) = 1  OR
                 nrg_common.has_value_changed(old_version.alias_type,new_version.alias_type) = 1  OR
                 nrg_common.has_value_changed(old_version.alias_name,new_version.alias_name) = 1 OR
                 nrg_common.has_value_changed(old_version.person_id,p_person_id) = 1)
        WHEN NOT MATCHED THEN
          INSERT (alias_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  person_id,
                  alias_version,
                  alias_type,
                  alias_name)
          VALUES  (new_version.alias_id,
                   p_logical_load_timestamp,
                   p_user,
                   SYSTIMESTAMP,
                   p_user,
                   SYSTIMESTAMP,
                   p_effective_start_timestamp,
                   p_person_id,
                   new_version.alias_version,
                   new_version.alias_type,
                   new_version.alias_name);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --

        SELECT *
        BULK COLLECT INTO ltab_updated_per_als
        FROM  person_aliases
        WHERE person_id = p_person_id AND
              alias_id NOT IN (SELECT alias_id
                               FROM   TABLE(CAST(p_person_aliases AS person_aliases_tab)))
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_per_als.COUNT LOOP
          put_history(p_old_person_alias => ltab_updated_per_als(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D');
        END LOOP;

        FORALL lv_count IN 1..ltab_updated_per_als.COUNT
          DELETE person_aliases
          WHERE person_id = ltab_updated_per_als(lv_count).person_id AND
                alias_id = ltab_updated_per_als(lv_count).alias_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (per_als_tab 1) */alias_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_person_id,
               alias_version,
               alias_type,
               alias_name
        BULK COLLECT INTO ltab_updated_per_als_old
        FROM   TABLE(CAST(p_person_aliases AS person_aliases_tab)) per_als_tab
        WHERE  --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
              (alias_version, alias_type, alias_name) NOT IN (SELECT alias_version, alias_type, alias_name
                                                              FROM   person_aliases
                                                              WHERE  person_id = p_person_id AND
                                                                     effective_start_timestamp <= p_effective_start_timestamp
                                                              UNION ALL
                                                              SELECT alias_version, alias_type, alias_name
                                                              FROM   person_aliases_h
                                                              WHERE  person_id = p_person_id AND
                                                                     effective_start_timestamp <= p_effective_start_timestamp AND
                                                                     effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_count IN 1..ltab_updated_per_als_old.COUNT LOOP
          put_history(p_old_person_alias => ltab_updated_per_als_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I');
        END LOOP;
    END CASE;
  END put_person_aliases;

  -- ===================================================================================
  -- put_person_email_addresses
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person email addresses
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_EMAIL_ADDRESSES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     p_person_email_addresses         Person Email Addresses
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_email_addresses ( p_user persons.created_by%TYPE,
                                         p_logical_load_timestamp person_email_addresses.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp person_email_addresses.effective_start_timestamp%TYPE,
                                         p_lv_effective_start_time person_email_addresses.effective_start_timestamp%TYPE,
                                         p_person_id persons.person_id%TYPE,
                                         p_person_email_addresses person_email_addresses_tab,
                                         p_record_source person_email_addresses.record_source%TYPE)
  IS
  TYPE ltyp_updated_eml_add IS TABLE OF person_email_addresses%rowtype;

  ltab_updated_eml_add ltyp_updated_eml_add;
  ltab_updated_eml_add_old ltyp_updated_eml_add;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_email_addresses
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              person_id,
                                              email_address_id,
                                              email_version,
                                              email_type,
                                              email_address,
                                              is_primary,
                                              is_deleted,
                                              record_source,
                                              source_email_address_id
                                          )
                                          SELECT /*+ CARDINALITY (eml_add_tab 3) */p_user,
                                                 SYSTIMESTAMP,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_logical_load_timestamp,
                                                 p_effective_start_timestamp,
                                                 p_person_id,
                                                 email_address_id,
                                                 email_version,
                                                 email_type,
                                                 email_address,
                                                 is_primary,
                                                 is_deleted,
                                                 p_record_source,
                                                 source_email_address_id
                                          FROM   TABLE(CAST(p_person_email_addresses AS person_email_addresses_tab)) eml_add_tab
                                          WHERE  NOT EXISTS (SELECT 1
                                                            FROM person_email_addresses
                                                            WHERE email_address_id = eml_add_tab.email_address_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (eml_add_tab 3) */per_eml.email_address_id,
               p_logical_load_timestamp,
               per_eml.created_by,
               per_eml.create_timestamp,
               per_eml.updated_by,
               per_eml.update_timestamp,
               per_eml.effective_start_timestamp,
               per_eml.person_id,
               per_eml.email_version,
               per_eml.email_type,
               per_eml.email_address,
               per_eml.is_primary,
               per_eml.is_deleted,
               per_eml.record_source,
               per_eml.source_email_address_id
        BULK COLLECT INTO ltab_updated_eml_add_old
        FROM   person_email_addresses per_eml, TABLE(CAST(p_person_email_addresses AS person_email_addresses_tab)) eml_add_tab
        WHERE  per_eml.person_id = p_person_id AND
               per_eml.email_address_id = eml_add_tab.email_address_id AND
               (nrg_common.has_value_changed(per_eml.email_version,eml_add_tab.email_version) =1 OR
                nrg_common.has_value_changed(per_eml.email_type,eml_add_tab.email_type) =1 OR
                nrg_common.has_value_changed(per_eml.email_address,eml_add_tab.email_address) =1 OR
                nrg_common.has_value_changed(per_eml.is_primary,eml_add_tab.is_primary) =1 OR
                nrg_common.has_value_changed(per_eml.is_deleted,eml_add_tab.is_deleted ) = 1 OR
                nrg_common.has_value_changed(per_eml.record_source, p_record_source) = 1 OR
                nrg_common.has_value_changed(per_eml.source_email_address_id, eml_add_tab.source_email_address_id) = 1)
        FOR UPDATE OF per_eml.email_address_id;

        FOR lv_count IN 1..ltab_updated_eml_add_old.COUNT LOOP

          put_history(p_old_person_email_add => ltab_updated_eml_add_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');

        END LOOP;

        MERGE /*+ CARDINALITY (new_version 3) */INTO person_email_addresses old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_email_addresses AS person_email_addresses_tab))) new_version
        ON (old_version.email_address_id = new_version.email_address_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 email_version = new_version.email_version,
                 email_type = new_version.email_type,
                 email_address = new_version.email_address,
                 is_primary = new_version.is_primary,
                 is_deleted = new_version.is_deleted,
                 record_source = p_record_source,
                 source_email_address_id = new_version.source_email_address_id
          WHERE (nrg_common.has_value_changed(old_version.email_version,new_version.email_version) =1 OR
                nrg_common.has_value_changed(old_version.email_type,new_version.email_type) =1 OR
                nrg_common.has_value_changed(old_version.email_address,new_version.email_address) =1 OR
                nrg_common.has_value_changed(old_version.is_primary,new_version.is_primary) =1 OR
                nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted ) = 1 OR
                nrg_common.has_value_changed(old_version.record_source, p_record_source) = 1 OR
                nrg_common.has_value_changed(old_version.source_email_address_id, new_version.source_email_address_id) = 1)
        WHEN NOT MATCHED THEN
          INSERT  (
                   created_by,
                   create_timestamp,
                   updated_by,
                   update_timestamp,
                   logical_load_timestamp,
                   effective_start_timestamp,
                   person_id,
                   email_address_id,
                   email_version,
                   email_type,
                   email_address,
                   is_primary,
                   is_deleted,
                   record_source,
                   source_email_address_id
                  )
           VALUES (p_user,
                   SYSTIMESTAMP,
                   p_user,
                   SYSTIMESTAMP,
                   p_logical_load_timestamp,
                   p_effective_start_timestamp,
                   p_person_id,
                   new_version.email_address_id,
                   new_version.email_version,
                   new_version.email_type,
                   new_version.email_address,
                   new_version.is_primary,
                   new_version.is_deleted,
                   p_record_source,
                   new_version.source_email_address_id);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --
        SELECT *
        BULK COLLECT INTO ltab_updated_eml_add
        FROM person_email_addresses
        WHERE person_id = p_person_id AND
              email_address_id NOT IN (SELECT email_address_id
                                       FROM   TABLE(CAST(p_person_email_addresses AS person_email_addresses_tab)))
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_eml_add.COUNT LOOP

          put_history(p_old_person_email_add => ltab_updated_eml_add(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D');

        END LOOP;

        FORALL lv_count IN 1..ltab_updated_eml_add.COUNT
          DELETE person_email_addresses
          WHERE person_id = ltab_updated_eml_add(lv_count).person_id AND
                email_address_id = ltab_updated_eml_add(lv_count).email_address_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (eml_add_tab 3) */email_address_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_person_id,
               email_version,
               email_type,
               email_address,
               is_primary,
               is_deleted,
               p_record_source,
               source_email_address_id
        BULK COLLECT INTO ltab_updated_eml_add
        FROM   TABLE(CAST(p_person_email_addresses AS person_email_addresses_tab)) eml_add_tab
        WHERE  --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (email_version, email_type, email_address, is_primary, p_record_source, source_email_address_id)
                                                                      NOT IN (SELECT email_version, email_type, email_address, is_primary, record_source, source_email_address_id
                                                                              FROM    person_email_addresses
                                                                              WHERE   person_id = p_person_id AND
                                                                                     effective_start_timestamp <= p_effective_start_timestamp
                                                                              UNION ALL
                                                                              SELECT email_version, email_type, email_address, is_primary, record_source, source_email_address_id
                                                                              FROM   person_email_addresses_h
                                                                              WHERE  person_id = p_person_id AND
                                                                                     effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                     effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_count IN 1..ltab_updated_eml_add.COUNT LOOP
          put_history(p_old_person_email_add => ltab_updated_eml_add(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I');

        END LOOP;
    END CASE;
    END put_person_email_addresses;

    -- ===================================================================================
    -- put_person_national_identif
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put person national identifiers
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PERSON_NATIONAL_IDENTIFIERS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_person_id                      Person Id
    --     p_person_national_identifiers    Person National Identifiers
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_national_identif(p_user persons.created_by%TYPE,
                                               p_logical_load_timestamp person_national_identifiers.logical_load_timestamp%TYPE,
                                               p_effective_start_timestamp person_national_identifiers.effective_start_timestamp%TYPE,
                                               p_lv_effective_start_time person_national_identifiers.effective_start_timestamp%TYPE,
                                               p_person_id persons.person_id%TYPE,
                                               p_person_national_identifiers person_national_identif_tab)
  IS
  TYPE ltyp_updated_per_nat IS TABLE OF person_national_identifiers%rowtype;

  ltab_updated_per_nat ltyp_updated_per_nat;
  ltab_updated_per_nat_old ltyp_updated_per_nat;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_national_identifiers
                                               (
                                                created_by,
                                                create_timestamp,
                                                updated_by,
                                                update_timestamp,
                                                logical_load_timestamp,
                                                effective_start_timestamp,
                                                person_id,
                                                identification_id,
                                                identification_version,
                                                identification_type,
                                                national_identication,
                                                document_expiry_date,
                                                place_of_issue,
                                                country_of_issue,
                                                is_deleted
                                               )
                                                SELECT /*+ CARDINALITY (per_nat_tab 2) */p_user,
                                                       SYSTIMESTAMP,
                                                       p_user,
                                                       SYSTIMESTAMP,
                                                       p_logical_load_timestamp,
                                                       p_effective_start_timestamp,
                                                       p_person_id,
                                                       identification_id,
                                                       identification_version,
                                                       identification_type,
                                                       national_identication,
                                                       document_expiry_date,
                                                       place_of_issue,
                                                       country_of_issue,
                                                       is_deleted
                                                FROM   TABLE(CAST(p_person_national_identifiers AS person_national_identif_tab)) per_nat_tab
                                                WHERE  NOT EXISTS (SELECT 1
                                                                    FROM person_national_identifiers
                                                                    WHERE identification_id = per_nat_tab.identification_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (per_nat_tab 2) */per_nat.identification_id,
               per_nat.logical_load_timestamp,
               per_nat.created_by,
               per_nat.create_timestamp,
               per_nat.updated_by,
               per_nat.update_timestamp,
               per_nat.effective_start_timestamp,
               per_nat.person_id,
               per_nat.identification_version,
               per_nat.identification_type,
               per_nat.national_identication,
               per_nat.document_expiry_date,
               per_nat.place_of_issue,
               per_nat.Country_Of_Issue,
               per_nat.Is_Deleted
        BULK COLLECT INTO ltab_updated_per_nat_old
        FROM   person_national_identifiers per_nat, TABLE(CAST(p_person_national_identifiers AS person_national_identif_tab)) per_nat_tab
        WHERE  per_nat.identification_id = per_nat_tab.identification_id AND
               (nrg_common.has_value_changed(per_nat.identification_version,per_nat_tab.identification_version ) =1 OR
                nrg_common.has_value_changed(per_nat.identification_type,per_nat_tab.identification_type ) =1 OR
                nrg_common.has_value_changed(per_nat.national_identication,per_nat_tab.national_identication ) =1 OR
                nrg_common.has_value_changed(per_nat.document_expiry_date,per_nat_tab.document_expiry_date ) =1 OR
                nrg_common.has_value_changed(per_nat.place_of_issue,per_nat_tab.place_of_issue ) = 1 OR
                nrg_common.has_value_changed(per_nat.Country_Of_Issue,per_nat_tab.Country_Of_Issue ) = 1 OR
                nrg_common.has_value_changed(per_nat.Is_Deleted,per_nat_tab.Is_Deleted ) = 1
                )
        FOR UPDATE OF per_nat.identification_id;

        FOR lv_count IN 1..ltab_updated_per_nat_old.COUNT LOOP
          put_history(p_old_person_national_id => ltab_updated_per_nat_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');
        END LOOP;

        MERGE /*+ CARDINALITY (new_version 2) */INTO person_national_identifiers old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_national_identifiers AS person_national_identif_tab))) new_version
        ON (old_version.identification_id = new_version.identification_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 identification_version = new_version.identification_version,
                 identification_type = new_version.identification_type,
                 national_identication = new_version.national_identication,
                 document_expiry_date = new_version.document_expiry_date,
                 place_of_issue = new_version.place_of_issue,
                 country_of_issue = new_version.country_of_issue,
                 is_deleted = new_version.is_deleted
          WHERE (nrg_common.has_value_changed(old_version.identification_version,new_version.identification_version ) =1 OR
                nrg_common.has_value_changed(old_version.identification_type,new_version.identification_type ) =1 OR
                nrg_common.has_value_changed(old_version.national_identication,new_version.national_identication ) =1 OR
                nrg_common.has_value_changed(old_version.document_expiry_date,new_version.document_expiry_date ) =1 OR
                nrg_common.has_value_changed(old_version.place_of_issue,new_version.place_of_issue ) = 1 OR
                nrg_common.has_value_changed(old_version.country_of_issue,new_version.country_of_issue ) = 1 OR
                nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted ) = 1
                )
        WHEN NOT MATCHED THEN
          INSERT (identification_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  person_id,
                  identification_version,
                  identification_type,
                  national_identication,
                  document_expiry_date,
                  place_of_issue,
                  country_of_issue,
                  is_deleted)
          VALUES (new_version.identification_id,
                  p_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_effective_start_timestamp,
                  p_person_id,
                  new_version.identification_version,
                  new_version.identification_type,
                  new_version.national_identication,
                  new_version.document_expiry_date,
                  new_version.place_of_issue,
                  new_version.country_of_issue,
                  new_version.is_deleted
                  );

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --

        SELECT *
        BULK COLLECT INTO ltab_updated_per_nat
        FROM person_national_identifiers
        WHERE person_id = p_person_id AND
              identification_id NOT IN (SELECT identification_id
                                        FROM   TABLE(CAST(p_person_national_identifiers AS person_national_identif_tab)))
        FOR UPDATE;

       FOR lv_count IN 1..ltab_updated_per_nat.COUNT LOOP
         put_history(p_old_person_national_id   => ltab_updated_per_nat(lv_count),
                     p_effective_end_timestamp  => p_effective_start_timestamp,
                     p_action                   => 'D');
       END LOOP;

       FORALL lv_count IN 1..ltab_updated_per_nat.COUNT
       DELETE person_national_identifiers
       WHERE person_id = ltab_updated_per_nat(lv_count).person_id AND
             identification_id = ltab_updated_per_nat(lv_count).identification_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (per_nat_tab 2) */identification_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_person_id,
               identification_version,
               identification_type,
               national_identication,
               document_expiry_date,
               place_of_issue,
               country_of_issue,
               is_deleted
        BULK COLLECT INTO ltab_updated_per_nat
        FROM   TABLE(CAST(p_person_national_identifiers AS person_national_identif_tab)) per_nat_tab
        WHERE
              --
              -- We need to exclude any data that entering the history where the non-key columns
              -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
              -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
              -- and replaying exising messages
              --
              (identification_version, identification_type, national_identication, document_expiry_date, place_of_issue
               ,country_of_issue, is_deleted) NOT IN (SELECT identification_version, identification_type, national_identication, document_expiry_date, place_of_issue,country_of_issue, is_deleted
                                                                                                                                  FROM   person_national_identifiers
                                                                                                                                  WHERE  person_id = p_person_id AND
                                                                                                                                         effective_start_timestamp <= p_effective_start_timestamp
                                                                                                                                  UNION ALL
                                                                                                                                  SELECT identification_version, identification_type, national_identication, document_expiry_date, place_of_issue, country_of_issue, is_deleted
                                                                                                                                  FROM   person_national_identifiers_h
                                                                                                                                  WHERE  person_id = p_person_id AND
                                                                                                                                         effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                                                                         effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_count IN 1..ltab_updated_per_nat.COUNT LOOP

         put_history(p_old_person_national_id   => ltab_updated_per_nat(lv_count),
                     p_effective_end_timestamp  => p_effective_start_timestamp,
                     p_action                   => 'I');
       END LOOP;
    END CASE;
  END put_person_national_identif;

  -- ===================================================================================
  -- put_person_relationships
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person relationships
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_RELATIONSHIPS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_relationships           Person Relationship
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_person_relationships(p_person_id persons.person_id%TYPE,
                                     p_user persons.created_by%TYPE,
                                     p_logical_load_timestamp person_relationships.logical_load_timestamp%TYPE,
                                     p_effective_start_timestamp person_relationships.effective_start_timestamp%TYPE,
                                     p_lv_effective_start_time person_relationships.effective_start_timestamp%TYPE,
                                     p_person_relationships person_relationships_tab)
  IS
  TYPE ltyp_updated_per_rel IS TABLE OF person_relationships%rowtype;

  ltab_updated_per_rel ltyp_updated_per_rel;
  ltab_updated_per_rel_old ltyp_updated_per_rel;

  BEGIN

      IF p_person_relationships IS NOT NULL THEN
        FOR i IN 1..p_person_relationships.COUNT LOOP
          IF p_person_id <> p_person_relationships(i).person_id_from THEN
            --Create Person Id Stub
            nrg_person.create_person_stub (p_user                        => p_user,
                                           p_logical_load_timestamp      => p_logical_load_timestamp,
                                           p_effective_start_timestamp   => p_effective_start_timestamp,
                                           p_person_id                   => p_person_relationships(i).person_id_from);
          END IF;

          IF p_person_id <> p_person_relationships(i).person_id_to THEN
            --Create Person Id Stub
            nrg_person.create_person_stub (p_user                        => p_user,
                                           p_logical_load_timestamp      => p_logical_load_timestamp,
                                           p_effective_start_timestamp   => p_effective_start_timestamp,
                                           p_person_id                   => p_person_relationships(i).person_id_to);
          END IF;
        END LOOP;
      END IF;

    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_relationships
                                        (
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            logical_load_timestamp,
                                            effective_start_timestamp,
                                            relationship_id,
                                            relationship_version,
                                            person_id_from,
                                            person_id_to,
                                            relationship_type,
                                            is_previous
                                         )
                                        SELECT p_user,
                                               SYSTIMESTAMP,
                                               p_user,
                                               SYSTIMESTAMP,
                                               p_logical_load_timestamp,
                                               p_effective_start_timestamp,
                                               relationship_id,
                                               relationship_version,
                                               person_id_from,
                                               person_id_to,
                                               relationship_type,
                                               is_previous
                                        FROM   TABLE(CAST(p_person_relationships AS person_relationships_tab)) per_rel_tab
                                        WHERE  NOT EXISTS (SELECT 1
                                                            FROM person_relationships
                                                            WHERE relationship_id = per_rel_tab.relationship_id);





      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT per_rel.relationship_id,
               per_rel.logical_load_timestamp,
               per_rel.created_by,
               per_rel.create_timestamp,
               per_rel.updated_by,
               per_rel.update_timestamp,
               per_rel.effective_start_timestamp,
               per_rel.relationship_version,
               per_rel.person_id_from,
               per_rel.person_id_to,
               per_rel.relationship_type,
               per_rel.is_previous
        BULK COLLECT INTO ltab_updated_per_rel_old
        FROM   person_relationships per_rel, TABLE(CAST(p_person_relationships AS person_relationships_tab)) per_rel_tab
        WHERE  per_rel.relationship_id = per_rel_tab.relationship_id AND
               (nrg_common.has_value_changed(per_rel.relationship_version,per_rel_tab.relationship_version) =1 OR
                nrg_common.has_value_changed(per_rel.relationship_type,per_rel_tab.relationship_type) =1 OR
                nrg_common.has_value_changed(per_rel.is_previous,per_rel_tab.is_previous) =1)
        FOR UPDATE OF per_rel.relationship_id;

        FOR lv_count IN 1..ltab_updated_per_rel_old.COUNT LOOP
          put_history(p_old_person_relationship   => ltab_updated_per_rel_old(lv_count),
                        p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action                  => 'U');
        END LOOP;

        MERGE INTO person_relationships old_version
        USING (SELECT *
               FROM   TABLE(CAST(p_person_relationships AS person_relationships_tab))) new_version
        ON (old_version.relationship_id = new_version.relationship_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 relationship_version = new_version.relationship_version,
                 relationship_type = new_version.relationship_type,
                 is_previous = new_version.is_previous,
                 person_id_from = new_version.person_id_from,
                 person_id_to = new_version.person_id_to
          WHERE (nrg_common.has_value_changed(old_version.relationship_version,new_version.relationship_version) =1 OR
                 nrg_common.has_value_changed(old_version.relationship_type,new_version.relationship_type) =1 OR
                 nrg_common.has_value_changed(old_version.is_previous,new_version.is_previous) =1 )
          WHEN NOT MATCHED THEN
            INSERT (relationship_id,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    effective_start_timestamp,
                    relationship_version,
                    person_id_from,
                    person_id_to,
                    relationship_type,
                    is_previous)
            VALUES (new_version.relationship_id,
                    p_logical_load_timestamp,
                    p_user,
                    SYSTIMESTAMP,
                    p_user,
                    SYSTIMESTAMP,
                    p_effective_start_timestamp,
                    new_version.relationship_version,
                    new_version.person_id_from,
                    new_version.person_id_to,
                    new_version.relationship_type,
                    new_version.is_previous);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --
        SELECT *
        BULK COLLECT INTO ltab_updated_per_rel
        FROM person_relationships t
        WHERE relationship_id NOT IN (SELECT relationship_id
                                      FROM   TABLE(CAST(p_person_relationships AS person_relationships_tab)))
        AND t.Person_Id_From = p_person_id
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_per_rel.COUNT LOOP
          put_history(p_old_person_relationship => ltab_updated_per_rel(lv_count),
                        p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action => 'D');
        END LOOP;

        FORALL lv_count IN 1..ltab_updated_per_rel.COUNT
          DELETE person_relationships
          WHERE relationship_id = ltab_updated_per_rel(lv_count).relationship_id
          AND Person_Id_From = p_person_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT relationship_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               relationship_version,
               person_id_from,
               person_id_to,
               relationship_type,
               is_previous
        BULK COLLECT INTO ltab_updated_per_rel
        FROM   TABLE(CAST(p_person_relationships AS person_relationships_tab)) per_rel_tab
        WHERE
               --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (relationship_version, relationship_type, is_previous) NOT IN (SELECT relationship_version, relationship_type, is_previous
                                                                              FROM    person_relationships
                                                                              WHERE   relationship_id = per_rel_tab.relationship_id AND
                                                                                     effective_start_timestamp <= p_effective_start_timestamp
                                                                              UNION ALL
                                                                              SELECT relationship_version, relationship_type, is_previous
                                                                              FROM   person_relationships_h
                                                                              WHERE  relationship_id = per_rel_tab.relationship_id AND
                                                                                     effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                     effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_count IN 1..ltab_updated_per_rel.COUNT LOOP
          put_history(p_old_person_relationship => ltab_updated_per_rel(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action                  => 'I');
        END LOOP;
    END CASE;
  END put_person_relationships;


  -- ===================================================================================
  -- put_person_telephone_numbers
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person telephone numbers
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_TELEPHONE_NUMBERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     p_person_telephone_numbers       Person Telephone Numbers
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_telephone_numbers(p_user persons.created_by%TYPE,
                                         p_logical_load_timestamp person_telephone_numbers.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp person_telephone_numbers.effective_start_timestamp%TYPE,
                                         p_lv_effective_start_time person_telephone_numbers.effective_start_timestamp%TYPE,
                                         p_person_id persons.person_id%TYPE,
                                         p_person_telephone_numbers person_telephone_numbers_tab,
                                         p_record_source person_telephone_numbers.record_source%TYPE)
  IS
  TYPE ltyp_updated_tel_num IS TABLE OF person_telephone_numbers%rowtype;

  ltab_updated_tel_num ltyp_updated_tel_num;
  ltab_updated_tel_num_old ltyp_updated_tel_num;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_telephone_numbers
                                            (
                                                created_by,
                                                create_timestamp,
                                                updated_by,
                                                update_timestamp,
                                                logical_load_timestamp,
                                                effective_start_timestamp,
                                                person_id,
                                                telephone_id,
                                                telephone_version,
                                                telephone_type,
                                                telephone_number,
                                                is_primary,
                                                is_deleted,
                                                record_source,
                                                source_telephone_id
                                            )
                                            SELECT /*+ CARDINALITY (tel_num_tab 3) */p_user,
                                                   SYSTIMESTAMP,
                                                   p_user,
                                                   SYSTIMESTAMP,
                                                   p_logical_load_timestamp,
                                                   p_effective_start_timestamp,
                                                   p_person_id,
                                                   telephone_id,
                                                   telephone_version,
                                                   telephone_type,
                                                   telephone_number,
                                                   is_primary,
                                                   is_deleted,
                                                   p_record_source,
                                                   source_telephone_id
                                            FROM   TABLE(CAST(p_person_telephone_numbers AS person_telephone_numbers_tab)) tel_num_tab
                                            WHERE  NOT EXISTS (SELECT 1
                                                                    FROM person_telephone_numbers
                                                                    WHERE telephone_id = tel_num_tab.telephone_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (tel_num_tab 3) */per_tel.telephone_id,
               per_tel.logical_load_timestamp,
               per_tel.created_by,
               per_tel.create_timestamp,
               per_tel.updated_by,
               per_tel.update_timestamp,
               per_tel.effective_start_timestamp,
               per_tel.person_id,
               per_tel.telephone_version,
               per_tel.telephone_type,
               per_tel.telephone_number,
               per_tel.is_primary,
               per_tel.is_deleted,
               per_tel.record_source,
               per_tel.source_telephone_id
        BULK COLLECT INTO ltab_updated_tel_num_old
        FROM   person_telephone_numbers per_tel, TABLE(CAST(p_person_telephone_numbers AS person_telephone_numbers_tab)) tel_num_tab
        WHERE  per_tel.telephone_id = tel_num_tab.telephone_id AND
               (nrg_common.has_value_changed(per_tel.telephone_version,tel_num_tab.telephone_version) = 1 OR
                nrg_common.has_value_changed(per_tel.telephone_type,tel_num_tab.telephone_type) = 1 OR
                nrg_common.has_value_changed(per_tel.telephone_number,tel_num_tab.telephone_number) = 1 OR
                nrg_common.has_value_changed(per_tel.is_primary,tel_num_tab.is_primary) = 1 or
                nrg_common.has_value_changed(per_tel.is_deleted,tel_num_tab.is_deleted) =1 OR
                nrg_common.has_value_changed(per_tel.record_source, p_record_source) = 1 OR
                nrg_common.has_value_changed(per_tel.source_telephone_id, tel_num_tab.source_telephone_id) = 1
                )
        FOR UPDATE OF per_tel.telephone_id;

        FOR lv_count IN 1..ltab_updated_tel_num_old.COUNT LOOP
          put_history(p_old_person_telephone_num  => ltab_updated_tel_num_old(lv_count),
                        p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action                  => 'U');
        END LOOP;

        MERGE /*+ CARDINALITY (new_version 3) */ INTO person_telephone_numbers old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_telephone_numbers AS person_telephone_numbers_tab))) new_version
        ON (old_version.telephone_id = new_version.telephone_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 logical_load_timestamp = p_logical_load_timestamp,
                 effective_start_timestamp = p_effective_start_timestamp,
                 telephone_version = new_version.telephone_version,
                 telephone_type = new_version.telephone_type,
                 telephone_number = new_version.telephone_number,
                 is_primary = new_version.is_primary,
                 is_deleted = new_version.is_deleted,
                 person_id = p_person_id,
                 record_source = p_record_source,
                 source_Telephone_id = new_version.source_telephone_id
          WHERE (nrg_common.has_value_changed(old_version.telephone_version,new_version.telephone_version) = 1 OR
                nrg_common.has_value_changed(old_version.telephone_type,new_version.telephone_type) = 1 OR
                nrg_common.has_value_changed(old_version.telephone_number,new_version.telephone_number) = 1 OR
                nrg_common.has_value_changed(old_version.is_primary,new_version.is_primary) = 1 OR
                nrg_common.has_value_changed(old_version.is_deleted,new_version.is_deleted) =1 OR
                nrg_common.has_value_changed(old_version.person_id, p_person_id) = 1 OR
                nrg_common.has_value_changed(old_version.record_source, p_record_source) = 1 OR
                nrg_common.has_value_changed(old_version.source_telephone_id, new_version.source_telephone_id) = 1
                )
        WHEN NOT MATCHED THEN
          INSERT (
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  logical_load_timestamp,
                  effective_start_timestamp,
                  person_id,
                  telephone_id,
                  telephone_version,
                  telephone_type,
                  telephone_number,
                  is_primary,
                  is_deleted,
                  record_source,
                  source_telephone_id
                 )
          VALUES (p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  p_logical_load_timestamp,
                  p_effective_start_timestamp,
                  p_person_id,
                  new_version.telephone_id,
                  new_version.telephone_version,
                  new_version.telephone_type,
                  new_version.telephone_number,
                  new_version.is_primary,
                  new_version.is_deleted,
                  p_record_source,
                  new_version.source_telephone_id);


        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --
        SELECT *
        BULK COLLECT INTO ltab_updated_tel_num
        FROM person_telephone_numbers
        WHERE person_id = p_person_id AND
              telephone_id NOT IN (SELECT telephone_id
                                   FROM   TABLE(CAST(p_person_telephone_numbers AS person_telephone_numbers_tab)))
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_tel_num.COUNT LOOP
          put_history(p_old_person_telephone_num => ltab_updated_tel_num(lv_count),
                        p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action => 'D');
        END LOOP;


        FORALL lv_count IN 1..ltab_updated_tel_num.COUNT
          DELETE person_telephone_numbers
          WHERE person_id = ltab_updated_tel_num(lv_count).person_id AND
                telephone_id = ltab_updated_tel_num(lv_count).telephone_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN

        SELECT /*+ CARDINALITY (tel_num_tab 3) */telephone_id,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_person_id,
               telephone_version,
               telephone_type,
               telephone_number,
               is_primary,
               is_deleted,
               p_record_source,
               source_telephone_id
        BULK COLLECT INTO ltab_updated_tel_num
        FROM   TABLE(CAST(p_person_telephone_numbers AS person_telephone_numbers_tab)) tel_num_tab
        WHERE  (telephone_version, telephone_type, telephone_number, is_primary, p_record_source, source_telephone_id)
                                                                                NOT IN (SELECT telephone_version, telephone_type, telephone_number, is_primary, record_source, source_telephone_id
                                                                                         FROM    person_telephone_numbers
                                                                                         WHERE   person_id = p_person_id AND
                                                                                                 effective_start_timestamp <= p_effective_start_timestamp
                                                                                         UNION ALL
                                                                                         SELECT telephone_version, telephone_type, telephone_number, is_primary, record_source, source_telephone_id
                                                                                         FROM   person_telephone_numbers_h
                                                                                         WHERE  person_id = p_person_id AND
                                                                                                effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                                effective_end_timestamp > p_effective_start_timestamp);
        FOR lv_count IN 1..ltab_updated_tel_num.COUNT LOOP
          put_history(p_old_person_telephone_num => ltab_updated_tel_num(lv_count),
                        p_effective_end_timestamp => p_effective_start_timestamp,
                        p_action => 'I');
        END LOOP;
    END CASE;
  END put_person_telephone_numbers;

  -- ===================================================================================
  -- put_person_trading_experience
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person email addresses
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_TRADING_EXPERIENCE
  --     PERSON_TRADING_EXPERIENCE_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     person_trading_experience_tab    Person Trading Experience tab
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_trading_experience ( p_user persons.created_by%TYPE,
                                            p_logical_load_timestamp person_trading_experience.logical_load_timestamp%TYPE,
                                            p_effective_start_timestamp person_trading_experience.effective_start_timestamp%TYPE,
                                            p_lv_effective_start_time person_trading_experience.effective_start_timestamp%TYPE,
                                            p_person_id persons.person_id%TYPE,
                                            p_person_trading_experience person_trading_experience_tab)
  IS
  TYPE ltyp_updated_pte IS TABLE OF person_trading_experience%rowtype;

  ltab_updated_pte ltyp_updated_pte;
  ltab_updated_pte_old ltyp_updated_pte;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_trading_experience
                                          (   asset_class,
                                              logical_load_timestamp,
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              effective_start_timestamp,
                                              experience,
                                              value,
                                              person_id)
                                          SELECT pte_tab.asset_class,
                                                 p_logical_load_timestamp,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_effective_start_timestamp,
                                                 pte_tab.experience,
                                                 pte_tab.value,
                                                 p_person_id
                                          FROM   TABLE(CAST(p_person_trading_experience AS person_trading_experience_tab)) pte_tab
                                          WHERE  NOT EXISTS (SELECT 1
                                                            FROM person_trading_experience old_tab
                                                            WHERE old_tab.asset_class = pte_tab.asset_class
                                                             AND old_tab.Person_Id = p_person_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (pte_tab 3) */
               pte.asset_class,
               pte.logical_load_timestamp,
               pte.Created_By,
               pte.create_timestamp,
               pte.updated_by,
               pte.update_timestamp,
               pte.effective_start_timestamp,
               pte.experience,
               pte.Value,
               pte.person_id
        BULK COLLECT INTO ltab_updated_pte_old
        FROM   person_trading_experience pte, TABLE(CAST(p_person_trading_experience AS person_trading_experience_tab)) pte_tab
        WHERE   pte.asset_class = pte_tab.asset_class AND
                pte.Person_Id = p_person_id AND
               (nrg_common.has_value_changed(pte.experience,pte_tab.experience) =1 OR
                nrg_common.has_value_changed(pte.value,pte_tab.value) =1)
        FOR UPDATE OF pte.asset_class;

        FOR lv_count IN 1..ltab_updated_pte_old.COUNT LOOP

          put_history(p_old_pte => ltab_updated_pte_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');

        END LOOP;

        MERGE /*+ CARDINALITY (new_version 3) */INTO person_trading_experience old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_trading_experience AS person_trading_experience_tab))) new_version
        ON (old_version.Asset_Class = new_version.asset_class AND
            old_version.Person_Id = p_person_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 experience = new_version.experience,
                 value = new_version.value
          WHERE (nrg_common.has_value_changed(old_version.experience,new_version.experience) =1 OR
                nrg_common.has_value_changed(old_version.value,new_version.value) = 1)
        WHEN NOT MATCHED THEN
          INSERT  ( asset_class,
                    logical_load_timestamp,
                    created_by,
                    create_timestamp,
                    updated_by,
                    update_timestamp,
                    effective_start_timestamp,
                    experience,
                    value,
                    person_id)
           VALUES (new_version.asset_class,
                   p_logical_load_timestamp,
                   p_user,
                   SYSTIMESTAMP,
                   p_user,
                   SYSTIMESTAMP,
                   p_effective_start_timestamp,
                   new_version.experience,
                   new_version.value,
                   p_person_id);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --
        SELECT *
        BULK COLLECT INTO ltab_updated_pte
        FROM person_trading_experience pte
        WHERE pte.person_id = p_person_id
          AND NOT EXISTS
               (SELECT asset_class
                 FROM TABLE(CAST(p_person_trading_experience AS person_trading_experience_tab)) new_pte
                  WHERE pte.asset_class = new_pte.asset_class
                  AND  pte.Person_Id =p_person_id )
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_pte.COUNT LOOP

          put_history(p_old_pte => ltab_updated_pte(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D');

        END LOOP;

        FORALL lv_count IN 1..ltab_updated_pte.COUNT
          DELETE  person_trading_experience
          WHERE person_id = ltab_updated_pte(lv_count).person_id AND
                asset_class = ltab_updated_pte(lv_count).asset_class;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (pte_tab 3) */
               pte_tab.asset_class,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               pte_tab.experience,
               pte_tab.value,
               p_person_id
        BULK COLLECT INTO ltab_updated_pte
        FROM   TABLE(CAST(p_person_trading_experience AS person_trading_experience_tab)) pte_tab
        WHERE  --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (experience, value) NOT IN (SELECT experience, value
                                            FROM person_trading_experience
                                             WHERE  person_id = p_person_id
                                               AND effective_start_timestamp <= p_effective_start_timestamp
                                            UNION ALL
                                            SELECT experience, value
                                             FROM person_trading_experience_h
                                              WHERE  person_id = p_person_id
                                                AND effective_start_timestamp <= p_effective_start_timestamp
                                                AND effective_end_timestamp > p_effective_start_timestamp);

        FOR lv_count IN 1..ltab_updated_pte.COUNT LOOP
          put_history(p_old_pte => ltab_updated_pte(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I');

        END LOOP;
    END CASE;
    END put_person_trading_experience;
  -- ===================================================================================
  -- put_person_trading_profiles
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person email addresses
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSON_TRADING_PROFILES
  --     PERSON_TRADING_PROFILES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     person_trading_profiles_tab      Person Trading Profiles tab
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_trading_profiles ( p_user persons.created_by%TYPE,
                                          p_logical_load_timestamp person_trading_profiles.logical_load_timestamp%TYPE,
                                          p_effective_start_timestamp person_trading_profiles.effective_start_timestamp%TYPE,
                                          p_lv_effective_start_time person_trading_profiles.effective_start_timestamp%TYPE,
                                          p_person_id person_trading_profiles.person_id%TYPE,
                                          p_person_trading_profiles person_trading_profiles_tab)
  IS
  TYPE ltyp_updated_ptp IS TABLE OF person_trading_profiles%rowtype;

  ltab_updated_ptp ltyp_updated_ptp;
  ltab_updated_ptp_old ltyp_updated_ptp;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_trading_profiles(person_id
                                          ,trading_account_id
                                          ,logical_load_timestamp
                                          ,created_by
                                          ,create_timestamp
                                          ,updated_by
                                          ,update_timestamp
                                          ,effective_start_timestamp
                                          ,one_click_allowed
                                          ,one_click_enabled
                                          ,one_click_style
                                          ,daily_statement_requested
                                          ,Monthly_Statement_Requested
                                          ,Yearly_Statement_Requested
                                          ,is_trade_confirm_email_req
                                          ,is_suspend_all_alerts
                                          ,suspend_alerts_from
                                          ,suspend_alerts_to
                                          ,notification_email
                                          ,notification_sms)
                                          SELECT p_person_id,
                                                 ptp_tab.trading_account_id,
                                                 p_logical_load_timestamp,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_effective_start_timestamp,
                                                 ptp_tab.one_click_allowed,
                                                 ptp_tab.one_click_enabled,
                                                 ptp_tab.one_click_style,
                                                 ptp_tab.daily_statement_requested,
                                                 ptp_tab.Monthly_Statement_Requested,
                                                 ptp_tab.Yearly_Statement_Requested,
                                                 ptp_tab.is_trade_confirm_email_req,
                                                null,
						 null,
						 null,
						 /*
						 ptp_tab.is_suspend_all_alerts,
						 ptp_tab.suspend_alerts_from,
                                                 ptp_tab.suspend_alerts_to, */--DR
                                                 ptp_tab.notification_email,
                                                 ptp_tab.notification_sms
                                          FROM   TABLE(CAST(p_person_trading_profiles AS person_trading_profiles_tab)) ptp_tab
                                          WHERE  NOT EXISTS (SELECT 1
                                                            FROM person_trading_profiles old_tab
                                                            WHERE old_tab.person_id = p_person_id
                                                              AND old_tab.Trading_Account_Id = ptp_tab.trading_account_id);


      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (ptp_tab 3) */
                ptp.person_id
               ,ptp.trading_account_id
               ,ptp.Trading_Account_Type
               ,ptp.logical_load_timestamp
               ,ptp.created_by
               ,ptp.create_timestamp
               ,ptp.updated_by
               ,ptp.update_timestamp
               ,ptp.effective_start_timestamp
               ,ptp.one_click_allowed
               ,ptp.one_click_enabled
               ,ptp.one_click_style
               ,ptp.daily_statement_requested
               ,ptp.Monthly_Statement_Requested
               ,ptp.Yearly_Statement_Requested
               ,ptp.is_trade_confirm_email_req
		,null
		,null
		,null
		/*
		,ptp.is_suspend_all_alerts
		,ptp.suspend_alerts_from
		,ptp.suspend_alerts_to */ --DR
               ,ptp.notification_email
               ,ptp.notification_sms
        BULK COLLECT INTO ltab_updated_ptp_old
        FROM   person_trading_profiles ptp, TABLE(CAST(p_person_trading_profiles AS person_trading_profiles_tab)) ptp_tab
        WHERE   ptp.person_id = p_person_id AND
                ptp.trading_account_id = ptp_tab.trading_account_id AND
               (nrg_common.has_value_changed(ptp.one_click_allowed, ptp_tab.one_click_allowed) =1 OR
                nrg_common.has_value_changed(ptp.one_click_enabled, ptp_tab.one_click_enabled) =1 OR
                nrg_common.has_value_changed(ptp.one_click_style, ptp_tab.one_click_style) =1 OR
                nrg_common.has_value_changed(ptp.daily_statement_requested, ptp_tab.daily_statement_requested) = 1 OR
                nrg_common.has_value_changed(ptp.Monthly_Statement_Requested, ptp_tab.Monthly_Statement_Requested) = 1 OR
                nrg_common.has_value_changed(ptp.Yearly_Statement_Requested, ptp_tab.Yearly_Statement_Requested) = 1 OR
                nrg_common.has_value_changed(ptp.is_trade_confirm_email_req, ptp_tab.is_trade_confirm_email_req) = 1 OR
		/*
		nrg_common.has_value_changed(ptp.is_suspend_all_alerts, ptp_tab.is_suspend_all_alerts) = 1 OR
		nrg_common.has_value_changed(ptp.suspend_alerts_from, ptp_tab.suspend_alerts_from) = 1 OR
		nrg_common.has_value_changed(ptp.suspend_alerts_to, ptp_tab.suspend_alerts_to) = 1 OR */ --DR
                nrg_common.has_value_changed(ptp.notification_email,ptp_tab.notification_email) = 1 OR
                nrg_common.has_value_changed(ptp.notification_sms,ptp_tab.notification_sms) = 1)
        FOR UPDATE OF ptp.person_id;

        FOR lv_count IN 1..ltab_updated_ptp_old.COUNT LOOP

          put_history(p_old_ptp => ltab_updated_ptp_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');

        END LOOP;

        MERGE /*+ CARDINALITY (new_version 3) */INTO person_trading_profiles old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_trading_profiles AS person_trading_profiles_tab))) new_version
        ON (old_version.trading_account_id = new_version.trading_account_id AND
            old_version.person_id=p_person_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 effective_start_timestamp = p_effective_start_timestamp,
                 logical_load_timestamp = p_logical_load_timestamp,
                 one_click_allowed = new_version.one_click_allowed,
                 one_click_enabled = new_version.one_click_enabled,
                 one_click_style = new_version.one_click_style,
                 daily_statement_requested = new_version.daily_statement_requested,
                 monthly_statement_requested = new_version.monthly_statement_requested,
                 yearly_statement_requested = new_version.yearly_statement_requested,
                 is_trade_confirm_email_req = new_version.is_trade_confirm_email_req,
		/*
		is_suspend_all_alerts = new_version.is_suspend_all_alerts,
		suspend_alerts_from = new_version.suspend_alerts_from,
		suspend_alerts_to = new_version.suspend_alerts_to, */ --DR
                 notification_email = new_version.notification_email,
                 notification_sms = new_version.notification_sms
          WHERE (nrg_common.has_value_changed(old_version.one_click_allowed,new_version.one_click_allowed) =1 OR
                 nrg_common.has_value_changed(old_version.one_click_enabled,new_version.one_click_enabled) = 1 OR
                 nrg_common.has_value_changed(old_version.one_click_style,new_version.one_click_style) = 1 OR
                 nrg_common.has_value_changed(old_version.daily_statement_requested, new_version.daily_statement_requested) = 1 OR
                 nrg_common.has_value_changed(old_version.monthly_statement_requested, new_version.monthly_statement_requested) = 1 OR
                 nrg_common.has_value_changed(old_version.yearly_statement_requested, new_version.yearly_statement_requested) = 1 OR
                 nrg_common.has_value_changed(old_version.is_trade_confirm_email_req, new_version.is_trade_confirm_email_req) = 1 OR
		/*
		nrg_common.has_value_changed(old_version.is_suspend_all_alerts, new_version.is_suspend_all_alerts) = 1 OR
		nrg_common.has_value_changed(old_version.suspend_alerts_from, new_version.suspend_alerts_from) = 1 OR
		nrg_common.has_value_changed(old_version.suspend_alerts_to, new_version.suspend_alerts_to) = 1 OR */ --DR
                 nrg_common.has_value_changed(old_version.notification_email, new_version.notification_email) = 1 OR
                 nrg_common.has_value_changed(old_version.notification_sms, new_version.notification_sms) = 1)
        WHEN NOT MATCHED THEN
          INSERT  ( person_id
                   ,trading_account_id
                   ,logical_load_timestamp
                   ,created_by
                   ,create_timestamp
                   ,updated_by
                   ,update_timestamp
                   ,effective_start_timestamp
                   ,one_click_allowed
                   ,one_click_enabled
                   ,one_click_style
                   ,daily_statement_requested
                   ,monthly_statement_requested
                   ,yearly_statement_requested
                   ,is_trade_confirm_email_req
		/*
		,is_suspend_all_alerts
		,suspend_alerts_from
		,suspend_alerts_to */ --DR
                   ,notification_email
                   ,notification_sms)
           VALUES (p_person_id,
                   new_version.trading_account_id,
                   p_logical_load_timestamp,
                   p_user,
                   SYSTIMESTAMP,
                   p_user,
                   SYSTIMESTAMP,
                   p_effective_start_timestamp,
                   new_version.one_click_allowed,
                   new_version.one_click_enabled,
                   new_version.one_click_style,
                   new_version.daily_statement_requested,
                   new_version.monthly_statement_requested,
                   new_version.yearly_statement_requested,
                   new_version.is_trade_confirm_email_req,
		/*
		new_version.is_suspend_all_alerts,
		new_version.suspend_alerts_from,
		new_version.suspend_alerts_to,*/ --DR
                   new_version.notification_email,
                   new_version.notification_sms);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --
        SELECT *
        BULK COLLECT INTO ltab_updated_ptp
        FROM person_trading_profiles ptp
        WHERE ptp.person_id = p_person_id
          AND NOT EXISTS
               (SELECT 1
                 FROM TABLE(CAST(p_person_trading_profiles AS person_trading_profiles_tab)) new_ptp
                  WHERE ptp.trading_account_id = new_ptp.trading_account_id
                   AND ptp.Person_Id = p_person_id)
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_ptp.COUNT LOOP

          put_history(p_old_ptp => ltab_updated_ptp(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D');

        END LOOP;

        FORALL lv_count IN 1..ltab_updated_ptp.COUNT
          DELETE  person_trading_profiles
          WHERE person_id = ltab_updated_ptp(lv_count).person_id AND
                trading_account_id = ltab_updated_ptp(lv_count).trading_account_id;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (pte_tab 3) */
               p_person_id,
               ptp_tab.trading_account_id,
               'CUSTOMER',
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               ptp_tab.one_click_allowed,
               ptp_tab.one_click_enabled ,
               ptp_tab.one_click_style,
               ptp_tab.daily_statement_requested,
               ptp_tab.monthly_statement_requested,
               ptp_tab.yearly_statement_requested,
               ptp_tab.is_trade_confirm_email_req,
              null,
		null,
		null,
		/*
		ptp_tab.is_suspend_all_alerts,
		ptp_tab.suspend_alerts_from,
		ptp_tab.suspend_alerts_to,
		*/ --DR
               ptp_tab.notification_email,
               ptp_tab.notification_sms
        BULK COLLECT INTO ltab_updated_ptp
        FROM   TABLE(CAST(p_person_trading_profiles AS person_trading_profiles_tab)) ptp_tab
        WHERE  --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
               (one_click_allowed,
                one_click_enabled,
                one_click_style,
                daily_statement_requested,
                monthly_statement_requested,
                yearly_statement_requested,
                is_trade_confirm_email_req,
		null,
		null,
		null,
		/*              is_suspend_all_alerts,
		suspend_alerts_from,
		suspend_alerts_to, */ --DR
                notification_email,
                notification_sms) NOT IN
                                    (SELECT one_click_allowed,
                                            one_click_enabled,
                                            one_click_style,
                                            daily_statement_requested,
                                            monthly_statement_requested,
                                            yearly_statement_requested,
                                            is_trade_confirm_email_req,
                                            null,
					null,
					null,
				       /* is_suspend_all_alerts,
					suspend_alerts_from,
                                            suspend_alerts_to, */--DR
                                            notification_email,
                                            notification_sms
                                     FROM person_trading_profiles
                                     WHERE  person_id = p_person_id AND
                                            effective_start_timestamp <= p_effective_start_timestamp
                                     UNION ALL
                                     SELECT one_click_allowed,
                                            one_click_enabled,
                                            one_click_style,
                                            daily_statement_requested,
                                            monthly_statement_requested,
                                            yearly_statement_requested,
                                            is_trade_confirm_email_req,
						null,
						null,
						null,
						/*
						is_suspend_all_alerts,
						suspend_alerts_from,
						suspend_alerts_to,*/--DR
                                            notification_email,
                                            notification_sms
                                     FROM person_trading_profiles_h
                                     WHERE  person_id = p_person_id AND
                                            effective_start_timestamp <= p_effective_start_timestamp AND
                                            effective_end_timestamp > p_effective_start_timestamp);

        FOR lv_count IN 1..ltab_updated_ptp.COUNT LOOP
          put_history(p_old_ptp => ltab_updated_ptp(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I');

        END LOOP;
    END CASE;

    ------------------------------------------------
    ---
    --- Populate person_stmnt_email_address table
    ---
    -----------------------------------------------


       FOR i IN 1..p_person_trading_profiles.count LOOP

            put_person_stmt_email_address(p_user                           => p_user,
                                          p_logical_load_timestamp         => p_logical_load_timestamp,
                                          p_effective_start_timestamp      => p_effective_start_timestamp,
                                          p_person_id                      => p_person_id,
                                          p_trading_account_id             => p_person_trading_profiles(i).trading_account_id,
                                          p_trading_account_type           => 'CUSTOMER',
                                          p_person_stmnt_email_addr_tab    => p_person_trading_profiles(i).person_stmnt_email_address);

       END LOOP;

       FOR i IN 1..p_person_trading_profiles.count LOOP
         put_trd_cnfrm_eml_addrss(p_user                      => p_user,
                                p_logical_load_timestamp    => p_logical_load_timestamp,
                                p_effective_start_timestamp => p_effective_start_timestamp,
                                p_lv_effective_start_time   => p_lv_effective_start_time,
                                p_person_id                 => p_person_id,
                                p_trading_account_id        => p_person_trading_profiles(i).trading_account_id,
                                p_trading_account_type      => 'CUSTOMER',
                                p_prsn_trd_cnfrm_email_address=> p_person_trading_profiles(i).prsn_trd_cnfrm_email_addrss);
       END LOOP;

	   /* --DR
       FOR i IN 1..p_person_trading_profiles.count LOOP
       put_alert_preferences  (p_user                      => p_user,
                               p_logical_load_timestamp    => p_logical_load_timestamp,
                               p_effective_start_timestamp => p_effective_start_timestamp,
                               p_lv_effective_start_time   => p_lv_effective_start_time,
                               p_person_id                 => p_person_id,
                               p_trading_account_id        => p_person_trading_profiles(i).trading_account_id,
                               p_trading_account_type      => 'CUSTOMER',
                               p_alert_preferences         => p_person_trading_profiles(i).alert_preferences);
       END LOOP;
      */
    END put_person_trading_profiles;

  -- ===================================================================================
  -- put_person_mifid_idntfctns
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put person mifid identifcation records
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     person_mifid_idntfctns
  --     person_mifid_idntfctns_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     person_mifid_idntfctns_tab       person_mifid_idntfctns tab
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_person_mifid_idntfctns (p_user                      persons.created_by%TYPE,
                                        p_logical_load_timestamp    person_mifid_idntfctns.logical_load_timestamp%TYPE,
                                        p_effective_start_timestamp person_mifid_idntfctns.effective_start_timestamp%TYPE,
                                        p_lv_effective_start_time   person_mifid_idntfctns.effective_start_timestamp%TYPE,
                                        p_person_id                 persons.person_id%TYPE,
                                        p_person_mifid_idntfctns    person_mifid_idntfctns_tab)
  IS
  TYPE ltyp_updated_pmi IS TABLE OF person_mifid_idntfctns%rowtype;

  ltab_updated_pmi     ltyp_updated_pmi;
  ltab_updated_pmi_old ltyp_updated_pmi;

  BEGIN
    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO person_mifid_idntfctns (person_id,
                                            mifir_identification_type,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            identification,
                                            document_expiry_date,
                                            priority,
                                            is_verified,
                                            is_issued
                                            )
                                          SELECT p_person_id,
                                                 pmi_tab.mifir_identification_type,
                                                 p_logical_load_timestamp,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_effective_start_timestamp,
                                                 pmi_tab.identification,
                                                 pmi_tab.document_expiry_date,
                                                 pmi_tab.priority,
                                                 pmi_tab.is_verified,
                                                 pmi_tab.is_issued
                                          FROM   TABLE(CAST(p_person_mifid_idntfctns AS person_mifid_idntfctns_tab)) pmi_tab
                                          WHERE  NOT EXISTS (SELECT 1
                                                            FROM person_mifid_idntfctns old_tab
                                                            WHERE old_tab.mifir_identification_type = pmi_tab.mifir_identification_type
                                                             AND old_tab.Person_Id = p_person_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN

        --
        -- Update the data that has got updated as compared to the previous version
        --
        SELECT /*+ CARDINALITY (pmi_tab 3) */
               pmi.person_id,
               pmi.mifir_identification_type,
               pmi.logical_load_timestamp,
               pmi.Created_By,
               pmi.create_timestamp,
               pmi.updated_by,
               pmi.update_timestamp,
               pmi.effective_start_timestamp,
               pmi.identification,
               pmi.document_expiry_date,
               pmi.priority,
               pmi.is_verified,
               pmi.is_issued
        BULK COLLECT INTO ltab_updated_pmi_old
        FROM   person_mifid_idntfctns pmi, TABLE(CAST(p_person_mifid_idntfctns AS person_mifid_idntfctns_tab)) pmi_tab
        WHERE   pmi.mifir_identification_type = pmi_tab.mifir_identification_type AND
                pmi.Person_Id = p_person_id AND
               (nrg_common.has_value_changed(pmi.identification,pmi_tab.identification) =1 OR
                nrg_common.has_value_changed(pmi.document_expiry_date,pmi_tab.document_expiry_date) =1 OR
                nrg_common.has_value_changed(pmi.priority,pmi_tab.priority) =1 OR
                nrg_common.has_value_changed(pmi.is_verified,pmi_tab.is_verified) =1 OR
                nrg_common.has_value_changed(pmi.is_issued,pmi_tab.is_issued) =1)
        FOR UPDATE OF pmi.mifir_identification_type;

        FOR lv_count IN 1..ltab_updated_pmi_old.COUNT LOOP

          put_history(p_old_pmi => ltab_updated_pmi_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'U');

        END LOOP;

        MERGE /*+ CARDINALITY (new_version 3) */INTO person_mifid_idntfctns old_version
        USING (SELECT *
               FROM TABLE(CAST(p_person_mifid_idntfctns AS person_mifid_idntfctns_tab))) new_version
        ON (old_version.mifir_identification_type = new_version.mifir_identification_type AND
            old_version.Person_Id = p_person_id)
        WHEN MATCHED THEN
          UPDATE
          SET    updated_by = p_user,
                 update_timestamp = SYSTIMESTAMP,
                 effective_start_timestamp = p_effective_start_timestamp,
                 logical_load_timestamp = p_logical_load_timestamp,
                 identification = new_version.identification,
                 document_expiry_date = new_version.document_expiry_date,
                 priority = new_version.priority,
                 is_verified = new_version.is_verified,
                 is_issued = new_version.is_issued
          WHERE (nrg_common.has_value_changed(old_version.identification,new_version.identification) =1 OR
                 nrg_common.has_value_changed(old_version.document_expiry_date,new_version.document_expiry_date) =1 OR
                 nrg_common.has_value_changed(old_version.priority,new_version.priority) =1 OR
                 nrg_common.has_value_changed(old_version.is_verified,new_version.is_verified) =1 OR
                 nrg_common.has_value_changed(old_version.is_issued,new_version.is_issued) =1)
        WHEN NOT MATCHED THEN
          INSERT (person_id,
                  mifir_identification_type,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  identification,
                  document_expiry_date,
                  priority,
                  is_verified,
                  is_issued)
           VALUES (p_person_id,
                   new_version.mifir_identification_type,
                   p_logical_load_timestamp,
                   p_user,
                   SYSTIMESTAMP,
                   p_user,
                   SYSTIMESTAMP,
                   p_effective_start_timestamp,
                   new_version.identification,
                   new_version.document_expiry_date,
                   new_version.priority,
                   new_version.is_verified,
                   new_version.is_issued);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --
        SELECT *
        BULK COLLECT INTO ltab_updated_pmi
        FROM person_mifid_idntfctns pmi
        WHERE pmi.person_id = p_person_id
          AND NOT EXISTS
               (SELECT mifir_identification_type
                 FROM TABLE(CAST(p_person_mifid_idntfctns AS person_mifid_idntfctns_tab)) new_pmi
                  WHERE pmi.mifir_identification_type = new_pmi.mifir_identification_type
                  AND  pmi.Person_Id = p_person_id )
        FOR UPDATE;

        FOR lv_count IN 1..ltab_updated_pmi.COUNT LOOP

          put_history(p_old_pmi => ltab_updated_pmi(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D');

        END LOOP;

        FORALL lv_count IN 1..ltab_updated_pmi.COUNT
          DELETE  person_mifid_idntfctns
          WHERE person_id = ltab_updated_pmi(lv_count).person_id AND
                mifir_identification_type = ltab_updated_pmi(lv_count).mifir_identification_type;


      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN
        --
        -- Process out of sequence data/replayed messages
        --
        SELECT /*+ CARDINALITY (pmi_tab 3) */
               p_person_id,
               pmi_tab.mifir_identification_type,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               pmi_tab.identification,
               pmi_tab.document_expiry_date,
               pmi_tab.priority,
               pmi_tab.is_verified,
               pmi_tab.is_issued
        BULK COLLECT INTO ltab_updated_pmi
        FROM   TABLE(CAST(p_person_mifid_idntfctns AS person_mifid_idntfctns_tab)) pmi_tab
        WHERE  --
               -- We need to exclude any data that entering the history where the non-key columns
               -- have not changed and an effective period already encompasses the incoming effective_start_timestamp.
               -- This scenario can occur processing out of sequence publication/load records i.e. CMS/AMS bug
               -- and replaying exising messages
               --
                  (identification,
                   document_expiry_date,
                   priority,
                   is_verified,
                   is_issued ) NOT IN (SELECT identification,
                                              document_expiry_date,
                                              priority,
                                              is_verified,
                                              is_issued
                                         FROM person_mifid_idntfctns
                                        WHERE person_id = p_person_id
                                          AND effective_start_timestamp <= p_effective_start_timestamp
                                        UNION ALL
                                       SELECT identification,
                                              document_expiry_date,
                                              priority,
                                              is_verified,
                                              is_issued
                                         FROM person_mifid_idntfctns_h
                                        WHERE person_id = p_person_id
                                          AND effective_start_timestamp <= p_effective_start_timestamp
                                          AND effective_end_timestamp > p_effective_start_timestamp);

        FOR lv_count IN 1..ltab_updated_pmi.COUNT LOOP
          put_history(p_old_pmi => ltab_updated_pmi(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'I');

        END LOOP;
    END CASE;
    END put_person_mifid_idntfctns;

  -- ===================================================================================
  -- put_companies_persons_link
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put company and person id
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     COMPANIES_PERSONS_LINK
  --     COMPANIES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_PERSON_ID                      Person Id
  --     p_companies_persons_link         Companies id
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  --
  -- This code needs to be re reviewed in case we have a person associated to many companies
  --
/*  PROCEDURE put_companies_persons_link(p_user persons.created_by%TYPE,
                                       p_logical_load_timestamp persons.logical_load_timestamp%TYPE,
                                       p_effective_start_timestamp persons.effective_start_timestamp%TYPE,
                                       p_lv_effective_start_time persons.effective_start_timestamp%TYPE,
                                       p_person_id persons.person_id%TYPE,
                                       p_companies_persons_link companies_persons_link_tab)
  IS

  TYPE ltyp_updated_com_per IS TABLE OF companies_persons_link%rowtype;

  lvtab_updated_com_per ltyp_updated_com_per;


  BEGIN
    nrg_company.create_company_stub   (p_user                        => p_user,
                                       p_logical_load_timestamp      => p_logical_load_timestamp,
                                       p_effective_start_timestamp   => p_effective_start_timestamp,
                                       p_company_id                  => p_company_ids);

    CASE
      WHEN p_lv_effective_start_time IS NULL THEN
        INSERT INTO companies_persons_link
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              person_id,
                                              company_id,
                                              relationship_type,
                                              relationship_id,
                                              relationship_version,
                                              job_title,
                                              shareholding_id,
                                              shareholding_version,
                                              shareholding_percentage,
                                              shareholding_ubo_percentage,
                                              is_key_contact
                                          )
                                          SELECT  p_user,
                                                  SYSTIMESTAMP,
                                                  p_user,
                                                  SYSTIMESTAMP,
                                                  p_logical_load_timestamp,
                                                  p_effective_start_timestamp,
                                                  p_person_id,
                                                  company_id,
                                                  relationship_type,
                                                  relationship_id,
                                                  relationship_version,
                                                  job_title,
                                                  shareholding_id,
                                                  shareholding_version,
                                                  shareholding_percentage,
                                                  shareholding_ubo_percentage,
                                                  is_key_contact
                                          FROM   TABLE(CAST(p_companies_persons_link AS companies_persons_link_tab)) com_per_tab
                                          WHERE  NOT EXISTS (SELECT 1
                                                            FROM companies_persons_link
                                                            WHERE person_id = p_person_id AND
                                                                  company_id = com_per_tab.company_id);

      WHEN p_effective_start_timestamp >= p_lv_effective_start_time THEN
        --
        -- Update the data that has got updated as compared to the previous version
        --

        SELECT com_per.company_id,
               com_per.person_id,
               com_per_tab.relationship_type,
               p_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               com_per_tab.relationship_id,
               com_per_tab.relationship_version,
               com_per_tab.job_title,
               com_per_tab.shareholding_id,
               com_per_tab.shareholding_version,
               com_per_tab.shareholding_percentage,
               com_per_tab.shareholding_ubo_percentage,
               com_per_tab.is_key_contact
        BULK COLLECT INTO lvtab_updated_com_per
        FROM    companies_persons_link com_per, TABLE(CAST(p_companies_persons_link AS companies_persons_link_tab)) com_per_tab
        WHERE  com_per.person_id = p_person_id AND
               com_per.company_id = com_per_tab.company_id AND
               (com_per.relationship_type != com_per_tab.relationship_type OR
                com_per.relationship_id != com_per_tab.relationship_id OR
                com_per.relationship_version != com_per_tab.relationship_version OR
                com_per.job_title != com_per_tab.job_title OR
                com_per.shareholding_id != com_per_tab.shareholding_id OR
                com_per.shareholding_version != com_per_tab.shareholding_version OR
                com_per.shareholding_percentage != com_per_tab.shareholding_percentage OR
                com_per.shareholding_ubo_percentage != com_per_tab.shareholding_ubo_percentage OR
                com_per.is_key_contact != com_per_tab.is_key_contact);

        FOR lv_count IN 1..lvtab_updated_com_per.count loop
          UPDATE companies_persons_link
          SET    created_by = lvtab_updated_com_per(lv_count).created_by,
                 create_timestamp = lvtab_updated_com_per(lv_count).create_timestamp,
                 updated_by = lvtab_updated_com_per(lv_count).updated_by,
                 update_timestamp = lvtab_updated_com_per(lv_count).update_timestamp,
                 logical_load_timestamp = lvtab_updated_com_per(lv_count).logical_load_timestamp,
                 effective_start_timestamp = lvtab_updated_com_per(lv_count).effective_start_timestamp,
                 relationship_type = lvtab_updated_com_per(lv_count).relationship_type,
                 relationship_id = lvtab_updated_com_per(lv_count).relationship_id,
                 relationship_version = lvtab_updated_com_per(lv_count).relationship_version,
                 job_title = lvtab_updated_com_per(lv_count).job_title,
                 shareholding_id = lvtab_updated_com_per(lv_count).shareholding_id,
                 shareholding_version = lvtab_updated_com_per(lv_count).shareholding_version,
                 shareholding_percentage = lvtab_updated_com_per(lv_count).shareholding_percentage,
                 shareholding_ubo_percentage = lvtab_updated_com_per(lv_count).shareholding_ubo_percentage,
                 is_key_contact = lvtab_updated_com_per(lv_count).is_key_contact
          WHERE  person_id = p_person_id AND
                 company_id = lvtab_updated_com_per(lv_count).company_id;
        END LOOP;

        --
    -- Insert the new person telephone number data
    --

    INSERT INTO companies_persons_link
                                          (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              person_id,
                                              company_id,
                                              relationship_type,
                                              relationship_id,
                                              relationship_version,
                                              job_title,
                                              shareholding_id,
                                              shareholding_version,
                                              shareholding_percentage,
                                              shareholding_ubo_percentage,
                                              is_key_contact
                                          )
                                          SELECT p_user,
                                                 SYSTIMESTAMP,
                                                 p_user,
                                                 SYSTIMESTAMP,
                                                 p_logical_load_timestamp,
                                                 p_effective_start_timestamp,
                                                 p_person_id,
                                                 company_id,
                                                 relationship_type,
                                                 relationship_id,
                                                 relationship_version,
                                                 job_title,
                                                 shareholding_id,
                                                 shareholding_version,
                                                 shareholding_percentage,
                                                 shareholding_ubo_percentage,
                                                 is_key_contact
                                          FROM   TABLE(CAST(p_companies_persons_link AS companies_persons_link_tab)) com_per_tab
                                          WHERE  NOT EXISTS (SELECT 1
                                                            FROM companies_persons_link
                                                            WHERE person_id = p_person_id AND
                                                                  company_id = com_per_tab.company_id);

        --
        -- Delete the data that has got deleted in this version as compare to the previous version
        --

        DELETE companies_persons_link
        WHERE person_id = p_person_id AND
              company_id NOT IN (SELECT company_id
                                 FROM   TABLE(CAST(p_companies_persons_link AS companies_persons_link_tab)));

      WHEN p_effective_start_timestamp < p_lv_effective_start_time THEN

        INSERT INTO companies_persons_link_h
                                            (
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              logical_load_timestamp,
                                              effective_start_timestamp,
                                              effective_end_timestamp,
                                              action,
                                              action_timestamp,
                                              person_id,
                                              company_id,
                                              relationship_type,
                                              relationship_id,
                                              relationship_version,
                                              job_title,
                                              shareholding_id,
                                              shareholding_version,
                                              shareholding_percentage,
                                              shareholding_ubo_percentage,
                                              is_key_contact
                                            )
                                            SELECT p_user,
                                                   SYSTIMESTAMP,
                                                   p_user,
                                                   SYSTIMESTAMP,
                                                   p_logical_load_timestamp,
                                                   p_effective_start_timestamp,
                                                   p_effective_start_timestamp + 1/(24*60*60),
                                                   'I',
                                                   SYSTIMESTAMP,
                                                   p_person_id,
                                                   company_id,
                                                   relationship_type,
                                                   relationship_id,
                                                   relationship_version,
                                                   job_title,
                                                   shareholding_id,
                                                   shareholding_version,
                                                   shareholding_percentage,
                                                   shareholding_ubo_percentage,
                                                   is_key_contact
                                            FROM   TABLE(CAST(p_companies_persons_link AS companies_persons_link_tab)) com_per_tab
                                            WHERE  (relationship_type, relationship_id, relationship_version, job_title, shareholding_id,
                                                    shareholding_version, shareholding_percentage, shareholding_ubo_percentage, is_key_contact) NOT IN
                                                                                                                                                (SELECT  relationship_type, relationship_id, relationship_version, job_title, shareholding_id,
                                                                                                                                                         shareholding_version, shareholding_percentage, shareholding_ubo_percentage, is_key_contact
                                                                                                                                                 FROM    companies_persons_link
                                                                                                                                                 WHERE   person_id = p_person_id AND
                                                                                                                                                         effective_start_timestamp <= p_effective_start_timestamp
                                                                                                                                                 UNION ALL
                                                                                                                                                 SELECT relationship_type, relationship_id, relationship_version, job_title, shareholding_id,
                                                                                                                                                        shareholding_version, shareholding_percentage, shareholding_ubo_percentage, is_key_contact
                                                                                                                                                 FROM   companies_persons_link_h
                                                                                                                                                 WHERE  person_id = p_person_id AND
                                                                                                                                                        effective_start_timestamp <= p_effective_start_timestamp AND
                                                                                                                                                        effective_end_timestamp > p_effective_start_timestamp);
    END CASE;

  end put_companies_persons_link;
*/

  -- ===================================================================================
  -- PUBLIC MODULES
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION VERSION
    RETURN VARCHAR2 deterministic
  IS

  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END VERSION;

  -- ===================================================================================
  -- create_person_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a person stub in case the person is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE create_person_stub  (p_user                       IN persons.created_by%TYPE,
                                 p_logical_load_timestamp     IN persons.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp  IN persons.effective_start_timestamp%TYPE,
                                 p_person_id                  IN persons.person_id%TYPE)
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
    INSERT INTO persons (person_id,
                         logical_load_timestamp,
                         created_by,
                         create_timestamp,
                         updated_by,
                         update_timestamp,
                         effective_start_timestamp,
             record_source
                        )
                 VALUES (
                         p_person_id,
                         p_logical_load_timestamp,
                         p_user,
                         SYSTIMESTAMP,
                         p_user,
                         SYSTIMESTAMP,
                         gc_default_timestamp,
             'CM'
                        );
    COMMIT;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      --
      --Since person record already exists no need to create stub
      --
      NULL;
  END create_person_stub;

  -- ===================================================================================
  -- create_person_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a person stub in case the person is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id Table
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE create_person_stub  (p_user                       IN persons.created_by%TYPE,
                                 p_logical_load_timestamp     IN persons.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp  IN persons.effective_start_timestamp%TYPE,
                                 p_person_id                  person_id_tab)
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
    INSERT INTO persons (
                          person_id,
                          logical_load_timestamp,
                          created_by,
                          create_timestamp,
                          updated_by,
                          update_timestamp,
                          effective_start_timestamp,
              record_source
                        )
                        SELECT person_id,
                               p_logical_load_timestamp,
                               p_user,
                               SYSTIMESTAMP,
                               p_user,
                               SYSTIMESTAMP,
                               gc_default_timestamp,
                 'CM'
                          FROM TABLE(CAST(p_person_id AS person_id_tab)) pers
                          WHERE NOT EXISTS (SELECT 1
                                              FROM persons
                                             WHERE person_id = pers.person_id);
    COMMIT;
  END create_person_stub;

  -- ===================================================================================
  -- put_person
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a person
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PERSONS
  --     PERSONS_H
  --     PERSON_ADDRESSES
  --     PERSON_ADDRESSES_H
  --     PERSON_ALIASES
  --     PERSON_ALIASES_H
  --     PERSON_EMAIL_ADDRESSES
  --     PERSON_EMAIL_ADDRESSES_H
  --     PERSON_NATIONAL_IDENTIFIERS
  --     PERSON_NATIONAL_IDENTIFIERS_H
  --     PERSON_RELATIONSHIPS
  --     PERSON_RELATIONSHIPS_H
  --     PERSON_TELEPHONE_NUMBERS
  --     PERSON_TELEPHONE_NUMBERS_H
  --     PRSN_CMPLNC_EML_ADDRSSSS
  --     PRSN_CMPLNC_EML_ADDRSSSS_H
  --     CUSTOMERS_PERSONS_LINK
  --     CUSTOMERS_PERSONS_LINK_H
  --     POWER_OF_ATTORNEYS
  --     POWER_OF_ATTORNEYS_H
  --     PERSON_EMPLOYMENT_DETAILS
  --     PERSON_EMPLOYMENT_DETAILS_H
  --     PERSON_FNNCL_DTL_SRCS
  --     PERSON_FNNCL_DTL_SRCS_H
  --     PERSON_TRADING_PROFILES
  --     PERSON_TRADING_PROFILES_H
  --     PERSON_TRADING_EXPERIENCE
  --     PERSON_TRADING_EXPERIENCE_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_person_id                      Person Id
  --     p_person_version                 Person Version
  --     p_title                          Title
  --     p_first_name                     First Name
  --     p_last_name                      Last Name
  --     p_gender                         Gender
  --     p_date_of_birth                  Date Of Birth
  --     p_nationality                    Nationality
  --     p_spoken_language                Spoken Language
  --     p_exchange_data_classification   Exchange Data Classification
  --     p_remediation_date               Remediation date
  --     p_financial_detail_id            Financial Detail Id
  --     p_origin_of_wealth               Origin Of Wealth
  --     p_origin_of_wealth_detail        Origim Of Wealth Details
  --     p_annual_income_range            Annual Income Range
  --     p_annual_income_currency         Annual Income Currency
  --     p_val_svngs_invstmnt_range       Value Of Savings And Investment Range
  --     p_val_svngs_invstmnt_currency    Value Of Savings And Investment Currency
  --     p_sources_of_funds               Source Of Funds
  --     p_sources_of_income              Source Of Income
  --     p_employment_details             Employment Details
  --     p_person_addresses               Person Addresses
  --     p_person_aliases                 Person Aliases
  --     p_person_email_addresses         Person Email Addresses
  --     p_person_national_identifiers    Person National Identifiers
  --     p_person_relationships           Person Relationships
  --     p_person_telephone_numbers       Person Telephone Numbers
  --     p_customer_ids                   Customer Ids
  --     p_power_of_attorneys             Power of attorneys tab
  --     p_is_deleted                     Is Deleted
  --     p_person_trading_experience      Person Trading Experience tab
  --     p_initial_lead_channel           Initial Lead Channel
  --     p_person_trading_profiles        Person Trading Profiles tab
  --     p_record_source                  Record Source
  --     p_source_person_id               Source Person Id

  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Person Deleted Before Update and After Insert
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_person (p_user                         IN persons.created_by%TYPE,
                        p_effective_start_timestamp    IN persons.effective_start_timestamp%TYPE,
                        p_person_id                    IN persons.person_id%TYPE,
                        p_person_version               IN persons.person_version%TYPE,
                        p_title                        IN persons.title%TYPE,
                        p_first_name                   IN persons.first_name%TYPE,
                        p_last_name                    IN persons.last_name%TYPE,
                        p_gender                       IN persons.gender%TYPE,
                        p_date_of_birth                IN persons.date_of_birth%TYPE,
                        p_nationality                  IN persons.nationality%TYPE,
                        p_spoken_language              IN persons.spoken_language%TYPE,
                        p_exchange_data_classification IN persons.exchange_data_classification%TYPE,
                        p_financial_detail_id          IN persons.financial_detail_id%TYPE,
                        p_origin_of_wealth             IN persons.origin_of_wealth%TYPE,
                        p_origin_of_wealth_detail      IN persons.origin_of_wealth_detail%TYPE,
                        p_annual_income_range          IN persons.annual_income_range%TYPE,
                        p_annual_income_currency       IN persons.annual_income_currency%TYPE,
                        p_val_svngs_invstmnt_range     IN persons.val_svngs_invstmnt_range%TYPE,
                        p_val_svngs_invstmnt_currency  IN persons.val_svngs_invstmnt_currency%TYPE,
                        p_sources_of_funds             IN person_fnncl_stl_srcs_tab,
                        p_sources_of_income            IN person_fnncl_stl_srcs_tab,
                        p_employment_details           IN employment_detail_tab,
                        p_person_addresses             IN person_addresses_tab,
                        p_person_aliases               IN person_aliases_tab,
                        p_person_email_addresses       IN person_email_addresses_tab,
                        p_person_national_identifiers  IN person_national_identif_tab,
                        p_person_relationships         IN person_relationships_tab,
                        p_person_telephone_numbers     IN person_telephone_numbers_tab,
                        p_customer_ids                 IN customer_id_tab,
                        p_power_of_attorneys           IN power_of_attorney_tab,
                        p_is_deleted                   IN persons.is_deleted%TYPE,
                        p_person_trading_experience    IN person_trading_experience_tab,
                        p_initial_lead_channel         IN persons.Initial_Lead_Channel%TYPE,
                        p_person_trading_profiles      IN person_trading_profiles_tab,
                        p_us_citizen                   IN persons.us_citizen%TYPE,
                        p_marital_status               IN persons.marital_status%TYPE,
                        p_market_counterpart_partnr_id IN persons.market_counterparty_partner_id%TYPE,
                        p_middle_name                  IN persons.middle_name%TYPE,
                        p_is_politically_exposed       IN persons.is_politically_exposed%TYPE,
                        p_politically_exposed_details  IN persons.politically_exposed_details%TYPE,
                        p_financial_detail_version     IN persons.financial_detail_version%TYPE,
                        p_investment_portfolio         IN persons.investment_portfolio%TYPE,
                        p_investment_portfolio_ccy     IN persons.investment_portfolio_currency%TYPE,
                        p_liquid_assets                IN persons.liquid_assets%TYPE,
                        p_liquid_assets_currency       IN persons.liquid_assets_currency%TYPE,
                        p_cumulative_risk_limit        IN persons.cumulative_risk_limit%TYPE,
                        p_cumulative_risk_limit_ccy    IN persons.cumulative_risk_limit_currency%TYPE,
                        p_record_source                IN persons.record_source%TYPE DEFAULT 'CM',
                        p_source_person_id             IN persons.source_person_id%TYPE,
                        p_exchng_data_clssfctn_cnfrmd  IN persons.exchng_data_clssfctn_cnfrmd%TYPE,
                        p_is_accredited_investor       IN persons.is_accredited_investor%TYPE,
                        p_is_related_to_regulated_firm IN persons.is_related_to_regulated_firm%TYPE,
                        p_is_insider_of_public_company IN persons.is_insider_of_public_company%TYPE,
                        p_person_mifid_idntfctns       IN person_mifid_idntfctns_tab,
                        p_first_name_latin             IN persons.first_name_latin%TYPE,
                        p_last_name_latin              IN persons.last_name_latin%TYPE,
                        p_nationality_country_code     IN persons.nationality_country_code%TYPE,
                        p_is_mifid_details_remediated  IN persons.is_mifid_details_remediated%TYPE,
                        p_mkt_cpty_rgltry_clssfctn     IN persons.mkt_cpty_rgltry_clssfctn%TYPE,
                        p_country_of_birth             IN persons.country_of_birth%TYPE
                        )
  IS
    lv_logical_load_timestamp    persons.logical_load_timestamp%TYPE;
    lv_effective_start_timestamp persons.effective_start_timestamp%TYPE;
    lv_old_person                persons%ROWTYPE;

    lex_unknown_operation_type   EXCEPTION;
    lex_person_not_found         EXCEPTION;
  BEGIN

    logger.logger.set_module('put_person');

    -- set the logical load timestamp to now

    lv_logical_load_timestamp := SYSTIMESTAMP;

    -- PERSONS

    BEGIN


      --
      --Insert the person
      --
      INSERT INTO persons
                         (
                            created_by,
                            create_timestamp,
                            updated_by,
                            update_timestamp,
                            logical_load_timestamp,
                            effective_start_timestamp,
                            person_id,
                            person_version,
                            title,
                            first_name,
                            last_name,
                            gender,
                            date_of_birth,
                            nationality,
                            spoken_language,
                            exchange_data_classification,
                            is_deleted,
                            financial_detail_id,
                            origin_of_wealth,
                            origin_of_wealth_detail,
                            annual_income_range,
                            annual_income_currency,
                            val_svngs_invstmnt_range,
                            val_svngs_invstmnt_currency,
                            initial_lead_channel,
                            us_citizen,
                            marital_status,
                            market_counterparty_partner_id,
                            middle_name,
                            is_politically_exposed,
                            politically_exposed_details,
                            financial_detail_version,
                            investment_portfolio,
                            investment_portfolio_currency,
                            liquid_assets,
                            liquid_assets_currency,
                            cumulative_risk_limit,
                            cumulative_risk_limit_currency,
                            record_source,
                            source_person_id,
                            exchng_data_clssfctn_cnfrmd,
                            is_accredited_investor,
                            is_related_to_regulated_firm,
                            is_insider_of_public_company,
                            first_name_latin,
                            last_name_latin,
                            nationality_country_code,
                            is_mifid_details_remediated,
                            mkt_cpty_rgltry_clssfctn,
                            country_of_birth
                            )
                          VALUES
                          (
                            p_user,
                            SYSTIMESTAMP,
                            p_user,
                            SYSTIMESTAMP,
                            lv_logical_load_timestamp,
                            p_effective_start_timestamp,
                            p_person_id,
                            p_person_version,
                            p_title,
                            p_first_name,
                            p_last_name,
                            p_gender,
                            p_date_of_birth,
                            p_nationality,
                            p_spoken_language,
                            p_exchange_data_classification,
                            p_is_deleted,
                            p_financial_detail_id,
                            p_origin_of_wealth,
                            p_origin_of_wealth_detail,
                            p_annual_income_range,
                            p_annual_income_currency,
                            p_val_svngs_invstmnt_range,
                            p_val_svngs_invstmnt_currency,
                            p_initial_lead_channel,
                            p_us_citizen,
                            p_marital_status,
                            p_market_counterpart_partnr_id,
                            p_middle_name,
                            p_is_politically_exposed,
                            p_politically_exposed_details,
                            p_financial_detail_version,
                            p_investment_portfolio,
                            p_investment_portfolio_ccy,
                            p_liquid_assets,
                            p_liquid_assets_currency,
                            p_cumulative_risk_limit,
                            p_cumulative_risk_limit_ccy,
                            p_record_source,
                            p_source_person_id,
                            p_exchng_data_clssfctn_cnfrmd,
                            p_is_accredited_investor,
                            p_is_related_to_regulated_firm,
                            p_is_insider_of_public_company,
                            p_first_name_latin,
                            p_last_name_latin,
                            p_nationality_country_code,
                            p_is_mifid_details_remediated,
                            p_mkt_cpty_rgltry_clssfctn,
                            p_country_of_birth
                            );
      lv_effective_start_timestamp := null;
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
      -- look up and lock a PERSON by PK
        BEGIN
          SELECT prsn.*
          INTO   lv_old_person
          FROM   persons prsn
          WHERE  person_id = p_person_id
          FOR UPDATE;
        EXCEPTION
          WHEN no_data_found THEN
            RAISE lex_person_not_found;
        END;
        lv_effective_start_timestamp := lv_old_person.effective_start_timestamp;
    END;

    CASE
      WHEN lv_effective_start_timestamp IS NULL THEN
        --
        --Since the trade is already inserted no operation will be performed
        --
        NULL;

      WHEN lv_effective_start_timestamp IS NOT NULL AND lv_effective_start_timestamp <= p_effective_start_timestamp THEN

        -- the person exists and its effective start timestamp is
        -- earlier than the new effective start timestamp so update only
        -- where there has been some data change, otherwise do nothing

        UPDATE persons
        SET    updated_by                   = p_user,
               update_timestamp             = SYSTIMESTAMP,
               logical_load_timestamp       = lv_logical_load_timestamp,
               effective_start_timestamp    = p_effective_start_timestamp,
               person_version               = p_person_version,
               title                        = p_title,
               first_name                   = p_first_name,
               last_name                    = p_last_name,
               gender                       = p_gender,
               date_of_birth                = p_date_of_birth,
               nationality                  = p_nationality,
               spoken_language              = p_spoken_language,
               exchange_data_classification = p_exchange_data_classification,
               is_deleted                   = p_is_deleted,
               financial_detail_id          = p_financial_detail_id,
               origin_of_wealth             = p_origin_of_wealth,
               origin_of_wealth_detail      = p_origin_of_wealth_detail,
               annual_income_range          = p_annual_income_range,
               annual_income_currency       = p_annual_income_currency,
               val_svngs_invstmnt_range     = p_val_svngs_invstmnt_range,
               val_svngs_invstmnt_currency  = p_val_svngs_invstmnt_currency,
               initial_lead_channel         = p_initial_lead_channel,
               us_citizen                   = p_us_citizen,
               marital_status               = p_marital_status,
               market_counterparty_partner_id = p_market_counterpart_partnr_id,
               middle_name                  = p_middle_name,
               is_politically_exposed       = p_is_politically_exposed,
               politically_exposed_details  = p_politically_exposed_details,
               financial_detail_version      = p_financial_detail_version,
               investment_portfolio          = p_investment_portfolio,
               investment_portfolio_currency =  p_investment_portfolio_ccy,
               liquid_assets                = p_liquid_assets,
               liquid_assets_currency       = p_liquid_assets_currency,
               cumulative_risk_limit        = p_cumulative_risk_limit,
               cumulative_risk_limit_currency = p_cumulative_risk_limit_ccy,
               record_source                = p_record_source,
               source_person_id             = p_source_person_id,
               exchng_data_clssfctn_cnfrmd  = p_exchng_data_clssfctn_cnfrmd,
               is_accredited_investor       = p_is_accredited_investor,
               is_related_to_regulated_firm = p_is_related_to_regulated_firm,
               is_insider_of_public_company = p_is_insider_of_public_company,
               first_name_latin             = p_first_name_latin,
               last_name_latin              = p_last_name_latin,
               nationality_country_code     = p_nationality_country_code,
               is_mifid_details_remediated  = p_is_mifid_details_remediated,
               mkt_cpty_rgltry_clssfctn     = p_mkt_cpty_rgltry_clssfctn,
               country_of_birth             = p_country_of_birth
        WHERE  person_id = p_person_id AND
               (/* only where there have been for data changes */
                nrg_common.has_value_changed(person_version,p_person_version) = 1 OR
                nrg_common.has_value_changed(title,p_title) = 1 OR
                nrg_common.has_value_changed(first_name,p_first_name) = 1 OR
                nrg_common.has_value_changed(last_name,p_last_name) = 1 OR
                nrg_common.has_value_changed(gender,p_gender) = 1 OR
                nrg_common.has_value_changed(date_of_birth,p_date_of_birth) = 1 OR
                nrg_common.has_value_changed(nationality,p_nationality) = 1 OR
                nrg_common.has_value_changed(spoken_language,p_spoken_language) = 1 OR
                nrg_common.has_value_changed(exchange_data_classification,p_exchange_data_classification) = 1 OR
                nrg_common.has_value_changed(is_deleted,p_is_deleted) = 1 OR
                nrg_common.has_value_changed(financial_detail_id, p_financial_detail_id) = 1 OR
                nrg_common.has_value_changed(origin_of_wealth, p_origin_of_wealth) = 1 OR
                nrg_common.has_value_changed(origin_of_wealth_detail, p_origin_of_wealth_detail) = 1 OR
                nrg_common.has_value_changed(annual_income_range, p_annual_income_range) = 1 OR
                nrg_common.has_value_changed(annual_income_currency, p_annual_income_currency) = 1 OR
                nrg_common.has_value_changed(val_svngs_invstmnt_range, p_val_svngs_invstmnt_range) = 1 OR
                nrg_common.has_value_changed(val_svngs_invstmnt_currency, p_val_svngs_invstmnt_currency) = 1 OR
                nrg_common.has_value_changed(initial_lead_channel, p_initial_lead_channel) = 1 OR
                nrg_common.has_value_changed(us_citizen, p_us_citizen) = 1 OR
                nrg_common.has_value_changed(p_marital_status, marital_status) = 1 OR
                nrg_common.has_value_changed(p_market_counterpart_partnr_id, market_counterparty_partner_id) = 1 OR
                nrg_common.has_value_changed(p_middle_name, middle_name) = 1 OR
                nrg_common.has_value_changed(p_is_politically_exposed, is_politically_exposed) = 1 OR
                nrg_common.has_value_changed(politically_exposed_details, p_politically_exposed_details) = 1 OR
                nrg_common.has_value_changed(financial_detail_version, p_financial_detail_version) = 1 OR
                nrg_common.has_value_changed(investment_portfolio, p_investment_portfolio) = 1 OR
                nrg_common.has_value_changed(investment_portfolio_currency,  p_investment_portfolio_ccy) = 1 OR
                nrg_common.has_value_changed(liquid_assets, p_liquid_assets) = 1 OR
                nrg_common.has_value_changed(liquid_assets_currency, p_liquid_assets_currency) = 1 OR
                nrg_common.has_value_changed(cumulative_risk_limit, p_cumulative_risk_limit) = 1 OR
                nrg_common.has_value_changed(cumulative_risk_limit_currency, p_cumulative_risk_limit_ccy) = 1 OR
                nrg_common.has_value_changed(record_source, p_record_source)= 1 OR
                nrg_common.has_value_changed(source_person_id, p_source_person_id) = 1 OR
                nrg_common.has_value_changed(exchng_data_clssfctn_cnfrmd, p_exchng_data_clssfctn_cnfrmd) = 1 OR
                nrg_common.has_value_changed(is_accredited_investor, p_is_accredited_investor) = 1 OR
                nrg_common.has_value_changed(is_related_to_regulated_firm, p_is_related_to_regulated_firm) = 1 OR
                nrg_common.has_value_changed(is_insider_of_public_company, p_is_insider_of_public_company) = 1 OR
                nrg_common.has_value_changed(first_name_latin, p_first_name_latin) = 1 OR
                nrg_common.has_value_changed(last_name_latin, p_last_name_latin) = 1 OR
                nrg_common.has_value_changed(nationality_country_code, p_nationality_country_code) = 1 OR
                nrg_common.has_value_changed(is_mifid_details_remediated, p_is_mifid_details_remediated) = 1 OR
                nrg_common.has_value_changed(mkt_cpty_rgltry_clssfctn, p_mkt_cpty_rgltry_clssfctn) = 1 OR
                nrg_common.has_value_changed(country_of_birth, p_country_of_birth) = 1
                );

        --
        --If the row has got updated, and was not a stub, then write the history
        --
        IF lv_effective_start_timestamp <> gc_default_timestamp AND SQL%ROWCOUNT > 0 THEN
          put_history(p_old_person_record         => lv_old_person,
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_action                    => 'U');
        END IF;

        lv_old_person := NULL;

      WHEN lv_effective_start_timestamp IS NOT NULL AND lv_effective_start_timestamp > p_effective_start_timestamp THEN
        --
        -- the person exists but its effective start timestamp is
        -- later than the new effective start timestamp so insert a history record
        --
        SELECT p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               lv_logical_load_timestamp,
               p_effective_start_timestamp,
               p_person_id,
               p_person_version,
               p_title,
               p_first_name,
               p_last_name,
               p_gender,
               p_date_of_birth,
               p_nationality,
               p_spoken_language,
               p_exchange_data_classification,
               p_is_deleted,
               p_financial_detail_id,
               p_origin_of_wealth,
               p_origin_of_wealth_detail,
               p_annual_income_range,
               p_annual_income_currency,
               p_val_svngs_invstmnt_range,
               p_val_svngs_invstmnt_currency,
               p_initial_lead_channel,
               p_us_citizen,
               p_marital_status,
               p_market_counterpart_partnr_id,
               p_middle_name,
               p_is_politically_exposed,
               p_politically_exposed_details,
               p_financial_detail_version,
               p_investment_portfolio,
               p_investment_portfolio_ccy ,
               p_liquid_assets,
               p_liquid_assets_currency,
               p_cumulative_risk_limit,
               p_cumulative_risk_limit_ccy,
               p_record_source,
               p_source_person_id,
               p_exchng_data_clssfctn_cnfrmd,
               p_is_accredited_investor,
               p_is_related_to_regulated_firm,
               p_is_insider_of_public_company,
               p_first_name_latin,
               p_last_name_latin,
               p_nationality_country_code,
               p_is_mifid_details_remediated,
               p_mkt_cpty_rgltry_clssfctn,
               p_country_of_birth
        INTO   lv_old_person.created_by,
               lv_old_person.create_timestamp,
               lv_old_person.updated_by,
               lv_old_person.update_timestamp,
               lv_old_person.logical_load_timestamp,
               lv_old_person.effective_start_timestamp,
               lv_old_person.person_id,
               lv_old_person.person_version,
               lv_old_person.title,
               lv_old_person.first_name,
               lv_old_person.last_name,
               lv_old_person.gender,
               lv_old_person.date_of_birth,
               lv_old_person.nationality,
               lv_old_person.spoken_language,
               lv_old_person.exchange_data_classification,
               lv_old_person.is_deleted,
               lv_old_person.financial_detail_id,
               lv_old_person.origin_of_wealth,
               lv_old_person.origin_of_wealth_detail,
               lv_old_person.annual_income_range,
               lv_old_person.annual_income_currency,
               lv_old_person.val_svngs_invstmnt_range,
               lv_old_person.val_svngs_invstmnt_currency,
               lv_old_person.initial_lead_channel,
               lv_old_person.Us_Citizen,
               lv_old_person.marital_status,
               lv_old_person.market_counterparty_partner_id,
               lv_old_person.middle_name,
               lv_old_person.is_politically_exposed,
               lv_old_person.politically_exposed_details,
               lv_old_person.financial_detail_version,
               lv_old_person.investment_portfolio,
               lv_old_person.investment_portfolio_currency,
               lv_old_person.liquid_assets,
               lv_old_person.liquid_assets_currency,
               lv_old_person.cumulative_risk_limit,
               lv_old_person.cumulative_risk_limit_currency,
               lv_old_person.record_source,
               lv_old_person.source_person_id,
               lv_old_person.exchng_data_clssfctn_cnfrmd,
               lv_old_person.is_accredited_investor,
               lv_old_person.is_related_to_regulated_firm,
               lv_old_person.is_insider_of_public_company,
               lv_old_person.first_name_latin,
               lv_old_person.last_name_latin,
               lv_old_person.nationality_country_code,
               lv_old_person.is_mifid_details_remediated,
               lv_old_person.mkt_cpty_rgltry_clssfctn,
               lv_old_person.country_of_birth

        FROM dual;

        put_history(p_old_person_record => lv_old_person,
                    p_effective_end_timestamp => p_effective_start_timestamp,
                    p_action => 'I');

        lv_old_person := NULL;
        ELSE
          RAISE lex_unknown_operation_type;
      END CASE;

        --
        --Create Customer Stub for referential integrity
        --
      IF  p_customer_ids IS NOT NULL THEN
        for i in 1..p_customer_ids.count loop
        nrg_customer.create_customer_stub (p_user                      => p_user,
                                         p_logical_load_timestamp      => lv_logical_load_timestamp,
                                         p_effective_start_timestamp   => p_effective_start_timestamp,
                                         p_customer_id                 => p_customer_ids(i).customer_id);
        END LOOP;
      END IF;
      -- PERSON ADDRESSES
      put_person_addresses(p_user                      => p_user,
                           p_logical_load_timestamp    => lv_logical_load_timestamp,
                           p_effective_start_timestamp => p_effective_start_timestamp,
                           p_lv_effective_start_time   => lv_effective_start_timestamp,
                           p_person_id                 => p_person_id,
                           p_person_addresses          => p_person_addresses,
                           p_record_source             => p_record_source);

      -- PERSON ALIASES
      put_person_aliases (p_user                      => p_user,
                          p_logical_load_timestamp    => lv_logical_load_timestamp,
                          p_effective_start_timestamp => p_effective_start_timestamp,
                          p_lv_effective_start_time   => lv_effective_start_timestamp,
                          p_person_id                 => p_person_id,
                          p_person_aliases            => p_person_aliases);

      -- PERSON EMAIL ADDRESSES
      put_person_email_addresses(p_user                     => p_user,
                                 p_logical_load_timestamp    => lv_logical_load_timestamp,
                                 p_effective_start_timestamp => p_effective_start_timestamp,
                                 p_lv_effective_start_time   => lv_effective_start_timestamp,
                                 p_person_id                 => p_person_id,
                                 p_person_email_addresses    => p_person_email_addresses,
                                 p_record_source             => p_record_source);

      -- PERSON NATIONAL IDENTIFIERS
      put_person_national_identif(p_user                        => p_user,
                                  p_logical_load_timestamp      => lv_logical_load_timestamp,
                                  p_effective_start_timestamp   => p_effective_start_timestamp,
                                  p_lv_effective_start_time     => lv_effective_start_timestamp,
                                  p_person_id                   => p_person_id,
                                  p_person_national_identifiers => p_person_national_identifiers);

      -- PERSON RELATIONSHIPS
      put_person_relationships (p_person_id                   => p_person_id,
                                p_user                        => p_user,
                                p_logical_load_timestamp      => lv_logical_load_timestamp,
                                p_effective_start_timestamp   => p_effective_start_timestamp,
                                p_lv_effective_start_time     => lv_effective_start_timestamp,
                                p_person_relationships        => p_person_relationships);

      -- PERSON TELEPHONE NUMBERS
      put_person_telephone_numbers(p_user                     => p_user,
                                   p_logical_load_timestamp      => lv_logical_load_timestamp,
                                   p_effective_start_timestamp   => p_effective_start_timestamp,
                                   p_lv_effective_start_time     => lv_effective_start_timestamp,
                                   p_person_id                   => p_person_id,
                                   p_person_telephone_numbers    => p_person_telephone_numbers,
                                   p_record_source               => p_record_source);

      -- COMPANY PERSON LINK

      --
      -- Company Person Link is being sent as scalar for now. Will be revisited later
      --

      /*IF (p_companies_persons_link IS NOT NULL AND p_companies_persons_link.count > 0)
      THEN
        put_companies_persons_link(p_user                        => p_user,
                                   p_logical_load_timestamp      => lv_logical_load_timestamp,
                                   p_effective_start_timestamp   => p_effective_start_timestamp,
                                   p_lv_effective_start_time     => lv_effective_start_timestamp,
                                   p_person_id                   => p_person_id,
                                   p_companies_persons_link      => p_companies_persons_link);
      END IF; */

      -- POWER OF ATTORNEYS
      nrg_poa.put_poa(p_user                         => p_user,
                      p_effective_start_timestamp    => p_effective_start_timestamp,
                      p_logical_load_timestamp       => lv_logical_load_timestamp,
                      p_entity_name                  => 'PERSON',
                      p_entity_id                    => p_person_id,
                      p_power_of_attorneys           => p_power_of_attorneys,
                      p_called_by                    => 'NRG_PERSON');


      --SOURCE OF FUNDS

      put_source_of_funds(p_user                        => p_user,
                          p_logical_load_timestamp      => lv_logical_load_timestamp,
                          p_effective_start_timestamp   => p_effective_start_timestamp,
                          p_lv_effective_start_time     => lv_effective_start_timestamp,
                          p_person_id                   => p_person_id,
                          p_type_of_fund                => 'FUND',
                          p_source_of_funds             => p_sources_of_funds);

      --SOURCE OF INCOME

      put_source_of_funds(p_user                        => p_user,
                          p_logical_load_timestamp      => lv_logical_load_timestamp,
                          p_effective_start_timestamp   => p_effective_start_timestamp,
                          p_lv_effective_start_time     => lv_effective_start_timestamp,
                          p_person_id                   => p_person_id,
                          p_type_of_fund                => 'INCOME',
                          p_source_of_funds             => p_sources_of_income);

      -- PERSON EMPLOYMENT DETAILS
      put_person_employment_details(p_user                       => p_user,
                                    p_logical_load_timestamp      => lv_logical_load_timestamp,
                                    p_effective_start_timestamp   => p_effective_start_timestamp,
                                    p_lv_effective_start_time     => lv_effective_start_timestamp,
                                    p_person_id                   => p_person_id,
                                    p_person_employment_details   => p_employment_details);

      -- PERSON TRADING EXPERIENCE
      put_person_trading_experience(p_user                     => p_user,
                                   p_logical_load_timestamp    => lv_logical_load_timestamp,
                                   p_effective_start_timestamp => p_effective_start_timestamp,
                                   p_lv_effective_start_time   => lv_effective_start_timestamp,
                                   p_person_id                 => p_person_id,
                                   p_person_trading_experience => p_person_trading_experience);

      -- PERSON TRADING PROFILES
      put_person_trading_profiles(p_user                      => p_user,
                                  p_logical_load_timestamp    => lv_logical_load_timestamp,
                                  p_effective_start_timestamp => p_effective_start_timestamp,
                                  p_lv_effective_start_time   => lv_effective_start_timestamp,
                                  p_person_id                 => p_person_id,
                                  p_person_trading_profiles   => p_person_trading_profiles);

      -- PERSON MIFID IDENTIFICATION
      put_person_mifid_idntfctns (p_user                      => p_user,
                                  p_logical_load_timestamp    => lv_logical_load_timestamp,
                                  p_effective_start_timestamp => p_effective_start_timestamp,
                                  p_lv_effective_start_time   => lv_effective_start_timestamp,
                                  p_person_id                 => p_person_id,
                                  p_person_mifid_idntfctns    => p_person_mifid_idntfctns);

      logger.logger.set_module(NULL);

  EXCEPTION
    WHEN lex_person_not_found THEN
          logger.logger.severe('Person Deleted Before Update and After Insert');
          logger.logger.set_module(NULL);
          raise_application_error(-20003, 'Person Deleted Before Update and After Insert');
    WHEN lex_unknown_operation_type THEN
          logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
          logger.logger.set_module(NULL);
          raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
    WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END put_person;

  -- ===================================================================================
  -- get_stubbed_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed ids to return (Optional - not set returns all)
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
  FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR

  IS
        lcuv_result           SYS_REFCURSOR;

        lex_unknown_platform  EXCEPTION;
  BEGIN
        logger.logger.set_module('get_stubbed_ids');

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT person_id
               FROM   persons
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT person_id
               FROM   persons
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;

        logger.logger.set_module(NULL);

        RETURN lcuv_result;

  EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
  END get_stubbed_ids;


END nrg_person;
/