CREATE OR REPLACE PACKAGE BODY nrg_transfers
AS
-- ===================================================================================
-- NRG_TRANSFERS
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     This package encapsulates NRG (Near Realtime Gatherer)
--
--     Management of the Transfers Admin Service model
--
-- -----------------------------------------------------------------------------------
--
-- Notes:
-- ------
--
--     Run as BI_ODS
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--     -20001    Entity already exists
--
-- Modifications:
-- --------------
--
--   Date         Modified By       Vers    Action
--   ----------   ---------------   -----   ----------------------------------------
--   20/01/2022   Patrick Dinwiddy  1.0     Creation
-- ===================================================================================
--
--
-- ===================================================================================
-- PACKAGE CONSTANTS
-- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '1.0';

-- ===================================================================================
-- version
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Function to retrieve the version of the package
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--
-- Return:
-- -------
--
--     Returns a VARCHAR2 representing the version of the package
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--
-- -----------------------------------------------------------------------------------

  FUNCTION version
    RETURN VARCHAR2 DETERMINISTIC
  IS

  BEGIN
  logger.logger.set_module('version');
  RETURN gc_version;
  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    RAISE;
  END version;


-- ===================================================================================
-- put_transfer_requests_pstns
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate transfer_requests_pstns and call put procedures for nested objects
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_transfer_requests_pstns        table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_transfer_requests_pstns(p_user                         IN transfer_request_positions.created_by%TYPE,
                                      p_transfer_request_id          IN transfer_request_positions.transfer_request_id%TYPE,
                                      p_effective_start_timestamp    IN transfer_request_positions.effective_start_timestamp%TYPE,
                                      p_transfer_requests_pstns      IN transfer_requests_pstns_tab,
                                      p_business_date                IN transfer_request_positions.business_date%TYPE,
                                      p_reporting_date               IN transfer_request_positions.reporting_date%TYPE) IS

   lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

BEGIN

    MERGE INTO transfer_request_positions_h history USING
       (SELECT old_version.*
          FROM transfer_request_positions old_version
          LEFT JOIN TABLE(CAST(p_transfer_requests_pstns AS transfer_requests_pstns_tab)) quest
            ON old_version.transfer_request_position_id = quest.transfer_request_position_id
         WHERE old_version.transfer_request_id = p_transfer_request_id
          AND ((nrg_common.has_value_changed(old_version.name,quest.name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.instrument_code,quest.instrument_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.ric,quest.ric)= 1 ) OR
               (nrg_common.has_value_changed(old_version.isin,quest.isin)= 1 ) OR
               (nrg_common.has_value_changed(old_version.sedol,quest.sedol)= 1 ) OR
               (nrg_common.has_value_changed(old_version.quantity,quest.quantity)= 1 ) OR
               (nrg_common.has_value_changed(old_version.currency_code,quest.currency_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.value,quest.value)= 1 ) OR
               (nrg_common.has_value_changed(old_version.status,quest.status)= 1 ) OR
               (nrg_common.has_value_changed(old_version.is_in_pms,quest.is_in_pms)= 1 ) OR
               (nrg_common.has_value_changed(old_version.custody_account,quest.custody_account)= 1 ) OR
               (nrg_common.has_value_changed(old_version.cost,quest.cost)= 1 ) OR
               (nrg_common.has_value_changed(old_version.is_cash,quest.is_cash)= 1 ))
               ) new_version
    ON (history.transfer_request_position_id = new_version.transfer_request_position_id AND
        history.effective_start_timestamp    = new_version.effective_start_timestamp)
    WHEN MATCHED THEN UPDATE
      SET
        history.updated_by                 = new_version.updated_by,
        history.action                     = 'U',
        history.action_timestamp           = SYSTIMESTAMP,
        history.update_timestamp           = new_version.update_timestamp,
        history.logical_load_timestamp     = new_version.logical_load_timestamp,
        history.name                       = new_version.name,
        history.instrument_code            = new_version.instrument_code,
        history.ric                        = new_version.ric,
        history.isin                       = new_version.isin,
        history.sedol                      = new_version.sedol,
        history.quantity                   = new_version.quantity,
        history.currency_code              = new_version.currency_code,
        history.value                      = new_version.value,
        history.status                     = new_version.status,
        history.is_in_pms               = new_version.is_in_pms,
        history.custody_account            = new_version.custody_account,
        history.cost                       = new_version.cost,
        history.is_cash                    = new_version.is_cash
    WHEN NOT MATCHED THEN INSERT
       (transfer_request_position_id,
        transfer_request_id,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        effective_end_timestamp,
        action,
        action_timestamp,
        business_date,
        reporting_date,
        name,
        instrument_code,
        ric,
        isin,
        sedol,
        quantity,
        currency_code,
        value,
        status,
        is_in_pms,
        custody_account,
        cost,
        is_cash)
    VALUES
       (new_version.transfer_request_position_id,
        new_version.transfer_request_id,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.effective_start_timestamp,
        p_effective_start_timestamp,
        'U',
        SYSTIMESTAMP,
        new_version.business_date,
        new_version.reporting_date,
        new_version.name,
        new_version.instrument_code,
        new_version.ric,
        new_version.isin,
        new_version.sedol,
        new_version.quantity,
        new_version.currency_code,
        new_version.value,
        new_version.status,
        new_version.is_in_pms,
        new_version.custody_account,
        new_version.cost,
        new_version.is_cash);

    DELETE transfer_request_positions trp
    WHERE trp.transfer_request_id = p_transfer_request_id
    AND NOT EXISTS (SELECT 1
                     FROM TABLE(CAST(p_transfer_requests_pstns AS transfer_requests_pstns_tab)) req
                    WHERE req.transfer_request_position_id = trp.transfer_request_position_id);

    MERGE INTO transfer_request_positions old_version USING
       (SELECT DISTINCT quest.transfer_request_position_id,
                        quest.name,
                        quest.instrument_code,
                        quest.ric,
                        quest.isin,
                        quest.sedol,
                        quest.quantity,
                        quest.currency_code,
                        quest.value,
                        quest.status,
                        quest.is_in_pms,
                        quest.custody_account,
                        quest.cost,
                        quest.is_cash
          FROM TABLE(CAST(p_transfer_requests_pstns AS transfer_requests_pstns_tab)) quest) new_version
      ON (old_version.transfer_request_position_id = new_version.transfer_request_position_id)
      WHEN MATCHED THEN UPDATE
        SET
          old_version.logical_load_timestamp       = lv_logical_load_timestamp,
          old_version.updated_by                   = p_user,
          old_version.update_timestamp             = SYSTIMESTAMP,
          old_version.effective_start_timestamp    = p_effective_start_timestamp,
          old_version.name                         = new_version.name,
          old_version.instrument_code              = new_version.instrument_code,
          old_version.ric                          = new_version.ric,
          old_version.isin                         = new_version.isin,
          old_version.sedol                        = new_version.sedol,
          old_version.quantity                     = new_version.quantity,
          old_version.currency_code                = new_version.currency_code,
          old_version.value                        = new_version.value,
          old_version.status                       = new_version.status,
          old_version.is_in_pms                 = new_version.is_in_pms,
          old_version.custody_account              = new_version.custody_account,
          old_version.cost                         = new_version.cost,
          old_version.is_cash                      = new_version.is_cash
        WHERE ((nrg_common.has_value_changed(old_version.name,new_version.name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.instrument_code,new_version.instrument_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.ric,new_version.ric)= 1 ) OR
               (nrg_common.has_value_changed(old_version.isin,new_version.isin)= 1 ) OR
               (nrg_common.has_value_changed(old_version.sedol,new_version.sedol)= 1 ) OR
               (nrg_common.has_value_changed(old_version.quantity,new_version.quantity)= 1 ) OR
               (nrg_common.has_value_changed(old_version.currency_code,new_version.currency_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.value,new_version.value)= 1 ) OR
               (nrg_common.has_value_changed(old_version.status,new_version.status)= 1 ) OR
               (nrg_common.has_value_changed(old_version.is_in_pms,new_version.is_in_pms)= 1 ) OR
               (nrg_common.has_value_changed(old_version.custody_account,new_version.custody_account)= 1 ) OR
               (nrg_common.has_value_changed(old_version.cost,new_version.cost)= 1 ) OR
               (nrg_common.has_value_changed(old_version.is_cash,new_version.is_cash)= 1 ))
      WHEN NOT MATCHED THEN INSERT
         (transfer_request_position_id,
          transfer_request_id,
          logical_load_timestamp,
          created_by,
          create_timestamp,
          updated_by,
          update_timestamp,
          effective_start_timestamp,
          business_date,
          reporting_date,
          name,
          instrument_code,
          ric,
          isin,
          sedol,
          quantity,
          currency_code,
          value,
          status,
          is_in_pms,
          custody_account,
          cost,
          is_cash)
      VALUES
         (new_version.transfer_request_position_id,
          p_transfer_request_id,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          p_effective_start_timestamp,
          p_business_date,
          p_reporting_date,
          new_version.name,
          new_version.instrument_code,
          new_version.ric,
          new_version.isin,
          new_version.sedol,
          new_version.quantity,
          new_version.currency_code,
          new_version.value,
          new_version.status,
          new_version.is_in_pms,
          new_version.custody_account,
          new_version.cost,
          new_version.is_cash
          );

EXCEPTION WHEN OTHERS THEN

    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

END;

-- ===================================================================================
-- put_transfer_requests_answr
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate transfer_requests_answr and call put procedures for nested objects
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_transfer_requests_answr        table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_transfer_requests_answr(p_user                         IN transfer_request_answers.created_by%TYPE,
                                      p_transfer_request_id          IN transfer_request_answers.transfer_request_id%TYPE,
                                      p_effective_start_timestamp    IN transfer_request_answers.effective_start_timestamp%TYPE,
                                      p_transfer_requests_answr      IN transfer_requests_answr_tab) IS

   lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

BEGIN

    MERGE INTO transfer_request_answers_h history USING
       (SELECT old_version.*
          FROM transfer_request_answers old_version
          LEFT JOIN TABLE(CAST(p_transfer_requests_answr AS transfer_requests_answr_tab)) quest
            ON old_version.transfer_request_question_id = quest.transfer_request_question_id
         WHERE old_version.transfer_request_id = p_transfer_request_id
          AND ((nrg_common.has_value_changed(old_version.question,quest.question)= 1 ) OR
               (nrg_common.has_value_changed(old_version.answer,quest.answer)= 1 ))
               ) new_version
    ON (history.transfer_request_question_id = new_version.transfer_request_question_id AND
        history.transfer_request_id          = new_version.transfer_request_id AND
        history.effective_start_timestamp    = new_version.effective_start_timestamp)
    WHEN MATCHED THEN UPDATE
      SET
        history.updated_by                 = new_version.updated_by,
        history.action                     = 'U',
        history.action_timestamp           = SYSTIMESTAMP,
        history.update_timestamp           = new_version.update_timestamp,
        history.logical_load_timestamp     = new_version.logical_load_timestamp,
        history.question                   = new_version.question,
        history.answer                     = new_version.answer
    WHEN NOT MATCHED THEN INSERT
       (transfer_request_question_id,
        transfer_request_id,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        effective_end_timestamp,
        action,
        action_timestamp,
        question,
        answer)
    VALUES
       (new_version.transfer_request_question_id,
        new_version.transfer_request_id,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.effective_start_timestamp,
        p_effective_start_timestamp,
        'U',
        SYSTIMESTAMP,
        new_version.question,
        new_version.answer);

    DELETE transfer_request_answers trp
    WHERE trp.transfer_request_id = p_transfer_request_id
    AND NOT EXISTS (SELECT 1
                      FROM TABLE(CAST(p_transfer_requests_answr AS transfer_requests_answr_tab)) req
                     WHERE req.transfer_request_question_id = trp.transfer_request_question_id);

    MERGE INTO transfer_request_answers old_version USING
       (SELECT DISTINCT quest.transfer_request_question_id,
                        quest.question,
                        quest.answer
          FROM TABLE(CAST(p_transfer_requests_answr AS transfer_requests_answr_tab)) quest) new_version
      ON (old_version.transfer_request_question_id = new_version.transfer_request_question_id
      AND old_version.transfer_request_id = p_transfer_request_id)
      WHEN MATCHED THEN UPDATE
        SET
          old_version.logical_load_timestamp       = lv_logical_load_timestamp,
          old_version.updated_by                   = p_user,
          old_version.update_timestamp             = SYSTIMESTAMP,
          old_version.effective_start_timestamp    = p_effective_start_timestamp,
          old_version.question                     = new_version.question,
          old_version.answer                       = new_version.answer
        WHERE ((nrg_common.has_value_changed(old_version.question,new_version.question)= 1 ) OR
               (nrg_common.has_value_changed(old_version.answer,new_version.answer)= 1 ))
      WHEN NOT MATCHED THEN INSERT
         (transfer_request_question_id,
          transfer_request_id,
          logical_load_timestamp,
          created_by,
          create_timestamp,
          updated_by,
          update_timestamp,
          effective_start_timestamp,
          question,
          answer)
      VALUES
         (new_version.transfer_request_question_id,
          p_transfer_request_id,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          p_effective_start_timestamp,
          new_version.question,
          new_version.answer
          );

EXCEPTION WHEN OTHERS THEN

    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

END;

-- ===================================================================================
-- put_transfer_requests_email
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate transfer_requests_email and call put procedures for nested objects
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_transfer_requests_email        table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_transfer_requests_email(p_user                         IN transfer_req_broker_email.created_by%TYPE,
                                      p_transfer_request_id          IN transfer_req_broker_email.transfer_request_id%TYPE,
                                      p_broker_id                    IN transfer_req_broker_email.broker_id%TYPE,
                                      p_effective_start_timestamp    IN transfer_req_broker_email.effective_start_timestamp%TYPE,
                                      p_transfer_requests_email      IN transfer_requests_email_tab) IS

   lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

BEGIN

    MERGE INTO transfer_req_broker_email_h old_version USING
       (SELECT old_version.*
          FROM transfer_req_broker_email old_version
         WHERE old_version.transfer_request_id = p_transfer_request_id
           AND NOT EXISTS (SELECT 1
                             FROM TABLE(CAST(p_transfer_requests_email AS transfer_requests_email_tab)) new_version
                            WHERE old_version.transfer_request_id   = p_transfer_request_id
                              AND old_version.email_address         = new_version.email_address)) new_version
      ON (old_version.transfer_request_id = p_transfer_request_id
    AND old_version.email_address = new_version.email_address)
      WHEN NOT MATCHED THEN INSERT
         (transfer_request_id,
          logical_load_timestamp,
          created_by,
          create_timestamp,
          updated_by,
          update_timestamp,
          effective_start_timestamp,
          broker_id,
          email_address)
      VALUES
         (p_transfer_request_id,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          p_effective_start_timestamp,
          p_broker_id,
          new_version.email_address
          );

    DELETE transfer_req_broker_email old_version
     WHERE old_version.transfer_request_id = p_transfer_request_id
       AND NOT EXISTS (SELECT 1
                         FROM TABLE(CAST(p_transfer_requests_email AS transfer_requests_email_tab)) new_version
                        WHERE old_version.transfer_request_id   = p_transfer_request_id
                          AND old_version.email_address         = new_version.email_address);

    MERGE INTO transfer_req_broker_email old_version USING
       (SELECT DISTINCT email_address
          FROM TABLE(CAST(p_transfer_requests_email AS transfer_requests_email_tab)) quest) new_version
      ON (old_version.transfer_request_id = p_transfer_request_id
    AND old_version.broker_id = p_broker_id
    AND old_version.email_address = new_version.email_address)
      WHEN NOT MATCHED THEN INSERT
         (transfer_request_id,
          logical_load_timestamp,
          created_by,
          create_timestamp,
          updated_by,
          update_timestamp,
          effective_start_timestamp,
          broker_id,
          email_address)
      VALUES
         (p_transfer_request_id,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          p_effective_start_timestamp,
          p_broker_id,
          new_version.email_address
          );

EXCEPTION WHEN OTHERS THEN

    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

END;

-- ===================================================================================
-- put_transfer_requests
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate transfer_requests and call put procedures for nested objects
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_transfer_requests              table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

 PROCEDURE put_transfer_requests(p_user                           IN transfer_requests.created_by%TYPE,
                                 p_transfer_requests              IN transfer_requests_tab) IS

   lv_logical_load_timestamp TIMESTAMP(6) := SYSTIMESTAMP;

BEGIN

    MERGE INTO transfer_requests_h history USING
       (SELECT old_version.*,quest.last_modified as new_eff_start_timestamp
          FROM transfer_requests old_version
          JOIN TABLE(CAST(p_transfer_requests AS transfer_requests_tab)) quest
            ON old_version.transfer_request_id = quest.transfer_request_id
        WHERE ((nrg_common.has_value_changed(old_version.pega_case_id,quest.pega_case_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.direction,quest.direction)= 1 ) OR
               (nrg_common.has_value_changed(old_version.cardinality,quest.cardinality)= 1 ) OR
               (nrg_common.has_value_changed(old_version.customer_id,quest.customer_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.trading_account_id,quest.trading_account_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.external_account_reference,quest.external_account_reference)= 1 ) OR
               (nrg_common.has_value_changed(old_version.external_account_type,quest.external_account_type)= 1 ) OR
               (nrg_common.has_value_changed(old_version.creation_time,quest.creation_time)= 1 ) OR
               (nrg_common.has_value_changed(old_version.last_modified,quest.last_modified)= 1 ) OR
               (nrg_common.has_value_changed(old_version.is_full_transfer,quest.is_full_transfer)= 1 ) OR
               (nrg_common.has_value_changed(old_version.target_completion,quest.target_completion)= 1 ) OR
               (nrg_common.has_value_changed(old_version.state,quest.state)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_address_line_1,quest.user_address_line_1)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_address_line_2,quest.user_address_line_2)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_address_line_3,quest.user_address_line_3)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_city,quest.user_city)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_county,quest.user_county)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_country,quest.user_country)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_post_code,quest.user_post_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_first_name,quest.user_first_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_middle_name,quest.user_middle_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_last_name,quest.user_last_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_email_address,quest.user_email_address)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_phone_country_code,quest.user_phone_country_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_phone_number,quest.user_phone_number)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_id,quest.broker_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_address_line_1,quest.broker_address_line_1)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_address_line_2,quest.broker_address_line_2)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_address_line_3,quest.broker_address_line_3)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_city,quest.broker_city)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_county,quest.broker_county)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_country,quest.broker_country)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_post_code,quest.broker_post_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_name,quest.broker_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_phone_country_code,quest.broker_phone_country_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_phone_number,quest.broker_phone_number)= 1 ) OR
               (nrg_common.has_value_changed(old_version.time_frame,quest.time_frame)= 1 ) OR
               (nrg_common.has_value_changed(old_version.current_year,quest.current_year)= 1 ) OR
               (nrg_common.has_value_changed(old_version.previous_years,quest.previous_years)= 1 ) OR
               (nrg_common.has_value_changed(old_version.all_years,quest.all_years)= 1 ) OR
               (nrg_common.has_value_changed(old_version.external_Case_Reference,quest.external_Case_Reference)= 1 ) OR
               (nrg_common.has_value_changed(old_version.dob,quest.dob)= 1 ) OR
               (nrg_common.has_value_changed(old_version.nino,quest.nino)= 1 ) OR
               (nrg_common.has_value_changed(old_version.completed_date,quest.completed_date)= 1 ) OR
               (nrg_common.has_value_changed(old_version.freeze_days,quest.freeze_days)= 1 ))
               ) new_version
    ON (history.transfer_request_id        = new_version.transfer_request_id AND
        history.effective_start_timestamp  = new_version.effective_start_timestamp)
    WHEN MATCHED THEN UPDATE
      SET
        history.updated_by                 = new_version.updated_by,
        history.action                     = 'U',
        history.action_timestamp           = SYSTIMESTAMP,
        history.update_timestamp           = new_version.update_timestamp,
        history.logical_load_timestamp     = new_version.logical_load_timestamp,
        history.pega_case_id               = new_version.pega_case_id,
        history.direction                  = new_version.direction,
        history.cardinality                = new_version.cardinality,
        history.customer_id                = new_version.customer_id,
        history.trading_account_id         = new_version.trading_account_id,
        history.external_account_reference = new_version.external_account_reference,
        history.external_account_type      = new_version.external_account_type,
        history.creation_time              = new_version.creation_time,
        history.last_modified              = new_version.last_modified,
        history.is_full_transfer           = new_version.is_full_transfer,
        history.target_completion          = new_version.target_completion,
        history.state                      = new_version.state,
        history.user_address_line_1        = new_version.user_address_line_1,
        history.user_address_line_2        = new_version.user_address_line_2,
        history.user_address_line_3        = new_version.user_address_line_3,
        history.user_city                  = new_version.user_city,
        history.user_county                = new_version.user_county,
        history.user_country               = new_version.user_country,
        history.user_post_code             = new_version.user_post_code,
        history.user_first_name            = new_version.user_first_name,
        history.user_middle_name           = new_version.user_middle_name,
        history.user_last_name             = new_version.user_last_name,
        history.user_email_address         = new_version.user_email_address,
        history.user_phone_country_code    = new_version.user_phone_country_code,
        history.user_phone_number          = new_version.user_phone_number,
        history.broker_id                  = new_version.broker_id,
        history.broker_address_line_1      = new_version.broker_address_line_1,
        history.broker_address_line_2      = new_version.broker_address_line_2,
        history.broker_address_line_3      = new_version.broker_address_line_3,
        history.broker_city                = new_version.broker_city,
        history.broker_county              = new_version.broker_county,
        history.broker_country             = new_version.broker_country,
        history.broker_post_code           = new_version.broker_post_code,
        history.broker_name                = new_version.broker_name,
        history.broker_phone_country_code  = new_version.broker_phone_country_code,
        history.broker_phone_number        = new_version.broker_phone_number,
        history.time_frame                 = new_version.time_frame,
        history.current_year               = new_version.current_year,
        history.previous_years             = new_version.previous_years,
        history.all_years                  = new_version.all_years,
        history.external_Case_Reference    = new_version.external_Case_Reference,
        history.dob                        = new_version.dob,
        history.nino                       = new_version.nino,
        history.completed_date             = new_version.completed_date,
        history.freeze_days                = new_version.freeze_days
    WHEN NOT MATCHED THEN INSERT
       (transfer_request_id,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        effective_start_timestamp,
        effective_end_timestamp,
        action,
        action_timestamp,
        pega_case_id,
        direction,
        cardinality,
        customer_id,
        trading_account_id,
        external_account_reference,
        external_account_type,
        creation_time,
        last_modified,
        is_full_transfer,
        target_completion,
        state,
        user_address_line_1,
        user_address_line_2,
        user_address_line_3,
        user_city,
        user_county,
        user_country,
        user_post_code,
        user_first_name,
        user_middle_name,
        user_last_name,
        user_email_address,
        user_phone_country_code,
        user_phone_number,
        broker_id,
        broker_address_line_1,
        broker_address_line_2,
        broker_address_line_3,
        broker_city,
        broker_county,
        broker_country,
        broker_post_code,
        broker_name,
        broker_phone_country_code,
        broker_phone_number,
        time_frame,
        current_year,
        previous_years,
        all_years,
        external_Case_Reference,
        dob,
        nino,
        completed_date,
        freeze_days)
    VALUES
       (new_version.transfer_request_id,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.effective_start_timestamp,
        new_version.new_eff_start_timestamp,
        'U',
        SYSTIMESTAMP,
        new_version.pega_case_id,
        new_version.direction,
        new_version.cardinality,
        new_version.customer_id,
        new_version.trading_account_id,
        new_version.external_account_reference,
        new_version.external_account_type,
        new_version.creation_time,
        new_version.last_modified,
        new_version.is_full_transfer,
        new_version.target_completion,
        new_version.state,
        new_version.user_address_line_1,
        new_version.user_address_line_2,
        new_version.user_address_line_3,
        new_version.user_city,
        new_version.user_county,
        new_version.user_country,
        new_version.user_post_code,
        new_version.user_first_name,
        new_version.user_middle_name,
        new_version.user_last_name,
        new_version.user_email_address,
        new_version.user_phone_country_code,
        new_version.user_phone_number,
        new_version.broker_id,
        new_version.broker_address_line_1,
        new_version.broker_address_line_2,
        new_version.broker_address_line_3,
        new_version.broker_city,
        new_version.broker_county,
        new_version.broker_country,
        new_version.broker_post_code,
        new_version.broker_name,
        new_version.broker_phone_country_code,
        new_version.broker_phone_number,
        new_version.time_frame,
        new_version.current_year,
        new_version.previous_years,
        new_version.all_years,
        new_version.external_Case_Reference,
        new_version.dob,
        new_version.nino,
        new_version.completed_date,
        new_version.freeze_days);

    MERGE INTO transfer_requests old_version USING
       (SELECT DISTINCT quest.transfer_request_id,
                        quest.pega_case_id,
                        quest.direction,
                        quest.cardinality,
                        quest.customer_id,
                        quest.trading_account_id,
                        quest.external_account_reference,
                        quest.external_account_type,
                        quest.creation_time,
                        quest.last_modified,
                        quest.is_full_transfer,
                        quest.target_completion,
                        quest.state,
                        quest.user_address_line_1,
                        quest.user_address_line_2,
                        quest.user_address_line_3,
                        quest.user_city,
                        quest.user_county,
                        quest.user_country,
                        quest.user_post_code,
                        quest.user_first_name,
                        quest.user_middle_name,
                        quest.user_last_name,
                        quest.user_email_address,
                        quest.user_phone_country_code,
                        quest.user_phone_number,
                        quest.broker_id,
                        quest.broker_address_line_1,
                        quest.broker_address_line_2,
                        quest.broker_address_line_3,
                        quest.broker_city,
                        quest.broker_county,
                        quest.broker_country,
                        quest.broker_post_code,
                        quest.broker_name,
                        quest.broker_phone_country_code,
                        quest.broker_phone_number,
                        quest.time_frame,
                        quest.current_year,
                        quest.previous_years,
                        quest.all_years,
                        quest.external_Case_Reference,
                        quest.dob,
                        quest.nino,
                        quest.completed_date,
                        quest.freeze_days
          FROM TABLE(CAST(p_transfer_requests AS transfer_requests_tab)) quest) new_version
      ON (old_version.transfer_request_id = new_version.transfer_request_id)
      WHEN MATCHED THEN UPDATE
        SET
          old_version.logical_load_timestamp       = lv_logical_load_timestamp,
          old_version.updated_by                   = p_user,
          old_version.update_timestamp             = SYSTIMESTAMP,
          old_version.effective_start_timestamp    = new_version.last_modified,
          old_version.business_date                = nrg_common.get_business_date(new_version.creation_time),
          old_version.reporting_date               = nrg_common.get_reporting_date(new_version.creation_time),
          old_version.pega_case_id                 = new_version.pega_case_id,
          old_version.direction                    = new_version.direction,
          old_version.cardinality                  = new_version.cardinality,
          old_version.customer_id                  = new_version.customer_id,
          old_version.trading_account_id           = new_version.trading_account_id,
          old_version.external_account_reference   = new_version.external_account_reference,
          old_version.external_account_type        = new_version.external_account_type,
          old_version.creation_time                = new_version.creation_time,
          old_version.last_modified                = new_version.last_modified,
          old_version.is_full_transfer             = new_version.is_full_transfer,
          old_version.target_completion            = new_version.target_completion,
          old_version.state                        = new_version.state,
          old_version.user_address_line_1          = new_version.user_address_line_1,
          old_version.user_address_line_2          = new_version.user_address_line_2,
          old_version.user_address_line_3          = new_version.user_address_line_3,
          old_version.user_city                    = new_version.user_city,
          old_version.user_county                  = new_version.user_county,
          old_version.user_country                 = new_version.user_country,
          old_version.user_post_code               = new_version.user_post_code,
          old_version.user_first_name              = new_version.user_first_name,
          old_version.user_middle_name             = new_version.user_middle_name,
          old_version.user_last_name               = new_version.user_last_name,
          old_version.user_email_address           = new_version.user_email_address,
          old_version.user_phone_country_code      = new_version.user_phone_country_code,
          old_version.user_phone_number            = new_version.user_phone_number,
          old_version.broker_id                    = new_version.broker_id,
          old_version.broker_address_line_1        = new_version.broker_address_line_1,
          old_version.broker_address_line_2        = new_version.broker_address_line_2,
          old_version.broker_address_line_3        = new_version.broker_address_line_3,
          old_version.broker_city                  = new_version.broker_city,
          old_version.broker_county                = new_version.broker_county,
          old_version.broker_country               = new_version.broker_country,
          old_version.broker_post_code             = new_version.broker_post_code,
          old_version.broker_name                  = new_version.broker_name,
          old_version.broker_phone_country_code    = new_version.broker_phone_country_code,
          old_version.broker_phone_number          = new_version.broker_phone_number,
          old_version.time_frame                   = new_version.time_frame,
          old_version.current_year                 = new_version.current_year,
          old_version.previous_years               = new_version.previous_years,
          old_version.all_years                    = new_version.all_years,
          old_version.external_Case_Reference      = new_version.external_Case_Reference,
          old_version.dob                          = new_version.dob,
          old_version.nino                         = new_version.nino,
          old_version.completed_date               = new_version.completed_date,
          old_version.freeze_days                  = new_version.freeze_days
        WHERE ((nrg_common.has_value_changed(old_version.pega_case_id,new_version.pega_case_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.direction,new_version.direction)= 1 ) OR
               (nrg_common.has_value_changed(old_version.cardinality,new_version.cardinality)= 1 ) OR
               (nrg_common.has_value_changed(old_version.customer_id,new_version.customer_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.trading_account_id,new_version.trading_account_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.external_account_reference,new_version.external_account_reference)= 1 ) OR
               (nrg_common.has_value_changed(old_version.external_account_type,new_version.external_account_type)= 1 ) OR
               (nrg_common.has_value_changed(old_version.creation_time,new_version.creation_time)= 1 ) OR
               (nrg_common.has_value_changed(old_version.last_modified,new_version.last_modified)= 1 ) OR
               (nrg_common.has_value_changed(old_version.is_full_transfer,new_version.is_full_transfer)= 1 ) OR
               (nrg_common.has_value_changed(old_version.target_completion,new_version.target_completion)= 1 ) OR
               (nrg_common.has_value_changed(old_version.state,new_version.state)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_address_line_1,new_version.user_address_line_1)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_address_line_2,new_version.user_address_line_2)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_address_line_3,new_version.user_address_line_3)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_city,new_version.user_city)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_county,new_version.user_county)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_country,new_version.user_country)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_post_code,new_version.user_post_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_first_name,new_version.user_first_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_middle_name,new_version.user_middle_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_last_name,new_version.user_last_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_email_address,new_version.user_email_address)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_phone_country_code,new_version.user_phone_country_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.user_phone_number,new_version.user_phone_number)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_id,new_version.broker_id)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_address_line_1,new_version.broker_address_line_1)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_address_line_2,new_version.broker_address_line_2)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_address_line_3,new_version.broker_address_line_3)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_city,new_version.broker_city)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_county,new_version.broker_county)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_country,new_version.broker_country)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_post_code,new_version.broker_post_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_name,new_version.broker_name)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_phone_country_code,new_version.broker_phone_country_code)= 1 ) OR
               (nrg_common.has_value_changed(old_version.broker_phone_number,new_version.broker_phone_number)= 1 ) OR
               (nrg_common.has_value_changed(old_version.time_frame,new_version.time_frame)= 1 ) OR
               (nrg_common.has_value_changed(old_version.current_year,new_version.current_year)= 1 ) OR
               (nrg_common.has_value_changed(old_version.previous_years,new_version.previous_years)= 1 ) OR
               (nrg_common.has_value_changed(old_version.all_years,new_version.all_years)= 1 ) OR
               (nrg_common.has_value_changed(old_version.external_Case_Reference,new_version.external_Case_Reference)= 1 ) OR
               (nrg_common.has_value_changed(old_version.dob,new_version.dob)= 1 ) OR
               (nrg_common.has_value_changed(old_version.nino,new_version.nino)= 1 ) OR
               (nrg_common.has_value_changed(old_version.completed_date,new_version.completed_date)= 1 ) OR
               (nrg_common.has_value_changed(old_version.freeze_days,new_version.freeze_days)= 1 ))
      WHEN NOT MATCHED THEN INSERT
         (transfer_request_id,
          logical_load_timestamp,
          created_by,
          create_timestamp,
          updated_by,
          update_timestamp,
          effective_start_timestamp,
          business_date,
          reporting_date,
          pega_case_id,
          direction,
          cardinality,
          customer_id,
          trading_account_id,
          external_account_reference,
          external_account_type,
          creation_time,
          last_modified,
          is_full_transfer,
          target_completion,
          state,
          user_address_line_1,
          user_address_line_2,
          user_address_line_3,
          user_city,
          user_county,
          user_country,
          user_post_code,
          user_first_name,
          user_middle_name,
          user_last_name,
          user_email_address,
          user_phone_country_code,
          user_phone_number,
          broker_id,
          broker_address_line_1,
          broker_address_line_2,
          broker_address_line_3,
          broker_city,
          broker_county,
          broker_country,
          broker_post_code,
          broker_name,
          broker_phone_country_code,
          broker_phone_number,
          time_frame,
          current_year,
          previous_years,
          all_years,
          external_Case_Reference,
          dob,
          nino,
          completed_date,
          freeze_days)
      VALUES
         (new_version.transfer_request_id,
          lv_logical_load_timestamp,
          p_user,
          SYSTIMESTAMP,
          p_user,
          SYSTIMESTAMP,
          new_version.last_modified,
          nrg_common.get_business_date(new_version.creation_time),
          nrg_common.get_reporting_date(new_version.creation_time),
          new_version.pega_case_id,
          new_version.direction,
          new_version.cardinality,
          new_version.customer_id,
          new_version.trading_account_id,
          new_version.external_account_reference,
          new_version.external_account_type,
          new_version.creation_time,
          new_version.last_modified,
          new_version.is_full_transfer,
          new_version.target_completion,
          new_version.state,
          new_version.user_address_line_1,
          new_version.user_address_line_2,
          new_version.user_address_line_3,
          new_version.user_city,
          new_version.user_county,
          new_version.user_country,
          new_version.user_post_code,
          new_version.user_first_name,
          new_version.user_middle_name,
          new_version.user_last_name,
          new_version.user_email_address,
          new_version.user_phone_country_code,
          new_version.user_phone_number,
          new_version.broker_id,
          new_version.broker_address_line_1,
          new_version.broker_address_line_2,
          new_version.broker_address_line_3,
          new_version.broker_city,
          new_version.broker_county,
          new_version.broker_country,
          new_version.broker_post_code,
          new_version.broker_name,
          new_version.broker_phone_country_code,
          new_version.broker_phone_number,
          new_version.time_frame,
          new_version.current_year,
          new_version.previous_years,
          new_version.all_years,
          new_version.external_Case_Reference,
          new_version.dob,
          new_version.nino,
          new_version.completed_date,
          new_version.freeze_days
          );

    IF p_transfer_requests IS NOT NULL THEN

      FOR lv_count in 1.. p_transfer_requests.count LOOP

        put_transfer_requests_pstns (p_user                        => p_user,
                                     p_transfer_request_id         => p_transfer_requests(lv_count).transfer_request_id,
                                     p_effective_start_timestamp   => p_transfer_requests(lv_count).last_modified,
                                     p_transfer_requests_pstns     => p_transfer_requests(lv_count).transfer_requests_pstns,
                                     p_business_date               => nrg_common.get_business_date(p_transfer_requests(lv_count).creation_time),
                                     p_reporting_date              => nrg_common.get_reporting_date(p_transfer_requests(lv_count).creation_time));


        put_transfer_requests_answr (p_user                        => p_user,
                                     p_transfer_request_id         => p_transfer_requests(lv_count).transfer_request_id,
                                     p_effective_start_timestamp   => p_transfer_requests(lv_count).last_modified,
                                     p_transfer_requests_answr     => p_transfer_requests(lv_count).transfer_requests_answr);

        put_transfer_requests_email (p_user                        => p_user,
                                     p_transfer_request_id         => p_transfer_requests(lv_count).transfer_request_id,
                                     p_broker_id                   => nvl(p_transfer_requests(lv_count).broker_id,-1),
                                     p_effective_start_timestamp   => p_transfer_requests(lv_count).last_modified,
                                     p_transfer_requests_email     => p_transfer_requests(lv_count).transfer_requests_email);

      END LOOP;

    END IF;

EXCEPTION WHEN OTHERS THEN

    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

END;

END nrg_transfers;
/