GRANT EXECUTE ON ACCNT_CUST_CHNG_RSNS_OBJ TO BI_NRG;
GRANT EXECUTE ON ACCNT_CUST_CHNG_RSNS_TAB TO BI_NRG;
GRANT EXECUTE ON ACCNT_LIQUIDATION_LEVELS_OBJ TO BI_NRG;
GRANT EXECUTE ON ACCNT_LIQUIDATION_LEVELS_TAB TO BI_NRG;
GRANT EXECUTE ON ACCNT_MANUAL_PRODUCTS_OBJ TO BI_NRG;
GRANT EXECUTE ON ACCNT_MANUAL_PRODUCTS_TAB TO BI_NRG;
GRANT EXECUTE ON ACCNT_MON_RGN_NOTIF_LVLS_OBJ TO BI_NRG;
GRANT EXECUTE ON ACCNT_MON_RGN_NOTIF_LVLS_TAB TO BI_NRG;
GRANT EXECUTE ON ACCNT_ORDER_EXECUTION_TYPE_OBJ TO BI_NRG;
GRANT EXECUTE ON ACCNT_ORDER_EXECUTION_TYPE_TAB TO BI_NRG;
GRANT EXECUTE ON ACCOUNT_VALUE_OBJ TO BI_NRG;
GRANT EXECUTE ON ACCOUNT_VALUE_TAB TO BI_NRG;
GRANT EXECUTE ON ACNT_VL_SNP_SCNDRY_BLNCS_OBJ TO BI_NRG;
GRANT EXECUTE ON ACNT_VL_SNP_SCNDRY_BLNCS_TAB TO BI_NRG;
GRANT EXECUTE ON ALERT_NOTIFICATION_TYPES_OBJ TO BI_NRG;
GRANT EXECUTE ON ALERT_NOTIFICATION_TYPES_TAB TO BI_NRG;
GRANT EXECUTE ON ANYTIME_POSITIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON ANYTIME_POSITIONS_TAB TO BI_NRG;
GRANT EXECUTE ON ANYTIME_PRICES_OBJ TO BI_NRG;
GRANT EXECUTE ON ANYTIME_PRICES_TAB TO BI_NRG;
GRANT EXECUTE ON ASSET_CLASSES_OBJ TO BI_NRG;
GRANT EXECUTE ON ASSET_CLASSES_TAB TO BI_NRG;
GRANT EXECUTE ON ASSET_CLASS_REBATE_OBJ TO BI_NRG;
GRANT EXECUTE ON ASSET_CLASS_REBATE_TAB TO BI_NRG;
GRANT EXECUTE ON AS_CONFIGURATION_TOPICS_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_CONFIGURATION_TOPICS_TAB TO BI_NRG;
GRANT EXECUTE ON AS_CONFIG_PROPERTIES_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_CONFIG_PROPERTIES_TAB TO BI_NRG;
GRANT EXECUTE ON AS_QSTNAIRE_CRRCT_ANSWRS_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_QSTNAIRE_CRRCT_ANSWRS_TAB TO BI_NRG;
GRANT EXECUTE ON AS_QSTNAIRE_SUBMISSIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_QSTNAIRE_SUBMISSIONS_TAB TO BI_NRG;
GRANT EXECUTE ON AS_QSTNAIRE_SUB_SCORES_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_QSTNAIRE_SUB_SCORES_TAB TO BI_NRG;
GRANT EXECUTE ON AS_QUESTIONNAIRES_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_QUESTIONNAIRES_TAB TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_ANSWERS_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_ANSWERS_TAB TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_PROPERTIES_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_PROPERTIES_TAB TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_QUESTIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_QUESTIONS_TAB TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_TOPICS_OBJ TO BI_NRG;
GRANT EXECUTE ON AS_SELECTED_TOPICS_TAB TO BI_NRG;
GRANT EXECUTE ON CASH_ACCOUNT_SCHEMA_DEFNS_OBJ TO BI_NRG;
GRANT EXECUTE ON CASH_ACCOUNT_SCHEMA_DEFNS_TAB TO BI_NRG;
GRANT EXECUTE ON CASH_TRANSACTION_OBJ TO BI_NRG;
GRANT EXECUTE ON CASH_TRANSACTION_TAB TO BI_NRG;
GRANT EXECUTE ON CCST_AST_SCHM_CDS_OBJ TO BI_NRG;
GRANT EXECUTE ON CCST_AST_SCHM_CDS_TAB TO BI_NRG;
GRANT EXECUTE ON CMC_SALES_PERSONS_OBJ TO BI_NRG;
GRANT EXECUTE ON CMC_SALES_PERSONS_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_ADDRESS_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_ADDRESS_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_ASSOCIATE_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_ASSOCIATE_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_EMAIL_ADDRESS_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_EMAIL_ADDRESS_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_EMPLOYEE_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_EMPLOYEE_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_FNNCL_DTL_SRC_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_FNNCL_DTL_SRC_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_LEI_ADDR_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_LEI_ADDR_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_LEI_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_LEI_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_PRIMARY_CONTACT_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_SHAREHOLDING_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_TAX_REGISTRATIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_TAX_REGISTRATIONS_TAB TO BI_NRG;
GRANT EXECUTE ON COMPANY_TELEPHONE_NUMBER_OBJ TO BI_NRG;
GRANT EXECUTE ON COMPANY_TELEPHONE_NUMBER_TAB TO BI_NRG;
GRANT EXECUTE ON CORPORATE_ACTION_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON COUNTRY_OBJ TO BI_NRG;
GRANT EXECUTE ON COUNTRY_TAB TO BI_NRG;
GRANT EXECUTE ON CPS_LEGAL_ENTITY_OBJ TO BI_NRG;
GRANT EXECUTE ON CPS_LEGAL_ENTITY_TAB TO BI_NRG;
GRANT EXECUTE ON CPS_OFFICE_OBJ TO BI_NRG;
GRANT EXECUTE ON CPS_OFFICE_TAB TO BI_NRG;
GRANT EXECUTE ON CPS_PROFIT_CENTRE_OBJ TO BI_NRG;
GRANT EXECUTE ON CPS_PROFIT_CENTRE_TAB TO BI_NRG;
GRANT EXECUTE ON CT_CARRYING_COSTS_OFFSET_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_CHARGE_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_CHARGE_REBATE_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_CHARGE_REBATE_TAB TO BI_NRG;
GRANT EXECUTE ON CT_CHARGE_TAB TO BI_NRG;
GRANT EXECUTE ON CT_COMMISSION_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_DIVIDEND_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_DIVIDEND_OPEN_TRADES_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_DIVIDEND_OPEN_TRADES_TAB TO BI_NRG;
GRANT EXECUTE ON CT_FOREX_CONVERSION_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_FOREX_CONVERSION_TAB TO BI_NRG;
GRANT EXECUTE ON CT_HEDGE_TRANSACTIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_HEDGE_TRANSACTIONS_TAB TO BI_NRG;
GRANT EXECUTE ON CT_INSTRUMENT_RELATIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_INSTRUMENT_RELATIONS_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_CC_OPEN_TRADES_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_CC_OPEN_TRADES_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_CHARGES_CD_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_CHARGES_CD_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_CMSN_CHARGE_CD_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_CRNG_COSTS_CD_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PARTNER_TRADE_SPREAD_CD_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PAYMENT_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PAYMENT_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PAYMENT_TRADING_ADJUST_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PAYMENT_TRADING_ADJUST_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PAYMENT_TRD_RBT_CD_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PAYMENT_TRD_RBT_CD_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PERIODIC_SETTLEMENT_ADJ_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PERIODIC_SETTLEMENT_ADJ_TAB TO BI_NRG;
GRANT EXECUTE ON CT_PERIODIC_SETTLEMENT_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_PERIODIC_SETTLEMENT_TAB TO BI_NRG;
GRANT EXECUTE ON CT_TRADE_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_APPRPRTNSS_INFO_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_APPRPRTNSS_INFO_TAB TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_ENQUIRY_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_ENQUIRY_TAB TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_ID_TAB TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_REGISTRATIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_REGISTRATIONS_TAB TO BI_NRG;
GRANT EXECUTE ON DATA_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON DATA_ID_TAB TO BI_NRG;
GRANT EXECUTE ON DEALER_EOD_POSITION_TAB TO BI_NRG;
GRANT EXECUTE ON DEALER_TRADE_OBJ TO BI_NRG;
GRANT EXECUTE ON DEALER_TRADE_TAB TO BI_NRG;
GRANT EXECUTE ON ECONOMIC_CALENDAR_EVENTS_OBJ TO BI_NRG;
GRANT EXECUTE ON ECONOMIC_CALENDAR_EVENTS_TAB TO BI_NRG;
GRANT EXECUTE ON ECONOMIC_CAL_EVENTS_INST_OBJ TO BI_NRG;
GRANT EXECUTE ON ECONOMIC_CAL_EVENTS_INST_TAB TO BI_NRG;
GRANT EXECUTE ON ECONOMIC_CAL_EVENTS_LANG_OBJ TO BI_NRG;
GRANT EXECUTE ON ECONOMIC_CAL_EVENTS_LANG_TAB TO BI_NRG;
GRANT EXECUTE ON EMAIL_DELIVERY_INFOS_OBJ TO BI_NRG;
GRANT EXECUTE ON EMAIL_DELIVERY_INFOS_TAB TO BI_NRG;
GRANT EXECUTE ON EMAIL_RECIPIENTS_OBJ TO BI_NRG;
GRANT EXECUTE ON EMAIL_RECIPIENTS_TAB TO BI_NRG;
GRANT EXECUTE ON EMAIL_SENT_OBJ TO BI_NRG;
GRANT EXECUTE ON EMAIL_SENT_TAB TO BI_NRG;
GRANT EXECUTE ON EMAIL_TRACKED_LINK_CLICKS_OBJ TO BI_NRG;
GRANT EXECUTE ON EMAIL_TRACKED_LINK_CLICKS_TAB TO BI_NRG;
GRANT EXECUTE ON EMPLOYMENT_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON EMPLOYMENT_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON EOD_OPEN_TRDS_MANAGED_INFO_OBJ TO BI_NRG;
GRANT EXECUTE ON EOD_OPEN_TRDS_MANAGED_INFO_TAB TO BI_NRG;
GRANT EXECUTE ON FINNC_GNRL_LDGR_MPPNGS_OBJ TO BI_NRG;
GRANT EXECUTE ON FINNC_GNRL_LDGR_MPPNGS_TAB TO BI_NRG;
GRANT EXECUTE ON FURAI_LATEST_PRICE_OBJ TO BI_NRG;
GRANT EXECUTE ON FURAI_LATEST_PRICE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_ADJUST_APPS_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_ADJUST_APPS_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_ADJUST_APPS_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_ADJUST_APPS_TAB TO BI_NRG;
GRANT EXECUTE ON GA_ADWORDS_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_ADWORDS_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHANNEL_TRACKING_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHANNEL_TRACKING_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_DM_GLS_CMP_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_DM_GLS_CMP_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_DM_GL_STR_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_DM_GL_STR_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_LV_GLS_CMP_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_LV_GLS_CMP_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_LV_GLS_STR_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_CHNNL_LV_GLS_STR_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_DM_GLS_CMP_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_DM_GLS_CMP_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_DM_GLS_STR_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_DM_GLS_STR_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_LV_GLS_CMP_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_LV_GLS_CMP_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_LV_GLS_STR_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LCTN_LV_GLS_STR_TAB TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LOCATION_TRACKING_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_AGG_LOCATION_TRACKING_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_DEMO_TRACKING_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_DEMO_TRACKING_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_5_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_GOALS_5_TAB TO BI_NRG;
GRANT EXECUTE ON GA_APP_LIVE_TRACKING_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_APP_LIVE_TRACKING_TAB TO BI_NRG;
GRANT EXECUTE ON GA_BY_MOBILE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_BY_MOBILE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_CLICKS_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_CLICKS_TAB TO BI_NRG;
GRANT EXECUTE ON GA_COST_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_COST_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_DEMO_APP_EVENTS_BY_COR_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_DEMO_APP_EVENTS_BY_COR_TAB TO BI_NRG;
GRANT EXECUTE ON GA_DEMO_APP_GCLID_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_DEMO_APP_GCLID_TAB TO BI_NRG;
GRANT EXECUTE ON GA_DM_APP_EVENTS_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_DM_APP_EVENTS_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_GOAL_BY_SOURCE_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_GOAL_BY_SOURCE_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_GOAL_BY_SOURCE_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_GOAL_BY_SOURCE_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LIVE_APP_EVENTS_BY_COR_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LIVE_APP_EVENTS_BY_COR_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LIVE_APP_GCLID_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LIVE_APP_GCLID_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LOCATION_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_LV_APP_EVENTS_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_LV_APP_EVENTS_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_OS_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_OS_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_OS_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_OS_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_OS_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_OS_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_OS_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_OS_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATHS_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATH_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PAGEPATH_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PDTSKU_BY_TRX_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PDTSKU_BY_TRX_TAB TO BI_NRG;
GRANT EXECUTE ON GA_PROFILE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_PROFILE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_REFERRALS_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_REFERRAL_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_REFERRAL_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_SOURCE_BY_PDTCAT_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_SOURCE_BY_PDTCAT_TAB TO BI_NRG;
GRANT EXECUTE ON GA_START_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_START_TAB TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_SUMMARY_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_TOPLINE_DAILY_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_TOPLINE_DAILY_TAB TO BI_NRG;
GRANT EXECUTE ON GA_TOPLINE_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_TOPLINE_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON GA_TOPLINE_MONTHLY_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_TOPLINE_MONTHLY_TAB TO BI_NRG;
GRANT EXECUTE ON GA_TYPE_VISITOR_BY_SOURCE_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_TYPE_VISITOR_BY_SOURCE_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_BY_MOBILE_TRX_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_BY_MOBILE_TRX_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_BY_MOBILE_TRX_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_BY_MOBILE_TRX_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_BY_TRX_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_BY_TRX_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_GOALS_BY_LOC_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_GOALS_BY_LOC_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_GOALS_BY_LOC_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_GOALS_BY_LOC_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_GOALS_BY_LOC_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISITS_GOALS_BY_LOC_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_5_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_5_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_6_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VISIT_GAL_BY_MBL_SRC_6_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_1_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_1_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_2_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_2_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_3_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_3_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_4_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_4_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_5_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_5_TAB TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_6_OBJ TO BI_NRG;
GRANT EXECUTE ON GA_VSTS_GLS_BY_OPTNG_SYS_6_TAB TO BI_NRG;
GRANT EXECUTE ON HEDGE_ACCOUNT_ADDRESS_OBJ TO BI_NRG;
GRANT EXECUTE ON HEDGE_ACCOUNT_ADDRESS_TAB TO BI_NRG;
GRANT EXECUTE ON HEDGE_ACCOUNT_ASSET_CLASS_OBJ TO BI_NRG;
GRANT EXECUTE ON HEDGE_ACCOUNT_ASSET_CLASS_TAB TO BI_NRG;
GRANT EXECUTE ON HEDGE_ACCOUNT_CONTACT_OBJ TO BI_NRG;
GRANT EXECUTE ON HEDGE_ACCOUNT_CONTACT_TAB TO BI_NRG;
GRANT EXECUTE ON HOLDING_COSTS_OBJ TO BI_NRG;
GRANT EXECUTE ON HOLDING_COSTS_TAB TO BI_NRG;
GRANT EXECUTE ON IDENTITY_ATTRIBUTE_OBJ TO BI_NRG;
GRANT EXECUTE ON IDENTITY_ATTRIBUTE_TAB TO BI_NRG;
GRANT EXECUTE ON IDENTITY_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON IDENTITY_ID_TAB TO BI_NRG;
GRANT EXECUTE ON IDENTITY_PERMISSION_OBJ TO BI_NRG;
GRANT EXECUTE ON IDENTITY_PERMISSION_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRMNT_PRICE_INTERVAL_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRMNT_PRICE_INTERVAL_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_FEED_SETTING_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_FEED_SETTING_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_LANGUAGES_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_LANGUAGES_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_TYPE_LANGUAGES_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_TYPE_LANGUAGES_TAB TO BI_NRG;
GRANT EXECUTE ON INST_TYPE_BROKER_MARGINS_OBJ TO BI_NRG;
GRANT EXECUTE ON INST_TYPE_BROKER_MARGINS_TAB TO BI_NRG;
GRANT EXECUTE ON INTERVALS_OBJ TO BI_NRG;
GRANT EXECUTE ON INTERVALS_TAB TO BI_NRG;
GRANT EXECUTE ON KEY_INF_DOCUMENTS_OBJ TO BI_NRG;
GRANT EXECUTE ON KEY_INF_DOCUMENTS_TAB TO BI_NRG;
GRANT EXECUTE ON LATEST_PRICES_OBJ TO BI_NRG;
GRANT EXECUTE ON LATEST_PRICES_TAB TO BI_NRG;
GRANT EXECUTE ON LATEST_PRICE_OBJ TO BI_NRG;
GRANT EXECUTE ON LATEST_PRICE_TAB TO BI_NRG;
GRANT EXECUTE ON MARGIN_DEF_NG_CFD_OBJ TO BI_NRG;
GRANT EXECUTE ON MARGIN_DEF_NG_CFD_TAB TO BI_NRG;
GRANT EXECUTE ON MARGIN_DEF_NG_SB_OBJ TO BI_NRG;
GRANT EXECUTE ON MARGIN_DEF_NG_SB_TAB TO BI_NRG;
GRANT EXECUTE ON MARGIN_TIER_OBJ TO BI_NRG;
GRANT EXECUTE ON MARGIN_TIER_TAB TO BI_NRG;
GRANT EXECUTE ON MARKET_CALENDAR_EVENT_OBJ TO BI_NRG;
GRANT EXECUTE ON MARKET_CALENDAR_EVENT_TAB TO BI_NRG;
GRANT EXECUTE ON MARKET_DATA_CHARGE_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON MARKET_DATA_CHARGE_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON MARKET_ECONOMIC_EVENT_OBJ TO BI_NRG;
GRANT EXECUTE ON MARKET_ECONOMIC_EVENT_TAB TO BI_NRG;
GRANT EXECUTE ON MARKET_IND_OBJ TO BI_NRG;
GRANT EXECUTE ON MARKET_IND_TAB TO BI_NRG;
GRANT EXECUTE ON MRGN_TRS_SUMMRY_OBJ TO BI_NRG;
GRANT EXECUTE ON MRGN_TRS_SUMMRY_TAB TO BI_NRG;
GRANT EXECUTE ON ONBOARDING_COUNTRIES_OBJ TO BI_NRG;
GRANT EXECUTE ON ONBOARDING_COUNTRIES_TAB TO BI_NRG;
GRANT EXECUTE ON ONBOARDING_LANGUAGES_OBJ TO BI_NRG;
GRANT EXECUTE ON ONBOARDING_LANGUAGES_TAB TO BI_NRG;
GRANT EXECUTE ON ORDERS_MANAGED_INFO_OBJ TO BI_NRG;
GRANT EXECUTE ON ORDERS_MANAGED_INFO_TAB TO BI_NRG;
GRANT EXECUTE ON ORDER_CLOSE_COMMENT_OBJ TO BI_NRG;
GRANT EXECUTE ON ORDER_ID_TAB TO BI_NRG;
GRANT EXECUTE ON OTQ_AVG_SPREAD_OBJ TO BI_NRG;
GRANT EXECUTE ON OTQ_AVG_SPREAD_TAB TO BI_NRG;
GRANT EXECUTE ON OTQ_DMA_SRC_ORD_HDG_TRD_OBJ TO BI_NRG;
GRANT EXECUTE ON OTQ_DMA_SRC_ORD_HDG_TRD_TAB TO BI_NRG;
GRANT EXECUTE ON OTQ_PRICE_TRAVEL_OBJ TO BI_NRG;
GRANT EXECUTE ON OTQ_PRICE_TRAVEL_TAB TO BI_NRG;
GRANT EXECUTE ON OTQ_PRICE_VOLATILITY_OBJ TO BI_NRG;
GRANT EXECUTE ON OTQ_PRICE_VOLATILITY_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_AGENT_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON PARTNER_AGENT_ID_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_ONBOARDING_CRRN_OBJ TO BI_NRG;
GRANT EXECUTE ON PARTNER_ONBOARDING_CRRN_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_TRADING_ACCOUNT_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON PARTNER_TRADING_ACCOUNT_ID_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_TRADING_ACCOUNT_OBJ TO BI_NRG;
GRANT EXECUTE ON PATTERN_REC_COUNTRIES_OBJ TO BI_NRG;
GRANT EXECUTE ON PATTERN_REC_COUNTRIES_TAB TO BI_NRG;
GRANT EXECUTE ON PATTERN_STATUSES_OBJ TO BI_NRG;
GRANT EXECUTE ON PATTERN_STATUSES_TAB TO BI_NRG;
GRANT EXECUTE ON PATTERN_TYPES_OBJ TO BI_NRG;
GRANT EXECUTE ON PATTERN_TYPES_TAB TO BI_NRG;
GRANT EXECUTE ON PAYMENT_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON PAYMENT_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON PERIODIC_SETTLEMENT_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON PERIODIC_SETTLEMENT_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_ADDRESSES_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_ADDRESSES_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_ALERT_PREFERENCES_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_ALERT_PREFERENCES_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_ALIASES_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_ALIASES_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_EMAIL_ADDRESSES_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_EMAIL_ADDRESSES_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_FNNCL_STL_SRCS_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_FNNCL_STL_SRCS_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_ID_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_MIFID_IDNTFCTNS_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_MIFID_IDNTFCTNS_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_NATIONAL_IDENTIF_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_NATIONAL_IDENTIF_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_RELATIONSHIPS_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_RELATIONSHIPS_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_STMNT_EMAIL_ADDRESS_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_STMNT_EMAIL_ADDRESS_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_TELEPHONE_NUMBERS_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_TELEPHONE_NUMBERS_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_TRADING_EXPERIENCE_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_TRADING_EXPERIENCE_TAB TO BI_NRG;
GRANT EXECUTE ON PERSON_TRADING_PROFILES_OBJ TO BI_NRG;
GRANT EXECUTE ON PERSON_TRADING_PROFILES_TAB TO BI_NRG;
GRANT EXECUTE ON PMS_OVERRIDES_OBJ TO BI_NRG;
GRANT EXECUTE ON PMS_OVERRIDES_TAB TO BI_NRG;
GRANT EXECUTE ON POSITION_OBJ TO BI_NRG;
GRANT EXECUTE ON POSITION_TAB TO BI_NRG;
GRANT EXECUTE ON POSITION_TRANSACTIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON POSITION_TRANSACTIONS_TAB TO BI_NRG;
GRANT EXECUTE ON POSITION_VALUE_OBJ TO BI_NRG;
GRANT EXECUTE ON POSITION_VALUE_TAB TO BI_NRG;
GRANT EXECUTE ON POWER_OF_ATTORNEY_OBJ TO BI_NRG;
GRANT EXECUTE ON POWER_OF_ATTORNEY_TAB TO BI_NRG;
GRANT EXECUTE ON PRC_AST_SCHM_CDS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRC_AST_SCHM_CDS_TAB TO BI_NRG;
GRANT EXECUTE ON PRDCT_STTNGS_INST_SCHM_CCY_OBJ TO BI_NRG;
GRANT EXECUTE ON PRDCT_STTNGS_INST_SCHM_CCY_TAB TO BI_NRG;
GRANT EXECUTE ON PRDCT_STTNGS_INST_SCHM_OBJ TO BI_NRG;
GRANT EXECUTE ON PRDCT_STTNGS_INST_SCHM_TAB TO BI_NRG;
GRANT EXECUTE ON PRDCT_STTNGS_KEY_OBJ TO BI_NRG;
GRANT EXECUTE ON PRDCT_STTNGS_KEY_TAB TO BI_NRG;
GRANT EXECUTE ON PRICES_BINARIES_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICES_BINARIES_TAB TO BI_NRG;
GRANT EXECUTE ON PRICE_ARRAY_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_ARRAY_TAB TO BI_NRG;
GRANT EXECUTE ON PRICE_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_SCHEMAS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_SCHEMAS_TAB TO BI_NRG;
GRANT EXECUTE ON PRICE_SCHEMA_ITEM_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_SCHEMA_ITEM_TAB TO BI_NRG;
GRANT EXECUTE ON PRICE_SUBSCRIPTION_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_SUBSCRIPTION_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON PRICE_TAB TO BI_NRG;
GRANT EXECUTE ON PRODUCTS_GSKO_OBJ TO BI_NRG;
GRANT EXECUTE ON PRODUCTS_GSKO_TAB TO BI_NRG;
GRANT EXECUTE ON PRODUCT_OBJ TO BI_NRG;
GRANT EXECUTE ON PRODUCT_SETTINGS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRODUCT_SETTINGS_TAB TO BI_NRG;
GRANT EXECUTE ON PRODUCT_SNAPSHOT_IDNTFR_OBJ TO BI_NRG;
GRANT EXECUTE ON PRODUCT_SNAPSHOT_IDNTFR_TAB TO BI_NRG;
GRANT EXECUTE ON PRODUCT_TAB TO BI_NRG;
GRANT EXECUTE ON PRODUCT_WRAPPER_OBJ TO BI_NRG;
GRANT EXECUTE ON PRODUCT_WRAPPER_SETTING_OBJ TO BI_NRG;
GRANT EXECUTE ON PRODUCT_WRAPPER_SETTING_TAB TO BI_NRG;
GRANT EXECUTE ON PRODUCT_WRAPPER_TAB TO BI_NRG;
GRANT EXECUTE ON PRSN_CMPLNC_EML_ADDRSSS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRSN_CMPLNC_EML_ADDRSSS_TAB TO BI_NRG;
GRANT EXECUTE ON PRSN_TRD_CNFRM_EML_ADDRSS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRSN_TRD_CNFRM_EML_ADDRSS_TAB TO BI_NRG;
GRANT EXECUTE ON REFERENCE_HOLDING_COSTS_OBJ TO BI_NRG;
GRANT EXECUTE ON REFERENCE_HOLDING_COSTS_TAB TO BI_NRG;
GRANT EXECUTE ON REF_CC_SCHM_CDS_ASST_OBJ TO BI_NRG;
GRANT EXECUTE ON REF_CC_SCHM_CDS_ASST_TAB TO BI_NRG;
GRANT EXECUTE ON REF_PRICE_SCHM_CDS_ASST_OBJ TO BI_NRG;
GRANT EXECUTE ON REF_PRICE_SCHM_CDS_ASST_TAB TO BI_NRG;
GRANT EXECUTE ON REGION_OBJ TO BI_NRG;
GRANT EXECUTE ON REGION_TAB TO BI_NRG;
GRANT EXECUTE ON REG_INSTR_IDENTIFICATION_OBJ TO BI_NRG;
GRANT EXECUTE ON REG_INSTR_IDENTIFICATION_TAB TO BI_NRG;
GRANT EXECUTE ON RESTRICTED_PLAT_FEAT_OBJ TO BI_NRG;
GRANT EXECUTE ON RESTRICTED_PLAT_FEAT_TAB TO BI_NRG;
GRANT EXECUTE ON REVENUE_STREAM_CONFG_OBJ TO BI_NRG;
GRANT EXECUTE ON REVENUE_STREAM_CONFG_TAB TO BI_NRG;
GRANT EXECUTE ON SESSION_ATTRIBUTE_OBJ TO BI_NRG;
GRANT EXECUTE ON SESSION_ATTRIBUTE_TAB TO BI_NRG;
GRANT EXECUTE ON SUBSCRIPTION_DETAIL_OBJ TO BI_NRG;
GRANT EXECUTE ON SUBSCRIPTION_DETAIL_PERIOD_OBJ TO BI_NRG;
GRANT EXECUTE ON SUBSCRIPTION_DETAIL_PERIOD_TAB TO BI_NRG;
GRANT EXECUTE ON SUBSCRIPTION_DETAIL_TAB TO BI_NRG;
GRANT EXECUTE ON TAX_DECLARATION_OBJ TO BI_NRG;
GRANT EXECUTE ON TAX_DECLARATION_TAB TO BI_NRG;
GRANT EXECUTE ON TAX_TREATY_RELATIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON TAX_TREATY_RELATIONS_TAB TO BI_NRG;
GRANT EXECUTE ON TA_LIMIT_LOSS_OBJ TO BI_NRG;
GRANT EXECUTE ON TA_LIMIT_LOSS_TAB TO BI_NRG;
GRANT EXECUTE ON TA_ORDER_TIME_DELAY_OBJ TO BI_NRG;
GRANT EXECUTE ON TA_ORDER_TIME_DELAY_TAB TO BI_NRG;
GRANT EXECUTE ON TA_PLL_EXEC_TRAD_CHECKS_OBJ TO BI_NRG;
GRANT EXECUTE ON TA_PLL_EXEC_TRAD_CHECKS_TAB TO BI_NRG;
GRANT EXECUTE ON TA_WRPPR_STTNG_OPN_POS_LMT_OBJ TO BI_NRG;
GRANT EXECUTE ON TA_WRPPR_STTNG_OPN_POS_LMT_TAB TO BI_NRG;
GRANT EXECUTE ON TRADE_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON TRADE_ID_TAB TO BI_NRG;
GRANT EXECUTE ON TRADING_ACCOUNT_ID_OBJ TO BI_NRG;
GRANT EXECUTE ON TRADING_ACCOUNT_ID_TAB TO BI_NRG;
GRANT EXECUTE ON TRADING_ACCOUNT_MULTIPLIER_OBJ TO BI_NRG;
GRANT EXECUTE ON TRADING_ACCOUNT_MULTIPLIER_TAB TO BI_NRG;
GRANT EXECUTE ON TRADING_RISK_CLASS_OBJ TO BI_NRG;
GRANT EXECUTE ON TRADING_RISK_CLASS_TAB TO BI_NRG;
GRANT EXECUTE ON UI_EVENT_PARAM_OBJ TO BI_NRG;
GRANT EXECUTE ON UI_EVENT_PARAM_TAB TO BI_NRG;
GRANT EXECUTE ON UTT_REASON_OBJ TO BI_NRG;
GRANT EXECUTE ON UTT_REASON_TAB TO BI_NRG;
GRANT EXECUTE ON VALUED_OPEN_TRADE_OBJ TO BI_NRG;
GRANT EXECUTE ON VALUED_OPEN_TRADE_TAB TO BI_NRG;
GRANT EXECUTE ON VALUED_FX_OPEN_TRADE_OBJ TO BI_NRG;
GRANT EXECUTE ON VALUED_FX_OPEN_TRADE_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_BASKET_REL_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_BASKET_REL_TAB TO BI_NRG;

GRANT EXECUTE ON CUSTOMER_SEGREGATIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_SEGREGATIONS_TAB TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_REG_CLSFCTNS_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_REG_CLSFCTNS_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_MC_SEGREGATIONS_OBJ TO BI_NRG;
GRANT EXECUTE ON PARTNER_MC_SEGREGATIONS_TAB TO BI_NRG;
GRANT EXECUTE ON HISTORIC_ANNL_TX_AMNTS_OBJ TO BI_NRG;
GRANT EXECUTE ON HISTORIC_ANNL_TX_AMNTS_TAB TO BI_NRG;
GRANT EXECUTE ON EVENT_STORE_ATTRIBUTES_OBJ TO BI_NRG;
GRANT EXECUTE ON EVENT_STORE_ATTRIBUTES_TAB TO BI_NRG;
GRANT EXECUTE ON EVENT_STORE_EVENT_OBJ TO BI_NRG;
GRANT EXECUTE ON EVENT_STORE_EVENT_TAB TO BI_NRG;
GRANT EXECUTE ON CT_GERMAN_TAX_DIOCESES_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_GERMAN_TAX_DIOCESES_TAB TO BI_NRG;
GRANT EXECUTE ON CT_GERMAN_TAX_STATES_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_GERMAN_TAX_STATES_TAB TO BI_NRG;
GRANT EXECUTE ON CT_GERMAN_TAX_OBJ TO BI_NRG;
GRANT EXECUTE ON CT_GERMAN_TAX_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_PRC_BNDS_KEY_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_PRC_BNDS_KEY_TAB TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_PRICE_BANDS_OBJ TO BI_NRG;
GRANT EXECUTE ON INSTRUMENT_PRICE_BANDS_TAB TO BI_NRG;   
GRANT EXECUTE ON PRICE_STREAMS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_STREAMS_TAB TO BI_NRG;    
GRANT EXECUTE ON PRICE_FEED_SCHEMA_ITEMS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_FEED_SCHEMA_ITEMS_TAB TO BI_NRG;  
GRANT EXECUTE ON PRICE_FEED_SCHEMAS_OBJ TO BI_NRG;
GRANT EXECUTE ON PRICE_FEED_SCHEMAS_TAB TO BI_NRG;
GRANT EXECUTE ON TA_EVOLUTION_OBJ TO BI_NRG;
GRANT EXECUTE ON TA_EVOLUTION_TAB TO BI_NRG;
GRANT EXECUTE ON TA_WRPR_CRYPTO_OP_PS_LMTS_OBJ TO BI_NRG;
GRANT EXECUTE ON TA_WRPR_CRYPTO_OP_PS_LMTS_TAB TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_TAX_CHURCH_RATES_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_TAX_CHURCH_RATES_TAB TO BI_NRG;
GRANT EXECUTE ON CST_TX_GER_PSL_TX_FR_ALLW_OBJ TO BI_NRG;
GRANT EXECUTE ON CST_TX_GER_PSL_TX_FR_ALLW_TAB TO BI_NRG;
GRANT EXECUTE ON GERMAN_TAX_COUPLES_REL_OBJ TO BI_NRG;
GRANT EXECUTE ON GERMAN_TAX_COUPLES_REL_TAB TO BI_NRG;  
GRANT EXECUTE ON CST_TX_GER_CPL_TX_FR_ALLW_OBJ TO BI_NRG;
GRANT EXECUTE ON CST_TX_GER_CPL_TX_FR_ALLW_TAB TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_TAX_GERMANY_OBJ TO BI_NRG;
GRANT EXECUTE ON CUSTOMER_TAX_GERMANY_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_RELATION_OBJ TO BI_NRG;
GRANT EXECUTE ON PARTNER_RELATION_TAB TO BI_NRG;
GRANT EXECUTE ON PARTNER_ONBOARDING_PLTFRM_OBJ TO BI_NRG;
GRANT EXECUTE ON PARTNER_ONBOARDING_PLTFRM_TAB TO BI_NRG;
GRANT EXECUTE ON german_dioceses_obj TO BI_NRG;
GRANT EXECUTE ON german_dioceses_tab TO BI_NRG;
GRANT EXECUTE ON customer_pro_opt_up_obj TO BI_NRG;
GRANT EXECUTE ON customer_pro_opt_up_tab TO BI_NRG;
GRANT EXECUTE ON HISTORIC_ACCOUNT_VALUE_OBJ TO BI_NRG;
GRANT EXECUTE ON HISTORIC_ACCOUNT_VALUE_TAB TO BI_NRG;
GRANT EXECUTE ON customer_reg_experience_obj TO bi_nrg;
GRANT EXECUTE ON customer_reg_experience_tab TO bi_nrg;
GRANT EXECUTE ON customer_email_preferences_obj TO bi_nrg;
GRANT EXECUTE ON customer_email_preferences_tab TO bi_nrg;  
GRANT EXECUTE ON ct_prtnr_cc_abs_np_obj TO bi_nrg;
GRANT EXECUTE ON ct_prtnr_cc_abs_np_tab TO bi_nrg;
GRANT EXECUTE ON ct_prtnr_cc_abs_obj TO bi_nrg;
GRANT EXECUTE ON ct_prtnr_cc_abs_tab TO bi_nrg;
GRANT EXECUTE ON inst_schema_metadata_obj TO bi_nrg;
GRANT EXECUTE ON inst_schema_metadata_tab TO bi_nrg;
GRANT EXECUTE ON ct_payment_trd_rbt_ai_obj TO bi_nrg;
GRANT EXECUTE ON ct_payment_trd_rbt_ai_tab TO bi_nrg;
GRANT EXECUTE ON bi_ods.german_tax_roll_losses_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.german_tax_roll_losses_tab TO bi_nrg;
GRANT EXECUTE ON bi_ods.ta_give_up_profiles_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.ta_give_up_profiles_tab TO bi_nrg;

GRANT EXECUTE ON bi_ods.hstrc_acnt_vl_scndry_free_eq_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.hstrc_acnt_vl_scndry_free_eq_tab TO bi_nrg;

GRANT SELECT ON bi_ods.customer_reg_clsfctns TO BI_MVIEWS;
GRANT SELECT ON bi_ods.customer_segregations TO BI_DQ;
GRANT SELECT ON bi_ods.customer_segregations_h TO BI_DQ;
GRANT SELECT ON bi_ods.customer_reg_clsfctns TO BI_DQ;
GRANT SELECT ON bi_ods.customer_reg_clsfctns_h TO BI_DQ;
GRANT SELECT ON bi_ods.partner_mc_segregations TO BI_DQ;
GRANT SELECT ON bi_ods.partner_mc_segregations_h TO BI_DQ;

GRANT SELECT ON bi_ods.customer_segregations TO ETL_READER;
GRANT SELECT ON bi_ods.customer_segregations_h TO ETL_READER;
GRANT SELECT ON bi_ods.customer_reg_clsfctns TO ETL_READER;
GRANT SELECT ON bi_ods.customer_reg_clsfctns_h TO ETL_READER;
GRANT SELECT ON bi_ods.partner_mc_segregations TO ETL_READER;
GRANT SELECT ON bi_ods.partner_mc_segregations_h TO ETL_READER;

GRANT SELECT ON bi_ods.customer_segregations TO ETL_WRITER_PUBLIC;
GRANT SELECT ON bi_ods.customer_segregations_h TO ETL_WRITER_PUBLIC;
GRANT SELECT ON bi_ods.customer_reg_clsfctns TO ETL_WRITER_PUBLIC;
GRANT SELECT ON bi_ods.customer_reg_clsfctns_h TO ETL_WRITER_PUBLIC;
GRANT SELECT ON bi_ods.partner_mc_segregations TO ETL_WRITER_PUBLIC;
GRANT SELECT ON bi_ods.partner_mc_segregations_h TO ETL_WRITER_PUBLIC;

GRANT SELECT ON bi_ods.customer_segregations TO BI_READER_PUBLIC;
GRANT SELECT ON bi_ods.customer_segregations_h TO BI_READER_PUBLIC;
GRANT SELECT ON bi_ods.customer_reg_clsfctns TO BI_READER_PUBLIC;
GRANT SELECT ON bi_ods.customer_reg_clsfctns_h TO BI_READER_PUBLIC;
GRANT SELECT ON bi_ods.partner_mc_segregations TO BI_READER_PUBLIC;
GRANT SELECT ON bi_ods.partner_mc_segregations_h TO BI_READER_PUBLIC;

GRANT EXECUTE ON bi_ods.customer_tax_germany_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.customer_tax_germany_tab TO bi_nrg;

GRANT EXECUTE ON bi_ods.alert_preferences_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.alert_preferences_tab TO bi_nrg;
GRANT EXECUTE ON bi_ods.alert_suspensions_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.alert_suspensions_tab TO bi_nrg;
GRANT EXECUTE ON bi_ods.addtnl_allwbl_fx_bal_ccys_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.addtnl_allwbl_fx_bal_ccys_tab TO bi_nrg;

GRANT EXECUTE ON bi_ods.payment_bank_reg_obj TO bi_nrg;

GRANT EXECUTE ON company_add_trd_cntry_obj TO bi_nrg;
GRANT EXECUTE ON company_add_trd_cntry_tab TO bi_nrg;
GRANT EXECUTE ON ct_fx_profit_loss_obj TO bi_nrg;
GRANT EXECUTE ON ct_fx_profit_loss_tab TO bi_nrg;
GRANT EXECUTE ON swap_points_obj TO bi_nrg;
GRANT EXECUTE ON swap_points_tab TO bi_nrg;

GRANT EXECUTE ON ct_portfolio_profit_loss_obj TO bi_nrg;
GRANT EXECUTE ON ct_hedge_cc_obj TO bi_nrg;
GRANT EXECUTE ON ct_hedge_cc_tab TO bi_nrg;                          

GRANT EXECUTE ON bi_ods.customer_loyalty_schemes_obj TO bi_nrg;
GRANT EXECUTE ON bi_ods.customer_loyalty_schemes_tab TO bi_nrg;

GRANT EXECUTE ON ct_payment_alert_obj TO bi_nrg;
GRANT EXECUTE ON ct_payment_alert_tab TO bi_nrg;
GRANT EXECUTE ON ct_payment_audit_obj TO bi_nrg;
GRANT EXECUTE ON ct_payment_audit_tab TO bi_nrg;

GRANT EXECUTE ON ct_sec_ccy_conversion_obj TO bi_nrg;

GRANT EXECUTE ON ct_investments_client_trade_obj TO bi_nrg;

GRANT EXECUTE ON customer_isa_allowance_adjustments_obj TO bi_nrg;
GRANT EXECUTE ON customer_isa_allowance_adjustments_tab TO bi_nrg;

GRANT EXECUTE ON customer_isa_funding_allowance_obj TO bi_nrg;
GRANT EXECUTE ON customer_isa_funding_allowance_tab TO bi_nrg;

GRANT EXECUTE ON trade_tax_obj TO bi_nrg;
GRANT EXECUTE ON trade_tax_tab TO bi_nrg;

GRANT EXECUTE ON german_tax_statements_obj TO bi_nrg;
GRANT EXECUTE ON german_tax_statements_tab TO bi_nrg;

GRANT EXECUTE ON ct_sec_ccy_conversion_obj TO bi_nrg;

GRANT EXECUTE ON transfer_requests_pstns_obj TO bi_nrg;
GRANT EXECUTE ON transfer_requests_pstns_tab TO bi_nrg;
GRANT EXECUTE ON transfer_requests_answr_obj TO bi_nrg;
GRANT EXECUTE ON transfer_requests_answr_tab TO bi_nrg;
GRANT EXECUTE ON transfer_requests_email_obj TO bi_nrg;
GRANT EXECUTE ON transfer_requests_email_tab TO bi_nrg;
GRANT EXECUTE ON transfer_requests_obj TO bi_nrg;
GRANT EXECUTE ON transfer_requests_tab TO bi_nrg;

GRANT EXECUTE ON secondary_currencies_obj TO bi_nrg;
GRANT EXECUTE ON secondary_currencies_tab TO bi_nrg;

GRANT EXECUTE ON trade_settlements_proc_obj TO bi_nrg;
GRANT EXECUTE ON trade_settlements_proc_tab TO bi_nrg;
GRANT EXECUTE ON trade_settlements_obj TO bi_nrg;
GRANT EXECUTE ON trade_settlements_tab TO bi_nrg;

GRANT EXECUTE ON customer_marketing_preferences_obj TO bi_nrg;
GRANT EXECUTE ON customer_marketing_preferences_tab TO bi_nrg;

GRANT EXECUTE ON ct_corporate_action_cash_dividend_obj TO bi_nrg;
GRANT EXECUTE ON ct_corporate_action_cash_dividend_tab TO bi_nrg;
GRANT EXECUTE ON ct_liquidity_risk_management_conversion_obj TO bi_nrg;
GRANT EXECUTE ON ct_liquidity_risk_management_conversion_tab TO bi_nrg;
GRANT EXECUTE ON ct_card_deposit_obj TO bi_nrg;
GRANT EXECUTE ON ct_card_deposit_tab TO bi_nrg;
GRANT EXECUTE ON ct_corporate_action_ticket_obj TO bi_nrg;
GRANT EXECUTE ON ct_corporate_action_ticket_tab TO bi_nrg;
GRANT EXECUTE ON ct_client_transfer_obj TO bi_nrg;
GRANT EXECUTE ON ct_client_transfer_tab TO bi_nrg;
GRANT EXECUTE ON ct_carrying_costs_offset_tab TO bi_nrg;

GRANT EXECUTE ON hstrc_secondary_currency_withdrawable_amounts_obj TO bi_nrg;
GRANT EXECUTE ON hstrc_secondary_currency_withdrawable_amounts_tab TO bi_nrg;

GRANT EXECUTE ON settled_positions_obj TO bi_nrg;
GRANT EXECUTE ON settled_positions_tab TO bi_nrg;

GRANT EXECUTE ON funding_amount_accounts_obj TO bi_nrg;
GRANT EXECUTE ON funding_amount_accounts_tab TO bi_nrg;

GRANT EXECUTE ON funding_amounts_obj TO bi_nrg;
GRANT EXECUTE ON funding_amounts_tab TO bi_nrg;
GRANT EXECUTE ON ct_corporate_action_rights_issue_obj TO bi_nrg;
GRANT EXECUTE ON ct_corporate_action_rights_issue_tab TO bi_nrg;
GRANT EXECUTE ON ct_compensated_trade_loss_obj TO bi_nrg;
GRANT EXECUTE ON ct_compensated_trade_loss_tab TO bi_nrg;
GRANT EXECUTE ON terms_of_business_obj TO bi_nrg;
GRANT EXECUTE ON terms_of_business_tab TO bi_nrg;
GRANT EXECUTE ON regulatory_acceptances_obj TO bi_nrg;
GRANT EXECUTE ON regulatory_acceptances_tab TO bi_nrg;
GRANT EXECUTE ON customer_price_plan_obj TO BI_NRG;
GRANT EXECUTE ON customer_price_plan_tab TO BI_NRG;