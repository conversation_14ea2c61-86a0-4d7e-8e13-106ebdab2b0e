-- ===================================================================================
-- apply_ods.sql
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Wrapper script to update BI_ODS schema
--
-- Notes:
-- ------
--
--     Oracle 10g/11g environments
--
-- Modifications:
-- --------------
--
--     Date         Modified By       Ref     Action
--     ----------   ---------------   -----   ----------------------------------------
--     13/12/2022   Patrick <PERSON>  1.0     Creation
--
-- -----------------------------------------------------------------------------------

DEFINE application_name=ods
DEFINE application_version=518157
DEFINE build_number=1

DEFINE home_path=/dba/releases
DEFINE src=&&home_path/&&application_name._&&application_version/rollforward
DEFINE logs=&&home_path/logs

SET LINESIZE 200
SET PAGESIZE 50

COLUMN start_time NEW_VALUE gv_start_time
COLUMN end_time   NEW_VALUE gv_end_time
COLUMN db_name    NEW_VALUE gv_db_name

SELECT TO_CHAR(SYSDATE,'ddmonyyhh24miss') AS start_time
FROM   dual;

SELECT SYS_CONTEXT('USERENV', 'DB_NAME') AS db_name
FROM   dual;

ACCEPT dba PROMPT "Please enter dba performing release: "
ACCEPT dba_password PROMPT "Please enter password for dba performing release: " HIDE

SPOOL &logs/RollForward_&build_number._&&application_name._&&application_version._&gv_db_name._&gv_start_time..log

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Get All Invalid Objects                                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

SELECT owner,
       object_type,
       SUBSTR(object_name, 1, 30) AS object_name,
       status
FROM   dba_objects
WHERE  status != 'VALID'
ORDER BY owner,
         object_type,
         object_name;

-- -----------------------------
-- START OF CUSTOMIZABLE SECTION
-- -----------------------------
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ BI_ODS actions via ORADBA                                    		   +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

DEFINE ods_username       		    = "bi_ods"

CONNECT &&dba[&&ods_username]/&&dba_password

SHOW USER

WHENEVER SQLERROR CONTINUE

ACCEPT gv_continue_y_n PROMPT "Hit enter if OK to continue or hit CTRL-D to exit script"

PROMPT *****************************
PROMPT UPDATE PAYMENT BANK REGISTRATIONS
PROMPT *****************************
PROMPT

@"&src/ods/tables/payment_bank_reg_backup.sql"
@"&src/ods/tables/update_payment_bank_reg.sql"

SHOW ERRORS
PROMPT


PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Insert into RELEASE_LOG table as ORADBA                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

ACCEPT gv_continue_y_n PROMPT "Hit enter if OK to continue or hit CTRL-D to exit script"

INSERT INTO release_log
(
    application_name,
    version_number,
    build_number,
    rollforward_date,
    rollforward_by
)
VALUES
(
    '&&application_name',
    '&&application_version',
    '&&build_number',
    CURRENT_TIMESTAMP,
    '&&dba'
);

COMMIT;

-- ---------------------------
-- END OF CUSTOMIZABLE SECTION
-- ---------------------------

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Get All Invalid Objects                                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT owner,
       object_type,
       SUBSTR(object_name, 1, 30) AS object_name,
       status
FROM   dba_objects
WHERE  status != 'VALID'
ORDER BY owner,
         object_type,
         object_name;

SELECT TO_CHAR(SYSDATE,'ddmonyyhh24miss') AS end_time FROM dual;

SPOOL OFF