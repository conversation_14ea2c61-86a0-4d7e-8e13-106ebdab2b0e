------------------------------
-- ALTER CASH TXN TABLES
------------------------------
ALTER TABLE bi_ods.ct_portfolio_swaps       SET UNUSED (period_settlement_time);
ALTER TABLE bi_ods.ct_periodic_settlements  SET UNUSED (margin_amount_account_ccy,margin_offset);
ALTER TABLE bi_ods.ct_carrying_costs_offset	SET UNUSED (unrealized_ProfitLoss,total_open_trade_amnt_acct_ccy,offset_base_amount,carryingcostsoffset_totamnt,margin_offset_total_amount);
ALTER TABLE bi_ods.ct_portfolio_swaps_h       SET UNUSED (period_settlement_time);
ALTER TABLE bi_ods.ct_periodic_settlements_h  SET UNUSED (margin_amount_account_ccy,margin_offset);
ALTER TABLE bi_ods.ct_carrying_costs_offset_h	SET UNUSED (unrealized_ProfitLoss,total_open_trade_amnt_acct_ccy,offset_base_amount,carryingcostsoffset_totamnt,margin_offset_total_amount);

------------------------------
-- CREATE CUST LOYALTY TABLES
------------------------------
DROP TABLE bi_ods.customer_loyalty_schemes;
DROP TABLE bi_ods.customer_loyalty_schemes_h;

------------------------------
-- ALTER POSITIONS TABLES
------------------------------
ALTER TABLE bi_ods.eod_open_trades   SET UNUSED ( margin_percentage );
ALTER TABLE bi_ods.eod_open_trades_h SET UNUSED ( margin_percentage );

ALTER TABLE bi_ods.anytime_positions   SET UNUSED ( margin_percentage );
ALTER TABLE bi_ods.anytime_positions_h SET UNUSED ( margin_percentage );

------------------------------
-- ALTER POSITION TRANS TABLES
------------------------------
ALTER TABLE bi_ods.position_transactions   SET UNUSED ( open_trade_margin_percentage );
ALTER TABLE bi_ods.position_transactions_h SET UNUSED ( open_trade_margin_percentage );

------------------------------
-- ALTER ORDERS TABLES
------------------------------
ALTER TABLE orders   SET UNUSED ( margin_percentage );
ALTER TABLE orders_h SET UNUSED ( margin_percentage );

ALTER TABLE	orders_close_comment SET UNUSED ( eval_fxr_prim_to_ta_prim_ccy	                );
ALTER TABLE	orders_close_comment SET UNUSED ( eval_fxr_sec_to_ta_prim_ccy	                );
ALTER TABLE	orders_close_comment SET UNUSED ( fx_trade_date	                                );
ALTER TABLE	orders_close_comment SET UNUSED ( value_date	                                );
ALTER TABLE	orders_close_comment SET UNUSED ( fx_tenor	                                    );
ALTER TABLE	orders_close_comment SET UNUSED ( quote_key	                                    );
ALTER TABLE	orders_close_comment SET UNUSED ( is_swap_points_quoted_set	                    );
ALTER TABLE	orders_close_comment SET UNUSED ( swap_points_quoted_id	                        );
ALTER TABLE	orders_close_comment SET UNUSED ( used_fx_rate_symbol	                        );

ALTER TABLE	orders_close_comment_h SET UNUSED ( eval_fxr_prim_to_ta_prim_ccy	                );
ALTER TABLE	orders_close_comment_h SET UNUSED ( eval_fxr_sec_to_ta_prim_ccy	                    );
ALTER TABLE	orders_close_comment_h SET UNUSED ( fx_trade_date	                                );
ALTER TABLE	orders_close_comment_h SET UNUSED ( value_date	                                    );
ALTER TABLE	orders_close_comment_h SET UNUSED ( fx_tenor	                                    );
ALTER TABLE	orders_close_comment_h SET UNUSED ( quote_key	                                    );
ALTER TABLE	orders_close_comment_h SET UNUSED ( is_swap_points_quoted_set	                    );
ALTER TABLE	orders_close_comment_h SET UNUSED ( swap_points_quoted_id	                        );
ALTER TABLE	orders_close_comment_h SET UNUSED ( used_fx_rate_symbol	                            );

------------------------------
-- ALTER IDENTITIES TABLES
------------------------------
ALTER TABLE BI_ODS.identities   SET UNUSED ( is_2fa_enabled );
ALTER TABLE BI_ODS.identities_h SET UNUSED ( is_2fa_enabled );