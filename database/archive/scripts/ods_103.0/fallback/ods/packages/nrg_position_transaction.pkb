CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_position_transaction
AS
  -- ===================================================================================
  -- NRG_POSITION_TRANSACTION
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the Position Transactions model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Position Transaction Deleted Before Update and After Insert
  --     -20004    Default Exception
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --   ----------   ---------------   -----   ----------------------------------------
  --     23/05/2012   Mark Gornicki      1.0    Creation
  --     03/07/2012   Sanket Mittal      1.1    Added the parameter p_record_source
  --     21/11/2012   Shanavaz Malayodu  1.2    Modified the put_position call to pass p_open_trade_id instead of p_trade_id
  --     14/02/2013   Sanket Mittal      1.3    Updated the formula for normalised open value in trading currency
  --     18/03/2013   Sanket Mittal      1.4    Updated for P2 Changes
  --     01/07/2013   Prachi Shah        1.5    Removed case statement for Product Point Multiplier, Product Fractional Part Ratio
  --     23/08/2013   Prachi Shah        1.6    Changed the text for margin type and normalised margin type to PERCENTAGE
  --     23/08/2013   Prachi Shah        1.6    Updated get_position_transaction_ids to use udpate_time instead of creation_time
  --     31/12/2013   Prachi Shah        1.7    BER-781: Modified eod_position.put_position procedure call
  --     16/01/2014   Adam Krasnicki     1.8    Parameter record source changed to put_position instead for NULL
  --     19/02/2014   Adam Krasnicki     1.9    IN Get_position_transaction_ids the creation_time used instead of update_time
  --     07/03/2014   Adam Krasnicki     1.9    Call of put_positions only for non-MODIFIED transaction type in put_position_transaction
  --     18/07/2014   Adam Krasnicki     2.0    p_load_latest_positions parameter added to put_position_transaction
  --     08/05/2015   Adam Krasnicki     2.1    put_position_transaction: 3 new params:p_execution_type, p_opening_trade_instrmnt_price, p_opening_trade_strike_price
  --     16/06/2015   Adam Krasnicki     2.1    put_position call, p_direction_multiplier parameter added, set to NULL, as it is used for speedbets, for other positions it is calculated in put_position procedure
  --     01/10/2015   Sanket Mittal      2.2    added new parameters p_trading_scope and p_alloc_trading_risk_schema on put_position_transaction
  --     20/01/2016   Sanket Mittal      2.3    Added new parameters for binaries:bo_type, settle_time, tenor, strike_price, strike_price_additional, tenor_start_time
  --     09/05/2016   Adam Krasnicki     2.4    put_position_transactions procedure added (it takes an array of the position transactions)
  --     08/06/2016   Sanket Mittal      2.5    put_position_transactions added parameters BER-2585 SWS 34 - Data Contract Changes - PositionTransaction
  --     28/07/2016   Sanket Mittal      2.6    create_session_stub to include channel id BER-2778
  --     01/09/2016   Sanket Mittal      2.7    BER-2863 KNOCKOUT Normalisation Changes
  --     01/09/2016   Sanket Mittal      2.8    BER-2872 Knockouts Normalisation - Add the STRIKE_PRICE
  --     05/04/2018   Deepak Rajurkar    2.9    BER-4441 Add Column FORCED_MARGIN_FX_RATE to table BI_ODS.POSITION_TRANSACTIONS
  --     19/03/2019   Patrick dinwiddy   3.0    BER-5028 new attributes
  --     22/04/2020   Patrick Dinwiddy   3.1    JCS-11643
  --     04/09/2020   Patrick Dinwiddy   3.2    JCS-13251
  --     25/09/2020   Patrick Dinwiddy   3.3    JCS-13754
  --
  -- ==================================================================================

  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '3.3';
  gc_cr                 CONSTANT VARCHAR2(1) := CHR(10);
  gc_true               CONSTANT PLS_INTEGER := 1;
  gc_false              CONSTANT PLS_INTEGER := 0;
  gc_default_timestamp  CONSTANT TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');


  -- ===================================================================================
  -- PRIVATE MODULES
  -- ===================================================================================

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the position transaction
  --     record
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --      p_old_pstn_trnsctn_record       This is the old version of the trade transaction
  --      p_effective_end_timestamp       This is the end time for the record
  --      p_action                        Type of operation performed
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history (p_old_pstn_trnsctn_record position_transactions%ROWTYPE,
                         p_effective_end_timestamp position_transactions_h.effective_end_timestamp%TYPE,
                         p_action position_transactions_h.action%TYPE)
  IS

  BEGIN
    INSERT INTO position_transactions_h (
                                         transaction_id,
                                         open_trade_id,
                                         trade_id,
                                         booking_number,
                                         platform,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         effective_end_timestamp,
                                         action_timestamp,
                                         action,
                                         publish_time,
                                         event_time,
                                         version_number,
                                         is_deleted,
                                         creation_time,
                                         creation_identity_token,
                                         crtn_on_bhlf_of_idntty_tkn,
                                         update_time,
                                         update_identity_token,
                                         updt_on_bhlf_of_idntty_tkn,
                                         session_key,
                                         trading_account_id,
                                         trading_account_type,
                                         trading_account_function,
                                         trading_account_codifier,
                                         trdng_accnt_primary_currency,
                                         order_id,
                                         product_instrument_code,
                                         product_wrapper_code,
                                         --mm_instrument_id,
                                         --product_schema_code,
                                         product_generation,
                                         product_point_multiplier,
                                         product_currency,
                                         --product_financing_ratio_max,
                                         product_frctnl_part_ratio,
                                         is_ccy_in_fractional_parts,
                                         channel_id,
                                         request_id,
                                         business_date,
                                         reporting_date,
                                         --cstm_info_vrtl_prtfl_cd,
                                         transaction_time,
                                         transaction_type,
                                         transaction_refcodifier,
                                         transaction_refcode,
                                         is_automatically_rolled,
                                         position_direction,
                                         --position_financing_ratio,
                                         open_trade_quantity,
                                         --position_quantity_currency,
                                         quantity_designator,
                                         normalised_pstn_qntty,
                                         normalised_pstn_qntty_crrncy,
                                         --position_margin,
                                         --position_margin_currency,
                                         open_trade_amount,
                                         open_trade_amount_currency,
                                         --position_margin_in_tapc,
                                         open_trade_amount_in_tapc,
                                         open_trade_price,
                                         --open_trade_margin_fx_rate,
                                         opening_trade_amount_fx_rate,
                                         open_trade_time,
                                         --prev_position_quantity,
                                         --prev_position_margin,
                                         --prev_position_amount,
                                         --prev_position_margin_in_tapc,
                                         --prev_position_amount_in_tapc,
                                         --prev_pstn_financing_ratio,
                                         --prev_prdct_point_multiplier,
                                         record_source,
                                         cash_transaction_seq,
                                         Execution_Type,
                                         Opening_Trade_Instrument_Price,
                                         Opening_Trade_Strike_Price,
                                         binary_type,
                                         settle_time,
                                         tenor,
                                         strike_price,
                                         strike_price_additional,
                                         tenor_start_time,
                                         binary_result,
                                         alloc_instrument_schema,
                                         open_trade_instrmnt_amount ,
                                         forced_margin_fx_rate,
                                         opn_accrd_trnvr_in_accnt_ccy,
                                         is_pair_ccy_in_frctnl_prts
                                        )
                                 VALUES (
                                         p_old_pstn_trnsctn_record.transaction_id,
                                         p_old_pstn_trnsctn_record.open_trade_id,
                                         p_old_pstn_trnsctn_record.trade_id,
                                         p_old_pstn_trnsctn_record.booking_number,
                                         p_old_pstn_trnsctn_record.platform,
                                         p_old_pstn_trnsctn_record.logical_load_timestamp,
                                         p_old_pstn_trnsctn_record.created_by,
                                         p_old_pstn_trnsctn_record.create_timestamp,
                                         p_old_pstn_trnsctn_record.updated_by,
                                         p_old_pstn_trnsctn_record.update_timestamp,
                                         p_old_pstn_trnsctn_record.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         SYSTIMESTAMP,
                                         p_action,
                                         p_old_pstn_trnsctn_record.publish_time,
                                         p_old_pstn_trnsctn_record.event_time,
                                         p_old_pstn_trnsctn_record.version_number,
                                         p_old_pstn_trnsctn_record.is_deleted,
                                         p_old_pstn_trnsctn_record.creation_time,
                                         p_old_pstn_trnsctn_record.creation_identity_token,
                                         p_old_pstn_trnsctn_record.crtn_on_bhlf_of_idntty_tkn,
                                         p_old_pstn_trnsctn_record.update_time,
                                         p_old_pstn_trnsctn_record.update_identity_token,
                                         p_old_pstn_trnsctn_record.updt_on_bhlf_of_idntty_tkn,
                                         p_old_pstn_trnsctn_record.session_key,
                                         p_old_pstn_trnsctn_record.trading_account_id,
                                         p_old_pstn_trnsctn_record.trading_account_type,
                                         p_old_pstn_trnsctn_record.trading_account_function,
                                         p_old_pstn_trnsctn_record.trading_account_codifier,
                                         p_old_pstn_trnsctn_record.trdng_accnt_primary_currency,
                                         p_old_pstn_trnsctn_record.order_id,
                                         p_old_pstn_trnsctn_record.product_instrument_code,
                                         p_old_pstn_trnsctn_record.product_wrapper_code,
                                         --p_old_pstn_trnsctn_record.mm_instrument_id,
                                         --p_old_pstn_trnsctn_record.product_schema_code,
                                         p_old_pstn_trnsctn_record.product_generation,
                                         p_old_pstn_trnsctn_record.product_point_multiplier,
                                         p_old_pstn_trnsctn_record.product_currency,
                                         --p_old_pstn_trnsctn_record.product_financing_ratio_max,
                                         p_old_pstn_trnsctn_record.product_frctnl_part_ratio,
                                         p_old_pstn_trnsctn_record.is_ccy_in_fractional_parts,
                                         p_old_pstn_trnsctn_record.channel_id,
                                         p_old_pstn_trnsctn_record.request_id,
                                         p_old_pstn_trnsctn_record.business_date,
                                         p_old_pstn_trnsctn_record.reporting_date,
                                         --p_old_pstn_trnsctn_record.cstm_info_vrtl_prtfl_cd,
                                         p_old_pstn_trnsctn_record.transaction_time,
                                         p_old_pstn_trnsctn_record.transaction_type,
                                         p_old_pstn_trnsctn_record.transaction_refcodifier,
                                         p_old_pstn_trnsctn_record.transaction_refcode,
                                         p_old_pstn_trnsctn_record.is_automatically_rolled,
                                         p_old_pstn_trnsctn_record.position_direction,
                                         --p_old_pstn_trnsctn_record.position_financing_ratio,
                                         p_old_pstn_trnsctn_record.open_trade_quantity,
                                         --p_old_pstn_trnsctn_record.position_quantity_currency,
                                         p_old_pstn_trnsctn_record.quantity_designator,
                                         p_old_pstn_trnsctn_record.normalised_pstn_qntty,
                                         p_old_pstn_trnsctn_record.normalised_pstn_qntty_crrncy,
                                         --p_old_pstn_trnsctn_record.position_margin,
                                         --p_old_pstn_trnsctn_record.position_margin_currency,
                                         p_old_pstn_trnsctn_record.open_trade_amount,
                                         p_old_pstn_trnsctn_record.open_trade_amount_currency,
                                         --p_old_pstn_trnsctn_record.position_margin_in_tapc,
                                         p_old_pstn_trnsctn_record.open_trade_amount_in_tapc,
                                         p_old_pstn_trnsctn_record.open_trade_price,
                                         --p_old_pstn_trnsctn_record.open_trade_margin_fx_rate,
                                         p_old_pstn_trnsctn_record.opening_trade_amount_fx_rate,
                                         p_old_pstn_trnsctn_record.open_trade_time,
                                         --p_old_pstn_trnsctn_record.prev_position_quantity,
                                         --p_old_pstn_trnsctn_record.prev_position_margin,
                                         --p_old_pstn_trnsctn_record.prev_position_amount,
                                         --p_old_pstn_trnsctn_record.prev_position_margin_in_tapc,
                                         --p_old_pstn_trnsctn_record.prev_position_amount_in_tapc,
                                         --p_old_pstn_trnsctn_record.prev_pstn_financing_ratio,
                                         --p_old_pstn_trnsctn_record.prev_prdct_point_multiplier,
                                         p_old_pstn_trnsctn_record.record_source,
                                         p_old_pstn_trnsctn_record.cash_transaction_seq,
                                         p_old_pstn_trnsctn_record.Execution_Type,
                                         p_old_pstn_trnsctn_record.Opening_Trade_Instrument_Price,
                                         p_old_pstn_trnsctn_record.Opening_Trade_Strike_Price,
                                         p_old_pstn_trnsctn_record.binary_type,
                                         p_old_pstn_trnsctn_record.settle_time,
                                         p_old_pstn_trnsctn_record.tenor,
                                         p_old_pstn_trnsctn_record.strike_price,
                                         p_old_pstn_trnsctn_record.strike_price_additional,
                                         p_old_pstn_trnsctn_record.tenor_start_time,
                                         p_old_pstn_trnsctn_record.binary_result,
                                         p_old_pstn_trnsctn_record.alloc_instrument_schema,
                                         p_old_pstn_trnsctn_record.open_trade_instrmnt_amount,
                                         p_old_pstn_trnsctn_record.forced_margin_fx_rate,
                                         p_old_pstn_trnsctn_record.opn_accrd_trnvr_in_accnt_ccy,
                                         p_old_pstn_trnsctn_record.is_pair_ccy_in_frctnl_prts
                                        );
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
    --
    --
    -- This would occur in the case when the same message is replayed
    --
    -- Possible ways in which message gets replayed
    -- Request Response
    -- Upstream publishes for manual updates
    -- Message replayed
    --
      UPDATE position_transactions_h
      SET
          logical_load_timestamp           = p_old_pstn_trnsctn_record.logical_load_timestamp,
          updated_by                       = p_old_pstn_trnsctn_record.updated_by,
          update_timestamp                 = p_old_pstn_trnsctn_record.update_timestamp,
          open_trade_id                    = p_old_pstn_trnsctn_record.open_trade_id,
          trade_id                         = p_old_pstn_trnsctn_record.trade_id,
          booking_number                   = p_old_pstn_trnsctn_record.booking_number,
          publish_time                     = p_old_pstn_trnsctn_record.publish_time,
          event_time                       = p_old_pstn_trnsctn_record.event_time,
          version_number                   = p_old_pstn_trnsctn_record.version_number,
          is_deleted                       = p_old_pstn_trnsctn_record.is_deleted,
          creation_time                    = p_old_pstn_trnsctn_record.creation_time,
          creation_identity_token          = p_old_pstn_trnsctn_record.creation_identity_token,
          crtn_on_bhlf_of_idntty_tkn       = p_old_pstn_trnsctn_record.crtn_on_bhlf_of_idntty_tkn,
          update_time                      = p_old_pstn_trnsctn_record.update_time,
          update_identity_token            = p_old_pstn_trnsctn_record.update_identity_token,
          updt_on_bhlf_of_idntty_tkn       = p_old_pstn_trnsctn_record.updt_on_bhlf_of_idntty_tkn,
          session_key                      = p_old_pstn_trnsctn_record.session_key,
          trading_account_id               = p_old_pstn_trnsctn_record.trading_account_id,
          trading_account_type             = p_old_pstn_trnsctn_record.trading_account_type,
          trading_account_function         = p_old_pstn_trnsctn_record.trading_account_function,
          trading_account_codifier         = p_old_pstn_trnsctn_record.trading_account_codifier,
          trdng_accnt_primary_currency     = p_old_pstn_trnsctn_record.trdng_accnt_primary_currency,
          order_id                         = p_old_pstn_trnsctn_record.order_id,
          product_instrument_code          = p_old_pstn_trnsctn_record.product_instrument_code,
          product_wrapper_code             = p_old_pstn_trnsctn_record.product_wrapper_code,
          --mm_instrument_id                 = p_old_pstn_trnsctn_record.mm_instrument_id,
          --product_schema_code              = p_old_pstn_trnsctn_record.product_schema_code,
          product_generation               = p_old_pstn_trnsctn_record.product_generation,
          product_point_multiplier         = p_old_pstn_trnsctn_record.product_point_multiplier,
          product_currency                 = p_old_pstn_trnsctn_record.product_currency,
          --product_financing_ratio_max      = p_old_pstn_trnsctn_record.product_financing_ratio_max,
          product_frctnl_part_ratio        = p_old_pstn_trnsctn_record.product_frctnl_part_ratio,
          is_ccy_in_fractional_parts      = p_old_pstn_trnsctn_record.is_ccy_in_fractional_parts,
          channel_id                       = p_old_pstn_trnsctn_record.channel_id,
          request_id                       = p_old_pstn_trnsctn_record.request_id,
          business_date                    = p_old_pstn_trnsctn_record.business_date,
          reporting_date                   = p_old_pstn_trnsctn_record.reporting_date,
          --cstm_info_vrtl_prtfl_cd          = p_old_pstn_trnsctn_record.cstm_info_vrtl_prtfl_cd,
          transaction_time                 = p_old_pstn_trnsctn_record.transaction_time,
          transaction_type                 = p_old_pstn_trnsctn_record.transaction_type,
          transaction_refcodifier          = p_old_pstn_trnsctn_record.transaction_refcodifier,
          transaction_refcode              = p_old_pstn_trnsctn_record.transaction_refcode,
          is_automatically_rolled          = p_old_pstn_trnsctn_record.is_automatically_rolled,
          position_direction               = p_old_pstn_trnsctn_record.position_direction,
          --position_financing_ratio         = p_old_pstn_trnsctn_record.position_financing_ratio,
          open_trade_quantity              = p_old_pstn_trnsctn_record.open_trade_quantity,
          --position_quantity_currency       = p_old_pstn_trnsctn_record.position_quantity_currency,
          quantity_designator              = p_old_pstn_trnsctn_record.quantity_designator,
          normalised_pstn_qntty            = p_old_pstn_trnsctn_record.normalised_pstn_qntty,
          normalised_pstn_qntty_crrncy     = p_old_pstn_trnsctn_record.normalised_pstn_qntty_crrncy,
          --position_margin                  = p_old_pstn_trnsctn_record.position_margin,
          --position_margin_currency         = p_old_pstn_trnsctn_record.position_margin_currency,
          open_trade_amount                  = p_old_pstn_trnsctn_record.open_trade_amount,
          open_trade_amount_currency         = p_old_pstn_trnsctn_record.open_trade_amount_currency,
          --position_margin_in_tapc          = p_old_pstn_trnsctn_record.position_margin_in_tapc,
          open_trade_amount_in_tapc          = p_old_pstn_trnsctn_record.open_trade_amount_in_tapc,
          open_trade_price                 = p_old_pstn_trnsctn_record.open_trade_price,
          --open_trade_margin_fx_rate        = p_old_pstn_trnsctn_record.open_trade_margin_fx_rate,
          opening_trade_amount_fx_rate      = p_old_pstn_trnsctn_record.opening_trade_amount_fx_rate,
          open_trade_time                  = p_old_pstn_trnsctn_record.open_trade_time,
          --prev_position_quantity           = p_old_pstn_trnsctn_record.prev_position_quantity,
          --prev_position_margin             = p_old_pstn_trnsctn_record.prev_position_margin,
          --prev_position_amount             = p_old_pstn_trnsctn_record.prev_position_amount,
          --prev_position_margin_in_tapc     = p_old_pstn_trnsctn_record.prev_position_margin_in_tapc,
          --prev_position_amount_in_tapc     = p_old_pstn_trnsctn_record.prev_position_amount_in_tapc,
          --prev_pstn_financing_ratio        = p_old_pstn_trnsctn_record.prev_pstn_financing_ratio,
          --prev_prdct_point_multiplier      = p_old_pstn_trnsctn_record.prev_prdct_point_multiplier,
          record_source                    = p_old_pstn_trnsctn_record.record_source,
          cash_transaction_seq             = p_old_pstn_trnsctn_record.cash_transaction_seq,
          Execution_Type                   = p_old_pstn_trnsctn_record.Execution_Type,
          Opening_Trade_Instrument_Price   = p_old_pstn_trnsctn_record.Opening_Trade_Instrument_Price,
          Opening_Trade_Strike_Price       = p_old_pstn_trnsctn_record.Opening_Trade_Strike_Price,
          binary_type                      = p_old_pstn_trnsctn_record.binary_type,
          settle_time                      = p_old_pstn_trnsctn_record.settle_time,
          tenor                            = p_old_pstn_trnsctn_record.tenor,
          strike_price                     = p_old_pstn_trnsctn_record.strike_price,
          strike_price_additional          = p_old_pstn_trnsctn_record.strike_price_additional,
          tenor_start_time                 = p_old_pstn_trnsctn_record.tenor_start_time,
          binary_result                    = p_old_pstn_trnsctn_record.binary_result,
          alloc_instrument_schema          = p_old_pstn_trnsctn_record.alloc_instrument_schema,
          open_trade_instrmnt_amount       = p_old_pstn_trnsctn_record.open_trade_instrmnt_amount,
          forced_margin_fx_rate            = p_old_pstn_trnsctn_record.forced_margin_fx_rate,
          opn_accrd_trnvr_in_accnt_ccy     = p_old_pstn_trnsctn_record.opn_accrd_trnvr_in_accnt_ccy,
          is_pair_ccy_in_frctnl_prts       = p_old_pstn_trnsctn_record.is_pair_ccy_in_frctnl_prts
      WHERE
            transaction_id               = p_old_pstn_trnsctn_record.transaction_id AND
            platform                     = p_old_pstn_trnsctn_record.platform AND
            effective_start_timestamp    = p_old_pstn_trnsctn_record.effective_start_timestamp AND
            (
             nrg_common.has_value_changed(trade_id,p_old_pstn_trnsctn_record.trade_id) = 1 OR
             nrg_common.has_value_changed(booking_number,p_old_pstn_trnsctn_record.booking_number) = 1 OR
             nrg_common.has_value_changed(record_source,p_old_pstn_trnsctn_record.record_source) = 1 OR
             nrg_common.has_value_changed(publish_time,p_old_pstn_trnsctn_record.publish_time) = 1 OR
             nrg_common.has_value_changed(event_time,p_old_pstn_trnsctn_record.event_time) = 1 OR
             nrg_common.has_value_changed(version_number,p_old_pstn_trnsctn_record.version_number) = 1 OR
             nrg_common.has_value_changed(is_deleted,p_old_pstn_trnsctn_record.is_deleted) = 1 OR
             nrg_common.has_value_changed(creation_time,p_old_pstn_trnsctn_record.creation_time) = 1 OR
             nrg_common.has_value_changed(creation_identity_token,p_old_pstn_trnsctn_record.creation_identity_token) = 1 OR
             nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_old_pstn_trnsctn_record.crtn_on_bhlf_of_idntty_tkn) = 1 OR
             nrg_common.has_value_changed(update_time,p_old_pstn_trnsctn_record.update_time) = 1 OR
             nrg_common.has_value_changed(update_identity_token,p_old_pstn_trnsctn_record.update_identity_token) = 1 OR
             nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_old_pstn_trnsctn_record.updt_on_bhlf_of_idntty_tkn) = 1 OR
             nrg_common.has_value_changed(session_key,p_old_pstn_trnsctn_record.session_key) = 1 OR
             nrg_common.has_value_changed(trading_account_id,p_old_pstn_trnsctn_record.trading_account_id) = 1 OR
             nrg_common.has_value_changed(trading_account_type,p_old_pstn_trnsctn_record.trading_account_type) = 1 OR
             nrg_common.has_value_changed(trading_account_function,p_old_pstn_trnsctn_record.trading_account_function) = 1 OR
             nrg_common.has_value_changed(trading_account_codifier,p_old_pstn_trnsctn_record.trading_account_codifier) = 1 OR
             nrg_common.has_value_changed(trdng_accnt_primary_currency,p_old_pstn_trnsctn_record.trdng_accnt_primary_currency) = 1 OR
             nrg_common.has_value_changed(order_id,p_old_pstn_trnsctn_record.order_id) = 1 OR
             nrg_common.has_value_changed(product_instrument_code,p_old_pstn_trnsctn_record.product_instrument_code) = 1 OR
             nrg_common.has_value_changed(product_wrapper_code,p_old_pstn_trnsctn_record.product_wrapper_code) = 1 OR
             --nrg_common.has_value_changed(mm_instrument_id,p_old_pstn_trnsctn_record.mm_instrument_id) = 1 OR
             --nrg_common.has_value_changed(product_schema_code,p_old_pstn_trnsctn_record.product_schema_code) = 1 OR
             nrg_common.has_value_changed(product_generation,p_old_pstn_trnsctn_record.product_generation) = 1 OR
             nrg_common.has_value_changed(product_point_multiplier,p_old_pstn_trnsctn_record.product_point_multiplier) = 1 OR
             nrg_common.has_value_changed(product_currency,p_old_pstn_trnsctn_record.product_currency) = 1 OR
             --nrg_common.has_value_changed(product_financing_ratio_max,p_old_pstn_trnsctn_record.product_financing_ratio_max) = 1 OR
             nrg_common.has_value_changed(product_frctnl_part_ratio,p_old_pstn_trnsctn_record.product_frctnl_part_ratio) = 1 OR
             nrg_common.has_value_changed(is_ccy_in_fractional_parts,p_old_pstn_trnsctn_record.is_ccy_in_fractional_parts) = 1 OR
             nrg_common.has_value_changed(channel_id,p_old_pstn_trnsctn_record.channel_id) = 1 OR
             nrg_common.has_value_changed(request_id,p_old_pstn_trnsctn_record.request_id) = 1 OR
             nrg_common.has_value_changed(business_date,p_old_pstn_trnsctn_record.business_date) = 1 OR
             nrg_common.has_value_changed(reporting_date,p_old_pstn_trnsctn_record.reporting_date) = 1 OR
             --nrg_common.has_value_changed(cstm_info_vrtl_prtfl_cd,p_old_pstn_trnsctn_record.cstm_info_vrtl_prtfl_cd) = 1 OR
             nrg_common.has_value_changed(transaction_time,p_old_pstn_trnsctn_record.transaction_time) = 1 OR
             nrg_common.has_value_changed(transaction_type,p_old_pstn_trnsctn_record.transaction_type) = 1 OR
             nrg_common.has_value_changed(transaction_refcodifier,p_old_pstn_trnsctn_record.transaction_refcodifier) = 1 OR
             nrg_common.has_value_changed(transaction_refcode,p_old_pstn_trnsctn_record.transaction_refcode) = 1 OR
             nrg_common.has_value_changed(is_automatically_rolled,p_old_pstn_trnsctn_record.is_automatically_rolled) = 1 OR
             nrg_common.has_value_changed(position_direction,p_old_pstn_trnsctn_record.position_direction) = 1 OR
             --nrg_common.has_value_changed(position_financing_ratio,p_old_pstn_trnsctn_record.position_financing_ratio) = 1 OR
             nrg_common.has_value_changed(open_trade_quantity,p_old_pstn_trnsctn_record.open_trade_quantity) = 1 OR
             --nrg_common.has_value_changed(position_quantity_currency,p_old_pstn_trnsctn_record.position_quantity_currency) = 1 OR
             nrg_common.has_value_changed(quantity_designator,p_old_pstn_trnsctn_record.quantity_designator) = 1 OR
             nrg_common.has_value_changed(normalised_pstn_qntty,p_old_pstn_trnsctn_record.normalised_pstn_qntty) = 1 OR
             nrg_common.has_value_changed(normalised_pstn_qntty_crrncy,p_old_pstn_trnsctn_record.normalised_pstn_qntty_crrncy) = 1 OR
             --nrg_common.has_value_changed(position_margin,p_old_pstn_trnsctn_record.position_margin) = 1 OR
             --nrg_common.has_value_changed(position_margin_currency,p_old_pstn_trnsctn_record.position_margin_currency) = 1 OR
             nrg_common.has_value_changed(open_trade_amount,p_old_pstn_trnsctn_record.open_trade_amount) = 1 OR
             nrg_common.has_value_changed(open_trade_amount_currency,p_old_pstn_trnsctn_record.open_trade_amount_currency) = 1 OR
             --nrg_common.has_value_changed(position_margin_in_tapc,p_old_pstn_trnsctn_record.position_margin_in_tapc) = 1 OR
             nrg_common.has_value_changed(open_trade_amount_in_tapc,p_old_pstn_trnsctn_record.open_trade_amount_in_tapc) = 1 OR
             nrg_common.has_value_changed(open_trade_id,p_old_pstn_trnsctn_record.open_trade_id) = 1 OR
             nrg_common.has_value_changed(open_trade_price,p_old_pstn_trnsctn_record.open_trade_price) = 1 OR
             --nrg_common.has_value_changed(open_trade_margin_fx_rate,p_old_pstn_trnsctn_record.open_trade_margin_fx_rate) = 1 OR
             nrg_common.has_value_changed(opening_trade_amount_fx_rate,p_old_pstn_trnsctn_record.opening_trade_amount_fx_rate) = 1 OR
             nrg_common.has_value_changed(open_trade_time,p_old_pstn_trnsctn_record.open_trade_time) = 1 OR
             nrg_common.has_value_changed(Execution_Type, p_old_pstn_trnsctn_record.Execution_Type) = 1 OR
             nrg_common.has_value_changed(Opening_Trade_Instrument_Price, p_old_pstn_trnsctn_record.Opening_Trade_Instrument_Price) = 1 OR
             nrg_common.has_value_changed(Opening_Trade_Strike_Price, p_old_pstn_trnsctn_record.Opening_Trade_Strike_Price) = 1 OR
             nrg_common.has_value_changed(binary_type, p_old_pstn_trnsctn_record.binary_type) = 1 OR
             nrg_common.has_value_changed(settle_time, p_old_pstn_trnsctn_record.settle_time) = 1 OR
             nrg_common.has_value_changed(tenor, p_old_pstn_trnsctn_record.tenor) = 1 OR
             nrg_common.has_value_changed(strike_price, p_old_pstn_trnsctn_record.strike_price) = 1 OR
             nrg_common.has_value_changed(strike_price_additional, p_old_pstn_trnsctn_record.strike_price_additional) = 1 OR
             nrg_common.has_value_changed(tenor_start_time, p_old_pstn_trnsctn_record.tenor_start_time) = 1 OR
             nrg_common.has_value_changed(binary_result, p_old_pstn_trnsctn_record.binary_result) = 1 OR
             nrg_common.has_value_changed(alloc_instrument_schema, p_old_pstn_trnsctn_record.alloc_instrument_schema) = 1 OR
             nrg_common.has_value_changed(open_trade_instrmnt_amount, p_old_pstn_trnsctn_record.open_trade_instrmnt_amount) = 1 OR
             nrg_common.has_value_changed(forced_margin_fx_rate, p_old_pstn_trnsctn_record.forced_margin_fx_rate) = 1 OR
             nrg_common.has_value_changed(opn_accrd_trnvr_in_accnt_ccy, p_old_pstn_trnsctn_record.opn_accrd_trnvr_in_accnt_ccy) = 1 OR
             nrg_common.has_value_changed(is_pair_ccy_in_frctnl_prts, p_old_pstn_trnsctn_record.is_pair_ccy_in_frctnl_prts) = 1
            );

  END put_history;

  -- ===================================================================================
  -- PUBLIC MODULES
  -- ===================================================================================
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------
  FUNCTION version
      RETURN VARCHAR2 DETERMINISTIC
  IS
  BEGIN
      logger.logger.set_module('version');
      RETURN gc_version;
  EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          RAISE;
  END version;
  -- ===================================================================================
  -- put_position_transactions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a position transactions
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

 PROCEDURE put_position_transactions(p_position_transactions IN Position_Transactions_tab)
   IS

   BEGIN
    FOR i IN (SELECT * FROM TABLE(CAST(p_position_transactions AS Position_Transactions_tab)))
      LOOP
             put_position_transaction (p_user                             => i.p_user
                                      ,p_effective_start_timestamp        => i.effective_start_timestamp
                                      ,p_transaction_id                   => i.transaction_id
                                      ,p_open_trade_id                    => i.open_trade_id
                                      ,p_trade_id                         => i.trade_id
                                      ,p_transaction_booking_number       => i.transaction_booking_number
                                      ,p_platform                         => i.platform
                                      ,p_publish_time                     => i.publish_time
                                      ,p_event_time                       => i.event_time
                                      ,p_version_number                   => i.version_number
                                      ,p_is_deleted                       => i.is_deleted
                                      ,p_creation_time                    => i.creation_time
                                      ,p_creation_identity_token          => i.creation_identity_token
                                      ,p_crtn_on_bhlf_of_idntty_tkn       => i.crtn_on_bhlf_of_idntty_tkn
                                      ,p_update_time                      => i.update_time
                                      ,p_update_identity_token            => i.update_identity_token
                                      ,p_updt_on_bhlf_of_idntty_tkn       => i.updt_on_bhlf_of_idntty_tkn
                                      ,p_session_key                      => i.session_key
                                      ,p_trading_account_id               => i.trading_account_id
                                      ,p_trading_account_type             => i.trading_account_type
                                      ,p_trading_account_function         => i.trading_account_function
                                      ,p_trading_account_codifier         => i.trading_account_codifier
                                      ,p_trdng_accnt_primary_currency     => i.trdng_accnt_primary_currency
                                      ,p_order_id                         => i.order_id
                                      ,p_product_instrument_code          => i.product_instrument_code
                                      ,p_product_wrapper_code             => i.product_wrapper_code
                                      ,p_product_generation               => i.product_generation
                                      ,p_product_point_multiplier         => i.product_point_multiplier
                                      ,p_product_currency                 => i.product_currency
                                      ,p_product_frctnl_part_ratio        => i.product_frctnl_part_ratio
                                      ,p_iscurrencyinfractionalparts      => i.iscurrencyinfractionalparts
                                      ,p_channel_id                       => i.channel_id
                                      ,p_request_id                       => i.request_id
                                      ,p_transaction_time                 => i.transaction_time
                                      ,p_transaction_type                 => i.transaction_type
                                      ,p_transaction_refcodifier          => i.transaction_refcodifier
                                      ,p_transaction_refcode              => i.transaction_refcode
                                      ,p_is_automatically_rolled          => i.is_automatically_rolled
                                      ,p_position_direction               => i.position_direction
                                      ,p_open_trade_quantity              => i.open_trade_quantity
                                      ,p_quantity_designator              => i.quantity_designator
                                      ,p_open_trade_amount                => i.open_trade_amount
                                      ,p_open_trade_amount_currency       => i.open_trade_amount_currency
                                      ,p_open_trade_amount_in_tapc        => i.open_trade_amount_in_tapc
                                      ,p_open_trade_price                 => i.open_trade_price
                                      ,p_opening_trade_amount_fx_rate     => i.opening_trade_amount_fx_rate
                                      ,p_open_trade_time                  => i.open_trade_time
                                      ,p_record_source                    => i.record_source
                                      ,p_rolled_open_trade_id             => i.rolled_open_trade_id
                                      ,p_opening_trade_app_to_units       => i.opening_trade_app_to_units
                                      ,p_load_latest_positions            => i.load_latest_positions
                                      ,p_execution_type                   => i.execution_type
                                      ,p_opening_trade_instrmnt_price     => i.opening_trade_instrmnt_price
                                      --,p_opening_trade_strike_price       => i.opening_trade_strike_price
                                      ,p_trading_scope                    => i.trading_scope
                                      --,p_alloc_trading_risk_schema        => i.alloc_trading_risk_schema
                                      ,p_binary_type                      => i.binary_type
                                      ,p_settle_time                      => i.settle_time
                                      ,p_tenor                            => i.tenor
                                      ,p_strike_price                     => i.strike_price
                                      ,p_strike_price_additional          => i.strike_price_additional
                                      ,p_tenor_start_time                 => i.tenor_start_time
                                      ,p_binary_result                    => i.binary_result
                                      ,p_alloc_instrument_schema          => i.alloc_instrument_schema
                                      ,p_open_trade_instrmnt_amount       => i.open_trade_instrmnt_amount
                                      ,p_forced_margin_fx_rate            => i.forced_margin_fx_rate
                                      ,p_opn_accrd_trnvr_in_accnt_ccy     => i.opn_accrd_trnvr_in_accnt_ccy
                                      ,p_is_pair_ccy_in_frctnl_prts       => i.is_pair_ccy_in_frctnl_prts
                                      ,p_value_date                       => i.value_date
                                      ,p_pair_currency                    => i.pair_currency
                                      ,p_primary_currency                 => i.primary_currency
                                      ,p_secondary_currency               => i.secondary_currency
                                      ,p_primary_amount                   => i.primary_amount
                                      ,p_secondary_amount                 => i.secondary_amount
                                     );

   END LOOP;


 END;


  -- ===================================================================================
  -- put_position_transaction
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a position transaction
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     POSITION_TRANSACTIONS
  --     POSITION_TRANSACTIONS_H
  --     PSTN_TRANSACTION_TYPES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                          Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                             User
  --     p_effective_start_timestamp        Effective start timestamp
  --     p_transaction_id                   Transaction Id
  --     p_open_trade_id                    Open Trade Id
  --     p_trade_id                         Trade Id
  --     p_transaction_booking_number       Transaction Booking Number
  --     p_platform                         Platform
  --     p_publish_time                     Publish Time
  --     p_event_time                       Event Time
  --     p_version_number                   Version Number
  --     p_is_deleted                       Is Deleted
  --     p_creation_time                    Creation Time
  --     p_creation_identity_token          Creation Identity Token
  --     p_crtn_on_bhlf_of_idntty_tkn       Crtn On Bhlf Of Idntty Tkn
  --     p_update_time                      Update Time
  --     p_update_identity_token            Update Identity Token
  --     p_updt_on_bhlf_of_idntty_tkn       Updt On Bhlf Of Idntty Tkn
  --     p_session_key                      Session Key
  --     p_trading_account_id               Trading Account Id
  --     p_trading_account_type             Trading Account Type
  --     p_trading_account_function         Trading Account Function
  --     p_trading_account_codifier         Trading Account Codifier
  --     p_trdng_accnt_primary_currency     Trading Accnt Primary Currency
  --     p_order_id                         Order Id
  --     p_product_instrument_code          Product Instrument Code
  --     p_product_wrapper_code             Product Wrapper Code
  --     p_mm_instrument_id                 Mm Instrument Id
  --     p_product_schema_code              Product Schema Code
  --     p_product_generation               Product Generation
  --     p_product_point_multiplier         Product Point Multiplier
  --     p_product_currency                 Product Currency
  --     p_product_financing_ratio_max      Product Financing Ratio Max
  --     p_product_frctnl_part_ratio        Product Fractional Part Ratio
  --     p_iscurrencyinfractionalparts      Is currency in fractional parts
  --     p_channel_id                       Channel Id
  --     p_request_id                       Request Id
  --     p_business_date                    Business Date
  --     p_reporting_date                   Reporting Date
  --     p_cstm_info_vrtl_prtfl_cd          Cstm Info Vrtl Prtfl Cd
  --     p_transaction_time                 Transaction Time
  --     p_transaction_type                 Transaction Type
  --     p_transaction_refcodifier          Transaction Refcodifier
  --     p_transaction_refcode              Transaction Refcode
  --     p_is_automatically_rolled          Is Automatically Rolled
  --     p_position_direction               Position Direction
  --     p_position_financing_ratio         Position Financing Ratio
  --     p_open_trade_quantity              Open Trade Quantity
  --     p_position_quantity_currency       Position Quantity Currency
  --     p_quantity_designator              Position Quantity Designator
  --     p_position_margin                  Position Margin
  --     p_position_margin_currency         Position Margin Currency
  --     p_open_trade_amount                  Position Amount
  --     p_open_trade_amount_currency         Position Amount Currency
  --     p_position_margin_in_tapc          Position Margin In Tapc
  --     p_open_trade_amount_in_tapc          Position Amount In Tapc
  --     p_open_trade_price                 Open Trade Price
  --     p_open_trade_margin_fx_rate        Open Trade Margin Fx Rate
  --     p_opening_trade_amount_fx_rate      Open Trade Quantity Fx Rate
  --     p_open_trade_time                  Open Trade Time
  --     p_prev_position_quantity           Prev Position Quantity
  --     p_prev_position_margin             Prev Position Margin
  --     p_prev_position_amount             Prev Position Amount
  --     p_prev_position_margin_in_tapc     Prev Position Margin In Tapc
  --     p_prev_position_amount_in_tapc     Prev Position Amount In Tapc
  --     p_prev_pstn_financing_ratio        Prev Position Financing Ratio
  --     p_prev_prdct_point_multiplier      Prev Product Point Multiplier
  --     p_record_source                    Source For This Record. For normal Transactions It is the same as platform. For MM Backoffice it will be different
  --     p_trading_scope                    Trading Scope
  --     p_alloc_trading_risk_schema        Allocation Trading Risk Schema
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Position Transaction Deleted Before Update and After Insert
  --     -20004    Default Exception
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_position_transaction (p_user                             IN position_transactions.created_by%TYPE,
                                      p_effective_start_timestamp        IN position_transactions.effective_start_timestamp%TYPE,
                                      p_transaction_id                   IN position_transactions.transaction_id%TYPE,
                                      p_open_trade_id                    IN position_transactions.open_trade_id%TYPE,
                                      p_trade_id                         IN position_transactions.trade_id%TYPE,
                                      p_transaction_booking_number       IN position_transactions.booking_number%TYPE,
                                      p_platform                         IN position_transactions.platform%TYPE,
                                      p_publish_time                     IN position_transactions.publish_time%TYPE,
                                      p_event_time                       IN position_transactions.event_time%TYPE,
                                      p_version_number                   IN position_transactions.version_number%TYPE,
                                      p_is_deleted                       IN position_transactions.is_deleted%TYPE,
                                      p_creation_time                    IN position_transactions.creation_time%TYPE,
                                      p_creation_identity_token          IN position_transactions.creation_identity_token%TYPE,
                                      p_crtn_on_bhlf_of_idntty_tkn       IN position_transactions.crtn_on_bhlf_of_idntty_tkn%TYPE,
                                      p_update_time                      IN position_transactions.update_time%TYPE,
                                      p_update_identity_token            IN position_transactions.update_identity_token%TYPE,
                                      p_updt_on_bhlf_of_idntty_tkn       IN position_transactions.updt_on_bhlf_of_idntty_tkn%TYPE,
                                      p_session_key                      IN position_transactions.session_key%TYPE,
                                      p_trading_account_id               IN position_transactions.trading_account_id%TYPE,
                                      p_trading_account_type             IN position_transactions.trading_account_type%TYPE,
                                      p_trading_account_function         IN position_transactions.trading_account_function%TYPE,
                                      p_trading_account_codifier         IN position_transactions.trading_account_codifier%TYPE,
                                      p_trdng_accnt_primary_currency     IN position_transactions.trdng_accnt_primary_currency%TYPE,
                                      p_order_id                         IN position_transactions.order_id%TYPE,
                                      p_product_instrument_code          IN position_transactions.product_instrument_code%TYPE,
                                      p_product_wrapper_code             IN position_transactions.product_wrapper_code%TYPE,
                                      p_product_generation               IN position_transactions.product_generation%TYPE,
                                      p_product_point_multiplier         IN position_transactions.product_point_multiplier%TYPE,
                                      p_product_currency                 IN position_transactions.product_currency%TYPE,
                                      p_product_frctnl_part_ratio        IN position_transactions.product_frctnl_part_ratio%TYPE,
                                      p_iscurrencyinfractionalparts      IN position_transactions.is_ccy_in_fractional_parts%TYPE,
                                      p_channel_id                       IN position_transactions.channel_id%TYPE,
                                      p_request_id                       IN position_transactions.request_id%TYPE,
                                      p_transaction_time                 IN position_transactions.transaction_time%TYPE,
                                      p_transaction_type                 IN position_transactions.transaction_type%TYPE,
                                      p_transaction_refcodifier          IN position_transactions.transaction_refcodifier%TYPE,
                                      p_transaction_refcode              IN position_transactions.transaction_refcode%TYPE,
                                      p_is_automatically_rolled          IN position_transactions.is_automatically_rolled%TYPE,
                                      p_position_direction               IN position_transactions.position_direction%TYPE,
                                      p_open_trade_quantity              IN position_transactions.open_trade_quantity%TYPE,
                                      p_quantity_designator              IN position_transactions.quantity_designator%TYPE,
                                      p_open_trade_amount                IN position_transactions.open_trade_amount%TYPE,
                                      p_open_trade_amount_currency       IN position_transactions.open_trade_amount_currency%TYPE,
                                      p_open_trade_amount_in_tapc        IN position_transactions.open_trade_amount_in_tapc%TYPE,
                                      p_open_trade_price                 IN position_transactions.open_trade_price%TYPE,
                                      p_opening_trade_amount_fx_rate     IN position_transactions.opening_trade_amount_fx_rate%TYPE,
                                      p_open_trade_time                  IN position_transactions.open_trade_time%TYPE,
                                      p_record_source                    IN position_transactions.record_source%TYPE,
                                      p_rolled_open_trade_id             IN position_transactions.rolled_open_trade_id%TYPE,
                                      p_opening_trade_app_to_units       IN position_transactions.opening_trade_app_to_units%TYPE,
                                      p_load_latest_positions            IN VARCHAR2 DEFAULT 'NO',
                                      p_execution_type                   IN position_transactions.execution_type%TYPE ,
                                      p_opening_trade_instrmnt_price     IN position_transactions.opening_trade_instrument_price%TYPE,
                                      p_trading_scope                    IN position_transactions.trading_scope%TYPE,
                                      p_binary_type                      IN position_transactions.binary_type%TYPE,
                                      p_settle_time                      IN position_transactions.settle_time%TYPE,
                                      p_tenor                            IN position_transactions.tenor%TYPE,
                                      p_strike_price                     IN position_transactions.strike_price%TYPE,
                                      p_strike_price_additional          IN position_transactions.strike_price_additional%TYPE,
                                      p_tenor_start_time                 IN position_transactions.tenor_start_time%TYPE,
                                      p_binary_result                    IN position_transactions.binary_result%TYPE,
                                      p_alloc_instrument_schema          IN position_transactions.alloc_instrument_schema%TYPE,
                                      p_open_trade_instrmnt_amount       IN position_transactions.open_trade_instrmnt_amount%TYPE,
                                      p_forced_margin_fx_rate            IN position_transactions.forced_margin_fx_rate%TYPE,
                                      p_opn_accrd_trnvr_in_accnt_ccy     IN position_transactions.opn_accrd_trnvr_in_accnt_ccy%TYPE,
                                      p_is_pair_ccy_in_frctnl_prts       IN position_transactions.is_pair_ccy_in_frctnl_prts%TYPE,
                                      p_value_date                       IN position_transactions.value_date%TYPE,
                                      p_pair_currency                    IN position_transactions.pair_currency%TYPE,
                                      p_primary_currency                 IN position_transactions.primary_currency%TYPE,
                                      p_secondary_currency               IN position_transactions.secondary_currency%TYPE,
                                      p_primary_amount                   IN position_transactions.primary_amount%TYPE,
                                      p_secondary_amount                 IN position_transactions.secondary_amount%TYPE
                                     )
  IS
    lv_effective_start_time           position_transactions.effective_start_timestamp%TYPE := NULL;
    lv_logical_load_timestamp         position_transactions.logical_load_timestamp%TYPE;

    lv_product_frctnl_part_ratio      position_transactions.product_frctnl_part_ratio%TYPE;
    lv_product_point_multiplier       position_transactions.product_point_multiplier%TYPE := NULL;

    lv_normalised_pstn_qntty          position_transactions.normalised_pstn_qntty%TYPE;
    lv_normalised_pstn_qntty_crncy    position_transactions.normalised_pstn_qntty_crrncy%TYPE;

    lv_old_pstn_trnsctn               position_transactions%ROWTYPE;
    ltab_identity_id                  identity_id_tab;

    lv_normalised_open_value          latest_open_trades.nrmlsd_opn_value_in_trdng_ccy%TYPE;
    lv_normalised_trading_ccy         latest_open_trades.normalised_trading_currency%TYPE;

    lv_cash_transaction_seq           NUMBER;

    lex_unknown_operation_type        EXCEPTION;
    lex_pstn_trnsctn_not_found        EXCEPTION;

    lv_business_date                  DATE;
    lv_reporting_date                 DATE;

    lv_is_pair_ccy_in_frctnl_prts     VARCHAR2(3);
    lv_iscurrencyinfractionalparts    VARCHAR2(3);
    lv_trade_qty_ccy                  VARCHAR2(3);
    
    lv_quantity_designator            position_transactions.quantity_designator%TYPE;

  BEGIN
    logger.logger.set_module('put_position_transaction');

  -- set the logical load timestamp to now
    lv_logical_load_timestamp := SYSTIMESTAMP;

    lv_is_pair_ccy_in_frctnl_prts  := nvl(p_is_pair_ccy_in_frctnl_prts,'NO');
    lv_iscurrencyinfractionalparts := nvl(p_iscurrencyinfractionalparts,'NO');
    
    CASE 
        WHEN p_quantity_designator IS NOT NULL 
          THEN lv_quantity_designator := p_quantity_designator;
        WHEN p_product_wrapper_code = 'X-A'
          THEN lv_quantity_designator := 'UNITS';
        WHEN p_product_wrapper_code = 'A-EOVH'
          THEN lv_quantity_designator := 'AMOUNTPERPOINT';
        WHEN p_product_wrapper_code = 'X-AJYJI'
          THEN lv_quantity_designator := 'AMOUNT';
        WHEN p_product_wrapper_code IN ('X-MNRK','X-MNRL')
          THEN lv_quantity_designator := 'AMOUNTPERBET';
        WHEN p_product_wrapper_code IN ('X-MNRM','X-MNRN')
          THEN lv_quantity_designator := 'AMOUNT';
        WHEN p_product_wrapper_code = 'X-QOQH'
          THEN lv_quantity_designator := 'KOUNITS';
        ELSE lv_quantity_designator := NULL;
      END CASE;
    
    
    --
    -- Referential Integrity - Stub Creation
    --

    --
    -- Trading Account
    --
    IF p_trading_account_id IS NOT NULL THEN
       nrg_trading_account.create_trading_account_stub (p_user                       => p_user,
                                                        p_logical_load_timestamp     => lv_logical_load_timestamp,
                                                        p_effective_start_timestamp  => p_effective_start_timestamp,
                                                        p_trading_account_id         => p_trading_account_id,
                                                        p_trading_account_type       => p_trading_account_type);
    END IF;

    --
    -- Order
    --

    IF p_order_id IS NOT NULL THEN
      nrg_order.create_order_stub (p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_order_id                   => p_order_id,
                                   p_platform                   => p_platform);
    END IF;

    --
    -- Trade (Sourced from RefCode)
    --

    IF p_trade_id IS NOT NULL THEN
      nrg_trade.create_trade_stub (p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_trade_id                   => p_trade_id,
                                   p_platform                   => p_platform);
    END IF;

    --
    -- Trade
    --
    IF p_rolled_open_trade_id IS NOT NULL THEN
      nrg_trade.create_trade_stub (p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_trade_id                   => p_rolled_open_trade_id,
                                   p_platform                   => p_platform);
    END IF;

    --
    -- Opening Trade
    --
    IF p_open_trade_id IS NOT NULL THEN
      nrg_trade.create_trade_stub (p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_trade_id                   => p_open_trade_id,
                                   p_platform                   => p_platform);
    END IF;

    --
    -- Cash Transaction
    --

    IF p_transaction_booking_number IS NOT NULL THEN
      nrg_cash_transaction.create_cash_transaction_stub (p_user                       => p_user,
                                                         p_logical_load_timestamp     => lv_logical_load_timestamp,
                                                         p_effective_start_timestamp  => p_effective_start_timestamp,
                                                         p_booking_number             => p_transaction_booking_number,
                                                         p_platform                   => p_platform,
                                                         p_record_source              => p_record_source,
                                                         p_cash_transaction_seq       => lv_cash_transaction_seq);
    END IF;

    --
    -- Session
    --

    IF p_session_key IS NOT NULL THEN
      nrg_session.create_session_stub (p_user                       => p_user,
                                       p_logical_load_timestamp     => lv_logical_load_timestamp,
                                       p_effective_start_timestamp  => p_effective_start_timestamp,
                                       p_session_key                => p_session_key,
                                       p_channel_id                 => p_channel_id,
                                       p_identity_id                => p_creation_identity_token,
                                       p_on_behalf_of_identity_id   => p_crtn_on_bhlf_of_idntty_tkn);
    END IF;

    --
    -- Product
    --

    IF p_product_instrument_code IS NOT NULL AND p_product_wrapper_code IS NOT NULL THEN
      nrg_products.create_instrument_stub (p_user                       => p_user,
                                           p_logical_load_timestamp     => lv_logical_load_timestamp,
                                           p_effective_start_timestamp  => p_effective_start_timestamp,
                                           p_instrument_code            => p_product_instrument_code,
                                           p_product_platform           => p_platform,
                                           p_product_wrapper            => p_product_wrapper_code,
                                           p_mm_instrument_id           => NULL);
    END IF;

    --
    -- Position Transaction Type
    --

    IF p_transaction_type IS NOT NULL THEN
      nrg_pstn_transaction_type.put_pstn_transaction_type (p_user                         => p_user,
                                                           p_effective_start_timestamp    => p_effective_start_timestamp,
                                                           p_logical_load_timestamp       => lv_logical_load_timestamp,
                                                           p_position_transaction_type    => p_transaction_type);
    END IF;

    --
    -- Position Quantity Designator
    --

    IF p_quantity_designator IS NOT NULL THEN
      nrg_quantity_designator.put_quantity_designator (p_user                         => p_user,
                                                       p_effective_start_timestamp    => p_effective_start_timestamp,
                                                       p_logical_load_timestamp       => lv_logical_load_timestamp,
                                                       p_quantity_designator          => lv_quantity_designator);
    END IF;

    --
    -- Identity Stubs
    --
    -- Build array of ids
    --

    SELECT  identity_id_obj(identity_id)
    BULK COLLECT INTO ltab_identity_id
    FROM
       (SELECT DISTINCT t.identity_id
        FROM
          (SELECT p_creation_identity_token AS identity_id
           FROM DUAL
           UNION
           SELECT p_crtn_on_bhlf_of_idntty_tkn
           FROM DUAL
           UNION
           SELECT p_update_identity_token
           FROM DUAL
           UNION
           SELECT p_updt_on_bhlf_of_idntty_tkn
           FROM DUAL
          ) t
        WHERE t.identity_id IS NOT NULL
       );

    --
    -- Create Identity Stubs
    --

    IF ltab_identity_id.COUNT <> 0 THEN
       nrg_identity.create_identity_stub (p_user                       => p_user,
                                          p_logical_load_timestamp     => lv_logical_load_timestamp,
                                          p_effective_start_timestamp  => p_effective_start_timestamp,
                                          p_identity_id                => ltab_identity_id);
    END IF;


    --
    -- Calculate Business Date
    --

    lv_business_date := nrg_common.get_business_date(p_transaction_time);

    --
    -- Calculate Reporting Date
    --

    lv_reporting_date := nrg_common.get_reporting_date(p_transaction_time);

    --
    -- Calculate the transformed values for necessary platforms
    --
    CASE
      WHEN p_platform = 'NG' THEN
      --
      -- Product Point Multiplier
      -- As per story # 480, these case statements are no longer required.
      /*  CASE
          WHEN p_quantity_designator = 'UNITS'  THEN
               lv_product_point_multiplier := NULL;
          WHEN p_quantity_designator = 'AMOUNT' THEN
               lv_product_point_multiplier := NULL;
          WHEN p_quantity_designator = 'AMOUNTPERPOINT'  THEN
               lv_product_point_multiplier := p_product_point_multiplier;
          ELSE
               lv_product_point_multiplier := NULL;
        END CASE;
      --
      -- Product fractional part ratio
      --
        CASE
          WHEN p_quantity_designator = 'UNITS'  THEN
            CASE
              WHEN p_iscurrencyinfractionalparts = 'YES'  THEN
                lv_product_frctnl_part_ratio := p_product_frctnl_part_ratio;
              WHEN p_iscurrencyinfractionalparts = 'NO'  THEN
                lv_product_frctnl_part_ratio := NULL;
              ELSE
                lv_product_frctnl_part_ratio := NULL;
            END CASE;
          WHEN p_quantity_designator = 'AMOUNT' THEN
            lv_product_frctnl_part_ratio := NULL;
          WHEN p_quantity_designator = 'AMOUNTPERPOINT'  THEN --2
            lv_product_frctnl_part_ratio := NULL;
          ELSE
            lv_product_frctnl_part_ratio := NULL;
        END CASE;

        */


      -- Start Changes for story # 480

      -- Product Point Multiplier

      lv_product_point_multiplier := p_product_point_multiplier;

      -- Product Fractional Part Ratio

      -- lv_product_frctnl_part_ratio := p_product_frctnl_part_ratio;

      CASE WHEN p_product_wrapper_code = 'X-A' AND lv_iscurrencyinfractionalparts = 'YES' THEN
        lv_product_frctnl_part_ratio         := 0.01;
      ELSE
        lv_product_frctnl_part_ratio := NULL;
      END CASE;

      -- Complete Changes for Story # 480
      --
      -- Normalised Position Quantity
      --
        /*CASE
          WHEN p_product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
            lv_normalised_pstn_qntty := NULL;
          WHEN p_quantity_designator = 'AMOUNTPERPOINT' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity * nvl(lv_product_point_multiplier,1);
          WHEN p_quantity_designator = 'KOUNITS' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity * nvl(lv_product_point_multiplier,1);
          WHEN p_quantity_designator = 'UNITS' AND lv_iscurrencyinfractionalparts = 'YES' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity * nvl(lv_product_frctnl_part_ratio,1);
          WHEN p_quantity_designator = 'UNITS' AND lv_iscurrencyinfractionalparts = 'NO' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity;
          WHEN p_quantity_designator = 'AMOUNT' THEN
              CASE
                  WHEN p_open_trade_price = 0 THEN
                      lv_normalised_pstn_qntty := 0;
                  ELSE
                      lv_normalised_pstn_qntty := p_open_trade_amount / p_open_trade_price;
              END CASE;
          ELSE
            lv_normalised_pstn_qntty := NULL;
        END CASE;*/
        CASE
          WHEN p_product_wrapper_code IN ('A-EOVH','X-QOQH') THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity * nvl(lv_product_point_multiplier,1);
          WHEN p_product_wrapper_code = 'X-A' AND lv_iscurrencyinfractionalparts = 'YES' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity * nvl(lv_product_frctnl_part_ratio,1);
          WHEN p_product_wrapper_code = 'X-A' AND lv_iscurrencyinfractionalparts = 'NO' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity;
          WHEN p_product_wrapper_code = 'X-AJYJI' THEN
            lv_normalised_pstn_qntty := p_open_trade_quantity;
          ELSE
            lv_normalised_pstn_qntty := NULL;
        END CASE;

    ELSE
    -- Non NG Platform
    -- This is not currently expected - Do Nothing
    NULL;
    END CASE;

    --
    --Common Business Rules between Platforms
    --

    --
    -- Normalised Position Quantity Currency
    --
    /*CASE
      WHEN p_product_wrapper_code = 'A-EOVH' THEN
           lv_normalised_pstn_qntty_crncy := NVL(p_open_trade_amount_currency,p_trdng_accnt_primary_currency);
      WHEN p_product_wrapper_code = 'X-A' THEN
           lv_normalised_pstn_qntty_crncy := p_product_currency;
      ELSE
           lv_normalised_pstn_qntty_crncy := NULL;
    END CASE;*/
    CASE
      WHEN p_product_wrapper_code = 'A-EOVH' THEN
           lv_normalised_pstn_qntty_crncy := NVL(p_open_trade_amount_currency,p_trdng_accnt_primary_currency);
      WHEN p_product_wrapper_code = 'X-A' THEN
           lv_normalised_pstn_qntty_crncy := p_product_currency;
      WHEN p_product_wrapper_code   = 'X-AJYJI' THEN
           lv_normalised_pstn_qntty_crncy := p_primary_currency;
      ELSE
           lv_normalised_pstn_qntty_crncy := NULL;
    END CASE;

    --
    --Normalised open value
    --

    /*CASE
      WHEN UPPER(p_quantity_designator) = 'AMOUNTPERPOINT' THEN
        lv_normalised_open_value := p_open_trade_quantity * nvl(lv_product_point_multiplier, 1) * p_open_trade_price;
      WHEN UPPER(p_quantity_designator) = 'KOUNITS' THEN
        lv_normalised_open_value := p_open_trade_amount;
      WHEN UPPER(p_quantity_designator) = 'UNITS' THEN
        CASE
          WHEN NVL(lv_iscurrencyinfractionalparts, 'NO') = 'NO' THEN
            lv_normalised_open_value := p_open_trade_quantity * p_open_trade_price;
          ELSE
            lv_normalised_open_value := p_open_trade_quantity * nvl(lv_product_frctnl_part_ratio, 1) * p_open_trade_price;
        END CASE;
      WHEN UPPER(p_quantity_designator) = 'AMOUNT' THEN
        lv_normalised_open_value := p_open_trade_amount;
      ELSE
        lv_normalised_open_value := NULL;
    END CASE;*/
    CASE
      WHEN p_product_wrapper_code = 'A-EOVH' THEN
        lv_normalised_open_value := p_open_trade_quantity * nvl(lv_product_point_multiplier, 1) * p_open_trade_price;
      WHEN p_product_wrapper_code = 'X-QOQH' THEN
        lv_normalised_open_value := p_open_trade_amount;
      WHEN p_product_wrapper_code = 'X-A' THEN
        CASE
          WHEN NVL(lv_iscurrencyinfractionalparts, 'NO') = 'NO' THEN
            lv_normalised_open_value := p_open_trade_quantity * p_open_trade_price;
          ELSE
            lv_normalised_open_value := p_open_trade_quantity * nvl(lv_product_frctnl_part_ratio, 1) * p_open_trade_price;
        END CASE;
      WHEN p_product_wrapper_code IN ('X-MNRM','X-MNRN') THEN
        lv_normalised_open_value := p_open_trade_amount;
      WHEN p_product_wrapper_code = 'X-AJYJI' THEN
        lv_normalised_open_value := p_open_trade_amount;
      ELSE
        lv_normalised_open_value := NULL;
    END CASE;

    --
    --Normalised Trading Currency
    --

    /*CASE
      WHEN UPPER(p_quantity_designator) = 'AMOUNTPERPOINT' THEN
        lv_normalised_trading_ccy := p_open_trade_amount_currency;
      WHEN UPPER(p_quantity_designator) = 'KOUNITS' THEN
        lv_normalised_trading_ccy := p_open_trade_amount_currency;
      WHEN UPPER(p_quantity_designator) = 'UNITS' THEN
        lv_normalised_trading_ccy := p_product_currency;
      WHEN UPPER(p_quantity_designator) = 'AMOUNT' THEN
        lv_normalised_trading_ccy := p_open_trade_amount_currency;
      ELSE
        lv_normalised_trading_ccy := NULL;
    END CASE;*/
    CASE
      WHEN p_product_wrapper_code IN ('A-EOVH','X-QOQH','X-MNRM','X-MNRN') THEN
        lv_normalised_trading_ccy := p_open_trade_amount_currency;
      WHEN p_product_wrapper_code = 'X-A' THEN
        lv_normalised_trading_ccy := p_product_currency;
      WHEN p_product_wrapper_code = 'X-AJYJI' THEN
        lv_normalised_trading_ccy := p_secondary_currency;
      ELSE
        lv_normalised_trading_ccy := NULL;
    END CASE;
    --
    --
    --
    CASE
      WHEN p_product_wrapper_code = 'X-AJYJI' THEN
        lv_trade_qty_ccy := p_primary_currency;
      ELSE
        lv_trade_qty_ccy := p_open_trade_amount_currency;
    END CASE;

  IF(p_transaction_type != 'MODIFIED') THEN
    nrg_latest_position.put_position(p_user                              => p_user,
                              p_effective_start_timestamp         => p_effective_start_timestamp,
                              p_order_id                          => p_order_id,
                              p_state                             => NULL,
                              p_platform                          => p_platform,
                              p_trading_account_id                => p_trading_account_id,
                              p_trading_account_type              => p_trading_account_type,
                              p_trading_account_codifier          => p_trading_account_codifier,
                              p_trading_account_function          => p_trading_account_function,
                              p_mm_account_id                     => NULL,
                              p_product_instrument_code           => p_product_instrument_code,
                              p_product_wrapper_code              => p_product_wrapper_code,
                              p_product_point_multiplier          => lv_product_point_multiplier,
                              p_product_financing_ratio_max       => NULL,--p_product_financing_ratio_max,
                              p_product_generation                => p_product_generation,
                              p_is_crncy_in_frctnl_prts           => lv_iscurrencyinfractionalparts,
                              p_fractional_part_ratio             => lv_product_frctnl_part_ratio,
                              p_mm_instrument_id                  => NULL,
                              p_requested_direction               => p_position_direction,
                              p_creation_time                     => p_transaction_time,
                              p_trade_id                          => p_open_trade_id,
                              p_trade_time                        => p_open_trade_time,
                              p_is_primary                        => 'YES',
                              p_trdng_accnt_prmry_crrncy          => p_trdng_accnt_primary_currency,
                              p_product_currency                  => p_product_currency,
                              p_trade_quantity                    => p_open_trade_quantity,
                              p_trade_quantity_currency           => lv_trade_qty_ccy,
                              p_nrmlsd_opn_qty_in_trdng_ccy       => lv_normalised_pstn_qntty,
                              p_trade_price                       => p_open_trade_price,
                              p_normalised_open_price             => p_open_trade_price,
                              p_open_trade_financing_ratio        => NULL,--p_position_financing_ratio,
                              p_trade_amount                      => p_open_trade_amount,
                              p_trade_amount_currency             => p_open_trade_amount_currency,
                              p_amt_in_trdng_accnt_prmry_ccy      => p_open_trade_amount_in_tapc,
                              p_nrmlsd_opn_vle_in_trdng_ccy       => lv_normalised_open_value,
                              p_margin_type                       => 'PERCENTAGE',
                              p_normalised_margin_type            => 'PERCENTAGE',
                              p_normalised_margin_req             => NULL,--(100 * (1- nvl(p_position_financing_ratio, 0))),
                              p_margin                            => NULL,--p_position_margin,
                              p_mrgn_trdng_accnt_prmry_ccy        => NULL,--p_position_margin_in_tapc,
                              p_margin_currency                   => NULL,--p_position_margin_currency,
                              p_margin_secondary                  => NULL,
                              p_margin_secondary_currency         => NULL,
                              p_margin_fx_rate                    => NULL,--p_open_trade_margin_fx_rate,
                              p_open_trade_qntty_fx_rate          => NULL,--p_opening_trade_amount_fx_rate,
                              p_cstm_info_vrtl_prtfl_cd           => NULL,--p_cstm_info_vrtl_prtfl_cd,
                              p_quantity_designator               => lv_quantity_designator,
                              p_mm_backoffice_ref                 => NULL,
                              p_mm_value_date                     => NULL,
                              p_normalised_trading_currency       => lv_normalised_trading_ccy,
                              p_last_modified_time                => p_update_time,
                              p_old_effective_start_time          => NULL,
                              p_logical_load_timestamp            => lv_logical_load_timestamp
                              ,p_record_source                     => p_record_source
                              ,p_load_latest_positions             => p_load_latest_positions
                              ,p_direction_multiplier              => NULL
                              ,p_trading_scope                     => p_trading_scope
                              ,p_strike_price                      => p_strike_price
                              ,p_forced_margin_fx_rate             => p_forced_margin_fx_rate
                              ,p_value_date                        => p_value_date);
     END IF;

    --
    --Insert the new position_transaction on the table
    --
    BEGIN
      INSERT INTO position_transactions (
                                         transaction_id,
                                         open_trade_id,
                                         trade_id,
                                         booking_number,
                                         platform,
                                         logical_load_timestamp,
                                         created_by,
                                         create_timestamp,
                                         updated_by,
                                         update_timestamp,
                                         effective_start_timestamp,
                                         publish_time,
                                         event_time,
                                         version_number,
                                         is_deleted,
                                         creation_time,
                                         creation_identity_token,
                                         crtn_on_bhlf_of_idntty_tkn,
                                         update_time,
                                         update_identity_token,
                                         updt_on_bhlf_of_idntty_tkn,
                                         session_key,
                                         trading_account_id,
                                         trading_account_type,
                                         trading_account_function,
                                         trading_account_codifier,
                                         trdng_accnt_primary_currency,
                                         order_id,
                                         product_instrument_code,
                                         product_wrapper_code,
                                         --mm_instrument_id,
                                         --product_schema_code,
                                         product_generation,
                                         product_point_multiplier,
                                         product_currency,
                                         --product_financing_ratio_max,
                                         product_frctnl_part_ratio,
                                         is_ccy_in_fractional_parts,
                                         channel_id,
                                         request_id,
                                         business_date,
                                         reporting_date,
                                         --cstm_info_vrtl_prtfl_cd,
                                         transaction_time,
                                         transaction_type,
                                         transaction_refcodifier,
                                         transaction_refcode,
                                         is_automatically_rolled,
                                         position_direction,
                                         --position_financing_ratio,
                                         open_trade_quantity,
                                         --position_quantity_currency,
                                         quantity_designator,
                                         normalised_pstn_qntty,
                                         normalised_pstn_qntty_crrncy,
                                         --position_margin,
                                         --position_margin_currency,
                                         open_trade_amount,
                                         open_trade_amount_currency,
                                         --position_margin_in_tapc,
                                         open_trade_amount_in_tapc,
                                         open_trade_price,
                                         --open_trade_margin_fx_rate,
                                         opening_trade_amount_fx_rate,
                                         open_trade_time,
                                         --prev_position_quantity,
                                         --prev_position_margin,
                                         --prev_position_amount,
                                         --prev_position_margin_in_tapc,
                                         --prev_position_amount_in_tapc,
                                         --prev_pstn_financing_ratio,
                                         --prev_prdct_point_multiplier,
                                         record_source,
                                         cash_transaction_seq,
                                         rolled_open_trade_id,
                                         opening_trade_app_to_units,
                                         Execution_Type,
                                         Opening_Trade_Instrument_Price,
                                         Opening_Trade_Strike_Price,
                                         trading_scope,
                                         alloc_trading_risk_schema,
                                         binary_type,
                                         settle_time,
                                         tenor,
                                         strike_price,
                                         strike_price_additional,
                                         tenor_start_time,
                                         binary_result,
                                         alloc_instrument_schema,
                                         open_trade_instrmnt_amount,
                                         forced_margin_fx_rate,
                                         opn_accrd_trnvr_in_accnt_ccy,
                                         is_pair_ccy_in_frctnl_prts,
                                         value_date,
                                         pair_currency,
                                         primary_currency,
                                         secondary_currency,
                                         primary_amount,
                                         secondary_amount
                                        )
                                 VALUES (
                                         p_transaction_id,
                                         p_open_trade_id,
                                         p_trade_id,
                                         p_transaction_booking_number,
                                         p_platform,
                                         lv_logical_load_timestamp,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_effective_start_timestamp,
                                         p_publish_time,
                                         p_event_time,
                                         p_version_number,
                                         p_is_deleted,
                                         p_creation_time,
                                         p_creation_identity_token,
                                         p_crtn_on_bhlf_of_idntty_tkn,
                                         p_update_time,
                                         p_update_identity_token,
                                         p_updt_on_bhlf_of_idntty_tkn,
                                         p_session_key,
                                         p_trading_account_id,
                                         p_trading_account_type,
                                         p_trading_account_function,
                                         p_trading_account_codifier,
                                         p_trdng_accnt_primary_currency,
                                         p_order_id,
                                         p_product_instrument_code,
                                         p_product_wrapper_code,
                                         --p_mm_instrument_id,
                                         --p_product_schema_code,
                                         p_product_generation,
                                         lv_product_point_multiplier,
                                         p_product_currency,
                                         --p_product_financing_ratio_max,
                                         lv_product_frctnl_part_ratio,
                                         lv_iscurrencyinfractionalparts,
                                         p_channel_id,
                                         p_request_id,
                                         lv_business_date,
                                         lv_reporting_date,
                                         --p_cstm_info_vrtl_prtfl_cd,
                                         p_transaction_time,
                                         p_transaction_type,
                                         p_transaction_refcodifier,
                                         p_transaction_refcode,
                                         p_is_automatically_rolled,
                                         p_position_direction,
                                         --p_position_financing_ratio,
                                         p_open_trade_quantity,
                                         --p_position_quantity_currency,
                                         lv_quantity_designator,
                                         lv_normalised_pstn_qntty,
                                         lv_normalised_pstn_qntty_crncy,
                                         --p_position_margin,
                                         --p_position_margin_currency,
                                         p_open_trade_amount,
                                         p_open_trade_amount_currency,
                                         --p_position_margin_in_tapc,
                                         p_open_trade_amount_in_tapc,
                                         p_open_trade_price,
                                         --p_open_trade_margin_fx_rate,
                                         p_opening_trade_amount_fx_rate,
                                         p_open_trade_time,
                                         --p_prev_position_quantity,
                                         --p_prev_position_margin,
                                         --p_prev_position_amount,
                                         --p_prev_position_margin_in_tapc,
                                         --p_prev_position_amount_in_tapc,
                                         --p_prev_pstn_financing_ratio,
                                         --p_prev_prdct_point_multiplier,
                                         p_record_source,
                                         lv_cash_transaction_seq,
                                         p_rolled_open_trade_id,
                                         p_opening_trade_app_to_units,
                                         p_execution_type,
                                         p_opening_trade_instrmnt_price,
                                         NULL,
                                         p_trading_scope,
                                         NULL,
                                         p_binary_type,
                                         p_settle_time,
                                         p_tenor,
                                         p_strike_price,
                                         p_strike_price_additional,
                                         p_tenor_start_time,
                                         p_binary_result,
                                         p_alloc_instrument_schema,
                                         p_open_trade_instrmnt_amount,
                                         p_forced_margin_fx_rate,
                                         p_opn_accrd_trnvr_in_accnt_ccy,
                                         lv_is_pair_ccy_in_frctnl_prts,
                                         p_value_date,
                                         p_pair_currency,
                                         p_primary_currency,
                                         p_secondary_currency,
                                         p_primary_amount,
                                         p_secondary_amount
                                         );
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
      --Get the effective start time of the previous version of the trade
         BEGIN
          SELECT ptrx.*
          INTO lv_old_pstn_trnsctn
          FROM position_transactions ptrx
          WHERE ptrx.transaction_id = p_transaction_id AND
                ptrx.platform = p_platform
          FOR UPDATE;
           lv_effective_start_time := lv_old_pstn_trnsctn.effective_start_timestamp;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            RAISE lex_pstn_trnsctn_not_found;
        END;
    END;

    CASE
      WHEN lv_effective_start_time IS NULL THEN
        --
        --Since the position transaction is already inserted no operation will be performed
        --
        NULL;
      WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time <= p_effective_start_timestamp THEN

        --
        --In case when the effective start time of the incoming record is greater than the effective start time
        -- of the position transaction on the position_transactions table then this specifies the
        -- update of the position transaction
        -- Update position transaction with the latest version
        --
        UPDATE position_transactions
        SET logical_load_timestamp           = lv_logical_load_timestamp,
            updated_by                       = p_user,
            update_timestamp                 = SYSTIMESTAMP,
            effective_start_timestamp        = p_effective_start_timestamp,
            open_trade_id                    = p_open_trade_id,
            trade_id                         = p_trade_id,
            booking_number                   = p_transaction_booking_number,
            publish_time                     = p_publish_time,
            event_time                       = p_event_time,
            version_number                   = p_version_number,
            is_deleted                       = p_is_deleted,
            creation_time                    = p_creation_time,
            creation_identity_token          = p_creation_identity_token,
            crtn_on_bhlf_of_idntty_tkn       = p_crtn_on_bhlf_of_idntty_tkn,
            update_time                      = p_update_time,
            update_identity_token            = p_update_identity_token,
            updt_on_bhlf_of_idntty_tkn       = p_updt_on_bhlf_of_idntty_tkn,
            session_key                      = p_session_key,
            trading_account_id               = p_trading_account_id,
            trading_account_type             = p_trading_account_type,
            trading_account_function         = p_trading_account_function,
            trading_account_codifier         = p_trading_account_codifier,
            trdng_accnt_primary_currency     = p_trdng_accnt_primary_currency,
            order_id                         = p_order_id,
            product_instrument_code          = p_product_instrument_code,
            product_wrapper_code             = p_product_wrapper_code,
            product_generation               = p_product_generation,
            product_point_multiplier         = lv_product_point_multiplier,
            product_currency                 = p_product_currency,
            product_frctnl_part_ratio        = lv_product_frctnl_part_ratio,
            is_ccy_in_fractional_parts       = lv_iscurrencyinfractionalparts,
            channel_id                       = p_channel_id,
            request_id                       = p_request_id,
            business_date                    = lv_business_date,
            reporting_date                   = lv_reporting_date,
            transaction_time                 = p_transaction_time,
            transaction_type                 = p_transaction_type,
            transaction_refcodifier          = p_transaction_refcodifier,
            transaction_refcode              = p_transaction_refcode,
            is_automatically_rolled          = p_is_automatically_rolled,
            position_direction               = p_position_direction,
            open_trade_quantity              = p_open_trade_quantity,
            quantity_designator              = lv_quantity_designator,
            normalised_pstn_qntty            = lv_normalised_pstn_qntty,
            normalised_pstn_qntty_crrncy     = lv_normalised_pstn_qntty_crncy,
            open_trade_amount                = p_open_trade_amount,
            open_trade_amount_currency       = p_open_trade_amount_currency,
            open_trade_amount_in_tapc        = p_open_trade_amount_in_tapc,
            open_trade_price                 = p_open_trade_price,
            opening_trade_amount_fx_rate     = p_opening_trade_amount_fx_rate,
            open_trade_time                  = p_open_trade_time,
            record_source                    = p_record_source,
            cash_transaction_seq             = lv_cash_transaction_seq,
            rolled_open_trade_id             = p_rolled_open_trade_id,
            opening_trade_app_to_units       = p_opening_trade_app_to_units,
            execution_type                   = p_execution_type,
            opening_trade_instrument_price   = p_opening_trade_instrmnt_price,
            trading_scope                    = p_trading_scope,
            binary_type                      = p_binary_type,
            settle_time                      = p_settle_time,
            tenor                            = p_tenor,
            strike_price                     = p_strike_price,
            strike_price_additional          = p_strike_price_additional,
            tenor_start_time                 = p_tenor_start_time,
            binary_result                    = p_binary_result,
            alloc_instrument_schema          = p_alloc_instrument_schema,
            open_trade_instrmnt_amount       = p_open_trade_instrmnt_amount,
            forced_margin_fx_rate            = p_forced_margin_fx_rate,
            opn_accrd_trnvr_in_accnt_ccy     = p_opn_accrd_trnvr_in_accnt_ccy,
            is_pair_ccy_in_frctnl_prts       = lv_is_pair_ccy_in_frctnl_prts,
            value_date                       = p_value_date,
            pair_currency                    = p_pair_currency,
            primary_currency                 = p_primary_currency,
            secondary_currency               = p_secondary_currency,
            primary_amount                   = p_primary_amount,
            secondary_amount                 = p_secondary_amount
        WHERE transaction_id                 = p_transaction_id AND
              platform                       = p_platform AND
              (nrg_common.has_value_changed(open_trade_id,p_open_trade_id) = 1 OR
               nrg_common.has_value_changed(trade_id,p_trade_id) = 1 OR
               nrg_common.has_value_changed(booking_number,p_transaction_booking_number) = 1 OR
               nrg_common.has_value_changed(record_source,p_record_source) = 1 OR
               nrg_common.has_value_changed(publish_time,p_publish_time) = 1 OR
               nrg_common.has_value_changed(event_time,p_event_time) = 1 OR
               nrg_common.has_value_changed(version_number,p_version_number) = 1 OR
               nrg_common.has_value_changed(is_deleted,p_is_deleted) = 1 OR
               nrg_common.has_value_changed(creation_time,p_creation_time) = 1 OR
               nrg_common.has_value_changed(creation_identity_token,p_creation_identity_token) = 1 OR
               nrg_common.has_value_changed(crtn_on_bhlf_of_idntty_tkn,p_crtn_on_bhlf_of_idntty_tkn) = 1 OR
               nrg_common.has_value_changed(update_time,p_update_time) = 1 OR
               nrg_common.has_value_changed(update_identity_token,p_update_identity_token) = 1 OR
               nrg_common.has_value_changed(updt_on_bhlf_of_idntty_tkn,p_updt_on_bhlf_of_idntty_tkn) = 1 OR
               nrg_common.has_value_changed(session_key,p_session_key) = 1 OR
               nrg_common.has_value_changed(trading_account_id,p_trading_account_id) = 1 OR
               nrg_common.has_value_changed(trading_account_type,p_trading_account_type) = 1 OR
               nrg_common.has_value_changed(trading_account_function,p_trading_account_function) = 1 OR
               nrg_common.has_value_changed(trading_account_codifier,p_trading_account_codifier) = 1 OR
               nrg_common.has_value_changed(trdng_accnt_primary_currency,p_trdng_accnt_primary_currency) = 1 OR
               nrg_common.has_value_changed(order_id,p_order_id) = 1 OR
               nrg_common.has_value_changed(product_instrument_code,p_product_instrument_code) = 1 OR
               nrg_common.has_value_changed(product_wrapper_code,p_product_wrapper_code) = 1 OR
               nrg_common.has_value_changed(product_generation,p_product_generation) = 1 OR
               nrg_common.has_value_changed(product_point_multiplier,lv_product_point_multiplier) = 1 OR
               nrg_common.has_value_changed(product_currency,p_product_currency) = 1 OR
               nrg_common.has_value_changed(product_frctnl_part_ratio,lv_product_frctnl_part_ratio) = 1 OR
               nrg_common.has_value_changed(is_ccy_in_fractional_parts,lv_iscurrencyinfractionalparts) = 1 OR
               nrg_common.has_value_changed(channel_id,p_channel_id) = 1 OR
               nrg_common.has_value_changed(request_id,p_request_id) = 1 OR
               nrg_common.has_value_changed(business_date,lv_business_date) = 1 OR
               nrg_common.has_value_changed(reporting_date,lv_reporting_date) = 1 OR
               nrg_common.has_value_changed(transaction_time,p_transaction_time) = 1 OR
               nrg_common.has_value_changed(transaction_type,p_transaction_type) = 1 OR
               nrg_common.has_value_changed(transaction_refcodifier,p_transaction_refcodifier) = 1 OR
               nrg_common.has_value_changed(transaction_refcode,p_transaction_refcode) = 1 OR
               nrg_common.has_value_changed(is_automatically_rolled,p_is_automatically_rolled) = 1 OR
               nrg_common.has_value_changed(position_direction,p_position_direction) = 1 OR
               nrg_common.has_value_changed(open_trade_quantity,p_open_trade_quantity) = 1 OR
               nrg_common.has_value_changed(quantity_designator,lv_quantity_designator) = 1 OR
               nrg_common.has_value_changed(normalised_pstn_qntty,lv_normalised_pstn_qntty) = 1 OR
               nrg_common.has_value_changed(normalised_pstn_qntty_crrncy,lv_normalised_pstn_qntty_crncy) = 1 OR
               nrg_common.has_value_changed(open_trade_amount,p_open_trade_amount) = 1 OR
               nrg_common.has_value_changed(open_trade_amount_currency,p_open_trade_amount_currency) = 1 OR
               nrg_common.has_value_changed(open_trade_amount_in_tapc,p_open_trade_amount_in_tapc) = 1 OR
               nrg_common.has_value_changed(open_trade_price,p_open_trade_price) = 1 OR
               nrg_common.has_value_changed(opening_trade_amount_fx_rate,p_opening_trade_amount_fx_rate) = 1 OR
               nrg_common.has_value_changed(open_trade_time,p_open_trade_time) = 1 OR
               nrg_common.has_value_changed(rolled_open_trade_id, p_rolled_open_trade_id) = 1 OR
               nrg_common.has_value_changed(opening_trade_app_to_units, p_opening_trade_app_to_units) = 1 OR
               nrg_common.has_value_changed(execution_type, p_execution_type) = 1 OR
               nrg_common.has_value_changed(opening_trade_instrument_price, p_opening_trade_instrmnt_price) = 1 OR
               nrg_common.has_value_changed(trading_scope, p_trading_scope) = 1 OR
               nrg_common.has_value_changed(binary_type, p_binary_type) = 1 OR
               nrg_common.has_value_changed(settle_time, p_settle_time) = 1 OR
               nrg_common.has_value_changed(tenor, p_tenor) = 1 OR
               nrg_common.has_value_changed(strike_price, p_strike_price) = 1 OR
               nrg_common.has_value_changed(strike_price_additional, p_strike_price_additional) = 1 OR
               nrg_common.has_value_changed(tenor_start_time, p_tenor_start_time) = 1 OR
               nrg_common.has_value_changed(binary_result ,p_binary_result) = 1 OR
               nrg_common.has_value_changed(p_alloc_instrument_schema , alloc_instrument_schema) = 1 OR
               nrg_common.has_value_changed(p_open_trade_instrmnt_amount, open_trade_instrmnt_amount) = 1 OR
               nrg_common.has_value_changed(p_forced_margin_fx_rate, forced_margin_fx_rate) = 1 OR
               nrg_common.has_value_changed(p_opn_accrd_trnvr_in_accnt_ccy, opn_accrd_trnvr_in_accnt_ccy) = 1 OR
               nrg_common.has_value_changed(lv_is_pair_ccy_in_frctnl_prts, is_pair_ccy_in_frctnl_prts) = 1 OR
               nrg_common.has_value_changed(p_value_date, value_date) = 1 OR
               nrg_common.has_value_changed(p_pair_currency, pair_currency) = 1 OR
               nrg_common.has_value_changed(p_primary_currency, primary_currency) = 1 OR
               nrg_common.has_value_changed(p_secondary_currency, secondary_currency) = 1 OR
               nrg_common.has_value_changed(p_primary_amount, primary_amount) = 1 OR
               nrg_common.has_value_changed(p_secondary_amount, secondary_amount) = 1
               );

        --
        -- Write To History if update occurred
        --
        IF lv_old_pstn_trnsctn.effective_start_timestamp <> gc_default_timestamp AND SQL%ROWCOUNT > 0  THEN
          --
          --If the existing trade is not a stub record and the same record has not been replayed
          --then put the old record in the history
          --
          put_history (p_old_pstn_trnsctn_record => lv_old_pstn_trnsctn,
                       p_effective_end_timestamp => p_effective_start_timestamp,
                       p_action => 'U');
        END IF;

        lv_old_pstn_trnsctn := NULL;

      WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time > p_effective_start_timestamp THEN
        --
        --When the effective start time of the incoming record is smaller than the effective start time of the
        --trade on the trades table then it signifies old message is being replayed.
        --Write to history table directly
        --

        SELECT
               p_transaction_id,
               p_open_trade_id,
               p_trade_id,
               p_transaction_booking_number,
               p_platform,
               lv_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_publish_time,
               p_event_time,
               p_version_number,
               p_is_deleted,
               p_creation_time,
               p_creation_identity_token,
               p_crtn_on_bhlf_of_idntty_tkn,
               p_update_time,
               p_update_identity_token,
               p_updt_on_bhlf_of_idntty_tkn,
               p_session_key,
               p_trading_account_id,
               p_trading_account_type,
               p_trading_account_function,
               p_trading_account_codifier,
               p_trdng_accnt_primary_currency,
               p_order_id,
               p_product_instrument_code,
               p_product_wrapper_code,
               p_product_generation,
               lv_product_point_multiplier,
               p_product_currency,
               lv_product_frctnl_part_ratio,
               lv_iscurrencyinfractionalparts,
               p_channel_id,
               p_request_id,
               lv_business_date,
               lv_reporting_date,
               p_transaction_time,
               p_transaction_type,
               p_transaction_refcodifier,
               p_transaction_refcode,
               p_is_automatically_rolled,
               p_position_direction,
               p_open_trade_quantity,
               lv_quantity_designator,
               lv_normalised_pstn_qntty,
               lv_normalised_pstn_qntty_crncy,
               p_open_trade_amount,
               p_open_trade_amount_currency,
               p_open_trade_amount_in_tapc,
               p_open_trade_price,
               p_opening_trade_amount_fx_rate,
               p_open_trade_time,
               p_record_source,
               lv_cash_transaction_seq,
               p_rolled_open_trade_id,
               p_opening_trade_app_to_units,
               p_execution_type,
               p_opening_trade_instrmnt_price,
               NULL opening_trade_strike_price,
               p_binary_type,
               p_settle_time,
               p_tenor,
               p_strike_price,
               p_strike_price_additional,
               p_tenor_start_time,
               p_binary_result,
               p_alloc_instrument_schema,
               p_open_trade_instrmnt_amount,
               p_forced_margin_fx_rate,
               p_opn_accrd_trnvr_in_accnt_ccy,
               lv_is_pair_ccy_in_frctnl_prts,
               p_value_date,
               p_pair_currency,
               p_primary_currency,
               p_secondary_currency,
               p_primary_amount,
               p_secondary_amount
        INTO
             lv_old_pstn_trnsctn.transaction_id,
             lv_old_pstn_trnsctn.open_trade_id,
             lv_old_pstn_trnsctn.trade_id,
             lv_old_pstn_trnsctn.booking_number,
             lv_old_pstn_trnsctn.platform,
             lv_old_pstn_trnsctn.logical_load_timestamp,
             lv_old_pstn_trnsctn.created_by,
             lv_old_pstn_trnsctn.create_timestamp,
             lv_old_pstn_trnsctn.updated_by,
             lv_old_pstn_trnsctn.update_timestamp,
             lv_old_pstn_trnsctn.effective_start_timestamp,
             lv_old_pstn_trnsctn.publish_time,
             lv_old_pstn_trnsctn.event_time,
             lv_old_pstn_trnsctn.version_number,
             lv_old_pstn_trnsctn.is_deleted,
             lv_old_pstn_trnsctn.creation_time,
             lv_old_pstn_trnsctn.creation_identity_token,
             lv_old_pstn_trnsctn.crtn_on_bhlf_of_idntty_tkn,
             lv_old_pstn_trnsctn.update_time,
             lv_old_pstn_trnsctn.update_identity_token,
             lv_old_pstn_trnsctn.updt_on_bhlf_of_idntty_tkn,
             lv_old_pstn_trnsctn.session_key,
             lv_old_pstn_trnsctn.trading_account_id,
             lv_old_pstn_trnsctn.trading_account_type,
             lv_old_pstn_trnsctn.trading_account_function,
             lv_old_pstn_trnsctn.trading_account_codifier,
             lv_old_pstn_trnsctn.trdng_accnt_primary_currency,
             lv_old_pstn_trnsctn.order_id,
             lv_old_pstn_trnsctn.product_instrument_code,
             lv_old_pstn_trnsctn.product_wrapper_code,
             lv_old_pstn_trnsctn.product_generation,
             lv_old_pstn_trnsctn.product_point_multiplier,
             lv_old_pstn_trnsctn.product_currency,
             lv_old_pstn_trnsctn.product_frctnl_part_ratio,
             lv_old_pstn_trnsctn.is_ccy_in_fractional_parts,
             lv_old_pstn_trnsctn.channel_id,
             lv_old_pstn_trnsctn.request_id,
             lv_old_pstn_trnsctn.business_date,
             lv_old_pstn_trnsctn.reporting_date,
             lv_old_pstn_trnsctn.transaction_time,
             lv_old_pstn_trnsctn.transaction_type,
             lv_old_pstn_trnsctn.transaction_refcodifier,
             lv_old_pstn_trnsctn.transaction_refcode,
             lv_old_pstn_trnsctn.is_automatically_rolled,
             lv_old_pstn_trnsctn.position_direction,
             lv_old_pstn_trnsctn.open_trade_quantity,
             lv_old_pstn_trnsctn.quantity_designator,
             lv_old_pstn_trnsctn.normalised_pstn_qntty,
             lv_old_pstn_trnsctn.normalised_pstn_qntty_crrncy,
             lv_old_pstn_trnsctn.open_trade_amount,
             lv_old_pstn_trnsctn.open_trade_amount_currency,
             lv_old_pstn_trnsctn.open_trade_amount_in_tapc,
             lv_old_pstn_trnsctn.open_trade_price,
             lv_old_pstn_trnsctn.opening_trade_amount_fx_rate,
             lv_old_pstn_trnsctn.open_trade_time,
             lv_old_pstn_trnsctn.record_source,
             lv_old_pstn_trnsctn.cash_transaction_seq,
             lv_old_pstn_trnsctn.rolled_open_trade_id,
             lv_old_pstn_trnsctn.opening_trade_app_to_units,
             lv_old_pstn_trnsctn.execution_type,
             lv_old_pstn_trnsctn.opening_trade_instrument_price,
             lv_old_pstn_trnsctn.opening_trade_strike_price,
             lv_old_pstn_trnsctn.binary_type,
             lv_old_pstn_trnsctn.settle_time,
             lv_old_pstn_trnsctn.tenor,
             lv_old_pstn_trnsctn.strike_price,
             lv_old_pstn_trnsctn.strike_price_additional,
             lv_old_pstn_trnsctn.tenor_start_time,
             lv_old_pstn_trnsctn.binary_result,
             lv_old_pstn_trnsctn.alloc_instrument_schema,
             lv_old_pstn_trnsctn.open_trade_instrmnt_amount,
             lv_old_pstn_trnsctn.forced_margin_fx_rate,
             lv_old_pstn_trnsctn.opn_accrd_trnvr_in_accnt_ccy,
             lv_old_pstn_trnsctn.is_pair_ccy_in_frctnl_prts,
             lv_old_pstn_trnsctn.value_date,
             lv_old_pstn_trnsctn.pair_currency,
             lv_old_pstn_trnsctn.primary_currency,
             lv_old_pstn_trnsctn.secondary_currency,
             lv_old_pstn_trnsctn.primary_amount,
             lv_old_pstn_trnsctn.secondary_amount
        FROM DUAL;

        put_history (p_old_pstn_trnsctn_record => lv_old_pstn_trnsctn,
                     p_effective_end_timestamp => p_effective_start_timestamp,
                     p_action                  => 'I');

        lv_old_pstn_trnsctn := NULL;

      ELSE
        RAISE lex_unknown_operation_type;
    END CASE;

  EXCEPTION
    WHEN lex_pstn_trnsctn_not_found THEN
      logger.logger.severe('Position Transaction Deleted Before Update and After Insert');
      logger.logger.set_module(NULL);
      raise_application_error(-20003, 'Position Transaction Deleted Before Update and After Insert');
    WHEN lex_unknown_operation_type THEN
      logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
      logger.logger.set_module(NULL);
      raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_position_transaction;

  -- ===================================================================================
  -- get_position_transaction_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the position transaction ids
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --      p_creation_time_from            Creation Time From
  --      p_creation_time_to              Creation Time To
  --
  -- Return:
  -- -------
  --
  --     Returns a list of position transaction ids
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000   Default Exception
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION get_position_transaction_ids (p_creation_time_from IN position_transactions.creation_time%TYPE,
                                         p_creation_time_to   IN position_transactions.creation_time%TYPE) RETURN SYS_REFCURSOR
  IS
    lcuv_result           SYS_REFCURSOR;
  BEGIN
    logger.logger.set_module('get_position_transaction_ids');

    OPEN lcuv_result FOR  SELECT transaction_id
                          FROM position_transactions
                          WHERE creation_time >= p_creation_time_from AND
                                creation_time < p_creation_time_to;

    RETURN lcuv_result;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20001, logger.logger.error_backtrace);
  END;
END nrg_position_transaction;
/