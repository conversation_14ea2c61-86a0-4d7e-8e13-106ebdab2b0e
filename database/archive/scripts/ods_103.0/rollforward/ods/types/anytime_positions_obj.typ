DROP TYPE bi_ods.anytime_positions_tab FORCE;
DROP TYPE bi_ods.anytime_positions_obj FORCE;

CREATE OR REPLACE TYPE bi_ods.anytime_positions_obj
IS
  OBJECT
  (
    order_id                       VARCHAR2(50),
    direction                      VARCHAR2(5),
    quantity                       NUMBER,
    amount                         NUMBER,
    amount_currency                VARCHAR2(3),
    amnt_in_trdng_accnt_prmry_ccy  NUMBER,
    open_trade_id                  VARCHAR2(50),
    open_trade_price               NUMBER,
    opening_trade_amount_fx_rate   NUMBER,
    opening_trade_app_to_units     NUMBER,
    open_time                      TIMESTAMP(6),
    quantity_designator            VARCHAR2(50),
    product_instrument_code        VARCHAR2(50),
    product_wrapper_code           VARCHAR2(50),
    product_point_multiplier       NUMBER,
    product_currency               VARCHAR2(3),
    trading_account_id             NUMBER,
    trading_account_type           VARCHAR2(50),
    trading_account_codifier       VARCHAR2(50),
    trading_account_function       VARCHAR2(50),
    trading_accnt_primary_ccy      VARCHAR2(3),
    is_automatically_rolled        VARCHAR2(3),
    rolled_open_trade_id           VARCHAR2(50),
    execution_type                 VARCHAR2(50),
    trading_scope                  VARCHAR2(50),
    binary_type                    VARCHAR2(50),
    settle_time                    TIMESTAMP(6),
    strike_price                   NUMBER,
    strike_price_additional        NUMBER,
    tenor                          VARCHAR2(50),
    tenor_start_time               TIMESTAMP(6),
    opening_trade_instrument_price NUMBER,
    forced_margin_fx_rate          NUMBER,
    opn_accrd_trnvr_in_accnt_ccy   NUMBER,
    value_date                     TIMESTAMP(6),
    pair_currency                  VARCHAR2(3),
    primary_currency               VARCHAR2(3),
    secondary_currency             VARCHAR2(3),
    primary_amount                 NUMBER,
    secondary_amount               NUMBER,
    margin_percentage              NUMBER
  );
  /