CREATE OR REPLACE PACKAGE BODY nrg_position_value
AS
  -- ===================================================================================
  -- NRG_POSITION_VALUE
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the account value model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --    ----------   ---------------   -----   ----------------------------------------
  --     03/04/2013   Sanket Mittal     1.0     Creation
  --     18/07/2013   Prachi Shah       1.1     Updated the name of local variable position_value_tab to position_values_Tab
  --     29/07/2013   Prachi Shah       1.2     Update the logic to populate business date, reporting date
  --     24/06/2016   Sanket Mittal     1.3     BER-2681-SWS 34 - Data Contract Changes - ValuedPosition
  --     24/10/2016   Sanket Mittal     1.4     BER-3007 Position Value Snapshot - deprecated attributes
  --     19/03/2019   Patrick Dinwiddy  1.5     BER-5031 Refactor table/package design
  --     30/04/2019   Patrick Dinwiddy  1.6     JCS-10566 New attribute
  --     15/06/2020   Patrick Dinwiddy  1.7     JCS-13087 New attributes
  --     19/08/2020   Patrick Dinwiddy  1.8     JCS-13461 New entities
  --     01/12/2020   Patrick Dinwiddy  1.9     JCS-14061
  --
  -- ===================================================================================

  -- ===================================================================================
  -- Package Private Sub Routines And Constants
  -- ===================================================================================

  gc_default_timestamp   CONSTANT     TIMESTAMP(6) := to_date('01-Jan-1970','DD-Mon-YYYY');
  gc_version             CONSTANT     VARCHAR2(3)  := '1.9';

  TYPE position_values_tab           IS TABLE OF position_value_snapshots%ROWTYPE;
  TYPE old_valued_open_trades_tab    IS TABLE OF position_valued_open_trades%ROWTYPE;
  TYPE fx_currency_positions_tab     IS TABLE OF fx_currency_positions%ROWTYPE;
  TYPE old_valued_fx_open_trades_tab IS TABLE OF valued_fx_open_trades%ROWTYPE;

  PROCEDURE put_history(p_old_values_trades        old_valued_open_trades_tab,
                        p_effective_end_timestamp  position_value_snapshots_h.effective_end_timestamp%TYPE,
                        p_action                   position_value_snapshots_h.action%TYPE) IS

  BEGIN
      FORALL lv_cnt IN 1..p_old_values_trades.COUNT
        INSERT INTO position_valued_open_trades_h(snapshot_time,
                                                  platform,
                                                  trading_account_id,
                                                  trading_account_type,
                                                  product_instrument_code,
                                                  product_wrapper_code,
                                                  order_id,
                                                  logical_load_timestamp,
                                                  created_by,
                                                  create_timestamp,
                                                  updated_by,
                                                  update_timestamp,
                                                  effective_start_timestamp,
                                                  effective_end_timestamp,
                                                  action,
                                                  action_timestamp,
                                                  business_date,
                                                  reporting_date,
                                                  customer_upnl_in_prmry_ccy,
                                                  customer_upnl_evluatn_price,
                                                  customer_upnl_evluatn_reval,
                                                  decision_maker,
                                                  customer_id,
                                                  accnt_mon_upnl_in_prmry_ccy,
                                                  accnt_mon_upnl_evluatn_price,
                                                  accnt_mon_upnl_evluatn_reval,
                                                  cmc_upnl_in_prmry_ccy,
                                                  cmc_upnl_evluatn_price,
                                                  cmc_upnl_evluatn_reval,
                                                  prime_margin,
                                                  accnt_mon_opn_trd_amt_inst_ccy,
                                                  accnt_mon_opn_trd_amt_prm_ccy,
                                                  accnt_mon_opn_trd_amnt_usd,
                                                  accnt_mon_opn_trd_amnt_gbp,
                                                  accnt_mon_opn_trd_amnt_eur,
                                                  unrlzd_cptl_gns_in_prmry_ccy,
                                                  gslo_absolute_limit_price,
                                                  open_trade_quantity,
                                                  direction,
                                                  intl_cust_opn_trd_amnt,
                                                  intl_cust_opn_trd_amnt_ccy,
                                                  intl_cust_opn_trd_amnt_fxr,
                                                  intl_cust_opn_trd_amnt_prm_ccy,
                                                  opening_trade_id,
                                                  opening_trade_price,
                                                  opn_accrd_trnvr_in_accnt_ccy,
                                                  customer_upnl_in_inst_ccy,
                                                  customer_opn_trd_amnt_inst_ccy,
                                                  customer_opn_trd_amnt_prm_ccy,
                                                  margin_evaluation_fx_reval,
                                                  accrued_fees,
                                                  accrued_fees_in_account_currency)
                                    VALUES (p_old_values_trades(lv_cnt).snapshot_time,
                                            p_old_values_trades(lv_cnt).platform,
                                            p_old_values_trades(lv_cnt).trading_account_id,
                                            p_old_values_trades(lv_cnt).trading_account_type,
                                            p_old_values_trades(lv_cnt).product_instrument_code,
                                            p_old_values_trades(lv_cnt).product_wrapper_code,
                                            p_old_values_trades(lv_cnt).order_id,
                                            p_old_values_trades(lv_cnt).logical_load_timestamp,
                                            p_old_values_trades(lv_cnt).created_by,
                                            p_old_values_trades(lv_cnt).create_timestamp,
                                            p_old_values_trades(lv_cnt).updated_by,
                                            p_old_values_trades(lv_cnt).update_timestamp,
                                            p_old_values_trades(lv_cnt).effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_values_trades(lv_cnt).business_date,
                                            p_old_values_trades(lv_cnt).reporting_date,
                                            p_old_values_trades(lv_cnt).customer_upnl_in_prmry_ccy,
                                            p_old_values_trades(lv_cnt).customer_upnl_evluatn_price,
                                            p_old_values_trades(lv_cnt).customer_upnl_evluatn_reval,
                                            p_old_values_trades(lv_cnt).decision_maker,
                                            p_old_values_trades(lv_cnt).customer_id,
                                            p_old_values_trades(lv_cnt).accnt_mon_upnl_in_prmry_ccy,
                                            p_old_values_trades(lv_cnt).accnt_mon_upnl_evluatn_price,
                                            p_old_values_trades(lv_cnt).accnt_mon_upnl_evluatn_reval,
                                            p_old_values_trades(lv_cnt).cmc_upnl_in_prmry_ccy,
                                            p_old_values_trades(lv_cnt).cmc_upnl_evluatn_price,
                                            p_old_values_trades(lv_cnt).cmc_upnl_evluatn_reval,
                                            p_old_values_trades(lv_cnt).prime_margin,
                                            p_old_values_trades(lv_cnt).accnt_mon_opn_trd_amt_inst_ccy,
                                            p_old_values_trades(lv_cnt).accnt_mon_opn_trd_amt_prm_ccy,
                                            p_old_values_trades(lv_cnt).accnt_mon_opn_trd_amnt_usd,
                                            p_old_values_trades(lv_cnt).accnt_mon_opn_trd_amnt_gbp,
                                            p_old_values_trades(lv_cnt).accnt_mon_opn_trd_amnt_eur,
                                            p_old_values_trades(lv_cnt).unrlzd_cptl_gns_in_prmry_ccy,
                                            p_old_values_trades(lv_cnt).gslo_absolute_limit_price,
                                            p_old_values_trades(lv_cnt).open_trade_quantity,
                                            p_old_values_trades(lv_cnt).direction,
                                            p_old_values_trades(lv_cnt).intl_cust_opn_trd_amnt,
                                            p_old_values_trades(lv_cnt).intl_cust_opn_trd_amnt_ccy,
                                            p_old_values_trades(lv_cnt).intl_cust_opn_trd_amnt_fxr,
                                            p_old_values_trades(lv_cnt).intl_cust_opn_trd_amnt_prm_ccy,
                                            p_old_values_trades(lv_cnt).opening_trade_id,
                                            p_old_values_trades(lv_cnt).opening_trade_price,
                                            p_old_values_trades(lv_cnt).opn_accrd_trnvr_in_accnt_ccy,
                                            p_old_values_trades(lv_cnt).customer_upnl_in_inst_ccy,
                                            p_old_values_trades(lv_cnt).customer_opn_trd_amnt_inst_ccy,
                                            p_old_values_trades(lv_cnt).customer_opn_trd_amnt_prm_ccy,
                                            p_old_values_trades(lv_cnt).margin_evaluation_fx_reval,
                                            p_old_values_trades(lv_cnt).accrued_fees,
                                            p_old_values_trades(lv_cnt).accrued_fees_in_account_currency);
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the history for account value snapshot
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_old_position_value       position_values_tab,
                        p_effective_end_timestamp  position_value_snapshots_h.effective_end_timestamp%TYPE,
                        p_action                   position_value_snapshots_h.action%TYPE,
                        p_snapshot_time            position_value_snapshots_h.snapshot_time%TYPE,
                        p_platform                 position_value_snapshots_h.platform%TYPE) IS

  BEGIN

    --
    --If there is an existing snapshot on the history table then delete it and insert the new data
    --

    DELETE position_value_snapshots_h
    WHERE platform = p_platform AND
          snapshot_time = p_snapshot_time;

    FORALL lv_cnt IN 1..p_old_position_value.COUNT
      INSERT INTO position_value_snapshots_h(snapshot_time,
                                             platform,
                                             trading_account_id,
                                             trading_account_type,
                                             product_instrument_code,
                                             product_wrapper_code,
                                             logical_load_timestamp,
                                             created_by,
                                             create_timestamp,
                                             updated_by,
                                             update_timestamp,
                                             effective_start_timestamp,
                                             effective_end_timestamp,
                                             action,
                                             action_timestamp,
                                             business_date,
                                             reporting_date,
                                             trading_account_function,
                                             trading_account_primary_ccy,
                                             product_generation,
                                             product_currency,
                                             standard_margin,
                                             customer_upnl_in_prmry_ccy,
                                             prime_margin,
                                             binary_type,
                                             tenor,
                                             margin_evaluation_price,
                                             margin_evaluation_reval_rate,
                                             instrument_type,
                                             underlying_instrument_code,
                                             accnt_mon_upnl_in_prmry_ccy,
                                             cmc_upnl_in_prmry_ccy,
                                             app_to_units,
                                             price_stream_code,
                                             customer_level_1_bid_price,
                                             customer_level_1_mid_price,
                                             customer_level_1_ask_price,
                                             inst_to_prmry_ccy_bid_fxr,
                                             inst_to_prmry_ccy_mid_fxr,
                                             inst_to_prmry_ccy_ask_fxr,
                                             is_valid,
                                             pms_standard_margin)
                                      VALUES(p_snapshot_time,
                                             p_old_position_value(lv_cnt).platform,
                                             p_old_position_value(lv_cnt).trading_account_id,
                                             p_old_position_value(lv_cnt).trading_account_type,
                                             p_old_position_value(lv_cnt).product_instrument_code,
                                             p_old_position_value(lv_cnt).product_wrapper_code,
                                             p_old_position_value(lv_cnt).logical_load_timestamp,
                                             p_old_position_value(lv_cnt).created_by,
                                             p_old_position_value(lv_cnt).create_timestamp,
                                             p_old_position_value(lv_cnt).updated_by,
                                             p_old_position_value(lv_cnt).update_timestamp,
                                             p_old_position_value(lv_cnt).effective_start_timestamp,
                                             p_effective_end_timestamp,
                                             p_action,
                                             SYSTIMESTAMP,
                                             p_old_position_value(lv_cnt).business_date,
                                             p_old_position_value(lv_cnt).reporting_date,
                                             p_old_position_value(lv_cnt).trading_account_function,
                                             p_old_position_value(lv_cnt).trading_account_primary_ccy,
                                             p_old_position_value(lv_cnt).product_generation,
                                             p_old_position_value(lv_cnt).product_currency,
                                             p_old_position_value(lv_cnt).standard_margin,
                                             p_old_position_value(lv_cnt).customer_upnl_in_prmry_ccy,
                                             p_old_position_value(lv_cnt).prime_margin,
                                             p_old_position_value(lv_cnt).binary_type,
                                             p_old_position_value(lv_cnt).tenor,
                                             p_old_position_value(lv_cnt).margin_evaluation_price,
                                             p_old_position_value(lv_cnt).margin_evaluation_reval_rate,
                                             p_old_position_value(lv_cnt).instrument_type,
                                             p_old_position_value(lv_cnt).underlying_instrument_code,
                                             p_old_position_value(lv_cnt).accnt_mon_upnl_in_prmry_ccy,
                                             p_old_position_value(lv_cnt).cmc_upnl_in_prmry_ccy,
                                             p_old_position_value(lv_cnt).app_to_units,
                                             p_old_position_value(lv_cnt).price_stream_code,
                                             p_old_position_value(lv_cnt).customer_level_1_bid_price,
                                             p_old_position_value(lv_cnt).customer_level_1_mid_price,
                                             p_old_position_value(lv_cnt).customer_level_1_ask_price,
                                             p_old_position_value(lv_cnt).inst_to_prmry_ccy_bid_fxr,
                                             p_old_position_value(lv_cnt).inst_to_prmry_ccy_mid_fxr,
                                             p_old_position_value(lv_cnt).inst_to_prmry_ccy_ask_fxr,
                                             p_old_position_value(lv_cnt).is_valid,
                                             p_old_position_value(lv_cnt).pms_standard_margin);
  END;

  PROCEDURE put_history(p_old_valued_fx_trades     old_valued_fx_open_trades_tab,
                        p_effective_end_timestamp  valued_fx_open_trades_h.effective_end_timestamp%TYPE,
                        p_action                   valued_fx_open_trades_h.action%TYPE) IS

  BEGIN
      FORALL lv_cnt IN 1..p_old_valued_fx_trades.COUNT
        INSERT INTO valued_fx_open_trades_h(snapshot_time,
                                            trading_account_id,
                                            order_id,
                                            platform,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            effective_end_timestamp,
                                            action,
                                            action_timestamp,
                                            business_date,
                                            reporting_date,
                                            decision_maker,
                                            customer_id,
                                            instrument_code,
                                            stream_code,
                                            value_date,
                                            primary_amount,
                                            direction,
                                            opening_trade_id,
                                            opening_trade_price,
                                            opening_trade_spot_price,
                                            customer_upnl_in_prmry_ccy,
                                            customer_evaluation_price,
                                            customer_evaluation_spot_price,
                                            customer_l1_bid_spot_price,
                                            customer_l1_mid_spot_price,
                                            customer_l1_ask_spot_price,
                                            cstmr_evltn_fx_reval_rate,
                                            cstmr_evltn_bid_fx_reval_rate,
                                            cstmr_evltn_ask_fx_reval_rate,
                                            cstmr_evltn_mid_fx_reval_rate,
                                            customer_secondary_amount,
                                            intl_cstmr_secondary_amount,
                                            customer_upnl_in_inst_ccy,
                                            accnt_mon_evltn_price,
                                            accnt_mon_evltn_spot_price,
                                            accnt_mon_evltn_fx_reval_rate,
                                            accnt_mon_upnl_in_prmry_ccy,
                                            accnt_mon_secondary_amount,
                                            cmc_upnl_in_prmry_ccy,
                                            cmc_evaluation_price,
                                            cmc_evaluation_fx_reval_rate,
                                            cmc_evaluation_spot_price,
                                            evaluation_tenor,
                                            is_valid)
                                    VALUES (p_old_valued_fx_trades(lv_cnt).snapshot_time,
                                            p_old_valued_fx_trades(lv_cnt).trading_account_id,
                                            p_old_valued_fx_trades(lv_cnt).order_id,
                                            p_old_valued_fx_trades(lv_cnt).platform,
                                            p_old_valued_fx_trades(lv_cnt).logical_load_timestamp,
                                            p_old_valued_fx_trades(lv_cnt).created_by,
                                            p_old_valued_fx_trades(lv_cnt).create_timestamp,
                                            p_old_valued_fx_trades(lv_cnt).updated_by,
                                            p_old_valued_fx_trades(lv_cnt).update_timestamp,
                                            p_old_valued_fx_trades(lv_cnt).effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_valued_fx_trades(lv_cnt).business_date,
                                            p_old_valued_fx_trades(lv_cnt).reporting_date,
                                            p_old_valued_fx_trades(lv_cnt).decision_maker,
                                            p_old_valued_fx_trades(lv_cnt).customer_id,
                                            p_old_valued_fx_trades(lv_cnt).instrument_code,
                                            p_old_valued_fx_trades(lv_cnt).stream_code,
                                            p_old_valued_fx_trades(lv_cnt).value_date,
                                            p_old_valued_fx_trades(lv_cnt).primary_amount,
                                            p_old_valued_fx_trades(lv_cnt).direction,
                                            p_old_valued_fx_trades(lv_cnt).opening_trade_id,
                                            p_old_valued_fx_trades(lv_cnt).opening_trade_price,
                                            p_old_valued_fx_trades(lv_cnt).opening_trade_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).customer_upnl_in_prmry_ccy,
                                            p_old_valued_fx_trades(lv_cnt).customer_evaluation_price,
                                            p_old_valued_fx_trades(lv_cnt).customer_evaluation_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).customer_l1_bid_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).customer_l1_mid_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).customer_l1_ask_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).cstmr_evltn_fx_reval_rate,
                                            p_old_valued_fx_trades(lv_cnt).cstmr_evltn_bid_fx_reval_rate,
                                            p_old_valued_fx_trades(lv_cnt).cstmr_evltn_ask_fx_reval_rate,
                                            p_old_valued_fx_trades(lv_cnt).cstmr_evltn_mid_fx_reval_rate,
                                            p_old_valued_fx_trades(lv_cnt).customer_secondary_amount,
                                            p_old_valued_fx_trades(lv_cnt).intl_cstmr_secondary_amount,
                                            p_old_valued_fx_trades(lv_cnt).customer_upnl_in_inst_ccy,
                                            p_old_valued_fx_trades(lv_cnt).accnt_mon_evltn_price,
                                            p_old_valued_fx_trades(lv_cnt).accnt_mon_evltn_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).accnt_mon_evltn_fx_reval_rate,
                                            p_old_valued_fx_trades(lv_cnt).accnt_mon_upnl_in_prmry_ccy,
                                            p_old_valued_fx_trades(lv_cnt).accnt_mon_secondary_amount,
                                            p_old_valued_fx_trades(lv_cnt).cmc_upnl_in_prmry_ccy,
                                            p_old_valued_fx_trades(lv_cnt).cmc_evaluation_price,
                                            p_old_valued_fx_trades(lv_cnt).cmc_evaluation_fx_reval_rate,
                                            p_old_valued_fx_trades(lv_cnt).cmc_evaluation_spot_price,
                                            p_old_valued_fx_trades(lv_cnt).evaluation_tenor,
                                            p_old_valued_fx_trades(lv_cnt).is_valid);
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the history for fx_currency_positions
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_old_fx_position_value    fx_currency_positions_tab,
                        p_effective_end_timestamp  fx_currency_positions_h.effective_end_timestamp%TYPE,
                        p_action                   fx_currency_positions_h.action%TYPE,
                        p_snapshot_time            fx_currency_positions_h.snapshot_time%TYPE,
                        p_platform                 fx_currency_positions_h.platform%TYPE) IS

  BEGIN

    --
    --If there is an existing snapshot on the history table then delete it and insert the new data
    --

    DELETE fx_currency_positions_h
    WHERE platform = p_platform AND
          snapshot_time = p_snapshot_time;

    FORALL lv_cnt IN 1..p_old_fx_position_value.COUNT
      INSERT INTO fx_currency_positions_h(snapshot_time,
                                          trading_account_id,
                                          currency,
                                          platform,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          effective_start_timestamp,
                                          effective_end_timestamp,
                                          action,
                                          action_timestamp,
                                          business_date,
                                          reporting_date,
                                          fx_currency_position_time,
                                          trading_account_function,
                                          currency_amount,
                                          main_margin_currency,
                                          margin_direction,
                                          margin_fx_rate,
                                          margin,
                                          margin_in_main_margin_ccy,
                                          is_valid)
                                   VALUES(p_old_fx_position_value(lv_cnt).snapshot_time,
                                          p_old_fx_position_value(lv_cnt).trading_account_id,
                                          p_old_fx_position_value(lv_cnt).currency,
                                          p_old_fx_position_value(lv_cnt).platform,
                                          p_old_fx_position_value(lv_cnt).logical_load_timestamp,
                                          p_old_fx_position_value(lv_cnt).created_by,
                                          p_old_fx_position_value(lv_cnt).create_timestamp,
                                          p_old_fx_position_value(lv_cnt).updated_by,
                                          p_old_fx_position_value(lv_cnt).update_timestamp,
                                          p_old_fx_position_value(lv_cnt).effective_start_timestamp,
                                          p_effective_end_timestamp,
                                          p_action,
                                          SYSTIMESTAMP,
                                          p_old_fx_position_value(lv_cnt).business_date,
                                          p_old_fx_position_value(lv_cnt).reporting_date,
                                          p_old_fx_position_value(lv_cnt).fx_currency_position_time,
                                          p_old_fx_position_value(lv_cnt).trading_account_function,
                                          p_old_fx_position_value(lv_cnt).currency_amount,
                                          p_old_fx_position_value(lv_cnt).main_margin_currency,
                                          p_old_fx_position_value(lv_cnt).margin_direction,
                                          p_old_fx_position_value(lv_cnt).margin_fx_rate,
                                          p_old_fx_position_value(lv_cnt).margin,
                                          p_old_fx_position_value(lv_cnt).margin_in_main_margin_ccy,
                                          p_old_fx_position_value(lv_cnt).is_valid
                                          );
  END;

  -- ===================================================================================
  -- Public Subroutines
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION version
      RETURN VARCHAR2 DETERMINISTIC
  IS
  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END version;


  -- ===================================================================================
  -- put_account_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put account values
  --
  -- Notes:
  -- ------
  --
  --
  --     Tables potentially populated:
  --
  --     ACCOUNT_VALUES
  --     ACCOUNT_VALUES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_account_value                  Account Values
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_position_value(p_user                            IN position_value_snapshots.created_by%TYPE,
                               p_effective_start_timestamp       IN position_value_snapshots.effective_start_timestamp%TYPE,
                               p_platform                        IN position_value_snapshots.platform%TYPE,
                               p_snapshot_time                   IN position_value_snapshots.snapshot_time%TYPE,
                               p_position_value                  IN position_value_tab) IS

    lv_business_date            DATE;
    lv_reporting_date           DATE;
    lv_logical_load_timestamp   TIMESTAMP(6);
    lv_old_position_values      position_values_tab;

    lv_old_valued_trades        old_valued_open_trades_tab;


    lv_exists                   NUMBER;

  BEGIN

    --logger.logger.set_module('nrg_position_value.put_position_value');

    --
    --Evaluate Business date
    --

    --lv_business_date := nrg_common.get_business_date(p_snapshot_time);
    -- Updating the logic as per eod_positions table

    lv_business_date := trunc(p_effective_start_timestamp);



    --
    --Evaluate Reporting Date
    --

    --lv_reporting_date := nrg_common.get_reporting_date(p_snapshot_time);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

    --
    --Set Logical Load Timestamp
    --
    lv_logical_load_timestamp := SYSTIMESTAMP;

    --
    --Create Trading Account Stubs If Account Does Not Exist
    --



    IF p_position_value IS NOT NULL AND p_position_value.COUNT > 0 THEN
      FOR lv_cnt IN 1..p_position_value.COUNT LOOP

        nrg_trading_account.create_trading_account_stub(p_user                      => p_user,
                                                        p_logical_load_timestamp    => lv_logical_load_timestamp,
                                                        p_effective_start_timestamp => p_effective_start_timestamp,
                                                        p_trading_account_id        => p_position_value(lv_cnt).trading_account_id,
                                                        p_trading_account_type      => p_position_value(lv_cnt).trading_account_type);


      END LOOP;
    END IF;

    FOR lv_cnt IN 1..p_position_value.COUNT LOOP
      IF p_position_value(lv_cnt).valued_open_trades IS NOT NULL AND p_position_value(lv_cnt).valued_open_trades.COUNT > 1 THEN
        FOR lv_cnt_valued_trade IN 1.. p_position_value(lv_cnt).valued_open_trades.COUNT LOOP
          IF p_position_value(lv_cnt).valued_open_trades(lv_cnt_valued_trade).order_id IS NOT NULL THEN
            nrg_order.create_order_stub(p_order_id                    => p_position_value(lv_cnt).valued_open_trades(lv_cnt_valued_trade).order_id,
                                        p_platform                    => p_platform,
                                        p_logical_load_timestamp      => lv_logical_load_timestamp,
                                        p_effective_start_timestamp   => p_effective_start_timestamp,
                                        p_user                        => p_user);
          END IF;
        END LOOP;
      END IF;
    END LOOP;

    --
    --Check If the Snapshot Already Exists
    --

    BEGIN
      SELECT 1
      INTO lv_exists
      FROM position_value_snapshots
      WHERE platform = p_platform AND
            snapshot_time = p_snapshot_time AND
            rownum = 1;
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        lv_exists := 0;
    END;

    IF lv_exists = 1 THEN
      --
      --If the snapshot exists then take the copy of the snapshot from the base table and delete the data
      --
      SELECT *
      BULK COLLECT INTO lv_old_position_values
      FROM position_value_snapshots
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      SELECT *
      BULK COLLECT INTO lv_old_valued_trades
      FROM position_valued_open_trades
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      --
      --Clean up data for previous history of the same snapshot
      --
      DELETE FROM position_valued_open_trades_h
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      --
      --Write the existing data to history
      --


      put_history(lv_old_valued_trades,
                  p_effective_start_timestamp,
                  'U');

      put_history(p_old_position_value       => lv_old_position_values,
                  p_effective_end_timestamp  => p_effective_start_timestamp,
                  p_action                   => 'U',
                  p_snapshot_time            => p_snapshot_time,
                  p_platform                 => p_platform);


      DELETE position_valued_open_trades
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      DELETE position_value_snapshots
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;
    END IF;

    FORALL lv_cnt IN 1..p_position_value.COUNT
      INSERT INTO position_value_snapshots( snapshot_time,
                                            platform,
                                            trading_account_id,
                                            trading_account_type,
                                            product_instrument_code,
                                            product_wrapper_code,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            business_date,
                                            reporting_date,
                                            trading_account_function,
                                            trading_account_primary_ccy,
                                            product_generation,
                                            product_currency,
                                            standard_margin,
                                            customer_upnl_in_prmry_ccy,
                                            prime_margin,
                                            binary_type,
                                            tenor,
                                            margin_evaluation_price,
                                            margin_evaluation_reval_rate,
                                            instrument_type,
                                            underlying_instrument_code,
                                            accnt_mon_upnl_in_prmry_ccy,
                                            cmc_upnl_in_prmry_ccy,
                                            app_to_units,
                                            price_stream_code,
                                            customer_level_1_bid_price,
                                            customer_level_1_mid_price,
                                            customer_level_1_ask_price,
                                            inst_to_prmry_ccy_bid_fxr,
                                            inst_to_prmry_ccy_mid_fxr,
                                            inst_to_prmry_ccy_ask_fxr,
                                            is_valid,
                                            pms_standard_margin
                                            )
                                    VALUES(p_snapshot_time,
                                           p_platform,
                                           p_position_value(lv_cnt).trading_account_id,
                                           p_position_value(lv_cnt).trading_account_type,
                                           p_position_value(lv_cnt).product_instrument_code,
                                           p_position_value(lv_cnt).product_wrapper_code,
                                           lv_logical_load_timestamp,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_effective_start_timestamp,
                                           lv_business_date,
                                           lv_reporting_date,
                                           p_position_value(lv_cnt).trading_account_function,
                                           p_position_value(lv_cnt).trading_account_primary_ccy,
                                           p_position_value(lv_cnt).product_generation,
                                           p_position_value(lv_cnt).product_currency,
                                           p_position_value(lv_cnt).standard_margin,
                                           p_position_value(lv_cnt).customer_upnl_in_prmry_ccy,
                                           p_position_value(lv_cnt).prime_margin,
                                           p_position_value(lv_cnt).binary_type,
                                           p_position_value(lv_cnt).tenor,
                                           p_position_value(lv_cnt).margin_evaluation_price,
                                           p_position_value(lv_cnt).margin_evaluation_reval_rate,
                                           p_position_value(lv_cnt).instrument_type,
                                           p_position_value(lv_cnt).underlying_instrument_code,
                                           p_position_value(lv_cnt).accnt_mon_upnl_in_prmry_ccy,
                                           p_position_value(lv_cnt).cmc_upnl_in_prmry_ccy,
                                           p_position_value(lv_cnt).app_to_units,
                                           p_position_value(lv_cnt).price_stream_code,
                                           p_position_value(lv_cnt).customer_level_1_bid_price,
                                           p_position_value(lv_cnt).customer_level_1_mid_price,
                                           p_position_value(lv_cnt).customer_level_1_ask_price,
                                           p_position_value(lv_cnt).inst_to_prmry_ccy_bid_fxr,
                                           p_position_value(lv_cnt).inst_to_prmry_ccy_mid_fxr,
                                           p_position_value(lv_cnt).inst_to_prmry_ccy_ask_fxr,
                                           p_position_value(lv_cnt).is_valid,
                                           p_position_value(lv_cnt).pms_standard_margin
                                           );
  
    FOR lv_cnt IN 1..p_position_value.COUNT LOOP
      
      IF p_position_value(lv_cnt).valued_open_trades IS NOT NULL THEN
        FORALL lv_cnt_values_trades IN 1..p_position_value(lv_cnt).valued_open_trades.COUNT
        
          INSERT INTO position_valued_open_trades(snapshot_time,
                                                  platform,
                                                  trading_account_id,
                                                  trading_account_type,
                                                  product_instrument_code,
                                                  product_wrapper_code,
                                                  order_id,
                                                  logical_load_timestamp,
                                                  created_by,
                                                  create_timestamp,
                                                  updated_by,
                                                  update_timestamp,
                                                  effective_start_timestamp,
                                                  business_date,
                                                  reporting_date,
                                                  customer_upnl_in_prmry_ccy,
                                                  customer_upnl_evluatn_price,
                                                  customer_upnl_evluatn_reval,
                                                  decision_maker,
                                                  customer_id,
                                                  accnt_mon_upnl_in_prmry_ccy,
                                                  accnt_mon_upnl_evluatn_price,
                                                  accnt_mon_upnl_evluatn_reval,
                                                  cmc_upnl_in_prmry_ccy,
                                                  cmc_upnl_evluatn_price,
                                                  cmc_upnl_evluatn_reval,
                                                  prime_margin,
                                                  accnt_mon_opn_trd_amt_inst_ccy,
                                                  accnt_mon_opn_trd_amt_prm_ccy,
                                                  accnt_mon_opn_trd_amnt_usd,
                                                  accnt_mon_opn_trd_amnt_gbp,
                                                  accnt_mon_opn_trd_amnt_eur,
                                                  unrlzd_cptl_gns_in_prmry_ccy,
                                                  gslo_absolute_limit_price,
                                                  open_trade_quantity,
                                                  direction,
                                                  intl_cust_opn_trd_amnt,
                                                  intl_cust_opn_trd_amnt_ccy,
                                                  intl_cust_opn_trd_amnt_fxr,
                                                  intl_cust_opn_trd_amnt_prm_ccy,
                                                  opening_trade_id,
                                                  opening_trade_price,
                                                  opn_accrd_trnvr_in_accnt_ccy,
                                                  customer_upnl_in_inst_ccy,
                                                  customer_opn_trd_amnt_inst_ccy,
                                                  customer_opn_trd_amnt_prm_ccy,
                                                  margin_evaluation_fx_reval,
                                                  accrued_fees
                                                  )
                                    VALUES (p_snapshot_time,
                                            p_platform,
                                            p_position_value(lv_cnt).trading_account_id,
                                            p_position_value(lv_cnt).trading_account_type,
                                            p_position_value(lv_cnt).product_instrument_code,
                                            p_position_value(lv_cnt).product_wrapper_code,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).order_id,
                                            lv_logical_load_timestamp,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_effective_start_timestamp,
                                            lv_business_date,
                                            lv_reporting_date,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_upnl_in_prmry_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_upnl_evluatn_price,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_upnl_evluatn_reval,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).decision_maker,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_id,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_upnl_in_prmry_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_upnl_evluatn_price,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_upnl_evluatn_reval,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).cmc_upnl_in_prmry_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).cmc_upnl_evluatn_price,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).cmc_upnl_evluatn_reval,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).prime_margin,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_opn_trd_amt_inst_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_opn_trd_amt_prm_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_opn_trd_amnt_usd,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_opn_trd_amnt_gbp,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accnt_mon_opn_trd_amnt_eur,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).unrlzd_cptl_gns_in_prmry_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).gslo_absolute_limit_price,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).open_trade_quantity,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).direction,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).intl_cust_opn_trd_amnt,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).intl_cust_opn_trd_amnt_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).intl_cust_opn_trd_amnt_fxr,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).intl_cust_opn_trd_amnt_prm_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).opening_trade_id,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).opening_trade_price,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).opn_accrd_trnvr_in_accnt_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_upnl_in_inst_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_opn_trd_amnt_inst_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).customer_opn_trd_amnt_prm_ccy,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).margin_evaluation_fx_reval,
                                            p_position_value(lv_cnt).valued_open_trades(lv_cnt_values_trades).accrued_fees
                                            );     
                                                                      
      END IF;      
    END LOOP;
  
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_latest_position_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put latest position values
  --
  -- Notes:
  -- ------
  --
  --
  --     Tables potentially populated:
  --
  --     LATEST_VALUED_POSITIONS
  --     LATEST_VALUED_OPEN_TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_position_value                  Account Values
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_latest_position_value(p_user                            IN position_value_snapshots.created_by%TYPE,
                                      p_effective_start_timestamp       IN position_value_snapshots.effective_start_timestamp%TYPE,
                                      p_platform                        IN position_value_snapshots.platform%TYPE,
                                      p_snapshot_time                   IN position_value_snapshots.snapshot_time%TYPE,
                                      p_position_value                  IN position_value_tab) IS

    lv_business_date            DATE;
    lv_reporting_date           DATE;
    lv_logical_load_timestamp   TIMESTAMP(6);

  BEGIN

    lv_business_date := trunc(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

    lv_logical_load_timestamp := SYSTIMESTAMP;

    IF p_position_value IS NOT NULL AND p_position_value.COUNT > 0 THEN
      FOR lv_cnt IN 1..p_position_value.COUNT LOOP

        nrg_trading_account.create_trading_account_stub(p_user                      => p_user,
                                                        p_logical_load_timestamp    => lv_logical_load_timestamp,
                                                        p_effective_start_timestamp => p_effective_start_timestamp,
                                                        p_trading_account_id        => p_position_value(lv_cnt).trading_account_id,
                                                        p_trading_account_type      => p_position_value(lv_cnt).trading_account_type);

      END LOOP;
    END IF;

    FOR lv_cnt IN 1..p_position_value.COUNT LOOP
      IF p_position_value(lv_cnt).valued_open_trades IS NOT NULL AND p_position_value(lv_cnt).valued_open_trades.COUNT > 1 THEN
        FOR lv_cnt_valued_trade IN 1.. p_position_value(lv_cnt).valued_open_trades.COUNT LOOP
          IF p_position_value(lv_cnt).valued_open_trades(lv_cnt_valued_trade).order_id IS NOT NULL THEN
            nrg_order.create_order_stub(p_order_id                    => p_position_value(lv_cnt).valued_open_trades(lv_cnt_valued_trade).order_id,
                                        p_platform                    => p_platform,
                                        p_logical_load_timestamp      => lv_logical_load_timestamp,
                                        p_effective_start_timestamp   => p_effective_start_timestamp,
                                        p_user                        => p_user);
          END IF;
        END LOOP;
      END IF;
    END LOOP;

    DELETE latest_valued_open_trades;
    DELETE latest_valued_positions;

    INSERT INTO latest_valued_positions (
           snapshot_time,
           platform,
           trading_account_id,
           trading_account_type,
           product_instrument_code,
           product_wrapper_code,
           logical_load_timestamp,
           created_by,
           create_timestamp,
           updated_by,
           update_timestamp,
           effective_start_timestamp,
           business_date,
           reporting_date,
           trading_account_function,
           trading_account_primary_ccy,
           product_generation,
           product_currency,
           standard_margin,
           customer_upnl_in_prmry_ccy,
           prime_margin,
           margin_evaluation_price,
           margin_evaluation_reval_rate,
           instrument_type,
           underlying_instrument_code,
           accnt_mon_upnl_in_prmry_ccy,
           cmc_upnl_in_prmry_ccy,
           app_to_units,
           price_stream_code,
           customer_level_1_bid_price,
           customer_level_1_mid_price,
           customer_level_1_ask_price,
           inst_to_prmry_ccy_bid_fxr,
           inst_to_prmry_ccy_mid_fxr,
           inst_to_prmry_ccy_ask_fxr,
           is_valid,
           pms_standard_margin)
    SELECT p_snapshot_time                          AS snapshot_time,
           p_platform                               AS platform,
           new_version.trading_account_id           AS trading_account_id,
           new_version.trading_account_type         AS trading_account_type,
           new_version.product_instrument_code      AS product_instrument_code,
           new_version.product_wrapper_code         AS product_wrapper_code,
           lv_logical_load_timestamp                AS logical_load_timestamp,
           p_user                                   AS created_by,
           lv_logical_load_timestamp                AS create_timestamp,
           p_user                                   AS updated_by,
           lv_logical_load_timestamp                AS update_timestamp,
           p_effective_start_timestamp              AS effective_start_timestamp,
           lv_business_date                         AS business_date,
           lv_reporting_date                        AS reporting_date,
           new_version.trading_account_function     AS trading_account_function,
           new_version.trading_account_primary_ccy  AS trading_account_primary_ccy,
           new_version.product_generation           AS product_generation,
           new_version.product_currency             AS product_currency,
           new_version.standard_margin              AS standard_margin,
           new_version.customer_upnl_in_prmry_ccy   AS customer_upnl_in_prmry_ccy,
           new_version.prime_margin                 AS prime_margin,
           new_version.margin_evaluation_price      AS margin_evaluation_price,
           new_version.margin_evaluation_reval_rate AS margin_evaluation_reval_rate,
           new_version.instrument_type              AS instrument_type,
           new_version.underlying_instrument_code   AS underlying_instrument_code,
           new_version.accnt_mon_upnl_in_prmry_ccy  AS accnt_mon_upnl_in_prmry_ccy,
           new_version.cmc_upnl_in_prmry_ccy        AS cmc_upnl_in_prmry_ccy,
           new_version.app_to_units                 AS app_to_units,
           new_version.price_stream_code            AS price_stream_code,
           new_version.customer_level_1_bid_price   AS customer_level_1_bid_price,
           new_version.customer_level_1_mid_price   AS customer_level_1_mid_price,
           new_version.customer_level_1_ask_price   AS customer_level_1_ask_price,
           new_version.inst_to_prmry_ccy_bid_fxr    AS inst_to_prmry_ccy_bid_fxr,
           new_version.inst_to_prmry_ccy_mid_fxr    AS inst_to_prmry_ccy_mid_fxr,
           new_version.inst_to_prmry_ccy_ask_fxr    AS inst_to_prmry_ccy_ask_fxr,
           new_version.is_valid                     AS is_valid,
           new_version.pms_standard_margin          AS pms_standard_margin
      FROM TABLE(CAST(p_position_value AS position_value_tab)) new_version;

    FOR lv_cnt IN 1..p_position_value.COUNT LOOP

      INSERT INTO latest_valued_open_trades(snapshot_time,
                                            platform,
                                            trading_account_id,
                                            trading_account_type,
                                            product_instrument_code,
                                            product_wrapper_code,
                                            order_id,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            business_date,
                                            reporting_date,
                                            customer_upnl_in_prmry_ccy,
                                            customer_upnl_evluatn_price,
                                            customer_upnl_evluatn_reval,
                                            decision_maker,
                                            customer_id,
                                            accnt_mon_upnl_in_prmry_ccy,
                                            accnt_mon_upnl_evluatn_price,
                                            accnt_mon_upnl_evluatn_reval,
                                            cmc_upnl_in_prmry_ccy,
                                            cmc_upnl_evluatn_price,
                                            cmc_upnl_evluatn_reval,
                                            prime_margin,
                                            accnt_mon_opn_trd_amt_inst_ccy,
                                            accnt_mon_opn_trd_amt_prm_ccy,
                                            accnt_mon_opn_trd_amnt_usd,
                                            accnt_mon_opn_trd_amnt_gbp,
                                            accnt_mon_opn_trd_amnt_eur,
                                            unrlzd_cptl_gns_in_prmry_ccy,
                                            gslo_absolute_limit_price,
                                            open_trade_quantity,
                                            direction,
                                            intl_cust_opn_trd_amnt,
                                            intl_cust_opn_trd_amnt_ccy,
                                            intl_cust_opn_trd_amnt_fxr,
                                            intl_cust_opn_trd_amnt_prm_ccy,
                                            opening_trade_id,
                                            opening_trade_price,
                                            opn_accrd_trnvr_in_accnt_ccy,
                                            customer_upnl_in_inst_ccy,
                                            customer_opn_trd_amnt_inst_ccy,
                                            customer_opn_trd_amnt_prm_ccy,
                                            margin_evaluation_fx_reval,
                                            accrued_fees)
                                     SELECT p_snapshot_time,
                                            p_platform,
                                            p_position_value(lv_cnt).trading_account_id,
                                            p_position_value(lv_cnt).trading_account_type,
                                            p_position_value(lv_cnt).product_instrument_code,
                                            p_position_value(lv_cnt).product_wrapper_code,
                                            new_version.order_id,
                                            lv_logical_load_timestamp,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_effective_start_timestamp,
                                            lv_business_date,
                                            lv_reporting_date,
                                            new_version.customer_upnl_in_prmry_ccy,
                                            new_version.customer_upnl_evluatn_price,
                                            new_version.customer_upnl_evluatn_reval,
                                            new_version.decision_maker,
                                            new_version.customer_id,
                                            new_version.accnt_mon_upnl_in_prmry_ccy,
                                            new_version.accnt_mon_upnl_evluatn_price,
                                            new_version.accnt_mon_upnl_evluatn_reval,
                                            new_version.cmc_upnl_in_prmry_ccy,
                                            new_version.cmc_upnl_evluatn_price,
                                            new_version.cmc_upnl_evluatn_reval,
                                            new_version.prime_margin,
                                            new_version.accnt_mon_opn_trd_amt_inst_ccy,
                                            new_version.accnt_mon_opn_trd_amt_prm_ccy,
                                            new_version.accnt_mon_opn_trd_amnt_usd,
                                            new_version.accnt_mon_opn_trd_amnt_gbp,
                                            new_version.accnt_mon_opn_trd_amnt_eur,
                                            new_version.unrlzd_cptl_gns_in_prmry_ccy,
                                            new_version.gslo_absolute_limit_price,
                                            new_version.open_trade_quantity,
                                            new_version.direction,
                                            new_version.intl_cust_opn_trd_amnt,
                                            new_version.intl_cust_opn_trd_amnt_ccy,
                                            new_version.intl_cust_opn_trd_amnt_fxr,
                                            new_version.intl_cust_opn_trd_amnt_prm_ccy,
                                            new_version.opening_trade_id,
                                            new_version.opening_trade_price,
                                            new_version.opn_accrd_trnvr_in_accnt_ccy,
                                            new_version.customer_upnl_in_inst_ccy,
                                            new_version.customer_opn_trd_amnt_inst_ccy,
                                            new_version.customer_opn_trd_amnt_prm_ccy,
                                            new_version.margin_evaluation_fx_reval,
                                            new_version.accrued_fees
                                       FROM TABLE(CAST(p_position_value(lv_cnt).valued_open_trades AS valued_open_trade_tab)) new_version;

    END LOOP;

     EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;


  -- ===================================================================================
  -- put_account_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put position value
  --
  -- Notes:
  -- ------
  --
  --
  --     Tables potentially populated:
  --
  --     fx_currency_positions
  --     fx_currency_positions_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_account_value                  Account Values
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_fx_position_value(p_user                            IN fx_currency_positions.created_by%TYPE,
                                  p_effective_start_timestamp       IN fx_currency_positions.effective_start_timestamp%TYPE,
                                  p_platform                        IN fx_currency_positions.platform%TYPE,
                                  p_snapshot_time                   IN fx_currency_positions.snapshot_time%TYPE,
                                  p_fx_position_value               IN position_value_fx_tab) IS

    lv_business_date            DATE;
    lv_reporting_date           DATE;
    lv_logical_load_timestamp   TIMESTAMP(6);
    lv_old_fx_position_values   fx_currency_positions_tab;
    lv_old_valued_trades        old_valued_fx_open_trades_tab;
    lv_exists                   NUMBER;

  BEGIN

    lv_business_date := trunc(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

    lv_logical_load_timestamp := SYSTIMESTAMP;

    IF p_fx_position_value IS NOT NULL AND p_fx_position_value.COUNT > 0 THEN
      FOR lv_cnt IN 1..p_fx_position_value.COUNT LOOP

        nrg_trading_account.create_trading_account_stub(p_user                      => p_user,
                                                        p_logical_load_timestamp    => lv_logical_load_timestamp,
                                                        p_effective_start_timestamp => p_effective_start_timestamp,
                                                        p_trading_account_id        => p_fx_position_value(lv_cnt).trading_account_id,
                                                        p_trading_account_type      => 'CUSTOMER');

      END LOOP;
    END IF;

   /* FOR lv_cnt IN 1..p_fx_position_value.COUNT LOOP
      IF p_fx_position_value(lv_cnt).valued_fx_open_trades IS NOT NULL AND p_fx_position_value(lv_cnt).valued_fx_open_trades.COUNT > 1 THEN

        FOR lv_cnt_valued_trade IN 1.. p_fx_position_value(lv_cnt).valued_fx_open_trades.COUNT LOOP
          IF p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_valued_trade).order_id IS NOT NULL THEN

            nrg_order.create_order_stub(p_order_id                    => p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_valued_trade).order_id,
                                        p_platform                    => p_platform,
                                        p_logical_load_timestamp      => lv_logical_load_timestamp,
                                        p_effective_start_timestamp   => p_effective_start_timestamp,
                                        p_user                        => p_user);

          END IF;
        END LOOP;
      END IF;
    END LOOP; */

    BEGIN
      SELECT 1
      INTO lv_exists
      FROM fx_currency_positions
      WHERE platform = p_platform AND
            snapshot_time = p_snapshot_time AND
            rownum = 1;
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        lv_exists := 0;
    END;

    IF lv_exists = 1 THEN

      SELECT *
      BULK COLLECT INTO lv_old_fx_position_values
      FROM fx_currency_positions
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      SELECT *
      BULK COLLECT INTO lv_old_valued_trades
      FROM valued_fx_open_trades
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      DELETE FROM valued_fx_open_trades_h
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      put_history(lv_old_valued_trades,
                  p_effective_start_timestamp,
                  'U');

      put_history(p_old_fx_position_value       => lv_old_fx_position_values,
                  p_effective_end_timestamp  => p_effective_start_timestamp,
                  p_action                   => 'U',
                  p_snapshot_time            => p_snapshot_time,
                  p_platform                 => p_platform);


      DELETE valued_fx_open_trades
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;

      DELETE fx_currency_positions
      WHERE snapshot_time = p_snapshot_time AND
            platform = p_platform;
    END IF;

  FOR lv_cnt IN 1..p_fx_position_value.COUNT LOOP

    FORALL lv_cnt_fx_currency_pos IN 1..p_fx_position_value(lv_cnt).fx_currency_pos.COUNT
      INSERT INTO fx_currency_positions(snapshot_time,
                                        trading_account_id,
                                        currency,
                                        platform,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        business_date,
                                        reporting_date,
                                        fx_currency_position_time,
                                        trading_account_function,
                                        currency_amount,
                                        main_margin_currency,
                                        margin_direction,
                                        margin_fx_rate,
                                        margin,
                                        margin_in_main_margin_ccy,
                                        is_valid
                                        )
                                  VALUES(p_snapshot_time,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).trading_account_id,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).currency,
                                         p_platform,
                                         lv_logical_load_timestamp,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_user,
                                         SYSTIMESTAMP,
                                         p_effective_start_timestamp,
                                         lv_business_date,
                                         lv_reporting_date,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).fx_currency_position_time,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).trading_account_function,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).currency_amount,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).main_margin_currency,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).margin_direction,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).margin_fx_rate,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).margin,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).margin_in_main_margin_ccy,
                                         p_fx_position_value(lv_cnt).fx_currency_pos(lv_cnt_fx_currency_pos).is_valid
                                         );

    FORALL lv_cnt_values_trades IN 1..p_fx_position_value(lv_cnt).valued_fx_open_trades.COUNT
      INSERT INTO valued_fx_open_trades(snapshot_time,
                                        trading_account_id,
                                        order_id,
                                        platform,
                                        logical_load_timestamp,
                                        created_by,
                                        create_timestamp,
                                        updated_by,
                                        update_timestamp,
                                        effective_start_timestamp,
                                        business_date,
                                        reporting_date,
                                        decision_maker,
                                        customer_id,
                                        instrument_code,
                                        stream_code,
                                        value_date,
                                        primary_amount,
                                        direction,
                                        opening_trade_id,
                                        opening_trade_price,
                                        opening_trade_spot_price,
                                        customer_upnl_in_prmry_ccy,
                                        customer_evaluation_price,
                                        customer_evaluation_spot_price,
                                        customer_l1_bid_spot_price,
                                        customer_l1_mid_spot_price,
                                        customer_l1_ask_spot_price,
                                        cstmr_evltn_fx_reval_rate,
                                        cstmr_evltn_bid_fx_reval_rate,
                                        cstmr_evltn_ask_fx_reval_rate,
                                        cstmr_evltn_mid_fx_reval_rate,
                                        customer_secondary_amount,
                                        intl_cstmr_secondary_amount,
                                        customer_upnl_in_inst_ccy,
                                        accnt_mon_evltn_price,
                                        accnt_mon_evltn_spot_price,
                                        accnt_mon_evltn_fx_reval_rate,
                                        accnt_mon_upnl_in_prmry_ccy,
                                        accnt_mon_secondary_amount,
                                        cmc_upnl_in_prmry_ccy,
                                        cmc_evaluation_price,
                                        cmc_evaluation_fx_reval_rate,
                                        cmc_evaluation_spot_price,
                                        evaluation_tenor,
                                        is_valid
                                        )
                                VALUES (p_snapshot_time,
                                        p_fx_position_value(lv_cnt).trading_account_id,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).order_id,
                                        p_platform,
                                        lv_logical_load_timestamp,
                                        p_user,
                                        SYSTIMESTAMP,
                                        p_user,
                                        SYSTIMESTAMP,
                                        p_effective_start_timestamp,
                                        lv_business_date,
                                        lv_reporting_date,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).decision_maker,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_id,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).instrument_code,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).stream_code,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).value_date,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).primary_amount,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).direction,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).opening_trade_id,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).opening_trade_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).opening_trade_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_upnl_in_prmry_ccy,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_evaluation_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_evaluation_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_l1_bid_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_l1_mid_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_l1_ask_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cstmr_evltn_fx_reval_rate,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cstmr_evltn_bid_fx_reval_rate,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cstmr_evltn_ask_fx_reval_rate,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cstmr_evltn_mid_fx_reval_rate,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_secondary_amount,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).intl_cstmr_secondary_amount,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).customer_upnl_in_inst_ccy,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).accnt_mon_evltn_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).accnt_mon_evltn_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).accnt_mon_evltn_fx_reval_rate,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).accnt_mon_upnl_in_prmry_ccy,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).accnt_mon_secondary_amount,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cmc_upnl_in_prmry_ccy,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cmc_evaluation_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cmc_evaluation_fx_reval_rate,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).cmc_evaluation_spot_price,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).evaluation_tenor,
                                        p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_values_trades).is_valid
                                        );

  END LOOP;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_latest_fx_position_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put latest position values
  --
  -- Notes:
  -- ------
  --
  --
  --     Tables potentially populated:
  --
  --     LATEST_VALUED_POSITIONS
  --     LATEST_VALUED_OPEN_TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_fx_position_value                  Account Values
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_latest_fx_position_value(p_user                            IN fx_currency_positions.created_by%TYPE,
                                         p_effective_start_timestamp       IN fx_currency_positions.effective_start_timestamp%TYPE,
                                         p_platform                        IN fx_currency_positions.platform%TYPE,
                                         p_snapshot_time                   IN fx_currency_positions.snapshot_time%TYPE,
                                         p_fx_position_value               IN position_value_fx_tab) IS

    lv_business_date            DATE;
    lv_reporting_date           DATE;
    lv_logical_load_timestamp   TIMESTAMP(6);

  BEGIN

    lv_business_date := trunc(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

    lv_logical_load_timestamp := SYSTIMESTAMP;

    IF p_fx_position_value IS NOT NULL AND p_fx_position_value.COUNT > 0 THEN
      FOR lv_cnt IN 1..p_fx_position_value.COUNT LOOP

        nrg_trading_account.create_trading_account_stub(p_user                      => p_user,
                                                        p_logical_load_timestamp    => lv_logical_load_timestamp,
                                                        p_effective_start_timestamp => p_effective_start_timestamp,
                                                        p_trading_account_id        => p_fx_position_value(lv_cnt).trading_account_id,
                                                        p_trading_account_type      => 'CUSTOMER');

      END LOOP;
    END IF;

    FOR lv_cnt IN 1..p_fx_position_value.COUNT LOOP
      IF p_fx_position_value(lv_cnt).valued_fx_open_trades IS NOT NULL AND p_fx_position_value(lv_cnt).valued_fx_open_trades.COUNT > 1 THEN
        FOR lv_cnt_valued_trade IN 1.. p_fx_position_value(lv_cnt).valued_fx_open_trades.COUNT LOOP
          IF p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_valued_trade).order_id IS NOT NULL THEN
            nrg_order.create_order_stub(p_order_id                    => p_fx_position_value(lv_cnt).valued_fx_open_trades(lv_cnt_valued_trade).order_id,
                                        p_platform                    => p_platform,
                                        p_logical_load_timestamp      => lv_logical_load_timestamp,
                                        p_effective_start_timestamp   => p_effective_start_timestamp,
                                        p_user                        => p_user);
          END IF;
        END LOOP;
      END IF;
    END LOOP;

    DELETE latest_valued_fx_open_trades;
    DELETE latest_fx_currency_positions;

  FOR lv_cnt IN 1..p_fx_position_value.COUNT LOOP

    INSERT INTO latest_fx_currency_positions (
                snapshot_time,
                trading_account_id,
                currency,
                platform,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                business_date,
                reporting_date,
                fx_currency_position_time,
                trading_account_function,
                currency_amount,
                main_margin_currency,
                margin_direction,
                margin_fx_rate,
                margin,
                margin_in_main_margin_ccy,
                is_valid)
         SELECT p_snapshot_time                          AS snapshot_time,
                new_version.trading_account_id           AS trading_account_id,
                new_version.currency                     AS currency,
                p_platform                               AS platform,
                lv_logical_load_timestamp                AS logical_load_timestamp,
                p_user                                   AS created_by,
                lv_logical_load_timestamp                AS create_timestamp,
                p_user                                   AS updated_by,
                lv_logical_load_timestamp                AS update_timestamp,
                p_effective_start_timestamp              AS effective_start_timestamp,
                lv_business_date                         AS business_date,
                lv_reporting_date                        AS reporting_date,
                new_version.fx_currency_position_time    AS fx_currency_position_time,
                new_version.trading_account_function     AS trading_account_function,
                new_version.currency_amount              AS currency_amount,
                new_version.main_margin_currency         AS main_margin_currency,
                new_version.margin_direction             AS margin_direction,
                new_version.margin_fx_rate               AS margin_fx_rate,
                new_version.margin                       AS margin,
                new_version.margin_in_main_margin_ccy    AS margin_in_main_margin_ccy,
                new_version.is_valid                     AS is_valid
           FROM TABLE(CAST(p_fx_position_value(lv_cnt).fx_currency_pos AS fx_currency_pos_tab)) new_version;

    INSERT INTO latest_valued_fx_open_trades(
                snapshot_time,
                trading_account_id,
                order_id,
                platform,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                business_date,
                reporting_date,
                decision_maker,
                customer_id,
                instrument_code,
                stream_code,
                value_date,
                primary_amount,
                direction,
                opening_trade_id,
                opening_trade_price,
                opening_trade_spot_price,
                customer_upnl_in_prmry_ccy,
                customer_evaluation_price,
                customer_evaluation_spot_price,
                customer_l1_bid_spot_price,
                customer_l1_mid_spot_price,
                customer_l1_ask_spot_price,
                cstmr_evltn_fx_reval_rate,
                cstmr_evltn_bid_fx_reval_rate,
                cstmr_evltn_ask_fx_reval_rate,
                cstmr_evltn_mid_fx_reval_rate,
                customer_secondary_amount,
                intl_cstmr_secondary_amount,
                customer_upnl_in_inst_ccy,
                accnt_mon_evltn_price,
                accnt_mon_evltn_spot_price,
                accnt_mon_evltn_fx_reval_rate,
                accnt_mon_upnl_in_prmry_ccy,
                accnt_mon_secondary_amount,
                cmc_upnl_in_prmry_ccy,
                cmc_evaluation_price,
                cmc_evaluation_fx_reval_rate,
                cmc_evaluation_spot_price,
                evaluation_tenor,
                is_valid)
         SELECT p_snapshot_time,
                p_fx_position_value(lv_cnt).trading_account_id,
                new_version.order_id,
                p_platform,
                lv_logical_load_timestamp,
                p_user,
                SYSTIMESTAMP,
                p_user,
                SYSTIMESTAMP,
                p_effective_start_timestamp,
                lv_business_date,
                lv_reporting_date,
                new_version.decision_maker,
                new_version.customer_id,
                new_version.instrument_code,
                new_version.stream_code,
                new_version.value_date,
                new_version.primary_amount,
                new_version.direction,
                new_version.opening_trade_id,
                new_version.opening_trade_price,
                new_version.opening_trade_spot_price,
                new_version.customer_upnl_in_prmry_ccy,
                new_version.customer_evaluation_price,
                new_version.customer_evaluation_spot_price,
                new_version.customer_l1_bid_spot_price,
                new_version.customer_l1_mid_spot_price,
                new_version.customer_l1_ask_spot_price,
                new_version.cstmr_evltn_fx_reval_rate,
                new_version.cstmr_evltn_bid_fx_reval_rate,
                new_version.cstmr_evltn_ask_fx_reval_rate,
                new_version.cstmr_evltn_mid_fx_reval_rate,
                new_version.customer_secondary_amount,
                new_version.intl_cstmr_secondary_amount,
                new_version.customer_upnl_in_inst_ccy,
                new_version.accnt_mon_evltn_price,
                new_version.accnt_mon_evltn_spot_price,
                new_version.accnt_mon_evltn_fx_reval_rate,
                new_version.accnt_mon_upnl_in_prmry_ccy,
                new_version.accnt_mon_secondary_amount,
                new_version.cmc_upnl_in_prmry_ccy,
                new_version.cmc_evaluation_price,
                new_version.cmc_evaluation_fx_reval_rate,
                new_version.cmc_evaluation_spot_price,
                new_version.evaluation_tenor,
                new_version.is_valid
           FROM TABLE(CAST(p_fx_position_value(lv_cnt).valued_fx_open_trades AS valued_fx_open_trade_tab)) new_version;

    END LOOP;

     EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

END nrg_position_value;
/