CREATE OR REPLACE PACKAGE nrg_account_value
AS
  -- ===================================================================================
  -- NRG_ACCOUNT_VALUE
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the account value model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --    ----------   ---------------   -----   ----------------------------------------
  --     03/04/2013   Sanket Mittal     1.0     Creation
  --     24/06/2016   Sanket Mittal     1.3     BER-2679-SWS 34 - Data Contract Changes - AccountValue
  --     17/01/2017   Sanket Mittal     1.4     BER-3154 BI_ODS.ACCOUNT_VALUE_SNAPSHOTS - New Attribute
  --     19/03/2019   Patrick Dinwiddy  1.5     BER-5035 new contract and target tables
  --     21/02/2022   Patrick Dinwiddy  1.9     JCS-14997
	--
  -- ===================================================================================

  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;


  -- ===================================================================================
  -- put_account_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put account values
	--
  -- Notes:
  -- ------
	--
  --
  --     Tables potentially populated:
  --
  --     ACCOUNT_VALUES
	--     ACCOUNT_VALUES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_account_value                  Account Values
  --
	--
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_account_value( p_user                            IN account_value_snapshots.created_by%TYPE,
                                 p_effective_start_timestamp       IN account_value_snapshots.effective_start_timestamp%TYPE,
                                 p_platform                        IN account_value_snapshots.platform%TYPE,
                                 p_snapshot_time                   IN account_value_snapshots.snapshot_time%TYPE,
                                 p_account_value                   IN account_value_tab);

    PROCEDURE put_historic_account_value( p_user                            IN historic_account_value.created_by%TYPE,
                                          p_effective_start_timestamp       IN historic_account_value.effective_start_timestamp%TYPE,
                                          p_platform                        IN historic_account_value.platform%TYPE,
                                          p_snapshot_time                   IN historic_account_value.snapshot_time%TYPE,
                                          p_historic_account_value          IN historic_account_value_tab);

    PROCEDURE put_hstrc_secondary_account_monit_free_equity(p_user          IN hstrc_acnt_vl_scndry_free_eq.created_by%TYPE,
                                            p_effective_start_timestamp     IN hstrc_acnt_vl_scndry_free_eq.effective_start_timestamp%TYPE,
                                            p_snapshot_time                 IN hstrc_acnt_vl_scndry_free_eq.snapshot_time%TYPE,
                                            p_platform                      IN hstrc_acnt_vl_scndry_free_eq.platform%TYPE,
                                            p_secondary_free_eqs            IN hstrc_acnt_vl_scndry_free_eq_tab,
                                            p_logical_load_timestamp        IN hstrc_acnt_vl_scndry_free_eq.logical_load_timestamp%TYPE);

    PROCEDURE put_hstrc_secondary_currency_withdrawable_amounts(p_user       IN hstrc_secondary_currency_withdrawable_amounts.created_by%TYPE,
                                                 p_effective_start_timestamp IN hstrc_secondary_currency_withdrawable_amounts.effective_start_timestamp%TYPE,
                                                 p_snapshot_time             IN hstrc_secondary_currency_withdrawable_amounts.snapshot_time%TYPE,
                                                 p_platform                  IN hstrc_secondary_currency_withdrawable_amounts.platform%TYPE,
                                                 p_withdrawable_amounts      IN hstrc_secondary_currency_withdrawable_amounts_tab,
                                                 p_logical_load_timestamp    IN hstrc_secondary_currency_withdrawable_amounts.logical_load_timestamp%TYPE);

    PROCEDURE put_settled_positions(p_user                            IN settled_positions.created_by%TYPE,
                                    p_effective_start_timestamp       IN settled_positions.effective_start_timestamp%TYPE,
                                    p_platform                        IN settled_positions.platform%TYPE,
                                    p_snapshot_time                   IN settled_positions.snapshot_time%TYPE,
                                    p_settled_positions               IN settled_positions_tab);
                                    
END nrg_account_value;
/