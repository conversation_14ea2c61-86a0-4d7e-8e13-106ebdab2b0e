------------------------------
-- JCS-15597 ALTER TRADES
------------------------------
ALTER TABLE transfer_request_positions   RENAME COLUMN is_tradeable TO is_in_pms;
ALTER TABLE transfer_request_positions_h RENAME COLUMN is_tradeable TO is_in_pms;

------------------------------
-- JCS-15486 AND JCS-15560 ALTER TRADES
------------------------------
AL<PERSON>R TABLE trades   ADD amount_fx_rate_banding                 NUMBER;
ALTER TABLE trades   ADD hdg_settlement_time 					TIMESTAMP(6);
ALTER TABLE trades   ADD is_skip_mifid_reporting	            VARCHAR2(3);
ALTER TABLE trades   ADD is_skip_contract_notes	              	VARCHAR2(3);
ALTER TABLE trades_h ADD amount_fx_rate_banding  				NUMBER;
<PERSON><PERSON><PERSON> TABLE trades_h ADD hdg_settlement_time 					TIMESTAMP(6);
<PERSON>TER TABLE trades_h ADD is_skip_mifid_reporting	            VARCHAR2(3);
ALTER TABLE trades_h ADD is_skip_contract_notes	              	VARCHAR2(3);
------------------------------
-- JCS-15560 ALTER ORDERS
------------------------------
ALTER TABLE orders   ADD external_order_comment	              VARCHAR2(200);
ALTER TABLE orders   ADD commission_to_account_fx_reval_rate  NUMBER;
ALTER TABLE orders_h ADD external_order_comment	              VARCHAR2(200);
ALTER TABLE orders_h ADD commission_to_account_fx_reval_rate  NUMBER;
ALTER TABLE orders   ADD is_skip_mifid_reporting	          VARCHAR2(3);
ALTER TABLE orders   ADD is_skip_contract_notes	              VARCHAR2(3);
ALTER TABLE orders_h ADD is_skip_mifid_reporting	          VARCHAR2(3);
ALTER TABLE orders_h ADD is_skip_contract_notes	              VARCHAR2(3);

------------------------------
-- JCS-15559 ALTER SETTLEMENTS
------------------------------
ALTER TABLE trade_settlements ADD version_number                          number;
ALTER TABLE trade_settlements ADD is_deleted                              varchar2(3);
ALTER TABLE trade_settlements ADD creation_time                           timestamp(6);
ALTER TABLE trade_settlements ADD update_time                             timestamp(6);
ALTER TABLE trade_settlements ADD creation_identity_token                 number;
ALTER TABLE trade_settlements ADD crtn_on_bhlf_of_idntty_tkn              number;
ALTER TABLE trade_settlements ADD update_identity_token                   number;
ALTER TABLE trade_settlements ADD updt_on_bhlf_of_idntty_tkn              number;

ALTER TABLE trade_settlements_h ADD version_number                          number;
ALTER TABLE trade_settlements_h ADD is_deleted                              varchar2(3);
ALTER TABLE trade_settlements_h ADD creation_time                           timestamp(6);
ALTER TABLE trade_settlements_h ADD update_time                             timestamp(6);
ALTER TABLE trade_settlements_h ADD creation_identity_token                 number;
ALTER TABLE trade_settlements_h ADD crtn_on_bhlf_of_idntty_tkn              number;
ALTER TABLE trade_settlements_h ADD update_identity_token                   number;
ALTER TABLE trade_settlements_h ADD updt_on_bhlf_of_idntty_tkn              number;

------------------------------
-- JCS-15558 ALTER CT_DIVIDENDS
------------------------------
ALTER TABLE ct_dividends   ADD trading_Account_id NUMBER;
ALTER TABLE ct_dividends_h ADD trading_Account_id NUMBER;
ALTER TABLE ct_dividends   ADD corporate_action_official_type	varchar2(200);
ALTER TABLE ct_dividends_h ADD corporate_action_official_type	varchar2(200);

------------------------------
-- JCS-15495 ALTER CUST LOYALTY
------------------------------
ALTER TABLE customer_loyalty_schemes   DROP COLUMN is_fxat;
ALTER TABLE customer_loyalty_schemes_h DROP COLUMN is_fxat;

------------------------------
-- JCS-15521 ALTER TRADING ACCOUNTS
------------------------------
ALTER TABLE trading_accounts   ADD is_inactive_funds	VARCHAR2(3);
ALTER TABLE trading_accounts_h ADD is_inactive_funds	VARCHAR2(3);
ALTER TABLE ta_give_up_profiles   ADD client_prime_broker_broker_code VARCHAR2(200);
ALTER TABLE ta_give_up_profiles_h ADD client_prime_broker_broker_code VARCHAR2(200);


------------------------------
-- JCS-15511 ALTER POSITIONS TABLES
------------------------------
ALTER TABLE anytime_positions ADD transaction_type           VARCHAR2(100);
ALTER TABLE anytime_positions_h ADD transaction_type           VARCHAR2(100);

ALTER TABLE eod_open_trades ADD transaction_type           VARCHAR2(100);
ALTER TABLE eod_open_trades ADD hdg_evaluation_spot_price  NUMBER;
ALTER TABLE eod_open_trades ADD hdg_settlement_date        DATE;
ALTER TABLE eod_open_trades ADD hdg_settlement_time        TIMESTAMP(6);
ALTER TABLE eod_open_trades ADD hdg_settlement_status      VARCHAR2(100);
ALTER TABLE eod_open_trades_h ADD transaction_type           VARCHAR2(100);
ALTER TABLE eod_open_trades_h ADD hdg_evaluation_spot_price  NUMBER;
ALTER TABLE eod_open_trades_h ADD hdg_settlement_date        DATE;
ALTER TABLE eod_open_trades_h ADD hdg_settlement_time        TIMESTAMP(6);
ALTER TABLE eod_open_trades_h ADD hdg_settlement_status      VARCHAR2(100);

------------------------------
-- JCS-15509 CREATE SETTLED POSITIONS
------------------------------
CREATE TABLE settled_positions
(
  snapshot_time               TIMESTAMP(6) not null,
  platform                    VARCHAR2(10) not null,
  trading_account_id          NUMBER not null,
  trading_account_type        VARCHAR2(20) not null,
  product_instrument_code     VARCHAR2(50) not null,
  logical_load_timestamp      TIMESTAMP(6),
  created_by                  VARCHAR2(50),
  create_timestamp            TIMESTAMP(6),
  updated_by                  VARCHAR2(50),
  update_timestamp            TIMESTAMP(6),
  effective_start_timestamp   TIMESTAMP(6),
  business_date               DATE,
  reporting_date              DATE,
  is_eod                      VARCHAR2(3),
  trading_account_function    VARCHAR2(50),
  position_quantity           NUMBER,
  unsettled_position_quantity NUMBER,
  settled_position_quantity   NUMBER
)
partition by range (reporting_date)
(partition P0 values less than (TO_DATE(' 01/03/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_STTLD_PSTNS_D22,
 partition P1 values less than (TO_DATE(' 01/04/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_STTLD_PSTNS_D22,
 partition P2 values less than (TO_DATE(' 01/05/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_STTLD_PSTNS_D22,
 partition P3 values less than (TO_DATE(' 01/06/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_STTLD_PSTNS_D22
); 

alter table SETTLED_POSITIONS set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));  

alter table SETTLED_POSITIONS
  add constraint SETTLED_POSITIONS_PK primary key (SNAPSHOT_TIME, PLATFORM, TRADING_ACCOUNT_ID, TRADING_ACCOUNT_TYPE, PRODUCT_INSTRUMENT_CODE)
  using index 
  tablespace ODS_STTLD_PSTNS_IND
  ;
  
create index bi_ods.SETTLED_POSITIONS_x1 on bi_ods.SETTLED_POSITIONS(trading_account_id)
  tablespace ODS_STTLD_PSTNS_IND
  ;   
  
create table SETTLED_POSITIONS_h
(
  snapshot_time               TIMESTAMP(6) not null,
  platform                    VARCHAR2(10) not null,
  trading_account_id          NUMBER not null,
  trading_account_type        VARCHAR2(20) not null,
  product_instrument_code     VARCHAR2(50) not null,
  logical_load_timestamp      TIMESTAMP(6),
  created_by                  VARCHAR2(50),
  create_timestamp            TIMESTAMP(6),
  updated_by                  VARCHAR2(50),
  update_timestamp            TIMESTAMP(6),
  effective_start_timestamp   TIMESTAMP(6),
  effective_end_timestamp     TIMESTAMP(6) not null,
  action                      VARCHAR2(1) not null,
  action_timestamp            TIMESTAMP(6) not null,
  business_date               DATE,
  reporting_date              DATE,
  is_eod                      VARCHAR2(3),
  trading_account_function    VARCHAR2(50),
  position_quantity           NUMBER,
  unsettled_position_quantity NUMBER,
  settled_position_quantity   NUMBER
)
tablespace ODS_STTLD_PSTNS_DAT
compress for oltp;

alter table SETTLED_POSITIONS_h
  add constraint SETTLED_POSITIONS_h_PK primary key (SNAPSHOT_TIME, PLATFORM, TRADING_ACCOUNT_ID, TRADING_ACCOUNT_TYPE, PRODUCT_INSTRUMENT_CODE, effective_start_timestamp)
  using index 
  tablespace ODS_STTLD_PSTNS_IND
  ;  

------------------------------
-- JCS-15495 ALTER INSTRUMENTS
------------------------------
ALTER TABLE instruments   ADD cusip VARCHAR2(20);
ALTER TABLE instruments_h ADD cusip VARCHAR2(20);

------------------------------
-- JCS-15492 ALTER HIST ACC VALUE
------------------------------
ALTER TABLE historic_account_value   ADD total_unsettled_cash_primary_currency NUMBER;
ALTER TABLE historic_account_value_h ADD total_unsettled_cash_primary_currency NUMBER;

CREATE TABLE hstrc_secondary_currency_withdrawable_amounts (
snapshot_time	                             TIMESTAMP(6),
platform	                                 VARCHAR2(10),
trading_account_id	                       NUMBER,
trading_account_type	                     VARCHAR2(50),
currency	                                 VARCHAR2(10),
logical_load_timestamp                     TIMESTAMP(6),
created_by                                 VARCHAR2(50),
create_timestamp                           TIMESTAMP(6),
updated_by                                 VARCHAR2(50),
update_timestamp                           TIMESTAMP(6),
effective_start_timestamp                  TIMESTAMP(6),
withdrawable_amount                        NUMBER
)  
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.hstrc_secondary_currency_withdrawable_amounts
  add constraint hstrc_secondary_currency_withdrawable_amounts_pk primary key (snapshot_time, platform, trading_account_id, trading_account_type, currency)
  using index 
  tablespace ODS_IND
;

CREATE TABLE hstrc_secondary_currency_withdrawable_amounts_h (
snapshot_time	                             TIMESTAMP(6),
platform	                                 VARCHAR2(10),
trading_account_id	                       NUMBER,
trading_account_type	                     VARCHAR2(50),
currency	                                 VARCHAR2(10),
logical_load_timestamp                     TIMESTAMP(6),
created_by                                 VARCHAR2(50),
create_timestamp                           TIMESTAMP(6),
updated_by                                 VARCHAR2(50),
update_timestamp                           TIMESTAMP(6),
effective_start_timestamp                  TIMESTAMP(6),
effective_end_TIMESTAMP                    TIMESTAMP(6),
action                                     VARCHAR2(1),
action_TIMESTAMP                           TIMESTAMP(6),
withdrawable_amount                        NUMBER
)  
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.hstrc_secondary_currency_withdrawable_amounts_h
  add constraint hstrc_secondary_currency_withdrawable_amounts_h_pk primary key (snapshot_time, platform, trading_account_id, trading_account_type, currency, effective_start_timestamp)
  using index 
  tablespace ODS_IND
;

------------------------------
-- JCS-15483 CASH TRANSACTION OBJECTS
------------------------------

CREATE TABLE ct_client_transfer (
cash_transaction_seq	         NUMBER,
booking_NUMBER	               NUMBER,
logical_load_timestamp         TIMESTAMP(6),
created_by                     VARCHAR2(50),
create_timestamp               TIMESTAMP(6),
updated_by                     VARCHAR2(50),
update_timestamp               TIMESTAMP(6),
effective_start_timestamp      TIMESTAMP(6),
business_date                  DATE,
reporting_date                 DATE,
requested_amount	             NUMBER,
source_currency	               VARCHAR2(3),
target_currency	               VARCHAR2(3),
amount_in_target_currency	     NUMBER,
fx_rate	                       NUMBER,
fx_banding	                   NUMBER,
spread_amount	                 NUMBER
)
partition by range (reporting_date)
(partition P3 values less than (TO_DATE(' 01/03/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P4 values less than (TO_DATE(' 01/04/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P5 values less than (TO_DATE(' 01/05/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
); 

alter table ct_client_transfer set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));  

alter table bi_ods.ct_client_transfer 
  add constraint ct_client_transfer_pk primary key (cash_transaction_seq)
  using index 
  tablespace ODS_IND
;

alter table ct_client_transfer
  add constraint ct_client_transfer_fk1 foreign key (CASH_TRANSACTION_SEQ)
  references CASH_TRANSACTIONS (CASH_TRANSACTION_SEQ);

CREATE TABLE ct_client_transfer_h (
cash_transaction_seq	         NUMBER,
booking_NUMBER	               NUMBER,
logical_load_timestamp         TIMESTAMP(6),
created_by                     VARCHAR2(50),
create_timestamp               TIMESTAMP(6),
updated_by                     VARCHAR2(50),
update_timestamp               TIMESTAMP(6),
effective_start_timestamp      TIMESTAMP(6),
effective_end_TIMESTAMP        TIMESTAMP(6),
action                         VARCHAR2(1),
action_TIMESTAMP               TIMESTAMP(6),
business_date                  DATE,
reporting_date                 DATE,
requested_amount	             NUMBER,
source_currency	               VARCHAR2(3),
target_currency	               VARCHAR2(3),
amount_in_target_currency	     NUMBER,
fx_rate	                       NUMBER,
fx_banding	                   NUMBER,
spread_amount	                 NUMBER
)
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.ct_client_transfer_h 
  add constraint ct_client_transfer_h_pk primary key (cash_transaction_seq,effective_start_timestamp)
  using index 
  tablespace ODS_IND
;

CREATE TABLE ct_corporate_action_ticket (
cash_transaction_seq	                         NUMBER,
booking_NUMBER                                 NUMBER,
logical_load_timestamp                         TIMESTAMP(6),
created_by                                     VARCHAR2(50),
create_timestamp                               TIMESTAMP(6),
updated_by                                     VARCHAR2(50),
update_timestamp                               TIMESTAMP(6),
effective_start_timestamp                      TIMESTAMP(6),
business_date                                  DATE,
reporting_date                                 DATE,
order_id                                       varchar2(50),
quantity_before_corporate_action               number,
avg_opening_price_before_corporate_action      number,
avg_opening_price_after_corporate_action       number,
instrument_quote_id                            varchar2(300),
price_bid_after_corporate_action               number,
source_amount                                  number,
target_amount                                  number,
target_currency                                varchar2(3),
integer_quantity_after_corporate_action        number,
fractional_quantity_after_corporate_action     number,
conversion_fee                                 number,
conversion_fee_currency                        varchar2(3),
source_currency                                varchar2(3),
fx_rate                                        number,
fx_bid_band                                    number,
instrument_currency_fractional_part_ratio      number,
instrument_code                                varchar2(50),
instrument_currency                            varchar2(3)
)
partition by range (reporting_date)
(partition P3 values less than (TO_DATE(' 01/03/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P4 values less than (TO_DATE(' 01/04/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P5 values less than (TO_DATE(' 01/05/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
); 

alter table ct_corporate_action_ticket set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));  

alter table bi_ods.ct_corporate_action_ticket 
  add constraint ct_corporate_action_ticket_pk primary key (cash_transaction_seq)
  using index 
  tablespace ODS_IND
;

alter table ct_corporate_action_ticket
  add constraint ct_corporate_action_ticket_fk1 foreign key (CASH_TRANSACTION_SEQ)
  references CASH_TRANSACTIONS (CASH_TRANSACTION_SEQ);

CREATE TABLE ct_corporate_action_ticket_h (
cash_transaction_seq	                         NUMBER,
booking_NUMBER                                 NUMBER,
logical_load_timestamp                         TIMESTAMP(6),
created_by                                     VARCHAR2(50),
create_timestamp                               TIMESTAMP(6),
updated_by                                     VARCHAR2(50),
update_timestamp                               TIMESTAMP(6),
effective_start_timestamp                      TIMESTAMP(6),
effective_end_TIMESTAMP                        TIMESTAMP(6),
action                                         VARCHAR2(1),
action_TIMESTAMP                               TIMESTAMP(6),
business_date                                  DATE,
reporting_date                                 DATE,
order_id                                       varchar2(50),
quantity_before_corporate_action               number,
avg_opening_price_before_corporate_action      number,
avg_opening_price_after_corporate_action       number,
instrument_quote_id                            varchar2(300),
price_bid_after_corporate_action               number,
source_amount                                  number,
target_amount                                  number,
target_currency                                varchar2(3),
integer_quantity_after_corporate_action        number,
fractional_quantity_after_corporate_action     number,
conversion_fee                                 number,
conversion_fee_currency                        varchar2(3),
source_currency                                varchar2(3),
fx_rate                                        number,
fx_bid_band                                    number,
instrument_currency_fractional_part_ratio      number,
instrument_code                                varchar2(50),
instrument_currency                            varchar2(3)
)
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.ct_corporate_action_ticket_h 
  add constraint ct_corporate_action_ticket_h_pk primary key (cash_transaction_seq,effective_start_timestamp)
  using index 
  tablespace ODS_IND
;

CREATE TABLE ct_card_deposit (
cash_transaction_seq	         NUMBER,
booking_NUMBER	               NUMBER,
logical_load_timestamp         TIMESTAMP(6),
created_by                     VARCHAR2(50),
create_timestamp               TIMESTAMP(6),
updated_by                     VARCHAR2(50),
update_timestamp               TIMESTAMP(6),
effective_start_timestamp      TIMESTAMP(6),
business_date                  DATE,
reporting_date                 DATE,
payment_id	                   VARCHAR2(50),
card_deposit_amount	           NUMBER,
card_deposit_amount_currency	 VARCHAR2(3),
bank_account_code	             VARCHAR2(50)
)
partition by range (reporting_date)
(partition P3 values less than (TO_DATE(' 01/03/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P4 values less than (TO_DATE(' 01/04/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P5 values less than (TO_DATE(' 01/05/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
); 

alter table ct_card_deposit set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));  

alter table bi_ods.ct_card_deposit 
  add constraint ct_card_deposit_pk primary key (payment_id,cash_transaction_seq)
  using index 
  tablespace ODS_IND
;

alter table ct_card_deposit
  add constraint ct_card_deposit_fk1 foreign key (CASH_TRANSACTION_SEQ)
  references CASH_TRANSACTIONS (CASH_TRANSACTION_SEQ);
  
  
CREATE TABLE ct_card_deposit_h (
cash_transaction_seq	         NUMBER,
booking_NUMBER	               NUMBER,
logical_load_timestamp         TIMESTAMP(6),
created_by                     VARCHAR2(50),
create_timestamp               TIMESTAMP(6),
updated_by                     VARCHAR2(50),
update_timestamp               TIMESTAMP(6),
effective_start_timestamp      TIMESTAMP(6),
effective_end_TIMESTAMP        TIMESTAMP(6),
action                         VARCHAR2(1),
action_TIMESTAMP               TIMESTAMP(6),
business_date                  DATE,
reporting_date                 DATE,
payment_id	                   VARCHAR2(50),
card_deposit_amount	           NUMBER,
card_deposit_amount_currency	 VARCHAR2(3),
bank_account_code	             VARCHAR2(50)
)
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.ct_card_deposit_h 
  add constraint ct_card_deposit_h_pk primary key (payment_id,cash_transaction_seq,effective_start_timestamp)
  using index 
  tablespace ODS_IND
;

CREATE TABLE ct_liquidity_risk_management_conversion (
cash_transaction_seq	         NUMBER,
booking_NUMBER	               NUMBER,
logical_load_timestamp         TIMESTAMP(6),
created_by                     VARCHAR2(50),
create_timestamp               TIMESTAMP(6),
updated_by                     VARCHAR2(50),
update_timestamp               TIMESTAMP(6),
effective_start_timestamp      TIMESTAMP(6),
business_date                  DATE,
reporting_date                 DATE,
source_currency	               VARCHAR2(3),
target_currency	               VARCHAR2(3),
source_amount	                 NUMBER,
target_amount	                 NUMBER,
trade_date	                   DATE,
settlement_date	               DATE
)
partition by range (reporting_date)
(partition P3 values less than (TO_DATE(' 01/03/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P4 values less than (TO_DATE(' 01/04/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P5 values less than (TO_DATE(' 01/05/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
); 

alter table ct_liquidity_risk_management_conversion set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));  

alter table bi_ods.ct_liquidity_risk_management_conversion 
  add constraint ct_liquidity_risk_management_conversion_pk primary key (cash_transaction_seq)
  using index 
  tablespace ODS_IND
;

alter table ct_liquidity_risk_management_conversion
  add constraint ct_liquidity_risk_management_conversion_fk1 foreign key (CASH_TRANSACTION_SEQ)
  references CASH_TRANSACTIONS (CASH_TRANSACTION_SEQ);
  
  
CREATE TABLE ct_liquidity_risk_management_conversion_h (
cash_transaction_seq	         NUMBER,
booking_NUMBER	               NUMBER,
logical_load_timestamp         TIMESTAMP(6),
created_by                     VARCHAR2(50),
create_timestamp               TIMESTAMP(6),
updated_by                     VARCHAR2(50),
update_timestamp               TIMESTAMP(6),
effective_start_timestamp      TIMESTAMP(6),
effective_end_TIMESTAMP        TIMESTAMP(6),
action                         VARCHAR2(1),
action_TIMESTAMP               TIMESTAMP(6),
business_date                  DATE,
reporting_date                 DATE,
source_currency	               VARCHAR2(3),
target_currency	               VARCHAR2(3),
source_amount	                 NUMBER,
target_amount	                 NUMBER,
trade_date	                   DATE,
settlement_date	               DATE
)
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.ct_liquidity_risk_management_conversion_h 
  add constraint ct_liquidity_risk_management_conversion_h_pk primary key (cash_transaction_seq,effective_start_timestamp)
  using index 
  tablespace ODS_IND
;

CREATE TABLE ct_corporate_action_cash_dividend (
cash_transaction_seq	                     NUMBER,
booking_NUMBER                             NUMBER,
logical_load_timestamp                     TIMESTAMP(6),
created_by                                 VARCHAR2(50),
create_timestamp                           TIMESTAMP(6),
updated_by                                 VARCHAR2(50),
update_timestamp                           TIMESTAMP(6),
effective_start_timestamp                  TIMESTAMP(6),
business_date                              DATE,
reporting_date                             DATE,
cash_dividend_id                           NUMBER,
instrument_code                            VARCHAR2(50),
instrument_currency                        VARCHAR2(3),
instrument_currency_fractional_part_ratio  NUMBER,
product_wrapper_code                       VARCHAR2(50),
corporate_action_code                      VARCHAR2(50),
corporate_action_execution_type            VARCHAR2(100),
corporate_action_execution_date            DATE,
payment_date                               DATE,
dividend_gross_amount                      NUMBER,
dividend_amount_currency                   VARCHAR2(3),
dividend_amount_fractional_part_ratio      NUMBER,
trading_account_id                         NUMBER,
source_amount                              NUMBER,
source_currency                            VARCHAR2(3),
target_amount                              NUMBER,
target_currency                            VARCHAR2(3),
fx_rate                                    NUMBER,
fx_bid_band                                NUMBER,
conversion_fee                             NUMBER,
conversion_fee_currency                    varchar2(3),
quantity                                   number,
original_dividend_amount_currency          varchar2(3),
original_source_gross_amount               number,
corporate_action_official_type             varchar2(200),
dividend_tax_name                          varchar2(100),
tax_country                                varchar2(100),
us_withholding_tax_reduction               varchar2(100),
is_tax_rate_residency_dependent            varchar2(3),
dividend_tax_rate                          number,
source_gross_amount                        number,
tax_amount                                 number,
period                                     varchar2(50)
)
partition by range (reporting_date)
(partition P3 values less than (TO_DATE(' 01/03/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P4 values less than (TO_DATE(' 01/04/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT,
 partition P5 values less than (TO_DATE(' 01/05/2022 00:00:00', 'DD/MM/YYYY HH24:MI:SS', 'NLS_CALENDAR=GREGORIAN')) tablespace ODS_DAT
); 

alter table ct_corporate_action_cash_dividend set INTERVAL(NUMTOYMINTERVAL(1, 'MONTH'));  

alter table bi_ods.ct_corporate_action_cash_dividend 
  add constraint ct_corporate_action_cash_dividend_pk primary key (cash_transaction_seq,cash_dividend_id)
  using index 
  tablespace ODS_IND
;

alter table ct_corporate_action_cash_dividend
  add constraint ct_corporate_action_cash_dividend_fk1 foreign key (CASH_TRANSACTION_SEQ)
  references CASH_TRANSACTIONS (CASH_TRANSACTION_SEQ);
  
  
CREATE TABLE ct_corporate_action_cash_dividend_h (
cash_transaction_seq	                     NUMBER,
booking_NUMBER                             NUMBER,
logical_load_timestamp                     TIMESTAMP(6),
created_by                                 VARCHAR2(50),
create_timestamp                           TIMESTAMP(6),
updated_by                                 VARCHAR2(50),
update_timestamp                           TIMESTAMP(6),
effective_start_timestamp                  TIMESTAMP(6),
effective_end_TIMESTAMP                    TIMESTAMP(6),
action                                     VARCHAR2(1),
action_TIMESTAMP                           TIMESTAMP(6),
business_date                              DATE,
reporting_date                             DATE,
cash_dividend_id                           NUMBER,
instrument_code                            VARCHAR2(50),
instrument_currency                        VARCHAR2(3),
instrument_currency_fractional_part_ratio  NUMBER,
product_wrapper_code                       VARCHAR2(50),
corporate_action_code                      VARCHAR2(50),
corporate_action_execution_type            VARCHAR2(100),
corporate_action_execution_date            DATE,
payment_date                               DATE,
dividend_gross_amount                      NUMBER,
dividend_amount_currency                   VARCHAR2(3),
dividend_amount_fractional_part_ratio      NUMBER,
trading_account_id                         NUMBER,
source_amount                              NUMBER,
source_currency                            VARCHAR2(3),
target_amount                              NUMBER,
target_currency                            VARCHAR2(3),
fx_rate                                    NUMBER,
fx_bid_band                                NUMBER,
conversion_fee                             NUMBER,
conversion_fee_currency                    varchar2(3),
quantity                                   number,
original_dividend_amount_currency          varchar2(3),
original_source_gross_amount               number,
corporate_action_official_type             varchar2(200),
dividend_tax_name                          varchar2(100),
tax_country                                varchar2(100),
us_withholding_tax_reduction               varchar2(100),
is_tax_rate_residency_dependent            varchar2(3),
dividend_tax_rate                          number,
source_gross_amount                        number,
tax_amount                                 number,
period                                     varchar2(50)
)  
tablespace ODS_DAT
compress for oltp;

alter table bi_ods.ct_corporate_action_cash_dividend_h 
  add constraint ct_corporate_action_cash_dividend_h_pk primary key (cash_transaction_seq,cash_dividend_id,effective_start_timestamp)
  using index 
  tablespace ODS_IND
;
