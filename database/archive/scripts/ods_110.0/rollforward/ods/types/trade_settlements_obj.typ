DROP TYPE trade_settlements_tab FORCE;
DROP TYPE trade_settlements_obj FORCE;

CREATE OR REPLACE TYPE trade_settlements_obj IS OBJECT (
publish_time                            timestamp(6),
settlement_id                           number,
ref_code                                varchar2(100),
ref_codifier                            varchar2(100),
trading_account_id                      number,
account_function                        varchar2(50),
settlement_time                         timestamp(6),
trade_amount_in_account_ccy             number,
account_ccy                             varchar2(20),
state                                   varchar2(20),
actual_settlement_time                  timestamp(6),
client_cash_booking_direction           varchar2(20),
transaction_time                        timestamp(6),
trade_amount_settlement_ccy             number,
settlement_ccy                          varchar2(20),
total_fees_in_account_ccy               number,
total_fees_in_settlement_ccy            number,
transaction_settlement_period_start     timestamp(6),
settlement_date                         timestamp(6),
market_end_of_day                       timestamp(6),
version_number	                        number,
is_deleted	                            varchar2(3),
creation_time	                          timestamp(6),
update_time	                            timestamp(6),
creation_identity_token	                number,
crtn_on_bhlf_of_idntty_tkn	            number,
update_identity_token	                  number,
updt_on_bhlf_of_idntty_tkn	            number
);
/