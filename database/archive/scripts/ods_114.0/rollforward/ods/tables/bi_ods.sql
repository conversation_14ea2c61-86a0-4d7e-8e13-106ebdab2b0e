------------------------------
-- JCS-15919 POSITIONS
------------------------------
ALTER TABLE anytime_positions RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE anytime_positions ADD accrued_fees_in_primary_ccy      NUMBER;
ALTER TABLE anytime_positions ADD opn_trd_instr_prc_in_prmry_ccy 	 NUMBER;  

ALTER TABLE anytime_positions_h RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE anytime_positions_h ADD accrued_fees_in_primary_ccy      NUMBER;
ALTER TABLE anytime_positions_h ADD opn_trd_instr_prc_in_prmry_ccy 	 NUMBER;  

ALTER TABLE eod_open_trades RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE eod_open_trades ADD accrued_fees_in_primary_ccy      NUMBER;
ALTER TABLE eod_open_trades ADD opn_trd_instr_prc_in_prmry_ccy 	 NUMBER;  

ALTER TABLE eod_open_trades_h RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE eod_open_trades_h ADD accrued_fees_in_primary_ccy      NUMBER;
ALTER TABLE eod_open_trades_h ADD opn_trd_instr_prc_in_prmry_ccy 	 NUMBER;  

------------------------------
-- JCS-15924 FUNDING AMOUNTS
------------------------------
ALTER TABLE bi_ods.funding_amounts   ADD allowance_adjustment_type VARCHAR2(50);
ALTER TABLE bi_ods.funding_amounts_h ADD allowance_adjustment_type VARCHAR2(50);

------------------------------
-- JCS-15921 POSITION_TRANSACTIONS
------------------------------
ALTER TABLE position_transactions RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE position_transactions ADD accrued_fees_in_primary_ccy      NUMBER;
ALTER TABLE position_transactions ADD opn_trd_instr_prc_in_prmry_ccy    NUMBER;  

ALTER TABLE position_transactions_h RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE position_transactions_h ADD accrued_fees_in_primary_ccy      NUMBER;
ALTER TABLE position_transactions_h ADD opn_trd_instr_prc_in_prmry_ccy    NUMBER;  

------------------------------
-- JCS-15920 POSITION VALUE
------------------------------
ALTER TABLE position_valued_open_trades   RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE position_valued_open_trades_h RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;
ALTER TABLE latest_valued_open_trades     RENAME COLUMN accrued_fees TO accrued_fees_in_instrument_currency;

------------------------------
-- JCS-15922 TRADES
------------------------------
ALTER TABLE trades   ADD hdg_venue_MIC	varchar2(100);
ALTER TABLE trades   ADD hdg_is_give_up	varchar2(3);
ALTER TABLE trades_h ADD hdg_venue_MIC	varchar2(100);
ALTER TABLE trades_h ADD hdg_is_give_up	varchar2(3);

------------------------------
-- JCS-15916 CASH
------------------------------
ALTER TABLE ct_corporate_action_cash_dividend ADD stock_dividend_rate number;
ALTER TABLE ct_corporate_action_cash_dividend ADD stock_dividend_price number;
ALTER TABLE ct_corporate_action_cash_dividend_h ADD stock_dividend_rate number;
ALTER TABLE ct_corporate_action_cash_dividend_h ADD stock_dividend_price number;


ALTER TABLE ct_client_transfer ADD trading_account_id number;
ALTER TABLE ct_client_transfer ADD transfer_request_id VARCHAR2(100);
ALTER TABLE ct_client_transfer_h ADD trading_account_id number;
ALTER TABLE ct_client_transfer_h ADD transfer_request_id VARCHAR2(100);

------------------------------
-- JCS-15908 TRANSFERS
------------------------------
ALTER TABLE transfer_requests   ADD completed_date TIMESTAMP(3);
ALTER TABLE transfer_requests_h ADD completed_date TIMESTAMP(3);