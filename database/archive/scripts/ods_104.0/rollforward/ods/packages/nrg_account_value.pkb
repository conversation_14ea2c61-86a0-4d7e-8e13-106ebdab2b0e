CREATE OR REPLACE PACKAGE BODY bi_ods.nrg_account_value
AS
  -- ===================================================================================
  -- NRG_ACCOUNT_VALUE
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the account value model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --    ----------   ---------------   -----   ----------------------------------------
  --     03/04/2013   Sanket Mittal     1.0     Creation
  --     18/07/2013   Prachi Shah       1.1     Updated the name for local variable account_value_tab to account_values_tab
  --     29/07/2013   Prachi Shah       1.2     Updated the logic to populate business date, reporting date
  --     24/06/2016   Sanket Mittal     1.3     BER-2679-SWS 34 - Data Contract Changes - AccountValue
  --     17/01/2017   Sanket Mittal     1.4     BER-3154 BI_ODS.ACCOUNT_VALUE_SNAPSHOTS - New Attribute
  --     19/03/2019   Patrick Dinwiddy  1.5     BER-5035 new contract and target tables
  --     29/04/2019   Patrick Dinwiddy  1.6     JCS-10567 new attributes
  --     15/10/2019   Patrick Dinwiddy  1.7     JCS-11658 new attributes
  --     04/09/2020   Patrick Dinwiddy  1.8     JCS-13254
  --     01/12/2020   Patrick Dinwiddy  1.9     JCS-14066
  --
  -- ===================================================================================

  -- ===================================================================================
  -- Package Private Sub Routines And Constants
  -- ===================================================================================

  gc_default_timestamp   CONSTANT     TIMESTAMP(6) := to_date('01-Jan-1970','DD-Mon-YYYY');
  gc_version             CONSTANT     VARCHAR2(3)  := '1.9';

  TYPE account_values_tab IS TABLE OF account_value_snapshots%ROWTYPE;
  TYPE secondary_balances_tab IS TABLE OF acnt_vl_snp_scndry_blncs%ROWTYPE;
  TYPE historic_account_values_tab IS TABLE OF historic_account_value%ROWTYPE;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the history for account value snapshot secondary balances
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_old_records              secondary_balances_tab,
                        p_effective_end_timestamp  acnt_vl_snp_scndry_blncs_h.effective_end_timestamp%TYPE,
                        p_action                   acnt_vl_snp_scndry_blncs_h.action%TYPE,
                        p_snapshot_time            acnt_vl_snp_scndry_blncs_h.account_value_time%TYPE,
                        p_platform                 acnt_vl_snp_scndry_blncs_h.platform%TYPE) IS

  BEGIN
    DELETE acnt_vl_snp_scndry_blncs_h
    WHERE snapshot_time = p_snapshot_time AND
          platform = p_platform;

    FORALL lv_cnt IN 1..p_old_records.COUNT
      INSERT INTO acnt_vl_snp_scndry_blncs_h(snapshot_time,
                                            platform,
                                            trading_account_id,
                                            trading_account_type,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            effective_end_timestamp,
                                            action,
                                            action_timestamp,
                                            account_value_time,
                                            currency,
                                            balance)
                                     VALUES(p_old_records(lv_cnt).snapshot_time,
                                            p_old_records(lv_cnt).platform,
                                            p_old_records(lv_cnt).trading_account_id,
                                            p_old_records(lv_cnt).trading_account_type,
                                            p_old_records(lv_cnt).logical_load_timestamp,
                                            p_old_records(lv_cnt).created_by,
                                            p_old_records(lv_cnt).create_timestamp,
                                            p_old_records(lv_cnt).updated_by,
                                            p_old_records(lv_cnt).update_timestamp,
                                            p_old_records(lv_cnt).effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_records(lv_cnt).account_value_time,
                                            p_old_records(lv_cnt).currency,
                                            p_old_records(lv_cnt).balance);
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the history for account value snapshot
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_old_account_value        account_values_tab,
                        p_effective_end_timestamp  account_value_snapshots_h.effective_end_timestamp%TYPE,
                        p_action                   account_value_snapshots_h.action%TYPE,
                        p_snapshot_time            account_value_snapshots_h.snapshot_time%TYPE,
                        p_platform                 account_value_snapshots_h.platform%TYPE) IS

  BEGIN

    --
    --If there is an existing snapshot on the history table then delete it and insert the new data
    --

    DELETE account_value_snapshots_h
    WHERE platform = p_platform AND
          snapshot_time = p_snapshot_time;

    FORALL lv_cnt IN 1..p_old_account_value.COUNT
      INSERT INTO account_value_snapshots_h(account_value_time,
                                       platform,
                                       trading_account_id,
                                       trading_account_type,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       effective_end_timestamp,
                                       action,
                                       action_timestamp,
                                       snapshot_time,
                                       business_date,
                                       reporting_date,
                                       trading_account_codifier,
                                       trading_account_function,
                                       trading_account_prmry_ccy,
                                       cash_balance,
                                       margin,
                                       unrealized_profit_loss,
                                       liquidation_amount,
                                       equity,
                                       free_equity,
                                       is_subsequent_eval_pending,
                                       prdctd_tm_dpndnt_lqdtn_tm,
                                       standard_position_margin,
                                       prime_margin,
                                       prime_liquidation_amount,
                                       prime_reset_level,
                                       withdrawable_amount,
                                       is_eod,
                                       is_valid,
                                       standard_reset_level,
                                       primary_cash_balance,
                                       accnt_mon_unrealized_pnl,
                                       accnt_mon_equity,
                                       accnt_mon_free_equity,
                                       customer_unrealized_pnl,
                                       customer_equity,
                                       customer_free_equity,
                                       unrlzd_txbl_prfts_prmry_ccy,
                                       reserved_tax_in_prmry_ccy
                                       )
                                VALUES(p_old_account_value(lv_cnt).account_value_time,
                                       p_old_account_value(lv_cnt).platform,
                                       p_old_account_value(lv_cnt).trading_account_id,
                                       p_old_account_value(lv_cnt).trading_account_type,
                                       p_old_account_value(lv_cnt).logical_load_timestamp,
                                       p_old_account_value(lv_cnt).created_by,
                                       p_old_account_value(lv_cnt).create_timestamp,
                                       p_old_account_value(lv_cnt).updated_by,
                                       p_old_account_value(lv_cnt).update_timestamp,
                                       p_old_account_value(lv_cnt).effective_start_timestamp,
                                       p_effective_end_timestamp,
                                       p_action,
                                       SYSTIMESTAMP,
                                       p_old_account_value(lv_cnt).snapshot_time,
                                       p_old_account_value(lv_cnt).business_date,
                                       p_old_account_value(lv_cnt).reporting_date,
                                       p_old_account_value(lv_cnt).trading_account_codifier,
                                       p_old_account_value(lv_cnt).trading_account_function,
                                       p_old_account_value(lv_cnt).trading_account_prmry_ccy,
                                       p_old_account_value(lv_cnt).cash_balance,
                                       p_old_account_value(lv_cnt).margin,
                                       p_old_account_value(lv_cnt).unrealized_profit_loss,
                                       p_old_account_value(lv_cnt).liquidation_amount,
                                       p_old_account_value(lv_cnt).equity,
                                       p_old_account_value(lv_cnt).free_equity,
                                       p_old_account_value(lv_cnt).is_subsequent_eval_pending,
                                       p_old_account_value(lv_cnt).prdctd_tm_dpndnt_lqdtn_tm,
                                       p_old_account_value(lv_cnt).standard_position_margin,
                                       p_old_account_value(lv_cnt).prime_margin,
                                       p_old_account_value(lv_cnt).prime_liquidation_amount,
                                       p_old_account_value(lv_cnt).prime_reset_level,
                                       p_old_account_value(lv_cnt).withdrawable_amount,
                                       p_old_account_value(lv_cnt).is_eod,
                                       p_old_account_value(lv_cnt).is_valid,
                                       p_old_account_value(lv_cnt).standard_reset_level,
                                       p_old_account_value(lv_cnt).primary_cash_balance,
                                       p_old_account_value(lv_cnt).accnt_mon_unrealized_pnl,
                                       p_old_account_value(lv_cnt).accnt_mon_equity,
                                       p_old_account_value(lv_cnt).accnt_mon_free_equity,
                                       p_old_account_value(lv_cnt).customer_unrealized_pnl,
                                       p_old_account_value(lv_cnt).customer_equity,
                                       p_old_account_value(lv_cnt).customer_free_equity,
                                       p_old_account_value(lv_cnt).unrlzd_txbl_prfts_prmry_ccy,
                                       p_old_account_value(lv_cnt).reserved_tax_in_prmry_ccy);
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the history for account value snapshot secondary balances
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history2(p_old_records              secondary_balances_tab,
                        p_effective_end_timestamp  hstrc_acnt_vl_scndry_blncs_h.effective_end_timestamp%TYPE,
                        p_action                   hstrc_acnt_vl_scndry_blncs_h.action%TYPE,
                        p_snapshot_time            hstrc_acnt_vl_scndry_blncs_h.account_value_time%TYPE,
                        p_platform                 hstrc_acnt_vl_scndry_blncs_h.platform%TYPE) IS

  BEGIN
    DELETE hstrc_acnt_vl_scndry_blncs_h
    WHERE snapshot_time = p_snapshot_time AND
          platform = p_platform;

    FORALL lv_cnt IN 1..p_old_records.COUNT
      INSERT INTO hstrc_acnt_vl_scndry_blncs_h(snapshot_time,
                                            platform,
                                            trading_account_id,
                                            trading_account_type,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            effective_end_timestamp,
                                            action,
                                            action_timestamp,
                                            account_value_time,
                                            currency,
                                            balance)
                                     VALUES(p_old_records(lv_cnt).snapshot_time,
                                            p_old_records(lv_cnt).platform,
                                            p_old_records(lv_cnt).trading_account_id,
                                            p_old_records(lv_cnt).trading_account_type,
                                            p_old_records(lv_cnt).logical_load_timestamp,
                                            p_old_records(lv_cnt).created_by,
                                            p_old_records(lv_cnt).create_timestamp,
                                            p_old_records(lv_cnt).updated_by,
                                            p_old_records(lv_cnt).update_timestamp,
                                            p_old_records(lv_cnt).effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_records(lv_cnt).account_value_time,
                                            p_old_records(lv_cnt).currency,
                                            p_old_records(lv_cnt).balance);
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put the history for account value snapshot
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history2(p_old_historic_account_value       historic_account_values_tab,
                         p_effective_end_timestamp          historic_account_value_h.effective_end_timestamp%TYPE,
                         p_action                           historic_account_value_h.action%TYPE,
                         p_snapshot_time                    historic_account_value_h.snapshot_time%TYPE,
                         p_platform                         historic_account_value_h.platform%TYPE) IS

  BEGIN

    --
    --If there is an existing snapshot on the history table then delete it and insert the new data
    --

    DELETE historic_account_value_h
    WHERE platform = p_platform AND
          snapshot_time = p_snapshot_time;

    FORALL lv_cnt IN 1..p_old_historic_account_value.COUNT
      INSERT INTO historic_account_value_h
                                        (snapshot_time               ,
                                         platform                   ,
                                         trading_account_id         ,
                                         trading_account_type       ,
                                         logical_load_timestamp     ,
                                         created_by                 ,
                                         create_timestamp           ,
                                         updated_by                 ,
                                         update_timestamp           ,
                                         effective_start_timestamp   ,
                                         effective_end_timestamp    ,
                                         action                     ,
                                         action_timestamp           ,
                                         business_date               ,
                                         reporting_date             ,
                                         trading_account_function   ,
                                         trading_account_primary_ccy,
                                         cash_balance               ,
                                         total_margin               ,
                                         standard_liquidation_amount,
                                         standard_reset_level       ,
                                         is_subsequent_evluatn_pndng,
                                         prdctd_tm_dpndnt_lqdtn_tm   ,
                                         standard_margin             ,
                                         prime_margin               ,
                                         prime_liquidation_amount   ,
                                         prime_reset_level           ,
                                         withdrawable_amount         ,
                                         is_valid                   ,
                                         primary_cash_balance       ,
                                         accnt_mon_upnl_in_prmry_ccy,
                                         accnt_mon_equity           ,
                                         accnt_mon_free_equity       ,
                                         customer_upnl_in_prmry_ccy,
                                         customer_equity            ,
                                         customer_free_equity       ,
                                         instrument_schema_code     ,
                                         closeout_schema_code       ,
                                         cash_accounting_schema_code,
                                         payment_schema_code         ,
                                         commission_schema_code     ,
                                         carrying_costs_schema_code ,
                                         crryng_csts_offst_schm_cd  ,
                                         price_feed_schema_code     ,
                                         independent_margin_amount  ,
                                         standard_liquidation_level ,
                                         prime_liquidation_level    ,
                                         standard_level_type        ,
                                         prime_level_type           ,
                                         cmc_upnl_in_prmry_ccy      ,
                                         unrl_txbl_profit_in_prmry_ccy,
                                         reserved_tax_in_prmry_ccy ,
                                         margin_coverage,
                                         accrued_capital_gains,
                                         used_tax_free_allowance,
                                         loss_offset_account_balance,
                                         accrd_capital_gains_tax_amnt,
                                         accrd_reunfctn_srchrg_tx_amt,
                                         accrued_church_tax_amount,
                                         fx_margin,
                                         fx_margin_in_main_margin_ccy,
                                         relevant_fx_margin_direction,
                                         main_margin_currency,
                                         main_to_prmry_ccy_reval_rate,
                                         pending_cg_in_primary_ccy,
                                         PARENT_ACCOUNT_PRIMARY_FXR,
                                         CASH_BALANCE_PRNT_ACCNT_PRMRY_CCY,
                                         CUSTOMER_UPNL_PRNT_ACCNT_PRMRY_CCY,
                                         CUSTOMER_EQUITY_PRNT_ACCNT_PRMRY_CCY,
                                         pending_profit_loss)
                               VALUES(p_old_historic_account_value(lv_cnt).snapshot_time,
                                      p_old_historic_account_value(lv_cnt).platform,
                                      p_old_historic_account_value(lv_cnt).trading_account_id,
                                      p_old_historic_account_value(lv_cnt).trading_account_type,
                                      p_old_historic_account_value(lv_cnt).logical_load_timestamp,
                                      p_old_historic_account_value(lv_cnt).created_by,
                                      p_old_historic_account_value(lv_cnt).create_timestamp,
                                      p_old_historic_account_value(lv_cnt).updated_by,
                                      p_old_historic_account_value(lv_cnt).update_timestamp,
                                      p_old_historic_account_value(lv_cnt).effective_start_timestamp,
                                      p_effective_end_timestamp,
                                      p_action,
                                      SYSTIMESTAMP,
                                      p_old_historic_account_value(lv_cnt).business_date,
                                      p_old_historic_account_value(lv_cnt).reporting_date,
                                      p_old_historic_account_value(lv_cnt).trading_account_function   ,
                                      p_old_historic_account_value(lv_cnt).trading_account_primary_ccy,
                                      p_old_historic_account_value(lv_cnt).cash_balance               ,
                                      p_old_historic_account_value(lv_cnt).total_margin               ,
                                      p_old_historic_account_value(lv_cnt).standard_liquidation_amount,
                                      p_old_historic_account_value(lv_cnt).standard_reset_level       ,
                                      p_old_historic_account_value(lv_cnt).is_subsequent_evluatn_pndng,
                                      p_old_historic_account_value(lv_cnt).prdctd_tm_dpndnt_lqdtn_tm   ,
                                      p_old_historic_account_value(lv_cnt).standard_margin             ,
                                      p_old_historic_account_value(lv_cnt).prime_margin               ,
                                      p_old_historic_account_value(lv_cnt).prime_liquidation_amount   ,
                                      p_old_historic_account_value(lv_cnt).prime_reset_level           ,
                                      p_old_historic_account_value(lv_cnt).withdrawable_amount         ,
                                      p_old_historic_account_value(lv_cnt).is_valid                   ,
                                      p_old_historic_account_value(lv_cnt).primary_cash_balance       ,
                                      p_old_historic_account_value(lv_cnt).accnt_mon_upnl_in_prmry_ccy,
                                      p_old_historic_account_value(lv_cnt).accnt_mon_equity           ,
                                      p_old_historic_account_value(lv_cnt).accnt_mon_free_equity       ,
                                      p_old_historic_account_value(lv_cnt).customer_upnl_in_prmry_ccy,
                                      p_old_historic_account_value(lv_cnt).customer_equity             ,
                                      p_old_historic_account_value(lv_cnt).customer_free_equity       ,
                                      p_old_historic_account_value(lv_cnt).instrument_schema_code     ,
                                      p_old_historic_account_value(lv_cnt).closeout_schema_code       ,
                                      p_old_historic_account_value(lv_cnt).cash_accounting_schema_code,
                                      p_old_historic_account_value(lv_cnt).payment_schema_code         ,
                                      p_old_historic_account_value(lv_cnt).commission_schema_code     ,
                                      p_old_historic_account_value(lv_cnt).carrying_costs_schema_code ,
                                      p_old_historic_account_value(lv_cnt).crryng_csts_offst_schm_cd  ,
                                      p_old_historic_account_value(lv_cnt).price_feed_schema_code     ,
                                      p_old_historic_account_value(lv_cnt).independent_margin_amount  ,
                                      p_old_historic_account_value(lv_cnt).standard_liquidation_level ,
                                      p_old_historic_account_value(lv_cnt).prime_liquidation_level    ,
                                      p_old_historic_account_value(lv_cnt).standard_level_type        ,
                                      p_old_historic_account_value(lv_cnt).prime_level_type           ,
                                      p_old_historic_account_value(lv_cnt).cmc_upnl_in_prmry_ccy      ,
                                      p_old_historic_account_value(lv_cnt).unrl_txbl_profit_in_prmry_ccy,
                                      p_old_historic_account_value(lv_cnt).reserved_tax_in_prmry_ccy,
                                      p_old_historic_account_value(lv_cnt).margin_coverage,
                                      p_old_historic_account_value(lv_cnt).accrued_capital_gains,
                                      p_old_historic_account_value(lv_cnt).used_tax_free_allowance,
                                      p_old_historic_account_value(lv_cnt).loss_offset_account_balance,
                                      p_old_historic_account_value(lv_cnt).accrd_capital_gains_tax_amnt,
                                      p_old_historic_account_value(lv_cnt).accrd_reunfctn_srchrg_tx_amt,
                                      p_old_historic_account_value(lv_cnt).accrued_church_tax_amount,
                                      p_old_historic_account_value(lv_cnt).fx_margin,
                                      p_old_historic_account_value(lv_cnt).fx_margin_in_main_margin_ccy,
                                      p_old_historic_account_value(lv_cnt).relevant_fx_margin_direction,
                                      p_old_historic_account_value(lv_cnt).main_margin_currency,
                                      p_old_historic_account_value(lv_cnt).main_to_prmry_ccy_reval_rate,
                                      p_old_historic_account_value(lv_cnt).pending_cg_in_primary_ccy,
                                      p_old_historic_account_value(lv_cnt).PARENT_ACCOUNT_PRIMARY_FXR,
                                      p_old_historic_account_value(lv_cnt).CASH_BALANCE_PRNT_ACCNT_PRMRY_CCY,
                                      p_old_historic_account_value(lv_cnt).CUSTOMER_UPNL_PRNT_ACCNT_PRMRY_CCY,
                                      p_old_historic_account_value(lv_cnt).CUSTOMER_EQUITY_PRNT_ACCNT_PRMRY_CCY,
                                      p_old_historic_account_value(lv_cnt).pending_profit_loss);
  END;
  -- ===================================================================================
  -- Public Subroutines
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION version
      RETURN VARCHAR2 DETERMINISTIC
  IS
  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END version;

  -- ===================================================================================
  -- put_secondary_balances
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put account value secondary Balances
	--
  -- Notes:
  -- ------
	--
  --
  --     Tables potentially populated:
  --
  --     ACNT_VL_SNP_SCNDRY_BLNCS
	--     ACNT_VL_SNP_SCNDRY_BLNCS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_secondary_balances             Secondary Balances
  --     p_logical_load_timestamp         Logical Load Timestamp
  --
	--
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_secondary_balances (p_user                        IN acnt_vl_snp_scndry_blncs.created_by%TYPE,
                                    p_effective_start_timestamp   IN acnt_vl_snp_scndry_blncs.effective_start_timestamp%TYPE,
                                    p_snapshot_time               IN acnt_vl_snp_scndry_blncs.snapshot_time%TYPE,
                                    p_platform                    IN acnt_vl_snp_scndry_blncs.platform%TYPE,
                                    p_secondary_balances          IN acnt_vl_snp_scndry_blncs_tab,
                                    p_logical_load_timestamp      IN acnt_vl_snp_scndry_blncs.logical_load_timestamp%TYPE) IS

  BEGIN
    INSERT INTO acnt_vl_snp_scndry_blncs (snapshot_time,
                                          platform,
                                          trading_account_id,
                                          trading_account_type,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          effective_start_timestamp,
                                          account_value_time,
                                          currency,
                                          balance)
                                   SELECT p_snapshot_time,
                                          p_platform,
                                          trading_account_id,
                                          trading_account_type,
                                          p_logical_load_timestamp,
                                          p_user,
                                          SYSTIMESTAMP,
                                          p_user,
                                          SYSTIMESTAMP,
                                          p_effective_start_timestamp,
                                          account_value_time,
                                          currency,
                                          balance
                                   FROM TABLE(CAST(p_secondary_balances AS acnt_vl_snp_scndry_blncs_tab));
  END;




  -- ===================================================================================
  -- put_hstrc_secondary_balances
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put account value secondary Balances
	--
  -- Notes:
  -- ------
	--
  --
  --     Tables potentially populated:
  --
  --     ACNT_VL_SNP_SCNDRY_BLNCS
	--     ACNT_VL_SNP_SCNDRY_BLNCS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_secondary_balances             Secondary Balances
  --     p_logical_load_timestamp         Logical Load Timestamp
  --
	--
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_hstrc_secondary_balances(p_user                        IN acnt_vl_snp_scndry_blncs.created_by%TYPE,
                                            p_effective_start_timestamp   IN acnt_vl_snp_scndry_blncs.effective_start_timestamp%TYPE,
                                            p_snapshot_time               IN acnt_vl_snp_scndry_blncs.snapshot_time%TYPE,
                                            p_platform                    IN acnt_vl_snp_scndry_blncs.platform%TYPE,
                                            p_secondary_balances          IN acnt_vl_snp_scndry_blncs_tab,
                                            p_logical_load_timestamp      IN acnt_vl_snp_scndry_blncs.logical_load_timestamp%TYPE) IS

  BEGIN
    INSERT INTO hstrc_acnt_vl_scndry_blncs (snapshot_time,
                                            platform,
                                            trading_account_id,
                                            trading_account_type,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            account_value_time,
                                            currency,
                                            balance)
                                     SELECT p_snapshot_time,
                                            p_platform,
                                            trading_account_id,
                                            trading_account_type,
                                            p_logical_load_timestamp,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_user,
                                            SYSTIMESTAMP,
                                            p_effective_start_timestamp,
                                            account_value_time,
                                            currency,
                                            balance
                                     FROM TABLE(CAST(p_secondary_balances AS acnt_vl_snp_scndry_blncs_tab));
  END;



  --
  --
  --
  -- ===================================================================================
  -- put_account_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put account values
	--
  -- Notes:
  -- ------
	--
  --
  --     Tables potentially populated:
  --
  --     ACCOUNT_VALUES
	--     ACCOUNT_VALUES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_account_value                  Account Values
  --
	--
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_account_value( p_user                            IN account_value_snapshots.created_by%TYPE,
                                 p_effective_start_timestamp       IN account_value_snapshots.effective_start_timestamp%TYPE,
                                 p_platform                        IN account_value_snapshots.platform%TYPE,
                                 p_snapshot_time                   IN account_value_snapshots.snapshot_time%TYPE,
                                 p_account_value                   IN account_value_tab) IS

      lv_business_date            DATE;
      lv_reporting_date           DATE;
      lv_logical_load_timestamp   TIMESTAMP(6);
      lv_old_account_values       account_values_tab;
      lv_old_ant_val_scndry_bl    secondary_balances_tab;

      lv_exists                   NUMBER;

    BEGIN

      logger.logger.set_module('nrg_account_value.put_account_value');

      --
      --Evaluate Business date
      --

      --lv_business_date := nrg_common.get_business_date(p_snapshot_time);
      -- Updated the logic as per eod position

      lv_business_date := trunc(p_effective_start_timestamp);

      --
      --Evaluate Reporting Date
      --

      --lv_reporting_date := nrg_common.get_reporting_date(p_snapshot_time);

      IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
        lv_reporting_date := lv_business_date + 2;
      ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
        lv_reporting_date := lv_business_date + 1;
      ELSE
        lv_reporting_date := lv_business_date;
      END IF;

      --
      --Set Logical Load Timestamp
      --
      lv_logical_load_timestamp := SYSTIMESTAMP;

      --
      --Create Trading Account Stubs If Account Does Not Exist
      --

      IF p_account_value IS NOT NULL AND p_account_value.COUNT > 0 THEN
        FOR lv_cnt IN 1..p_account_value.COUNT LOOP
          nrg_trading_account.create_trading_account_stub(p_user                      => p_user,
                                                          p_logical_load_timestamp    => lv_logical_load_timestamp,
                                                          p_effective_start_timestamp => p_effective_start_timestamp,
                                                          p_trading_account_id        => p_account_value(lv_cnt).trading_account_id,
                                                          p_trading_account_type      => p_account_value(lv_cnt).trading_account_type);
        END LOOP;
      END IF;

      --
      --Check If the Snapshot Already Exists
      --

      BEGIN
        SELECT 1
        INTO lv_exists
        FROM account_value_snapshots
        WHERE platform = p_platform AND
              snapshot_time = p_snapshot_time AND
              rownum = 1;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          lv_exists := 0;
      END;

      IF lv_exists = 1 THEN
        --
        --If the snapshot exists then take the copy of the snapshot from the base table and delete the data
        --
        SELECT *
        BULK COLLECT INTO lv_old_account_values
        FROM account_value_snapshots
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;

        SELECT *
        BULK COLLECT INTO lv_old_ant_val_scndry_bl
        FROM acnt_vl_snp_scndry_blncs
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;

        --
        --Write the existing data to history
        --

        put_history(p_old_account_value        => lv_old_account_values,
                    p_effective_end_timestamp  => p_effective_start_timestamp,
                    p_action                   => 'U',
                    p_snapshot_time            => p_snapshot_time,
                    p_platform                 => p_platform);

        put_history(p_old_records              => lv_old_ant_val_scndry_bl,
                    p_effective_end_timestamp  => p_effective_start_timestamp,
                    p_action                   => 'U',
                    p_snapshot_time            => p_snapshot_time,
                    p_platform                 => p_platform);

        DELETE acnt_vl_snp_scndry_blncs
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;

        DELETE account_value_snapshots
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;
      END IF;

      FORALL lv_cnt IN 1..p_account_value.COUNT
        INSERT INTO account_value_snapshots(account_value_time,
                                       platform,
                                       trading_account_id,
                                       trading_account_type,
                                       logical_load_timestamp,
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       effective_start_timestamp,
                                       snapshot_time,
                                       business_date,
                                       reporting_date,
                                       trading_account_codifier,
                                       trading_account_function,
                                       trading_account_prmry_ccy,
                                       cash_balance,
                                       margin,
                                       unrealized_profit_loss,
                                       liquidation_amount,
                                       equity,
                                       free_equity,
                                       standard_reset_level,
                                       is_subsequent_eval_pending,
                                       prdctd_tm_dpndnt_lqdtn_tm,
                                       standard_position_margin,
                                       prime_margin,
                                       prime_liquidation_amount,
                                       prime_reset_level,
                                       withdrawable_amount,
                                       is_eod,
                                       is_valid,
                                       primary_cash_balance,
                                       accnt_mon_unrealized_pnl,
                                       accnt_mon_equity,
                                       accnt_mon_free_equity,
                                       customer_unrealized_pnl,
                                       customer_equity,
                                       customer_free_equity,
                                       unrlzd_txbl_prfts_prmry_ccy,
                                       reserved_tax_in_prmry_ccy)
                                VALUES(p_account_value(lv_cnt).account_value_time,
                                       p_platform,
                                       p_account_value(lv_cnt).trading_account_id,
                                       p_account_value(lv_cnt).trading_account_type,
                                       lv_logical_load_timestamp,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_user,
                                       SYSTIMESTAMP,
                                       p_effective_start_timestamp,
                                       p_snapshot_time,
                                       lv_business_date,
                                       lv_reporting_date,
                                       p_account_value(lv_cnt).trading_account_codifier,
                                       p_account_value(lv_cnt).trading_account_function,
                                       p_account_value(lv_cnt).trading_account_prmry_ccy,
                                       p_account_value(lv_cnt).cash_balance,
                                       p_account_value(lv_cnt).margin,
                                       p_account_value(lv_cnt).unrealized_profit_loss,
                                       p_account_value(lv_cnt).liquidation_amount,
                                       p_account_value(lv_cnt).equity,
                                       p_account_value(lv_cnt).free_equity,
                                       p_account_value(lv_cnt).standard_reset_level,
                                       p_account_value(lv_cnt).is_subsequent_eval_pending,
                                       p_account_value(lv_cnt).prdctd_tm_dpndnt_lqdtn_tm,
                                       p_account_value(lv_cnt).standard_position_margin,
                                       p_account_value(lv_cnt).prime_margin,
                                       p_account_value(lv_cnt).prime_liquidation_amount,
                                       p_account_value(lv_cnt).prime_reset_level,
                                       p_account_value(lv_cnt).withdrawable_amount,
                                       p_account_value(lv_cnt).is_eod,
                                       p_account_value(lv_cnt).is_valid,
                                       p_account_value(lv_cnt).primary_cash_balance,
                                       p_account_value(lv_cnt).accnt_mon_unrealized_pnl,
                                       p_account_value(lv_cnt).accnt_mon_equity,
                                       p_account_value(lv_cnt).accnt_mon_free_equity,
                                       p_account_value(lv_cnt).customer_unrealized_pnl,
                                       p_account_value(lv_cnt).customer_equity,
                                       p_account_value(lv_cnt).customer_free_equity,
                                       p_account_value(lv_cnt).unrlzd_txbl_prfts_prmry_ccy,
                                       p_account_value(lv_cnt).reserved_tax_in_prmry_ccy);


      FOR lv_cnt IN 1..p_account_value.COUNT LOOP
        put_secondary_balances (p_user                        => p_user,
                                p_effective_start_timestamp   => p_effective_start_timestamp,
                                p_snapshot_time               => p_snapshot_time,
                                p_platform                    => p_platform,
                                p_secondary_balances          => p_account_value(lv_cnt).secondary_balances,
                                p_logical_load_timestamp      => lv_logical_load_timestamp);
      END LOOP;

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

  -- ===================================================================================
  -- put_historic_account_value
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put historic account values
	--
  -- Notes:
  -- ------
	--
  --
  --     Tables potentially populated:
  --
  --     historic_account_value
	--     historic_account_value_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_historic_account_value                  Account Values
  --
	--
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_historic_account_value( p_user                            IN historic_account_value.created_by%TYPE,
                                          p_effective_start_timestamp       IN historic_account_value.effective_start_timestamp%TYPE,
                                          p_platform                        IN historic_account_value.platform%TYPE,
                                          p_snapshot_time                   IN historic_account_value.snapshot_time%TYPE,
                                          p_historic_account_value          IN historic_account_value_tab) IS

      lv_business_date                DATE;
      lv_reporting_date               DATE;
      lv_logical_load_timestamp       TIMESTAMP(6);
      lv_old_historic_account_values  historic_account_values_tab;
      lv_old_hist_ant_val_scndry_bl   secondary_balances_tab;

      lv_exists                   NUMBER;

    BEGIN

      logger.logger.set_module('nrg_account_value.put_historic_account_value');

      --
      --Evaluate Business date
      --

      --lv_business_date := nrg_common.get_business_date(p_snapshot_time);
      -- Updated the logic as per eod position

      lv_business_date := trunc(p_effective_start_timestamp);

      --
      --Evaluate Reporting Date
      --

      --lv_reporting_date := nrg_common.get_reporting_date(p_snapshot_time);

      IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
        lv_reporting_date := lv_business_date + 2;
      ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
        lv_reporting_date := lv_business_date + 1;
      ELSE
        lv_reporting_date := lv_business_date;
      END IF;

      --
      --Set Logical Load Timestamp
      --
      lv_logical_load_timestamp := SYSTIMESTAMP;

      --
      --Create Trading Account Stubs If Account Does Not Exist
      --

      IF p_historic_account_value IS NOT NULL AND p_historic_account_value.COUNT > 0 THEN
        FOR lv_cnt IN 1..p_historic_account_value.COUNT LOOP
          nrg_trading_account.create_trading_account_stub(p_user                      => p_user,
                                                          p_logical_load_timestamp    => lv_logical_load_timestamp,
                                                          p_effective_start_timestamp => p_effective_start_timestamp,
                                                          p_trading_account_id        => p_historic_account_value(lv_cnt).trading_account_id,
                                                          p_trading_account_type      => p_historic_account_value(lv_cnt).trading_account_type);
        END LOOP;
      END IF;

      --
      --Check If the Snapshot Already Exists
      --

      BEGIN
        SELECT 1
        INTO lv_exists
        FROM historic_account_value
        WHERE platform = p_platform AND
              snapshot_time = p_snapshot_time AND
              rownum = 1;
      EXCEPTION
        WHEN NO_DATA_FOUND THEN
          lv_exists := 0;
      END;

      IF lv_exists = 1 THEN
        --
        --If the snapshot exists then take the copy of the snapshot from the base table and delete the data
        --
        SELECT *
        BULK COLLECT INTO lv_old_historic_account_values
        FROM historic_account_value
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;

        SELECT *
        BULK COLLECT INTO lv_old_hist_ant_val_scndry_bl
        FROM hstrc_acnt_vl_scndry_blncs
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;

        --
        --Write the existing data to history
        --

        put_history2(p_old_historic_account_value => lv_old_historic_account_values,
                    p_effective_end_timestamp    => p_effective_start_timestamp,
                    p_action                     => 'U',
                    p_snapshot_time              => p_snapshot_time,
                    p_platform                   => p_platform);

        put_history2(p_old_records                => lv_old_hist_ant_val_scndry_bl,
                    p_effective_end_timestamp    => p_effective_start_timestamp,
                    p_action                     => 'U',
                    p_snapshot_time              => p_snapshot_time,
                    p_platform                   => p_platform);

        DELETE hstrc_acnt_vl_scndry_blncs
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;

        DELETE historic_account_value
        WHERE snapshot_time = p_snapshot_time AND
              platform = p_platform;
      END IF;

      FORALL lv_cnt IN 1..p_historic_account_value.COUNT
         INSERT INTO historic_account_value(snapshot_time,
                                            platform,
                                            trading_account_id,
                                            trading_account_type,
                                            logical_load_timestamp,
                                            created_by,
                                            create_timestamp,
                                            updated_by,
                                            update_timestamp,
                                            effective_start_timestamp,
                                            business_date,
                                            reporting_date,
                                            trading_account_function,
                                            trading_account_primary_ccy,
                                            cash_balance,
                                            total_margin,
                                            standard_liquidation_amount,
                                            standard_reset_level,
                                            is_subsequent_evluatn_pndng,
                                            prdctd_tm_dpndnt_lqdtn_tm,
                                            standard_margin,
                                            prime_margin,
                                            prime_liquidation_amount,
                                            prime_reset_level,
                                            withdrawable_amount,
                                            is_valid,
                                            primary_cash_balance,
                                            accnt_mon_upnl_in_prmry_ccy,
                                            accnt_mon_equity,
                                            accnt_mon_free_equity,
                                            customer_upnl_in_prmry_ccy,
                                            customer_equity,
                                            customer_free_equity,
                                            instrument_schema_code,
                                            closeout_schema_code,
                                            cash_accounting_schema_code,
                                            payment_schema_code,
                                            commission_schema_code,
                                            carrying_costs_schema_code,
                                            crryng_csts_offst_schm_cd,
                                            price_feed_schema_code,
                                            independent_margin_amount,
                                            standard_liquidation_level,
                                            prime_liquidation_level,
                                            standard_level_type,
                                            prime_level_type,
                                            cmc_upnl_in_prmry_ccy,
                                            unrl_txbl_profit_in_prmry_ccy,
                                            reserved_tax_in_prmry_ccy,
                                            margin_coverage,
                                            accrued_capital_gains,
                                            used_tax_free_allowance,
                                            loss_offset_account_balance,
                                            accrd_capital_gains_tax_amnt,
                                            accrd_reunfctn_srchrg_tx_amt,
                                            accrued_church_tax_amount,
                                            fx_margin,
                                            fx_margin_in_main_margin_ccy,
                                            relevant_fx_margin_direction,
                                            main_margin_currency,
                                            main_to_prmry_ccy_reval_rate,
                                            pending_cg_in_primary_ccy,
                                            PARENT_ACCOUNT_PRIMARY_FXR,
                                            CASH_BALANCE_PRNT_ACCNT_PRMRY_CCY,
                                            CUSTOMER_UPNL_PRNT_ACCNT_PRMRY_CCY,
                                            CUSTOMER_EQUITY_PRNT_ACCNT_PRMRY_CCY,
                                            pending_profit_loss
                                            )
                                    VALUES(p_snapshot_time,
                                           p_platform,
                                           p_historic_account_value(lv_cnt).trading_account_id,
                                           p_historic_account_value(lv_cnt).trading_account_type,
                                           lv_logical_load_timestamp,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_user,
                                           SYSTIMESTAMP,
                                           p_effective_start_timestamp,
                                           lv_business_date,
                                           lv_reporting_date,
                                           p_historic_account_value(lv_cnt).trading_account_function,
                                           p_historic_account_value(lv_cnt).trading_account_primary_ccy,
                                           p_historic_account_value(lv_cnt).cash_balance,
                                           p_historic_account_value(lv_cnt).total_margin,
                                           p_historic_account_value(lv_cnt).standard_liquidation_amount,
                                           p_historic_account_value(lv_cnt).standard_reset_level,
                                           p_historic_account_value(lv_cnt).is_subsequent_evluatn_pndng,
                                           p_historic_account_value(lv_cnt).prdctd_tm_dpndnt_lqdtn_tm,
                                           p_historic_account_value(lv_cnt).standard_margin,
                                           p_historic_account_value(lv_cnt).prime_margin,
                                           p_historic_account_value(lv_cnt).prime_liquidation_amount,
                                           p_historic_account_value(lv_cnt).prime_reset_level,
                                           p_historic_account_value(lv_cnt).withdrawable_amount,
                                           p_historic_account_value(lv_cnt).is_valid,
                                           p_historic_account_value(lv_cnt).primary_cash_balance,
                                           p_historic_account_value(lv_cnt).accnt_mon_upnl_in_prmry_ccy,
                                           p_historic_account_value(lv_cnt).accnt_mon_equity,
                                           p_historic_account_value(lv_cnt).accnt_mon_free_equity,
                                           p_historic_account_value(lv_cnt).customer_upnl_in_prmry_ccy,
                                           p_historic_account_value(lv_cnt).customer_equity,
                                           p_historic_account_value(lv_cnt).customer_free_equity,
                                           p_historic_account_value(lv_cnt).instrument_schema_code,
                                           p_historic_account_value(lv_cnt).closeout_schema_code,
                                           p_historic_account_value(lv_cnt).cash_accounting_schema_code,
                                           p_historic_account_value(lv_cnt).payment_schema_code,
                                           p_historic_account_value(lv_cnt).commission_schema_code,
                                           p_historic_account_value(lv_cnt).carrying_costs_schema_code,
                                           p_historic_account_value(lv_cnt).crryng_csts_offst_schm_cd,
                                           p_historic_account_value(lv_cnt).price_feed_schema_code,
                                           p_historic_account_value(lv_cnt).independent_margin_amount,
                                           p_historic_account_value(lv_cnt).standard_liquidation_level,
                                           p_historic_account_value(lv_cnt).prime_liquidation_level,
                                           p_historic_account_value(lv_cnt).standard_level_type,
                                           p_historic_account_value(lv_cnt).prime_level_type,
                                           p_historic_account_value(lv_cnt).cmc_upnl_in_prmry_ccy,
                                           p_historic_account_value(lv_cnt).unrl_txbl_profit_in_prmry_ccy,
                                           p_historic_account_value(lv_cnt).reserved_tax_in_prmry_ccy,
                                           p_historic_account_value(lv_cnt).margin_coverage,
                                           p_historic_account_value(lv_cnt).accrued_capital_gains,
                                           p_historic_account_value(lv_cnt).used_tax_free_allowance,
                                           p_historic_account_value(lv_cnt).loss_offset_account_balance,
                                           p_historic_account_value(lv_cnt).accrd_capital_gains_tax_amnt,
                                           p_historic_account_value(lv_cnt).accrd_reunfctn_srchrg_tx_amt,
                                           p_historic_account_value(lv_cnt).accrued_church_tax_amount,
                                           p_historic_account_value(lv_cnt).fx_margin,
                                           p_historic_account_value(lv_cnt).fx_margin_in_main_margin_ccy,
                                           p_historic_account_value(lv_cnt).relevant_fx_margin_direction,
                                           p_historic_account_value(lv_cnt).main_margin_currency,
                                           p_historic_account_value(lv_cnt).main_to_prmry_ccy_reval_rate,
                                           p_historic_account_value(lv_cnt).pending_cg_in_primary_ccy,
                                           p_historic_account_value(lv_cnt).PARENT_ACCOUNT_PRIMARY_FXR,
                                           p_historic_account_value(lv_cnt).CASH_BALANCE_PRNT_ACCNT_PRMRY_CCY,
                                           p_historic_account_value(lv_cnt).CUSTOMER_UPNL_PRNT_ACCNT_PRMRY_CCY,
                                           p_historic_account_value(lv_cnt).CUSTOMER_EQUITY_PRNT_ACCNT_PRMRY_CCY,
                                           p_historic_account_value(lv_cnt).pending_profit_loss
                                           );


      FOR lv_cnt IN 1..p_historic_account_value.COUNT LOOP

        put_hstrc_secondary_balances (p_user                        => p_user,
                                      p_effective_start_timestamp   => p_effective_start_timestamp,
                                      p_snapshot_time               => p_snapshot_time,
                                      p_platform                    => p_platform,
                                      p_secondary_balances          => p_historic_account_value(lv_cnt).secondary_balances,
                                      p_logical_load_timestamp      => lv_logical_load_timestamp);
      END LOOP;

    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

END nrg_account_value;
/