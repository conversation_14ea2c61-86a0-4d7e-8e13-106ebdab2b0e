DROP TYPE price_feed_schemas_tab FORCE;
DROP TYPE price_feed_schemas_obj FORCE;

CREATE OR REPLACE TYPE price_feed_schemas_obj IS OBJECT (
price_feed_schema_name                 VARCHAR2(50),
default_band                           NUMBER,
default_price_stream_type              NUMBER,
price_feed_schema_items                price_feed_schema_items_tab,
default_adjust_id                      NUMBER,
default_adjust_alias                   VARCHAR2(50),
default_band_alias                     VARCHAR2(50),
dflt_price_stream_type_alias           VARCHAR2(50),
is_deleted	                           VARCHAR2(3),
is_eod                                 VARCHAR2(3),
is_execute_at_origin                   VARCHAR2(3),
default_swap_point_band                NUMBER,
dflt_swap_point_band_alias             VARCHAR(50),
default_spread_proportion              NUMBER,
default_out_of_hours_band_id	         NUMBER,
default_out_of_hours_band_alias	       VARCHAR2(50)
);
/