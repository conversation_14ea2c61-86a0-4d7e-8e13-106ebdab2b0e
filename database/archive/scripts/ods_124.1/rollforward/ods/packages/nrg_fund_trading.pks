create or replace PACKAGE NRG_FUND_TRADING AS

 -- ===================================================================================
  -- put_aggregated_fund_orders
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put aggregated_fund_orders
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_aggregated_fund_orders(p_user                           IN aggregated_fund_orders.created_by%TYPE,
                                       p_aggregated_fund_orders         IN aggregated_fund_orders_tab);


-- ===================================================================================
  -- put_aggregated_fund_order
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put aggregated_fund_order
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_aggregated_fund_order(p_user                           IN aggregated_fund_orders.created_by%TYPE,
                                       p_aggregated_fund_order         IN aggregated_fund_orders_obj);
                                       
  -- ===================================================================================
  -- put_fund_cost_and_charges
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put fund_cost_and_charges
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_fund_cost_charges(p_user                           IN fund_cost_and_charges.created_by%TYPE,
                                  p_fund_cost_and_charges          IN fund_cost_and_charges_tab);


  -- ===================================================================================
  -- put_aggregated_fund_orders_ids
  -- ===================================================================================
  PROCEDURE put_aggregated_fund_orders_ids(p_aggregated_fund_orders_ids IN AGGREGATED_FUND_ORDERS_IDS_TAB);

  -- ===================================================================================
  -- put_fund_orders_ids
  -- ===================================================================================
  PROCEDURE put_fund_orders_ids(p_fund_order_id IN AGGREGATED_FUND_ORDERS_IDS_OBJ);

-- ===================================================================================
  -- put_fund_orders_ids_h
  -- ===================================================================================
  PROCEDURE put_fund_orders_ids_h(p_del_order_id IN AGGREGATED_FUND_ORDERS_IDS%ROWTYPE);

END NRG_FUND_TRADING;
/