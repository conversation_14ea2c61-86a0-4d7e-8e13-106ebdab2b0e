create or replace PACKAGE BODY NRG_FUND_TRADING AS

  -- ===================================================================================
  -- put_aggregated_fund_orders
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put aggregated_fund_orders data
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_aggregated_fund_orders(p_user                    IN aggregated_fund_orders.created_by%TYPE,
                                p_aggregated_fund_orders         IN aggregated_fund_orders_tab)
IS
    t_aggregated_fund_orders aggregated_fund_orders_proc_tab;

BEGIN

    logger.logger.set_module('put_aggregated_fund_orders');
    -- start to put aggregated fund order
    for i in p_aggregated_fund_orders.first .. p_aggregated_fund_orders.last
    loop
        put_aggregated_fund_order(p_user, p_aggregated_fund_orders(i));
    end loop;

    EXCEPTION WHEN OTHERS THEN
        logger.logger.SEVERE(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

  -- ===================================================================================
    -- aggregated_fund_order
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put aggregated_fund_order
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_aggregated_fund_order(p_user                  IN aggregated_fund_orders.created_by%TYPE,
                                        p_aggregated_fund_order IN aggregated_fund_orders_obj)
        IS
            lv_logical_load_timestamp aggregated_fund_orders.logical_load_timestamp%TYPE;
            lv_row_exists number;
            lv_same_row number;
            lv_insert_row number;

        BEGIN
            logger.logger.set_module('put_aggregated_fund_order');

            lv_logical_load_timestamp := SYSTIMESTAMP;
            lv_row_exists:=0;
            lv_same_row:=0;

            -- if there is a record that has the same order id:
            --   if yes:
            --      if anything change
            --         if yes, insert the current record in main table into historical, update the current record and also ids
            --         otherwise, skip it
            --   otherwise, insert the current record into main table and ids
            -- may need a small change if you can really get the every change of every order,
            -- because it depends which table you want to represent that.

            select count(*) into lv_row_exists from BI_ODS.AGGREGATED_FUND_ORDERS afo
                where afo.aggregated_fund_order_id = p_aggregated_fund_order.aggregated_fund_order_id;

            if(lv_row_exists > 0) then
                -- if the version number larger than the current version number or any values changed for the same version number
                -- should move to historical table and update main table
                SELECT count(*) into lv_same_row
                FROM BI_ODS.AGGREGATED_FUND_ORDERS afo
                WHERE AFO.aggregated_fund_order_id = p_aggregated_fund_order.aggregated_fund_order_id
                    AND ((NVL(AFO.VERSION_NUMBER,0)) < NVL(p_aggregated_fund_order.VERSION_NUMBER,0)
                        OR (NVL(AFO.VERSION_NUMBER,0) = NVL(p_aggregated_fund_order.VERSION_NUMBER,0)
                        and (
                        nrg_common.has_value_changed(afo.effective_start_timestamp,  p_aggregated_fund_order.publish_time)= 1 OR
                        nrg_common.has_value_changed(afo.process_id,           p_aggregated_fund_order.process_id)= 1 OR
                        nrg_common.has_value_changed(afo.direction,            p_aggregated_fund_order.direction)= 1 OR
                        nrg_common.has_value_changed(afo.amount,               p_aggregated_fund_order.amount)= 1 OR
                        nrg_common.has_value_changed(afo.instrument_currency,  p_aggregated_fund_order.instrument_currency)= 1 OR
                        nrg_common.has_value_changed(afo.quantity,             p_aggregated_fund_order.quantity)= 1 OR
                        nrg_common.has_value_changed(afo.instrument_code,      p_aggregated_fund_order.instrument_code)= 1 OR
                        nrg_common.has_value_changed(afo.isin,                 p_aggregated_fund_order.isin)= 1 OR
                        nrg_common.has_value_changed(afo.status_code,          p_aggregated_fund_order.status_code)= 1 OR
                        nrg_common.has_value_changed(afo.status_reason,        p_aggregated_fund_order.status_reason)= 1 OR
                        nrg_common.has_value_changed(afo.state,                p_aggregated_fund_order.state)= 1 OR
                        nrg_common.has_value_changed(afo.trade_date,           p_aggregated_fund_order.trade_date)= 1 OR
                        nrg_common.has_value_changed(afo.settlement_date,      p_aggregated_fund_order.settlement_date)= 1 OR
                        nrg_common.has_value_changed(afo.deal_price,           p_aggregated_fund_order.deal_price)= 1 OR
                        nrg_common.has_value_changed(afo.deal_price_currency,  p_aggregated_fund_order.deal_price_currency)= 1 OR
                        nrg_common.has_value_changed(afo.confirmed_quantity,   p_aggregated_fund_order.confirmed_quantity)= 1 OR
                        nrg_common.has_value_changed(afo.deal_amount,          p_aggregated_fund_order.deal_amount)= 1 OR
                        nrg_common.has_value_changed(afo.version_number,       p_aggregated_fund_order.version_number) = 1 OR
                        nrg_common.has_value_changed(afo.version_is_deleted,   p_aggregated_fund_order.version_is_deleted) = 1 OR
                        nrg_common.has_value_changed(afo.version_creation_time,p_aggregated_fund_order.version_creation_time) = 1 OR
                        nrg_common.has_value_changed(afo.version_update_time,  p_aggregated_fund_order.version_update_time) = 1 OR
                        nrg_common.has_value_changed(afo.version_creation_identity_token,   p_aggregated_fund_order.version_creation_identity_token) = 1 OR
                        nrg_common.has_value_changed(afo.version_creation_obo_identity_token,  p_aggregated_fund_order.version_creation_obo_identity_token) = 1 OR
                        nrg_common.has_value_changed(afo.version_updated_identity_token,  p_aggregated_fund_order.version_updated_identity_token) = 1 OR
                        nrg_common.has_value_changed(afo.version_obo_updated_identity_token,  p_aggregated_fund_order.version_obo_updated_identity_token) = 1)
                        )
                    );

                if(lv_same_row > 0) then
                    -- same row not existed
                    -- firstly, move the old data to historical table
                    insert into BI_ODS.AGGREGATED_FUND_ORDERS_H
                        select aggregated_fund_order_id, logical_load_timestamp,
                               CREATED_BY, CREATE_TIMESTAMP, UPDATED_BY, UPDATE_TIMESTAMP, EFFECTIVE_START_TIMESTAMP,
                               SYSDATE AS EFFECTIVE_END_TIMESTAMP, 'U' as ACTION, SYSDATE as ACTION_TIMESTAMP,
                               process_id, direction, business_date, reporting_date, amount, instrument_currency, quantity,
                               instrument_code,isin, status_code, status_reason, state, trade_date, settlement_date,
                               deal_price, deal_price_currency, confirmed_quantity, deal_amount, version_number, version_is_deleted,
                               version_creation_time, version_update_time, version_creation_identity_token, version_creation_obo_identity_token,
                               version_updated_identity_token,version_obo_updated_identity_token
                        from BI_ODS.AGGREGATED_FUND_ORDERS
                        where aggregated_fund_order_id = p_aggregated_fund_order.aggregated_fund_order_id;

                    -- secondly, update the current record
                    update BI_ODS.AGGREGATED_FUND_ORDERS set
                           logical_load_timestamp = lv_logical_load_timestamp,
                           updated_by = p_user,
                           update_timestamp = SYSDATE,
                           effective_start_timestamp = p_aggregated_fund_order.publish_time,
                           process_id = p_aggregated_fund_order.process_id,
                           direction = p_aggregated_fund_order.direction,
                           business_date = nrg_common.get_business_date(p_aggregated_fund_order.publish_time),
                           reporting_date = nrg_common.get_reporting_date(p_aggregated_fund_order.publish_time),
                           amount = p_aggregated_fund_order.amount,
                           instrument_currency = p_aggregated_fund_order.instrument_currency,
                           quantity = p_aggregated_fund_order.quantity,
                           instrument_code = p_aggregated_fund_order.instrument_code,
                           isin = p_aggregated_fund_order.isin,
                           status_code = p_aggregated_fund_order.status_code,
                           status_reason = p_aggregated_fund_order.status_reason,
                           state = p_aggregated_fund_order.state,
                           trade_date = p_aggregated_fund_order.trade_date,
                           settlement_date = p_aggregated_fund_order.settlement_date,
                           deal_price = p_aggregated_fund_order.deal_price,
                           deal_price_currency = p_aggregated_fund_order.deal_price_currency,
                           confirmed_quantity = p_aggregated_fund_order.confirmed_quantity,
                           deal_amount = p_aggregated_fund_order.deal_amount,
                           version_number = p_aggregated_fund_order.version_number,
                           version_is_deleted = p_aggregated_fund_order.version_is_deleted,
                           version_creation_time = p_aggregated_fund_order.version_creation_time,
                           version_update_time = p_aggregated_fund_order.version_update_time,
                           version_creation_identity_token = p_aggregated_fund_order.version_creation_identity_token,
                           version_creation_obo_identity_token = p_aggregated_fund_order.version_creation_obo_identity_token,
                           version_updated_identity_token = p_aggregated_fund_order.version_updated_identity_token,
                           version_obo_updated_identity_token= p_aggregated_fund_order.version_obo_updated_identity_token
                    where aggregated_fund_order_id = p_aggregated_fund_order.aggregated_fund_order_id;
                end if;
            else
                -- not existed, insert directly
                INSERT into BI_ODS.AGGREGATED_FUND_ORDERS
                 (aggregated_fund_order_id,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  process_id,
                  direction,
                  business_date,
                  reporting_date,
                  amount,
                  instrument_currency,
                  quantity,
                  instrument_code,
                  isin,
                  status_code,
                  status_reason,
                  state,
                  trade_date,
                  settlement_date,
                  deal_price,
                  deal_price_currency,
                  confirmed_quantity,
                  deal_amount,
                  version_number,
                  version_is_deleted,
                  version_creation_time,
                  version_update_time,
                  version_creation_identity_token,
                  version_creation_obo_identity_token,
                  version_updated_identity_token,
                  version_obo_updated_identity_token)
          VALUES (
                  p_aggregated_fund_order.aggregated_fund_order_id,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSDATE,
                  p_user,
                  SYSDATE,
                  p_aggregated_fund_order.publish_time,
                  p_aggregated_fund_order.process_id,
                  p_aggregated_fund_order.direction,
                  nrg_common.get_business_date(p_aggregated_fund_order.publish_time),
                  nrg_common.get_reporting_date(p_aggregated_fund_order.publish_time),
                  p_aggregated_fund_order.amount,
                  p_aggregated_fund_order.instrument_currency,
                  p_aggregated_fund_order.quantity,
                  p_aggregated_fund_order.instrument_code,
                  p_aggregated_fund_order.isin,
                  p_aggregated_fund_order.status_code,
                  p_aggregated_fund_order.status_reason,
                  p_aggregated_fund_order.state,
                  p_aggregated_fund_order.trade_date,
                  p_aggregated_fund_order.settlement_date,
                  p_aggregated_fund_order.deal_price,
                  p_aggregated_fund_order.deal_price_currency,
                  p_aggregated_fund_order.confirmed_quantity,
                  p_aggregated_fund_order.deal_amount,
                  p_aggregated_fund_order.version_number,
                  p_aggregated_fund_order.version_is_deleted,
                  p_aggregated_fund_order.version_creation_time,
                  p_aggregated_fund_order.version_update_time,
                  p_aggregated_fund_order.version_creation_identity_token,
                  p_aggregated_fund_order.version_creation_obo_identity_token,
                  p_aggregated_fund_order.version_updated_identity_token,
                  p_aggregated_fund_order.version_obo_updated_identity_token);
            end if;

            -- start to put order ids
            if (p_aggregated_fund_order.aggregated_fund_orders_ids is not null and p_aggregated_fund_order.aggregated_fund_orders_ids.count > 0)
            then
                put_aggregated_fund_orders_ids(p_aggregated_fund_order.aggregated_fund_orders_ids);
            end if;

            logger.logger.set_module(null);

        EXCEPTION WHEN OTHERS THEN
            logger.logger.SEVERE(logger.logger.error_backtrace);
            logger.logger.set_module(NULL);
            raise_application_error(-20004, logger.logger.error_backtrace);
        END put_aggregated_fund_order;

  -- ===================================================================================
  -- put_fund_cost_and_charges
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put fund_cost_and_charges
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADES
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
    PROCEDURE put_fund_cost_charges(p_user                           IN fund_cost_and_charges.created_by%TYPE,
                                    p_fund_cost_and_charges          IN fund_cost_and_charges_tab)
    IS
    
        lv_logical_load_timestamp fund_cost_and_charges.logical_load_timestamp%TYPE;
       -- lv_business_date fund_cost_and_charges.business_date%TYPE;
       -- lv_reporting_date fund_cost_and_charges.reporting_date%TYPE;
        
    BEGIN
    
        logger.logger.set_module('put_fund_cost_and_charges');
        lv_logical_load_timestamp := SYSTIMESTAMP;
        
    
    MERGE INTO fund_cost_and_charges_h old_version USING
        (SELECT old_version.*,
                afo.effective_start_timestamp AS new_effective_start_timestamp
         FROM fund_cost_and_charges old_version,
              TABLE(CAST(p_fund_cost_and_charges AS fund_cost_and_charges_tab)) afo
         WHERE old_version.net_asset_value_date = afo.net_asset_value_date AND
               old_version.instrument_code = afo.instrument_code AND
             (nrg_common.has_value_changed(old_version.transaction_fee_Actual,afo.transaction_fee_Actual)= 1 OR
              nrg_common.has_value_changed(old_version.transaction_fee_Actual_date,afo.transaction_fee_Actual_date)= 1 OR
              nrg_common.has_value_changed(old_version.performance_fee_Actual,afo.performance_fee_Actual)= 1 OR
              nrg_common.has_value_changed(old_version.performance_fee_Actual_date,afo.performance_fee_Actual_date)= 1 OR
              nrg_common.has_value_changed(old_version.ongoing_cost_Actual,afo.ongoing_cost_Actual)= 1 OR
              nrg_common.has_value_changed(old_version.ongoing_cost_Actual_date,afo.ongoing_cost_Actual_date)= 1 OR
              nrg_common.has_value_changed(old_version.net_asset_value,afo.net_asset_value)= 1 OR
              nrg_common.has_value_changed(old_version.net_asset_value_ccy,afo.net_asset_value_ccy)= 1)) new_version
        ON (old_version.net_asset_value_date      = new_version.net_asset_value_date
        AND old_version.instrument_code        	  = new_version.instrument_code        
        AND old_version.effective_start_timestamp = new_version.effective_start_timestamp)
        WHEN MATCHED THEN UPDATE SET
            old_version.updated_by                    = new_version.updated_by,
            old_version.action                        = 'U',
            old_version.action_timestamp              = SYSTIMESTAMP,
            old_version.update_timestamp              = new_version.update_timestamp,
            old_version.logical_load_timestamp        = new_version.logical_load_timestamp,
            old_version.transaction_fee_Actual        = new_version.transaction_fee_Actual,
            old_version.transaction_fee_Actual_date   = new_version.transaction_fee_Actual_date,
            old_version.performance_fee_Actual        = new_version.performance_fee_Actual,
            old_version.performance_fee_Actual_date   = new_version.performance_fee_Actual_date,
            old_version.ongoing_cost_Actual           = new_version.ongoing_cost_Actual,
            old_version.ongoing_cost_Actual_date      = new_version.ongoing_cost_Actual_date,
            old_version.net_asset_value               = new_version.net_asset_value,
            old_version.net_asset_value_ccy           = new_version.net_asset_value_ccy
        WHEN NOT MATCHED THEN INSERT
            (net_asset_value_date,
             instrument_code,
             logical_load_timestamp,
             created_by,
             create_timestamp,
             updated_by,
             update_timestamp,
             effective_start_timestamp,
             effective_end_timestamp,
             action,
             action_timestamp,
             business_date,
             reporting_date,
             transaction_fee_Actual,
             transaction_fee_Actual_date,
             performance_fee_Actual,
             performance_fee_Actual_date,
             ongoing_cost_Actual,
             ongoing_cost_Actual_date,
             net_asset_value,
             net_asset_value_ccy)
            VALUES (new_version.net_asset_value_date,
                    new_version.instrument_code,
                    new_version.logical_load_timestamp,
                    new_version.created_by,
                    new_version.create_timestamp,
                    new_version.updated_by,
                    new_version.update_timestamp,
                    new_version.effective_start_timestamp,
                    new_version.new_effective_start_timestamp,
                    'U',
                    SYSTIMESTAMP,
                    new_version.business_date,
                    new_version.reporting_date,
                    new_version.transaction_fee_Actual,
                    new_version.transaction_fee_Actual_date,
                    new_version.performance_fee_Actual,
                    new_version.performance_fee_Actual_date,
                    new_version.ongoing_cost_Actual,
                    new_version.ongoing_cost_Actual_date,
                    new_version.net_asset_value,
                    new_version.net_asset_value_ccy);
    
    MERGE INTO fund_cost_and_charges old_version USING
        (SELECT DISTINCT afo.net_asset_value_date,
                         afo.effective_start_timestamp,
                         afo.instrument_code,
                         nrg_common.get_business_date(afo.net_asset_value_date) AS business_date,
                         nrg_common.get_reporting_date(afo.net_asset_value_date) AS reporting_date,
                         afo.transaction_fee_Actual,
                         afo.transaction_fee_Actual_date,
                         afo.performance_fee_Actual,
                         afo.performance_fee_Actual_date,
                         afo.ongoing_cost_Actual,
                         afo.ongoing_cost_Actual_date,
                         afo.net_asset_value,
                         afo.net_asset_value_ccy
        FROM TABLE(CAST(p_fund_cost_and_charges AS fund_cost_and_charges_tab)) afo) new_version
        ON (old_version.net_asset_value_date          = new_version.net_asset_value_date
        AND old_version.instrument_code        		  = new_version.instrument_code)
        WHEN MATCHED THEN UPDATE SET
            old_version.updated_by                    = p_user,
            old_version.update_timestamp              = SYSTIMESTAMP,
            old_version.logical_load_timestamp        = lv_logical_load_timestamp,
            old_version.effective_start_timestamp     = new_version.effective_start_timestamp,
            old_version.transaction_fee_Actual        = nvl(new_version.transaction_fee_Actual,old_version.transaction_fee_Actual),
            old_version.transaction_fee_Actual_date   = nvl(new_version.transaction_fee_Actual_date,old_version.transaction_fee_Actual_date),
            old_version.performance_fee_Actual        = nvl(new_version.performance_fee_Actual,old_version.performance_fee_Actual),
            old_version.performance_fee_Actual_date   = nvl(new_version.performance_fee_Actual_date,old_version.performance_fee_Actual_date),
            old_version.ongoing_cost_Actual           = nvl(new_version.ongoing_cost_Actual,old_version.ongoing_cost_Actual),
            old_version.ongoing_cost_Actual_date      = nvl(new_version.ongoing_cost_Actual_date,old_version.ongoing_cost_Actual_date),
            old_version.net_asset_value               = nvl(new_version.net_asset_value,old_version.net_asset_value),
            old_version.net_asset_value_ccy           = nvl(new_version.net_asset_value_ccy,old_version.net_asset_value_ccy)
       WHERE (nrg_common.has_value_changed(old_version.transaction_fee_Actual,nvl(new_version.transaction_fee_Actual,old_version.transaction_fee_Actual))= 1 OR
              nrg_common.has_value_changed(old_version.transaction_fee_Actual_date,nvl(new_version.transaction_fee_Actual_date,old_version.transaction_fee_Actual_date))= 1 OR
              nrg_common.has_value_changed(old_version.performance_fee_Actual,nvl(new_version.performance_fee_Actual,old_version.performance_fee_Actual))= 1 OR
              nrg_common.has_value_changed(old_version.performance_fee_Actual_date,nvl(new_version.performance_fee_Actual_date,old_version.performance_fee_Actual_date))= 1 OR
              nrg_common.has_value_changed(old_version.ongoing_cost_Actual,nvl(new_version.ongoing_cost_Actual,old_version.ongoing_cost_Actual))= 1 OR
              nrg_common.has_value_changed(old_version.ongoing_cost_Actual_date,nvl(new_version.ongoing_cost_Actual_date,old_version.ongoing_cost_Actual_date))= 1 OR
              nrg_common.has_value_changed(old_version.net_asset_value,nvl(new_version.net_asset_value,old_version.net_asset_value))= 1 OR
              nrg_common.has_value_changed(old_version.net_asset_value_ccy,nvl(new_version.net_asset_value_ccy,old_version.net_asset_value_ccy))= 1)
             WHEN NOT MATCHED THEN INSERT
                 (net_asset_value_date,
                  instrument_code,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  business_date,
                  reporting_date,
                  transaction_fee_Actual,
                  transaction_fee_Actual_date,
                  performance_fee_Actual,
                  performance_fee_Actual_date,
                  ongoing_cost_Actual,
                  ongoing_cost_Actual_date,
                  net_asset_value,
                  net_asset_value_ccy)
          VALUES (new_version.net_asset_value_date,
                  new_version.instrument_code,
                  lv_logical_load_timestamp,
                  p_user,
                  SYSTIMESTAMP,
                  p_user,
                  SYSTIMESTAMP,
                  new_version.effective_start_timestamp,
                  new_version.business_date,
                  new_version.reporting_date,
                  new_version.transaction_fee_Actual,
                  new_version.transaction_fee_Actual_date,
                  new_version.performance_fee_Actual,
                  new_version.performance_fee_Actual_date,
                  new_version.ongoing_cost_Actual,
                  new_version.ongoing_cost_Actual_date,
                  new_version.net_asset_value,
                  new_version.net_asset_value_ccy);
    
    EXCEPTION WHEN OTHERS THEN
        logger.logger.SEVERE(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
    END;

  -- ===================================================================================
  -- put_aggregated_fund_orders_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put aggregated_fund_orders_ids
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_aggregated_fund_orders_ids(p_aggregated_fund_orders_ids IN AGGREGATED_FUND_ORDERS_IDS_TAB)
      IS
          TYPE type_del_order_ids IS TABLE OF AGGREGATED_FUND_ORDERS_IDS%ROWTYPE;
          table_del_order_ids type_del_order_ids;
          type_first_order_ids AGGREGATED_FUND_ORDERS_IDS_OBJ;
      BEGIN
          logger.logger.set_module('put_aggregated_fund_orders_ids');
          if (p_aggregated_fund_orders_ids is not null and p_aggregated_fund_orders_ids.count > 0)
          then
            type_first_order_ids := p_aggregated_fund_orders_ids(1);
            -- for the deleted records, delete the main table and insert into the h table with 'D'
            select old_arpa.*
            bulk collect into table_del_order_ids
            from AGGREGATED_FUND_ORDERS_IDS old_arpa
            left join TABLE(p_aggregated_fund_orders_ids) new_arpa
             on old_arpa.ORDER_ID  = new_arpa.ORDER_ID
             AND old_arpa.aggregated_fund_orders_id = new_arpa.aggregated_fund_orders_id
            where new_arpa.aggregated_fund_orders_id is NULL
            AND old_arpa.aggregated_fund_orders_id = type_first_order_ids.aggregated_fund_orders_id;

            FOR i IN 1..table_del_order_ids.COUNT LOOP
                put_fund_orders_ids_h(p_del_order_id => table_del_order_ids(i));
            END LOOP;

            for i in p_aggregated_fund_orders_ids.first .. p_aggregated_fund_orders_ids.last
            LOOP
               put_fund_orders_ids(p_aggregated_fund_orders_ids(i));
            end loop;
           end if;

          logger.logger.set_module(null);

      EXCEPTION WHEN OTHERS THEN
          logger.logger.SEVERE(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
      END put_aggregated_fund_orders_ids;

    -- ===================================================================================
    -- put_fund_orders_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put fund_orders_ids
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    -- -----------------------------------------------------------------------------------
  procedure put_fund_orders_ids(p_fund_order_id IN AGGREGATED_FUND_ORDERS_IDS_OBJ)
      is
          lv_row_exists number;
          lv_same_row number;
      begin
          logger.logger.set_module('p_fund_order_id');

            -- if it is a different record
          --   insert the existed record into historical table
          --   update the existed record including the timestamp
          --

          lv_row_exists:=0;
          lv_same_row:=0;

          select count(*) into lv_row_exists
          from BI_ODS.AGGREGATED_FUND_ORDERS_IDS
          where aggregated_fund_orders_id = p_fund_order_id.aggregated_fund_orders_id
         	AND order_id = p_fund_order_id.order_id;

          select count(*) into lv_same_row
          from BI_ODS.AGGREGATED_FUND_ORDERS_IDS
          where aggregated_fund_orders_id = p_fund_order_id.aggregated_fund_orders_id
          AND nrg_common.has_value_changed(order_id, p_fund_order_id.order_id) = 0
          and nrg_common.has_value_changed(updated_by, p_fund_order_id.updated_by) = 0;

          if(lv_row_exists > 0) then
              if(lv_same_row = 0) then
              -- same row not exsited
              -- firstly, move the old data to historical table
              insert into BI_ODS.AGGREGATED_FUND_ORDERS_IDS_H
                  select id, logical_load_timestamp, created_by,
                  create_timestamp, updated_by, update_timestamp,
                  effective_start_timestamp, aggregated_fund_orders_id, order_id,
                  'U' AS ACTION, SYSDATE AS ACTION_TIMESTAMP
              from BI_ODS.AGGREGATED_FUND_ORDERS_IDS
              where aggregated_fund_orders_id = p_fund_order_id.aggregated_fund_orders_id
              and order_id = p_fund_order_id.order_id;

              -- secondly, update the current record
              UPDATE BI_ODS.AGGREGATED_FUND_ORDERS_IDS SET
                  order_id = p_fund_order_id.order_id,
                  LOGICAL_LOAD_TIMESTAMP = p_fund_order_id.LOGICAL_LOAD_TIMESTAMP,
                  updated_by = p_fund_order_id.updated_by,
                  UPDATE_TIMESTAMP = SYSDATE,
                  EFFECTIVE_START_TIMESTAMP = SYSDATE
              where aggregated_fund_orders_id = p_fund_order_id.aggregated_fund_orders_id
              and order_id = p_fund_order_id.order_id;

              end if;
          else
              -- not existed, need to insert new data
              INSERT INTO BI_ODS.AGGREGATED_FUND_ORDERS_IDS (logical_load_timestamp,
              created_by, create_timestamp, updated_by, update_timestamp, effective_start_timestamp,
              aggregated_fund_orders_id, order_id)
              VALUES (
                  p_fund_order_id.logical_load_timestamp,
                  p_fund_order_id.created_by,
                  p_fund_order_id.create_timestamp,
                  p_fund_order_id.updated_by,
                  p_fund_order_id.update_timestamp,
                  p_fund_order_id.effective_start_timestamp,
                  p_fund_order_id.aggregated_fund_orders_id,
                  p_fund_order_id.order_id
              );
          end if;

          logger.logger.set_module(null);
      exception
          when others then
              logger.logger.severe(logger.logger.error_backtrace);
              logger.logger.set_module(null);
              raise_application_error(-20004, logger.logger.error_backtrace);
      end put_fund_orders_ids;

    -- ===================================================================================
    -- put_fund_orders_ids_h
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put fund_orders_ids
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    -- -----------------------------------------------------------------------------------
  procedure put_fund_orders_ids_h(p_del_order_id IN AGGREGATED_FUND_ORDERS_IDS%ROWTYPE)
      is

      begin
          logger.logger.set_module('PUT AGGREGATED_FUND_ORDERS_IDS_H');

          INSERT INTO BI_ODS.AGGREGATED_FUND_ORDERS_IDS_H (
          	id, logical_load_timestamp, created_by,
                  create_timestamp, updated_by, update_timestamp,
                  effective_start_timestamp, aggregated_fund_orders_id, order_id,
                  ACTION,ACTION_TIMESTAMP)
              VALUES (
                  p_del_order_id.id,
                  p_del_order_id.logical_load_timestamp,
                  p_del_order_id.created_by,
                  p_del_order_id.create_timestamp,
                  p_del_order_id.updated_by,
                  p_del_order_id.update_timestamp,
                  p_del_order_id.effective_start_timestamp,
                  p_del_order_id.aggregated_fund_orders_id,
                  p_del_order_id.order_id,
                  'D',
                  SYSDATE
              );

          delete from AGGREGATED_FUND_ORDERS_IDS
          where ID  = p_del_order_id.id;


          logger.logger.set_module(null);
      exception
          when others then
              logger.logger.severe(logger.logger.error_backtrace);
              logger.logger.set_module(null);
              raise_application_error(-20004, logger.logger.error_backtrace);
      end put_fund_orders_ids_h;
END NRG_FUND_TRADING;
/