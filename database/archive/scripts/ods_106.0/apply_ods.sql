-- ===================================================================================
-- apply_ods.sql
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Wrapper script to update BI_ODS schema
--
-- Notes:
-- ------
--
--     Oracle 10g/11g environments
--
-- Modifications:
-- --------------
--
--     Date         Modified By       Ref     Action
--     ----------   ---------------   -----   ----------------------------------------
--     21/09/2021   Patrick <PERSON>ddy  1.0     Creation
--
-- -----------------------------------------------------------------------------------

DEFINE application_name=ods
DEFINE application_version=106.0
DEFINE build_number=1

DEFINE home_path=/dba/releases
DEFINE src=&&home_path/&&application_name._&&application_version/rollforward
DEFINE logs=&&home_path/logs

SET LINESIZE 200
SET PAGESIZE 50

COLUMN start_time NEW_VALUE gv_start_time
COLUMN end_time   NEW_VALUE gv_end_time
COLUMN db_name    NEW_VALUE gv_db_name

SELECT TO_CHAR(SYSDATE,'ddmonyyhh24miss') AS start_time
FROM   dual;

SELECT SYS_CONTEXT('USERENV', 'DB_NAME') AS db_name
FROM   dual;

ACCEPT dba PROMPT "Please enter dba performing release: "
ACCEPT dba_password PROMPT "Please enter password for dba performing release: " HIDE

SPOOL &logs/RollForward_&build_number._&&application_name._&&application_version._&gv_db_name._&gv_start_time..log

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Get All Invalid Objects                                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

SELECT owner,
       object_type,
       SUBSTR(object_name, 1, 30) AS object_name,
       status
FROM   dba_objects
WHERE  status != 'VALID'
ORDER BY owner,
         object_type,
         object_name;

-- -----------------------------
-- START OF CUSTOMIZABLE SECTION
-- -----------------------------
DEFINE ods_username       		    = "bi_ods"

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ BI_ODS actions via ORADBA                                    		   +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba[&&ods_username]/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

ACCEPT gv_continue_y_n PROMPT "Hit enter if OK to continue or hit CTRL-D to exit script"

PROMPT *****************************
PROMPT CREATE TABLES
PROMPT *****************************
PROMPT

@"&src/ods/tables/bi_ods.sql"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE TYPES
PROMPT *****************************

PROMPT *****************************
PROMPT CREATE anytime_positions_obj
PROMPT *****************************
PROMPT

@"&src/ods/types/anytime_positions_obj.typ"
@"&src/ods/types/anytime_positions_tab.typ"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE positions_obj
PROMPT *****************************
PROMPT

@"&src/ods/types/position_obj.typ"
@"&src/ods/types/position_tab.typ"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE company_employee_obj
PROMPT *****************************
PROMPT

@"&src/ods/types/company_employee_obj.typ"
@"&src/ods/types/company_employee_tab.typ"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE customer_ddo_history_obj
PROMPT *****************************
PROMPT

@"&src/ods/types/customer_ddo_history_obj.typ"
@"&src/ods/types/customer_ddo_history_tab.typ"

SHOW ERRORS
PROMPT

PROMPT ******************************
PROMPT ADD BI_ODS TYPE GRANTS
PROMPT ******************************
PROMPT

@"&src/ods/privileges/bi_ods_type_grants.sql"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE PACKAGES
PROMPT *****************************

PROMPT *****************************
PROMPT CREATE PACKAGE nrg_customer
PROMPT *****************************

@"&src/ods/packages/nrg_customer.pks"
@"&src/ods/packages/nrg_customer.pkb"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE PACKAGE nrg_cash_account
PROMPT *****************************

@"&src/ods/packages/nrg_cash_account.pkb"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE PACKAGE nrg_position
PROMPT *****************************

@"&src/ods/packages/nrg_position.pkb"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE PACKAGE nrg_company
PROMPT *****************************

@"&src/ods/packages/nrg_company.pkb"

SHOW ERRORS
PROMPT

PROMPT *****************************
PROMPT CREATE PACKAGE nrg_identity
PROMPT *****************************

@"&src/ods/packages/nrg_identity.pks"
@"&src/ods/packages/nrg_identity.pkb"

SHOW ERRORS
PROMPT

PROMPT ******************************
PROMPT RECOMPILE PACKAGES
PROMPT ******************************
PROMPT

@"&src/ods/packages/recompile_packages.sql"

SHOW ERRORS
PROMPT

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Insert into RELEASE_LOG table as ORADBA                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

CONNECT &&dba/&&dba_password

SHOW USER

WHENEVER SQLERROR EXIT

ACCEPT gv_continue_y_n PROMPT "Hit enter if OK to continue or hit CTRL-D to exit script"

INSERT INTO release_log
(
    application_name,
    version_number,
    build_number,
    rollforward_date,
    rollforward_by
)
VALUES
(
    '&&application_name',
    '&&application_version',
    '&&build_number',
    CURRENT_TIMESTAMP,
    '&&dba'
);

COMMIT;

-- ---------------------------
-- END OF CUSTOMIZABLE SECTION
-- ---------------------------

PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++ Get All Invalid Objects                                               +++++++
PROMPT +++++                                                                       +++++++
PROMPT +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT owner,
       object_type,
       SUBSTR(object_name, 1, 30) AS object_name,
       status
FROM   dba_objects
WHERE  status != 'VALID'
ORDER BY owner,
         object_type,
         object_name;

SELECT TO_CHAR(SYSDATE,'ddmonyyhh24miss') AS end_time FROM dual;

SPOOL OFF