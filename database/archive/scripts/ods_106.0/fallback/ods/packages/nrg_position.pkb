create or replace PACKAGE BODY        nrg_position AS

  -- ===================================================================================
  -- NRG_POSITION
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of Positions
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     25/06/2020   Patrick Dinwiddy  1.0    Creation
  --     04/09/2020   Patrick Dinwiddy  1.1    JCS-13253
  --     08/03/2021   Patrick Dinwiddy  1.2    JCS-14420
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================
  gc_version           CONSTANT VARCHAR2(3) := '1.2';

  --
  --
  --
  -- ===================================================================================
  -- PUBLIC MODULES
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------
  FUNCTION version RETURN VARCHAR2 DETERMINISTIC IS
  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END version;

  -- ===================================================================================
  -- put_eod_position_snapshots
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This procedure maintains the helper table BI_ODS.EOD_POSITION_SNAPSHOTS
  --     which is used to aid loading of the TRADING_ACTIVITY_SUMMARY table in EDW.
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     EOD_POSITION_SNAPSHOTS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_business_date                  Business Date
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_eod_position_snapshots(p_snapshot_time  IN TIMESTAMP,
                                       p_platform       IN VARCHAR2,
                                       p_summary_type   IN VARCHAR2,
                                       p_is_processed   IN VARCHAR2,
                                       p_processed_time IN TIMESTAMP,
                                       p_record_source  IN eod_open_trades.record_source%TYPE,
                                       p_business_date  IN DATE,
                                       p_reporting_date IN DATE) IS

    lv_snapshot_exists NUMBER;

  BEGIN

    SELECT CASE
             WHEN EXISTS (SELECT 1
                            FROM bi_ods.eod_position_snapshots
                           WHERE snapshot_time = p_snapshot_time
                             AND platform      = p_platform
                             AND record_source = p_record_source) THEN
              1
             ELSE
              0
           END
      INTO lv_snapshot_exists
      FROM dual;

    IF lv_snapshot_exists = 1 THEN
      IF p_summary_type = 'TAS' THEN

        UPDATE bi_ods.eod_position_snapshots
           SET processed_time_tas = p_processed_time,
               is_processed_tas   = p_is_processed,
               business_date      = p_business_date,
               reporting_date     = p_reporting_date
         WHERE snapshot_time      = p_snapshot_time
           AND platform           = p_platform
           AND record_source      = p_record_source;

      ELSIF p_summary_type = '1MIN' THEN

        UPDATE bi_ods.eod_position_snapshots
           SET processed_time_1min = p_processed_time,
               is_processed_1min   = p_is_processed,
               business_date       = p_business_date,
               reporting_date      = p_reporting_date
         WHERE snapshot_time       = p_snapshot_time
           AND platform            = p_platform
           AND record_source       = p_record_source;

      ELSIF p_summary_type = 'EOD' THEN

        UPDATE bi_ods.eod_position_snapshots
           SET processed_time_tas  = p_processed_time,
               is_processed_tas    = p_is_processed,
               processed_time_1min = p_processed_time,
               is_processed_1min   = p_is_processed,
               business_date       = p_business_date,
               reporting_date      = p_reporting_date
         WHERE snapshot_time       = p_snapshot_time
           AND platform            = p_platform
           AND record_source       = p_record_source;

      END IF;

    ELSE

      INSERT INTO bi_ods.eod_position_snapshots
        (platform,
         snapshot_time,
         is_processed_tas,
         is_processed_1min,
         record_source,
         business_date,
         reporting_date)
      VALUES
        (p_platform,
         p_snapshot_time,
         NULL,
         'N',
         p_record_source,
         p_business_date,
         p_reporting_date);

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_eod_position_snapshots;

  -- ===================================================================================
  -- put_anytime_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     ANYTIME_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_anytime_position                    Anytime Position Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_anytime_position_set(p_user                      IN VARCHAR2,
                                     p_effective_start_timestamp IN TIMESTAMP,
                                     p_platform                  IN VARCHAR2,
                                     p_anytime_positions         IN anytime_positions_tab)
  IS

    lv_business_date anytime_positions.Business_Date%TYPE;
    lv_reporting_date anytime_positions.Reporting_Date%TYPE;

    TYPE lvtab_anytime_pos IS TABLE OF anytime_positions%rowtype;
    ltab_anytime_pos lvtab_anytime_pos;

  BEGIN

    logger.logger.set_module('nrg_position.put_anytime_position_set');

    lv_business_date := Nrg_Common.get_business_date(p_effective_start_timestamp);

    IF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(TO_CHAR(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

       /*
    --
      This is to select the positions into a structure in which they will be stored
      Once the data is ready it will be bulk inserted into the table
    --
    */

     SELECT p_platform,
            SYSTIMESTAMP ,
            p_user,
            SYSTIMESTAMP,
            P_USER,
            SYSTIMESTAMP,
            p_effective_start_timestamp,
            lv_business_date,
            lv_reporting_date,
            p_effective_start_timestamp,
            pos_tab.order_id,
            pos_tab.direction,
            pos_tab.quantity,
            pos_tab.amount,
            pos_tab.amount_currency,
            pos_tab.amnt_in_trdng_accnt_prmry_ccy,
            pos_tab.open_trade_id,
            pos_tab.open_trade_price,
            pos_tab.opening_trade_amount_fx_rate,
            pos_tab.opening_trade_app_to_units,
            pos_tab.open_time,
            pos_tab.quantity_designator,
            pos_tab.product_instrument_code,
            pos_tab.product_wrapper_code,
            pos_tab.product_point_multiplier,
            pos_tab.product_currency,
            pos_tab.trading_account_id,
            pos_tab.trading_account_type,
            pos_tab.trading_account_codifier,
            pos_tab.trading_account_function,
            pos_tab.trading_accnt_primary_ccy,
            pos_tab.is_automatically_rolled,
            pos_tab.rolled_open_trade_id,
            pos_tab.execution_type,
            pos_tab.trading_scope,
            pos_tab.binary_type,
            pos_tab.settle_time,
            pos_tab.strike_price,
            pos_tab.strike_price_additional,
            pos_tab.tenor,
            pos_tab.tenor_start_time,
            pos_tab.opening_trade_instrument_price,
            pos_tab.forced_margin_fx_rate,
            pos_tab.opn_accrd_trnvr_in_accnt_ccy,
            value_date,
            pos_tab.pair_currency,
            pos_tab.primary_currency,
            pos_tab.secondary_currency,
            pos_tab.primary_amount,
            pos_tab.secondary_amount,
            pos_tab.margin_percentage
       BULK COLLECT INTO ltab_anytime_pos
       FROM TABLE(CAST(p_anytime_positions AS anytime_positions_tab)) pos_tab;

    /*
    --
      This is to delete the data from the history incase the same snapshot is replayed more than 2 times
      Generally this is a very rare chance that a snapshot is re played more than twice

      This has been done because the effective start timestamp for a snapshot is the time for which it is requested
      For market maker there can only be one snapshot a day but for Next Gen there can be many more snapshots(24 a day if required)
    --
    */
    DELETE anytime_positions_h
     WHERE platform                  = p_platform
       AND effective_start_timestamp = p_effective_start_timestamp;

   INSERT INTO anytime_positions_h
           (platform
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,effective_end_timestamp
           ,action
           ,action_timestamp
           ,business_date
           ,reporting_date
           ,requested_snapshot_time
           ,order_id
           ,direction
           ,quantity
           ,amount
           ,amount_currency
           ,amnt_in_trdng_accnt_prmry_ccy
           ,open_trade_id
           ,open_trade_price
           ,opening_trade_amount_fx_rate
           ,opening_trade_app_to_units
           ,open_time
           ,quantity_designator
           ,product_instrument_code
           ,product_wrapper_code
           ,product_point_multiplier
           ,product_currency
           ,trading_account_id
           ,trading_account_type
           ,trading_account_codifier
           ,trading_account_function
           ,trading_accnt_primary_ccy
           ,is_automatically_rolled
           ,rolled_open_trade_id
           ,execution_type
           ,trading_scope
           ,binary_type
           ,settle_time
           ,strike_price
           ,strike_price_additional
           ,tenor
           ,tenor_start_time
           ,opening_trade_instrument_price
           ,forced_margin_fx_rate
           ,opn_accrd_trnvr_in_accnt_ccy
           ,value_date
           ,pair_currency
           ,primary_currency
           ,secondary_currency
           ,primary_amount
           ,secondary_amount
           ,margin_percentage)
     SELECT platform
           ,logical_load_timestamp
           ,created_by
           ,create_timestamp
           ,updated_by
           ,update_timestamp
           ,effective_start_timestamp
           ,p_effective_start_timestamp effective_end_timestamp
           ,'U'
           ,SYSTIMESTAMP action_timestamp
           ,business_date
           ,reporting_date
           ,requested_snapshot_time
           ,order_id
           ,direction
           ,quantity
           ,amount
           ,amount_currency
           ,amnt_in_trdng_accnt_prmry_ccy
           ,open_trade_id
           ,open_trade_price
           ,opening_trade_amount_fx_rate
           ,opening_trade_app_to_units
           ,open_time
           ,quantity_designator
           ,product_instrument_code
           ,product_wrapper_code
           ,product_point_multiplier
           ,product_currency
           ,trading_account_id
           ,trading_account_type
           ,trading_account_codifier
           ,trading_account_function
           ,trading_accnt_primary_ccy
           ,is_automatically_rolled
           ,rolled_open_trade_id
           ,execution_type
           ,trading_scope
           ,binary_type
           ,settle_time
           ,strike_price
           ,strike_price_additional
           ,tenor
           ,tenor_start_time
           ,opening_trade_instrument_price
           ,forced_margin_fx_rate
           ,opn_accrd_trnvr_in_accnt_ccy
           ,value_date
           ,pair_currency
           ,primary_currency
           ,secondary_currency
           ,primary_amount
           ,secondary_amount
           ,margin_percentage
     FROM  anytime_positions
     WHERE platform                  = p_platform AND
           effective_start_timestamp = p_effective_start_timestamp AND
           business_date             = lv_business_date;

    DELETE anytime_positions
     WHERE platform                  = p_platform
       AND effective_start_timestamp = p_effective_start_timestamp AND
           business_date             = lv_business_Date;

      FORALL i IN ltab_anytime_pos.FIRST..ltab_anytime_pos.LAST

        INSERT INTO /*+ APPEND_VALUES */ anytime_positions
        VALUES
        (ltab_anytime_pos(i).platform
        ,ltab_anytime_pos(i).logical_load_timestamp
        ,ltab_anytime_pos(i).created_by
        ,ltab_anytime_pos(i).create_timestamp
        ,ltab_anytime_pos(i).updated_by
        ,ltab_anytime_pos(i).update_timestamp
        ,ltab_anytime_pos(i).effective_start_timestamp
        ,ltab_anytime_pos(i).business_date
        ,ltab_anytime_pos(i).reporting_date
        ,ltab_anytime_pos(i).requested_snapshot_time
        ,ltab_anytime_pos(i).order_id
        ,ltab_anytime_pos(i).direction
        ,ltab_anytime_pos(i).quantity
        ,ltab_anytime_pos(i).amount
        ,ltab_anytime_pos(i).amount_currency
        ,ltab_anytime_pos(i).amnt_in_trdng_accnt_prmry_ccy
        ,ltab_anytime_pos(i).open_trade_id
        ,ltab_anytime_pos(i).open_trade_price
        ,ltab_anytime_pos(i).opening_trade_amount_fx_rate
        ,ltab_anytime_pos(i).opening_trade_app_to_units
        ,ltab_anytime_pos(i).open_time
        ,ltab_anytime_pos(i).quantity_designator
        ,ltab_anytime_pos(i).product_instrument_code
        ,ltab_anytime_pos(i).product_wrapper_code
        ,ltab_anytime_pos(i).product_point_multiplier
        ,ltab_anytime_pos(i).product_currency
        ,ltab_anytime_pos(i).trading_account_id
        ,ltab_anytime_pos(i).trading_account_type
        ,ltab_anytime_pos(i).trading_account_codifier
        ,ltab_anytime_pos(i).trading_account_function
        ,ltab_anytime_pos(i).trading_accnt_primary_ccy
        ,ltab_anytime_pos(i).is_automatically_rolled
        ,ltab_anytime_pos(i).rolled_open_trade_id
        ,ltab_anytime_pos(i).execution_type
        ,ltab_anytime_pos(i).trading_scope
        ,ltab_anytime_pos(i).binary_type
        ,ltab_anytime_pos(i).settle_time
        ,ltab_anytime_pos(i).strike_price
        ,ltab_anytime_pos(i).strike_price_additional
        ,ltab_anytime_pos(i).tenor
        ,ltab_anytime_pos(i).tenor_start_time
        ,ltab_anytime_pos(i).opening_trade_instrument_price
        ,ltab_anytime_pos(i).forced_margin_fx_rate
        ,ltab_anytime_pos(i).opn_accrd_trnvr_in_accnt_ccy
        ,ltab_anytime_pos(i).value_date
        ,ltab_anytime_pos(i).pair_currency
        ,ltab_anytime_pos(i).primary_currency
        ,ltab_anytime_pos(i).secondary_currency
        ,ltab_anytime_pos(i).primary_amount
        ,ltab_anytime_pos(i).secondary_amount
        ,ltab_anytime_pos(i).margin_percentage);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_anytime_position_set;

  -- ===================================================================================
  -- put_eod_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     LATEST_POSITIONS
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_business_date                       Business Date
  --      p_reporting_date                      Reporting Date
  --      p_position                            Position Details
  --      /***** Begin Modification for V3.7(a) - BER781 *****/
  --      p_record_source                       Record Source
  --      /***** End Modification for V3.7(a) *****/
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------
  PROCEDURE put_eod_position_set(p_user                      IN VARCHAR2,
                                 p_effective_start_timestamp IN TIMESTAMP,
                                 p_platform                  IN VARCHAR2,
                                 p_position                  IN position_tab,
                                 p_record_source             IN eod_open_trades.record_source%TYPE,
                                 p_batch_detail              IN VARCHAR2) IS

    TYPE lvtab_eod_pos IS TABLE OF eod_open_trades%ROWTYPE;
    ltab_eod_pos lvtab_eod_pos;
    lv_business_date           eod_open_trades.business_date%TYPE;
    lv_reporting_date          eod_open_trades.reporting_date%TYPE;

  BEGIN

    lv_business_date := trunc(p_effective_start_timestamp);
    IF TRIM(to_char(lv_business_date, 'DAY')) = 'SATURDAY' THEN
      lv_reporting_date := lv_business_date + 2;
    ELSIF TRIM(to_char(lv_business_date, 'DAY')) = 'SUNDAY' THEN
      lv_reporting_date := lv_business_date + 1;
    ELSE
      lv_reporting_date := lv_business_date;
    END IF;

    /*
      This is to select the positions into a structure in which they will be stored
      This is done so that all the calculations can be performed
      Once the data is ready it will be bulk inserted into the table
    */
    IF p_platform = 'NG' THEN

      SELECT p_platform,
             order_id,
             snapshot_time,
             systimestamp,
             p_user,
             systimestamp,
             p_user,
             systimestamp,
             p_effective_start_timestamp,
             lv_business_date,
             lv_reporting_date,
             mm_value_date,
             trading_account_code,
             trading_account_type,
             trading_account_function,
             trading_account_codifier,
             mm_account_id,
             product_instrument_code,
             product_wrapper_code,
             mm_instrument_id,
             product_generation,
             product_point_multiplier,
             product_currency,
             product_financing_rat_max,
             product_schema_code,
             custominfo_virt_portcode,
             direction,
             financing_ratio,
             quantity,
             quantity_currency,
             margin,
             margin_currency,
             amount,
             amount_currency,
             margin_secondary,
             margin_secondary_ccy,
             margin_in_trad_acnt_prim_ccy,
             amount_in_trad_acnt_prim_ccy,
             open_trade_id,
             open_trade_price,
             open_trade_margin_fx_rate,
             open_time,
             last_modified_time,
             quantity_designator,
             trading_accnt_primary_ccy,
             opentrade_quantity_fx_rate,
             price_designator,
             is_inst_ccy_in_fracnal_parts,
             fractional_part_ratio,
             normalised_order_type,
             normalised_open_price,
             normalised_direction_mulplr,
             normalised_open_qty_trad_ccy,
             normalised_open_val_trad_ccy,
             normalised_trading_ccy,
             normalised_margin_type,
             normalised_margin_reqrment,
             mm_backoffice_ref,
             is_primary,
             eod_price,
             counterparty_id,
             opening_trade_amount_fx_rate,
             opening_trade_app_to_units,
             is_automatically_rolled,
             rolled_open_trade_id,
             eod_value,
             eod_value_ccy,
             eod_value_in_accnt_ccy,
             unrealised_pnl,
             unrealised_pnl_ccy,
             unrealised_pnl_in_accnt_ccy,
             cnvrsn_rate_to_accnt_ccy,
             is_mm_old_style,
             p_record_source,
             hedge_asset_class,
             hedge_instrument_code_external,
             hedge_expiry_month_code,
             hedge_risk_bucket,
             hedge_profitloss,
             hedge_commission,
             execution_type,
             NULL                           AS cd_state,
             trading_scope,
             binary_type,
             settle_time,
             tenor,
             strike_price,
             strike_price_additional,
             tenor_start_time,
             open_trade_instrument_price,
             NULL                           AS hedge_execution_commission,
             NULL                           AS price_band,
             hedge_opening_reference,
             hdg_redemption_date,
             hdg_current_coupon_date,
             hdg_accrued_interest_days,
             hdg_accrued_interest_amount,
             hdg_eval_accrued_intrst_days,
             hdg_eval_accrued_intrst_amnt,
             hdg_effctv_intrst_rate,
             hdg_effctv_intrst_base_amnt,
             hdg_afs_reserve_amount,
             hdg_quantity_settlement_date,
             hdg_new_effctv_intrst_bse_amt,
             hdg_new_afs_reserve_amount,
             hdg_was_manually_corrected,
             hdg_crrnt_coupon_pymnt_date,
             hdg_redemption_payment_date,
             forced_margin_fx_rate,
             trading_accnt_primary_ccy,
             opn_accrd_trnvr_in_accnt_ccy,
             NULL,
             NULL,
             NULL,
             NULL,
             value_date,
             pair_currency,
             primary_currency,
             secondary_currency,
             primary_amount,
             secondary_amount,
             NULL,
             margin_percentage
        BULK COLLECT
        INTO ltab_eod_pos
        FROM TABLE(CAST(p_position AS position_tab)) pos_tab;

    END IF;

    /*
    --
      This is to delete the data from the history incase the same snapshot is replayed more than 2 times
      Generally this is a very rare chance that a snapshot is re played more than twice

      This has been done because the effective start timestamp for a snapshot is the time for which it is requested
      For market maker there can only be one snapshot a day but for Next Gen there can be many more snapshots(24 a day if required)
    --
    */

    IF p_batch_detail = 'FIRST' THEN

      DELETE eod_open_trades_h
       WHERE platform = p_platform
         AND record_source = p_record_source
         AND effective_start_timestamp = p_effective_start_timestamp;

      /*
      --
        Insert the old snapshot copy into history table
      --
      */
      INSERT INTO eod_open_trades_h
              (platform,
               order_id,
               snapshot_time,
               logical_load_timestamp,
               created_by,
               create_timestamp,
               updated_by,
               update_timestamp,
               effective_start_timestamp,
               effective_end_timestamp,
               action,
               action_timestamp,
               business_date,
               reporting_date,
               mm_value_date,
               trading_account_id,
               trading_account_type,
               trading_account_function,
               trading_account_codifier,
               mm_account_id,
               product_instrument_code,
               product_wrapper_code,
               mm_instrument_id,
               product_generation,
               product_point_multiplier,
               product_currency,
               product_financing_ratio_max,
               product_schema_code,
               custom_info_virt_portcode,
               direction,
               financing_ratio,
               quantity,
               quantity_currency,
               margin,
               margin_currency,
               amount,
               amount_currency,
               margin_secondary,
               margin_secondary_ccy,
               mrgn_in_trdng_accnt_prmry_ccy,
               amnt_in_trdng_accnt_prmry_ccy,
               open_trade_id,
               open_trade_price,
               open_trade_margin_fx_rate,
               open_time,
               last_modified_time,
               quantity_designator,
               trading_accnt_primary_ccy,
               open_trade_quantity_fx_rate,
               price_designator,
               is_inst_ccy_in_fracnal_parts,
               fractional_part_ratio,
               normalised_order_type,
               normalised_open_price,
               normalised_direction_mulplr,
               normalised_open_qty_trad_ccy,
               normalised_open_val_trad_ccy,
               normalised_trading_ccy,
               normalised_margin_type,
               normalised_margin_rqrmnt,
               mm_backoffice_ref,
               is_primary,
               eod_price,
               counterparty_id,
               opening_trade_amount_fx_rate,
               opening_trade_app_to_units,
               is_automatically_rolled,
               rolled_open_trade_id,
               eod_value,
               eod_value_ccy,
               eod_value_in_accnt_ccy,
               unrealised_pnl,
               unrealised_pnl_ccy,
               unrealised_pnl_in_accnt_ccy,
               cnvrsn_rate_to_accnt_ccy,
               is_mm_old_style,
               record_source,
               hedge_asset_class,
               hedge_instrument_code_external,
               hedge_expiry_month_code,
               hedge_risk_bucket,
               hedge_profitloss,
               hedge_commission,
               execution_type,
               cd_state,
               trading_scope,
               binary_type,
               settle_time,
               tenor,
               strike_price,
               strike_price_additional,
               tenor_start_time,
               open_trade_instrument_price,
               hedge_execution_commission,
               price_band,
               hedge_opening_reference,
               hdg_redemption_date,
               hdg_current_coupon_date,
               hdg_accrued_interest_days,
               hdg_accrued_interest_amount,
               hdg_eval_accrued_intrst_days,
               hdg_eval_accrued_intrst_amnt,
               hdg_effctv_intrst_rate,
               hdg_effctv_intrst_base_amnt,
               hdg_afs_reserve_amount,
               hdg_quantity_settlement_date,
               hdg_new_effctv_intrst_bse_amt,
               hdg_new_afs_reserve_amount,
               hdg_was_manually_corrected,
               hdg_crrnt_coupon_pymnt_date,
               hdg_redemption_payment_date,
               forced_margin_fx_rate,
               trading_account_currency,
               opn_accrd_trnvr_in_accnt_ccy,
               value_date,
               pair_currency,
               primary_currency,
               secondary_currency,
               primary_amount,
               secondary_amount,
               margin_percentage)
        SELECT platform,
               order_id,
               snapshot_time,
               logical_load_timestamp,
               created_by,
               create_timestamp,
               updated_by,
               update_timestamp,
               effective_start_timestamp,
               p_effective_start_timestamp effective_end_timestamp,
               'U' action,
               systimestamp action_timestamp,
               business_date,
               reporting_date,
               mm_value_date,
               trading_account_id,
               trading_account_type,
               trading_account_function,
               trading_account_codifier,
               mm_account_id,
               product_instrument_code,
               product_wrapper_code,
               mm_instrument_id,
               product_generation,
               product_point_multiplier,
               product_currency,
               product_financing_ratio_max,
               product_schema_code,
               custom_info_virt_portcode,
               direction,
               financing_ratio,
               quantity,
               quantity_currency,
               margin,
               margin_currency,
               amount,
               amount_currency,
               margin_secondary,
               margin_secondary_ccy,
               mrgn_in_trdng_accnt_prmry_ccy,
               amnt_in_trdng_accnt_prmry_ccy,
               open_trade_id,
               open_trade_price,
               open_trade_margin_fx_rate,
               open_time,
               last_modified_time,
               quantity_designator,
               trading_accnt_primary_ccy,
               open_trade_quantity_fx_rate,
               price_designator,
               is_inst_ccy_in_fracnal_parts,
               fractional_part_ratio,
               normalised_order_type,
               normalised_open_price,
               normalised_direction_mulplr,
               normalised_open_qty_trad_ccy,
               normalised_open_val_trad_ccy,
               normalised_trading_ccy,
               normalised_margin_type,
               normalised_margin_rqrmnt,
               mm_backoffice_ref,
               is_primary,
               eod_price,
               counterparty_id,
               opening_trade_amount_fx_rate,
               opening_trade_app_to_units,
               is_automatically_rolled,
               rolled_open_trade_id,
               eod_value,
               eod_value_ccy,
               eod_value_in_accnt_ccy,
               unrealised_pnl,
               unrealised_pnl_ccy,
               unrealised_pnl_in_accnt_ccy,
               cnvrsn_rate_to_accnt_ccy,
               is_mm_old_style,
               record_source,
               hedge_asset_class,
               hedge_instrument_code_external,
               hedge_expiry_month_code,
               hedge_risk_bucket,
               hedge_profitloss,
               hedge_commission,
               execution_type,
               cd_state,
               trading_scope,
               binary_type,
               settle_time,
               tenor,
               strike_price,
               strike_price_additional,
               tenor_start_time,
               open_trade_instrument_price,
               hedge_execution_commission,
               price_band,
               hedge_opening_reference,
               hdg_redemption_date,
               hdg_current_coupon_date,
               hdg_accrued_interest_days,
               hdg_accrued_interest_amount,
               hdg_eval_accrued_intrst_days,
               hdg_eval_accrued_intrst_amnt,
               hdg_effctv_intrst_rate,
               hdg_effctv_intrst_base_amnt,
               hdg_afs_reserve_amount,
               hdg_quantity_settlement_date,
               hdg_new_effctv_intrst_bse_amt,
               hdg_new_afs_reserve_amount,
               hdg_was_manually_corrected,
               hdg_crrnt_coupon_pymnt_date,
               hdg_redemption_payment_date,
               forced_margin_fx_rate,
               trading_account_currency,
               opn_accrd_trnvr_in_accnt_ccy,
               value_date,
               pair_currency,
               primary_currency,
               secondary_currency,
               primary_amount,
               secondary_amount,
               margin_percentage
          FROM eod_open_trades
         WHERE platform = p_platform
           AND record_source = p_record_source
           AND effective_start_timestamp = p_effective_start_timestamp
           AND business_date = lv_business_date;

    END IF;

    IF p_batch_detail = 'FIRST' THEN

      DELETE eod_open_trades
       WHERE platform = p_platform
         AND record_source = p_record_source
         AND effective_start_timestamp = p_effective_start_timestamp
         AND business_date = lv_business_date;

    END IF;

    IF p_platform = 'NG' AND p_record_source='NG-HEDGE-FX' THEN

        FOR lv_count in 1.. ltab_eod_pos.count LOOP

          ltab_eod_pos(lv_count).order_id := 'NG-HEDGE-FX-'||lv_count;

        END LOOP;

    END IF;
    /*
      Now bulk insert all the values to the eod positions table
    */

    FORALL lv_count IN 1 .. ltab_eod_pos.count
      INSERT INTO eod_open_trades VALUES ltab_eod_pos (lv_count);

    /*
    --Clean up history snapshot for managed order info
    */

    IF p_batch_detail = 'LAST' THEN
      put_eod_position_snapshots(p_snapshot_time  => p_effective_start_timestamp,
                                 p_platform       => p_platform,
                                 p_summary_type   => 'EOD',
                                 p_is_processed   => NULL,
                                 p_processed_time => NULL,
                                 p_record_source  => p_record_source,
                                 p_business_date  => lv_business_date,
                                 p_reporting_date => lv_reporting_date);

    END IF;

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

END nrg_position;
/