
  CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."COMPANY_EMPLOYEE_OBJ" FORCE IS OBJECT
(
    person            person_obj,
    employee_version  NUMBER,
    job_title         NVARCHAR2(100),
    company_id        NUMBER,
    is_deleted        VARCHAR2(3),
    employee_id       NUMBER,
    source_employee_id  VARCHAR2(100),
    is_communication_group_member VARCHAR2(3)
);

//

  CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."COMPANY_EMPLOYEE_TAB" FORCE IS TABLE OF bi_ods.company_employee_obj;

//
