CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."EXTERNAL_RATES_OBJ" force IS OBJECT
(
    rate_type	                    VARCHAR2(50),
    currency	                    VARCHAR2(3),
    pair_currency	                VARCHAR2(3),
    external_code	                VARCHAR2(100),
    rate_timestamp                  TIMESTAMP(6),
    bid_price	                    NUMBER,
    mid_price	                    NUMBER,
    ask_price	                    NUMBER,
    external_id	                    VARCHAR2(100),
    is_manual	                    VARCHAR2(3)
 )

//

CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."EXTERNAL_RATES_TAB" force is table of external_rates_obj

//

GRANT EXECUTE ON "BI_ODS"."EXTERNAL_RATES_OBJ" TO bi_nrg
//
GRANT EXECUTE ON "BI_ODS"."EXTERNAL_RATES_TAB" TO bi_nrg
//

--//@UNDO
DROP TYPE "BI_ODS"."EXTERNAL_RATES_TAB" FORCE
//
DROP TYPE "BI_ODS"."EXTERNAL_RATES_OBJ" FORCE
//