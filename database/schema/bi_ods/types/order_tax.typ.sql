CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."ORDER_TAX_OBJ" FORCE IS OBJECT
  ( order_tax_type                 VARCHAR2(50),
    tax_currency                   VARCHAR2(50),
    amount_in_instrument_ccy       NUMBER,
    amount_in_account_ccy          NUMBER,
    amount_in_tax_ccy              NUMBER,
    fx_commission_in_account_ccy   NUMBER
    );

//

CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."ORDER_TAX_TAB" FORCE IS TABLE OF "BI_ODS"."ORDER_TAX_OBJ";
//

GRANT EXECUTE ON "BI_ODS"."ORDER_TAX_OBJ" TO bi_nrg
//

GRANT EXECUTE ON "BI_ODS"."ORDER_TAX_TAB" TO bi_nrg
//

--//@UNDO

DROP TYPE "BI_ODS"."ORDER_TAX_TAB" FORCE
//

DROP TYPE "BI_ODS"."ORDER_TAX_OBJ" FORCE
//
