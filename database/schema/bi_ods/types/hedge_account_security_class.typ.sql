-- Security Class Objects
CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."HEDGE_ACCOUNT_SECURITY_CLASS_OBJ" FORCE IS OBJECT
(
    trading_account_id        NUMBER,
    trading_account_type      VARCHAR2(20),
    security_class            NVARCHAR2(15),
    is_deleted                VARCHAR2(3)
);
//

CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."HEDGE_ACCOUNT_SECURITY_CLASS_TAB"
    FORCE IS TABLE OF hedge_account_security_class_obj;
//

GRANT EXECUTE ON "BI_ODS"."HEDGE_ACCOUNT_SECURITY_CLASS_OBJ" TO bi_nrg
//

GRANT EXECUTE ON "BI_ODS"."HEDGE_ACCOUNT_SECURITY_CLASS_TAB" TO bi_nrg
//

--//@UNDO

DROP TYPE "BI_ODS"."HEDGE_ACCOUNT_SECURITY_CLASS_TAB" FORCE
//

DROP TYPE "BI_ODS"."HEDGE_ACCOUNT_SECURITY_CLASS_OBJ" FORCE
//