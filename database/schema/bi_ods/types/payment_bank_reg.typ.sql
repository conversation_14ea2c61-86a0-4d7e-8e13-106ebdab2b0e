CREATE OR REPLACE EDITIONABLE TYPE "BI_ODS"."PAYMENT_BANK_REG_OBJ" FORCE IS OBJECT (
bank_registration_id       NUMBER,
account_number             NVARCHAR2(50),
iban                       VARCHAR2(50),
swift_code                 VARCHAR2(50),
account_country            VARCHAR2(3),
is_verified                VARCHAR2(3),
is_deleted                 VARCHAR2(3),
trading_account_id         NUMBER,
bank_branch_code           NVARCHAR2(50),
bank_name                  NVARCHAR2(200),
bank_description           NVARCHAR2(500),
account_holder_name        VARCHAR2(100),
account_currency           VARCHAR2(3),
creation_date              TIMESTAMP(6),
deletion_date              TIMESTAMP(6),
notes                      NVARCHAR2(250),
bank_branch                NVARCHAR2(500),
bank_province              NVARCHAR2(500),
bank_city                  NVARCHAR2(500),
has_withdrawn              VARCHAR2(3),
match_score                NUMBER,
person_id                  NUMBER,
intermediary_bic           VARCHAR2(50),
creation_channel           NVARCHAR2(500),
channel_last_update_time   timestamp(6),
last_updated_channel       NVARCHAR2(500),
deletion_reason            NVARCHAR2(500)
)


//
