
  CREATE OR REPLACE EDITIONABLE PACKAGE BODY "BI_ODS"."NRG_GOOGLE_ANALYTICS" 
AS
  -- ==========================================================================
  -- =========
  -- NRG_GOOGLE_ANALYTICS
  -- ==========================================================================
  -- =========
  --
  -- Synopsis:
  -- ---------
  --
  --     This package writes down google analytics data for marketing team
  --
  --
  --
  -----------------------------------------------------------------------------------
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------
  ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --   ----------   ---------------   -----
  ----------------------------------------
  --     11/12/2012   Sanket Mittal    1.0     Creation
  --     01/12/2014   Adam Krasnicki   1.1     BER-1528 - 4000 characters limit
  -- for tables columns: referral_path, second_page_path, page_path,
  -- exit_page_path
  --     19/08/2015   Adam Krasnicki   1.2     New 8 tables and 8 new
  -- parameters in put procedure
  --
  --    21/09/2015    Sanket Mittal    1.3     New tables included
  --    12/10/2016    Sanket Mittal    1.4     BER-2798 Remove PageViews
  --    17/10/2016    Sanket Mittal    1.5     Undone changes for BER-2798 and addd changes for BER-2985
  --    29/11/2016    Sanket Mittal    1.6     BER-3149 ODS - stop populating some GA tables
  --    23/02/2017    Patrick Dinwiddy 1.7     BER-3424 new attribute for GA_ADWORDS
  --    31/05/2019    Patrick Dinwiddy 1.8     JCS-10777 new attribs for app events
  --
  -- ==========================================================================
  -- =========
  -- ==========================================================================
  -- =========
  -- PACKAGE CONSTANTS
  -- =========
  gc_version           constant VARCHAR2(3) := '1.8';
  gc_cr                constant VARCHAR2(1) := chr(10);
  gc_true              constant pls_integer := 1;
  gc_false             constant pls_integer := 0;
  gc_default_timestamp constant TIMESTAMP   := to_timestamp('01-Jan-1970','DD-Mon-YYYY');
  -- ==========================================================================
  -- =========
  -- version
  -- ==========================================================================
  -- =========
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------
  ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------
  ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  --
  -----------------------------------------------------------------------------------
  FUNCTION VERSION
    RETURN VARCHAR2 deterministic
  IS
  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  exception
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise;
  END VERSION;
  -- ============================================================================
  -- =======
  -- put_google_analytics
  -- ============================================================================
  -- =======
  --
  -- Synopsis:
  -- ---------
  --
  --     Writes the google analytics data to ods
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------
  ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  --
  -----------------------------------------------------------------------------------
  PROCEDURE put_google_analytics(
      p_user                       IN VARCHAR2,
      p_ga_date                    IN DATE,
      p_profiles                   IN ga_profile_tab,
      --p_pagepath_by_source         IN ga_pagepath_by_source_tab,
      --p_referral_by_source         IN ga_referral_by_source_tab,
      --p_visits_by_trx              IN ga_visits_by_trx_tab,
      --p_cost_by_source             IN ga_cost_by_source_tab,
      --p_ga_visits_goals_by_loc_1   IN ga_visits_goals_by_loc_1_tab,
      --p_ga_visits_goals_by_loc_2   IN ga_visits_goals_by_loc_2_tab,
      --p_ga_visits_goals_by_loc_3   IN ga_visits_goals_by_loc_3_tab,
      --p_ga_goal_by_source_1        IN ga_goal_by_source_1_tab,
      --p_ga_goal_by_source_2        IN ga_goal_by_source_2_tab,
      --p_visits_by_mobile_trx_1     IN ga_visits_by_mobile_trx_1_tab,
      --p_visits_by_mobile_trx_2     IN ga_visits_by_mobile_trx_2_tab,
      --p_ga_visit_gal_by_mbl_src_1  IN ga_visit_gal_by_mbl_src_1_tab,
      --p_ga_visit_gal_by_mbl_src_2  IN ga_visit_gal_by_mbl_src_2_tab,
      --p_ga_visit_gal_by_mbl_src_3  IN ga_visit_gal_by_mbl_src_3_tab,
      --p_ga_visit_gal_by_mbl_src_4  IN ga_visit_gal_by_mbl_src_4_tab,
      --p_ga_visit_gal_by_mbl_src_5  IN ga_visit_gal_by_mbl_src_5_tab,
      --p_ga_visit_gal_by_mbl_src_6  IN ga_visit_gal_by_mbl_src_6_tab,
      --p_ga_vsts_gls_by_optng_sys_1 IN ga_vsts_gls_by_optng_sys_1_tab,
      --p_ga_vsts_gls_by_optng_sys_2 IN ga_vsts_gls_by_optng_sys_2_tab,
      --p_ga_vsts_gls_by_optng_sys_3 IN ga_vsts_gls_by_optng_sys_3_tab,
      --p_ga_vsts_gls_by_optng_sys_4 IN ga_vsts_gls_by_optng_sys_4_tab,
      --p_ga_vsts_gls_by_optng_sys_5 IN ga_vsts_gls_by_optng_sys_5_tab,
      --p_ga_vsts_gls_by_optng_sys_6 IN ga_vsts_gls_by_optng_sys_6_tab,
      --p_by_mobile                  IN ga_by_mobile_tab,
      --p_type_visitor_by_source     IN ga_type_visitor_by_source_tab,
      --p_pdtsku_by_trx              IN ga_pdtsku_by_trx_tab,
      --p_ga_agg_channel_goals_st    IN ga_agg_channel_goals_st_tab,
      --p_ga_agg_channel_goals_cmp   IN ga_agg_channel_goals_cmp_tab,
      --p_ga_agg_location_goals_st   IN ga_agg_location_goals_st_tab,
      --p_ga_agg_location_goals_cmp  IN ga_agg_location_goals_cmp_tab,
      p_ga_agg_channel_tracking    IN ga_agg_channel_tracking_tab,
      p_ga_agg_location_tracking   IN ga_agg_location_tracking_tab,
      p_ga_app_live_tracking       IN ga_app_live_tracking_tab,
      p_ga_app_demo_tracking       IN ga_app_demo_tracking_tab,
      p_ga_agg_chnnl_lv_gls_str    IN ga_agg_chnnl_lv_gls_str_tab,
      p_ga_agg_chnnl_lv_gls_cmp    IN ga_agg_chnnl_lv_gls_cmp_tab,
      p_ga_agg_lctn_dm_gls_str     IN ga_agg_lctn_dm_gls_str_tab,
      p_ga_agg_lctn_dm_gls_cmp     IN ga_agg_lctn_dm_gls_cmp_tab,
      p_ga_agg_lctn_lv_gls_str     IN ga_agg_lctn_lv_gls_str_tab,
      p_ga_agg_lctn_lv_gls_cmp     IN ga_agg_lctn_lv_gls_cmp_tab,
      p_ga_agg_chnnl_dm_gl_str     IN ga_agg_chnnl_dm_gl_str_tab,
      p_ga_agg_chnnl_dm_gls_cmp    IN ga_agg_chnnl_dm_gls_cmp_tab
      )
  IS
    lv_logical_load_timestamp TIMESTAMP(6);
  /*TYPE lt_ga_pagepath_by_source_rec
  IS
    record
    (
      ga_date           DATE,
      SOURCE            VARCHAR2(500 CHAR),
      medium            VARCHAR2(500 CHAR),
      campaign          VARCHAR2(1500 CHAR),
      page_path         VARCHAR2(32767 CHAR),
      keyword           VARCHAR2(4000 CHAR),
      exit_page_path    VARCHAR2(32767 CHAR),
      profile_id        VARCHAR2(1000 CHAR),
      unique_page_views NUMBER,
      bounces           NUMBER,
      entrances         NUMBER,
      visits            NUMBER,
      time_on_page      NUMBER,
      new_visits        NUMBER,
      page_views        NUMBER );*/
  /*TYPE lt_ga_pagepath_by_source_tab
  IS
    TABLE OF lt_ga_pagepath_by_source_rec INDEX BY pls_integer;
    lv_ga_pagepath_by_source_tab lt_ga_pagepath_by_source_tab;*/
  /*TYPE lt_ga_referral_by_source_rec
  IS
    record
    (
      ga_date          DATE,
      SOURCE           VARCHAR2(500 CHAR),
      medium           VARCHAR2(500 CHAR),
      campaign         VARCHAR2(1500 CHAR),
      keyword          VARCHAR2(4000 CHAR),
      referral_path    VARCHAR2(32767 CHAR),
      second_page_path VARCHAR2(32767 CHAR),
      profile_id       VARCHAR2(1000 CHAR),
      transactions     NUMBER,
      time_on_site     NUMBER,
      bounces          NUMBER,
      entrances        NUMBER,
      visits           NUMBER,
      new_visits       NUMBER,
      page_views       NUMBER,
      time_on_page     NUMBER );
  TYPE lt_ga_referral_by_source_tab
  IS
    TABLE OF lt_ga_referral_by_source_rec INDEX BY pls_integer;
    lv_ga_referral_by_source_tab lt_ga_referral_by_source_tab;*/
    /*
    l_page_path_substr VARCHAR2(32767 CHAR);
    l_exit_page_path_substr VARCHAR2(32767 CHAR);*/
  BEGIN
    logger.logger.set_module('put_google_analytics');
    lv_logical_load_timestamp := SYSTIMESTAMP;

    --DELETE ga_by_mobile WHERE ga_date = p_ga_date;
    --DELETE ga_cost_by_source WHERE ga_date = p_ga_date;
    --DELETE ga_goal_by_source WHERE ga_date = p_ga_date;
    --DELETE ga_pagepath_by_source WHERE ga_date = p_ga_date;
    --DELETE ga_pdtsku_by_trx WHERE ga_date = p_ga_date;
    --DELETE ga_referral_by_source WHERE ga_date = p_ga_date;
    --DELETE ga_type_visitor_by_source WHERE ga_date = p_ga_date;
    --DELETE ga_visits_by_mobile_trx WHERE ga_date = p_ga_date;
    --DELETE ga_visits_by_trx WHERE ga_date = p_ga_date;
    --DELETE ga_visits_gls_by_oprtng_systm WHERE ga_date = p_ga_date;
    --DELETE ga_visits_goals_by_location WHERE ga_date = p_ga_date;
    --DELETE ga_visits_goal_by_mobile_src WHERE ga_date = p_ga_date;
    --DELETE GA_AGG_CHANNEL_GOALS_STARTS WHERE ga_date = trunc(p_ga_date);
    --DELETE GA_AGG_CHANNEL_GOALS_COMPLETS WHERE ga_date = trunc(p_ga_date);
    --DELETE GA_AGG_LOCATION_GOALS_STARTS WHERE ga_date = trunc(p_ga_date);
    --DELETE GA_AGG_LOCATION_GOALS_COMPLETS WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_CHANNEL_TRACKING WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_LOCATION_TRACKING WHERE ga_date = trunc(p_ga_date);
    DELETE GA_APP_LIVE_TRACKING WHERE ga_date = trunc(p_ga_date);
    DELETE GA_APP_DEMO_TRACKING WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_CHANNEL_LIVE_GOALS_CMP WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_LOCATION_DEMO_GOALS_STR WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_LOCATION_DEMO_GOALS_CMP WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_LOCATION_LIVE_GOALS_STR WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_LOCATION_LIVE_GOALS_CMP WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_CHANNEL_DEMO_GOALS_STR WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_CHANNEL_DEMO_GOALS_CMP WHERE ga_date = trunc(p_ga_date);
    DELETE GA_AGG_CHANNEL_LIVE_GOALS_STR WHERE ga_date = trunc(p_ga_date);


    MERGE INTO ga_profiles q USING
    (
      SELECT DISTINCT
        *
      FROM
        TABLE(CAST(p_profiles AS ga_profile_tab))
    )
    mq ON
    (
      q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      updated_by             = p_user,
      update_timestamp       = SYSTIMESTAMP,
      logical_load_timestamp = lv_logical_load_timestamp,
      url                    = mq.url WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        create_timestamp,
        created_by,
        update_timestamp,
        updated_by,
        logical_load_timestamp,
        url
      )
      VALUES
      (
        mq.profile_id,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        p_user,
        lv_logical_load_timestamp,
        mq.url
      );
    ------------------------------------------------------------
    /*FOR i IN 1..p_pagepath_by_source.count
    loop lv_ga_pagepath_by_source_tab (i).ga_date := p_pagepath_by_source (i).ga_date;
      lv_ga_pagepath_by_source_tab(i).SOURCE   := p_pagepath_by_source(i).SOURCE;
      lv_ga_pagepath_by_source_tab(i).medium   := p_pagepath_by_source(i).medium;
      lv_ga_pagepath_by_source_tab(i).campaign := p_pagepath_by_source(i).campaign;
      lv_ga_pagepath_by_source_tab(i).page_path := CASE
                                                      WHEN LENGTH(p_pagepath_by_source(i).page_path)>4000 THEN
                                                        substr ( p_pagepath_by_source(i).page_path,1,4000)
                                                      ELSE
                                                        p_pagepath_by_source(i).page_path
                                                      END;

      lv_ga_pagepath_by_source_tab(i).keyword        := p_pagepath_by_source(i).keyword;

      lv_ga_pagepath_by_source_tab(i).exit_page_path :=
                                                        CASE
                                                        WHEN LENGTH(p_pagepath_by_source(i).exit_page_path)>4000 THEN
                                                          substr ( p_pagepath_by_source(i).exit_page_path,1,4000)
                                                        ELSE
                                                          p_pagepath_by_source(i).exit_page_path
                                                        END;
      lv_ga_pagepath_by_source_tab(i).profile_id := p_pagepath_by_source(i).profile_id;
      lv_ga_pagepath_by_source_tab(i).unique_page_views := p_pagepath_by_source(i).unique_page_views;
      lv_ga_pagepath_by_source_tab(i).bounces   := p_pagepath_by_source(i).bounces;
      lv_ga_pagepath_by_source_tab(i).entrances := p_pagepath_by_source(i).entrances;
      lv_ga_pagepath_by_source_tab(i).visits       := p_pagepath_by_source(i).visits;
      lv_ga_pagepath_by_source_tab(i).time_on_page := p_pagepath_by_source(i).time_on_page;
      lv_ga_pagepath_by_source_tab(i).new_visits := p_pagepath_by_source(i).new_visits;
      lv_ga_pagepath_by_source_tab(i).page_views := p_pagepath_by_source(i).page_views;
    END loop;
    forall i IN 1..lv_ga_pagepath_by_source_tab.count MERGE INTO
    ga_pagepath_by_source q USING
    (
      SELECT
        lv_ga_pagepath_by_source_tab(i).ga_date           AS ga_date,
        lv_ga_pagepath_by_source_tab(i).SOURCE            AS SOURCE,
        lv_ga_pagepath_by_source_tab(i).medium            AS medium,
        lv_ga_pagepath_by_source_tab(i).campaign          AS campaign,
        lv_ga_pagepath_by_source_tab(i).page_path         AS page_path,
        lv_ga_pagepath_by_source_tab(i).keyword           AS keyword,
        lv_ga_pagepath_by_source_tab(i).exit_page_path    AS exit_page_path,
        lv_ga_pagepath_by_source_tab(i).profile_id        AS profile_id,
        lv_ga_pagepath_by_source_tab(i).unique_page_views AS unique_page_views,
        lv_ga_pagepath_by_source_tab(i).bounces           AS bounces,
        lv_ga_pagepath_by_source_tab(i).entrances         AS entrances,
        lv_ga_pagepath_by_source_tab(i).visits            AS visits,
        lv_ga_pagepath_by_source_tab(i).time_on_page      AS time_on_page,
        lv_ga_pagepath_by_source_tab(i).new_visits        AS new_visits,
        lv_ga_pagepath_by_source_tab(i).page_views        AS page_views
      FROM
        dual
    )
    mq ON
    (
      q.ga_date                       = mq.ga_date AND q.SOURCE = mq.SOURCE AND q.medium = mq.medium
      AND q.campaign                  = mq.campaign AND q.page_path = mq.page_path AND q.keyword =
      mq.keyword AND q.exit_page_path = mq.exit_page_path AND q.profile_id =
      mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.unique_page_views      = mq.unique_page_views,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.time_on_page           = mq.time_on_page,
      q.new_visits             = mq.new_visits,
      q.page_views             = mq.page_views WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.page_path,
        q.keyword,
        q.exit_page_path,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.unique_page_views,
        q.bounces,
        q.entrances,
        q.visits,
        q.time_on_page,
        q.new_visits,
        q.page_views
      )
      VALUES
      (
        mq.ga_date,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.page_path,
        mq.keyword,
        mq.exit_page_path,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.unique_page_views,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.time_on_page,
        mq.new_visits,
        mq.page_views
      );*/
    ---------------------------------------------------
    /*FOR i IN 1..p_referral_by_source.count
    loop
      lv_ga_referral_by_source_tab
      (
        i
      )
      .ga_date := p_referral_by_source
      (
        i
      )
      .ga_date;
      lv_ga_referral_by_source_tab(i).SOURCE   := p_referral_by_source(i).SOURCE;
      lv_ga_referral_by_source_tab(i).medium   := p_referral_by_source(i).medium;
      lv_ga_referral_by_source_tab(i).campaign := p_referral_by_source(i)
      .campaign;
      lv_ga_referral_by_source_tab(i).keyword       := p_referral_by_source(i).keyword;
      lv_ga_referral_by_source_tab(i).referral_path :=
      CASE
      WHEN LENGTH(p_referral_by_source(i).referral_path)>4000 THEN
        substr ( p_referral_by_source(i).referral_path,1,4000)
      ELSE
        p_referral_by_source(i).referral_path
      END;
      lv_ga_referral_by_source_tab(i).second_page_path :=
      CASE
      WHEN LENGTH(p_referral_by_source(i).second_page_path)>4000 THEN
        substr ( p_referral_by_source(i).second_page_path,1,4000)
      ELSE
        p_referral_by_source(i).second_page_path
      END;
      lv_ga_referral_by_source_tab(i).profile_id := p_referral_by_source(i)
      .profile_id;
      lv_ga_referral_by_source_tab(i).transactions := p_referral_by_source(i)
      .transactions;
      lv_ga_referral_by_source_tab(i).time_on_site := p_referral_by_source(i)
      .time_on_site;
      lv_ga_referral_by_source_tab(i).bounces   := p_referral_by_source(i).bounces;
      lv_ga_referral_by_source_tab(i).entrances := p_referral_by_source(i)
      .entrances;
      lv_ga_referral_by_source_tab(i).visits     := p_referral_by_source(i).visits;
      lv_ga_referral_by_source_tab(i).new_visits := p_referral_by_source(i)
      .new_visits;
      lv_ga_referral_by_source_tab(i).page_views := p_referral_by_source(i)
      .page_views;
      lv_ga_referral_by_source_tab(i).time_on_page := p_referral_by_source(i)
      .time_on_page;
    END loop;
    forall i IN 1..lv_ga_referral_by_source_tab.count MERGE INTO
    ga_referral_by_source q USING
    (
      SELECT
        lv_ga_referral_by_source_tab(i).ga_date          AS ga_date,
        lv_ga_referral_by_source_tab(i).SOURCE           AS SOURCE,
        lv_ga_referral_by_source_tab(i).medium           AS medium,
        lv_ga_referral_by_source_tab(i).campaign         AS campaign,
        lv_ga_referral_by_source_tab(i).keyword          AS keyword,
        lv_ga_referral_by_source_tab(i).referral_path    AS referral_path,
        lv_ga_referral_by_source_tab(i).second_page_path AS second_page_path,
        lv_ga_referral_by_source_tab(i).profile_id       AS profile_id,
        lv_ga_referral_by_source_tab(i).transactions     AS transactions,
        lv_ga_referral_by_source_tab(i).time_on_site     AS time_on_site,
        lv_ga_referral_by_source_tab(i).bounces          AS bounces,
        lv_ga_referral_by_source_tab(i).entrances        AS entrances,
        lv_ga_referral_by_source_tab(i).visits           AS visits,
        lv_ga_referral_by_source_tab(i).new_visits       AS new_visits,
        lv_ga_referral_by_source_tab(i).page_views       AS page_views,
        lv_ga_referral_by_source_tab(i).time_on_page     AS time_on_page
      FROM
        dual
    )
    mq ON
    (
      q.ga_date                            = mq.ga_date AND q.SOURCE = mq.SOURCE AND q.medium = mq.medium
      AND q.campaign                       = mq.campaign AND q.keyword = mq.keyword AND q.referral_path
                                           = mq.referral_path AND q.second_page_path =
      mq.second_page_path AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.transactions           = mq.transactions,
      q.time_on_site           = mq.time_on_site,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.page_views             = mq.page_views,
      q.time_on_page           = mq.time_on_page WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.keyword,
        q.referral_path,
        q.second_page_path,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.transactions,
        q.time_on_site,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.page_views,
        q.time_on_page
      )
      VALUES
      (
        mq.ga_date,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.keyword,
        mq.referral_path,
        mq.second_page_path,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.transactions,
        mq.time_on_site,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.page_views,
        mq.time_on_page
      );*/
    ---------------------------------------------
    /*MERGE INTO ga_visits_by_trx q USING
    (
      SELECT
        *
      FROM
        TABLE(CAST(p_visits_by_trx AS ga_visits_by_trx_tab))
    )
    mq ON
    (
      q.ga_date      = mq.ga_date AND
      q.SOURCE = mq.SOURCE AND
      q.medium = mq.medium AND
      q.campaign = mq.campaign AND
      q.transactions_id = mq.transactions_id AND
      q.keyword      = mq.keyword AND
      q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.page_views             = mq.page_views,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.visitors               = mq.visitors,
      q.exits                  = mq.exits,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_page           = mq.time_on_page WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.transactions_id,
        q.keyword,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.page_views,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.visitors,
        q.exits,
        q.unique_page_views,
        q.time_on_page
      )
      VALUES
      (
        mq.ga_date,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.transactions_id,
        mq.keyword,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.page_views,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.visitors,
        mq.exits,
        mq.unique_page_views,
        mq.time_on_page
      );*/
    /*MERGE INTO ga_cost_by_source q USING
    (
      SELECT
        *
      FROM
        TABLE(CAST(p_cost_by_source AS ga_cost_by_source_tab))
    )
    mq ON
    (
      q.ga_date                     = mq.ga_date AND q.SOURCE = mq.SOURCE AND q.medium = mq.medium
      AND q.campaign                = mq.campaign AND q.keyword = mq.keyword AND q.is_mobile =
      mq.is_mobile AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.ad_cost                = mq.ad_cost,
      q.page_views             = mq.page_views,
      q.time_on_site           = mq.time_on_site,
      q.visits                 = mq.visits,
      q.ad_clicks              = mq.ad_clicks,
      q.impressions            = mq.impressions,
      q.time_on_page           = mq.time_on_page WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.keyword,
        q.is_mobile,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.ad_cost,
        q.page_views,
        q.time_on_site,
        q.visits,
        q.ad_clicks,
        q.impressions,
        q.time_on_page
      )
      VALUES
      (
        mq.ga_date,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.keyword,
        mq.is_mobile,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.ad_cost,
        mq.page_views,
        mq.time_on_site,
        mq.visits,
        mq.ad_clicks,
        mq.impressions,
        mq.time_on_page
      );*/
    /*MERGE INTO ga_visits_goals_by_location q USING
    (
      SELECT
        t1.ga_date,
        t1.country,
        t1.continent,
        t1.city,
        t1.latitude,
        t1.longitude,
        t1.region,
        t1.profile_id,
        page_views,
        bounces,
        entrances,
        visits,
        new_visits,
        exits,
        unique_page_views,
        time_on_page,
        goal_1_completions,
        goal_2_completions,
        goal_3_completions,
        goal_4_completions,
        goal_5_completions,
        goal_6_completions,
        goal_7_completions,
        goal_8_completions,
        goal_9_completions,
        goal_10_completions,
        goal_11_completions,
        goal_12_completions,
        goal_13_completions,
        goal_14_completions,
        goal_15_completions,
        goal_16_completions,
        goal_17_completions,
        goal_18_completions,
        goal_19_completions,
        goal_20_completions
      FROM
        TABLE(CAST(p_ga_visits_goals_by_loc_1 AS ga_visits_goals_by_loc_1_tab))
        t1,
        TABLE(CAST(p_ga_visits_goals_by_loc_2 AS ga_visits_goals_by_loc_2_tab))
        t2,
        TABLE(CAST(p_ga_visits_goals_by_loc_3 AS ga_visits_goals_by_loc_3_tab))
        t3
      WHERE
        t1.ga_date      = t2.ga_date(+)
      AND t1.country    = t2.country(+)
      AND t1.continent  = t2.continent(+)
      AND t1.city       = t2.city(+)
      AND t1.latitude   = t2.latitude (+)
      AND t1.longitude  = t2.longitude (+)
      AND t1.region     = t2.region(+)
      AND t1.profile_id = t2.profile_id(+)
      AND
        --
        t1.ga_date      = t3.ga_date(+)
      AND t1.country    = t3.country(+)
      AND t1.continent  = t3.continent(+)
      AND t1.city       = t3.city(+)
      AND t1.latitude   = t3.latitude (+)
      AND t1.longitude  = t3.longitude (+)
      AND t1.region     = t3.region(+)
      AND t1.profile_id = t3.profile_id(+)
    )
    mq ON
    (
      q.ga_date               = mq.ga_date AND q.country = mq.country AND q.continent =
      mq.continent AND q.city = mq.city AND q.latitude = mq.latitude AND
      q.longitude             = mq.longitude AND q.region = mq.region AND
      q.profile_id            = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.page_views             = mq.page_views,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.exits                  = mq.exits,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_page           = mq.time_on_page,
      q.goal_1_completions     = mq.goal_1_completions,
      q.goal_2_completions     = mq.goal_2_completions,
      q.goal_3_completions     = mq.goal_3_completions,
      q.goal_4_completions     = mq.goal_4_completions,
      q.goal_5_completions     = mq.goal_5_completions,
      q.goal_6_completions     = mq.goal_6_completions,
      q.goal_7_completions     = mq.goal_7_completions,
      q.goal_8_completions     = mq.goal_8_completions,
      q.goal_9_completions     = mq.goal_9_completions,
      q.goal_10_completions    = mq.goal_10_completions,
      q.goal_11_completions    = mq.goal_11_completions,
      q.goal_12_completions    = mq.goal_12_completions,
      q.goal_13_completions    = mq.goal_13_completions,
      q.goal_14_completions    = mq.goal_14_completions,
      q.goal_15_completions    = mq.goal_15_completions,
      q.goal_16_completions    = mq.goal_16_completions,
      q.goal_17_completions    = mq.goal_17_completions,
      q.goal_18_completions    = mq.goal_18_completions,
      q.goal_19_completions    = mq.goal_19_completions,
      q.goal_20_completions    = mq.goal_20_completions WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.country,
        q.continent,
        q.city,
        q.latitude,
        q.longitude,
        q.region,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.page_views,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.exits,
        q.unique_page_views,
        q.time_on_page,
        q.goal_1_completions,
        q.goal_2_completions,
        q.goal_3_completions,
        q.goal_4_completions,
        q.goal_5_completions,
        q.goal_6_completions,
        q.goal_7_completions,
        q.goal_8_completions,
        q.goal_9_completions,
        q.goal_10_completions,
        q.goal_11_completions,
        q.goal_12_completions,
        q.goal_13_completions,
        q.goal_14_completions,
        q.goal_15_completions,
        q.goal_16_completions,
        q.goal_17_completions,
        q.goal_18_completions,
        q.goal_19_completions,
        q.goal_20_completions
      )
      VALUES
      (
        mq.ga_date,
        mq.country,
        mq.continent,
        mq.city,
        mq.latitude,
        mq.longitude,
        mq.region,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.page_views,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.exits,
        mq.unique_page_views,
        mq.time_on_page,
        mq.goal_1_completions,
        mq.goal_2_completions,
        mq.goal_3_completions,
        mq.goal_4_completions,
        mq.goal_5_completions,
        mq.goal_6_completions,
        mq.goal_7_completions,
        mq.goal_8_completions,
        mq.goal_9_completions,
        mq.goal_10_completions,
        mq.goal_11_completions,
        mq.goal_12_completions,
        mq.goal_13_completions,
        mq.goal_14_completions,
        mq.goal_15_completions,
        mq.goal_16_completions,
        mq.goal_17_completions,
        mq.goal_18_completions,
        mq.goal_19_completions,
        mq.goal_20_completions
      );*/
    /*MERGE INTO ga_goal_by_source q USING
    (
      SELECT
        t1.ga_date,
        t1.SOURCE,
        t1.medium,
        t1.campaign,
        t1.keyword,
        t1.hostname,
        t1.profile_id,
        goal_1_completions,
        goal_2_completions,
        goal_3_completions,
        goal_4_completions,
        goal_5_completions,
        goal_6_completions,
        goal_7_completions,
        goal_8_completions,
        goal_9_completions,
        goal_10_completions,
        goal_11_completions,
        goal_12_completions,
        goal_13_completions,
        goal_14_completions,
        goal_15_completions,
        goal_16_completions,
        goal_17_completions,
        goal_18_completions,
        goal_19_completions,
        goal_20_completions
      FROM
        TABLE(CAST(p_ga_goal_by_source_1 AS ga_goal_by_source_1_tab)) t1,
        TABLE(CAST(p_ga_goal_by_source_2 AS ga_goal_by_source_2_tab)) t2
      WHERE
        t1.ga_date      = t2.ga_date(+)
      AND t1.SOURCE     = t2.SOURCE(+)
      AND t1.medium     = t2.medium(+)
      AND t1.campaign   = t2.campaign(+)
      AND t1.keyword    = t2.keyword(+)
      AND t1.hostname   = t2.hostname(+)
      AND t1.profile_id = t2.profile_id(+)
    )
    mq ON
    (
      q.ga_date                    = mq.ga_date AND q.SOURCE = mq.SOURCE AND q.medium = mq.medium
      AND q.campaign               = mq.campaign AND q.keyword = mq.keyword AND q.hostname =
      mq.hostname AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.goal_1_completions     = mq.goal_1_completions,
      q.goal_2_completions     = mq.goal_2_completions,
      q.goal_3_completions     = mq.goal_3_completions,
      q.goal_4_completions     = mq.goal_4_completions,
      q.goal_5_completions     = mq.goal_5_completions,
      q.goal_6_completions     = mq.goal_6_completions,
      q.goal_7_completions     = mq.goal_7_completions,
      q.goal_8_completions     = mq.goal_8_completions,
      q.goal_9_completions     = mq.goal_9_completions,
      q.goal_10_completions    = mq.goal_10_completions,
      q.goal_11_completions    = mq.goal_11_completions,
      q.goal_12_completions    = mq.goal_12_completions,
      q.goal_13_completions    = mq.goal_13_completions,
      q.goal_14_completions    = mq.goal_14_completions,
      q.goal_15_completions    = mq.goal_15_completions,
      q.goal_16_completions    = mq.goal_16_completions,
      q.goal_17_completions    = mq.goal_17_completions,
      q.goal_18_completions    = mq.goal_18_completions,
      q.goal_19_completions    = mq.goal_19_completions,
      q.goal_20_completions    = mq.goal_20_completions WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.keyword,
        q.hostname,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.goal_1_completions,
        q.goal_2_completions,
        q.goal_3_completions,
        q.goal_4_completions,
        q.goal_5_completions,
        q.goal_6_completions,
        q.goal_7_completions,
        q.goal_8_completions,
        q.goal_9_completions,
        q.goal_10_completions,
        q.goal_11_completions,
        q.goal_12_completions,
        q.goal_13_completions,
        q.goal_14_completions,
        q.goal_15_completions,
        q.goal_16_completions,
        q.goal_17_completions,
        q.goal_18_completions,
        q.goal_19_completions,
        q.goal_20_completions
      )
      VALUES
      (
        mq.ga_date,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.keyword,
        mq.hostname,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.goal_1_completions,
        mq.goal_2_completions,
        mq.goal_3_completions,
        mq.goal_4_completions,
        mq.goal_5_completions,
        mq.goal_6_completions,
        mq.goal_7_completions,
        mq.goal_8_completions,
        mq.goal_9_completions,
        mq.goal_10_completions,
        mq.goal_11_completions,
        mq.goal_12_completions,
        mq.goal_13_completions,
        mq.goal_14_completions,
        mq.goal_15_completions,
        mq.goal_16_completions,
        mq.goal_17_completions,
        mq.goal_18_completions,
        mq.goal_19_completions,
        mq.goal_20_completions
      );*/
    /*MERGE INTO ga_visits_by_mobile_trx q USING
    (
      SELECT
        t1.ga_date,
        t1.is_mobile,
        t1.SOURCE,
        t1.medium,
        t1.transactions_id,
        t1.campaign,
        t1.profile_id,
        nvl(t1.transactions,t2.transactions) transactions,
        nvl(t1.exits, t2.exits) exits,
        nvl(t1.bounces, t2.bounces) bounces,
        nvl(t1.entrances, t2.entrances) entrances,
        nvl(t1.visits, t2.visits) visits,
        nvl(t1.new_visits, t2.new_visits) new_visits,
        nvl(t1.page_views, t2.page_views) page_views,
        nvl(t1.time_on_page, t2.time_on_page) time_on_page,
        nvl(t1.unique_page_views, t2.unique_page_views) unique_page_views,
        nvl(t1.time_on_site, t2.time_on_site) time_on_site
      FROM
        TABLE(CAST(p_visits_by_mobile_trx_1 AS ga_visits_by_mobile_trx_1_tab)) t1
      ,
        TABLE(CAST(p_visits_by_mobile_trx_2 AS ga_visits_by_mobile_trx_2_tab)) t2
      WHERE
        t1.ga_date      = t2.ga_date(+)
      AND t1.is_mobile  = t2.is_mobile(+)
      AND t1.SOURCE     = t2.SOURCE (+)
      AND t1.medium     = t2.medium(+)
      AND t1.campaign   = t2.campaign(+)
      AND t1.profile_id = t2.profile_id(+)
    )
    mq ON
    (
      q.ga_date                         = mq.ga_date AND q.is_mobile = mq.is_mobile AND q.SOURCE =
      mq.SOURCE AND q.medium            = mq.medium AND q.transactions_id =
      mq.transactions_id AND q.campaign = mq.campaign AND q.profile_id =
      mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.transactions           = mq.transactions,
      q.exits                  = mq.exits,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.page_views             = mq.page_views,
      q.time_on_page           = mq.time_on_page,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_site           = mq.time_on_site WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.is_mobile,
        q.SOURCE,
        q.medium,
        q.transactions_id,
        q.campaign,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.transactions,
        q.exits,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.page_views,
        q.time_on_page,
        q.unique_page_views,
        q.time_on_site
      )
      VALUES
      (
        mq.ga_date,
        mq.is_mobile,
        mq.SOURCE,
        mq.medium,
        mq.transactions_id,
        mq.campaign,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.transactions,
        mq.exits,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.page_views,
        mq.time_on_page,
        mq.unique_page_views,
        mq.time_on_site
      );*/
    /*MERGE INTO ga_visits_goal_by_mobile_src q USING
    (
      SELECT DISTINCT
        mobile_source_yes_no.ga_date,
        mobile_source_yes_no.ga_source SOURCE,
        mobile_source_yes_no.ga_medium medium,
        mobile_source_yes_no.ga_campaign campaign,
        mobile_source_yes_no.keyword,
        mobile_source_yes_no.is_mobile,
        mobile_source_yes_no.profile_id,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.mobile_device_info
          ELSE NULL
        END mobile_device_info,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_1_completions
          ELSE mobile_source_yes_no.goal_1_completions
        END goal_1_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_2_completions
          ELSE mobile_source_yes_no.goal_2_completions
        END goal_2_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_3_completions
          ELSE mobile_source_yes_no.goal_3_completions
        END goal_3_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_4_completions
          ELSE mobile_source_yes_no.goal_4_completions
        END goal_4_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_5_completions
          ELSE mobile_source_yes_no.goal_5_completions
        END goal_5_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_6_completions
          ELSE mobile_source_yes_no.goal_6_completions
        END goal_6_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_7_completions
          ELSE mobile_source_yes_no.goal_7_completions
        END goal_7_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_8_completions
          ELSE mobile_source_yes_no.goal_8_completions
        END goal_8_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_9_completions
          ELSE mobile_source_yes_no.goal_9_completions
        END goal_9_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_10_completions
          ELSE mobile_source_yes_no.goal_10_completions
        END goal_10_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_11_completions
          ELSE mobile_source_yes_no.goal_11_completions
        END goal_11_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_12_completions
          ELSE mobile_source_yes_no.goal_12_completions
        END goal_12_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_13_completions
          ELSE mobile_source_yes_no.goal_13_completions
        END goal_13_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_14_completions
          ELSE mobile_source_yes_no.goal_14_completions
        END goal_14_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_15_completions
          ELSE mobile_source_yes_no.goal_15_completions
        END goal_15_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_16_completions
          ELSE mobile_source_yes_no.goal_16_completions
        END goal_16_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_17_completions
          ELSE mobile_source_yes_no.goal_17_completions
        END goal_17_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_18_completions
          ELSE mobile_source_yes_no.goal_18_completions
        END goal_18_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_19_completions
          ELSE mobile_source_yes_no.goal_19_completions
        END goal_19_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.goal_20_completions
          ELSE mobile_source_yes_no.goal_20_completions
        END goal_20_completions,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.page_views
          ELSE mobile_source_yes_no.page_views
        END page_views,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.bounces
          ELSE mobile_source_yes_no.bounces
        END bounces,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.entrances
          ELSE mobile_source_yes_no.entrances
        END entrances,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.visits
          ELSE mobile_source_yes_no.visits
        END visits,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.new_visits
          ELSE mobile_source_yes_no.new_visits
        END new_visits,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.time_on_site
          ELSE mobile_source_yes_no.time_on_site
        END time_on_site,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.exits
          ELSE mobile_source_yes_no.exits
        END exits,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.unique_page_views
          ELSE mobile_source_yes_no.unique_page_views
        END unique_page_views,
        CASE
          WHEN upper(mobile_source_yes_no.is_mobile) = 'YES'
          THEN mobile_source_yes.time_on_page
          ELSE mobile_source_yes_no.time_on_page
        END time_on_page
      FROM
        (
          SELECT
            t3.ga_date ga_date,
            t3.ga_source ga_source,
            t3.ga_campaign ga_campaign,
            t3.ga_medium ga_medium,
            t3.keyword keyword,
            t3.is_mobile is_mobile,
            t3.profile_id profile_id,
            t3.page_views page_views,
            t3.bounces bounces,
            t3.entrances entrances,
            t3.visits visits,
            t3.new_visits new_visits,
            t3.time_on_site time_on_site,
            t3.exits exits,
            t3.unique_page_views unique_page_views,
            t3.time_on_page time_on_page,
            t1.goal_1_completions goal_1_completions,
            t1.goal_2_completions goal_2_completions,
            t1.goal_3_completions goal_3_completions,
            t1.goal_4_completions goal_4_completions,
            t1.goal_5_completions goal_5_completions,
            t1.goal_6_completions goal_6_completions,
            t1.goal_7_completions goal_7_completions,
            t1.goal_8_completions goal_8_completions,
            t1.goal_9_completions goal_9_completions,
            t1.goal_10_completions goal_10_completions,
            t2.goal_11_completions goal_11_completions,
            t2.goal_12_completions goal_12_completions,
            t2.goal_13_completions goal_13_completions,
            t2.goal_14_completions goal_14_completions,
            t2.goal_15_completions goal_15_completions,
            t2.goal_16_completions goal_16_completions,
            t2.goal_17_completions goal_17_completions,
            t2.goal_18_completions goal_18_completions,
            t2.goal_19_completions goal_19_completions,
            t2.goal_20_completions goal_20_completions
          FROM
            TABLE(CAST(p_ga_visit_gal_by_mbl_src_1 AS
            ga_visit_gal_by_mbl_src_1_tab)) t1,
            TABLE(CAST(p_ga_visit_gal_by_mbl_src_2 AS
            ga_visit_gal_by_mbl_src_2_tab)) t2,
            TABLE(CAST(p_ga_visit_gal_by_mbl_src_3 AS
            ga_visit_gal_by_mbl_src_3_tab)) t3
          WHERE
            t3.ga_date       = t2.ga_date (+)
          AND t3.ga_source   = t2.SOURCE (+)
          AND t3.ga_medium   = t2.medium (+)
          AND t3.ga_campaign = t2.campaign (+)
          AND t3.keyword     = t2.keyword (+)
          AND t3.is_mobile   = t2.is_mobile (+)
          AND t3.profile_id  = t2.profile_id (+)
          AND
            --
            t3.ga_date       = t1.ga_date (+)
          AND t3.ga_source   = t1.SOURCE (+)
          AND t3.ga_medium   = t1.medium (+)
          AND t3.ga_campaign = t1.campaign (+)
          AND t3.keyword     = t1.keyword (+)
          AND t3.is_mobile   = t1.is_mobile (+)
          AND t3.profile_id  = t1.profile_id (+)
        )
        mobile_source_yes_no,
        (
          SELECT
            t4.ga_date ga_date,
            t4.ga_source ga_source,
            t4.ga_campaign ga_campaign,
            t4.ga_medium ga_medium,
            t4.keyword keyword,
            t4.is_mobile is_mobile,
            t4.profile_id profile_id,
            t4.mobile_device_info mobile_device_info,
            t4.page_views page_views,
            t4.bounces bounces,
            t4.entrances entrances,
            t4.visits visits,
            t4.new_visits new_visits,
            t4.time_on_site time_on_site,
            t4.exits exits,
            t4.unique_page_views unique_page_views,
            t4.time_on_page time_on_page,
            t5.goal_1_completions goal_1_completions,
            t5.goal_2_completions goal_2_completions,
            t5.goal_3_completions goal_3_completions,
            t5.goal_4_completions goal_4_completions,
            t5.goal_5_completions goal_5_completions,
            t5.goal_6_completions goal_6_completions,
            t5.goal_7_completions goal_7_completions,
            t5.goal_8_completions goal_8_completions,
            t5.goal_9_completions goal_9_completions,
            t5.goal_10_completions goal_10_completions,
            t6.goal_11_completions goal_11_completions,
            t6.goal_12_completions goal_12_completions,
            t6.goal_13_completions goal_13_completions,
            t6.goal_14_completions goal_14_completions,
            t6.goal_15_completions goal_15_completions,
            t6.goal_16_completions goal_16_completions,
            t6.goal_17_completions goal_17_completions,
            t6.goal_18_completions goal_18_completions,
            t6.goal_19_completions goal_19_completions,
            t6.goal_20_completions goal_20_completions
          FROM
            TABLE(CAST(p_ga_visit_gal_by_mbl_src_4 AS
            ga_visit_gal_by_mbl_src_4_tab)) t4,
            TABLE(CAST(p_ga_visit_gal_by_mbl_src_5 AS
            ga_visit_gal_by_mbl_src_5_tab)) t5,
            TABLE(CAST(p_ga_visit_gal_by_mbl_src_6 AS
            ga_visit_gal_by_mbl_src_6_tab)) t6
          WHERE
            t4.ga_date              = t5.ga_date (+)
          AND t4.ga_source          = t5.ga_source (+)
          AND t4.ga_medium          = t5.ga_medium (+)
          AND t4.ga_campaign        = t5.ga_campaign (+)
          AND t4.keyword            = t5.keyword (+)
          AND t4.is_mobile          = t5.is_mobile (+)
          AND t4.profile_id         = t5.profile_id (+)
          AND t4.mobile_device_info = t5.mobile_device_info (+)
          AND
            --
            t4.ga_date              = t6.ga_date (+)
          AND t4.ga_source          = t6.ga_source (+)
          AND t4.ga_medium          = t6.ga_medium (+)
          AND t4.ga_campaign        = t6.ga_campaign (+)
          AND t4.keyword            = t6.keyword (+)
          AND t4.is_mobile          = t6.is_mobile (+)
          AND t4.profile_id         = t6.profile_id (+)
          AND t4.mobile_device_info = t6.mobile_device_info (+)
        )
        mobile_source_yes
      WHERE
        mobile_source_yes_no.ga_date       = mobile_source_yes.ga_date (+)
      AND mobile_source_yes_no.ga_source   = mobile_source_yes.ga_source (+)
      AND mobile_source_yes_no.ga_medium   = mobile_source_yes.ga_medium (+)
      AND mobile_source_yes_no.ga_campaign = mobile_source_yes.ga_campaign (+)
      AND mobile_source_yes_no.keyword     = mobile_source_yes.keyword (+)
      AND mobile_source_yes_no.is_mobile   = mobile_source_yes.is_mobile (+)
      AND mobile_source_yes_no.profile_id  = mobile_source_yes.profile_id (+)
    )
    mq ON
    (
      q.ga_date                     = mq.ga_date AND q.SOURCE = mq.SOURCE AND q.medium = mq.medium
      AND q.campaign                = mq.campaign AND q.keyword = mq.keyword AND q.is_mobile =
      mq.is_mobile AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.mobile_device_info     = mq.mobile_device_info,
      q.goal_1_completions     = mq.goal_1_completions,
      q.goal_2_completions     = mq.goal_2_completions,
      q.goal_3_completions     = mq.goal_3_completions,
      q.goal_4_completions     = mq.goal_4_completions,
      q.goal_5_completions     = mq.goal_5_completions,
      q.goal_6_completions     = mq.goal_6_completions,
      q.goal_7_completions     = mq.goal_7_completions,
      q.goal_8_completions     = mq.goal_8_completions,
      q.goal_9_completions     = mq.goal_9_completions,
      q.goal_10_completions    = mq.goal_10_completions,
      q.goal_11_completions    = mq.goal_11_completions,
      q.goal_12_completions    = mq.goal_12_completions,
      q.goal_13_completions    = mq.goal_13_completions,
      q.goal_14_completions    = mq.goal_14_completions,
      q.goal_15_completions    = mq.goal_15_completions,
      q.goal_16_completions    = mq.goal_16_completions,
      q.goal_17_completions    = mq.goal_17_completions,
      q.goal_18_completions    = mq.goal_18_completions,
      q.goal_19_completions    = mq.goal_19_completions,
      q.goal_20_completions    = mq.goal_20_completions,
      q.page_views             = mq.page_views,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.time_on_site           = mq.time_on_site,
      q.exits                  = mq.exits,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_page           = mq.time_on_page WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.keyword,
        q.is_mobile,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.mobile_device_info,
        q.goal_1_completions,
        q.goal_2_completions,
        q.goal_3_completions,
        q.goal_4_completions,
        q.goal_5_completions,
        q.goal_6_completions,
        q.goal_7_completions,
        q.goal_8_completions,
        q.goal_9_completions,
        q.goal_10_completions,
        q.goal_11_completions,
        q.goal_12_completions,
        q.goal_13_completions,
        q.goal_14_completions,
        q.goal_15_completions,
        q.goal_16_completions,
        q.goal_17_completions,
        q.goal_18_completions,
        q.goal_19_completions,
        q.goal_20_completions,
        q.page_views,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.time_on_site,
        q.exits,
        q.unique_page_views,
        q.time_on_page
      )
      VALUES
      (
        mq.ga_date,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.keyword,
        mq.is_mobile,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.mobile_device_info,
        mq.goal_1_completions,
        mq.goal_2_completions,
        mq.goal_3_completions,
        mq.goal_4_completions,
        mq.goal_5_completions,
        mq.goal_6_completions,
        mq.goal_7_completions,
        mq.goal_8_completions,
        mq.goal_9_completions,
        mq.goal_10_completions,
        mq.goal_11_completions,
        mq.goal_12_completions,
        mq.goal_13_completions,
        mq.goal_14_completions,
        mq.goal_15_completions,
        mq.goal_16_completions,
        mq.goal_17_completions,
        mq.goal_18_completions,
        mq.goal_19_completions,
        mq.goal_20_completions,
        mq.page_views,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.time_on_site,
        mq.exits,
        mq.unique_page_views,
        mq.time_on_page
      );*/
    /*MERGE INTO ga_visits_gls_by_oprtng_systm q USING
    (
      SELECT
        t3.ga_date,
        t3.operating_system,
        t3.browser,
        t3.is_mobile,
        t3.language,
        t3.hostname,
        t3.profile_id,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN NULL
          ELSE t5.mobile_device_info
        END mobile_device_info,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_1_completions
          ELSE t5.goal_1_completions
        END goal_1_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_2_completions
          ELSE t5.goal_2_completions
        END goal_2_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_3_completions
          ELSE t5.goal_3_completions
        END goal_3_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_4_completions
          ELSE t5.goal_4_completions
        END goal_4_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_5_completions
          ELSE t5.goal_5_completions
        END goal_5_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_6_completions
          ELSE t5.goal_6_completions
        END goal_6_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_7_completions
          ELSE t5.goal_7_completions
        END goal_7_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_8_completions
          ELSE t5.goal_9_completions
        END goal_8_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_10_completions
          ELSE t5.goal_10_completions
        END goal_9_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t1.goal_10_completions
          ELSE t5.goal_10_completions
        END goal_10_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_11_completions
          ELSE t6.goal_11_completions
        END goal_11_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_12_completions
          ELSE t6.goal_12_completions
        END goal_12_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_13_completions
          ELSE t6.goal_13_completions
        END goal_13_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_14_completions
          ELSE t6.goal_14_completions
        END goal_14_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_15_completions
          ELSE t6.goal_15_completions
        END goal_15_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_16_completions
          ELSE t6.goal_16_completions
        END goal_16_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_17_completions
          ELSE t6.goal_17_completions
        END goal_17_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_18_completions
          ELSE t6.goal_18_completions
        END goal_18_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_19_completions
          ELSE t6.goal_19_completions
        END goal_19_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t2.goal_20_completions
          ELSE t6.goal_20_completions
        END goal_20_completions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.page_views
          ELSE t4.page_views
        END page_views,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.bounces
          ELSE t4.bounces
        END bounces,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.entrances
          ELSE t4.entrances
        END entrances,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.visits
          ELSE t4.visits
        END visits,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.new_visits
          ELSE t4.new_visits
        END new_visits,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.time_on_site
          ELSE t4.time_on_site
        END time_on_site,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.exits
          ELSE t4.exits
        END exits,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.unique_page_views
          ELSE t4.unique_page_views
        END unique_page_views,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.ga_transactions
          ELSE t4.ga_transactions
        END ga_transactions,
        CASE
          WHEN upper(t3.is_mobile) = 'NO'
          THEN t3.time_on_page
          ELSE t4.time_on_page
        END time_on_page
      FROM
        TABLE(CAST(p_ga_vsts_gls_by_optng_sys_1 AS ga_vsts_gls_by_optng_sys_1_tab
        )) t1,
        TABLE(CAST(p_ga_vsts_gls_by_optng_sys_2 AS ga_vsts_gls_by_optng_sys_2_tab
        )) t2,
        TABLE(CAST(p_ga_vsts_gls_by_optng_sys_3 AS ga_vsts_gls_by_optng_sys_3_tab
        )) t3,
        TABLE(CAST(p_ga_vsts_gls_by_optng_sys_4 AS ga_vsts_gls_by_optng_sys_4_tab
        )) t4,
        TABLE(CAST(p_ga_vsts_gls_by_optng_sys_5 AS ga_vsts_gls_by_optng_sys_5_tab
        )) t5,
        TABLE(CAST(p_ga_vsts_gls_by_optng_sys_6 AS ga_vsts_gls_by_optng_sys_6_tab
        )) t6
      WHERE
        t3.ga_date            = t2.ga_date(+)
      AND t3.operating_system = t2.operating_system(+)
      AND t3.browser          = t2.browser(+)
      AND t3.is_mobile        = t2.is_mobile(+)
      AND t3.language         = t2.language(+)
      AND t3.hostname         = t2.hostname(+)
      AND t3.profile_id       = t2.profile_id(+)
      AND
        --
        t3.ga_date            = t1.ga_date(+)
      AND t3.operating_system = t1.operating_system(+)
      AND t3.browser          = t1.browser(+)
      AND t3.is_mobile        = t1.is_mobile(+)
      AND t3.language         = t1.language(+)
      AND t3.hostname         = t1.hostname(+)
      AND t3.profile_id       = t1.profile_id(+)
      AND
        --
        t3.ga_date            = t4.ga_date(+)
      AND t3.operating_system = t4.operating_system(+)
      AND t3.browser          = t4.browser(+)
      AND t3.is_mobile        = t4.is_mobile(+)
      AND t3.language         = t4.language(+)
      AND t3.hostname         = t4.hostname(+)
      AND t3.profile_id       = t4.profile_id(+)
      AND
        --
        t3.ga_date            = t5.ga_date(+)
      AND t3.operating_system = t5.operating_system(+)
      AND t3.browser          = t5.browser(+)
      AND t3.is_mobile        = t5.is_mobile(+)
      AND t3.language         = t5.language(+)
      AND t3.hostname         = t5.hostname(+)
      AND t3.profile_id       = t5.profile_id(+)
      AND
        --
        t3.ga_date            = t6.ga_date(+)
      AND t3.operating_system = t6.operating_system(+)
      AND t3.browser          = t6.browser(+)
      AND t3.is_mobile        = t6.is_mobile(+)
      AND t3.language         = t6.language(+)
      AND t3.hostname         = t6.hostname(+)
      AND t3.profile_id       = t6.profile_id(+)
    )
    mq ON
    (
      q.ga_date                  = mq.ga_date AND q.operating_system = mq.operating_system AND
      q.browser                  = mq.browser AND q.is_mobile = mq.is_mobile AND q.language =
      mq.language AND q.hostname = mq.hostname AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.mobile_device_info     = mq.mobile_device_info,
      q.goal_1_completions     = mq.goal_1_completions,
      q.goal_2_completions     = mq.goal_2_completions,
      q.goal_3_completions     = mq.goal_3_completions,
      q.goal_4_completions     = mq.goal_4_completions,
      q.goal_5_completions     = mq.goal_5_completions,
      q.goal_6_completions     = mq.goal_6_completions,
      q.goal_7_completions     = mq.goal_7_completions,
      q.goal_8_completions     = mq.goal_8_completions,
      q.goal_9_completions     = mq.goal_9_completions,
      q.goal_10_completions    = mq.goal_10_completions,
      q.goal_11_completions    = mq.goal_11_completions,
      q.goal_12_completions    = mq.goal_12_completions,
      q.goal_13_completions    = mq.goal_13_completions,
      q.goal_14_completions    = mq.goal_14_completions,
      q.goal_15_completions    = mq.goal_15_completions,
      q.goal_16_completions    = mq.goal_16_completions,
      q.goal_17_completions    = mq.goal_17_completions,
      q.goal_18_completions    = mq.goal_18_completions,
      q.goal_19_completions    = mq.goal_19_completions,
      q.goal_20_completions    = mq.goal_20_completions,
      q.page_views             = mq.page_views,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.time_on_site           = mq.time_on_site,
      q.exits                  = mq.exits,
      q.unique_page_views      = mq.unique_page_views,
      q.ga_transactions        = mq.ga_transactions,
      q.time_on_page           = mq.time_on_page WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.operating_system,
        q.browser,
        q.is_mobile,
        q.language,
        q.hostname,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.mobile_device_info,
        q.goal_1_completions,
        q.goal_2_completions,
        q.goal_3_completions,
        q.goal_4_completions,
        q.goal_5_completions,
        q.goal_6_completions,
        q.goal_7_completions,
        q.goal_8_completions,
        q.goal_9_completions,
        q.goal_10_completions,
        q.goal_11_completions,
        q.goal_12_completions,
        q.goal_13_completions,
        q.goal_14_completions,
        q.goal_15_completions,
        q.goal_16_completions,
        q.goal_17_completions,
        q.goal_18_completions,
        q.goal_19_completions,
        q.goal_20_completions,
        q.page_views,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.time_on_site,
        q.exits,
        q.unique_page_views,
        q.ga_transactions,
        q.time_on_page
      )
      VALUES
      (
        mq.ga_date,
        mq.operating_system,
        mq.browser,
        mq.is_mobile,
        mq.language,
        mq.hostname,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.mobile_device_info,
        mq.goal_1_completions,
        mq.goal_2_completions,
        mq.goal_3_completions,
        mq.goal_4_completions,
        mq.goal_5_completions,
        mq.goal_6_completions,
        mq.goal_7_completions,
        mq.goal_8_completions,
        mq.goal_9_completions,
        mq.goal_10_completions,
        mq.goal_11_completions,
        mq.goal_12_completions,
        mq.goal_13_completions,
        mq.goal_14_completions,
        mq.goal_15_completions,
        mq.goal_16_completions,
        mq.goal_17_completions,
        mq.goal_18_completions,
        mq.goal_19_completions,
        mq.goal_20_completions,
        mq.page_views,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.time_on_site,
        mq.exits,
        mq.unique_page_views,
        mq.ga_transactions,
        mq.time_on_page
      );*/
    /*MERGE INTO ga_by_mobile q USING
    (
      SELECT
        *
      FROM
        TABLE(CAST(p_by_mobile AS ga_by_mobile_tab))
    )
    mq ON
    (
      q.ga_date = mq.ga_date AND q.is_mobile = mq.is_mobile AND q.profile_id =
      mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.page_views             = mq.page_views,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.visitors               = mq.visitors,
      q.exits                  = mq.exits,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_page           = mq.time_on_page,
      q.time_on_site           = mq.time_on_site WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.is_mobile,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.page_views,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.visitors,
        q.exits,
        q.unique_page_views,
        q.time_on_page,
        q.time_on_site
      )
      VALUES
      (
        mq.ga_date,
        mq.is_mobile,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.page_views,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.visitors,
        mq.exits,
        mq.unique_page_views,
        mq.time_on_page,
        mq.time_on_site
      );*/
    /*MERGE INTO ga_type_visitor_by_source q USING
    (
      SELECT
        *
      FROM
        TABLE(CAST(p_type_visitor_by_source AS ga_type_visitor_by_source_tab))
    )
    mq ON
    (
      q.ga_date                    = mq.ga_date AND q.is_mobile = mq.is_mobile AND q.visitor_type =
      mq.visitor_type AND q.SOURCE = mq.SOURCE AND q.medium = mq.medium AND
      q.campaign                   = mq.campaign AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.exits                  = mq.exits,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.page_views             = mq.page_views,
      q.time_on_page           = mq.time_on_page,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_site           = mq.time_on_site WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.is_mobile,
        q.visitor_type,
        q.SOURCE,
        q.medium,
        q.campaign,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.exits,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.page_views,
        q.time_on_page,
        q.unique_page_views,
        q.time_on_site
      )
      VALUES
      (
        mq.ga_date,
        mq.is_mobile,
        mq.visitor_type,
        mq.SOURCE,
        mq.medium,
        mq.campaign,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.exits,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.page_views,
        mq.time_on_page,
        mq.unique_page_views,
        mq.time_on_site
      );*/
    /*MERGE INTO ga_pdtsku_by_trx q USING
    (
      SELECT
        *
      FROM
        TABLE(CAST(p_pdtsku_by_trx AS ga_pdtsku_by_trx_tab))
    )
    mq ON
    (
      q.ga_date                    = mq.ga_date AND q.is_mobile = mq.is_mobile AND q.product_sku =
      mq.product_sku AND q.SOURCE  = mq.SOURCE AND q.medium = mq.medium AND
      q.transactions_id            = mq.transactions_id AND q.campaign =
      mq.campaign AND q.profile_id = mq.profile_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      q.logical_load_timestamp = lv_logical_load_timestamp,
      q.updated_by             = p_user,
      q.update_timestamp       = SYSTIMESTAMP,
      q.exits                  = mq.exits,
      q.bounces                = mq.bounces,
      q.entrances              = mq.entrances,
      q.visits                 = mq.visits,
      q.new_visits             = mq.new_visits,
      q.page_views             = mq.page_views,
      q.time_on_page           = mq.time_on_page,
      q.unique_page_views      = mq.unique_page_views,
      q.time_on_site           = mq.time_on_site WHEN NOT MATCHED THEN
    INSERT
      (
        q.ga_date,
        q.is_mobile,
        q.product_sku,
        q.SOURCE,
        q.medium,
        q.transactions_id,
        q.campaign,
        q.profile_id,
        q.logical_load_timestamp,
        q.created_by,
        q.create_timestamp,
        q.updated_by,
        q.update_timestamp,
        q.exits,
        q.bounces,
        q.entrances,
        q.visits,
        q.new_visits,
        q.page_views,
        q.time_on_page,
        q.unique_page_views,
        q.time_on_site
      )
      VALUES
      (
        mq.ga_date,
        mq.is_mobile,
        mq.product_sku,
        mq.SOURCE,
        mq.medium,
        mq.transactions_id,
        mq.campaign,
        mq.profile_id,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        mq.exits,
        mq.bounces,
        mq.entrances,
        mq.visits,
        mq.new_visits,
        mq.page_views,
        mq.time_on_page,
        mq.unique_page_views,
        mq.time_on_site
      );*/

    /*MERGE INTO ga_agg_channel_goals_starts old_version USING
    (
      SELECT
        profile_id,
        to_date(ga_date,'YYYYMMDDHH24') ga_date,
        sys_extract_utc(to_timestamp_tz(ga_date
        ||' Europe/London','YYYYMMDDHH24 TZR')) ga_date_utc,
        campaign,
        SOURCE,
        medium,
        keyword,
        trading_type,
        channel,
        goal_1_starts,
        goal_2_starts,
        goal_3_starts,
        goal_6_starts,
        goal_7_starts,
        goal_9_starts,
        goal_10_starts,
        live_app_starts,
        demo_app_starts,
        live_app_validation_email_send
      FROM
        TABLE(CAST(p_ga_agg_channel_goals_st AS ga_agg_channel_goals_st_tab))
    )
    new_version ON
    (
      old_version.ga_date                             = new_version.ga_date AND old_version.profile_id =
      new_version.profile_id AND old_version.campaign = new_version.campaign AND
      old_version.SOURCE                              = new_version.SOURCE AND
      old_version.medium                              = new_version.medium
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp         = lv_logical_load_timestamp,
      old_version.updated_by                     = p_user,
      old_version.update_timestamp               = SYSTIMESTAMP,
      old_version.keyword                        = new_version.keyword,
      old_version.trading_type                   = new_version.trading_type,
      old_version.channel                        = new_version.channel,
      old_version.goal_1_starts                  = new_version.goal_1_starts,
      old_version.goal_2_starts                  = old_version.goal_2_starts,
      old_version.goal_3_starts                  = old_version.goal_3_starts,
      old_version.goal_6_starts                  = old_version.goal_6_starts,
      old_version.goal_7_starts                  = old_version.goal_7_starts,
      old_version.goal_9_starts                  = old_version.goal_9_starts,
      old_version.goal_10_starts                 = old_version.goal_10_starts,
      old_version.live_app_starts                = old_version.live_app_starts,
      old_version.demo_app_starts                = old_version.demo_app_starts,
      old_version.live_app_validation_email_send =
      old_version.live_app_validation_email_send WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        campaign,
        SOURCE,
        medium,
        keyword,
        trading_type,
        channel,
        goal_1_starts,
        goal_2_starts,
        goal_3_starts,
        goal_6_starts,
        goal_7_starts,
        goal_9_starts,
        goal_10_starts,
        live_app_starts,
        demo_app_starts,
        live_app_validation_email_send
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.campaign,
        new_version.SOURCE,
        new_version.medium,
        new_version.keyword,
        new_version.trading_type,
        new_version.channel,
        new_version.goal_1_starts,
        new_version.goal_2_starts,
        new_version.goal_3_starts,
        new_version.goal_6_starts,
        new_version.goal_7_starts,
        new_version.goal_9_starts,
        new_version.goal_10_starts,
        new_version.live_app_starts,
        new_version.demo_app_starts,
        new_version.live_app_validation_email_send
      );
      */

    /*
    MERGE INTO ga_agg_channel_goals_complets old_version USING
    (
      SELECT
        profile_id,
        to_date(ga_date,'YYYYMMDDHH24') ga_date,
        sys_extract_utc(to_timestamp_tz(ga_date
        ||' Europe/London','YYYYMMDDHH24 TZR')) ga_date_utc,
        campaign,
        SOURCE,
        medium,
        keyword,
        trading_type,
        channel,
        goal_1_completions,
        goal_2_completions,
        goal_3_completions,
        goal_6_completions,
        goal_7_completions,
        goal_9_completions,
        goal_10_completions,
        live_app_submissions,
        demo_app_completions,
        demo_app_validation_email_send
      FROM
        TABLE(CAST(p_ga_agg_channel_goals_cmp AS ga_agg_channel_goals_cmp_tab))
    )
    new_version ON
    (
      old_version.ga_date                             = new_version.ga_date AND old_version.profile_id =
      new_version.profile_id AND old_version.campaign = new_version.campaign AND
      old_version.SOURCE                              = new_version.SOURCE AND
      old_version.medium                              = new_version.medium
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp         = lv_logical_load_timestamp,
      old_version.updated_by                     = p_user,
      old_version.update_timestamp               = SYSTIMESTAMP,
      old_version.keyword                        = new_version.keyword,
      old_version.trading_type                   = new_version.trading_type,
      old_version.channel                        = new_version.channel,
      old_version.goal_1_completions             = new_version.goal_1_completions,
      old_version.goal_2_completions             = old_version.goal_2_completions,
      old_version.goal_3_completions             = old_version.goal_3_completions,
      old_version.goal_6_completions             = old_version.goal_6_completions,
      old_version.goal_7_completions             = old_version.goal_7_completions,
      old_version.goal_9_completions             = old_version.goal_9_completions,
      old_version.goal_10_completions            = old_version.goal_10_completions,
      old_version.live_app_submissions           = old_version.live_app_submissions,
      old_version.demo_app_completions           = old_version.demo_app_completions,
      old_version.demo_app_validation_email_send =
      old_version.demo_app_validation_email_send WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        campaign,
        SOURCE,
        medium,
        keyword,
        trading_type,
        channel,
        goal_1_completions,
        goal_2_completions,
        goal_3_completions,
        goal_6_completions,
        goal_7_completions,
        goal_9_completions,
        goal_10_completions,
        live_app_submissions,
        demo_app_completions,
        demo_app_validation_email_send
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.campaign,
        new_version.SOURCE,
        new_version.medium,
        new_version.keyword,
        new_version.trading_type,
        new_version.channel,
        new_version.goal_1_completions,
        new_version.goal_2_completions,
        new_version.goal_3_completions,
        new_version.goal_6_completions,
        new_version.goal_7_completions,
        new_version.goal_9_completions,
        new_version.goal_10_completions,
        new_version.live_app_submissions,
        new_version.demo_app_completions,
        new_version.demo_app_validation_email_send
      );
    */

    /*
    MERGE INTO ga_agg_location_goals_starts old_version USING
    (
      SELECT
        profile_id,
        to_date(ga_date,'YYYYMMDDHH24') ga_date,
        sys_extract_utc(to_timestamp_tz(ga_date
        ||' Europe/London','YYYYMMDDHH24 TZR')) ga_date_utc,
        continent,
        country,
        region,
        city,
        SOURCE,
        medium,
        goal_1_starts,
        goal_2_starts,
        goal_3_starts,
        goal_6_starts,
        goal_7_starts,
        goal_9_starts,
        goal_10_starts,
        live_app_starts,
        demo_app_starts,
        live_app_validation_email_send
      FROM
        TABLE(CAST(p_ga_agg_location_goals_st AS ga_agg_location_goals_st_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND old_version.profile_id =
      new_version.profile_id AND old_version.SOURCE = new_version.SOURCE AND
      old_version.medium                            = new_version.medium
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp         = lv_logical_load_timestamp,
      old_version.updated_by                     = p_user,
      old_version.update_timestamp               = SYSTIMESTAMP,
      old_version.continent                      = new_version.continent,
      old_version.country                        = new_version.country,
      old_version.region                         = new_version.region,
      old_version.city                           = new_version.city,
      old_version.goal_1_starts                  = new_version.goal_1_starts,
      old_version.goal_2_starts                  = new_version.goal_2_starts,
      old_version.goal_3_starts                  = new_version.goal_3_starts,
      old_version.goal_6_starts                  = new_version.goal_6_starts,
      old_version.goal_7_starts                  = new_version.goal_7_starts,
      old_version.goal_9_starts                  = new_version.goal_9_starts,
      old_version.goal_10_starts                 = new_version.goal_10_starts,
      old_version.live_app_starts                = new_version.live_app_starts,
      old_version.demo_app_starts                = new_version.demo_app_starts,
      old_version.live_app_validation_email_send =
      new_version.live_app_validation_email_send WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        continent,
        country,
        region,
        city,
        SOURCE,
        medium,
        goal_1_starts,
        goal_2_starts,
        goal_3_starts,
        goal_6_starts,
        goal_7_starts,
        goal_9_starts,
        goal_10_starts,
        live_app_starts,
        demo_app_starts,
        live_app_validation_email_send
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.continent,
        new_version.country,
        new_version.region,
        new_version.city,
        new_version.SOURCE,
        new_version.medium,
        new_version.goal_1_starts,
        new_version.goal_2_starts,
        new_version.goal_3_starts,
        new_version.goal_6_starts,
        new_version.goal_7_starts,
        new_version.goal_9_starts,
        new_version.goal_10_starts,
        new_version.live_app_starts,
        new_version.demo_app_starts,
        new_version.live_app_validation_email_send
      );
    */

    /*
    MERGE INTO ga_agg_location_goals_complets old_version USING
    (
      SELECT
        profile_id,
        to_date(ga_date,'YYYYMMDDHH24') ga_date,
        sys_extract_utc(to_timestamp_tz(ga_date
        ||' Europe/London','YYYYMMDDHH24 TZR')) ga_date_utc,
        continent,
        country,
        region,
        city,
        SOURCE,
        medium,
        goal_1_completions,
        goal_2_completions,
        goal_3_completions,
        goal_6_completions,
        goal_7_completions,
        goal_9_completions,
        goal_10_completions,
        live_app_submissions,
        demo_app_completions,
        demo_app_validation_email_send
      FROM
        TABLE(CAST(p_ga_agg_location_goals_cmp AS ga_agg_location_goals_cmp_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND old_version.profile_id =
      new_version.profile_id AND old_version.SOURCE = new_version.SOURCE AND
      old_version.medium                            = new_version.medium
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp         = lv_logical_load_timestamp,
      old_version.updated_by                     = p_user,
      old_version.update_timestamp               = SYSTIMESTAMP,
      old_version.continent                      = new_version.continent,
      old_version.country                        = new_version.country,
      old_version.region                         = new_version.region,
      old_version.city                           = new_version.city,
      old_version.goal_1_completions             = new_version.goal_1_completions,
      old_version.goal_2_completions             = new_version.goal_2_completions,
      old_version.goal_3_completions             = new_version.goal_3_completions,
      old_version.goal_6_completions             = new_version.goal_6_completions,
      old_version.goal_7_completions             = new_version.goal_7_completions,
      old_version.goal_9_completions             = new_version.goal_9_completions,
      old_version.goal_10_completions            = new_version.goal_10_completions,
      old_version.live_app_submissions           = new_version.live_app_submissions,
      old_version.demo_app_completions           = new_version.demo_app_completions,
      old_version.demo_app_validation_email_send =
      new_version.demo_app_validation_email_send WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        continent,
        country,
        region,
        city,
        SOURCE,
        medium,
        goal_1_completions,
        goal_2_completions,
        goal_3_completions,
        goal_6_completions,
        goal_7_completions,
        goal_9_completions,
        goal_10_completions,
        live_app_submissions,
        demo_app_completions,
        demo_app_validation_email_send
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.continent,
        new_version.country,
        new_version.region,
        new_version.city,
        new_version.SOURCE,
        new_version.medium,
        new_version.goal_1_completions,
        new_version.goal_2_completions,
        new_version.goal_3_completions,
        new_version.goal_6_completions,
        new_version.goal_7_completions,
        new_version.goal_9_completions,
        new_version.goal_10_completions,
        new_version.live_app_submissions,
        new_version.demo_app_completions,
        new_version.demo_app_validation_email_send
      );
    */

    MERGE INTO ga_agg_channel_tracking old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        campaign,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        keyword,
        user_type,
        country_code,
        channel,
        users,
        new_users,
        sessions,
        session_duration,
        bounces,
        exits,
        entrances,
        total_events,
        unique_events,
        page_views
      FROM
        TABLE(CAST(p_ga_agg_channel_tracking AS ga_agg_channel_tracking_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.keyword                           = new_version.keyword AND
      old_version.medium                            = new_version.medium AND
      old_version.campaign                          = new_version.campaign AND
      old_version.country_code                      = new_version.country_code AND
      old_version.channel                           = new_version.channel AND
      old_version.user_type                         = new_version.user_type
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp = lv_logical_load_timestamp,
      old_version.updated_by             = p_user,
      old_version.update_timestamp       = SYSTIMESTAMP,
      old_version.users                  = new_version.users,
      old_version.new_users              = new_version.new_users,
      old_version.sessions               = new_version.sessions,
      old_version.session_duration       = new_version.session_duration,
      old_version.bounces                = new_version.bounces,
      old_version.exits                  = new_version.exits,
      old_version.entrances              = new_version.entrances,
      old_version.total_events           = new_version.total_events,
      old_version.unique_events          = new_version.unique_events,
      old_version.page_views             = new_version.page_views WHEN NOT
      MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        campaign,
        SOURCE,
        medium,
        keyword,
        user_type,
        country_code,
        channel,
        users,
        new_users,
        sessions,
        session_duration,
        bounces,
        exits,
        entrances,
        total_events,
        unique_events,
        page_views
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.campaign,
        new_version.SOURCE,
        new_version.medium,
        new_version.keyword,
        new_version.user_type,
        new_version.country_code,
        new_version.channel,
        new_version.users,
        new_version.new_users,
        new_version.sessions,
        new_version.session_duration,
        new_version.bounces,
        new_version.exits,
        new_version.entrances,
        new_version.total_events,
        new_version.unique_events,
        new_version.page_views
      );


    /*MERGE INTO ga_agg_location_tracking old_version USING
    (

    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.medium                            = new_version.medium AND
      old_version.continent                         = new_version.continent AND
      old_version.country                           = new_version.country AND
      old_version.region                            = new_version.region AND
      old_version.city                              = new_version.city
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp = lv_logical_load_timestamp,
      old_version.updated_by             = p_user,
      old_version.update_timestamp       = SYSTIMESTAMP,
      old_version.users                  = new_version.users,
      old_version.new_users              = new_version.new_users,
      old_version.sessions               = new_version.sessions,
      old_version.session_duration       = new_version.session_duration,
      old_version.bounces                = new_version.bounces,
      old_version.exits                  = new_version.exits,
      old_version.entrances              = new_version.entrances,
      old_version.total_events           = new_version.total_events,
      old_version.unique_events          = new_version.unique_events,
      old_version.page_views             = new_version.page_views WHEN NOT
      MATCHED THEN*/
    INSERT INTO ga_agg_location_tracking
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        continent,
        country,
        region,
        city,
        SOURCE,
        medium,
        country_code,
        users,
        new_users,
        sessions,
        session_duration,
        bounces,
        exits,
        entrances,
        total_events,
        unique_events,
        page_views
      )
      SELECT
        profile_id,
        ga_date,
        ga_date,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMEStAMP,
        p_user,
        SYSTIMESTAMP,
        continent,
        country,
        region,
        city,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        users,
        new_users,
        sessions,
        session_duration,
        bounces,
        exits,
        entrances,
        total_events,
        unique_events,
        page_views
      FROM
        TABLE(CAST(p_ga_agg_location_tracking AS ga_agg_location_tracking_tab));

    MERGE INTO ga_app_live_tracking old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        campaign,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        channel,
        keyword,
        application_id,
        total_events,
        unique_events
      FROM
        TABLE(CAST(p_ga_app_live_tracking AS ga_app_live_tracking_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND old_version.profile_id =
      new_version.profile_id AND old_version.SOURCE = new_version.SOURCE AND
      old_version.keyword                           = new_version.keyword AND
      old_version.medium                            = new_version.medium AND
      old_version.campaign                          = new_version.campaign AND
      old_version.country_code                      = new_version.country_code AND
      old_version.channel                           = new_version.channel and
      old_version.application_id                    = new_version.application_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp = lv_logical_load_timestamp,
      old_version.updated_by             = p_user,
      old_version.update_timestamp       = SYSTIMESTAMP,
      old_version.total_events           = new_version.total_events,
      old_version.unique_events          = new_version.unique_events
  WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        campaign,
        SOURCE,
        medium,
        country_code,
        channel,
        keyword,
        application_id,
        total_events,
        unique_events
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.campaign,
        new_version.SOURCE,
        new_version.medium,
        new_version.country_code,
        new_version.channel,
        new_version.keyword,
        new_version.application_id,
        new_version.total_events,
        new_version.unique_events
      );

    MERGE INTO ga_app_demo_tracking old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        campaign,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        channel,
        keyword,
        identity_id,
        total_events,
        unique_events
      FROM
        TABLE(CAST(p_ga_app_demo_tracking AS ga_app_demo_tracking_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.keyword                           = new_version.keyword AND
      old_version.medium                            = new_version.medium AND
      old_version.campaign                          = new_version.campaign AND
      old_version.country_code                      = new_version.country_code AND
      old_version.channel                           = new_version.channel AND
      old_version.identity_id                       = new_version.identity_id
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.logical_load_timestamp = lv_logical_load_timestamp,
      old_version.updated_by             = p_user,
      old_version.update_timestamp       = SYSTIMESTAMP,
      old_version.total_events           = new_version.total_events,
      old_version.unique_events          = new_version.unique_events
    WHEN NOT MATCHED THEN
    INSERT
      (
        profile_id,
        ga_date,
        ga_date_utc,
        logical_load_timestamp,
        created_by,
        create_timestamp,
        updated_by,
        update_timestamp,
        campaign,
        SOURCE,
        medium,
        country_code,
        keyword,
        identity_id,
        total_events,
        unique_events
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        lv_logical_load_timestamp,
        p_user,
        SYSTIMESTAMP,
        p_user,
        SYSTIMESTAMP,
        new_version.campaign,
        new_version.SOURCE,
        new_version.medium,
        new_version.country_code,
        new_version.keyword,
        new_version.identity_id,
        new_version.total_events,
        new_version.unique_events
      );

    MERGE INTO ga_agg_channel_live_goals_cmp old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        lv_logical_load_timestamp logical_load_timestamp,
        p_user created_by,
        SYSTIMESTAMP create_timestamp,
        p_user updated_by,
        SYSTIMESTAMP update_timestamp,
        campaign,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        keyword,
        trading_type,
        country_code,
        channel,
        goal_2_completions,
        goal_3_completions,
        goal_7_completions,
        goal_10_completions,
        goal_11_completions,
        goal_12_completions,
        live_app_verifies,
        live_app_submits
      FROM
        TABLE(CAST(p_ga_agg_chnnl_lv_gls_cmp AS ga_agg_chnnl_lv_gls_cmp_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.keyword                           = new_version.keyword AND
      old_version.medium                            = new_version.medium AND
      old_version.campaign                          = new_version.campaign AND
      old_version.country_code                      = new_version.country_code AND
      old_version.trading_type                      = new_version.trading_type AND
      old_version.channel                           = new_version.channel
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.ga_date_utc            = new_version.ga_date_utc,
      old_version.logical_load_timestamp = new_version.logical_load_timestamp,
      old_version.updated_by             = new_version.updated_by,
      old_version.update_timestamp       = new_version.update_timestamp,
      old_version.goal_2_completions     = new_version.goal_2_completions,
      old_version.goal_3_completions     = new_version.goal_3_completions,
      old_version.goal_7_completions     = new_version.goal_7_completions,
      old_version.goal_10_completions    = new_version.goal_10_completions,
      old_version.goal_11_completions    = new_version.goal_11_completions,
      old_version.goal_12_completions    = new_version.goal_12_completions,
      old_version.live_app_verifies      = new_version.live_app_verifies,
      old_version.live_app_submits       = new_version.live_app_submits

      WHEN NOT MATCHED THEN
    INSERT
      (
        old_version.profile_id,
        old_version.ga_date,
        old_version.ga_date_utc,
        old_version.logical_load_timestamp,
        old_version.created_by,
        old_version.create_timestamp,
        old_version.updated_by,
        old_version.update_timestamp,
        old_version.campaign,
        old_version.SOURCE,
        old_version.medium,
        old_version.keyword,
        old_version.trading_type,
        old_version.country_code,
        old_version.channel,
        old_version.goal_2_completions,
        old_version.goal_3_completions,
        old_version.goal_7_completions,
        old_version.goal_10_completions,
        old_version.goal_11_completions,
        old_version.goal_12_completions,
        old_version.live_app_verifies,
        old_version.live_app_submits
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.campaign,
        new_version.SOURCE,
        new_version.medium,
        new_version.keyword,
        new_version.trading_type,
        new_version.country_code,
        new_version.channel,
        new_version.goal_2_completions,
        new_version.goal_3_completions,
        new_version.goal_7_completions,
        new_version.goal_10_completions,
        new_version.goal_11_completions,
        new_version.goal_12_completions,
        new_version.live_app_verifies,
        new_version.live_app_submits
      );

    MERGE INTO ga_agg_location_demo_goals_str old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        lv_logical_load_timestamp logical_load_timestamp,
        p_user created_by,
        SYSTIMESTAMP create_timestamp,
        p_user updated_by,
        SYSTIMESTAMP update_timestamp,
        continent,
        country,
        region,
        city,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        goal_1_starts,
        goal_6_starts,
        goal_8_starts,
        goal_9_starts,
        goal_13_starts,
        demo_app_starts
      FROM
        TABLE(CAST(p_ga_agg_lctn_dm_gls_str as ga_agg_lctn_dm_gls_str_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.continent                         = new_version.continent AND
      old_version.country                           = new_version.country AND
      old_version.region                            = new_version.region AND
      old_version.city                              = new_version.city AND
      old_version.medium                            = new_version.medium AND
      old_version.country_code                      = new_version.country_code
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.ga_date_utc            = new_version.ga_date_utc,
      old_version.logical_load_timestamp = new_version.logical_load_timestamp,
      old_version.updated_by             = new_version.updated_by,
      old_version.update_timestamp       = new_version.update_timestamp,
      old_version.goal_1_starts          = new_version.goal_1_starts,
      old_version.goal_6_starts          = new_version.goal_6_starts,
      old_version.goal_8_starts          = new_version.goal_8_starts,
      old_version.goal_9_starts          = new_version.goal_9_starts,
      old_version.goal_13_starts         = new_version.goal_13_starts,
      old_version.demo_app_starts        = new_version.demo_app_starts
  WHEN NOT  MATCHED THEN
    INSERT
      (
        old_version.profile_id,
        old_version.ga_date,
        old_version.ga_date_utc,
        old_version.logical_load_timestamp,
        old_version.created_by,
        old_version.create_timestamp,
        old_version.updated_by,
        old_version.update_timestamp,
        old_version.continent,
        old_version.country,
        old_version.region,
        old_version.city,
        old_version.SOURCE,
        old_version.medium,
        old_version.country_code,
        old_version.goal_1_starts,
        old_version.goal_6_starts,
        old_version.goal_8_starts,
        old_version.goal_9_starts,
        old_version.goal_13_starts,
        old_version.demo_app_starts
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.continent,
        new_version.country,
        new_version.region,
        new_version.city,
        new_version.SOURCE,
        new_version.medium,
        new_version.country_code,
        new_version.goal_1_starts,
        new_version.goal_6_starts,
        new_version.goal_8_starts,
        new_version.goal_9_starts,
        new_version.goal_13_starts,
        new_version.demo_app_starts
      );

    MERGE INTO ga_agg_location_demo_goals_cmp old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        lv_logical_load_timestamp logical_load_timestamp,
        p_user created_by,
        SYSTIMESTAMP create_timestamp,
        p_user updated_by,
        SYSTIMESTAMP update_timestamp,
        continent,
        country,
        region,
        city,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        goal_1_completions,
        goal_6_completions,
        goal_8_completions,
        goal_9_completions,
        goal_13_completions,
        demo_app_verifies,
        demo_app_submits
      FROM
        TABLE(CAST(p_ga_agg_lctn_dm_gls_cmp as GA_AGG_LCTN_DM_GLS_CMP_TAB))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.continent                         = new_version.continent AND
      old_version.country                           = new_version.country AND
      old_version.region                            = new_version.region AND
      old_version.city                              = new_version.city AND
      old_version.medium                            = new_version.medium AND
      old_version.country_code                      = new_version.country_code
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.ga_date_utc            = new_version.ga_date_utc,
      old_version.logical_load_timestamp = new_version.logical_load_timestamp,
      old_version.updated_by             = new_version.updated_by,
      old_version.update_timestamp       = new_version.update_timestamp,
      old_version.goal_1_completions     = new_version.goal_1_completions,
      old_version.goal_6_completions     = new_version.goal_6_completions,
      old_version.goal_8_completions     = new_version.goal_8_completions,
      old_version.goal_9_completions     = new_version.goal_9_completions,
      old_version.goal_13_completions    = new_version.goal_13_completions,
      old_version.demo_app_verifies      = new_version.demo_app_verifies,
      old_version.demo_app_submits       = new_version.demo_app_submits
      WHEN NOT MATCHED THEN
    INSERT
      (
        old_version.profile_id,
        old_version.ga_date,
        old_version.ga_date_utc,
        old_version.logical_load_timestamp,
        old_version.created_by,
        old_version.create_timestamp,
        old_version.updated_by,
        old_version.update_timestamp,
        old_version.continent,
        old_version.country,
        old_version.region,
        old_version.city,
        old_version.SOURCE,
        old_version.medium,
        old_version.country_code,
        old_version.goal_1_completions,
        old_version.goal_6_completions,
        old_version.goal_8_completions,
        old_version.goal_9_completions,
        old_version.goal_13_completions,
        old_version.demo_app_verifies,
        old_version.demo_app_submits
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.continent,
        new_version.country,
        new_version.region,
        new_version.city,
        new_version.SOURCE,
        new_version.medium,
        new_version.country_code,
        new_version.goal_1_completions,
        new_version.goal_6_completions,
        new_version.goal_8_completions,
        new_version.goal_9_completions,
        new_version.goal_13_completions,
        new_version.demo_app_verifies,
        new_version.demo_app_submits
      );


    MERGE INTO ga_agg_location_live_goals_str old_version
    USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        lv_logical_load_timestamp logical_load_timestamp,
        p_user created_by,
        SYSTIMESTAMP create_timestamp,
        p_user updated_by,
        SYSTIMESTAMP update_timestamp,
        continent,
        country,
        region,
        city,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        goal_2_starts,
        goal_3_starts,
        goal_7_starts,
        goal_10_starts,
        goal_11_starts,
        goal_12_starts,
        live_app_starts,
        pre_app_views
      FROM
        TABLE(CAST(p_ga_agg_lctn_lv_gls_str as ga_agg_lctn_lv_gls_str_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.continent                         = new_version.continent AND
      old_version.country                           = new_version.country AND
      old_version.region                            = new_version.region AND
      old_version.city                              = new_version.city AND
      old_version.medium                            = new_version.medium AND
      old_version.country_code                      = new_version.country_code
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.ga_date_utc            = new_version.ga_date_utc,
      old_version.logical_load_timestamp = new_version.logical_load_timestamp,
      old_version.updated_by             = new_version.updated_by,
      old_version.update_timestamp       = new_version.update_timestamp,
      old_version.goal_2_starts          = new_version.goal_2_starts,
      old_version.goal_3_starts          = new_version.goal_3_starts,
      old_version.goal_7_starts          = new_version.goal_7_starts,
      old_version.goal_10_starts         = new_version.goal_10_starts,
      old_version.goal_11_starts         = new_version.goal_11_starts,
      old_version.goal_12_starts         = new_version.goal_12_starts,
      old_version.live_app_starts        = new_version.live_app_starts,
      old_version.pre_app_views          = new_version.pre_app_views
      WHEN NOT MATCHED THEN
    INSERT
      (
        old_version.profile_id,
        old_version.ga_date,
        old_version.ga_date_utc,
        old_version.logical_load_timestamp,
        old_version.created_by,
        old_version.create_timestamp,
        old_version.updated_by,
        old_version.update_timestamp,
        old_version.continent,
        old_version.country,
        old_version.region,
        old_version.city,
        old_version.SOURCE,
        old_version.medium,
        old_version.country_Code,
        old_version.goal_2_starts,
        old_version.goal_3_starts,
        old_version.goal_7_starts,
        old_version.goal_10_starts,
        old_version.goal_11_starts,
        old_version.goal_12_starts,
        old_version.live_app_starts,
        old_version.pre_app_views
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.continent,
        new_version.country,
        new_version.region,
        new_version.city,
        new_version.SOURCE,
        new_version.medium,
        new_version.country_code,
        new_version.goal_2_starts,
        new_version.goal_3_starts,
        new_version.goal_7_starts,
        new_version.goal_10_starts,
        new_version.goal_11_starts,
        new_version.goal_12_starts,
        new_version.live_app_starts,
        new_version.pre_app_views
      );


    MERGE INTO ga_agg_location_live_goals_cmp old_version USING
    (
      SELECT
        profile_id,
        ga_date,
        ga_date ga_date_utc,
        lv_logical_load_timestamp logical_load_timestamp,
        p_user created_by,
        SYSTIMESTAMP create_timestamp,
        p_user updated_by,
        SYSTIMESTAMP update_timestamp,
        continent,
        country,
        region,
        city,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
        RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
        country_code,
        goal_2_completions,
        goal_3_completions,
        goal_7_completions,
        goal_10_completions,
        goal_11_completions,
        goal_12_completions,
        live_app_verifies,
        live_app_submits
      FROM
        TABLE(CAST(p_ga_agg_lctn_lv_gls_cmp as ga_agg_lctn_lv_gls_cmp_tab))
    )
    new_version ON
    (
      old_version.ga_date                           = new_version.ga_date AND
      old_version.profile_id                        = new_version.profile_id AND
      old_version.SOURCE                            = new_version.SOURCE AND
      old_version.continent                         = new_version.continent AND
      old_version.country                           = new_version.country AND
      old_version.region                            = new_version.region AND
      old_version.city                              = new_version.city AND
      old_version.medium                            = new_version.medium AND
      old_version.country_code                      = new_version.country_code
    )
  WHEN MATCHED THEN
    UPDATE
    SET
      old_version.ga_date_utc            = new_version.ga_date_utc,
      old_version.logical_load_timestamp = new_version.logical_load_timestamp,
      old_version.updated_by             = new_version.updated_by,
      old_version.update_timestamp       = new_version.update_timestamp,
      old_version.goal_2_completions     = new_version.goal_2_completions,
      old_version.goal_3_completions     = new_version.goal_3_completions,
      old_version.goal_7_completions     = new_version.goal_7_completions,
      old_version.goal_10_completions    = new_version.goal_10_completions,
      old_version.goal_11_completions    = new_version.goal_11_completions,
      old_version.goal_12_completions    = new_version.goal_12_completions,
      old_version.live_app_verifies      = new_version.live_app_verifies,
      old_version.live_app_submits       = new_version.live_app_submits WHEN NOT
      MATCHED THEN
    INSERT
      (
        old_version.profile_id,
        old_version.ga_date,
        old_version.ga_date_utc,
        old_version.logical_load_timestamp,
        old_version.created_by,
        old_version.create_timestamp,
        old_version.updated_by,
        old_version.update_timestamp,
        old_version.continent,
        old_version.country,
        old_version.region,
        old_version.city,
        old_version.SOURCE,
        old_version.medium,
        old_version.country_Code,
        old_version.goal_2_completions,
        old_version.goal_3_completions,
        old_version.goal_7_completions,
        old_version.goal_10_completions,
        old_version.goal_11_completions,
        old_version.goal_12_completions,
        old_version.live_app_verifies,
        old_version.live_app_submits
      )
      VALUES
      (
        new_version.profile_id,
        new_version.ga_date,
        new_version.ga_date_utc,
        new_version.logical_load_timestamp,
        new_version.created_by,
        new_version.create_timestamp,
        new_version.updated_by,
        new_version.update_timestamp,
        new_version.continent,
        new_version.country,
        new_version.region,
        new_version.city,
        new_version.SOURCE,
        new_version.medium,
        new_version.country_code,
        new_version.goal_2_completions,
        new_version.goal_3_completions,
        new_version.goal_7_completions,
        new_version.goal_10_completions,
        new_version.goal_11_completions,
        new_version.goal_12_completions,
        new_version.live_app_verifies,
        new_version.live_app_submits
      );


      MERGE INTO   GA_AGG_CHANNEL_DEMO_GOALS_STR  old_version
      USING  (SELECT
                    profile_id,
                    ga_date,
                    ga_date ga_date_utc,
                    lv_logical_load_timestamp logical_load_timestamp,
                    p_user created_by,
                    SYSTIMESTAMP create_timestamp,
                    p_user updated_by,
                    SYSTIMESTAMP update_timestamp,
                    campaign,
                    RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
                    RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
                    keyword,
                    trading_type,
                    country_code,
                    channel,
                    goal_1_starts,
                    goal_6_starts,
                    goal_8_starts,
                    goal_9_starts,
                    goal_13_starts,
                    Demo_App_Starts
        FROM TABLE(CAST(P_GA_AGG_CHNNL_DM_GL_STR AS GA_AGG_CHNNL_DM_GL_STR_TAB))) new_version
      ON (
            old_version.ga_date                           = new_version.ga_date AND
            old_version.profile_id                        = new_version.profile_id AND
            old_version.SOURCE                            = new_version.SOURCE AND
            old_version.keyword                           = new_version.keyword AND
            old_version.medium                            = new_version.medium AND
            old_version.campaign                          = new_version.campaign AND
            old_version.country_code                      = new_version.country_code  AND
            old_version.channel                           = new_version.channel AND
            old_version.trading_type                      = new_version.trading_type
      )
      WHEN MATCHED THEN
      UPDATE SET
      old_version.ga_date_utc = new_version.ga_date_utc,
      old_version.logical_load_timestamp = new_version.logical_load_timestamp,
      old_version.updated_by = new_version.updated_by,
      old_version.update_timestamp = new_version.update_timestamp,
      old_version.goal_1_starts = new_version.goal_1_starts,
      old_version.goal_6_starts = new_version.goal_6_starts,
      old_version.goal_8_starts = new_version.goal_8_starts,
      old_version.goal_9_starts = new_version.goal_9_starts,
      old_version.goal_13_starts = new_version.goal_13_starts,
      old_version.Demo_App_Starts = new_version.Demo_App_Starts
      WHEN NOT MATCHED THEN
      INSERT  (
                old_version.profile_id,
                old_version.ga_date,
                old_version.ga_date_utc,
                old_version.logical_load_timestamp,
                old_version.created_by,
                old_version.create_timestamp,
                old_version.updated_by,
                old_version.update_timestamp,
                old_version.campaign,
                old_version.source,
                old_version.medium,
                old_version.keyword,
                old_version.trading_type,
                old_version.country_code,
                old_version.channel,
                old_version.goal_1_starts,
                old_version.goal_6_starts,
                old_version.goal_8_starts,
                old_version.goal_9_starts,
                old_version.goal_13_starts,
                old_version.Demo_App_Starts)
      VALUES (
                new_version.profile_id,
                new_version.ga_date,
                new_version.ga_date_utc,
                new_version.logical_load_timestamp,
                new_version.created_by,
                new_version.create_timestamp,
                new_version.updated_by,
                new_version.update_timestamp,
                new_version.campaign,
                new_version.source,
                new_version.medium,
                new_version.keyword,
                new_version.trading_type,
                new_version.country_code,
                new_version.channel,
                new_version.goal_1_starts,
                new_version.goal_6_starts,
                new_version.goal_8_starts,
                new_version.goal_9_starts,
                new_version.goal_13_starts,
                new_version.Demo_App_Starts);

    MERGE INTO   GA_AGG_CHANNEL_DEMO_GOALS_CMP  old_version
    USING  (SELECT
                  profile_id,
                  ga_date,
                  ga_date ga_date_utc,
                  lv_logical_load_timestamp logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  campaign,
                  RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
                  RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
                  keyword,
                  trading_type,
                  country_code,
                  channel,
                  goal_1_completions,
                  goal_6_completions,
                  goal_8_completions,
                  goal_9_completions,
                  goal_13_completions,
                  Demo_App_Verifies,
                  Demo_App_Submits
           FROM TABLE(CAST(P_GA_AGG_CHNNL_DM_GLS_CMP AS GA_AGG_CHNNL_DM_GLS_CMP_TAB))) new_version
    ON (
          old_version.ga_date                           = new_version.ga_date AND
          old_version.profile_id                        = new_version.profile_id AND
          old_version.SOURCE                            = new_version.SOURCE AND
          old_version.keyword                           = new_version.keyword AND
          old_version.medium                            = new_version.medium AND
          old_version.campaign                          = new_version.campaign AND
          old_version.country_code                      = new_version.country_code AND
          old_version.trading_type                      = new_version.trading_type AND
          old_version.channel                           = new_version.channel
    )
    WHEN MATCHED THEN
    UPDATE SET
              old_version.ga_date_utc = new_version.ga_date_utc,
              old_version.logical_load_timestamp = new_version.logical_load_timestamp,
              old_version.updated_by = new_version.updated_by,
              old_version.update_timestamp = new_version.update_timestamp,
              old_version.goal_1_completions = new_version.goal_1_completions,
              old_version.goal_6_completions = new_version.goal_6_completions,
              old_version.goal_8_completions = new_version.goal_8_completions,
              old_version.goal_9_completions = new_version.goal_9_completions,
              old_version.goal_13_completions = new_version.goal_13_completions,
              old_version.Demo_App_Verifies = new_version.Demo_App_Verifies,
              old_version.Demo_App_Submits = new_version.Demo_App_Submits
    WHEN NOT MATCHED THEN
    INSERT  (
              old_version.profile_id,
              old_version.ga_date,
              old_version.ga_date_utc,
              old_version.logical_load_timestamp,
              old_version.created_by,
              old_version.create_timestamp,
              old_version.updated_by,
              old_version.update_timestamp,
              old_version.campaign,
              old_version.source,
              old_version.medium,
              old_version.keyword,
              old_version.trading_type,
              old_version.country_code,
              old_version.channel,
              old_version.goal_1_completions,
              old_version.goal_6_completions,
              old_version.goal_8_completions,
              old_version.goal_9_completions,
              old_version.goal_13_completions,
              old_version.Demo_App_Verifies,
              old_version.Demo_App_Submits)
    VALUES (
              new_version.profile_id,
              new_version.ga_date,
              new_version.ga_date_utc,
              new_version.logical_load_timestamp,
              new_version.created_by,
              new_version.create_timestamp,
              new_version.updated_by,
              new_version.update_timestamp,
              new_version.campaign,
              new_version.source,
              new_version.medium,
              new_version.keyword,
              new_version.trading_type,
              new_version.country_code,
              new_version.channel,
              new_version.goal_1_completions,
              new_version.goal_6_completions,
              new_version.goal_8_completions,
              new_version.goal_9_completions,
              new_version.goal_13_completions,
              new_version.Demo_App_Verifies,
              new_version.Demo_App_Submits);



    MERGE INTO   GA_AGG_CHANNEL_LIVE_GOALS_STR  old_version
    USING  (SELECT
                  profile_id,
                  ga_date,
                  ga_date ga_date_utc,
                  lv_logical_load_timestamp logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  campaign,
                  RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1)))SOURCE,
                  RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
                  keyword,
                  trading_type,
                  country_code,
                  channel,
                  goal_2_starts,
                  goal_3_starts,
                  goal_7_starts,
                  goal_10_starts,
                  goal_11_starts,
                  goal_12_starts,
                  Live_App_Starts,
                  pre_app_views
           FROM TABLE(CAST(P_GA_AGG_CHNNL_LV_GLS_STR AS GA_AGG_CHNNL_LV_GLS_STR_TAB))) new_version
    ON (
          old_version.ga_date                           = new_version.ga_date AND
          old_version.profile_id                        = new_version.profile_id AND
          old_version.SOURCE                            = new_version.SOURCE AND
          old_version.keyword                           = new_version.keyword AND
          old_version.medium                            = new_version.medium AND
          old_version.campaign                          = new_version.campaign AND
          old_version.country_code                      = new_version.country_code  AND
          old_version.trading_type                      = new_version.trading_type AND
          old_version.channel                           = new_version.channel
    )
    WHEN MATCHED THEN
    UPDATE SET
              old_version.ga_date_utc = new_version.ga_date_utc,
              old_version.logical_load_timestamp = new_version.logical_load_timestamp,
              old_version.updated_by = new_version.updated_by,
              old_version.update_timestamp = new_version.update_timestamp,
              old_version.goal_2_starts = new_version.goal_2_starts,
              old_version.goal_3_starts = new_version.goal_3_starts,
              old_version.goal_7_starts = new_version.goal_7_starts,
              old_version.goal_10_starts = new_version.goal_10_starts,
              old_version.goal_11_starts = new_version.goal_11_starts,
              old_version.goal_12_starts = new_version.goal_12_starts,
              old_version.Live_App_Starts = new_version.Live_App_Starts,
              old_version.pre_app_views = new_version.pre_app_views
    WHEN NOT MATCHED THEN
    INSERT  (
              old_version.profile_id,
              old_version.ga_date,
              old_version.ga_date_utc,
              old_version.logical_load_timestamp,
              old_version.created_by,
              old_version.create_timestamp,
              old_version.updated_by,
              old_version.update_timestamp,
              old_version.campaign,
              old_version.source,
              old_version.medium,
              old_version.keyword,
              old_version.trading_type,
              old_version.country_code,
              old_version.channel,
              old_version.goal_2_starts,
              old_version.goal_3_starts,
              old_version.goal_7_starts,
              old_version.goal_10_starts,
              old_version.goal_11_starts,
              old_version.goal_12_starts,
              old_version.Live_App_Starts,
              old_version.pre_app_views)
    VALUES (
              new_version.profile_id,
              new_version.ga_date,
              new_version.ga_date_utc,
              new_version.logical_load_timestamp,
              new_version.created_by,
              new_version.create_timestamp,
              new_version.updated_by,
              new_version.update_timestamp,
              new_version.campaign,
              new_version.source,
              new_version.medium,
              new_version.keyword,
              new_version.trading_type,
              new_version.country_code,
              new_version.channel,
              new_version.goal_2_starts,
              new_version.goal_3_starts,
              new_version.goal_7_starts,
              new_version.goal_10_starts,
              new_version.goal_11_starts,
              new_version.goal_12_starts,
              new_version.Live_App_Starts,
              new_version.pre_app_views);


  EXCEPTION
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_google_analytics;

  -- ===================================================================================
  -- put_google_analytics_v2
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Writes the google analytics data to ods
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_google_analytics_v2( p_user                           IN VARCHAR2,
                                     p_ga_date                        IN DATE,
                                     p_profiles                       IN ga_profile_tab,
                                     p_ga_app_goals_1                 IN ga_app_goals_1_tab,
                                     p_ga_app_goals_2                 IN ga_app_goals_2_tab,
                                     p_ga_app_goals_3                 IN ga_app_goals_3_tab,
                                     p_ga_app_goals_4                 IN ga_app_goals_4_tab,
                                     p_ga_app_goals_5                 IN ga_app_goals_5_tab,
                                     p_ga_live_app_events_by_source   IN ga_lv_app_events_by_source_tab,
                                     p_ga_demo_app_events_by_source   IN ga_dm_app_events_by_source_tab,
                                     p_ga_live_app_events_by_cor      IN ga_live_app_events_by_cor_tab,
                                     p_ga_demo_app_events_by_cor      IN ga_demo_app_events_by_cor_tab,
                                     p_ga_summary_1                   IN ga_summary_1_tab,
                                     p_ga_summary_2                   IN ga_summary_2_tab,
                                     p_ga_summary_3                   IN ga_summary_3_tab,
                                     p_ga_summary_4                   IN ga_summary_4_tab,
                                     p_ga_pagepaths_1                 IN ga_pagepaths_1_tab,
                                     p_ga_pagepaths_2                 IN ga_pagepaths_2_tab,
                                     p_ga_pagepaths_3                 IN ga_pagepaths_3_tab,
                                     p_ga_pagepaths_4                 IN ga_pagepaths_4_tab,
                                     p_ga_referrals_1                 IN ga_referrals_1_tab,
                                     p_ga_referrals_2                 IN ga_referrals_2_tab,
                                     p_ga_referrals_3                 IN ga_referrals_3_tab,
                                     p_ga_referrals_4                 IN ga_referrals_4_tab,
                                     p_ga_os_1                        IN ga_os_1_tab,
                                     p_ga_os_2                        IN ga_os_2_tab,
                                     p_ga_os_3                        IN ga_os_3_tab,
                                     p_ga_os_4                        IN ga_os_4_tab,
                                     p_ga_location_1                  IN ga_location_1_tab,
                                     p_ga_location_2                  IN ga_location_2_tab,
                                     p_ga_location_3                  IN ga_location_3_tab,
                                     p_ga_location_4                  IN ga_location_4_tab,
                                     p_ga_adwords                     IN ga_adwords_tab,
                                     p_ga_clicks                      IN ga_clicks_tab,
                                     p_ga_adjust_apps                 IN ga_adjust_apps_tab,
                                     p_ga_adjust_apps_by_source       IN ga_adjust_apps_by_source_tab,
                                     p_ga_demo_app_gclid              IN ga_demo_app_gclid_tab,
                                     p_ga_live_app_gclid              IN ga_live_app_gclid_tab,
                                     p_ga_start                       IN ga_start_tab,
                                     p_ga_topline_detail              IN ga_topline_detail_tab,
                                     p_ga_topline_daily               IN ga_topline_daily_tab,
                                     p_ga_topline_monthly             IN ga_topline_monthly_tab,
                                     p_ga_adjust_apps_start           IN ga_adjust_apps_tab
                                     ) IS

    lv_logical_load_timestamp TIMESTAMP(6);

  BEGIN

    logger.logger.set_module('put_google_analytics_V2');

    lv_logical_load_timestamp := SYSTIMESTAMP;
    --
    --Delete Existing data from tables
    --

    DELETE ga_app_goals WHERE ga_date = p_ga_date;
    DELETE ga_live_app_events_by_source WHERE ga_date = p_ga_date;
    DELETE ga_demo_app_events_by_source WHERE ga_date = p_ga_date;
    DELETE ga_live_app_events_by_cor WHERE ga_date = p_ga_date;
    DELETE ga_demo_app_events_by_cor WHERE ga_date = p_ga_date;
    DELETE ga_summary WHERE start_date = p_ga_date and period = 'Daily';
    DELETE ga_summary WHERE start_date = trunc(next_day (p_ga_date,'SUNDAY')-14) and period = 'Weekly';
    DELETE ga_summary WHERE start_date = cast (trunc(trunc(p_ga_date,'MM')- 1,'MM') as date) and period = 'Monthly';
    DELETE ga_pagepaths WHERE ga_date = p_ga_date;
    DELETE ga_referrals WHERE ga_date = p_ga_date;
    DELETE ga_os WHERE ga_date = p_ga_date;
    DELETE ga_location WHERE ga_date = p_ga_date;
    DELETE ga_adwords WHERE ga_date = p_ga_date;
    DELETE ga_clicks WHERE ga_date = p_ga_date;
    DELETE ga_adjust_apps WHERE ga_date = p_ga_date;
    DELETE ga_adjust_apps_by_source WHERE ga_date = p_ga_date;
    DELETE ga_demo_app_gclid WHERE ga_date = p_ga_date;
    DELETE ga_live_app_gclid WHERE ga_date = p_ga_date;
    DELETE ga_start WHERE ga_date = p_ga_date;
    DELETE ga_topline_detail WHERE ga_date = p_ga_date;
    DELETE ga_topline_daily WHERE ga_date = p_ga_date;
    DELETE ga_topline_monthly WHERE ga_date = cast (trunc(trunc(p_ga_date,'MM')- 1,'MM') as date);



    --
    --Insert new data
    --

    INSERT INTO ga_app_goals( profile_id,
                              ga_date,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              campaign,
                              source,
                              medium,
                              keyword,
                              dimension5,
                              dimension9,
                              dimension11,
                              goal_1_starts,
                              goal_2_starts,
                              goal_3_starts,
                              goal_4_starts,
                              goal_5_starts,
                              goal_6_starts,
                              goal_7_starts,
                              goal_8_starts,
                              goal_9_starts,
                              goal_10_starts,
                              goal_11_starts,
                              goal_12_starts,
                              goal_13_starts,
                              goal_14_starts,
                              goal_15_starts,
                              goal_16_starts,
                              goal_17_starts,
                              goal_18_starts,
                              goal_19_starts,
                              goal_20_starts,
                              goal_1_completions,
                              goal_2_completions,
                              goal_3_completions,
                              goal_4_completions,
                              goal_5_completions,
                              goal_6_completions,
                              goal_7_completions,
                              goal_8_completions,
                              goal_9_completions,
                              goal_10_completions,
                              goal_11_completions,
                              goal_12_completions,
                              goal_13_completions,
                              goal_14_completions,
                              goal_15_completions,
                              goal_16_completions,
                              goal_17_completions,
                              goal_18_completions,
                              goal_19_completions,
                              goal_20_completions,
                              metric1,
                              metric2,
                              metric3,
                              metric4,
                              metric5,
                              metric6,
                              metric7,
                              metric8,
                              metric9)
                    SELECT  NVL(A.profile_id,NVL(b.profile_id,NVL(c.profile_id,nvl(d.profile_id,e.profile_id)))),
                            NVL(A.ga_date,NVL(b.ga_date,NVL(c.ga_date,nvl(d.ga_date,e.ga_date))))ga_date,
                            lv_logical_load_timestamp,
                            p_user,
                            SYSTIMESTAMP,
                            p_user,
                            SYSTIMESTAMP,
                            NVL(A.campaign,NVL(b.campaign,NVL(c.campaign,nvl(d.campaign,e.campaign))))campaign,
                            NVL(rtrim(ltrim(SUBSTR(A.source_medium, 1, instr(A.source_medium,'/')-1))),NVL(rtrim(ltrim(SUBSTR(b.source_medium, 1, instr(b.source_medium,'/')-1))),NVL(rtrim(ltrim(SUBSTR(c.source_medium, 1, instr(c.source_medium,'/')-1))),nvl(rtrim(ltrim(SUBSTR(d.source_medium, 1, instr(d.source_medium,'/')-1))),rtrim(ltrim(SUBSTR(e.source_medium, 1, instr(e.source_medium,'/')-1)))))))SOURCE,
                            NVL(rtrim(ltrim(SUBSTR(A.source_medium, instr(A.source_medium,'/')   +1, LENGTH(A.source_medium)))),NVL(rtrim(ltrim(SUBSTR(b.source_medium, instr(b.source_medium,'/')+1, LENGTH(b.source_medium)))),NVL(rtrim(ltrim(SUBSTR(c.source_medium, instr(c.source_medium,'/')+1, LENGTH(c.source_medium)))),nvl(rtrim(ltrim(SUBSTR(d.source_medium, instr(d.source_medium,'/')+1, LENGTH(d.source_medium)))),rtrim(ltrim(SUBSTR(e.source_medium, instr(e.source_medium,'/')+1, LENGTH(e.source_medium))))))))medium,
                            NVL(A.keyword,NVL(b.keyword,NVL(c.keyword,nvl(d.keyword,e.keyword))))keyword,
                            NVL(rtrim(ltrim(SUBSTR(A.dimension19, instr(A.dimension19,'_')   +1, LENGTH(A.dimension19)))),NVL(rtrim(ltrim(SUBSTR(b.dimension19, instr(b.dimension19,'_')+1, LENGTH(b.dimension19)))),NVL(rtrim(ltrim(SUBSTR(c.dimension19, instr(c.dimension19,'_')+1, LENGTH(c.dimension19)))),nvl(rtrim(ltrim(SUBSTR(d.dimension19, instr(d.dimension19,'_')+1, LENGTH(d.dimension19)))),rtrim(ltrim(SUBSTR(e.dimension19, instr(e.dimension19,'_')+1, LENGTH(e.dimension19))))))))dimension5,
                            NVL(rtrim(ltrim(SUBSTR(A.dimension19, 1, instr(A.dimension19,'_')-1))),NVL(rtrim(ltrim(SUBSTR(b.dimension19, 1, instr(b.dimension19,'_')-1))),NVL(rtrim(ltrim(SUBSTR(c.dimension19, 1, instr(c.dimension19,'_')-1))),nvl(rtrim(ltrim(SUBSTR(d.dimension19, 1, instr(d.dimension19,'_')-1))),rtrim(ltrim(SUBSTR(e.dimension19, 1, instr(e.dimension19,'_')-1)))))))dimension9,
                            NVL(A.dimension11,NVL(b.dimension11,NVL(c.dimension11,nvl(d.dimension11,e.dimension11))))dimension11,
                            nvl(goal_1_starts,0),
                            nvl(goal_2_starts,0),
                            nvl(goal_3_starts,0),
                            nvl(goal_4_starts,0),
                            nvl(goal_5_starts,0),
                            nvl(goal_6_starts,0),
                            nvl(goal_7_starts,0),
                            nvl(goal_8_starts,0),
                            nvl(goal_9_starts,0),
                            nvl(goal_10_starts,0),
                            nvl(goal_11_starts,0),
                            nvl(goal_12_starts,0),
                            nvl(goal_13_starts,0),
                            nvl(goal_14_starts,0),
                            nvl(goal_15_starts,0),
                            nvl(goal_16_starts,0),
                            nvl(goal_17_starts,0),
                            nvl(goal_18_starts,0),
                            nvl(goal_19_starts,0),
                            nvl(goal_20_starts,0),
                            nvl(goal_1_completions,0),
                            nvl(goal_2_completions,0),
                            nvl(goal_3_completions,0),
                            nvl(goal_4_completions,0),
                            nvl(goal_5_completions,0),
                            nvl(goal_6_completions,0),
                            nvl(goal_7_completions,0),
                            nvl(goal_8_completions,0),
                            nvl(goal_9_completions,0),
                            nvl(goal_10_completions,0),
                            nvl(goal_11_completions,0),
                            nvl(goal_12_completions,0),
                            nvl(goal_13_completions,0),
                            nvl(goal_14_completions,0),
                            nvl(goal_15_completions,0),
                            nvl(goal_16_completions,0),
                            nvl(goal_17_completions,0),
                            nvl(goal_18_completions,0),
                            nvl(goal_19_completions,0),
                            nvl(goal_20_completions,0),
                            nvl(metric1,0),
                            nvl(metric2,0),
                            nvl(metric3,0),
                            nvl(metric4,0),
                            nvl(metric5,0),
                            nvl(metric6,0),
                            nvl(metric7,0),
                            nvl(metric8,0),
                            nvl(metric9,0)
                          FROM TABLE(CAST(p_ga_app_goals_1 AS ga_app_goals_1_tab))A
                          FULL OUTER JOIN TABLE(CAST(p_ga_app_goals_2 AS ga_app_goals_2_tab)) b
                          ON ( A.profile_id   = b.profile_id
                          AND A.ga_date       = b.ga_date
                          AND A.campaign      = b.campaign
                          AND A.source_medium = b.source_medium
                          AND A.keyword       = b.keyword
                          AND A.dimension19   = b.dimension19
                          AND A.dimension11   = b.dimension11)
                          FULL OUTER JOIN TABLE(CAST(p_ga_app_goals_3 AS ga_app_goals_3_tab)) c
                          ON ( A.profile_id   = c.profile_id
                          AND A.ga_date       = c.ga_date
                          AND A.campaign      = c.campaign
                          AND A.source_medium = c.source_medium
                          AND A.keyword       = c.keyword
                          AND A.dimension19   = c.dimension19
                          AND A.dimension11   = c.dimension11)
                          FULL OUTER JOIN TABLE(CAST(p_ga_app_goals_4 AS ga_app_goals_4_tab)) d
                          ON ( A.profile_id   = d.profile_id
                          AND A.ga_date       = d.ga_date
                          AND A.campaign      = d.campaign
                          AND A.source_medium = d.source_medium
                          AND A.keyword       = d.keyword
                          AND A.dimension19   = d.dimension19
                          AND A.dimension11   = d.dimension11)
                          FULL OUTER JOIN TABLE(CAST(p_ga_app_goals_5 AS ga_app_goals_5_tab)) e
                          ON ( A.profile_id   = e.profile_id
                          AND A.ga_date       = e.ga_date
                          AND A.campaign      = e.campaign
                          AND A.source_medium = e.source_medium
                          AND A.keyword       = e.keyword
                          AND A.dimension19   = e.dimension19
                          AND A.dimension11   = e.dimension11);

    INSERT INTO ga_live_app_events_by_source( profile_id,
                                              ga_date,
                                              logical_load_timestamp,
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              campaign,
                                              source,
                                              medium,
                                              keyword,
                                              dimension5,
                                              dimension9,
                                              dimension2,
                                              total_events,
                                              unique_events,
                                              device_category)
                                      SELECT  profile_id,
                                              ga_date,
                                              lv_logical_load_timestamp,
                                              p_user,
                                              SYSTIMESTAMP,
                                              p_user,
                                              SYSTIMESTAMP,
                                              campaign,
                                              RTRIM(LTRIM(substr(source_medium, 1, instr(source_medium,'/')-1))),
                                              RTRIM(LTRIM(substr(source_medium, instr(source_medium,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
                                              keyword,
                                              RTRIM(LTRIM(substr(dimension19, instr(dimension19,'_')+1, LENGTH(dimension19))))dimension5,
                                              rtrim(ltrim(SUBSTR(dimension19, 1, instr(dimension19,'_')-1))),
                                              dimension2,
                                              nvl(total_events,0),
                                              nvl(unique_events,0),
                                              device_category
                                      FROM TABLE(CAST(p_ga_live_app_events_by_source AS ga_lv_app_events_by_source_tab));

    INSERT INTO ga_demo_app_events_by_source ( profile_id,
                                              ga_date,
                                              logical_load_timestamp,
                                              created_by,
                                              create_timestamp,
                                              updated_by,
                                              update_timestamp,
                                              campaign,
                                              source,
                                              medium,
                                              keyword,
                                              dimension5,
                                              dimension9,
                                              dimension7,
                                              total_events,
                                              unique_events,
                                              device_category)
                                      SELECT  profile_id,
                                              ga_date,
                                              lv_logical_load_timestamp,
                                              p_user,
                                              SYSTIMESTAMP,
                                              p_user,
                                              SYSTIMESTAMP,
                                              campaign,
                                              RTRIM(LTRIM(substr(source_medium, 1, instr(source_medium,'/')-1))),
                                              RTRIM(LTRIM(substr(source_medium, instr(source_medium,'/')+1, LENGTH(SOURCE_MEDIUM))))medium,
                                              keyword,
                                              RTRIM(LTRIM(substr(dimension19, instr(dimension19,'_')+1, LENGTH(dimension19))))dimension5,
                                              rtrim(ltrim(SUBSTR(dimension19, 1, instr(dimension19,'_')-1)))dimension9,
                                              dimension7,
                                              nvl(total_events,0),
                                              nvl(unique_events,0),
                                              device_category
                                      FROM TABLE(CAST(p_ga_demo_app_events_by_source AS ga_dm_app_events_by_source_tab));

    INSERT INTO ga_live_app_events_by_cor(profile_id,
                                          ga_date,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          dimension5,
                                          dimension9,
                                          dimension11,
                                          dimension2,
                                          total_events,
                                          unique_events,
                                          landing_page_path)
                                  SELECT  profile_id,
                                          ga_date,
                                          lv_logical_load_timestamp,
                                          p_user,
                                          SYSTIMESTAMP,
                                          p_user,
                                          SYSTIMESTAMP,
                                          RTRIM(LTRIM(substr(dimension19, instr(dimension19,'_')+1, LENGTH(dimension19)))),
                                          rtrim(ltrim(SUBSTR(dimension19, 1, instr(dimension19,'_')-1))),
                                          dimension11,
                                          dimension2,
                                          nvl(total_events,0),
                                          nvl(unique_events,0),
                                          landing_page_path
                                FROM TABLE(CAST(p_ga_live_app_events_by_cor AS ga_live_app_events_by_cor_tab));

    INSERT INTO ga_demo_app_events_by_cor (profile_id,
                                          ga_date,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          dimension5,
                                          dimension9,
                                          dimension11,
                                          dimension7,
                                          total_events,
                                          unique_events,
                                          landing_page_path)
                                  SELECT  profile_id,
                                          ga_date,
                                          lv_logical_load_timestamp,
                                          p_user,
                                          SYSTIMESTAMP,
                                          p_user,
                                          SYSTIMESTAMP,
                                          RTRIM(LTRIM(substr(dimension19, instr(dimension19,'_')+1, LENGTH(dimension19)))),
                                          rtrim(ltrim(SUBSTR(dimension19, 1, instr(dimension19,'_')-1))),
                                          dimension11,
                                          dimension7,
                                          nvl(total_events,0),
                                          nvl(unique_events,0),
                                          landing_page_path
                                FROM TABLE(CAST(p_ga_demo_app_events_by_cor AS ga_demo_app_events_by_cor_tab));

    INSERT INTO ga_summary (profile_id,
                            start_date,
                            period,
                            logical_load_timestamp,
                            created_by,
                            create_timestamp,
                            updated_by,
                            update_timestamp,
                            campaign,
                            source,
                            medium,
                            keyword,
                            dimension5,
                            dimension9,
                            user_type,
                            dimension8,
                            device_category,
                            users,
                            new_users,
                            sessions,
                            session_duration,
                            entrances,
                            exits,
                            bounces,
                            page_views,
                            total_events,
                            unique_events,
                            goal_1_completions,
                            goal_2_completions,
                            goal_3_completions,
                            goal_4_completions,
                            goal_5_completions,
                            goal_6_completions,
                            goal_7_completions,
                            goal_8_completions,
                            goal_9_completions,
                            goal_10_completions,
                            goal_11_completions,
                            goal_12_completions,
                            goal_13_completions,
                            goal_14_completions,
                            goal_15_completions,
                            goal_16_completions,
                            goal_17_completions,
                            goal_18_completions,
                            goal_19_completions,
                            goal_20_completions,
                            metric1,
                            metric2,
                            metric3,
                            metric4,
                            metric5,
                            metric6,
                            metric7,
                            metric8,
                            metric9
                            )
                    SELECT  NVL(A.profile_id,NVL(b.profile_id,NVL(c.profile_id,d.profile_id))),
                            NVL(A.start_date,NVL(b.start_date,NVL(c.start_date,d.start_date)))start_date,
                            NVL(A.period,NVL(b.period,NVL(c.period,d.period)))period,
                            lv_logical_load_timestamp,
                            p_user,
                            SYSTIMESTAMP,
                            p_user,
                            SYSTIMESTAMP,
                            NVL(A.campaign,NVL(b.campaign,NVL(c.campaign,d.campaign)))campaign,
                            NVL(rtrim(ltrim(SUBSTR(A.source_medium, 1, instr(A.source_medium,'/')-1))),NVL(rtrim(ltrim(SUBSTR(b.source_medium, 1, instr(b.source_medium,'/')-1))),NVL(rtrim(ltrim(SUBSTR(c.source_medium, 1, instr(c.source_medium,'/')-1))),rtrim(ltrim(SUBSTR(d.source_medium, 1, instr(d.source_medium,'/')-1))))))SOURCE,
                            NVL(rtrim(ltrim(SUBSTR(A.source_medium, instr(A.source_medium,'/')   +1, LENGTH(A.source_medium)))),NVL(rtrim(ltrim(SUBSTR(b.source_medium, instr(b.source_medium,'/')+1, LENGTH(b.source_medium)))),NVL(rtrim(ltrim(SUBSTR(c.source_medium, instr(c.source_medium,'/')+1, LENGTH(c.source_medium)))),rtrim(ltrim(SUBSTR(d.source_medium, instr(d.source_medium,'/')+1, LENGTH(d.source_medium)))))))medium,
                            NVL(A.keyword,NVL(b.keyword,NVL(c.keyword,d.keyword)))keyword,
                            NVL(rtrim(ltrim(SUBSTR(A.dimension19, instr(A.dimension19,'_')   +1, LENGTH(A.dimension19)))),NVL(rtrim(ltrim(SUBSTR(b.dimension19, instr(b.dimension19,'_')+1, LENGTH(b.dimension19)))),NVL(rtrim(ltrim(SUBSTR(c.dimension19, instr(c.dimension19,'_')+1, LENGTH(c.dimension19)))),rtrim(ltrim(SUBSTR(d.dimension19, instr(d.dimension19,'_')+1, LENGTH(d.dimension19)))))))dimension5,
                            NVL(rtrim(ltrim(SUBSTR(A.dimension19, 1, instr(A.dimension19,'_')-1))),NVL(rtrim(ltrim(SUBSTR(b.dimension19, 1, instr(b.dimension19,'_')-1))),NVL(rtrim(ltrim(SUBSTR(c.dimension19, 1, instr(c.dimension19,'_')-1))),rtrim(ltrim(SUBSTR(d.dimension19, 1, instr(d.dimension19,'_')-1))))))dimension9,
                            NVL(A.user_type,NVL(b.user_type,NVL(c.user_type,d.user_type)))user_type,
                            NVL(A.dimension8,NVL(b.dimension8,NVL(c.dimension8,d.dimension8)))dimension8,
                            NVL(A.device_category,NVL(b.device_category,NVL(c.device_category,d.device_category)))device_category,
                            nvl(users,0),
                            nvl(new_users,0),
                            nvl(sessions,0),
                            nvl(session_duration,0),
                            nvl(entrances,0),
                            nvl(exits,0),
                            nvl(bounces,0),
                            nvl(page_views,0),
                            nvl(total_events,0),
                            nvl(unique_events,0),
                            nvl(goal_1_completions,0),
                            nvl(goal_2_completions,0),
                            nvl(goal_3_completions,0),
                            nvl(goal_4_completions,0),
                            nvl(goal_5_completions,0),
                            nvl(goal_6_completions,0),
                            nvl(goal_7_completions,0),
                            nvl(goal_8_completions,0),
                            nvl(goal_9_completions,0),
                            nvl(goal_10_completions,0),
                            nvl(goal_11_completions,0),
                            nvl(goal_12_completions,0),
                            nvl(goal_13_completions,0),
                            nvl(goal_14_completions,0),
                            nvl(goal_15_completions,0),
                            nvl(goal_16_completions,0),
                            nvl(goal_17_completions,0),
                            nvl(goal_18_completions,0),
                            nvl(goal_19_completions,0),
                            nvl(goal_20_completions,0),
                            nvl(metric1,0),
                            nvl(metric2,0),
                            nvl(metric3,0),
                            nvl(metric4,0),
                            nvl(metric5,0),
                            nvl(metric6,0),
                            nvl(metric7,0),
                            nvl(metric8,0),
                            nvl(metric9,0)
                    FROM TABLE(CAST(p_ga_summary_1 AS ga_summary_1_tab)) A
                          FULL OUTER JOIN TABLE(CAST(p_ga_summary_2 AS ga_summary_2_tab)) b
                          ON (a.profile_id          = b.profile_id
                              AND A.start_date      = b.start_date
                              AND A.period          = b.period
                              AND A.campaign        = b.campaign
                              AND A.source_medium   = b.source_medium
                              AND A.keyword         = b.keyword
                              AND A.dimension19     = b.dimension19
                              AND A.user_type       = b.user_type
                              AND A.dimension8      = b.dimension8
                              AND A.device_category = b.device_category)
                          FULL OUTER JOIN TABLE(CAST(p_ga_summary_3 AS ga_summary_3_tab)) c
                          ON( a.profile_id          = c.profile_id
                              AND A.start_date      = c.start_date
                              AND A.period          = c.period
                              AND A.campaign        = c.campaign
                              AND A.source_medium   = c.source_medium
                              AND A.keyword         = c.keyword
                              AND A.dimension19     = c.dimension19
                              AND A.user_type       = c.user_type
                              AND A.dimension8      = c.dimension8
                              AND A.device_category = c.device_category)
                          FULL OUTER JOIN TABLE(CAST(p_ga_summary_4 AS ga_summary_4_tab)) d
                          ON( a.profile_id          = d.profile_id
                              AND  A.start_date     = d.start_date
                              AND A.period          = d.period
                              AND A.campaign        = d.campaign
                              AND A.source_medium   = d.source_medium
                              AND A.keyword         = d.keyword
                              AND A.dimension19     = d.dimension19
                              AND A.user_type       = d.user_type
                              AND A.dimension8      = d.dimension8
                              AND A.device_category = d.device_category);

    INSERT INTO ga_pagepaths( profile_id,
                              ga_date,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              campaign,
                              SOURCE,
                              medium,
                              dimension5,
                              dimension9,
                              page_path,
                              exit_page_path,
                              users,
                              new_users,
                              sessions,
                              session_duration,
                              entrances,
                              exits,
                              bounces,
                              page_views,
                              total_events,
                              unique_events,
                              unique_page_views,
                              goal_1_completions,
                              goal_2_completions,
                              goal_3_completions,
                              goal_4_completions,
                              goal_5_completions,
                              goal_6_completions,
                              goal_7_completions,
                              goal_8_completions,
                              goal_9_completions,
                              goal_10_completions,
                              goal_11_completions,
                              goal_12_completions,
                              goal_13_completions,
                              goal_14_completions,
                              goal_15_completions,
                              goal_16_completions,
                              goal_17_completions,
                              goal_18_completions,
                              goal_19_completions,
                              goal_20_completions,
                              metric1,
                              metric2,
                              metric3,
                              metric4,
                              metric5,
                              metric6,
                              metric7,
                              metric8,
                              metric9)
                      SELECT  nvl(A.profile_id,nvl(b.profile_id,nvl(c.profile_id,d.profile_id))),
                              nvl(A.ga_date,nvl(b.ga_date,nvl(c.ga_date,d.ga_date)))ga_date,
                              lv_logical_load_timestamp,
                              p_user,
                              SYSTIMESTAMP,
                              p_user,
                              SYSTIMESTAMP,
                              nvl(A.campaign,nvl(b.campaign,nvl(c.campaign,d.campaign)))campaign,
                              nvl(rtrim(ltrim(substr(A.source_medium, 1, instr(A.source_medium,'/')-1))),nvl(rtrim(ltrim(substr(b.source_medium, 1, instr(b.source_medium,'/')-1))),nvl(rtrim(ltrim(substr(c.source_medium, 1, instr(c.source_medium,'/')-1))),rtrim(ltrim(substr(d.source_medium, 1, instr(d.source_medium,'/')-1))))))SOURCE,
                              nvl(rtrim(ltrim(substr(A.source_medium, instr(A.source_medium,'/')   +1, LENGTH(A.source_medium)))),nvl(rtrim(ltrim(substr(b.source_medium, instr(b.source_medium,'/')+1, LENGTH(b.source_medium)))),nvl(rtrim(ltrim(substr(c.source_medium, instr(c.source_medium,'/')+1, LENGTH(c.source_medium)))),rtrim(ltrim(substr(d.source_medium, instr(d.source_medium,'/')+1, LENGTH(d.source_medium)))))))medium,
                              nvl(rtrim(ltrim(substr(A.dimension19, instr(A.dimension19,'_')       +1, LENGTH(A.dimension19)))),nvl(rtrim(ltrim(substr(b.dimension19, instr(b.dimension19,'_')+1, LENGTH(b.dimension19)))),nvl(rtrim(ltrim(substr(c.dimension19, instr(c.dimension19,'_')+1, LENGTH(c.dimension19)))),rtrim(ltrim(substr(d.dimension19, instr(d.dimension19,'_')+1, LENGTH(d.dimension19)))))))dimension5,
                              nvl(rtrim(ltrim(substr(A.dimension19, 1, instr(A.dimension19,'_')    -1))),nvl(rtrim(ltrim(substr(b.dimension19, 1, instr(b.dimension19,'_')-1))),nvl(rtrim(ltrim(substr(c.dimension19, 1, instr(c.dimension19,'_')-1))),rtrim(ltrim(substr(d.dimension19, 1, instr(d.dimension19,'_')-1))))))dimension9,
                              nvl(A.page_path,nvl(b.page_path,nvl(c.page_path,d.page_path)))page_path,
                              nvl(A.exit_page_path,nvl(b.exit_page_path,nvl(c.exit_page_path,d.exit_page_path)))exit_page_path,
                              nvl(users,0),
                              nvl(new_users,0),
                              nvl(sessions,0),
                              nvl(session_duration,0),
                              nvl(entrances,0),
                              nvl(exits,0),
                              nvl(bounces,0),
                              nvl(page_views,0),
                              nvl(total_events,0),
                              nvl(unique_events,0),
                              nvl(unique_page_views,0),
                              nvl(goal_1_completions,0),
                              nvl(goal_2_completions,0),
                              nvl(goal_3_completions,0),
                              nvl(goal_4_completions,0),
                              nvl(goal_5_completions,0),
                              nvl(goal_6_completions,0),
                              nvl(goal_7_completions,0),
                              nvl(goal_8_completions,0),
                              nvl(goal_9_completions,0),
                              nvl(goal_10_completions,0),
                              nvl(goal_11_completions,0),
                              nvl(goal_12_completions,0),
                              nvl(goal_13_completions,0),
                              nvl(goal_14_completions,0),
                              nvl(goal_15_completions,0),
                              nvl(goal_16_completions,0),
                              nvl(goal_17_completions,0),
                              nvl(goal_18_completions,0),
                              nvl(goal_19_completions,0),
                              nvl(goal_20_completions,0),
                              nvl(metric1,0),
                              nvl(metric2,0),
                              nvl(metric3,0),
                              nvl(metric4,0),
                              nvl(metric5,0),
                              nvl(metric6,0),
                              nvl(metric7,0),
                              nvl(metric8,0),
                              nvl(metric9,0)
                            FROM TABLE(CAST(p_ga_pagepaths_1 AS ga_pagepaths_1_tab)) A
                            FULL OUTER JOIN TABLE(CAST(p_ga_pagepaths_2 AS ga_pagepaths_2_tab)) b
                            ON ( A.ga_date       = b.ga_date
                                AND A.profile_id     = b.profile_id
                                AND A.campaign       = b.campaign
                                AND A.source_medium  = b.source_medium
                                AND A.page_path      = b.page_path
                                AND A.dimension19    = b.dimension19
                                AND A.exit_page_path = b.exit_page_path)
                            FULL OUTER JOIN TABLE(CAST(p_ga_pagepaths_3 AS ga_pagepaths_3_tab)) c
                            ON( A.ga_date        = c.ga_date
                                AND A.profile_id     = c.profile_id
                                AND A.campaign       = c.campaign
                                AND A.source_medium  = c.source_medium
                                AND A.page_path      = c.page_path
                                AND A.dimension19    = c.dimension19
                                AND A.exit_page_path = c.exit_page_path)
                            FULL OUTER JOIN TABLE(CAST(p_ga_pagepaths_4 AS ga_pagepaths_4_tab)) d
                            ON( A.ga_date        = d.ga_date
                                AND A.profile_id     = d.profile_id
                                AND A.campaign       = d.campaign
                                AND A.source_medium  = d.source_medium
                                AND A.page_path      = d.page_path
                                AND A.dimension19    = d.dimension19
                                AND A.exit_page_path = d.exit_page_path);

    INSERT INTO ga_referrals (profile_id,
                              ga_date,
                              logical_load_timestamp,
                              created_by,
                              create_timestamp,
                              updated_by,
                              update_timestamp,
                              campaign,
                              source,
                              medium,
                              dimension5,
                              dimension9,
                              referral_path,
                              second_page_path,
                              users,
                              new_users,
                              sessions,
                              session_duration,
                              entrances,
                              exits,
                              bounces,
                              page_views,
                              total_events,
                              unique_events,
                              goal_1_completions,
                              goal_2_completions,
                              goal_3_completions,
                              goal_4_completions,
                              goal_5_completions,
                              goal_6_completions,
                              goal_7_completions,
                              goal_8_completions,
                              goal_9_completions,
                              goal_10_completions,
                              goal_11_completions,
                              goal_12_completions,
                              goal_13_completions,
                              goal_14_completions,
                              goal_15_completions,
                              goal_16_completions,
                              goal_17_completions,
                              goal_18_completions,
                              goal_19_completions,
                              goal_20_completions,
                              metric1,
                              metric2,
                              metric3,
                              metric4,
                              metric5,
                              metric6,
                              metric7,
                              metric8,
                              metric9
                              )
                      SELECT  nvl(A.profile_id,nvl(b.profile_id,nvl(c.profile_id,d.profile_id))),
                              nvl(A.ga_date,nvl(b.ga_date,nvl(c.ga_date,d.ga_date)))ga_date,
                              lv_logical_load_timestamp,
                              p_user,
                              SYSTIMESTAMP,
                              p_user,
                              SYSTIMESTAMP,
                              nvl(A.campaign,nvl(b.campaign,nvl(c.campaign,d.campaign)))campaign,
                              nvl(rtrim(ltrim(substr(A.source_medium, 1, instr(A.source_medium,'/')-1))),nvl(rtrim(ltrim(substr(b.source_medium, 1, instr(b.source_medium,'/')-1))),nvl(rtrim(ltrim(substr(c.source_medium, 1, instr(c.source_medium,'/')-1))),rtrim(ltrim(substr(d.source_medium, 1, instr(d.source_medium,'/')-1))))))SOURCE,
                              nvl(rtrim(ltrim(substr(A.source_medium, instr(A.source_medium,'/')   +1, LENGTH(A.source_medium)))),nvl(rtrim(ltrim(substr(b.source_medium, instr(b.source_medium,'/')+1, LENGTH(b.source_medium)))),nvl(rtrim(ltrim(substr(c.source_medium, instr(c.source_medium,'/')+1, LENGTH(c.source_medium)))),rtrim(ltrim(substr(d.source_medium, instr(d.source_medium,'/')+1, LENGTH(d.source_medium)))))))medium,
                              nvl(rtrim(ltrim(substr(A.dimension19, instr(A.dimension19,'_')       +1, LENGTH(A.dimension19)))),nvl(rtrim(ltrim(substr(b.dimension19, instr(b.dimension19,'_')+1, LENGTH(b.dimension19)))),nvl(rtrim(ltrim(substr(c.dimension19, instr(c.dimension19,'_')+1, LENGTH(c.dimension19)))),rtrim(ltrim(substr(d.dimension19, instr(d.dimension19,'_')+1, LENGTH(d.dimension19)))))))dimension5,
                              nvl(rtrim(ltrim(substr(A.dimension19, 1, instr(A.dimension19,'_')    -1))),nvl(rtrim(ltrim(substr(b.dimension19, 1, instr(b.dimension19,'_')-1))),nvl(rtrim(ltrim(substr(c.dimension19, 1, instr(c.dimension19,'_')-1))),rtrim(ltrim(substr(d.dimension19, 1, instr(d.dimension19,'_')-1))))))dimension9,
                              nvl(A.referral_path,nvl(b.referral_path,nvl(c.referral_path,d.referral_path)))referral_path,
                              nvl(A.second_page_path,nvl(b.second_page_path,nvl(c.second_page_path,d.second_page_path)))second_page_path,
                              nvl(users,0),
                              nvl(new_users,0),
                              nvl(sessions,0),
                              nvl(session_duration,0),
                              nvl(entrances,0),
                              nvl(exits,0),
                              nvl(bounces,0),
                              nvl(page_views,0),
                              nvl(total_events,0),
                              nvl(unique_events,0),
                              nvl(goal_1_completions,0),
                              nvl(goal_2_completions,0),
                              nvl(goal_3_completions,0),
                              nvl(goal_4_completions,0),
                              nvl(goal_5_completions,0),
                              nvl(goal_6_completions,0),
                              nvl(goal_7_completions,0),
                              nvl(goal_8_completions,0),
                              nvl(goal_9_completions,0),
                              nvl(goal_10_completions,0),
                              nvl(goal_11_completions,0),
                              nvl(goal_12_completions,0),
                              nvl(goal_13_completions,0),
                              nvl(goal_14_completions,0),
                              nvl(goal_15_completions,0),
                              nvl(goal_16_completions,0),
                              nvl(goal_17_completions,0),
                              nvl(goal_18_completions,0),
                              nvl(goal_19_completions,0),
                              nvl(goal_20_completions,0),
                              nvl(metric1,0),
                              nvl(metric2,0),
                              nvl(metric3,0),
                              nvl(metric4,0),
                              nvl(metric5,0),
                              nvl(metric6,0),
                              nvl(metric7,0),
                              nvl(metric8,0),
                              nvl(metric9,0)
                      FROM TABLE(CAST(p_ga_referrals_1 AS ga_referrals_1_tab)) A
                           FULL OUTER JOIN TABLE(CAST(p_ga_referrals_2 AS ga_referrals_2_tab)) b
                           ON ( A.ga_date         = b.ga_date
                                AND A.profile_id       = b.profile_id
                                AND A.campaign         = b.campaign
                                AND A.source_medium    = b.source_medium
                                AND A.referral_path    = b.referral_path
                                AND A.dimension19      = b.dimension19
                                AND A.second_page_path = b.second_page_path)
                           FULL OUTER JOIN TABLE(CAST(p_ga_referrals_3 AS ga_referrals_3_tab)) c
                           ON( A.ga_date          = c.ga_date
                                AND A.profile_id       = c.profile_id
                                AND A.campaign         = c.campaign
                                AND A.source_medium    = c.source_medium
                                AND A.referral_path    = c.referral_path
                                AND A.dimension19      = c.dimension19
                                AND A.second_page_path = c.second_page_path)
                           FULL OUTER JOIN TABLE(CAST(p_ga_referrals_4 AS ga_referrals_4_tab)) d
                           ON( A.ga_date          = d.ga_date
                                AND A.profile_id       = d.profile_id
                                AND A.campaign         = d.campaign
                                AND A.source_medium    = d.source_medium
                                AND A.referral_path    = d.referral_path
                                AND A.dimension19      = d.dimension19
                                AND A.second_page_path = d.second_page_path);

    INSERT INTO ga_os (profile_id,
                      ga_date,
                      logical_load_timestamp,
                      created_by,
                      create_timestamp,
                      updated_by,
                      update_timestamp,
                      dimension5,
                      dimension9,
                      dimension8,
                      device_category,
                      operating_system,
                      browser,
                      users,
                      new_users,
                      sessions,
                      session_duration,
                      entrances,
                      exits,
                      bounces,
                      page_views,
                      total_events,
                      unique_events,
                      goal_1_completions,
                      goal_2_completions,
                      goal_3_completions,
                      goal_4_completions,
                      goal_5_completions,
                      goal_6_completions,
                      goal_7_completions,
                      goal_8_completions,
                      goal_9_completions,
                      goal_10_completions,
                      goal_11_completions,
                      goal_12_completions,
                      goal_13_completions,
                      goal_14_completions,
                      goal_15_completions,
                      goal_16_completions,
                      goal_17_completions,
                      goal_18_completions,
                      goal_19_completions,
                      goal_20_completions,
                      metric1,
                      metric2,
                      metric3,
                      metric4,
                      metric5,
                      metric6,
                      metric7,
                      metric8,
                      metric9
                      )
              SELECT  nvl(A.profile_id,nvl(b.profile_id,nvl(c.profile_id,d.profile_id))),
                      nvl(A.ga_date,nvl(b.ga_date,nvl(c.ga_date,d.ga_date)))ga_date,
                      lv_logical_load_timestamp,
                      p_user,
                      SYSTIMESTAMP,
                      p_user,
                      SYSTIMESTAMP,
                      nvl(rtrim(ltrim(substr(A.dimension19, instr(A.dimension19,'_') +1, LENGTH(A.dimension19)))),nvl(rtrim(ltrim(substr(b.dimension19, instr(b.dimension19,'_')+1, LENGTH(b.dimension19)))),nvl(rtrim(ltrim(substr(c.dimension19, instr(c.dimension19,'_')+1, LENGTH(c.dimension19)))),rtrim(ltrim(substr(d.dimension19, instr(d.dimension19,'_')+1, LENGTH(d.dimension19)))))))dimension5,
                      nvl(rtrim(ltrim(substr(A.dimension19, 1, instr(A.dimension19,'_') -1))),nvl(rtrim(ltrim(substr(b.dimension19, 1, instr(b.dimension19,'_')-1))),nvl(rtrim(ltrim(substr(c.dimension19, 1, instr(c.dimension19,'_')-1))),rtrim(ltrim(substr(d.dimension19, 1, instr(d.dimension19,'_')-1))))))dimension9,
                      nvl(A.dimension8,nvl(b.dimension8,nvl(c.dimension8,d.dimension8))),
                      nvl(A.device_category,nvl(b.device_category,nvl(c.device_category,d.device_category))),
                      nvl(A.operating_system,nvl(b.operating_system,nvl(c.operating_system,d.operating_system))),
                      nvl(A.browser,nvl(b.browser,nvl(c.browser,d.browser))),
                      NVL(users,0),
                      nvl(new_users,0),
                      nvl(sessions,0),
                      nvl(session_duration,0),
                      nvl(entrances,0),
                      nvl(exits,0),
                      nvl(bounces,0),
                      nvl(page_views,0),
                      nvl(total_events,0),
                      nvl(unique_events,0),
                      nvl(goal_1_completions,0),
                      nvl(goal_2_completions,0),
                      nvl(goal_3_completions,0),
                      nvl(goal_4_completions,0),
                      nvl(goal_5_completions,0),
                      nvl(goal_6_completions,0),
                      nvl(goal_7_completions,0),
                      nvl(goal_8_completions,0),
                      nvl(goal_9_completions,0),
                      nvl(goal_10_completions,0),
                      nvl(goal_11_completions,0),
                      nvl(goal_12_completions,0),
                      nvl(goal_13_completions,0),
                      nvl(goal_14_completions,0),
                      nvl(goal_15_completions,0),
                      nvl(goal_16_completions,0),
                      nvl(goal_17_completions,0),
                      nvl(goal_18_completions,0),
                      nvl(goal_19_completions,0),
                      nvl(goal_20_completions,0),
                      nvl(metric1,0),
                      nvl(metric2,0),
                      nvl(metric3,0),
                      nvl(metric4,0),
                      nvl(metric5,0),
                      nvl(metric6,0),
                      nvl(metric7,0),
                      nvl(metric8,0),
                      nvl(metric9,0)
                    FROM TABLE(CAST(p_ga_os_1 AS ga_os_1_tab)) A
                    FULL OUTER JOIN TABLE(CAST(p_ga_os_2 AS ga_os_2_tab)) b
                    ON (A.profile_id       = b.profile_id
                    AND A.ga_date          = b.ga_date
                    AND A.dimension19      = b.dimension19
                    AND A.dimension8       = b.dimension8
                    AND A.device_category  = b.device_category
                    AND A.operating_system = b.operating_system
                    AND A.browser          = b.browser )
                    FULL OUTER JOIN TABLE(CAST(p_ga_os_3 AS ga_os_3_tab)) c
                    ON( A.profile_id       = c.profile_id
                    AND A.ga_date          = c.ga_date
                    AND A.dimension19      = c.dimension19
                    AND A.dimension8       = c.dimension8
                    AND A.device_category  = c.device_category
                    AND A.operating_system = c.operating_system
                    AND A.browser          = c.browser )
                    FULL OUTER JOIN TABLE(CAST(p_ga_os_4 AS ga_os_4_tab)) d
                    ON(A.profile_id        = d.profile_id
                    AND A.ga_date          = d.ga_date
                    AND A.dimension19      = d.dimension19
                    AND A.dimension8       = d.dimension8
                    AND A.device_category  = d.device_category
                    AND A.operating_system = d.operating_system
                    AND A.browser          = d.browser);

    INSERT INTO ga_location (profile_id,
                            ga_date,
                            logical_load_timestamp,
                            created_by,
                            create_timestamp,
                            updated_by,
                            update_timestamp,
                            dimension5,
                            dimension9,
                            dimension8,
                            device_category,
                            longitude,
                            latitude,
                            users,
                            new_users,
                            sessions,
                            session_duration,
                            entrances,
                            exits,
                            bounces,
                            page_views,
                            total_events,
                            unique_events,
                            goal_1_completions,
                            goal_2_completions,
                            goal_3_completions,
                            goal_4_completions,
                            goal_5_completions,
                            goal_6_completions,
                            goal_7_completions,
                            goal_8_completions,
                            goal_9_completions,
                            goal_10_completions,
                            goal_11_completions,
                            goal_12_completions,
                            goal_13_completions,
                            goal_14_completions,
                            goal_15_completions,
                            goal_16_completions,
                            goal_17_completions,
                            goal_18_completions,
                            goal_19_completions,
                            goal_20_completions,
                            metric1,
                            metric2,
                            metric3,
                            metric4,
                            metric5,
                            metric6,
                            metric7,
                            metric8,
                            metric9
                            )
                    SELECT  NVL(A.profile_id,NVL(b.profile_id,NVL(c.profile_id,d.profile_id))),
                            NVL(A.ga_date,NVL(b.ga_date,NVL(c.ga_date,d.ga_date)))ga_date,
                            lv_logical_load_timestamp,
                            p_user,
                            SYSTIMESTAMP,
                            p_user,
                            SYSTIMESTAMP,
                            NVL(rtrim(ltrim(SUBSTR(A.dimension19, instr(A.dimension19,'_')    +1, LENGTH(A.dimension19)))),NVL(rtrim(ltrim(SUBSTR(b.dimension19, instr(b.dimension19,'_')+1, LENGTH(b.dimension19)))),NVL(rtrim(ltrim(SUBSTR(c.dimension19, instr(c.dimension19,'_')+1, LENGTH(c.dimension19)))),rtrim(ltrim(SUBSTR(d.dimension19, instr(d.dimension19,'_')+1, LENGTH(d.dimension19)))))))dimension5,
                            NVL(rtrim(ltrim(SUBSTR(A.dimension19, 1, instr(A.dimension19,'_') -1))),NVL(rtrim(ltrim(SUBSTR(b.dimension19, 1, instr(b.dimension19,'_')-1))),NVL(rtrim(ltrim(SUBSTR(c.dimension19, 1, instr(c.dimension19,'_')-1))),rtrim(ltrim(SUBSTR(d.dimension19, 1, instr(d.dimension19,'_')-1))))))dimension9,
                            NVL(A.dimension8,NVL(b.dimension8,NVL(c.dimension8,d.dimension8))),
                            NVL(A.device_category,NVL(b.device_category,NVL(c.device_category,d.device_category))),
                            NVL(A.longitude,NVL(b.longitude,NVL(c.longitude,d.longitude))),
                            NVL(A.latitude,NVL(b.latitude,NVL(c.latitude,d.latitude))),
                            NVL(users,0),
                            nvl(new_users,0),
                            nvl(sessions,0),
                            nvl(session_duration,0),
                            nvl(entrances,0),
                            nvl(exits,0),
                            nvl(bounces,0),
                            nvl(page_views,0),
                            nvl(total_events,0),
                            nvl(unique_events,0),
                            nvl(goal_1_completions,0),
                            nvl(goal_2_completions,0),
                            nvl(goal_3_completions,0),
                            nvl(goal_4_completions,0),
                            nvl(goal_5_completions,0),
                            nvl(goal_6_completions,0),
                            nvl(goal_7_completions,0),
                            nvl(goal_8_completions,0),
                            nvl(goal_9_completions,0),
                            nvl(goal_10_completions,0),
                            nvl(goal_11_completions,0),
                            nvl(goal_12_completions,0),
                            nvl(goal_13_completions,0),
                            nvl(goal_14_completions,0),
                            nvl(goal_15_completions,0),
                            nvl(goal_16_completions,0),
                            nvl(goal_17_completions,0),
                            nvl(goal_18_completions,0),
                            nvl(goal_19_completions,0),
                            nvl(goal_20_completions,0),
                            nvl(metric1,0),
                            nvl(metric2,0),
                            nvl(metric3,0),
                            nvl(metric4,0),
                            nvl(metric5,0),
                            nvl(metric6,0),
                            nvl(metric7,0),
                            nvl(metric8,0),
                            nvl(metric9,0)
                    FROM TABLE(CAST(p_ga_location_1 AS ga_location_1_tab)) A
                          FULL OUTER JOIN TABLE(CAST(p_ga_location_2 AS ga_location_2_tab)) b
                          ON (A.profile_id      = b.profile_id
                              AND A.ga_date         = b.ga_date
                              AND A.dimension19     = b.dimension19
                              AND A.dimension8      = b.dimension8
                              AND A.device_category = b.device_category
                              AND A.longitude        = b.longitude
                              AND A.latitude        = b.latitude)
                          FULL OUTER JOIN TABLE(CAST(p_ga_location_3 AS ga_location_3_tab)) c
                          ON( A.profile_id      = c.profile_id
                              AND A.ga_date         = c.ga_date
                              AND A.dimension19     = c.dimension19
                              AND A.dimension8      = c.dimension8
                              AND A.device_category = c.device_category
                              AND A.longitude        = c.longitude
                              AND A.latitude        = c.latitude)
                          FULL OUTER JOIN TABLE(CAST(p_ga_location_4 AS ga_location_4_tab)) d
                          ON(A.profile_id       = d.profile_id
                              AND A.ga_date         = d.ga_date
                              AND A.dimension19     = d.dimension19
                              AND A.dimension8      = d.dimension8
                              AND A.device_category = d.device_category
                              AND A.longitude        = d.longitude
                              AND A.latitude        = d.latitude );


    INSERT INTO ga_adwords(profile_id,
                          ga_date,
                          logical_load_timestamp,
                          created_by,
                          create_timestamp,
                          updated_by,
                          update_timestamp,
                          campaign,
                          source,
                          medium,
                          keyword,
                          device_category,
                          time_on_page,
                          ad_cost,
                          ad_clicks,
                          impressions,
                          adwords_customer_id,
                          ad_keyword_match_type)
                 SELECT   profile_id,
                          ga_date,
                          lv_logical_load_timestamp,
                          p_user,
                          SYSTIMESTAMP,
                          p_user,
                          SYSTIMESTAMP,
                          campaign,
                          rtrim(ltrim(substr(source_medium, 1, instr(source_medium,'/')-1))),
                          rtrim(ltrim(substr(source_medium, instr(source_medium,'/')   +1, LENGTH(source_medium)))),
                          keyword,
                          device_category,
                          nvl(time_on_page,0),
                          nvl(ad_cost,0),
                          nvl(ad_clicks,0),
                          nvl(impressions,0),
                          adwords_customer_id,
                          ad_keyword_match_type
                  FROM TABLE(CAST(p_ga_adwords AS ga_adwords_tab));

    --
    --GA_CLICKS
    --

    INSERT INTO ga_clicks(profile_id,
                          ga_date,
                          event_action,
                          click_id,
                          source,
                          medium,
                          page_path,
                          logical_load_timestamp,
                          created_by,
                          create_timestamp,
                          updated_by,
                          update_timestamp,
                          total_events,
                          unique_dimension_combinations,
                          campaign,
                          keyword)
                   SELECT profile_id,
                          p_ga_date ga_date,
                          event_action,
                          click_id,
                          RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1))) source,
                          RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM)))) medium,
                          page_path,
                          lv_logical_load_timestamp logical_load_timestamp,
                          p_user created_by,
                          SYSTIMESTAMP create_timestamp,
                          p_user updated_by,
                          SYSTIMESTAMP update_timestamp,
                          total_events,
                          unique_dimension_combinations,
                          campaign,
                          keyword
                  FROM TABLE(CAST(p_ga_clicks AS ga_clicks_tab));

    --
    --GA_ADJUST_APPS
    --
    INSERT INTO ga_adjust_apps(profile_id,
                               ga_date,
                               ga_hour,
                               ga_minute,
                               event_action,
                               event_category,
                               event_label,
                               page_path,
                               logical_load_timestamp,
                               created_by,
                               create_timestamp,
                               updated_by,
                               update_timestamp,
                               identity_id,
                               application_id,
                               click_id,
                               total_events,
                               unique_dimension_combinations)
                        SELECT profile_id,
                               p_ga_date ga_date,
                               ga_hour,
                               ga_minute,
                               event_action,
                               event_category,
                               event_label,
                               page_path,
                               lv_logical_load_timestamp logical_load_timestamp,
                               p_user created_by,
                               SYSTIMESTAMP create_timestamp,
                               p_user updated_by,
                               SYSTIMESTAMP update_timestamp,
                               CASE WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                 NULL -- string must contain a _
                               ELSE
                                 REGEXP_SUBSTR(event_label, '^\d[^_]+') -- must start with a number. Find everything up to the _
                               END identity_id,
                               CASE WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                 NULL -- string must contain a _
                               ELSE
                                 REGEXP_SUBSTR(event_label, '^[[:alpha:]][^_]+') -- must start with a character. Find everything up to the _
                               END application_id,
                               CASE
                                 WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                   EVENT_LABEL -- if string does not contain a _ then take it
                                 WHEN REGEXP_SUBSTR(event_label, '^_') = '_' THEN
                                   REGEXP_SUBSTR(event_label, '[^_]+', 1, 1) -- if it starts with _ then remove the _
                                 ELSE
                                   REGEXP_SUBSTR(event_label, '[^_]+', 1, 2) -- else take everything after the underscore
                               END click_id,
                               total_events,
                               unique_dimension_combinations
                        FROM TABLE(CAST(p_ga_adjust_apps AS ga_adjust_apps_tab));

    --
    --GA_ADJUST_APPS_BY_SOURCE
    --

    INSERT INTO  ga_adjust_apps_by_source(profile_id,
                                          ga_date,
                                          ga_hour,
                                          ga_minute,
                                          event_label,
                                          source,
                                          medium,
                                          logical_load_timestamp,
                                          created_by,
                                          create_timestamp,
                                          updated_by,
                                          update_timestamp,
                                          identity_id,
                                          application_id,
                                          click_id,
                                          unique_events,
                                          unique_dimension_combinations,
                                          campaign,
                                          keyword)
                                   SELECT profile_id,
                                          p_ga_date ga_date,
                                          ga_hour,
                                          ga_minute,
                                          event_label,
                                          RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1))) source,
                                          RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM)))) medium,
                                          lv_logical_load_timestamp logical_load_timestamp,
                                          p_user created_by,
                                          SYSTIMESTAMP create_timestamp,
                                          p_user updated_by,
                                          SYSTIMESTAMP update_timestamp,
                                          CASE
                                            WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                              NULL -- string must contain a _
                                            ELSE
                                              REGEXP_SUBSTR(event_label, '^\d[^_]+') -- must start with a number. Find everything up to the _
                                          END identity_id,
                                          CASE
                                            WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                              NULL -- string must contain a _
                                            ELSE
                                              REGEXP_SUBSTR(event_label, '^[[:alpha:]][^_]+') -- must start with a character. Find everything up to the _
                                          END application_id,
                                          CASE
                                            WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                              EVENT_LABEL -- if string does not contain a _ then take it
                                            WHEN REGEXP_SUBSTR(event_label, '^_') = '_' THEN
                                              REGEXP_SUBSTR(event_label, '[^_]+', 1, 1) -- if it starts with _ then remove the _
                                            ELSE
                                              REGEXP_SUBSTR(event_label, '[^_]+', 1, 2) -- else take everything after the underscore
                                          END click_id,
                                          unique_events,
                                          unique_dimension_combinations,
                                          campaign,
                                          keyword
                                  FROM TABLE(CAST(p_ga_adjust_apps_by_source AS ga_adjust_apps_by_source_tab));
    INSERT INTO ga_demo_app_gclid(profile_id,
                                  ga_date,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  dimension7,
                                  dimension15,
                                  page_views)
                          SELECT  profile_id,
                                  p_ga_date,
                                  lv_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  dimension7,
                                  dimension15,
                                  page_views
                          FROM TABLE(CAST(p_ga_demo_app_gclid AS ga_demo_app_gclid_tab));

    INSERT INTO ga_live_app_gclid(profile_id,
                                  ga_date,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  dimension2,
                                  dimension15,
                                  page_views)
                          SELECT  profile_id,
                                  p_ga_date,
                                  lv_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  dimension2,
                                  dimension15,
                                  page_views
                          FROM TABLE(CAST(p_ga_live_app_gclid AS ga_live_app_gclid_tab));

    INSERT INTO ga_start(profile_id,
                         ga_date,
                         logical_load_timestamp,
                         created_by,
                         create_timestamp,
                         updated_by,
                         update_timestamp,
                         event_action,
                         event_category,
                         event_label,
                         campaign,
                         source,
                         medium,
                         keyword,
                         total_events,
                         unique_events)
                  select profile_id,
                         p_ga_date ga_date,
                         lv_logical_load_timestamp,
                         p_user,
                         SYSTIMESTAMP,
                         p_user,
                         SYSTIMESTAMP,
                         event_action,
                         event_category,
                         event_label,
                         campaign,
                         RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1))) source,
                         RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM)))) medium,
                         keyword,
                         total_events,
                         unique_events
                    FROM TABLE(CAST(p_ga_start AS ga_start_tab));

    INSERT INTO ga_topline_detail(profile_id,
                                  ga_date,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  campaign,
                                  source,
                                  medium,
                                  keyword,
                                  user_type,
                                  device_category,
                                  sessions,
                                  bounces,
                                  session_duration,
                                  page_views,
                                  users,
                                  new_users,
                                  goal_12_completions,
                                  goal_13_completions)
                          select  profile_id,
                                  p_ga_date ga_date,
                                  lv_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  campaign,
                                  RTRIM(LTRIM(substr(SOURCE_MEDIUM, 1, instr(SOURCE_MEDIUM,'/')-1))) source,
                                  RTRIM(LTRIM(substr(SOURCE_MEDIUM, instr(SOURCE_MEDIUM,'/')+1, LENGTH(SOURCE_MEDIUM)))) medium,
                                  keyword,
                                  user_type,
                                  device_category,
                                  sessions,
                                  bounces,
                                  session_duration,
                                  page_views,
                                  users,
                                  new_users,
                                  goal_12_completions,
                                  goal_13_completions
                             FROM TABLE(CAST(p_ga_topline_detail AS ga_topline_detail_tab));

    INSERT INTO ga_topline_daily(profile_id,
                                  ga_date,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  users)
                          select  profile_id,
                                  p_ga_date ga_date,
                                  lv_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  users
                             FROM TABLE(CAST(p_ga_topline_daily AS ga_topline_daily_tab));


    INSERT INTO ga_topline_monthly(profile_id,
                                  ga_date,
                                  logical_load_timestamp,
                                  created_by,
                                  create_timestamp,
                                  updated_by,
                                  update_timestamp,
                                  users)
                          select  profile_id,
                                  start_date ga_date,
                                  lv_logical_load_timestamp,
                                  p_user,
                                  SYSTIMESTAMP,
                                  p_user,
                                  SYSTIMESTAMP,
                                  users
                             FROM TABLE(CAST(p_ga_topline_monthly AS ga_topline_monthly_tab));

    INSERT INTO ga_adjust_apps(profile_id,
                               ga_date,
                               ga_hour,
                               ga_minute,
                               event_action,
                               event_category,
                               event_label,
                               page_path,
                               logical_load_timestamp,
                               created_by,
                               create_timestamp,
                               updated_by,
                               update_timestamp,
                               identity_id,
                               application_id,
                               click_id,
                               total_events,
                               unique_dimension_combinations)
                        SELECT profile_id,
                               p_ga_date ga_date,
                               ga_hour,
                               ga_minute,
                               event_action,
                               event_category,
                               event_label,
                               page_path,
                               lv_logical_load_timestamp logical_load_timestamp,
                               p_user created_by,
                               SYSTIMESTAMP create_timestamp,
                               p_user updated_by,
                               SYSTIMESTAMP update_timestamp,
                               CASE WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                 NULL -- string must contain a _
                               ELSE
                                 REGEXP_SUBSTR(event_label, '^\d[^_]+') -- must start with a number. Find everything up to the _
                               END identity_id,
                               CASE WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                 NULL -- string must contain a _
                               ELSE
                                 REGEXP_SUBSTR(event_label, '^[[:alpha:]][^_]+') -- must start with a character. Find everything up to the _
                               END application_id,
                               CASE
                                 WHEN EVENT_LABEL NOT LIKE '%\_%' ESCAPE '\' THEN
                                   EVENT_LABEL -- if string does not contain a _ then take it
                                 WHEN REGEXP_SUBSTR(event_label, '^_') = '_' THEN
                                   REGEXP_SUBSTR(event_label, '[^_]+', 1, 1) -- if it starts with _ then remove the _
                                 ELSE
                                   REGEXP_SUBSTR(event_label, '[^_]+', 1, 2) -- else take everything after the underscore
                               END click_id,
                               total_events,
                               unique_dimension_combinations
                        FROM TABLE(CAST(p_ga_adjust_apps_start AS ga_adjust_apps_tab));


  EXCEPTION
  WHEN others THEN
    logger.logger.SEVERE(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END;
END nrg_google_analytics;
//