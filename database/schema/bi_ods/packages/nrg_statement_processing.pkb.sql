
  CREATE OR REPLACE EDITIONABLE PACKAGE BODY "BI_ODS"."NRG_STATEMENT_PROCESSING" 
AS
  -- ===================================================================================
  -- NRG_STATEMENT_PROCESSING
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Package created to insert/update processed statements in statement service.
  --     The data is published by NRG.
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --   ----------   ---------------   -----   ----------------------------------------
  --     29/07/2015   Adam Krasnicki   1.0    Creation
  --     22/09/2015   Sanket Mittal    1.1    Updated to add partner_id
  --
  -- ===================================================================================
  --------------------------------------------------------------------------------------
  -- GLOBAL VARIABLES
  --------------------------------------------------------------------------------------
  gc_version NUMBER := 1.1;
  gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');


  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC

    IS

    BEGIN
      logger.logger.set_module('version');
      RETURN gc_version;
    EXCEPTION
      WHEN OTHERS THEN
        logger.logger.SEVERE(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        RAISE;
    END version;
  -- ===================================================================================
  -- get_full_year_start_date
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the full year start date from BKO
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION get_full_year_start_date(p_profit_centre_name IN statement_processing.profit_centre_name%TYPE)
        RETURN VARCHAR2 RESULT_CACHE
   AS 
    lv_full_year_start_date VARCHAR2(30); 
    
   BEGIN
    BEGIN
      SELECT config_value INTO lv_full_year_start_date
      FROM bi_bko.bko_configuration
      WHERE config_value_type = 'FY_START_DATE'
       AND source_id_type = 'PROFIT_CENTRE'
       AND source_id = p_profit_centre_name;
   EXCEPTION
     WHEN NO_DATA_FOUND THEN
        lv_full_year_start_date := NULL;
     WHEN TOO_MANY_ROWS THEN 
        lv_full_year_start_date := NULL;
     WHEN OTHERS THEN
        lv_full_year_start_date := NULL;
    END;
   RETURN lv_full_year_start_date;
   
  END get_full_year_start_date;

  -- ===================================================================================
  -- get_date
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the date from string sent in different formats
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a DATE 
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION get_date(p_date_string IN statement_processing.Statement_Date_String%TYPE)
        RETURN DATE RESULT_CACHE
   AS 
    lv_return_date DATE; 
    
   BEGIN
    
    BEGIN
      CASE WHEN length(p_date_string)= 8 THEN
        BEGIN
         lv_return_date := to_date(p_date_string,'DDMMYYYY');
         EXCEPTION WHEN OTHERS THEN
         lv_return_date := to_date(p_date_string,'YYYYMMDD');
        END;        
      WHEN length(p_date_string)= 6 THEN
        BEGIN
         lv_return_date := to_date(p_date_string,'MMYYYY');
         EXCEPTION WHEN OTHERS THEN
         lv_return_date := to_date(p_date_string,'YYYYMM'); 
        END;
      WHEN length(p_date_string)= 4 THEN
         lv_return_date := trunc(to_date(p_date_string,'YYYY'),'YYYY');
      ELSE
         lv_return_date := NULL;
      END CASE;
    EXCEPTION
        WHEN OTHERS THEN
          lv_return_date := NULL;                  
    END;
    
   RETURN lv_return_date;
   
  END get_date;
  -- ===================================================================================
  -- put_partner
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put processed statement
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     STATEMENT_PROCESSING
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --   p_user
  --   p_effective_start_timestamp
  --   p_user
  --   p_statement_type
  --   p_statement_date
  --   p_trading_account_id
  --   p_trading_account_type
  --   p_booking_id
  --   p_modified_date
  --   p_identity_id
  --   p_profit_centre_name
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_statement_process_complete(p_user                         IN statement_processing.created_by%TYPE
                                          ,p_statement_type               IN statement_processing.statement_type%TYPE
                                          ,p_statement_date_string        IN statement_processing.Statement_Date_String%TYPE
                                          ,p_trading_account_id           IN statement_processing.trading_account_id%TYPE
                                          ,p_trading_account_type         IN statement_processing.trading_account_type%TYPE
                                          ,p_booking_id                   IN statement_processing.booking_id%TYPE
                                          ,p_modified_date                IN statement_processing.modified_date%TYPE
                                          ,p_identity_id                  IN statement_processing.identity_id%TYPE
                                          ,p_profit_centre_name           IN statement_processing.profit_centre_name%TYPE
                                          ,p_partner_id                   IN statement_processing.partner_id%TYPE
                                          ,p_customer_id                  IN statement_processing.customer_id%TYPE)
  IS
  lv_logical_load_timestamp TIMESTAMP(6):= SYSTIMESTAMP;
  lv_business_date  DATE;
  lv_reporting_date DATE;
  BEGIN
  
 

  --get business_date and reporting_date
  lv_business_date :=
            CASE WHEN p_statement_type IN ('DAILY','MARKET_DATA_TAX_INVOICE','MONTHLY') THEN get_date(p_statement_date_string)
                 WHEN p_statement_type = 'ANNUAL' THEN TO_DATE(SUBSTR(get_full_year_start_date(p_profit_centre_name),1,7) || TO_CHAR(get_date(p_statement_date_string)),'YYYY')
                 ELSE get_date(p_statement_date_string)
            END;
       
           
  lv_reporting_date := CASE WHEN p_statement_type IN ('DAILY','MARKET_DATA_TAX_INVOICE') THEN Nrg_Common.get_reporting_date(lv_business_date)
                            ELSE lv_business_date
                       END;
                       
                       
    --
    --Create Partner Stub
    --
    
    IF p_partner_id IS NOT NULL THEN
      nrg_partner.create_partner_stub(p_user                         => p_user,
                                      p_logical_load_timestamp       => lv_logical_load_timestamp,
                                      p_effective_start_timestamp    => gc_default_timestamp,
                                      p_partner_id                   => p_partner_id);
    END IF;


    MERGE INTO statement_processing existing_version
     USING (SELECT p_statement_type AS statement_type
                  ,p_statement_date_string AS statement_date_string
                  ,nvl(p_trading_account_id,0) AS trading_account_id
                  ,nvl(p_trading_account_type,'NA') AS trading_account_type
                  ,NVL(p_booking_id,0) AS booking_id
                  ,nvl(lv_business_date,to_date('01-JAN-1970','DD-MON-YYYY'))  AS business_date
                  ,nvl(lv_reporting_date,to_date('01-JAN-1970','DD-MON-YYYY')) AS reporting_date
                  ,p_modified_date AS modified_date
                  ,p_identity_id AS identity_id
                  ,p_profit_centre_name AS profit_centre_name
                  ,nvl(p_partner_id,0) AS partner_id
                  ,nvl(p_customer_id,0) AS customer_id
             FROM dual) new_version
      ON (existing_version.statement_type = new_version.statement_type AND
          existing_version.statement_date_string = new_version.statement_date_string AND
          existing_version.trading_account_id = new_version.trading_account_id AND
          existing_version.trading_account_type = new_version.trading_account_type AND
          existing_version.Booking_Id = new_version.booking_id AND
          existing_version.partner_id = new_version.partner_id AND
          existing_version.customer_id = new_version.customer_id)
     WHEN MATCHED THEN
        UPDATE SET existing_version.logical_load_timestamp = lv_logical_load_timestamp
              ,existing_version.updated_by = p_user
              ,existing_version.update_timestamp = lv_logical_load_timestamp
              ,existing_version.business_date = new_version.business_date
              ,existing_version.reporting_date = new_version.reporting_date
              ,existing_version.modified_date = new_version.modified_date
              ,existing_version.identity_id = new_version.identity_id
              ,existing_version.profit_centre_name = new_version.profit_centre_name
           WHERE (nrg_common.has_value_changed(existing_version.business_date, new_version.business_date) = 1 OR
                  nrg_common.has_value_changed(existing_version.reporting_date, new_version.reporting_date) = 1 OR
                  nrg_common.has_value_changed(existing_version.modified_date, new_version.modified_date) = 1 OR
                  nrg_common.has_value_changed(existing_version.identity_id, new_version.identity_id) = 1 OR
                  nrg_common.has_value_changed(existing_version.profit_centre_name, new_version.profit_centre_name) = 1)
                AND existing_version.modified_date < new_version.modified_date
      WHEN NOT MATCHED THEN
        INSERT
              (existing_version.statement_type,
               existing_version.statement_date_string,
               existing_version.trading_account_id,
               existing_version.trading_account_type,
               existing_version.booking_id,
               existing_version.logical_load_timestamp,
               existing_version.created_by,
               existing_version.create_timestamp,
               existing_version.updated_by,
               existing_version.update_timestamp,
               existing_version.business_date,
               existing_version.reporting_date,
               existing_version.modified_date,
               existing_version.identity_id,
               existing_version.profit_centre_name,
               existing_version.partner_id,
               existing_version.customer_id)
        VALUES
          (new_version.statement_type
          ,new_version.statement_date_string
          ,new_version.trading_account_id
          ,new_version.trading_account_type
          ,new_version.booking_id
          ,lv_logical_load_timestamp
          ,p_user
          ,lv_logical_load_timestamp
          ,p_user
          ,lv_logical_load_timestamp
          ,new_version.business_date
          ,new_version.reporting_date
          ,new_version.modified_date
          ,new_version.identity_id
          ,new_version.profit_centre_name
          ,new_version.partner_id
          ,new_version.customer_id);


  END;


END nrg_statement_processing;

//