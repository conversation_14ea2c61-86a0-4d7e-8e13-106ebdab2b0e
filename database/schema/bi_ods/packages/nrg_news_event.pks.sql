
  CREATE OR REPLACE EDITIONABLE PACKAGE "BI_ODS"."NRG_NEWS_EVENT" 
AS
    -- ===================================================================================
    -- NRG_NEWS_EVENT
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the news event model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     25/04/2013   Sanket Mittal     1.0     Creation
    --     21/07/2014   Adam Krasnicki    1.1     News provider parameter added
    --
    --
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

    -- ===================================================================================
    -- put_news_event
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a news event
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     news_events
    --     news_events_h
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_event_time                     Event Time
    --     p_session_key                    Session Key
    --     p_sender_identity                Sender Identity
    --     p_on_behalf_of_identity          On Behalf Of Identity
    --     p_request_id                     Request Id
    --     p_channel_id                     Channel Id
    --     p_account_function               Account Function

    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------

  PROCEDURE put_news_event(p_user                           IN news_events.created_by%TYPE,
                           p_effective_start_timestamp      IN news_events.effective_start_timestamp%TYPE,
                           p_event_time                     IN news_events.event_time%TYPE,
                           p_session_key                    IN news_events.session_key%TYPE,
                           p_sender_identity                IN news_events.sender_identity%TYPE,
                           p_on_behalf_of_identity          IN news_events.on_behalf_of_identity%TYPE,
                           p_request_id                     IN news_events.request_id%TYPE,
                           p_channel_id                     IN news_events.channel_id%TYPE,
                           p_account_function               IN news_events.account_function%TYPE,
                           p_news_provider                  IN news_events.News_Provider%TYPE);

END nrg_news_event;

//