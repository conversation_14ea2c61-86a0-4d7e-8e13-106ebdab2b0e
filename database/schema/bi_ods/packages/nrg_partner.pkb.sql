
  CREATE OR REPLACE EDITIONABLE PACKAGE BODY "BI_ODS"."NRG_PARTNER" 
AS
  -- ===================================================================================
  -- NRG_PARTNER
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of the partner model
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --   ----------   ---------------   -----   ----------------------------------------
  --     03/09/2012   Sanket Mittal      1.0    Creation
  --     28/11/2013   Adam Krasnicki     1.1    Package extension
  --     29/01/2014   Adam Krasnicki     1.2    New partners entities:
  --                                            a- put_onboarding_countries
  --                                            b- put_desk_trading_accounts
  --                                            c- put_desk_employees
  --                                            d- put_partner_desk
  --                                            e- put_partner_agents
  --     17/02/2014   Adam Krasnicki     1.3    Put_partner_agents - changed number of parameters
  --     04/03/2014   Adam Krasnicki     1.4    Changes in all partners procedures
  --     05/03/2014   Adam Krasnicki     1.4    Stbbing corrected
  --     08/04/2014   Adam Krasnicki     1.4    Put_partner, new argument p_ca_schm_cd
  --     09/05/2014   Adam Krasnicki     1.4    PK changed PARTNER_REVENUE_STREAM_H_PK
  --     05/06/2014   Adam Krasnicki     1.5    partner_desk_id goes to partner_agents, create_partner_desk_stub procedure added
  --     09/10/2014   Adam Krasnicki     1.6    4 new columns added to partners[_h] tables, new table added onboarding currencies
  --     10/02/2015   Adam Krasnicki     1.7    partner_function column added to partners[_h] and change in put_partner procedure
  --     26/06/2015   Adam Krasnicki     1.7    put_partner: logic changed to determine the profit_center
  --     08/02/2016   Adam Krasnicki     1.8    put_desk_trading_accounts: is_deleted flag added to table and object
  --                                            put_desk_agents: is_deleted flag added to table and object
  --     04/07/2016   S Kinkhabwala      1.9    Adding new parameters to put_partner and put_partner_agents
  --     03/08/2016   Sanket Mittal      2.0    put_partner_desks BER-2790 Integrate updated partner Desk data contract- Knockouts
  --     25/01/2017   Sanket Mittal      2.1    BER-3327 ODS - PARTNER - Integrate updated partner data contract
  --     18/04/2017   Sanket Mittal      2.2    BER-3535 ODS - NRG_PARTNER - Data contract changes
  --     23/04/2018   D Rajurkar         2.3    BER-4437 NRG_PARTNER - Data contract changes
  --     06/05/2019   Patrick Dinwiddy   2.4    JCS-10584 new Partner Relation objects
  --     06/05/2019   Patrick Dinwiddy   2.5    JCS-11150 MC segregation entity
  --     28/02/2020   Patrick Dinwiddy   2.6    JCS-12373 New attributes
  --     06/09/2020   Patrick Dinwiddy   2.7    JCS-13552 New attributes
  -- ===================================================================================
  --
  --
  -- ===================================================================================
  -- PACKAGE CONSTANTS
  -- ===================================================================================

  gc_version            CONSTANT VARCHAR2(3) := '2.7';
  gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
  gc_true               CONSTANT PLS_INTEGER := 1;
  gc_false              CONSTANT PLS_INTEGER := 0;
  gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

  --
  --
  -- ===================================================================================
  -- PRIVATE MODULES
  -- ===================================================================================

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the partner_mc_segregations
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for customer_tax_germany_h
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history_seg(p_action                    IN partner_mc_segregations_h.action%TYPE,
                            p_effective_end_timestamp   IN partner_mc_segregations_h.effective_end_timestamp%TYPE,
                            p_old_record                IN partner_mc_segregations%ROWTYPE) IS

  BEGIN

    INSERT INTO partner_mc_segregations_h (partner_id,
                                           segregation_id,
                                           customer_id,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           legal_entity,
                                           is_segregated)
                                   VALUES (p_old_record.partner_id,
                                           p_old_record.segregation_id,
                                           p_old_record.customer_id,
                                           p_old_record.logical_load_timestamp,
                                           p_old_record.created_by,
                                           p_old_record.create_timestamp,
                                           p_old_record.updated_by,
                                           p_old_record.update_timestamp,
                                           p_old_record.effective_start_timestamp,
                                           p_effective_end_timestamp,
                                           p_action,
                                           SYSTIMESTAMP,
                                           p_old_record.legal_entity,
                                           p_old_record.is_segregated);

  EXCEPTION WHEN DUP_VAL_ON_INDEX THEN

    UPDATE partner_mc_segregations_h
       SET logical_load_timestamp          = p_old_record.logical_load_timestamp,
           created_by                      = p_old_record.created_by,
           create_timestamp                = p_old_record.create_timestamp,
           updated_by                      = p_old_record.updated_by,
           update_timestamp                = p_old_record.update_timestamp,
           effective_end_timestamp         = p_effective_end_timestamp,
           action                          = p_action,
           action_timestamp                = SYSTIMESTAMP,
           legal_entity                    = p_old_record.legal_entity,
           is_segregated                   = p_old_record.is_segregated
     WHERE partner_id                      = p_old_record.partner_id
       AND customer_id                     = p_old_record.customer_id
       AND segregation_id                  = p_old_record.segregation_id
       AND effective_start_timestamp       = p_old_record.effective_start_timestamp;

  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_ONBOARDING_COUNTRIES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_onboarding_countries
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_onboarding_countries IN partner_onboarding_countries%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_countries_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_onboarding_countries_h (partner_id
                                              ,logical_load_timestamp
                                              ,created_by
                                              ,create_timestamp
                                              ,updated_by
                                              ,update_timestamp
                                              ,effective_start_timestamp
                                              ,effective_end_timestamp
                                              ,action
                                              ,action_timestamp
                                              ,country_code
                                              ,is_deleted)
          VALUES (p_tab_old_onboarding_countries.partner_id,
                  p_tab_old_onboarding_countries.logical_load_timestamp,
                  p_tab_old_onboarding_countries.created_by,
                  p_tab_old_onboarding_countries.create_timestamp,
                  p_tab_old_onboarding_countries.updated_by,
                  p_tab_old_onboarding_countries.update_timestamp,
                  p_tab_old_onboarding_countries.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_onboarding_countries.Country_Code,
                  p_tab_old_onboarding_countries.Is_Deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_onboarding_countries_h
      SET    logical_load_timestamp    = p_tab_old_onboarding_countries.logical_load_timestamp,
             updated_by                = p_tab_old_onboarding_countries.updated_by,
             update_timestamp          = p_tab_old_onboarding_countries.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             country_code              = p_tab_old_onboarding_countries.Country_Code,
             is_deleted                = p_tab_old_onboarding_countries.Is_Deleted
      WHERE  partner_id   = p_tab_old_onboarding_countries.partner_id
        AND  country_code = p_tab_old_onboarding_countries.country_code
        AND  effective_start_timestamp = p_tab_old_onboarding_countries.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --  Deepak Rajurkar     10-04-2018  PARTNER_KEY_INF_DOCUMENTS_H  --DR
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_KEY_INF_DOCUMENTS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_key_inf_documents
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_key_inf_doc          IN PARTNER_KEY_INF_DOCUMENTS%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN PARTNER_KEY_INF_DOCUMENTS_H.action%TYPE)

  IS
  BEGIN
     INSERT INTO PARTNER_KEY_INF_DOCUMENTS_H (partner_id
                                              ,logical_load_timestamp
                                              ,created_by
                                              ,create_timestamp
                                              ,updated_by
                                              ,update_timestamp
                                              ,effective_start_timestamp
                                              ,effective_end_timestamp
                                              ,action
                                              ,action_timestamp
                                              ,product_wrapper_code
                                              ,asset_class_code
                                              ,url
                                              ,is_deleted
                                              )
          VALUES (p_tab_old_key_inf_doc.partner_id,
                  p_tab_old_key_inf_doc.logical_load_timestamp,
                  p_tab_old_key_inf_doc.created_by,
                  p_tab_old_key_inf_doc.create_timestamp,
                  p_tab_old_key_inf_doc.updated_by,
                  p_tab_old_key_inf_doc.update_timestamp,
                  p_tab_old_key_inf_doc.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_key_inf_doc.product_wrapper_code,
                  p_tab_old_key_inf_doc.asset_class_code,
                  p_tab_old_key_inf_doc.url,
                  p_tab_old_key_inf_doc.is_deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE PARTNER_KEY_INF_DOCUMENTS_H
      SET    logical_load_timestamp    = p_tab_old_key_inf_doc.logical_load_timestamp,
             updated_by                = p_tab_old_key_inf_doc.updated_by,
             update_timestamp          = p_tab_old_key_inf_doc.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             url                       = p_tab_old_key_inf_doc.url,
             is_deleted                = p_tab_old_key_inf_doc.Is_Deleted
      WHERE  partner_id   = p_tab_old_key_inf_doc.partner_id
        AND  product_wrapper_code = p_tab_old_key_inf_doc.product_wrapper_code
        AND   asset_class_code = p_tab_old_key_inf_doc.asset_class_code
        AND   effective_start_timestamp = p_tab_old_key_inf_doc.effective_start_timestamp;

  END put_history;


  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_ONBOARDING_CURRENCY_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_onboarding_countries
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_onboarding_currency  IN partner_onboarding_currency%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_currency_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_onboarding_currency_h (partner_id
                                              ,logical_load_timestamp
                                              ,created_by
                                              ,create_timestamp
                                              ,updated_by
                                              ,update_timestamp
                                              ,effective_start_timestamp
                                              ,effective_end_timestamp
                                              ,action
                                              ,action_timestamp
                                              ,ccy_code
                                              ,is_deleted)
          VALUES (p_tab_old_onboarding_currency.partner_id,
                  p_tab_old_onboarding_currency.logical_load_timestamp,
                  p_tab_old_onboarding_currency.created_by,
                  p_tab_old_onboarding_currency.create_timestamp,
                  p_tab_old_onboarding_currency.updated_by,
                  p_tab_old_onboarding_currency.update_timestamp,
                  p_tab_old_onboarding_currency.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_onboarding_currency.Ccy_Code,
                  p_tab_old_onboarding_currency.Is_Deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_onboarding_currency_h
      SET    logical_load_timestamp    = p_tab_old_onboarding_currency.logical_load_timestamp,
             updated_by                = p_tab_old_onboarding_currency.updated_by,
             update_timestamp          = p_tab_old_onboarding_currency.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             ccy_code                  = p_tab_old_onboarding_currency.Ccy_Code,
             is_deleted                = p_tab_old_onboarding_currency.Is_Deleted
      WHERE  partner_id   = p_tab_old_onboarding_currency.partner_id
        AND  ccy_code = p_tab_old_onboarding_currency.ccy_code
        AND  effective_start_timestamp = p_tab_old_onboarding_currency.effective_start_timestamp;

  END put_history;


  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_ONBOARDING_LANGUAGES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_onboarding_languages
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_onboarding_languages IN partner_onboarding_languages%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_currency_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_onboarding_languages_h (partner_id
                                              ,logical_load_timestamp
                                              ,created_by
                                              ,create_timestamp
                                              ,updated_by
                                              ,update_timestamp
                                              ,effective_start_timestamp
                                              ,effective_end_timestamp
                                              ,action
                                              ,action_timestamp
                                              ,language_code
                                              ,is_deleted
                        ,is_prioritised)
          VALUES (p_tab_old_onboarding_languages.partner_id,
                  p_tab_old_onboarding_languages.logical_load_timestamp,
                  p_tab_old_onboarding_languages.created_by,
                  p_tab_old_onboarding_languages.create_timestamp,
                  p_tab_old_onboarding_languages.updated_by,
                  p_tab_old_onboarding_languages.update_timestamp,
                  p_tab_old_onboarding_languages.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_onboarding_languages.language_code,
                  p_tab_old_onboarding_languages.Is_Deleted,
          p_tab_old_onboarding_languages.is_prioritised) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_onboarding_languages_h
      SET    logical_load_timestamp    = p_tab_old_onboarding_languages.logical_load_timestamp,
             updated_by                = p_tab_old_onboarding_languages.updated_by,
             update_timestamp          = p_tab_old_onboarding_languages.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             language_code             = p_tab_old_onboarding_languages.language_code,
             is_deleted                = p_tab_old_onboarding_languages.Is_Deleted,
       is_prioritised            = p_tab_old_onboarding_languages.is_prioritised
      WHERE  partner_id   = p_tab_old_onboarding_languages.partner_id
        AND  language_code = p_tab_old_onboarding_languages.language_code
        AND  effective_start_timestamp = p_tab_old_onboarding_languages.effective_start_timestamp;

  END put_history;

 -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_ONBOARDING_LANGUAGES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_onboarding_languages
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_tab_old_restricted_plat_feat IN partner_restricted_plat_feat%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_currency_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_restricted_plat_feat_h (partner_id
                                              ,logical_load_timestamp
                                              ,created_by
                                              ,create_timestamp
                                              ,updated_by
                                              ,update_timestamp
                                              ,effective_start_timestamp
                                              ,effective_end_timestamp
                                              ,action
                                              ,action_timestamp
                                              ,platform_feature_name
                                              ,is_deleted)
          VALUES (p_tab_old_restricted_plat_feat.partner_id,
                  p_tab_old_restricted_plat_feat.logical_load_timestamp,
                  p_tab_old_restricted_plat_feat.created_by,
                  p_tab_old_restricted_plat_feat.create_timestamp,
                  p_tab_old_restricted_plat_feat.updated_by,
                  p_tab_old_restricted_plat_feat.update_timestamp,
                  p_tab_old_restricted_plat_feat.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_restricted_plat_feat.platform_feature_name,
                  p_tab_old_restricted_plat_feat.Is_Deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_restricted_plat_feat_h
      SET    logical_load_timestamp    = p_tab_old_restricted_plat_feat.logical_load_timestamp,
             updated_by                = p_tab_old_restricted_plat_feat.updated_by,
             update_timestamp          = p_tab_old_restricted_plat_feat.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             platform_feature_name     = p_tab_old_restricted_plat_feat.platform_feature_name,
             is_deleted                = p_tab_old_restricted_plat_feat.Is_Deleted
      WHERE  partner_id = p_tab_old_restricted_plat_feat.partner_id
        AND  platform_feature_name = p_tab_old_restricted_plat_feat.platform_feature_name
        AND  effective_start_timestamp = p_tab_old_restricted_plat_feat.effective_start_timestamp;

  END put_history;
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_DESK_EMPLOYEES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_desk_employees
  --     p_effective_end_timestamp
  --     p_partner_desk_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_desk_agents        IN partner_desk_agents%ROWTYPE,
                        p_effective_end_timestamp    IN partner_desk_agents.effective_start_timestamp%TYPE,
                        p_partner_desk_id            IN partner_desk_agents.partner_desk_id%TYPE,
                        p_action                     IN partner_desk_agents_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_desk_agents_h (partner_desk_id
                                          ,logical_load_timestamp
                                          ,created_by
                                          ,create_timestamp
                                          ,updated_by
                                          ,update_timestamp
                                          ,effective_start_timestamp
                                          ,effective_end_timestamp
                                          ,action
                                          ,action_timestamp
                                          ,partner_agent_id)
          VALUES (p_tab_old_desk_agents.partner_desk_id,
                  p_tab_old_desk_agents.logical_load_timestamp,
                  p_tab_old_desk_agents.created_by,
                  p_tab_old_desk_agents.create_timestamp,
                  p_tab_old_desk_agents.updated_by,
                  p_tab_old_desk_agents.update_timestamp,
                  p_tab_old_desk_agents.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_desk_agents.partner_agent_id) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_desk_agents_h
      SET    logical_load_timestamp    = p_tab_old_desk_agents.logical_load_timestamp,
             updated_by                = p_tab_old_desk_agents.updated_by,
             update_timestamp          = p_tab_old_desk_agents.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             partner_agent_id          = p_tab_old_desk_agents.partner_agent_id
      WHERE  partner_desk_id   = p_tab_old_desk_agents.partner_desk_id
        AND  partner_agent_id = p_tab_old_desk_agents.partner_agent_id
        AND  effective_start_timestamp = p_tab_old_desk_agents.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_DESK_TRADING_ACCOUNT_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_desk_trading_account
  --     p_effective_end_timestamp
  --     p_partner_desk_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_desk_trading_account   IN partner_desk_trading_account%ROWTYPE,
                        p_effective_end_timestamp    IN partner_desk_trading_account.effective_start_timestamp%TYPE,
                        p_partner_desk_id            IN partner_desk_trading_account.partner_desk_id%TYPE,
                        p_action                     IN partner_desk_trading_account_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_desk_trading_account_h (partner_desk_id
                                                ,logical_load_timestamp
                                                ,created_by
                                                ,create_timestamp
                                                ,updated_by
                                                ,update_timestamp
                                                ,effective_start_timestamp
                                                ,effective_end_timestamp
                                                ,action
                                                ,action_timestamp
                                                ,trading_account_id
                                                ,trading_account_type)
          VALUES (p_tab_desk_trading_account.partner_desk_id,
                  p_tab_desk_trading_account.logical_load_timestamp,
                  p_tab_desk_trading_account.created_by,
                  p_tab_desk_trading_account.create_timestamp,
                  p_tab_desk_trading_account.updated_by,
                  p_tab_desk_trading_account.update_timestamp,
                  p_tab_desk_trading_account.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_desk_trading_account.trading_account_id,
                  p_tab_desk_trading_account.trading_account_type) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_desk_trading_account_h
      SET    logical_load_timestamp    = p_tab_desk_trading_account.logical_load_timestamp,
             updated_by                = p_tab_desk_trading_account.updated_by,
             update_timestamp          = p_tab_desk_trading_account.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             trading_account_id        = p_tab_desk_trading_account.trading_account_id,
             trading_account_type      = p_tab_desk_trading_account.trading_account_type
      WHERE  partner_desk_id    = p_tab_desk_trading_account.partner_desk_id
        AND  trading_account_id = p_tab_desk_trading_account.trading_account_id
        AND  trading_account_type = p_tab_desk_trading_account.trading_account_type
        AND  effective_start_timestamp = p_tab_desk_trading_account.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_DESKS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_partner_desks          This is the old version of the partners data
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

 PROCEDURE put_history(p_tab_old_partner_desks        IN partner_desks%ROWTYPE,
                       p_effective_end_timestamp      IN partner_desks.effective_start_timestamp%TYPE,
                       p_action                       IN partner_desks_h.action%TYPE)
    IS
  BEGIN
      INSERT INTO partner_desks_h (partner_desk_id
                             ,partner_id
                             ,logical_load_timestamp
                             ,created_by
                             ,create_timestamp
                             ,updated_by
                             ,update_timestamp
                             ,effective_start_timestamp
                             ,effective_end_timestamp
                             ,action
                             ,action_timestamp
                             ,desk_name
                             ,function
                             ,is_default_desk
                             ,is_accessible_by_all_agents
                             ,is_deleted
                             ,allocation_account_id
                             ,copy_emails_to_employees)
                      VALUES(
                             p_tab_old_partner_desks.partner_desk_id,
                             p_tab_old_partner_desks.partner_id,
                             p_tab_old_partner_desks.logical_load_timestamp,
                             p_tab_old_partner_desks.created_by,
                             p_tab_old_partner_desks.create_timestamp,
                             p_tab_old_partner_desks.updated_by,
                             p_tab_old_partner_desks.update_timestamp,
                             p_tab_old_partner_desks.effective_start_timestamp,
                             p_effective_end_timestamp,
                             p_action,
                             SYSTIMESTAMP,
                             p_tab_old_partner_desks.desk_name,
                             p_tab_old_partner_desks.function,
                             p_tab_old_partner_desks.is_default_desk,
                             p_tab_old_partner_desks.is_accessible_by_all_agents,
                             p_tab_old_partner_desks.is_deleted,
                             p_tab_old_partner_desks.allocation_account_id,
                             p_tab_old_partner_desks.copy_emails_to_employees
                            );
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_desks_h
      SET    updated_by                  = p_tab_old_partner_desks.updated_by,
             update_timestamp            = p_tab_old_partner_desks.update_timestamp,
             logical_load_timestamp      = p_tab_old_partner_desks.logical_load_timestamp,
             effective_end_timestamp     = p_effective_end_timestamp,
             action                      = p_action,
             action_timestamp            = SYSTIMESTAMP,
             desk_name                   = p_tab_old_partner_desks.desk_name,
             function                    = p_tab_old_partner_desks.function,
             is_default_desk             = p_tab_old_partner_desks.is_default_desk,
             is_accessible_by_all_agents = p_tab_old_partner_desks.is_accessible_by_all_agents,
             is_deleted                  = p_tab_old_partner_desks.is_deleted,
             allocation_account_id       = p_tab_old_partner_desks.allocation_account_id,
             copy_emails_to_employees    = p_tab_old_partner_desks.copy_emails_to_employees
    WHERE    partner_desk_id = p_tab_old_partner_desks.partner_desk_id AND
             effective_start_timestamp = p_tab_old_partner_desks.Effective_Start_Timestamp AND
             (nrg_common.has_value_changed(desk_name, p_tab_old_partner_desks.desk_name) = 1 OR
             nrg_common.has_value_changed(function, p_tab_old_partner_desks.function) = 1 OR
             nrg_common.has_value_changed(is_default_desk, p_tab_old_partner_desks.is_default_desk) = 1 OR
             nrg_common.has_value_changed(is_accessible_by_all_agents, p_tab_old_partner_desks.is_accessible_by_all_agents) = 1 OR
             nrg_common.has_value_changed(is_deleted, p_tab_old_partner_desks.is_deleted) = 1 OR
             nrg_common.has_value_changed(allocation_account_id, p_tab_old_partner_desks.allocation_account_id) = 1 OR
             nrg_common.has_value_changed(copy_emails_to_employees, p_tab_old_partner_desks.copy_emails_to_employees) = 1);

 END put_history;


  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNER_AGENTS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_partner_agents          This is the old version of the partners data
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

 PROCEDURE put_history(p_tab_old_partner_agents       IN partner_agents%ROWTYPE,
                       p_effective_end_timestamp      IN partner_agents.Effective_Start_Timestamp%TYPE,
                       p_action                       IN partner_agents_h.Action%TYPE)

 IS
 BEGIN
         INSERT INTO partner_agents_h
                            (partner_agent_id
                            ,partner_id
                            ,person_id
                            ,employee_id
                            ,logical_load_timestamp
                            ,created_by
                            ,create_timestamp
                            ,updated_by
                            ,update_timestamp
                            ,effective_start_timestamp
                            ,effective_end_timestamp
                            ,action
                            ,action_timestamp
                            ,allowed_access_to_all_desks
                            ,is_deleted
                            ,primary_desk_id
                            ,person_title
                            ,spoken_language)
                      VALUES(p_tab_old_partner_agents.partner_agent_id,
                             p_tab_old_partner_agents.partner_id,
                             p_tab_old_partner_agents.person_id,
                             p_tab_old_partner_agents.employee_id,
                             p_tab_old_partner_agents.logical_load_timestamp,
                             p_tab_old_partner_agents.created_by,
                             p_tab_old_partner_agents.create_timestamp,
                             p_tab_old_partner_agents.updated_by,
                             p_tab_old_partner_agents.update_timestamp,
                             p_tab_old_partner_agents.effective_start_timestamp,
                             p_effective_end_timestamp,
                             p_action,
                             SYSTIMESTAMP,
                             p_tab_old_partner_agents.allowed_access_to_all_desks,
                             p_tab_old_partner_agents.is_deleted,
                             p_tab_old_partner_agents.primary_desk_id,
                             p_tab_old_partner_agents.person_title,
                             p_tab_old_partner_agents.spoken_language
                            );
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_agents_h
      SET    updated_by                  = p_tab_old_partner_agents.updated_by,
             update_timestamp            = p_tab_old_partner_agents.update_timestamp,
             logical_load_timestamp      = p_tab_old_partner_agents.logical_load_timestamp,
             effective_end_timestamp     = p_effective_end_timestamp,
             action                      = p_action,
             action_timestamp            = SYSTIMESTAMP,
             allowed_access_to_all_desks = p_tab_old_partner_agents.allowed_access_to_all_desks,
             is_deleted                  = p_tab_old_partner_agents.is_deleted,
             primary_desk_id             = p_tab_old_partner_agents.primary_desk_id,
            person_title              =  p_tab_old_partner_agents.person_title,
             spoken_language             = p_tab_old_partner_agents.spoken_language
    WHERE    partner_agent_id  = p_tab_old_partner_agents.partner_agent_id AND
             effective_start_timestamp = p_tab_old_partner_agents.effective_start_timestamp AND
             (nrg_common.has_value_changed(allowed_access_to_all_desks, p_tab_old_partner_agents.allowed_access_to_all_desks) = 1 OR
              nrg_common.has_value_changed(partner_id , p_tab_old_partner_agents.partner_id) = 1 OR
              nrg_common.has_value_changed(person_id , p_tab_old_partner_agents.person_id) = 1 OR
              nrg_common.has_value_changed(employee_id , p_tab_old_partner_agents.employee_id) = 1 OR
              nrg_common.has_value_changed(is_deleted, p_tab_old_partner_agents.is_deleted) = 1 OR
              nrg_common.has_value_changed(primary_desk_id, p_tab_old_partner_agents.primary_desk_id) = 1 OR
              nrg_common.has_value_changed(person_title, p_tab_old_partner_agents.person_title) = 1 OR
              nrg_common.has_value_changed(spoken_language, p_tab_old_partner_agents.spoken_language) = 1);


 END put_history;
  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNERS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_revenue_stream_confg_old_tab   This is the old version of the partners data
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE  put_history(p_revenue_stream_confg_old_tab  IN partner_revenue_stream_confg%ROWTYPE,
                         p_effective_end_timestamp       IN partner_revenue_stream_confg.Effective_Start_Timestamp%TYPE,
                         p_partner_id                    IN partner_revenue_stream_confg.partner_id%TYPE,
                         p_action                        IN partner_revenue_stream_confg_h.Action%TYPE)
  IS

  BEGIN
           INSERT INTO partner_revenue_stream_confg_h
                            (partner_id
                            ,logical_load_timestamp
                            ,created_by
                            ,create_timestamp
                            ,updated_by
                            ,update_timestamp
                            ,effective_start_timestamp
                            ,effective_end_timestamp
                            ,action
                            ,action_timestamp
                            ,revenue_type
                            ,rebate_terms
                            ,rebate_taxable
                            ,widening_taxable
                            )
                      VALUES(p_revenue_stream_confg_old_tab.partner_id,
                             p_revenue_stream_confg_old_tab.logical_load_timestamp,
                             p_revenue_stream_confg_old_tab.created_by,
                             p_revenue_stream_confg_old_tab.create_timestamp,
                             p_revenue_stream_confg_old_tab.updated_by,
                             p_revenue_stream_confg_old_tab.update_timestamp,
                             p_revenue_stream_confg_old_tab.effective_start_timestamp,
                             p_effective_end_timestamp,
                             p_action,
                             SYSTIMESTAMP,
                             p_revenue_stream_confg_old_tab.revenue_type,
                             p_revenue_stream_confg_old_tab.rebate_terms,
                             p_revenue_stream_confg_old_tab.rebate_taxable,
                             p_revenue_stream_confg_old_tab.widening_taxable
                            );
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_revenue_stream_confg_h
      SET    updated_by                  = p_revenue_stream_confg_old_tab.updated_by,
             update_timestamp            = p_revenue_stream_confg_old_tab.update_timestamp,
             logical_load_timestamp      = p_revenue_stream_confg_old_tab.logical_load_timestamp,
             effective_end_timestamp     = p_effective_end_timestamp,
             action                      = p_action,
             action_timestamp            = SYSTIMESTAMP,
             revenue_type                = p_revenue_stream_confg_old_tab.revenue_type,
             rebate_terms                = p_revenue_stream_confg_old_tab.rebate_terms,
             rebate_taxable              = p_revenue_stream_confg_old_tab.rebate_taxable,
             widening_taxable            = p_revenue_stream_confg_old_tab.widening_taxable
    WHERE    partner_id  = p_revenue_stream_confg_old_tab.partner_id AND
             effective_start_timestamp = p_revenue_stream_confg_old_tab.effective_start_timestamp AND
             revenue_type = p_revenue_stream_confg_old_tab.revenue_type AND
             (nrg_common.has_value_changed(revenue_type, p_revenue_stream_confg_old_tab.revenue_type) = 1 OR
              nrg_common.has_value_changed(rebate_terms , p_revenue_stream_confg_old_tab.rebate_terms) = 1 OR
              nrg_common.has_value_changed(rebate_taxable , p_revenue_stream_confg_old_tab.rebate_taxable) = 1 OR
              nrg_common.has_value_changed(widening_taxable, p_revenue_stream_confg_old_tab.widening_taxable) = 1 );

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PARTNERS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_partners               This is the old version of the partners data
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

 PROCEDURE put_history(p_tab_old_partners             IN partners%ROWTYPE,
                       p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                       p_action                       IN partners_h.action%TYPE)
    IS
  BEGIN
      INSERT INTO partners_h (partner_id
                             ,logical_load_timestamp
                             ,created_by
                             ,create_timestamp
                             ,updated_by
                             ,update_timestamp
                             ,effective_start_timestamp
                             ,effective_end_timestamp
                             ,action
                             ,action_timestamp
                             ,version
                             ,partner_name
                             ,source_platform
                             ,source_partner_id
                             ,agreement_type
                             ,agreement_conditions
                             ,is_white_labelled
                             ,profit_centre_name
                             ,company_id
                             ,email_type
                             ,external_reference
                             ,service_offering
                             ,live_help_enabled
                             ,active
                             ,Ca_Schm_Cd
                             ,is_market_data_taxable
                             ,cash_account_ccy
                             ,allow_credit
                             ,Allow_Acc_Open_From_Platform
                             ,Partner_Function
                             ,Relationship_Country
                             ,Mrkt_Cntrpart_Customer_Id
                             ,Mrkt_Cntrpart_Short_Code
                             ,Mrkt_Cntrpart_Live_Tenant_Tmpl
                             ,Mrkt_Cntrpart_Demo_Tenant_Tmpl
                             ,Mrkt_Cntrpart_Cash_Account_Seg
                             ,statement_format
                             ,is_email_requested
                             ,creation_date
                             ,website
                             ,termination_notice_period
                             ,cmc_relationship_manager
                             ,terminated
                             ,termination_date
                             ,mrkt_cntrpart_plat_url_sub_dom
                             ,is_allowed_funding
                             ,last_remediation_date
                             ,rest_of_world_onboarding_url
                             ,is_sftp_requested
                             ,is_pay_spread_to_partner
                             ,is_fofa_enabled
                             ,additional_extract_time_format
                             ,is_internal
                             ,cmc_sales_introducer_reference
                             )
                      VALUES(
                             p_tab_old_partners.partner_id,
                             p_tab_old_partners.logical_load_timestamp,
                             p_tab_old_partners.created_by,
                             p_tab_old_partners.create_timestamp,
                             p_tab_old_partners.updated_by,
                             p_tab_old_partners.update_timestamp,
                             p_tab_old_partners.effective_start_timestamp,
                             p_effective_end_timestamp,
                             p_action,
                             SYSTIMESTAMP,
                             p_tab_old_partners.version,
                             p_tab_old_partners.partner_name,
                             p_tab_old_partners.source_platform,
                             p_tab_old_partners.source_partner_id,
                             p_tab_old_partners.agreement_type,
                             p_tab_old_partners.agreement_conditions,
                             p_tab_old_partners.is_white_labelled,
                             p_tab_old_partners.profit_centre_name,
                             p_tab_old_partners.company_id,
                             p_tab_old_partners.email_type,
                             p_tab_old_partners.external_reference,
                             p_tab_old_partners.service_offering,
                             p_tab_old_partners.live_help_enabled,
                             p_tab_old_partners.active,
                             p_tab_old_partners.Ca_Schm_Cd,
                             p_tab_old_partners.Is_Market_Data_Taxable,
                             p_tab_old_partners.Cash_Account_Ccy,
                             p_tab_old_partners.Allow_Credit,
                             p_tab_old_partners.Allow_Acc_Open_From_Platform,
                             p_tab_old_partners.Partner_Function,
                             p_tab_old_partners.Relationship_Country,
                             p_tab_old_partners.Mrkt_Cntrpart_Customer_Id,
                             p_tab_old_partners.Mrkt_Cntrpart_Short_Code,
                             p_tab_old_partners.Mrkt_Cntrpart_Live_Tenant_Tmpl,
                             p_tab_old_partners.Mrkt_Cntrpart_Demo_Tenant_Tmpl,
                             p_tab_old_partners.Mrkt_Cntrpart_Cash_Account_Seg,
                             p_tab_old_partners.statement_format,
                             p_tab_old_partners.is_email_requested,
                             p_tab_old_partners.creation_date,
                             p_tab_old_partners.website,
                             p_tab_old_partners.termination_notice_period,
                             p_tab_old_partners.cmc_relationship_manager,
                             p_tab_old_partners.terminated,
                             p_tab_old_partners.termination_date,
                             p_tab_old_partners.mrkt_cntrpart_plat_url_sub_dom,
                             p_tab_old_partners.is_allowed_funding,
                             p_tab_old_partners.last_remediation_date,
                             p_tab_old_partners.rest_of_world_onboarding_url,
                             p_tab_old_partners.is_sftp_requested,
                             p_tab_old_partners.is_pay_spread_to_partner,
                             p_tab_old_partners.is_fofa_enabled,
                             p_tab_old_partners.additional_extract_time_format,
                             p_tab_old_partners.is_internal,
                             p_tab_old_partners.cmc_sales_introducer_reference
                             );
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partners_h
      SET    updated_by                    = p_tab_old_partners.updated_by,
             update_timestamp              = p_tab_old_partners.update_timestamp,
             logical_load_timestamp        = p_tab_old_partners.logical_load_timestamp,
             effective_end_timestamp       = p_effective_end_timestamp,
             action                        = p_action,
             action_timestamp              = SYSTIMESTAMP,
             version                       = p_tab_old_partners.version,
             partner_name                  = p_tab_old_partners.partner_name,
             source_platform               = p_tab_old_partners.source_platform,
             source_partner_id             = p_tab_old_partners.source_partner_id,
             agreement_type                = p_tab_old_partners.agreement_type,
             agreement_conditions          = p_tab_old_partners.agreement_conditions,
             is_white_labelled             = p_tab_old_partners.is_white_labelled,
             profit_centre_name            = p_tab_old_partners.profit_centre_name,
             company_id                    = p_tab_old_partners.company_id,
             email_type                    = p_tab_old_partners.email_type,
             external_reference            = p_tab_old_partners.external_reference,
             service_offering              = p_tab_old_partners.service_offering,
             live_help_enabled             = p_tab_old_partners.live_help_enabled,
             active                        = p_tab_old_partners.active,
             ca_schm_cd                    = p_tab_old_partners.Ca_Schm_Cd,
             Is_Market_Data_Taxable        = p_tab_old_partners.Is_Market_Data_Taxable,
             Cash_Account_Ccy              = p_tab_old_partners.Cash_Account_Ccy,
             Allow_Credit                  = p_tab_old_partners.Allow_Credit,
             Allow_Acc_Open_From_Platform  = p_tab_old_partners.Allow_Acc_Open_From_Platform,
             Partner_Function              = p_tab_old_partners.Partner_Function,
             Relationship_Country          = p_tab_old_partners.Relationship_Country,
             Mrkt_Cntrpart_Short_Code      = p_tab_old_partners.Mrkt_Cntrpart_Short_Code,
             Mrkt_Cntrpart_Live_Tenant_Tmpl= p_tab_old_partners.Mrkt_Cntrpart_Live_Tenant_Tmpl,
             Mrkt_Cntrpart_Demo_Tenant_Tmpl= p_tab_old_partners.Mrkt_Cntrpart_Demo_Tenant_Tmpl,
             Mrkt_Cntrpart_Cash_Account_Seg= p_tab_old_partners.Mrkt_Cntrpart_Cash_Account_Seg,
             statement_format              = p_tab_old_partners.statement_format,
             is_email_requested            = p_tab_old_partners.is_email_requested,
             creation_date                 = p_tab_old_partners.creation_date,
             website                       = p_tab_old_partners.website,
             termination_notice_period     = p_tab_old_partners.termination_notice_period,
             cmc_relationship_manager       = p_tab_old_partners.cmc_relationship_manager,
             terminated                     = p_tab_old_partners.terminated,
             termination_date               = p_tab_old_partners.termination_date,
             mrkt_cntrpart_plat_url_sub_dom = p_tab_old_partners.mrkt_cntrpart_plat_url_sub_dom,
             is_allowed_funding             = p_tab_old_partners.is_allowed_funding,
             last_remediation_date          = p_tab_old_partners.last_remediation_date,
             rest_of_world_onboarding_url   = p_tab_old_partners.rest_of_world_onboarding_url,
             is_sftp_requested              = p_tab_old_partners.is_sftp_requested,
             is_pay_spread_to_partner       = p_tab_old_partners.is_pay_spread_to_partner,
             is_fofa_enabled                = p_tab_old_partners.is_fofa_enabled,
             additional_extract_time_format = p_tab_old_partners.additional_extract_time_format,
             is_internal                    = p_tab_old_partners.is_internal,
             cmc_sales_introducer_reference = p_tab_old_partners.cmc_sales_introducer_reference
       WHERE partner_id = p_tab_old_partners.partner_id
        AND (nrg_common.has_value_changed(version, p_tab_old_partners.version) = 1 OR
             nrg_common.has_value_changed(partner_name, p_tab_old_partners.partner_name) = 1 OR
             nrg_common.has_value_changed(source_platform, p_tab_old_partners.source_platform) = 1 OR
             nrg_common.has_value_changed(source_partner_id, p_tab_old_partners.source_partner_id) = 1 OR
             nrg_common.has_value_changed(agreement_type, p_tab_old_partners.agreement_type) = 1 OR
             nrg_common.has_value_changed(agreement_conditions, p_tab_old_partners.agreement_conditions) = 1 OR
             nrg_common.has_value_changed(is_white_labelled, p_tab_old_partners.is_white_labelled) = 1 OR
             nrg_common.has_value_changed(profit_centre_name, p_tab_old_partners.profit_centre_name) = 1 OR
             nrg_common.has_value_changed(company_id, p_tab_old_partners.company_id) = 1 OR
             nrg_common.has_value_changed(is_white_labelled, p_tab_old_partners.is_white_labelled) = 1 OR
             nrg_common.has_value_changed(email_type, p_tab_old_partners.email_type) = 1 OR
             nrg_common.has_value_changed(external_reference, p_tab_old_partners.external_reference) = 1 OR
             nrg_common.has_value_changed(service_offering, p_tab_old_partners.service_offering) = 1 OR
             nrg_common.has_value_changed(live_help_enabled, p_tab_old_partners.live_help_enabled) = 1 OR
             nrg_common.has_value_changed(active, p_tab_old_partners.active) = 1 OR
             nrg_common.has_value_changed(ca_schm_cd, p_tab_old_partners.ca_schm_cd) = 1 OR
             nrg_common.has_value_changed(Is_Market_Data_Taxable, p_tab_old_partners.Is_Market_Data_Taxable) = 1 OR
             nrg_common.has_value_changed(Cash_Account_Ccy, p_tab_old_partners.Cash_Account_Ccy) = 1 OR
             nrg_common.has_value_changed(Allow_Credit, p_tab_old_partners.Allow_Credit) = 1 OR
             nrg_common.has_value_changed(Allow_Acc_Open_From_Platform, p_tab_old_partners.Allow_Acc_Open_From_Platform) = 1 OR
             nrg_common.has_value_changed(Partner_Function, p_tab_old_partners.Partner_Function) = 1 OR
             nrg_common.has_value_changed(Relationship_Country, p_tab_old_partners.Relationship_Country) = 1 OR
             nrg_common.has_value_changed(Mrkt_Cntrpart_Short_Code, p_tab_old_partners.Mrkt_Cntrpart_Short_Code) = 1 OR
             nrg_common.has_value_changed(Mrkt_Cntrpart_Live_Tenant_Tmpl, p_tab_old_partners.Mrkt_Cntrpart_Live_Tenant_Tmpl) = 1 OR
             nrg_common.has_value_changed(Mrkt_Cntrpart_Demo_Tenant_Tmpl, p_tab_old_partners.Mrkt_Cntrpart_Demo_Tenant_Tmpl) = 1 OR
             nrg_common.has_value_changed(Mrkt_Cntrpart_Cash_Account_Seg, p_tab_old_partners.Mrkt_Cntrpart_Cash_Account_Seg) = 1 OR
             nrg_common.has_value_changed(statement_format, p_tab_old_partners.statement_format) = 1 OR
             nrg_common.has_value_changed(is_email_requested, p_tab_old_partners.is_email_requested) = 1 OR
             nrg_common.has_value_changed(creation_date, p_tab_old_partners.creation_date) = 1 OR
             nrg_common.has_value_changed(website, p_tab_old_partners.website) = 1 OR
             nrg_common.has_value_changed(termination_notice_period, p_tab_old_partners.termination_notice_period) = 1 OR
             nrg_common.has_value_changed(cmc_relationship_manager, p_tab_old_partners.cmc_relationship_manager) = 1 OR
             nrg_common.has_value_changed(terminated, p_tab_old_partners.terminated) = 1 OR
             nrg_common.has_value_changed(termination_date, p_tab_old_partners.termination_date) = 1 OR
             nrg_common.has_value_changed(mrkt_cntrpart_plat_url_sub_dom, p_tab_old_partners.mrkt_cntrpart_plat_url_sub_dom) = 1 or
             nrg_common.has_value_changed(is_allowed_funding, p_tab_old_Partners.is_allowed_funding) = 1 OR
             nrg_common.has_value_changed(last_remediation_date, p_tab_old_Partners.last_remediation_date) = 1 OR
             nrg_common.has_value_changed(rest_of_world_onboarding_url, p_tab_old_Partners.rest_of_world_onboarding_url) = 1 OR
             nrg_common.has_value_changed(is_sftp_requested, p_tab_old_Partners.is_sftp_requested) = 1 OR
             nrg_common.has_value_changed(is_pay_spread_to_partner, p_tab_old_Partners.is_pay_spread_to_partner) = 1 OR
             nrg_common.has_value_changed(is_fofa_enabled, p_tab_old_Partners.is_fofa_enabled) = 1 OR
             nrg_common.has_value_changed(additional_extract_time_format, p_tab_old_Partners.additional_extract_time_format) = 1 OR
             nrg_common.has_value_changed(is_internal, p_tab_old_Partners.is_internal) = 1 OR
             nrg_common.has_value_changed(cmc_sales_introducer_reference, p_tab_old_Partners.cmc_sales_introducer_reference) = 1
          );

 END put_history;

-- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for CMC_SALES_PERSONS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_cmc_sales_persons
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_tab_old_cmc_sales_persons IN partner_cmc_sales_persons%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_currency_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_cmc_sales_persons_h (partner_id
                                              ,logical_load_timestamp
                                              ,created_by
                                              ,create_timestamp
                                              ,updated_by
                                              ,update_timestamp
                                              ,effective_start_timestamp
                                              ,effective_end_timestamp
                                              ,action
                                              ,action_timestamp
                                              ,id
                                              ,value
                                              ,is_deleted)
          VALUES (p_tab_old_cmc_sales_persons.partner_id,
                  p_tab_old_cmc_sales_persons.logical_load_timestamp,
                  p_tab_old_cmc_sales_persons.created_by,
                  p_tab_old_cmc_sales_persons.create_timestamp,
                  p_tab_old_cmc_sales_persons.updated_by,
                  p_tab_old_cmc_sales_persons.update_timestamp,
                  p_tab_old_cmc_sales_persons.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_cmc_sales_persons.id,
                  p_tab_old_cmc_sales_persons.value,
                  p_tab_old_cmc_sales_persons.Is_Deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_cmc_sales_persons_h
      SET    logical_load_timestamp    = p_tab_old_cmc_sales_persons.logical_load_timestamp,
             updated_by                = p_tab_old_cmc_sales_persons.updated_by,
             update_timestamp          = p_tab_old_cmc_sales_persons.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             value     = p_tab_old_cmc_sales_persons.value,
             is_deleted                = p_tab_old_cmc_sales_persons.Is_Deleted
      WHERE  partner_id = p_tab_old_cmc_sales_persons.partner_id
        AND  id = p_tab_old_cmc_sales_persons.id
        AND  effective_start_timestamp = p_tab_old_cmc_sales_persons.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     procedure to write the history table for all the old versions of the tax declarations
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_effective_end_timestamp        This is the end time for the record
  --     p_action                         Type of operation performed
  --     p_old_record                     Old record for partner_tax_declarations
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_action                    IN partner_tax_declarations_h.action%TYPE,
                        p_effective_end_timestamp   IN partner_tax_declarations_h.effective_end_timestamp%TYPE,
                        p_old_record                IN partner_tax_declarations%ROWTYPE) IS

  BEGIN
    INSERT INTO partner_tax_declarations_h(partner_id,
                                           legal_entity,
                                           reporting_country,
                                           logical_load_timestamp,
                                           created_by,
                                           create_timestamp,
                                           updated_by,
                                           update_timestamp,
                                           effective_start_timestamp,
                                           effective_end_timestamp,
                                           action,
                                           action_timestamp,
                                           declaration_form_name,
                                           country_of_residence,
                                           signature_date,
                                           is_valid,
                                           is_exempt,
                                           is_opt_out,
                                           withholding_tax_agree_type)
                                    VALUES (p_old_record.partner_id,
                                            p_old_record.legal_entity,
                                            p_old_record.reporting_country,
                                            p_old_record.logical_load_timestamp,
                                            p_old_record.created_by,
                                            p_old_record.create_timestamp,
                                            p_old_record.updated_by,
                                            p_old_record.update_timestamp,
                                            p_old_record.effective_start_timestamp,
                                            p_effective_end_timestamp,
                                            p_action,
                                            SYSTIMESTAMP,
                                            p_old_record.declaration_form_name,
                                            p_old_record.country_of_residence,
                                            p_old_record.signature_date,
                                            p_old_record.is_valid,
                                            p_old_record.is_exempt,
                                            p_old_record.is_opt_out,
                                            p_old_record.withholding_tax_agree_type);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_tax_declarations_h
      SET
          logical_load_timestamp = p_old_record.logical_load_timestamp,
          created_by = p_old_record.created_by,
          create_timestamp = p_old_record.create_timestamp,
          updated_by = p_old_record.updated_by,
          update_timestamp = p_old_record.update_timestamp,
          effective_end_timestamp = p_effective_end_timestamp,
          action = p_action,
          action_timestamp = SYSTIMESTAMP,
          declaration_form_name = p_old_record.declaration_form_name,
          country_of_residence = p_old_record.country_of_residence,
          signature_date = p_old_record.signature_date,
          is_valid = p_old_record.is_valid,
          is_exempt = p_old_record.is_exempt,
          is_opt_out = p_old_record.is_opt_out,
          withholding_tax_agree_type = p_old_record.withholding_tax_agree_type
      WHERE partner_id = p_old_record.partner_id AND
            legal_entity = p_old_record.legal_entity AND
            reporting_country = p_old_record.reporting_country AND
            effective_start_timestamp = p_old_record.effective_start_timestamp;
  END;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for CMC_SALES_PERSONS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_cmc_sales_persons
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_tab_old_partner_relation IN partner_partner_relation%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_currency_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_partner_relation_h (partner_id
                                            ,relation_partner_id
                                            ,relation
                                            ,logical_load_timestamp
                                            ,created_by
                                            ,create_timestamp
                                            ,updated_by
                                            ,update_timestamp
                                            ,effective_start_timestamp
                                            ,effective_end_timestamp
                                            ,action
                                            ,action_timestamp
                                            ,update_time
                                            ,is_deleted)
          VALUES (p_tab_old_partner_relation.partner_id,
                  p_tab_old_partner_relation.relation_partner_id,
                  p_tab_old_partner_relation.relation,
                  p_tab_old_partner_relation.logical_load_timestamp,
                  p_tab_old_partner_relation.created_by,
                  p_tab_old_partner_relation.create_timestamp,
                  p_tab_old_partner_relation.updated_by,
                  p_tab_old_partner_relation.update_timestamp,
                  p_tab_old_partner_relation.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_partner_relation.update_time,
                  p_tab_old_partner_relation.Is_Deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_partner_relation_h
      SET    logical_load_timestamp    = p_tab_old_partner_relation.logical_load_timestamp,
             updated_by                = p_tab_old_partner_relation.updated_by,
             update_timestamp          = p_tab_old_partner_relation.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             relation                  = p_tab_old_partner_relation.relation,
             update_time               = p_tab_old_partner_relation.update_time,
             is_deleted                = p_tab_old_partner_relation.Is_Deleted
      WHERE  partner_id                = p_tab_old_partner_relation.partner_id
        AND  relation_partner_id       = p_tab_old_partner_relation.relation_partner_id
        AND  effective_start_timestamp = p_tab_old_partner_relation.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for partner_onboarding_platforms
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_partner_onboarding_platforms
  --     p_effective_end_timestamp
  --     p_partner_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_history(p_tab_old_partner_onbdg_pfm    IN partner_onboarding_platforms%ROWTYPE,
                        p_effective_end_timestamp      IN partners.effective_start_timestamp%TYPE,
                        p_partner_id                   IN partners.partner_id%TYPE,
                        p_action                       IN partner_onboarding_currency_h.action%TYPE)

  IS
  BEGIN
     INSERT INTO partner_onboarding_platforms_h (partner_id
                                                ,source_platform
                                                ,logical_load_timestamp
                                                ,created_by
                                                ,create_timestamp
                                                ,updated_by
                                                ,update_timestamp
                                                ,effective_start_timestamp
                                                ,effective_end_timestamp
                                                ,action
                                                ,action_timestamp
                                                ,is_deleted)
          VALUES (p_tab_old_partner_onbdg_pfm.partner_id,
                  p_tab_old_partner_onbdg_pfm.source_platform,
                  p_tab_old_partner_onbdg_pfm.logical_load_timestamp,
                  p_tab_old_partner_onbdg_pfm.created_by,
                  p_tab_old_partner_onbdg_pfm.create_timestamp,
                  p_tab_old_partner_onbdg_pfm.updated_by,
                  p_tab_old_partner_onbdg_pfm.update_timestamp,
                  p_tab_old_partner_onbdg_pfm.effective_start_timestamp,
                  p_effective_end_timestamp,
                  p_action,
                  SYSTIMESTAMP,
                  p_tab_old_partner_onbdg_pfm.Is_Deleted) ;
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE partner_onboarding_platforms_h
      SET    logical_load_timestamp    = p_tab_old_partner_onbdg_pfm.logical_load_timestamp,
             updated_by                = p_tab_old_partner_onbdg_pfm.updated_by,
             update_timestamp          = p_tab_old_partner_onbdg_pfm.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             is_deleted                = p_tab_old_partner_onbdg_pfm.Is_Deleted
      WHERE  partner_id                = p_tab_old_partner_onbdg_pfm.partner_id
        AND  source_platform           = p_tab_old_partner_onbdg_pfm.source_platform
        AND  effective_start_timestamp = p_tab_old_partner_onbdg_pfm.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  --  PUBLIC MODULES
  -- ===================================================================================
  --
  --
  --
  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION version
      RETURN VARCHAR2 DETERMINISTIC
  IS

  BEGIN
    logger.logger.set_module('version');
    RETURN gc_version;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.SEVERE(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      RAISE;
  END version;

  -- ===================================================================================
  -- create_partner_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a partner stub in case the partner is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner Id
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE create_partner_stub (p_user                         IN partners.created_by%TYPE,
                                 p_logical_load_timestamp       IN partners.logical_load_timestamp%TYPE,
                                 p_effective_start_timestamp    IN partners.effective_start_timestamp%TYPE,
                                 p_partner_id                   IN partners.partner_id%TYPE)
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
    IF p_partner_id IS NOT NULL THEN
      INSERT INTO partners(created_by,
                           create_timestamp,
                           updated_by,
                           update_timestamp,
                           logical_load_timestamp,
                           effective_start_timestamp,
                           partner_id)
                   SELECT  p_user,
                           SYSTIMESTAMP,
                           p_user,
                           SYSTIMESTAMP,
                           p_logical_load_timestamp,
                           gc_default_timestamp,
                           p_partner_id
                      FROM dual
                    WHERE NOT EXISTS (SELECT 1 FROM partners p WHERE p.partner_id=p_partner_id);
    END IF;

    COMMIT;

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      --
      --Since the partner already exists no need to create the stub
      --
      NULL;
  END;

  -- ===================================================================================
  -- create_partner_desk_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a partner stub in case the partner is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner Id
  --     p_partner_desk_id                Partner Desk Id
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE create_partner_desk_stub (p_user                         IN partners.created_by%TYPE,
                                      p_logical_load_timestamp       IN partners.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp    IN partners.effective_start_timestamp%TYPE,
                                      p_partner_id                   IN partners.Partner_Id%TYPE,
                                      p_partner_desk_id              IN partner_desks.partner_desk_id%TYPE)
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
    IF p_partner_desk_id IS NOT NULL THEN
      INSERT INTO partner_desks(created_by,
                                 create_timestamp,
                                 updated_by,
                                 update_timestamp,
                                 logical_load_timestamp,
                                 effective_start_timestamp,
                                 partner_id,
                                 partner_desk_id)
                         SELECT  p_user,
                                 SYSTIMESTAMP,
                                 p_user,
                                 SYSTIMESTAMP,
                                 p_logical_load_timestamp,
                                 gc_default_timestamp,
                                 p_partner_id,
                                 p_partner_desk_id
                         FROM dual
         WHERE NOT EXISTS (SELECT 1 FROM partner_desks p WHERE p.partner_desk_id=p_partner_desk_id);
    END IF;

    COMMIT;

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      --
      --Since the partner already exists no need to create the stub
      --
      NULL;
  END;

  -- ===================================================================================
  -- create_partner_agent_stub
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a partner stub in case the partner is missing
  --     This is done to ensure the transactional integrity
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_agent_id               Partner Agent Id
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE create_partner_agent_stub (p_user                         IN partners.created_by%TYPE,
                                       p_logical_load_timestamp       IN partners.logical_load_timestamp%TYPE,
                                       p_effective_start_timestamp    IN partners.effective_start_timestamp%TYPE,
                                       p_partner_agent_id             IN partner_agents.partner_agent_id%TYPE)
  IS
    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
    IF p_partner_agent_id IS NOT NULL THEN
      INSERT INTO partner_agents(created_by,
                                 create_timestamp,
                                 updated_by,
                                 update_timestamp,
                                 logical_load_timestamp,
                                 effective_start_timestamp,
                                 partner_agent_id)
                         SELECT  p_user,
                                 SYSTIMESTAMP,
                                 p_user,
                                 SYSTIMESTAMP,
                                 p_logical_load_timestamp,
                                 gc_default_timestamp,
                                 p_partner_agent_id
                         FROM dual
         WHERE NOT EXISTS (SELECT 1 FROM partner_agents p WHERE p.partner_agent_id=p_partner_agent_id);
    END IF;

    COMMIT;

  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      --
      --Since the partner already exists no need to create the stub
      --
      NULL;
  END;

  -- ===================================================================================
  -- put_tax_declaration
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put tax declarations
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_TAX_DECLARATIONS
  --     PARTNER_TAX_DECLARATIONS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner id
  --     p_old_partner_version            Account Version of the existing record on ODS
  --     p_new_partner_version            Account Version of the new record that is received
  --     p_tax_declarations               Tax declarations
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_tax_declaration(p_user                        partners.created_by%TYPE,
                                p_logical_load_timestamp      partners.logical_load_timestamp%TYPE,
                                p_effective_start_timestamp   partners.effective_start_timestamp%TYPE,
                                p_partner_id                  partners.partner_id%TYPE,
                                p_old_partner_version         partners.version%TYPE,
                                p_new_partner_version         partners.version%TYPE,
                                p_tax_declarations            tax_declaration_tab) IS

    TYPE l_old_records_tab IS TABLE OF partner_tax_declarations%ROWTYPE;

    lv_old_records l_old_records_tab;
  BEGIN
    logger.logger.set_module('put_tax_declaration');
    CASE
      WHEN p_old_partner_version IS NULL OR p_old_partner_version <= p_new_partner_version THEN
      --
      --Incase of new accounts and updated account information
      --

      --
      --Identify updated records
      --
      SELECT old_records.*
      BULK COLLECT INTO lv_old_records
      FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)) new_records,
           partner_tax_declarations old_records
      WHERE old_records.partner_id = p_partner_id AND
            old_records.legal_entity = new_records.legal_entity AND
            old_records.reporting_country = new_records.reporting_country AND
            (nrg_common.has_value_changed(old_records.declaration_form_name, new_records.declaration_form_name) = 1 OR
             nrg_common.has_value_changed(old_records.country_of_residence, new_records.country_of_residence) = 1 OR
             nrg_common.has_value_changed(old_records.signature_date, new_records.signature_date) = 1 OR
             nrg_common.has_value_changed(old_records.is_valid, new_records.is_valid) = 1 OR
             --nrg_common.has_value_changed(old_records.is_exempt, new_records.is_exempt) = 1 OR
             nrg_common.has_value_changed(old_records.is_opt_out, new_records.is_opt_out) = 1 OR
             nrg_common.has_value_changed(old_records.withholding_tax_agree_type,new_records.withholding_tax_agree_type) = 1);

      --
      --Write updated records to history
      --

      FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
        put_history(p_action                    => 'U',
                    p_effective_end_timestamp   => p_effective_start_timestamp,
                    p_old_record                => lv_old_records(lv_cnt));
      END LOOP;

      --
      --Identify Deleted Records
      --

      SELECT old_records.*
      BULK COLLECT INTO lv_old_records
      FROM partner_tax_declarations old_records
      WHERE partner_id = p_partner_id AND
            (legal_entity,
             reporting_country) NOT IN (SELECT legal_entity,
                                               reporting_country
                                        FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)));

      --
      --Write deleted records to history
      --

      FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
        put_history(p_action                    => 'D',
                    p_effective_end_timestamp   => p_effective_start_timestamp,
                    p_old_record                => lv_old_records(lv_cnt));
      END LOOP;

      --
      --Delete reocrds from base table
      --

      DELETE partner_tax_declarations old_records
      WHERE partner_id = p_partner_id AND
            (legal_entity,
             reporting_country) NOT IN (SELECT legal_entity,
                                               reporting_country
                                        FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)));

      --
      --Write New records, updated records to base table
      --

      MERGE INTO partner_tax_declarations old_records
      USING (SELECT p_partner_id partner_id,
                    p_user created_by,
                    SYSTIMESTAMP message_time,
                    p_effective_start_timestamp effective_start_timestamp,
                    legal_entity,
                    reporting_country,
                    declaration_form_name,
                    country_of_residence,
                    signature_date,
                    is_valid,
                    --is_exempt,
                    is_opt_out,
                    withholding_tax_agree_type
            FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab)) aa) new_records
      ON (old_records.partner_id = new_records.partner_id AND
          old_records.legal_entity = new_records.legal_entity AND
          old_records.reporting_country = new_records.reporting_country)
      WHEN
        MATCHED THEN
          UPDATE
          SET old_records.updated_by = new_records.created_by,
              old_records.update_timestamp = new_records.message_time,
              old_records.logical_load_timestamp = SYSTIMESTAMP,
              old_records.effective_start_timestamp = new_records.effective_start_timestamp,
              old_records.declaration_form_name = new_records.declaration_form_name,
              old_records.country_of_residence = new_records.country_of_residence,
              old_records.signature_date = new_records.signature_date,
              old_records.is_valid = new_records.is_valid,
              --old_records.is_exempt = new_records.is_exempt,
              old_records.is_opt_out = new_records.is_opt_out,
              old_records.withholding_tax_agree_type = new_records.withholding_tax_agree_type
          WHERE (nrg_common.has_value_changed(old_records.declaration_form_name, new_records.declaration_form_name) = 1 OR
                 nrg_common.has_value_changed(old_records.country_of_residence, new_records.country_of_residence) = 1 OR
                 nrg_common.has_value_changed(old_records.signature_date, new_records.signature_date) = 1 OR
                 nrg_common.has_value_changed(old_records.is_valid, new_records.is_valid) = 1 OR
                 --nrg_common.has_value_changed(old_records.is_exempt, new_records.is_exempt) = 1 OR
                 nrg_common.has_value_changed(old_records.is_opt_out, new_records.is_opt_out) = 1 OR
                 nrg_common.has_value_changed(old_records.withholding_tax_agree_type, new_records.withholding_tax_agree_type) = 1)
      WHEN
        NOT MATCHED THEN
          INSERT (partner_id,
                  legal_entity,
                  reporting_country,
                  logical_load_timestamp,
                  created_by,
                  create_timestamp,
                  updated_by,
                  update_timestamp,
                  effective_start_timestamp,
                  declaration_form_name,
                  country_of_residence,
                  signature_date,
                  is_valid,
                  --is_exempt,
                  is_opt_out,
                  withholding_tax_agree_type)
          VALUES (new_records.partner_id,
                  new_records.legal_entity,
                  new_records.reporting_country,
                  SYSTIMESTAMP,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.created_by,
                  new_records.message_time,
                  new_records.effective_start_timestamp,
                  new_records.declaration_form_name,
                  new_records.country_of_residence,
                  new_records.signature_date,
                  new_records.is_valid,
                  --new_records.is_exempt,
                  new_records.is_opt_out,
                  new_records.withholding_tax_agree_type);

      WHEN p_old_partner_version > p_new_partner_version THEN

        SELECT p_partner_id,
               legal_entity,
               reporting_country,
               SYSTIMESTAMP logical_load_timestamp,
               p_user created_by,
               SYSTIMESTAMP create_timestamp,
               p_user updated_by,
               SYSTIMESTAMP update_timestamp,
               p_effective_start_timestamp,
               declaration_form_name,
               country_of_residence,
               signature_date,
               is_valid,
               NULL is_exempt,
               is_opt_out,
               withholding_tax_agree_type
        BULK COLLECT INTO lv_old_records
        FROM TABLE(CAST(p_tax_declarations AS tax_declaration_tab))
        WHERE (legal_entity,
               reporting_country,
               declaration_form_name,
               country_of_residence,
               signature_date,
               is_valid,
               --is_exempt,
               is_opt_out,
               withholding_tax_agree_type) NOT IN (SELECT legal_entity,
                                                           reporting_country,
                                                           declaration_form_name,
                                                           country_of_residence,
                                                           signature_date,
                                                           is_valid,
                                                           --is_exempt,
                                                           is_opt_out,
                                                           withholding_tax_agree_type
                                                    FROM partner_tax_declarations
                                                    WHERE partner_id = p_partner_id
                                                    UNION ALL
                                                    SELECT legal_entity,
                                                           reporting_country,
                                                           declaration_form_name,
                                                           country_of_residence,
                                                           signature_date,
                                                           is_valid,
                                                           --is_exempt,
                                                           is_opt_out,
                                                           withholding_tax_agree_type
                                                    FROM partner_tax_declarations_h
                                                    WHERE partner_id = p_partner_id);

        --
        --Write missing records to history
        --

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history(p_action                    => 'I',
                      p_effective_end_timestamp   => p_effective_start_timestamp,
                      p_old_record                => lv_old_records(lv_cnt));
        END LOOP;
    END CASE;
  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_onboarding_countries
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_ONBOARDING_COUNTRIES
  --     PARTNER_ONBOARDING_COUNTRIES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_onboarding_countries_tab       Onboarding countries table
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_onboarding_countries(p_user                       IN partners.created_by%TYPE,
                                     p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                     p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                     p_partner_id                 IN partners.partner_id%TYPE,
                                     p_onboarding_countries_tab   IN onboarding_countries_tab)
  IS
  TYPE ltype_onboarding_countries IS TABLE OF partner_onboarding_countries%ROWTYPE;
  ltab_onboarding_countries ltype_onboarding_countries;

 BEGIN

     logger.logger.set_module('put_onboarding_countries');


    -------------------------
    --- Select for delete a row for given partner_id for which tenant_code does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.country_code
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_onboarding_countries
      FROM partner_onboarding_countries old_version
        WHERE old_version.partner_id=p_partner_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_onboarding_countries_tab AS onboarding_countries_tab)) new_version
                         WHERE new_version.country_code=old_version.country_code)
        FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_onboarding_countries.COUNT
       LOOP
          put_history(p_tab_old_onboarding_countries => ltab_onboarding_countries(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_onboarding_countries.COUNT
      DELETE FROM partner_onboarding_countries
       WHERE country_code = ltab_onboarding_countries(l_vcount).country_code
         AND partner_id = p_partner_id;

      ltab_onboarding_countries.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
    SELECT old_version.partner_id
           ,old_version.logical_load_timestamp
           ,old_version.created_by
           ,old_version.create_timestamp
           ,old_version.updated_by
           ,old_version.update_timestamp
           ,old_version.effective_start_timestamp
           ,old_version.country_code
           ,old_version.is_deleted
      BULK COLLECT INTO ltab_onboarding_countries
     FROM partner_onboarding_countries old_version,
          (SELECT country_code, is_deleted
              FROM TABLE(CAST(p_onboarding_countries_tab AS onboarding_countries_tab))) new_version
     WHERE old_version.partner_id = p_partner_id
       AND old_version.country_code = new_version.country_code
       AND (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR l_vcount IN 1..ltab_onboarding_countries.COUNT
       LOOP
          put_history(p_tab_old_onboarding_countries => ltab_onboarding_countries(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'U');
      END LOOP;



     MERGE  INTO  partner_onboarding_countries old_version
      USING (SELECT country_code, is_deleted
              FROM TABLE(CAST(p_onboarding_countries_tab AS onboarding_countries_tab))) new_version
      ON (old_version.partner_id = p_partner_id AND
          old_version.country_code = new_version.country_code)
     WHEN MATCHED THEN
         UPDATE  SET
            is_deleted = new_version.is_deleted
           ,logical_load_timestamp = p_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           WHERE (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,country_code
             ,is_deleted)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.country_code
             ,new_version.is_deleted);



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_onboarding_countries;
  -- ===================================================================================
  -- put_onboarding_languages
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_ONBOARDING_LANGUAGES
  --     PARTNER_ONBOARDING_LANGUAGES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_onboarding_languages_tab       Onboarding languages table
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_onboarding_languages(p_user                       IN partners.created_by%TYPE,
                                     p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                     p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                     p_partner_id                 IN partners.partner_id%TYPE,
                                     p_onboarding_languages_tab   IN onboarding_languages_tab)
  IS
  TYPE ltype_onboarding_languages IS TABLE OF partner_onboarding_languages%ROWTYPE;
  ltab_onboarding_languages ltype_onboarding_languages;

 BEGIN

     logger.logger.set_module('put_onboarding_languages');


    -------------------------
    --- Select for delete a row for given partner_id for which tenant_code does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.language_code
            ,old_version.is_deleted
      ,old_version.is_prioritised
            BULK COLLECT INTO ltab_onboarding_languages
      FROM partner_onboarding_languages old_version
        WHERE old_version.partner_id=p_partner_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_onboarding_languages_tab AS onboarding_languages_tab)) new_version
                         WHERE new_version.language_code=old_version.language_code)
        FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_onboarding_languages.COUNT
       LOOP
         NULL;
          put_history(p_tab_old_onboarding_languages => ltab_onboarding_languages(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_onboarding_languages.COUNT
      DELETE FROM partner_onboarding_languages
       WHERE language_code = ltab_onboarding_languages(l_vcount).language_code
         AND partner_id = p_partner_id;

      ltab_onboarding_languages.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
    SELECT old_version.partner_id
           ,old_version.logical_load_timestamp
           ,old_version.created_by
           ,old_version.create_timestamp
           ,old_version.updated_by
           ,old_version.update_timestamp
           ,old_version.effective_start_timestamp
           ,old_version.language_code
           ,old_version.is_deleted
       ,old_version.is_prioritised
      BULK COLLECT INTO ltab_onboarding_languages
     FROM partner_onboarding_languages old_version,
          (SELECT language_code, is_deleted , is_prioritised
              FROM TABLE(CAST(p_onboarding_languages_tab AS onboarding_languages_tab))) new_version
     WHERE old_version.partner_id = p_partner_id
       AND old_version.language_code = new_version.language_code
       AND (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
          nrg_common.has_value_changed(new_version.is_prioritised, old_version.is_prioritised) = 1 );

     FOR l_vcount IN 1..ltab_onboarding_languages.COUNT
       LOOP NULL;
          put_history(p_tab_old_onboarding_languages => ltab_onboarding_languages(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'U');
      END LOOP;



     MERGE  INTO  partner_onboarding_languages old_version
      USING (SELECT language_code, is_deleted , is_prioritised
              FROM TABLE(CAST(p_onboarding_languages_tab AS onboarding_languages_tab))) new_version
      ON (old_version.partner_id = p_partner_id AND
          old_version.language_code = new_version.language_code)
     WHEN MATCHED THEN
         UPDATE  SET
            is_deleted = new_version.is_deleted
      ,is_prioritised = new_version.is_prioritised
           ,logical_load_timestamp = p_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           WHERE (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
              nrg_common.has_value_changed(new_version.is_prioritised, old_version.is_prioritised) = 1 )
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,language_code
             ,is_deleted
       ,is_prioritised)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.language_code
             ,new_version.is_deleted
       ,new_version.is_prioritised);



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_onboarding_languages;

  -- ===================================================================================
  -- put_onboarding_countries
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Onboarding Ccy
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_ONBOARDING_COUNTRIES
  --     PARTNER_ONBOARDING_COUNTRIES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_partner_onboarding_crrn_tab    Onboarding currencies table
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_onboarding_currencies(p_user                          IN partners.created_by%TYPE,
                                      p_logical_load_timestamp        IN partners.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp     IN partners.effective_start_timestamp%TYPE,
                                      p_partner_id                    IN partners.partner_id%TYPE,
                                      p_partner_onboarding_crrn_tab   IN partner_onboarding_crrn_tab)
  IS
  TYPE ltype_onboarding_currency IS TABLE OF partner_onboarding_currency%ROWTYPE;
  ltab_onboarding_currency ltype_onboarding_currency;

 BEGIN

     logger.logger.set_module('put_onboarding_currencies');


    -------------------------
    --- Select for delete a row for given partner_id for which ccy_code does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.ccy_code
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_onboarding_currency
      FROM partner_onboarding_currency old_version
        WHERE old_version.partner_id=p_partner_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_partner_onboarding_crrn_tab AS partner_onboarding_crrn_tab)) new_version
                         WHERE new_version.ccy_code=old_version.ccy_code)
        FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_onboarding_currency.COUNT
       LOOP
          put_history(p_tab_old_onboarding_currency => ltab_onboarding_currency(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_onboarding_currency.COUNT
      DELETE FROM partner_onboarding_currency
       WHERE ccy_code = ltab_onboarding_currency(l_vcount).ccy_code
         AND partner_id = p_partner_id;

      ltab_onboarding_currency.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
     SELECT old_version.partner_id
           ,old_version.logical_load_timestamp
           ,old_version.created_by
           ,old_version.create_timestamp
           ,old_version.updated_by
           ,old_version.update_timestamp
           ,old_version.effective_start_timestamp
           ,old_version.ccy_code
           ,old_version.is_deleted
      BULK COLLECT INTO ltab_onboarding_currency
     FROM partner_onboarding_currency old_version,
          (SELECT ccy_code, is_deleted
              FROM TABLE(CAST(p_partner_onboarding_crrn_tab AS partner_onboarding_crrn_tab))) new_version
     WHERE old_version.partner_id = p_partner_id
       AND old_version.ccy_code = new_version.ccy_code
       AND (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR l_vcount IN 1..ltab_onboarding_currency.COUNT
       LOOP
          put_history(p_tab_old_onboarding_currency  => ltab_onboarding_currency(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'U');
      END LOOP;



     MERGE  INTO  partner_onboarding_currency old_version
      USING (SELECT ccy_code, is_deleted
              FROM TABLE(CAST(p_partner_onboarding_crrn_tab AS partner_onboarding_crrn_tab))) new_version
      ON (old_version.partner_id = p_partner_id AND
          old_version.ccy_code = new_version.ccy_code)
     WHEN MATCHED THEN
         UPDATE  SET
            is_deleted = new_version.is_deleted
           ,logical_load_timestamp = p_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           WHERE (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,ccy_code
             ,is_deleted)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.ccy_code
             ,new_version.is_deleted);

   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_onboarding_currencies;

  -- ===================================================================================
  -- put_restricted_plat_feat
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_RESTRICTED_PLAT_FEAT
  --     PARTNER_RESTRICTED_PLAT_FEAT_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_restricted_plat_feat_tab       Restricted platform features
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_restricted_plat_feat(p_user                       IN partners.created_by%TYPE,
                                     p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                     p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                     p_partner_id                 IN partners.partner_id%TYPE,
                                     p_restricted_plat_feat_tab   IN restricted_plat_feat_tab)
  IS
  TYPE ltype_restricted_plat_feat IS TABLE OF partner_restricted_plat_feat%ROWTYPE;
  ltab_restricted_plat_feat ltype_restricted_plat_feat;

 BEGIN

     logger.logger.set_module('put_restricted_plat_feat');


    -------------------------
    --- Select for delete a row for given partner_id for which tenant_code does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.platform_feature_name
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_restricted_plat_feat
      FROM partner_restricted_plat_feat old_version
        WHERE old_version.partner_id=p_partner_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_restricted_plat_feat_tab AS restricted_plat_feat_tab)) new_version
                         WHERE new_version.platform_feature_name=old_version.platform_feature_name)
        FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_restricted_plat_feat.COUNT
       LOOP
          put_history(p_tab_old_restricted_plat_feat   => ltab_restricted_plat_feat(l_vcount),
                      p_effective_end_timestamp    => p_effective_start_timestamp,
                      p_partner_id                 => p_partner_id,
                      p_action                     => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_restricted_plat_feat.COUNT
      DELETE FROM partner_restricted_plat_feat
       WHERE platform_feature_name = ltab_restricted_plat_feat(l_vcount).platform_feature_name
         AND partner_id = p_partner_id;

      ltab_restricted_plat_feat.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
    SELECT old_version.partner_id
           ,old_version.logical_load_timestamp
           ,old_version.created_by
           ,old_version.create_timestamp
           ,old_version.updated_by
           ,old_version.update_timestamp
           ,old_version.effective_start_timestamp
           ,old_version.platform_feature_name
           ,old_version.is_deleted
      BULK COLLECT INTO ltab_restricted_plat_feat
     FROM partner_restricted_plat_feat old_version,
          (SELECT platform_feature_name, is_deleted
              FROM TABLE(CAST(p_restricted_plat_feat_tab AS restricted_plat_feat_tab))) new_version
     WHERE old_version.partner_id = p_partner_id
       AND old_version.platform_feature_name = new_version.platform_feature_name
       AND (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR l_vcount IN 1..ltab_restricted_plat_feat.COUNT
       LOOP
          put_history(p_tab_old_restricted_plat_feat   => ltab_restricted_plat_feat(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'U');
      END LOOP;



     MERGE  INTO  partner_restricted_plat_feat old_version
      USING (SELECT platform_feature_name, is_deleted
              FROM TABLE(CAST(p_restricted_plat_feat_tab AS restricted_plat_feat_tab))) new_version
      ON (old_version.partner_id = p_partner_id AND
          old_version.platform_feature_name = new_version.platform_feature_name)
     WHEN MATCHED THEN
         UPDATE  SET
            is_deleted = new_version.is_deleted
           ,logical_load_timestamp = p_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           WHERE (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,platform_feature_name
             ,is_deleted)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.platform_feature_name
             ,new_version.is_deleted);



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_restricted_plat_feat;

  /* --DR
  PROCEDURE put_prtnr_asst_clss_ovrrds(p_user                       IN prtnr_asset_class_overrides.Created_By%TYPE,
                                       p_logical_load_timestamp     IN prtnr_asset_class_overrides.Logical_Load_Timestamp%TYPE,
                                       p_effective_start_timestamp  IN prtnr_asset_class_overrides.Effective_Start_Timestamp%TYPE,
                                       p_partner_id                 IN prtnr_asset_class_overrides.Partner_Id%TYPE,
                                       p_revenue_type               IN prtnr_asset_class_overrides.revenue_type%TYPE,
                                       p_asset_class_overrides      IN asset_class_rebate_tab) IS

  BEGIN
    --
    --Prepare to write History
    --

    MERGE INTO prtnr_asset_class_overrides_h old_recs
    USING (SELECT old_records.*,
                  p_effective_start_timestamp effective_end_timestamp,
                  'U' action,
                  SYSTIMESTAMP action_timestamp
           FROM prtnr_asset_class_overrides old_records,
                TABLE(CAST(p_asset_class_overrides AS asset_class_rebate_tab))new_records
           WHERE old_records.partner_id = p_partner_id AND
                 old_records.revenue_type = p_revenue_type AND
                 old_records.asset_class = new_records.asset_class(+) AND
                 (nrg_common.has_value_changed(old_records.rebate_terms, new_records.rebate_terms) = 1 OR
                  nrg_common.has_value_changed(old_records.asset_class_code, new_records.asset_class_code) = 1 OR
                  nrg_common.has_value_changed(old_records.is_deleted, CASE WHEN new_records.asset_class IS NULL THEN 'YES' ELSE 'NO' END) = 1)
          )new_recs
    ON(old_recs.partner_id = new_recs.partner_id AND
       old_recs.revenue_type = new_recs.revenue_type AND
       old_recs.asset_class = new_recs.asset_class AND
       old_recs.effective_start_timestamp = new_recs.effective_start_timestamp)
    WHEN MATCHED THEN
      UPDATE
      SET
          logical_load_timestamp = new_recs.logical_load_timestamp,
          created_by = new_recs.created_by,
          create_timestamp = new_recs.create_timestamp,
          updated_by = new_recs.updated_by,
          update_timestamp = new_recs.update_timestamp,
          effective_end_timestamp = new_recs.effective_end_timestamp,
          action = new_recs.action,
          action_timestamp = new_recs.action_timestamp,
          rebate_terms = new_recs.rebate_terms,
          asset_class_code = new_recs.asset_class_code,
          is_deleted = new_recs.is_deleted
    WHEN NOT MATCHED THEN
      INSERT (partner_id,
              revenue_type,
              asset_class,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              effective_end_timestamp,
              action,
              action_timestamp,
              rebate_terms,
              asset_class_code,
              is_deleted)
       VALUES(new_recs.partner_id,
              new_recs.revenue_type,
              new_recs.asset_class,
              new_recs.logical_load_timestamp,
              new_recs.created_by,
              new_recs.create_timestamp,
              new_recs.updated_by,
              new_recs.update_timestamp,
              new_recs.effective_start_timestamp,
              new_recs.effective_end_timestamp,
              new_recs.action,
              new_recs.action_timestamp,
              new_recs.rebate_terms,
              new_recs.asset_class_code,
              new_recs.is_deleted);

    --
    --Identify Deletes
    --

    UPDATE prtnr_asset_class_overrides
    SET is_deleted = 'YES',
        logical_load_timestamp = p_logical_load_timestamp,
        effective_start_timestamp = p_effective_start_timestamp,
        update_timestamp = SYSTIMESTAMP,
        updated_by = p_user
    WHERE partner_id = p_partner_id AND
         revenue_type = p_revenue_type AND
         asset_class NOT IN (SELECT asset_class
                             FROM TABLE(CAST(p_asset_class_overrides AS asset_class_rebate_tab)));

    --
    --Update base table
    --

    MERGE INTO prtnr_asset_class_overrides old_recs
    USING (SELECT new_records.*,
                  p_effective_start_timestamp effective_start_timestamp,
                  p_logical_load_timestamp logical_load_timestamp,
                  p_user created_by,
                  SYSTIMESTAMP create_timestamp,
                  p_user updated_by,
                  SYSTIMESTAMP update_timestamp,
                  'NO' is_deleted,
                  p_revenue_type revenue_type,
                  p_partner_id partner_id
           FROM TABLE(CAST(p_asset_class_overrides AS asset_class_rebate_tab))new_records
          )new_recs
    ON(old_recs.partner_id = new_recs.partner_id AND
       old_recs.revenue_type = new_recs.revenue_type AND
       old_recs.asset_class = new_recs.asset_class AND
       (nrg_common.has_value_changed(old_recs.rebate_terms, new_recs.rebate_terms) = 1 OR
        nrg_common.has_value_changed(old_recs.asset_class_code, new_recs.asset_class_code) = 1 OR
        nrg_common.has_value_changed(old_recs.is_deleted, new_recs.asset_class) = 1))
    WHEN MATCHED THEN
      UPDATE
      SET
          logical_load_timestamp = new_recs.logical_load_timestamp,
          created_by = new_recs.created_by,
          create_timestamp = new_recs.create_timestamp,
          updated_by = new_recs.updated_by,
          update_timestamp = new_recs.update_timestamp,
          effective_start_timestamp = new_recs.effective_start_timestamp,
          rebate_terms = new_recs.rebate_terms,
          asset_class_code = new_recs.asset_class_code,
          is_deleted = new_recs.is_deleted
    WHEN NOT MATCHED THEN
      INSERT (partner_id,
              revenue_type,
              asset_class,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              rebate_terms,
              asset_class_code,
              is_deleted)
       VALUES(new_recs.partner_id,
              new_recs.revenue_type,
              new_recs.asset_class,
              new_recs.logical_load_timestamp,
              new_recs.created_by,
              new_recs.create_timestamp,
              new_recs.updated_by,
              new_recs.update_timestamp,
              new_recs.effective_start_timestamp,
              new_recs.rebate_terms,
              new_recs.asset_class_code,
              new_recs.is_deleted);
  END;
  */ --DR

  -- ===================================================================================
  -- put_revenue_stream_config
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_REVENUE_STREAM_CONFIG
  --     PARTNER_REVENUE_STREAM_CONFIG_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_revenue_stream_config_tab       Onboarding countries table
  --
  --------------------------------------------------------------------------------------
  PROCEDURE put_revenue_stream_config(p_user                       IN partner_revenue_stream_confg.Created_By%TYPE,
                                      p_logical_load_timestamp     IN partner_revenue_stream_confg.Logical_Load_Timestamp%TYPE,
                                      p_effective_start_timestamp  IN partner_revenue_stream_confg.Effective_Start_Timestamp%TYPE,
                                      p_partner_id                 IN partner_revenue_stream_confg.Partner_Id%TYPE,
                                      p_revenue_stream_confg_tab   IN revenue_stream_confg_tab)
  IS
  TYPE ltype_revenue_stream_confg IS TABLE OF partner_revenue_stream_confg%ROWTYPE;
  ltab_revenue_stream_confg ltype_revenue_stream_confg;

  BEGIN

      logger.logger.set_module('put_revenue_stream_config');


    -------------------------
    --- Select for delete a row for given partner_id for which revenue_stream_configuration does not exist in a new msg
    -------------------------
     BEGIN

     IF (p_revenue_stream_confg_tab IS NULL) THEN
     SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.revenue_type
            ,old_version.Rebate_Terms
            ,old_version.rebate_taxable
            ,old_version.Widening_Taxable
         BULK COLLECT INTO ltab_revenue_stream_confg
      FROM partner_revenue_stream_confg old_version,
         (SELECT revenue_type
                ,rebate_terms
                ,rebate_taxable
                ,widening_taxable
                ,p_partner_id partner_id
           FROM TABLE(CAST(p_revenue_stream_confg_tab AS revenue_stream_confg_tab))) new_version
        WHERE old_version.partner_id=new_version.partner_id
          AND old_version.Revenue_Type=new_version.revenue_type
          FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_revenue_stream_confg.COUNT
       LOOP
          put_history(p_revenue_stream_confg_old_tab  => ltab_revenue_stream_confg(l_vcount),
                      p_effective_end_timestamp       => p_effective_start_timestamp,
                      p_partner_id                    => p_partner_id,
                      p_action                        => 'D');

      DELETE FROM partner_revenue_stream_confg
       WHERE partner_id = ltab_revenue_stream_confg(l_vcount).partner_id
         AND revenue_type = ltab_revenue_stream_confg(l_vcount).revenue_type;

      END LOOP;

      ltab_revenue_stream_confg.delete();

      END IF;

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
    SELECT old_version.partner_id
          ,old_version.logical_load_timestamp
          ,old_version.created_by
          ,old_version.create_timestamp
          ,old_version.updated_by
          ,old_version.update_timestamp
          ,old_version.effective_start_timestamp
          ,old_version.revenue_type
          ,old_version.rebate_terms
          ,old_version.rebate_taxable
          ,old_version.widening_taxable
    BULK COLLECT INTO ltab_revenue_stream_confg
    FROM partner_revenue_stream_confg old_version
        ,(SELECT revenue_type
                ,rebate_terms
                ,rebate_taxable
                ,widening_taxable
                ,p_partner_id partner_id
           FROM TABLE(CAST(p_revenue_stream_confg_tab AS revenue_stream_confg_tab))) new_version
    WHERE  old_version.partner_id = new_version.partner_id AND
           old_version.Revenue_Type = new_version.revenue_type AND
           (nrg_common.has_value_changed(new_version.rebate_terms, old_version.rebate_terms) = 1 OR
            nrg_common.has_value_changed(new_version.rebate_taxable, old_version.rebate_taxable) = 1 OR
            nrg_common.has_value_changed(new_version.revenue_type, old_version.revenue_type) = 1)
       FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_revenue_stream_confg.COUNT
       LOOP
          put_history(p_revenue_stream_confg_old_tab  => ltab_revenue_stream_confg(l_vcount),
                      p_effective_end_timestamp       => p_effective_start_timestamp,
                      p_partner_id                    => p_partner_id,
                      p_action                        => 'U');
      END LOOP;



     MERGE  INTO  partner_revenue_stream_confg old_version
      USING (SELECT revenue_type
                ,rebate_terms
                ,rebate_taxable
                ,widening_taxable
                ,p_partner_id partner_id
           FROM TABLE(CAST(p_revenue_stream_confg_tab AS revenue_stream_confg_tab))) new_version
      ON (old_version.partner_id = new_version.partner_id AND
          old_version.Revenue_Type = new_version.revenue_type)
     WHEN MATCHED THEN
         UPDATE  SET
            logical_load_timestamp = p_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           ,rebate_terms = new_version.rebate_terms
           ,rebate_taxable = new_version.rebate_taxable
           ,widening_taxable = new_version.widening_taxable
           WHERE (nrg_common.has_value_changed(new_version.rebate_terms, old_version.rebate_terms) = 1 OR
                  nrg_common.has_value_changed(new_version.rebate_taxable, old_version.rebate_taxable) = 1 OR
                  nrg_common.has_value_changed(new_version.widening_taxable, old_version.widening_taxable) = 1)
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,revenue_type
             ,rebate_terms
             ,rebate_taxable
             ,widening_taxable)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.revenue_type
             ,new_version.rebate_terms
             ,new_version.rebate_taxable
             ,new_version.widening_taxable);

      /* --DR
      FOR lv_cnt IN 1..p_revenue_stream_confg_tab.COUNT LOOP
        put_prtnr_asst_clss_ovrrds(p_user                       => p_user,
                                   p_logical_load_timestamp     => p_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_Effective_Start_Timestamp,
                                   p_partner_id                 => p_Partner_Id,
                                   p_revenue_type               => p_revenue_stream_confg_tab(lv_cnt).revenue_type,
                                   p_asset_class_overrides      => p_revenue_stream_confg_tab(lv_cnt).asset_class_rebate);
      END LOOP;
     */ --DR
   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

   END put_revenue_stream_config;

 -- ===================================================================================
  -- put_desk_trading_account
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_DESK_TRADING_ACCOUNT
  --     PARTNER_DESK_TRADING_ACCOUNT_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_desk_id                Partner Desk ID
  --     p_trading_account_id_tab         Trading Account Id
  --
  --------------------------------------------------------------------------------------
 PROCEDURE  put_desk_trading_accounts( p_user                       IN partner_desk_trading_account.created_by%TYPE,
                                       p_logical_load_timestamp     IN partner_desk_trading_account.Logical_Load_Timestamp%TYPE,
                                       p_effective_start_timestamp  IN partner_desk_trading_account.Effective_Start_Timestamp%TYPE,
                                       p_partner_desk_id            IN partner_desk_trading_account.Partner_Desk_Id%TYPE,
                                       p_trading_account_id_tab     IN partner_trading_account_id_tab)
 IS
 TYPE ltype_desk_trading_account IS TABLE OF partner_desk_trading_account%ROWTYPE;
  ltab_desk_trading_account_del ltype_desk_trading_account;
  ltab_desk_trading_account_upd ltype_desk_trading_account;

 BEGIN

     logger.logger.set_module('put_desk_trading_accounts');

     ----------------------------------------------------------
     ---- Create trading account stub if it does not exists
     ----------------------------------------------------------


    FOR lv_count IN (SELECT trading_account_id FROM TABLE(CAST(p_trading_account_id_tab AS partner_trading_account_id_tab)))
       LOOP
       Nrg_Trading_Account.create_trading_account_stub(p_user => p_user
                                                      ,p_logical_load_timestamp    => p_logical_load_timestamp
                                                      ,p_effective_start_timestamp => p_effective_start_timestamp
                                                      ,p_trading_account_id        => lv_count.trading_account_id
                                                      ,p_trading_account_type      => 'CUSTOMER');
    END LOOP;

    -------------------------
    --- Select data for given partner_desk_id for which do not exist tarding_account_ids coming in a new partner_desks update
    --- Only trading_account_id is checked as trading_account_tyoe is assumed to be always 'CUSTOMER'
    -------------------------
     BEGIN

     --
     -- Deletes
     --
     SELECT old_version.partner_desk_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.trading_account_id
            ,old_version.trading_account_type
            ,old_version.Is_Deleted
            BULK COLLECT INTO ltab_desk_trading_account_del
      FROM partner_desk_trading_account old_version
        WHERE old_version.partner_desk_id=p_partner_desk_id
         AND old_version.Trading_Account_Type = 'CUSTOMER'
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_trading_account_id_tab AS partner_trading_account_id_tab)) new_version
                                 WHERE new_version.trading_account_id = old_version.trading_account_id)
        FOR UPDATE OF old_version.partner_desk_id;

     FOR lv_count IN 1..ltab_desk_trading_account_del.COUNT
       LOOP
          put_history(p_tab_desk_trading_account     => ltab_desk_trading_account_del(lv_count),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_desk_id              => p_partner_desk_id,
                      p_action                       => 'D');
      END LOOP;

     FORALL lv_count IN 1..ltab_desk_trading_account_del.COUNT
      DELETE FROM partner_desk_trading_account
       WHERE trading_account_id = ltab_desk_trading_account_del(lv_count).trading_account_id
         AND trading_account_type = 'CUSTOMER'
         AND partner_desk_id = p_partner_desk_id;

      ltab_desk_trading_account_del.delete();

     --
     -- Updates
     --

     SELECT old_version.partner_desk_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.trading_account_id
            ,old_version.trading_account_type
            ,old_version.Is_Deleted
            BULK COLLECT INTO ltab_desk_trading_account_upd
      FROM partner_desk_trading_account old_version, TABLE(CAST(p_trading_account_id_tab AS partner_trading_account_id_tab)) new_version
        WHERE  new_version.trading_account_id = old_version.trading_account_id
          AND old_version.partner_desk_id=p_partner_desk_id
          AND old_version.Trading_Account_Type = 'CUSTOMER'
          AND Nrg_Common.has_value_changed(old_version.Is_Deleted, new_version.Is_Deleted) = 1
        FOR UPDATE OF old_version.partner_desk_id;

     FOR lv_count IN 1..ltab_desk_trading_account_upd.COUNT
       LOOP
          put_history(p_tab_desk_trading_account     => ltab_desk_trading_account_upd(lv_count),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_desk_id              => p_partner_desk_id,
                      p_action                       => 'D');
      END LOOP;


      ltab_desk_trading_account_upd.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;

    --
    -- Insert only possible as there are no change on the existing combination partner_desk_id-trading_acount_id
    -- when trading_account_type = 'CUSTOMER' always
    -- Only new trading_account_id is added per partner_desk_id or it is removed (done aboved)
    --

     MERGE INTO partner_desk_trading_account old_version
      USING ( SELECT p_partner_desk_id partner_desk_id
             ,p_logical_load_timestamp logical_load_timestamp
             ,p_user created_by
             ,systimestamp create_timestamp
             ,p_user updated_by
             ,systimestamp update_timestamp
             ,p_effective_start_timestamp effective_start_timestamp
             ,trading_account_id trading_account_id
             ,'CUSTOMER' trading_account_type
             ,is_deleted is_deleted
            FROM TABLE(CAST(p_trading_account_id_tab AS partner_trading_account_id_tab))
            ) new_version
       ON (old_version.Partner_Desk_Id = new_version.Partner_Desk_Id AND
           old_version.trading_account_id = new_version.trading_account_id)
      WHEN MATCHED THEN
        UPDATE SET
             old_version.logical_load_timestamp = new_version.logical_load_timestamp
            ,old_version.update_timestamp = new_version.update_timestamp
            ,old_version.updated_by = new_version.updated_by
            ,old_version.is_deleted = new_version.is_deleted
        WHERE nrg_common.has_value_changed(old_version.is_deleted, new_version.is_deleted)=1
       WHEN NOT MATCHED THEN
       INSERT
       VALUES (new_version.partner_desk_id
              ,new_version.logical_load_timestamp
              ,new_version.created_by
              ,new_version.create_timestamp
              ,new_version.updated_by
              ,new_version.update_timestamp
              ,new_version.effective_start_timestamp
              ,new_version.trading_account_id
              ,new_version.trading_account_type
              ,new_version.is_deleted);


    logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
 END put_desk_trading_accounts;

  -- ===================================================================================
  -- put_desk_agents
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_DESK_AGENTS
  --     PARTNER_DESK_AGENTS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_desk_id                Partner Desk ID
  --     p_partner_agent_id_tab           Partner Agent ID table
  --
  --------------------------------------------------------------------------------------


  PROCEDURE put_desk_agents( p_user                      IN partner_desk_agents.created_by%TYPE,
                             p_logical_load_timestamp    IN partner_desk_agents.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp IN partner_desk_agents.effective_start_timestamp%TYPE,
                             p_partner_desk_id           IN partner_desk_agents.partner_desk_id%TYPE,
                             p_partner_agent_id_tab      IN partner_agent_id_tab) IS

  TYPE ltype_desk_agents IS TABLE OF partner_desk_agents%ROWTYPE;
  ltab_deleted_desk_agents ltype_desk_agents;
  ltab_updated_desk_agents ltype_desk_agents;

  TYPE ltype_partner_agents IS TABLE OF Partner_Agents.Partner_Agent_Id%TYPE;
  ltab_partner_agents ltype_partner_agents;

  l_partner_agent_exist SMALLINT := 0;

 BEGIN

       logger.logger.set_module('put_desk_agents');

    --------------------------------------------------------------------------------------
    --- Create partner agent stub
    --------------------------------------------------------------------------------------
    SELECT partner_agent_id BULK COLLECT INTO ltab_partner_agents
    FROM TABLE(CAST(p_partner_agent_id_tab AS partner_agent_id_tab));


 IF(ltab_partner_agents IS NOT NULL) THEN
   FOR lv_count IN 1..ltab_partner_agents.COUNT LOOP

   IF (ltab_partner_agents(lv_count) IS NOT NULL) THEN
    SELECT COUNT(1) INTO l_partner_agent_exist
     FROM partner_agents pa
    WHERE pa.Partner_Agent_Id = ltab_partner_agents(lv_count);

    IF(l_partner_agent_exist = 0) THEN
      create_partner_agent_stub(p_user => p_user
                               ,p_logical_load_timestamp => p_logical_load_timestamp
                               ,p_effective_start_timestamp => p_effective_start_timestamp
                               ,p_partner_agent_id => ltab_partner_agents(lv_count));
    END IF;
   END IF;

   END LOOP;
  END IF;

    -------------------------
    --- Select for delete/update
    -------------------------
     BEGIN

     ---
     -- Delete
     ---

     SELECT old_version.partner_desk_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.partner_agent_id
            ,old_version.Is_Deleted
            BULK COLLECT INTO ltab_deleted_desk_agents
      FROM partner_desk_agents old_version
        WHERE old_version.partner_desk_id=p_partner_desk_id
          AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_partner_agent_id_tab AS partner_agent_id_tab)) new_version
                                 WHERE new_version.partner_agent_id=old_version.partner_agent_id)
        FOR UPDATE OF old_version.partner_desk_id;


     FOR l_vcount IN 1..ltab_deleted_desk_agents.COUNT
       LOOP
          put_history(p_tab_old_desk_agents          => ltab_deleted_desk_agents(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_desk_id              => p_partner_desk_id,
                      p_action                       => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_deleted_desk_agents.COUNT
      DELETE FROM partner_desk_agents
       WHERE partner_agent_id = ltab_deleted_desk_agents(l_vcount).partner_agent_id
         AND partner_desk_id = p_partner_desk_id;

      ltab_deleted_desk_agents.delete();


      ---
      -- Update
      ---
      SELECT old_version.partner_desk_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.partner_agent_id
            ,old_version.Is_Deleted
            BULK COLLECT INTO ltab_updated_desk_agents
      FROM partner_desk_agents old_version, TABLE(CAST(p_partner_agent_id_tab AS partner_agent_id_tab)) new_version
        WHERE old_version.partner_agent_id=new_version.partner_agent_id
          AND old_version.partner_desk_id=p_partner_desk_id
          AND bi_ods.Nrg_Common.has_value_changed(old_version.Is_Deleted, new_version.Is_Deleted) = 1
        FOR UPDATE OF old_version.partner_desk_id;

           FOR l_vcount IN 1..ltab_updated_desk_agents.COUNT
             LOOP
                put_history(p_tab_old_desk_agents          => ltab_updated_desk_agents(l_vcount),
                            p_effective_end_timestamp      => p_effective_start_timestamp,
                            p_partner_desk_id              => p_partner_desk_id,
                            p_action                       => 'U');
            END LOOP;


        ltab_updated_desk_agents.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;


    --
    --
    --
     MERGE INTO partner_desk_agents old_version USING
                      (SELECT p_partner_desk_id AS partner_desk_id
                             ,p_logical_load_timestamp AS logical_load_timestamp
                             ,p_user AS created_by
                             ,systimestamp AS create_timestamp
                             ,p_user AS updated_by
                             ,systimestamp AS update_timestamp
                             ,p_effective_start_timestamp AS effective_start_timestamp
                             ,partner_agent_id AS partner_agent_id
                             ,is_deleted is_deleted
                       FROM TABLE(CAST(p_partner_agent_id_tab AS partner_agent_id_tab))) new_version
      ON (old_version.partner_desk_id=new_version.partner_desk_id AND
          old_version.partner_agent_id=new_version.partner_agent_id)
       WHEN MATCHED THEN
         UPDATE
         SET old_version.logical_load_timestamp = new_version.logical_load_timestamp
             ,old_version.updated_by = new_version.updated_by
             ,old_version.update_timestamp = new_version.update_timestamp
             ,old_version.is_deleted = new_version.is_deleted
       WHERE (bi_ods.Nrg_Common.has_value_changed(old_version.is_deleted, new_version.is_deleted) = 1)
      WHEN NOT MATCHED THEN
        INSERT
         VALUES
          (new_version.partner_desk_id
          ,new_version.logical_load_timestamp
          ,new_version.created_by
          ,new_version.create_timestamp
          ,new_version.updated_by
          ,new_version.update_timestamp
          ,new_version.effective_start_timestamp
          ,new_version.partner_agent_id
          ,new_version.is_deleted);


       logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
END put_desk_agents;

  -- ===================================================================================
  -- put_partner_desks
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Desks
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_DESKS
  --     PARTNERS_DESKS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --   p_user
  --   p_effective_start_timestamp
  --   p_partner_desk_id
  --   p_desk_name
  --   p_function
  --   p_is_default_desk
  --   p_is_accessible_by_all_agents
  --   p_is_deleted
  --   p_partner_agent_id_tab
  --   p_trading_account_id_tab
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_partner_desks ( p_user                        IN partner_desks.created_by%TYPE,
                                p_effective_start_timestamp   IN partner_desks.effective_start_timestamp%TYPE,
                                p_partner_desk_id             IN partner_desks.Partner_Desk_Id%TYPE,
                                p_partner_id                  IN partner_desks.Partner_Id%TYPE,
                                p_desk_name                   IN partner_desks.Desk_Name%TYPE,
                                p_function                    IN partner_desks.function%TYPE,
                                p_is_default_desk             IN partner_desks.is_default_desk%TYPE,
                                p_is_accessible_by_all_agents IN partner_desks.is_accessible_by_all_agents%TYPE,
                                p_is_deleted                  IN partner_desks.is_deleted%TYPE,
                                p_partner_agent_id_tab        IN partner_agent_id_tab,
                                p_trading_account_id_tab      in partner_trading_account_id_tab,
                                p_allocation_account_id       IN partner_desks.allocation_account_id%TYPE,
                                p_copy_emails_to_employees    IN partner_desks.copy_emails_to_employees%TYPE) IS


  ltab_old_partner_desks       partner_desks%ROWTYPE;
  lv_logical_load_timestamp    partner_desks.logical_load_timestamp%TYPE;

  l_count_partner SMALLINT;

  BEGIN

   logger.logger.set_module('put_partner_desks');

   lv_logical_load_timestamp := SYSTIMESTAMP;

   -------
   --- Check if stub for partner_id is required
   -------
  IF(p_partner_id IS NOT NULL) THEN
   SELECT COUNT(1)
   INTO l_count_partner
   FROM partners p
   WHERE partner_id = p_partner_id;

   -- create stub for partner
   IF (l_count_partner = 0) THEN
       create_partner_stub (p_user                       => p_user,
                            p_logical_load_timestamp     => lv_logical_load_timestamp,
                            p_effective_start_timestamp  => p_effective_start_timestamp,
                            p_partner_id                 => p_partner_id);
   END IF;
  END IF;

   BEGIN
       SELECT old_version.partner_desk_id
               ,old_version.partner_id
               ,old_version.logical_load_timestamp
               ,old_version.created_by
               ,old_version.create_timestamp
               ,old_version.updated_by
               ,old_version.update_timestamp
               ,old_version.effective_start_timestamp
               ,old_version.desk_name
               ,old_version.function
               ,old_version.is_default_desk
               ,old_version.is_accessible_by_all_agents
               ,old_version.is_deleted
               ,old_version.allocation_account_id
               ,old_version.copy_emails_to_employees
        INTO ltab_old_partner_desks
      FROM ( SELECT p_partner_desk_id partner_desk_id
                    ,p_partner_id  partner_id
                    ,p_desk_name desk_name
                    ,p_function function
                    ,p_is_default_desk is_default_desk
                    ,p_is_accessible_by_all_agents is_accessible_by_all_agents
                    ,p_is_deleted is_deleted
                    ,p_allocation_account_id allocation_account_id
                    ,p_copy_emails_to_employees copy_emails_to_employees
                    FROM dual ) new_version,
           partner_desks old_version
      WHERE new_version.partner_desk_id=old_version.partner_desk_id
        AND (nrg_common.has_value_changed(new_version.partner_id, old_version.partner_id) = 1 OR
             nrg_common.has_value_changed(new_version.desk_name, old_version.desk_name) = 1 OR
             nrg_common.has_value_changed(new_version.function, old_version.function) = 1 OR
             nrg_common.has_value_changed(new_version.is_default_desk, old_version.is_default_desk) = 1 OR
             nrg_common.has_value_changed(new_version.is_accessible_by_all_agents, old_version.is_accessible_by_all_agents) = 1 OR
             nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(new_version.allocation_account_id, old_version.allocation_account_id) = 1 OR
             nrg_common.has_value_changed(new_version.copy_emails_to_employees, old_version.copy_emails_to_employees) = 1)
       FOR UPDATE OF old_version.partner_desk_id;


        put_history(p_tab_old_partner_desks        => ltab_old_partner_desks,
                    p_effective_end_timestamp      => p_effective_start_timestamp,
                    p_action                       => 'U');

       EXCEPTION
         WHEN NO_DATA_FOUND  THEN
            NULL;
      END;

   --
    -- Insert/Update the new data
    --
     MERGE  INTO  partner_desks old_version
      USING ( SELECT p_partner_desk_id partner_desk_id
                    ,p_partner_id  partner_id
                    ,p_desk_name desk_name
                    ,p_function function
                    ,p_is_default_desk is_default_desk
                    ,p_is_accessible_by_all_agents is_accessible_by_all_agents
                    ,p_is_deleted is_deleted
                    ,p_allocation_account_id allocation_account_id
                    ,p_copy_emails_to_employees copy_emails_to_employees
                    FROM dual ) new_version
      ON (old_version.partner_desk_id = new_version.partner_desk_id)
      WHEN MATCHED THEN
      UPDATE
      SET   logical_load_timestamp = lv_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           ,partner_id = new_version.partner_id
           ,desk_name = new_version.desk_name
           ,function = new_version.function
           ,is_default_desk = new_version.is_default_desk
           ,is_accessible_by_all_agents = new_version.is_accessible_by_all_agents
           ,is_deleted = new_version.is_deleted
           ,allocation_account_id = new_version.allocation_account_id
           ,copy_emails_to_employees = new_version.copy_emails_to_employees
       WHERE new_version.partner_desk_id=old_version.partner_desk_id
       AND (nrg_common.has_value_changed(new_version.partner_id, old_version.partner_id) = 1 OR
             nrg_common.has_value_changed(new_version.desk_name, old_version.desk_name) = 1 OR
             nrg_common.has_value_changed(new_version.function, old_version.function) = 1 OR
             nrg_common.has_value_changed(new_version.is_default_desk, old_version.is_default_desk) = 1 OR
             nrg_common.has_value_changed(new_version.is_accessible_by_all_agents, old_version.is_accessible_by_all_agents) = 1 OR
             nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(new_version.copy_emails_to_employees, old_version.copy_emails_to_employees) = 1 OR
             nrg_common.has_value_changed(new_version.allocation_account_id, old_version.allocation_account_id) = 1)
      WHEN NOT MATCHED THEN
       INSERT   (partner_desk_id
               ,partner_id
               ,logical_load_timestamp
               ,created_by
               ,create_timestamp
               ,updated_by
               ,update_timestamp
               ,effective_start_timestamp
               ,desk_name
               ,function
               ,is_default_desk
               ,is_accessible_by_all_agents
               ,is_deleted
               ,allocation_account_id
               ,copy_emails_to_employees)
      VALUES (new_version.Partner_Desk_Id
             ,new_version.partner_id
             ,lv_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.desk_name
             ,new_version.function
             ,new_version.is_default_desk
             ,new_version.is_accessible_by_all_agents
             ,new_version.is_deleted
             ,new_version.allocation_account_id
             ,new_version.copy_emails_to_employees);


     --------------------
     --- put p_employee_id_tab
     --------------------

          put_desk_agents( p_user                       => p_user,
                           p_logical_load_timestamp     => lv_logical_load_timestamp,
                           p_effective_start_timestamp  => p_effective_start_timestamp,
                           p_partner_desk_id            => p_partner_desk_id,
                           p_partner_agent_id_tab       => p_partner_agent_id_tab);

     --------------------
     --- put p_trading_account_id_tab
     --------------------

          put_desk_trading_accounts( p_user                       => p_user,
                                    p_logical_load_timestamp     => lv_logical_load_timestamp,
                                    p_effective_start_timestamp  => p_effective_start_timestamp,
                                    p_partner_desk_id            => p_partner_desk_id,
                                    p_trading_account_id_tab     => p_trading_account_id_tab);



     logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
 END put_partner_desks;

  -- ===================================================================================
  -- put_partner_agents
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner Desks
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_AGENTS
  --     PARTNER_AGENTS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --   p_user
  --   p_effective_start_timestamp
  --   p_partner_agent_id
  --   p_partner_id
  --   p_person_id
  --   p_employee_id
  --   p_allowed_access_to_all_desks
  --   p_is_deleted
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_partner_agents (p_user                       IN partner_agents.created_by%TYPE,
                                p_effective_start_timestamp   IN partner_agents.effective_start_timestamp%TYPE,
                                p_partner_agent_id            IN partner_agents.Partner_Agent_Id%TYPE,
                                p_partner_id                  IN partner_agents.Partner_Id%TYPE,
                                p_person_id                   IN partner_agents.Person_Id%TYPE,
                                p_employee_id                 IN partner_agents.Employee_Id%TYPE,
                                p_allowed_access_to_all_desks IN partner_agents.Allowed_Access_To_All_Desks%TYPE,
                                p_is_deleted                  IN partner_agents.Is_Deleted%TYPE,
                                p_primary_desk_id             IN partner_agents.Primary_Desk_Id%TYPE,
                                p_person_title                IN partner_agents.person_title%TYPE,
                                 p_spoken_language              IN partner_agents.spoken_language%TYPE
                                ) IS


  ltab_old_partner_agents      partner_agents%ROWTYPE;
  lv_logical_load_timestamp    partner_agents.logical_load_timestamp%TYPE;

  l_count_partner      SMALLINT :=0;
  l_count_person       SMALLINT :=0;
  l_count_partner_desk SMALLINT :=0;

  BEGIN

   logger.logger.set_module('put_partner_agents');

   lv_logical_load_timestamp := SYSTIMESTAMP;

  IF(p_partner_id IS NOT NULL) THEN

   SELECT COUNT(1) INTO l_count_partner
   FROM partners
   WHERE partner_id = p_partner_id;

   IF (l_count_partner=0) THEN
    create_partner_stub(p_user => p_user
                       ,p_logical_load_timestamp => lv_logical_load_timestamp
                       ,p_effective_start_timestamp => p_effective_start_timestamp
                       ,p_partner_id => p_partner_id);

    END IF;
   END IF;

 IF (p_person_id IS NOT NULL) THEN

   SELECT COUNT(1) INTO l_count_person
   FROM persons
   WHERE person_id = p_person_id;

  IF (l_count_person=0) THEN
    Nrg_Person.create_person_stub(p_user => p_user
                                 ,p_logical_load_timestamp => lv_logical_load_timestamp
                                 ,p_effective_start_timestamp => p_effective_start_timestamp
                                 ,p_person_id => p_person_id);

  END IF;
 END IF;

 IF(p_primary_desk_id IS NOT NULL) THEN

   SELECT COUNT(1) INTO l_count_partner_desk
   FROM Partner_Desks
   WHERE partner_desk_id= p_primary_desk_id;

   IF(l_count_partner_desk = 0) THEN
      create_partner_desk_stub(p_user                       => p_user,
                               p_logical_load_timestamp     => lv_logical_load_timestamp,
                               p_effective_start_timestamp  => p_effective_start_timestamp,
                               p_partner_id                 => p_partner_id,
                               p_partner_desk_id            => p_primary_desk_id);
   END IF;
 END IF;


     BEGIN
       SELECT old_version.partner_agent_id
              ,old_version.partner_id
              ,old_version.person_id
              ,old_version.employee_id
               ,old_version.logical_load_timestamp
               ,old_version.created_by
               ,old_version.create_timestamp
               ,old_version.updated_by
               ,old_version.update_timestamp
               ,old_version.effective_start_timestamp
               ,old_version.allowed_access_to_all_desks
               ,old_version.is_deleted
               ,old_version.Primary_Desk_Id
               ,old_version.person_title
               ,old_version.spoken_language
        INTO ltab_old_partner_agents
      FROM ( SELECT p_partner_agent_id partner_agent_id
                    ,p_partner_id partner_id
                    ,p_person_id person_id
                    ,p_employee_id employee_id
                    ,p_allowed_access_to_all_desks  allowed_access_to_all_desks
                    ,p_is_deleted is_deleted
                    ,p_primary_desk_id primary_desk_id
                    ,p_person_title person_title
                    ,p_spoken_language spoken_language
                    FROM dual ) new_version,
           partner_agents old_version
      WHERE new_version.partner_agent_id=old_version.partner_agent_id
        AND (nrg_common.has_value_changed(new_version.partner_id, old_version.partner_id) = 1 OR
             nrg_common.has_value_changed(new_version.person_id, old_version.person_id) = 1 OR
             nrg_common.has_value_changed(new_version.employee_id, old_version.employee_id) = 1 OR
             nrg_common.has_value_changed(new_version.allowed_access_to_all_desks, old_version.allowed_access_to_all_desks) = 1 OR
             nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(new_version.primary_desk_id, old_version.primary_desk_id) = 1 OR
             nrg_common.has_value_changed(new_version.person_title, old_version.person_title) = 1 OR
             nrg_common.has_value_changed(new_version.spoken_language, old_version.spoken_language) = 1
             )
       FOR UPDATE OF old_version.employee_id;


       put_history(p_tab_old_partner_agents        => ltab_old_partner_agents,
                    p_effective_end_timestamp      => p_effective_start_timestamp,
                    p_action                       => 'U');

       EXCEPTION
         WHEN NO_DATA_FOUND  THEN
            NULL;
       END;

    --
    -- Insert/Update the new data
    --
     MERGE  INTO  partner_agents old_version
      USING (SELECT p_partner_agent_id partner_agent_id
                    ,p_partner_id partner_id
                    ,p_person_id person_id
                    ,p_employee_id employee_id
                    ,p_allowed_access_to_all_desks  allowed_access_to_all_desks
                    ,p_is_deleted is_deleted
                    ,p_primary_desk_id primary_desk_id
                     ,p_person_title person_title
                    ,p_spoken_language spoken_language
                    FROM dual ) new_version
      ON (old_version.partner_agent_id = new_version.partner_agent_id)
      WHEN MATCHED THEN
      UPDATE
      SET  partner_id = new_version.Partner_Id
           ,person_id = new_version.Person_Id
           ,employee_id = new_version.Employee_Id
           ,logical_load_timestamp = lv_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           ,allowed_access_to_all_desks = new_version.allowed_access_to_all_desks
           ,is_deleted = new_version.is_deleted
           ,primary_desk_id = new_version.primary_desk_id
           ,person_title = new_version.person_title
           ,spoken_language = new_version.spoken_language
       WHERE (nrg_common.has_value_changed(new_version.partner_id, old_version.partner_id) = 1 OR
             nrg_common.has_value_changed(new_version.person_id, old_version.person_id) = 1 OR
             nrg_common.has_value_changed(new_version.employee_id, old_version.employee_id) = 1 OR
             nrg_common.has_value_changed(new_version.allowed_access_to_all_desks, old_version.allowed_access_to_all_desks) = 1 OR
             nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(new_version.primary_desk_id, old_version.primary_desk_id) = 1 OR
              nrg_common.has_value_changed(new_version.person_title, old_version.person_title) = 1 OR
               nrg_common.has_value_changed(new_version.spoken_language, old_version.spoken_language) = 1 )

      WHEN NOT MATCHED THEN
       INSERT   (partner_agent_id
                ,partner_id
                ,person_id
                ,employee_id
                ,logical_load_timestamp
                ,created_by
                ,create_timestamp
                ,updated_by
                ,update_timestamp
                ,effective_start_timestamp
                ,allowed_access_to_all_desks
                ,is_deleted
                ,primary_desk_id
                ,person_title
                ,spoken_language)
      VALUES (new_version.partner_agent_id
             ,new_version.partner_id
             ,new_version.person_id
             ,new_version.employee_id
             ,lv_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.allowed_access_to_all_desks
             ,new_version.is_deleted
             ,new_version.primary_Desk_Id
             ,new_version.person_title
            ,new_version.spoken_language);

    logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_partner_agents;


-- ===================================================================================
  -- put_cmc_sales_persons
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_cmc_sales_persons
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_cmc_sales_persons
  --     PARTNER_cmc_sales_persons_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_cmc_sales_persons_tab          sales person info
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_cmc_sales_persons(p_user                       IN partners.created_by%TYPE,
                                     p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                     p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                     p_partner_id                 IN partners.partner_id%TYPE,
                                     p_cmc_sales_persons_tab   IN cmc_sales_persons_tab)
  IS
  TYPE ltype_cmc_sales_persons IS TABLE OF partner_cmc_sales_persons%ROWTYPE;
  ltab_cmc_sales_persons ltype_cmc_sales_persons;

 BEGIN

     logger.logger.set_module('put_cmc_sales_persons');


    -------------------------
    --- Select for delete a row for given partner_id for which tenant_code does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.id
            ,old_version.value
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_cmc_sales_persons
      FROM partner_cmc_sales_persons old_version
        WHERE old_version.partner_id=p_partner_id
         AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_cmc_sales_persons_tab AS cmc_sales_persons_tab)) new_version
                         WHERE new_version.id=old_version.id
                         AND new_version.value = old_version.value)
        FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_cmc_sales_persons.COUNT
       LOOP
          put_history(p_tab_old_cmc_sales_persons   => ltab_cmc_sales_persons(l_vcount),
                      p_effective_end_timestamp    => p_effective_start_timestamp,
                      p_partner_id                 => p_partner_id,
                      p_action                     => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_cmc_sales_persons.COUNT
      DELETE FROM partner_cmc_sales_persons
       WHERE value = ltab_cmc_sales_persons(l_vcount).value
         AND partner_id = p_partner_id
         AND id= ltab_cmc_sales_persons(l_vcount).id;

      ltab_cmc_sales_persons.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
    SELECT old_version.partner_id
           ,old_version.logical_load_timestamp
           ,old_version.created_by
           ,old_version.create_timestamp
           ,old_version.updated_by
           ,old_version.update_timestamp
           ,old_version.effective_start_timestamp
           ,old_version.id
           ,old_version.value
           ,old_version.is_deleted
      BULK COLLECT INTO ltab_cmc_sales_persons
     FROM partner_cmc_sales_persons old_version,
          (SELECT id,value, is_deleted
              FROM TABLE(CAST(p_cmc_sales_persons_tab AS cmc_sales_persons_tab))) new_version
     WHERE old_version.partner_id = p_partner_id
       AND old_version.id = new_version.id
       AND (nrg_common.has_value_changed(new_version.value, old_version.value) = 1 OR
              nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR l_vcount IN 1..ltab_cmc_sales_persons.COUNT
       LOOP
          put_history(p_tab_old_cmc_sales_persons   => ltab_cmc_sales_persons(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'U');
      END LOOP;



     MERGE  INTO  partner_cmc_sales_persons old_version
      USING (SELECT id,value, is_deleted
              FROM TABLE(CAST(p_cmc_sales_persons_tab AS cmc_sales_persons_tab))) new_version
      ON (old_version.partner_id = p_partner_id AND
          old_version.id = new_version.id)
     WHEN MATCHED THEN
         UPDATE  SET
            is_deleted = new_version.is_deleted
           ,logical_load_timestamp = p_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           WHERE (nrg_common.has_value_changed(new_version.value, old_version.value) = 1 OR
                nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,id
             ,value
             ,is_deleted)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.id
             ,new_version.value
             ,new_version.is_deleted);



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_cmc_sales_persons;

--DR
 PROCEDURE put_key_inf_documents    (p_user                       IN partners.created_by%TYPE,
                                     p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                     p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                     p_partner_id                 IN partners.partner_id%TYPE,
                                     p_key_inf_documents_tab      IN key_inf_documents_tab)
  IS
  TYPE ltype_partner_key_inf_doc IS TABLE OF partner_key_inf_documents%ROWTYPE;
  ltab_partner_key_inf_doc ltype_partner_key_inf_doc;

 BEGIN

     logger.logger.set_module('put_key_inf_documents');


    -------------------------
    --- Select for delete a row for given partner_id for which tenant_code does not exist in a new msg
    -------------------------
    BEGIN
      SELECT old_version.partner_id
            ,old_version.product_wrapper_code
            ,old_version.asset_class_code
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.url
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_partner_key_inf_doc
      FROM PARTNER_KEY_INF_DOCUMENTS old_version
        WHERE old_version.partner_id=p_partner_id
         AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_key_inf_documents_tab AS key_inf_documents_tab)) new_version
                         WHERE new_version.product_wrapper_code=old_version.product_wrapper_code
                         AND new_version.asset_class_code=old_version.asset_class_code
                         )
        FOR UPDATE OF old_version.partner_id;

     FOR l_vcount IN 1..ltab_partner_key_inf_doc.COUNT
       LOOP
          put_history(p_tab_old_key_inf_doc   => ltab_partner_key_inf_doc(l_vcount),
                      p_effective_end_timestamp    => p_effective_start_timestamp,
                      p_partner_id                 => p_partner_id,
                      p_action                     => 'D');
      END LOOP;

     FORALL l_vcount IN 1..ltab_partner_key_inf_doc.COUNT
      DELETE FROM PARTNER_KEY_INF_DOCUMENTS
       WHERE url = ltab_partner_key_inf_doc(l_vcount).url
         AND partner_id = p_partner_id
         AND asset_class_code= ltab_partner_key_inf_doc(l_vcount).asset_class_code;

      ltab_partner_key_inf_doc.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --


    SELECT old_version.partner_id
          ,old_version.product_wrapper_code
           ,old_version.asset_class_code
           ,old_version.logical_load_timestamp
           ,old_version.created_by
           ,old_version.create_timestamp
           ,old_version.updated_by
           ,old_version.update_timestamp
           ,old_version.effective_start_timestamp
            ,old_version.url
           ,old_version.is_deleted
      BULK COLLECT INTO ltab_partner_key_inf_doc
     FROM BI_ODS.PARTNER_KEY_INF_DOCUMENTS old_version,
          (SELECT product_wrapper_code,asset_class_code,url, is_deleted
              FROM TABLE(CAST(p_key_inf_documents_tab AS key_inf_documents_tab))) new_version
     WHERE old_version.partner_id = p_partner_id
       AND old_version.asset_class_code = new_version.asset_class_code
     AND old_version.product_wrapper_code = new_version.product_wrapper_code
       AND (nrg_common.has_value_changed(new_version.url, old_version.url) = 1 OR
              nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR l_vcount IN 1..ltab_partner_key_inf_doc.COUNT
       LOOP
          put_history(p_tab_old_key_inf_doc      => ltab_partner_key_inf_doc(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_partner_id                   => p_partner_id,
                      p_action                       => 'U');
      END LOOP;


     MERGE  INTO  PARTNER_KEY_INF_DOCUMENTS old_version
      USING (SELECT product_wrapper_code, asset_class_code,url, is_deleted
              FROM TABLE(CAST(p_key_inf_documents_tab AS key_inf_documents_tab))) new_version
      ON (old_version.partner_id = p_partner_id and
         old_version.product_wrapper_code  = new_version.product_wrapper_code and
         old_version.asset_class_code = new_version.asset_class_code)
     WHEN MATCHED THEN
         UPDATE  SET
            url = new_version.url
           ,is_deleted = new_version.is_deleted
           ,logical_load_timestamp = p_logical_load_timestamp
          ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp

           WHERE partner_id = p_partner_id
           AND product_wrapper_code = new_version.product_wrapper_code
          AND asset_class_code = new_version.asset_class_code
           AND (nrg_common.has_value_changed(new_version.url, old_version.url) = 1 OR
                nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
       INSERT(partner_id
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,product_wrapper_code
             ,asset_class_code
             ,url
             ,is_deleted)
      VALUES (p_partner_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.product_wrapper_code
             ,new_version.asset_class_code
             ,new_version.url
             ,new_version.is_deleted);



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_key_inf_documents;

  -- ===================================================================================
  -- put_partner_relation
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_partner_relation
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_partner_relation
  --     PARTNER_partner_relation
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_partner_relation_tab           partner_relation info
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_partner_relation (p_user                       IN partners.created_by%TYPE,
                                  p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                  p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                  p_partner_id                 IN partners.partner_id%TYPE,
                                  p_partner_relation_tab       IN partner_relation_tab)
  IS
  TYPE ltype_partner_relation IS TABLE OF partner_partner_relation%ROWTYPE;
  ltab_partner_relation ltype_partner_relation;

 BEGIN

     logger.logger.set_module('put_partner_relation');

     BEGIN
      SELECT old_version.partner_id
            ,old_version.relation_partner_id
            ,old_version.relation
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.update_time
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_partner_relation
      FROM partner_partner_relation old_version
        WHERE old_version.partner_id = p_partner_id
         AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_partner_relation_tab AS partner_relation_tab)) new_version
                         WHERE new_version.relation_partner_id = old_version.relation_partner_id)
        FOR UPDATE OF old_version.partner_id;

     FOR lv_count IN 1..ltab_partner_relation.COUNT
       LOOP
          put_history(p_tab_old_partner_relation   => ltab_partner_relation(lv_count),
                      p_effective_end_timestamp    => p_effective_start_timestamp,
                      p_partner_id                 => p_partner_id,
                      p_action                     => 'D');
      END LOOP;

     FORALL lv_count IN 1..ltab_partner_relation.COUNT
      DELETE FROM partner_partner_relation
       WHERE partner_id = p_partner_id
         AND relation_partner_id = ltab_partner_relation(lv_count).relation_partner_id;

      ltab_partner_relation.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
     SELECT  old_version.partner_id
            ,old_version.relation_partner_id
            ,old_version.relation
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.update_time
            ,old_version.is_deleted
      BULK COLLECT INTO ltab_partner_relation
     FROM partner_partner_relation old_version,
          (SELECT * FROM TABLE(CAST(p_partner_relation_tab AS partner_relation_tab))) new_version
     WHERE old_version.partner_id          = p_partner_id
       AND old_version.relation_partner_id = new_version.relation_partner_id
       AND (nrg_common.has_value_changed(new_version.relation, old_version.relation) = 1 OR
            nrg_common.has_value_changed(new_version.update_time, old_version.update_time) = 1 OR
            nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR lv_count IN 1..ltab_partner_relation.COUNT
       LOOP
          put_history(p_tab_old_partner_relation   => ltab_partner_relation(lv_count),
                      p_effective_end_timestamp    => p_effective_start_timestamp,
                      p_partner_id                 => p_partner_id,
                      p_action                     => 'U');
      END LOOP;

     MERGE INTO partner_partner_relation old_version
      USING (SELECT * FROM TABLE(CAST(p_partner_relation_tab AS partner_relation_tab))) new_version
      ON (old_version.partner_id          = p_partner_id AND
          old_version.relation_partner_id = new_version.relation_partner_id)
     WHEN MATCHED THEN UPDATE
      SET   old_version.updated_by                       = p_user,
            old_version.update_timestamp                 = systimestamp,
            old_version.effective_start_timestamp        = p_effective_start_timestamp,
            old_version.logical_load_timestamp           = p_logical_load_timestamp,
            old_version.relation                         = new_version.relation,
            old_version.update_time                      = new_version.update_time,
            old_version.is_deleted                       = new_version.is_deleted
           WHERE (nrg_common.has_value_changed(new_version.relation, old_version.relation) = 1 OR
                  nrg_common.has_value_changed(new_version.update_time, old_version.update_time) = 1 OR
                  nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
      INSERT (partner_id
             ,relation_partner_id
             ,relation
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,update_time
             ,is_deleted)
      VALUES (p_partner_id
             ,new_version.relation_partner_id
             ,new_version.relation
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.update_time
             ,new_version.is_deleted);

   logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END put_partner_relation;

  -- ===================================================================================
  -- put_partner_onboarding_pfm
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_partner_onboarding_pfm
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNER_partner_onboarding_pfm
  --     PARTNER_partner_onboarding_pfm
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_partner_id                     Partner ID
  --     p_partner_onboarding_pfm_tab           partner_onboarding_pfm info
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_partner_onboarding_pfm (p_user                       IN partners.created_by%TYPE,
                                        p_logical_load_timestamp     IN partners.logical_load_timestamp%TYPE,
                                        p_effective_start_timestamp  IN partners.effective_start_timestamp%TYPE,
                                        p_partner_id                 IN partners.partner_id%TYPE,
                                        p_partner_onboarding_pfm_tab IN partner_onboarding_pltfrm_tab)
  IS
  TYPE ltype_partner_onboarding_pfm IS TABLE OF partner_onboarding_platforms%ROWTYPE;
  ltab_partner_onboarding_pfm ltype_partner_onboarding_pfm;

 BEGIN

     logger.logger.set_module('put_partner_onboarding_pfm');

     BEGIN
      SELECT old_version.partner_id
            ,old_version.source_platform
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.is_deleted
            BULK COLLECT INTO ltab_partner_onboarding_pfm
      FROM partner_onboarding_platforms old_version
        WHERE old_version.partner_id = p_partner_id
         AND NOT EXISTS (SELECT * FROM TABLE(CAST(p_partner_onboarding_pfm_tab AS partner_onboarding_pltfrm_tab)) new_version
                         WHERE new_version.source_platform = old_version.source_platform)
        FOR UPDATE OF old_version.partner_id;

     FOR lv_count IN 1..ltab_partner_onboarding_pfm.COUNT
       LOOP
          put_history(p_tab_old_partner_onbdg_pfm        => ltab_partner_onboarding_pfm(lv_count),
                      p_effective_end_timestamp          => p_effective_start_timestamp,
                      p_partner_id                       => p_partner_id,
                      p_action                           => 'D');
      END LOOP;

     FORALL lv_count IN 1..ltab_partner_onboarding_pfm.COUNT
      DELETE FROM partner_onboarding_platforms
       WHERE partner_id = p_partner_id
         AND source_platform = ltab_partner_onboarding_pfm(lv_count).source_platform;

      ltab_partner_onboarding_pfm.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- Insert/Update the new data
    --
     SELECT  old_version.partner_id
            ,old_version.source_platform
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.is_deleted
      BULK COLLECT INTO ltab_partner_onboarding_pfm
     FROM partner_onboarding_platforms old_version,
          (SELECT * FROM TABLE(CAST(p_partner_onboarding_pfm_tab AS partner_onboarding_pltfrm_tab))) new_version
     WHERE old_version.partner_id          = p_partner_id
       AND old_version.source_platform     = new_version.source_platform
       AND (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1);

     FOR lv_count IN 1..ltab_partner_onboarding_pfm.COUNT
       LOOP
          put_history(p_tab_old_partner_onbdg_pfm   => ltab_partner_onboarding_pfm(lv_count),
                      p_effective_end_timestamp          => p_effective_start_timestamp,
                      p_partner_id                       => p_partner_id,
                      p_action                           => 'U');
      END LOOP;

     MERGE INTO partner_onboarding_platforms old_version
      USING (SELECT * FROM TABLE(CAST(p_partner_onboarding_pfm_tab AS partner_onboarding_pltfrm_tab))) new_version
      ON (old_version.partner_id          = p_partner_id AND
          old_version.source_platform     = new_version.source_platform)
     WHEN MATCHED THEN UPDATE
      SET   old_version.updated_by                       = p_user,
            old_version.update_timestamp                 = systimestamp,
            old_version.effective_start_timestamp        = p_effective_start_timestamp,
            old_version.logical_load_timestamp           = p_logical_load_timestamp,
            old_version.is_deleted                       = new_version.is_deleted
           WHERE (nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1)
     WHEN NOT MATCHED THEN
      INSERT (partner_id
             ,source_platform
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             ,is_deleted)
      VALUES (p_partner_id
             ,new_version.source_platform
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.is_deleted);

   logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END put_partner_onboarding_pfm;

  -- ===================================================================================
  -- put_customer_segregations
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put customer_segregations
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     customer_segregations
  --     customer_segregations_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_customer_id                    customer id
  --     p_old_customer_version           Account Version of the existing record on ODS
  --     p_new_customer_version           Account Version of the new record that is received
  --     partner_mc_segregations          partner_mc_segregations
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_partner_mc_segregations(p_user                        partner_mc_segregations.created_by%TYPE,
                                        p_logical_load_timestamp      partner_mc_segregations.logical_load_timestamp%TYPE,
                                        p_effective_start_timestamp   partner_mc_segregations.effective_start_timestamp%TYPE,
                                        p_partner_id                  partner_mc_segregations.partner_id%TYPE,
                                        p_customer_id                 partner_mc_segregations.customer_id%TYPE,
                                        p_partner_mc_segregations     partner_mc_segregations_tab) IS

    TYPE l_old_records_tab IS TABLE OF partner_mc_segregations%ROWTYPE;
    lv_old_records l_old_records_tab;

  BEGIN

    logger.logger.set_module('put_partner_mc_segregations');

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM TABLE(CAST(p_partner_mc_segregations AS partner_mc_segregations_tab)) new_records,
               partner_mc_segregations old_records
         WHERE old_records.partner_id      = p_partner_id
           AND old_records.customer_id     = p_customer_id
           AND old_records.segregation_id  = new_records.segregation_id
           AND (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
                nrg_common.has_value_changed(old_records.is_segregated, new_records.is_segregated) = 1);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP
          put_history_seg(p_action                    => 'U',
                          p_effective_end_timestamp   => p_effective_start_timestamp,
                          p_old_record                => lv_old_records(lv_cnt));
        END LOOP;

        SELECT old_records.*
          BULK COLLECT INTO lv_old_records
          FROM partner_mc_segregations old_records
         WHERE partner_id  = p_partner_id
           AND customer_id = p_customer_id
           AND segregation_id NOT IN (SELECT segregation_id
                                        FROM TABLE(CAST(p_partner_mc_segregations AS partner_mc_segregations_tab)));
        --
        --Write deleted records to history
        --
        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history_seg(p_action                    => 'D',
                          p_effective_end_timestamp   => p_effective_start_timestamp,
                          p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
        --
        --Delete reocrds from base table
        --
        DELETE partner_mc_segregations old_records
         WHERE partner_id  = p_partner_id
           AND customer_id = p_customer_id AND
               segregation_id NOT IN (SELECT segregation_id
                                        FROM TABLE(CAST(p_partner_mc_segregations AS partner_mc_segregations_tab)));
        --
        --Write New records, updated records to base table
        --
        MERGE INTO partner_mc_segregations old_records USING
             (SELECT segregation_id,
                     p_partner_id                   partner_id,
                     p_customer_id                  customer_id,
                     p_user                         created_by,
                     SYSTIMESTAMP                   message_time,
                     p_effective_start_timestamp    effective_start_timestamp,
                     legal_entity,
                     is_segregated
                FROM TABLE(CAST(p_partner_mc_segregations AS partner_mc_segregations_tab)) aa) new_records
            ON (old_records.partner_id     = new_records.partner_id
            AND old_records.customer_id    = new_records.customer_id
            AND old_records.segregation_id = new_records.segregation_id)
        WHEN MATCHED THEN UPDATE
            SET old_records.updated_by                    = new_records.created_by,
                old_records.update_timestamp              = new_records.message_time,
                old_records.logical_load_timestamp        = SYSTIMESTAMP,
                old_records.effective_start_timestamp     = new_records.effective_start_timestamp,
                old_records.legal_entity                  = new_records.legal_entity,
                old_records.is_segregated                 = new_records.is_segregated
         WHERE (nrg_common.has_value_changed(old_records.legal_entity, new_records.legal_entity) = 1 OR
                nrg_common.has_value_changed(old_records.is_segregated, new_records.is_segregated) = 1)
        WHEN NOT MATCHED THEN INSERT
               (partner_id,
                segregation_id,
                customer_id,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                legal_entity,
                is_segregated)
        VALUES (new_records.partner_id,
                new_records.segregation_id,
                new_records.customer_id,
                SYSTIMESTAMP,
                new_records.created_by,
                new_records.message_time,
                new_records.created_by,
                new_records.message_time,
                new_records.effective_start_timestamp,
                new_records.legal_entity,
                new_records.is_segregated);

/*         SELECT segregation_id,
                p_customer_id,
                SYSTIMESTAMP logical_load_timestamp,
                p_user created_by,
                SYSTIMESTAMP create_timestamp,
                p_user updated_by,
                SYSTIMESTAMP update_timestamp,
                p_effective_start_timestamp,
                legal_entity,
                is_segregated
           BULK COLLECT INTO lv_old_records
           FROM TABLE(CAST(p_partner_mc_segregations AS partner_mc_segregations_tab))
          WHERE (segregation_id,
                 legal_entity,
                 is_segregated) NOT IN (SELECT segregation_id,
                                       legal_entity,
                                       is_segregated
                                  FROM partner_mc_segregations
                                 WHERE customer_id = p_customer_id
                                 UNION ALL
                                SELECT segregation_id,
                                       legal_entity,
                                       is_segregated
                                  FROM partner_mc_segregations_h
                                 WHERE customer_id = p_customer_id);

        FOR lv_cnt IN 1..lv_old_records.COUNT LOOP

          put_history_seg(p_action                    => 'I',
                          p_effective_end_timestamp   => p_effective_start_timestamp,
                          p_old_record                => lv_old_records(lv_cnt));

        END LOOP;
*/
    logger.logger.set_module(NULL);

  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END;

  -- ===================================================================================
  -- put_partner
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Partner
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PARTNERS
  --     PARTNERS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --   p_user
  --   p_effective_start_timestamp
  --   p_is_deleted
  --   p_partner_id
  --   p_version
  --   p_partner_name
  --   p_source_platform
  --   p_source_partner_id
  --   p_agreement_type
  --   p_agreement_conditions
  --   p_is_white_labelled
  --   p_profit_centre_name
  --   p_company_id
  --   p_email_type
  --   p_onboarding_countries_tab
  --   p_external_reference
  --   p_service_offering
  --   p_live_help_enabled
  --   p_active
  --   p_revenue_stream_config_tab
  --   p_ca_schm_cd
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------
  PROCEDURE put_partner (p_user                         IN partners.created_by%TYPE,
                         p_effective_start_timestamp    IN partners.effective_start_timestamp%TYPE,
                         p_is_deleted                   IN partners.is_deleted%TYPE,
                         p_partner_id                   IN partners.partner_id%TYPE,
                         p_version                      IN partners.version%TYPE,
                         p_partner_name                 IN partners.partner_name%TYPE,
                         p_source_platform              IN partners.source_platform%TYPE,
                         p_source_partner_id            IN partners.source_partner_id%TYPE,
                         p_agreement_type               IN partners.agreement_type%TYPE,
                         p_agreement_conditions         IN partners.agreement_conditions%TYPE,
                         p_is_white_labelled            IN partners.is_white_labelled%TYPE,
                         p_profit_centre_name           IN partners.profit_centre_name%TYPE,
                         p_company_id                   IN partners.company_id%TYPE,
                         p_email_type                   IN partners.email_type%TYPE,
                         p_onboarding_countries_tab     IN onboarding_countries_tab,
                         p_external_reference           IN partners.External_Reference%TYPE,
                         p_service_offering             IN partners.Service_Offering%TYPE,
                         p_live_help_enabled            IN partners.Live_Help_Enabled%TYPE,
                         p_active                       IN partners.Active%TYPE,
                         p_revenue_stream_confg_tab     IN revenue_stream_confg_tab,
                         p_ca_schm_cd                   IN partners.Ca_Schm_Cd%TYPE,
                         p_onboarding_currencies_tab    IN partner_onboarding_crrn_tab,
                         p_market_data_taxable          IN partners.Is_Market_Data_Taxable%TYPE,
                         p_cash_account_currency        IN partners.Cash_Account_Ccy%TYPE,
                         p_allow_credit                 IN partners.Allow_Credit%TYPE,
                         p_allow_acc_open_from_platform IN partners.Allow_Acc_Open_From_Platform%TYPE,
                         p_partner_function             IN partners.partner_function%TYPE,
                         p_relationship_country         IN partners.Relationship_Country%TYPE,
                         p_mrkt_cntrpart_customer_id    IN partners.Mrkt_Cntrpart_Customer_Id%TYPE,
                         p_mrkt_cntrpart_short_code     IN partners.Mrkt_Cntrpart_Short_Code%TYPE,
                         p_mrkt_cntrpart_live_tenant_tm IN partners.Mrkt_Cntrpart_Live_Tenant_Tmpl%TYPE,
                         p_mrkt_cntrpart_demo_tenant_tm IN partners.Mrkt_Cntrpart_Demo_Tenant_Tmpl%TYPE,
                         p_mrkt_cntrpart_cash_account_s IN partners.Mrkt_Cntrpart_Cash_Account_Seg%TYPE,
                         p_onboarding_languages_tab     IN onboarding_languages_tab,
                         p_restricted_plat_feat_tab     IN restricted_plat_feat_tab,
                         p_statement_format             IN partners.statement_format%TYPE,
                         p_is_email_requested           IN partners.is_email_requested%TYPE,
                         p_creation_date                IN partners.creation_date%TYPE,
                         p_website                      IN partners.website%TYPE,
                         p_termination_notice_period    IN partners.termination_notice_period%TYPE,
                         p_cmc_relationship_manager     IN partners.cmc_relationship_manager%TYPE,
                         p_terminated                   IN partners.terminated%TYPE,
                         p_termination_date             IN partners.termination_date%TYPE,
                         p_mrkt_cntrpart_plat_url_s_dom IN partners.mrkt_cntrpart_plat_url_sub_dom%TYPE,
                         p_cmc_sales_persons_tab        IN cmc_sales_persons_tab,
                         p_tax_declarations             IN tax_declaration_tab,
                         p_key_inf_documents_tab        IN key_inf_documents_tab,
                         p_is_allowed_funding           IN partners.is_allowed_funding%TYPE,
                         p_last_remediation_date        IN partners.last_remediation_date%TYPE,
                         p_rest_of_world_onboarding_url IN partners.rest_of_world_onboarding_url%TYPE,
                         p_partner_relations            IN partner_relation_tab,
                         p_partner_onboarding_pltfrms   IN partner_onboarding_pltfrm_tab,
                         p_partner_mc_segregations      IN partner_mc_segregations_tab,
                         p_is_sftp_requested            IN partners.is_sftp_requested%TYPE,
                         p_is_pay_spread_to_partner     IN partners.is_pay_spread_to_partner%TYPE,
                         p_is_fofa_enabled              IN partners.is_fofa_enabled%TYPE,
                         p_additional_extract_time_format IN partners.additional_extract_time_format%TYPE,
                         p_is_internal                  IN partners.is_internal%TYPE,
                         p_cmc_sales_introducer_reference IN partners.cmc_sales_introducer_reference%TYPE
                        )
  IS

  ltab_old_partners            partners%ROWTYPE;

  l_company_id                 partners.company_id%TYPE;
  l_company_id_exist           SMALLINT :=0;

  lv_logical_load_timestamp    partners.logical_load_timestamp%TYPE;
  l_source_platform            partners.source_platform%TYPE;
  l_profit_centre_name         partners.Profit_Centre_Name%TYPE;

  BEGIN

   logger.logger.set_module('put_partner');
   lv_logical_load_timestamp := SYSTIMESTAMP;


   IF p_source_platform = 'CMC Marketmaker (CFD/FX)'
     THEN l_source_platform := 'MMCFD';
   ELSIF p_source_platform = 'CMC Marketmaker (SB)'
     THEN l_source_platform := 'MMSB';
   ELSE
       l_source_platform := p_source_platform;
   END IF;


   --------------------
   ---- Determine if any new company_id is coming within partner_tab
   --------------------

  IF (p_company_id IS NOT NULL) THEN
   SELECT COUNT(1)
   INTO l_company_id_exist
   FROM companies
   WHERE company_id = p_company_id;

      --
      -- This is to ensure that the company_id record exists for the referential integrity
      --
    IF (l_company_id_exist = 0) THEN
       nrg_company.create_company_stub(p_user => p_user
                                  ,p_logical_load_timestamp => lv_logical_load_timestamp
                                  ,p_effective_start_timestamp => p_effective_start_timestamp
                                  ,p_company_id => p_company_id);
    END IF;
   END IF;

   --------------------
   ---- Determine the profit centre name based on ca_schm_cd (cash_account_schema_definition_code)
   --------------------

   IF(p_profit_centre_name IS NULL AND p_ca_schm_cd IS NOT NULL)
      THEN

         IF(p_ca_schm_cd = 'CMC-CFD') THEN l_profit_centre_name:='CMC-CFDUK';
          ELSIF(p_ca_schm_cd = 'CMC-CFDAU') THEN l_profit_centre_name:='CMC-CFDAU';
          ELSIF(p_ca_schm_cd = 'CMC-CFDSG') THEN l_profit_centre_name:='CMC-CFDSG';
          ELSIF(p_ca_schm_cd = 'CMC-CFDNZ') THEN l_profit_centre_name:='CMC-CFDNZ';
          ELSIF(p_ca_schm_cd = 'CMC-CFDCA') THEN l_profit_centre_name:='CMC-CFDCA';
          ELSIF(p_ca_schm_cd = 'CMC-SB') THEN l_profit_centre_name:='SB London - UK Resident';
          ELSIF(p_ca_schm_cd = 'CMC-DEMO') THEN l_profit_centre_name:='Dummy';
          ELSIF(p_ca_schm_cd = 'CMC-DE') THEN l_profit_centre_name:='CMC-DE-CFDDE';
          ELSIF(p_ca_schm_cd = 'CMC-CFDME') THEN l_profit_centre_name:='CMC-CFDAE';
          ELSE l_profit_centre_name := p_ca_schm_cd;
         END IF;
       ELSE
         l_profit_centre_name := p_profit_centre_name;
   END IF;



    --
    -- Find partners if exist in the partners table, prepare table for update and then put the old recored to history
    --

    BEGIN
        SELECT old_version.partner_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.version
            ,old_version.partner_name
            ,old_version.source_platform
            ,old_version.source_partner_id
            ,old_version.agreement_type
            ,old_version.agreement_conditions
            ,old_version.is_white_labelled
            ,old_version.profit_centre_name
            ,old_version.company_id
            ,old_version.email_type
            ,old_version.external_reference
            ,old_version.Service_Offering
            ,old_version.Live_Help_Enabled
            ,old_version.Active
            ,old_version.Is_Deleted
            ,old_version.Ca_Schm_Cd
            ,old_version.Is_Market_Data_Taxable
            ,old_version.Cash_Account_Ccy
            ,old_version.Allow_Credit
            ,old_version.Allow_Acc_Open_From_Platform
            ,old_version.Partner_Function
            ,old_version.Relationship_Country
            ,old_version.Mrkt_Cntrpart_Customer_Id
            ,old_version.Mrkt_Cntrpart_Short_Code
            ,old_version.Mrkt_Cntrpart_Live_Tenant_Tmpl
            ,old_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl
            ,old_version.Mrkt_Cntrpart_Cash_Account_Seg
            ,old_version.statement_format
            ,old_version.is_email_requested
            ,old_version.creation_date
            ,old_version.website
            ,old_version.termination_notice_period
            ,old_version.cmc_relationship_manager
            ,old_version.terminated
            ,old_version.termination_date
            ,old_version.mrkt_cntrpart_plat_url_sub_dom
            ,old_version.is_allowed_funding
            ,old_version.last_remediation_date
            ,old_version.rest_of_world_onboarding_url
            ,old_version.is_sftp_requested
            ,old_version.is_pay_spread_to_partner
            ,old_version.is_fofa_enabled
            ,old_version.additional_extract_time_format
            ,old_version.is_internal
            ,old_version.cmc_sales_introducer_reference
        INTO ltab_old_partners
      FROM ( SELECT p_partner_id partner_id
                    ,p_version  version
                    ,p_partner_name partner_name
                    ,l_source_platform  source_platform
                    ,p_source_partner_id source_partner_id
                    ,p_agreement_type agreement_type
                    ,p_agreement_conditions agreement_conditions
                    ,p_is_white_labelled is_white_labelled
                    ,l_profit_centre_name profit_centre_name
                    ,p_company_id company_id
                    ,p_email_type email_type
                    ,p_external_reference external_reference
                    ,p_service_offering service_offering
                    ,p_live_help_enabled live_help_enabled
                    ,p_active active
                    ,p_is_deleted is_deleted
                    ,p_ca_schm_cd ca_schm_cd
                    ,p_market_data_taxable is_market_data_taxable
                    ,p_cash_account_currency cash_account_ccy
                    ,p_allow_credit allow_credit
                    ,p_allow_acc_open_from_platform allow_acc_open_from_platform
                    ,UPPER(p_partner_function) partner_function
                    ,p_relationship_country Relationship_Country
                    ,p_mrkt_cntrpart_customer_id Mrkt_Cntrpart_Customer_Id
                    ,p_mrkt_cntrpart_short_code Mrkt_Cntrpart_Short_Code
                    ,p_mrkt_cntrpart_live_tenant_tm Mrkt_Cntrpart_Live_Tenant_Tmpl
                    ,p_mrkt_cntrpart_demo_tenant_tm Mrkt_Cntrpart_Demo_Tenant_Tmpl
                    ,p_mrkt_cntrpart_cash_account_s Mrkt_Cntrpart_Cash_Account_Seg
                    ,p_statement_format statement_format
                    ,p_is_email_requested is_email_requested
                    ,p_creation_date creation_date
                    ,p_website  website
                    ,p_termination_notice_period termination_notice_period
                    ,p_cmc_relationship_manager cmc_relationship_manager
                    ,p_terminated terminated
                    ,p_termination_date termination_date
                    ,p_mrkt_cntrpart_plat_url_s_dom mrkt_cntrpart_plat_url_sub_dom
                    ,p_is_allowed_funding is_allowed_funding
                    ,p_last_remediation_date last_remediation_date
                    ,p_rest_of_world_onboarding_url rest_of_world_onboarding_url
                    ,p_is_sftp_requested is_sftp_requested
                    ,p_is_pay_spread_to_partner is_pay_spread_to_partner
                    ,p_is_fofa_enabled is_fofa_enabled
                    ,p_additional_extract_time_format additional_extract_time_format
                    ,p_is_internal is_internal
                    ,p_cmc_sales_introducer_reference cmc_sales_introducer_reference
             FROM dual ) new_version,
           partners old_version
      WHERE new_version.partner_id=old_version.partner_id
        AND (nrg_common.has_value_changed(new_version.version, old_version.version) = 1 OR
             nrg_common.has_value_changed(new_version.partner_name, old_version.partner_name) = 1 OR
             nrg_common.has_value_changed(new_version.source_platform, old_version.source_platform) = 1 OR
             nrg_common.has_value_changed(new_version.source_partner_id, old_version.source_partner_id) = 1 OR
             nrg_common.has_value_changed(new_version.agreement_type, old_version.agreement_type) = 1 OR
             nrg_common.has_value_changed(new_version.agreement_conditions, old_version.agreement_conditions) = 1 OR
             nrg_common.has_value_changed(new_version.is_white_labelled, old_version.is_white_labelled) = 1 OR
             nrg_common.has_value_changed(new_version.profit_centre_name, old_version.profit_centre_name) = 1 OR
             nrg_common.has_value_changed(new_version.company_id, old_version.company_id) = 1 OR
             nrg_common.has_value_changed(new_version.email_type, old_version.email_type) = 1 OR
             nrg_common.has_value_changed(new_version.external_reference, old_version.external_reference) = 1 OR
             nrg_common.has_value_changed(new_version.service_offering, old_version.service_offering) = 1 OR
             nrg_common.has_value_changed(new_version.live_help_enabled, old_version.live_help_enabled) = 1 OR
             nrg_common.has_value_changed(new_version.active, old_version.active) = 1 OR
             nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(new_version.ca_schm_cd, old_version.ca_schm_cd) = 1 OR
             nrg_common.has_value_changed(new_version.is_market_data_taxable, old_version.is_market_data_taxable) = 1 OR
             nrg_common.has_value_changed(new_version.cash_account_ccy, old_version.cash_account_ccy) = 1 OR
             nrg_common.has_value_changed(new_version.is_market_data_taxable, old_version.is_market_data_taxable) = 1 OR
             nrg_common.has_value_changed(new_version.allow_credit, old_version.allow_credit) = 1 OR
             nrg_common.has_value_changed(UPPER(new_version.partner_function), old_version.partner_function) = 1 OR
             nrg_common.has_value_changed(new_version.Relationship_Country, old_version.Relationship_Country) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Customer_Id, old_version.Mrkt_Cntrpart_Customer_Id) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Short_Code, old_version.Mrkt_Cntrpart_Short_Code) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Live_Tenant_Tmpl, old_version.Mrkt_Cntrpart_Live_Tenant_Tmpl) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl, old_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Cash_Account_Seg, old_version.Mrkt_Cntrpart_Cash_Account_Seg) = 1 OR
             nrg_common.has_value_changed(new_version.statement_format, old_version.statement_format) = 1 OR
             nrg_common.has_value_changed(new_version.is_email_requested, old_version.is_email_requested) = 1 OR
             nrg_common.has_value_changed(new_version.creation_date, old_version.creation_date) = 1 OR
             nrg_common.has_value_changed(new_version.website, old_version.website) = 1 OR
             nrg_common.has_value_changed(new_version.termination_notice_period, old_version.termination_notice_period) = 1 OR
             nrg_common.has_value_changed(new_version.cmc_relationship_manager, old_version.cmc_relationship_manager) = 1 OR
             nrg_common.has_value_changed(new_version.terminated, old_version.terminated) = 1 OR
             nrg_common.has_value_changed(new_version.termination_date, old_version.termination_date) = 1 OR
             nrg_common.has_value_changed(new_version.mrkt_cntrpart_plat_url_sub_dom, old_version.mrkt_cntrpart_plat_url_sub_dom) = 1 OR
             nrg_common.has_value_changed(new_version.is_allowed_funding, old_version.is_allowed_funding) = 1 OR
             nrg_common.has_value_changed(new_version.last_remediation_date, old_version.last_remediation_date) = 1 OR
             nrg_common.has_value_changed(new_version.rest_of_world_onboarding_url, old_version.rest_of_world_onboarding_url) = 1 OR
             nrg_common.has_value_changed(new_version.is_sftp_requested, old_version.is_sftp_requested) = 1 OR
             nrg_common.has_value_changed(new_version.is_pay_spread_to_partner, old_version.is_pay_spread_to_partner) = 1 OR
             nrg_common.has_value_changed(new_version.is_fofa_enabled, old_version.is_fofa_enabled) = 1 OR
             nrg_common.has_value_changed(new_version.additional_extract_time_format, old_version.additional_extract_time_format) = 1 OR
             nrg_common.has_value_changed(new_version.is_internal, old_version.is_internal) = 1 OR
             nrg_common.has_value_changed(new_version.cmc_sales_introducer_reference, old_version.cmc_sales_introducer_reference) = 1
             )
       FOR UPDATE OF old_version.partner_name;


        put_history(p_tab_old_partners             => ltab_old_partners,
                    p_effective_end_timestamp      => p_effective_start_timestamp,
                    p_action                       => 'U');

       EXCEPTION
         WHEN NO_DATA_FOUND  THEN
            NULL;
      END;

    --
    -- Insert/Update the new data
    --
     MERGE  INTO  partners old_version
      USING ( SELECT p_partner_id partner_id
                    ,p_version  version
                    ,p_partner_name partner_name
                    ,l_source_platform  source_platform
                    ,p_source_partner_id source_partner_id
                    ,p_agreement_type agreement_type
                    ,p_agreement_conditions agreement_conditions
                    ,p_is_white_labelled is_white_labelled
                    ,l_profit_centre_name profit_centre_name
                    ,p_company_id company_id
                    ,p_email_type email_type
                    ,p_external_reference external_reference
                    ,p_service_offering service_offering
                    ,p_live_help_enabled live_help_enabled
                    ,p_active active
                    ,p_is_deleted is_deleted
                    ,p_ca_schm_cd ca_schm_cd
                    ,p_market_data_taxable is_market_data_taxable
                    ,p_cash_account_currency cash_account_ccy
                    ,p_allow_credit allow_credit
                    ,p_allow_acc_open_from_platform allow_acc_open_from_platform
                    ,UPPER(p_partner_function) partner_function
                    ,p_relationship_country Relationship_Country
                    ,p_mrkt_cntrpart_customer_id Mrkt_Cntrpart_Customer_Id
                    ,p_mrkt_cntrpart_short_code Mrkt_Cntrpart_Short_Code
                    ,p_mrkt_cntrpart_live_tenant_tm Mrkt_Cntrpart_Live_Tenant_Tmpl
                    ,p_mrkt_cntrpart_demo_tenant_tm Mrkt_Cntrpart_Demo_Tenant_Tmpl
                    ,p_mrkt_cntrpart_cash_account_s Mrkt_Cntrpart_Cash_Account_Seg
                    ,p_statement_format statement_format
                    ,p_is_email_requested is_email_requested
                    ,p_creation_date creation_date
                    ,p_website  website
                    ,p_termination_notice_period termination_notice_period
                    ,p_cmc_relationship_manager cmc_relationship_manager
                    ,p_terminated terminated
                    ,p_termination_date termination_date
                    ,p_mrkt_cntrpart_plat_url_s_dom mrkt_cntrpart_plat_url_sub_dom
                    ,p_is_allowed_funding is_allowed_funding
                    ,p_last_remediation_date last_remediation_date
                    ,p_rest_of_world_onboarding_url rest_of_world_onboarding_url
                    ,p_is_sftp_requested is_sftp_requested
                    ,p_is_pay_spread_to_partner is_pay_spread_to_partner
                    ,p_is_fofa_enabled is_fofa_enabled
                    ,p_additional_extract_time_format additional_extract_time_format
                    ,p_is_internal is_internal
                    ,p_cmc_sales_introducer_reference cmc_sales_introducer_reference
          FROM dual ) new_version
      ON (old_version.partner_id = new_version.partner_id)
      WHEN MATCHED THEN
      UPDATE
      SET   logical_load_timestamp = lv_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp = p_effective_start_timestamp
           ,version = new_version.version
           ,partner_name = new_version.partner_name
           ,source_platform = new_version.source_platform
           ,source_partner_id = new_version.source_partner_id
           ,agreement_type = new_version.agreement_type
           ,agreement_conditions = new_version.agreement_conditions
           ,is_white_labelled = new_version.is_white_labelled
           ,profit_centre_name = new_version.profit_centre_name
           ,company_id = new_version.company_id
           ,email_type = new_version.email_type
           ,external_reference = new_version.external_reference
           ,service_offering = new_version.Service_Offering
           ,live_help_enabled = new_version.Live_Help_Enabled
           ,active = new_version.active
           ,is_deleted = new_version.Is_Deleted
           ,ca_schm_cd = new_version.Ca_Schm_Cd
           ,is_market_data_taxable = new_version.is_market_data_taxable
           ,cash_account_ccy = new_version.cash_account_ccy
           ,allow_credit = new_version.allow_credit
           ,allow_acc_open_from_platform = new_version.allow_acc_open_from_platform
           ,partner_function = UPPER(new_version.partner_function)
           ,Relationship_Country  = new_version.Relationship_Country
           ,Mrkt_Cntrpart_Customer_Id = new_version.Mrkt_Cntrpart_Customer_Id
           ,Mrkt_Cntrpart_Short_Code = new_version.Mrkt_Cntrpart_Short_Code
           ,Mrkt_Cntrpart_Live_Tenant_Tmpl = new_version.Mrkt_Cntrpart_Live_Tenant_Tmpl
           ,Mrkt_Cntrpart_Demo_Tenant_Tmpl = new_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl
           ,Mrkt_Cntrpart_Cash_Account_Seg = new_version.Mrkt_Cntrpart_Cash_Account_Seg
           ,statement_format = new_version.statement_format
           ,is_email_requested = new_version.is_email_requested
           ,creation_date = new_version.creation_date
           ,website = new_version.website
           ,termination_notice_period = new_version.termination_notice_period
           ,cmc_relationship_manager = new_version.cmc_relationship_manager
           ,terminated = new_version.terminated
           ,termination_date = new_version.termination_date
           ,mrkt_cntrpart_plat_url_sub_dom = new_version.mrkt_cntrpart_plat_url_sub_dom
           ,is_allowed_funding = new_version.is_allowed_funding
           ,last_remediation_date = new_version.last_remediation_date
           ,rest_of_world_onboarding_url = new_version.rest_of_world_onboarding_url
           ,is_sftp_requested = new_version.is_sftp_requested
           ,is_pay_spread_to_partner = new_version.is_pay_spread_to_partner
           ,is_fofa_enabled = new_version.is_fofa_enabled
           ,additional_extract_time_format = new_version.additional_extract_time_format
           ,is_internal = new_version.is_internal
           ,cmc_sales_introducer_reference = new_version.cmc_sales_introducer_reference
       WHERE new_version.partner_id=old_version.partner_id
        AND (nrg_common.has_value_changed(new_version.version, old_version.version) = 1 OR
             nrg_common.has_value_changed(new_version.partner_name, old_version.partner_name) = 1 OR
             nrg_common.has_value_changed(new_version.source_platform, old_version.source_platform) = 1 OR
             nrg_common.has_value_changed(new_version.source_partner_id, old_version.source_partner_id) = 1 OR
             nrg_common.has_value_changed(new_version.agreement_type, old_version.agreement_type) = 1 OR
             nrg_common.has_value_changed(new_version.agreement_conditions, old_version.agreement_conditions) = 1 OR
             nrg_common.has_value_changed(new_version.is_white_labelled, old_version.is_white_labelled) = 1 OR
             nrg_common.has_value_changed(new_version.profit_centre_name, old_version.profit_centre_name) = 1 OR
             nrg_common.has_value_changed(new_version.company_id, old_version.company_id) = 1 OR
             nrg_common.has_value_changed(new_version.email_type, old_version.email_type) = 1 OR
             nrg_common.has_value_changed(new_version.external_reference, old_version.external_reference) = 1 OR
             nrg_common.has_value_changed(new_version.service_offering, old_version.service_offering) = 1 OR
             nrg_common.has_value_changed(new_version.live_help_enabled, old_version.live_help_enabled) = 1 OR
             nrg_common.has_value_changed(new_version.active, old_version.active) = 1  OR
             nrg_common.has_value_changed(new_version.is_deleted, old_version.is_deleted) = 1 OR
             nrg_common.has_value_changed(new_version.is_market_data_taxable, old_version.is_market_data_taxable) = 1 OR
             nrg_common.has_value_changed(new_version.cash_account_ccy, old_version.cash_account_ccy) = 1 OR
             nrg_common.has_value_changed(new_version.allow_credit, old_version.allow_credit) = 1 OR
             nrg_common.has_value_changed(new_version.allow_acc_open_from_platform, old_version.allow_acc_open_from_platform) = 1 OR
             nrg_common.has_value_changed(UPPER(new_version.partner_function)  , old_version.partner_function) = 1 OR
             nrg_common.has_value_changed(new_version.Relationship_Country, old_version.Relationship_Country) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Customer_Id, old_version.Mrkt_Cntrpart_Customer_Id) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Short_Code, old_version.Mrkt_Cntrpart_Short_Code) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Live_Tenant_Tmpl, old_version.Mrkt_Cntrpart_Live_Tenant_Tmpl) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl, old_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl) = 1 OR
             nrg_common.has_value_changed(new_version.Mrkt_Cntrpart_Cash_Account_Seg, old_version.Mrkt_Cntrpart_Cash_Account_Seg) = 1 OR
             nrg_common.has_value_changed(new_version.statement_format, old_version.statement_format) = 1 OR
             nrg_common.has_value_changed(new_version.is_email_requested, old_version.is_email_requested) = 1 OR
             nrg_common.has_value_changed(new_version.creation_date, old_version.creation_date) = 1 OR
             nrg_common.has_value_changed(new_version.website, old_version.website) = 1 OR
             nrg_common.has_value_changed(new_version.termination_notice_period, old_version.termination_notice_period) = 1 OR
             nrg_common.has_value_changed(new_version.cmc_relationship_manager, old_version.cmc_relationship_manager) = 1 OR
             nrg_common.has_value_changed(new_version.terminated, old_version.terminated) = 1 OR
             nrg_common.has_value_changed(new_version.termination_date, old_version.termination_date) = 1 OR
             nrg_common.has_value_changed(new_version.mrkt_cntrpart_plat_url_sub_dom, old_version.mrkt_cntrpart_plat_url_sub_dom) = 1 OR
             nrg_common.has_value_changed(new_version.is_allowed_funding, old_version.is_allowed_funding) = 1 OR
             nrg_common.has_value_changed(new_version.last_remediation_date, old_version.last_remediation_date) = 1 OR
             nrg_common.has_value_changed(new_version.rest_of_world_onboarding_url, old_version.rest_of_world_onboarding_url) = 1 OR
             nrg_common.has_value_changed(new_version.is_sftp_requested, old_version.is_sftp_requested) = 1 OR
             nrg_common.has_value_changed(new_version.is_pay_spread_to_partner, old_version.is_pay_spread_to_partner) = 1 OR
             nrg_common.has_value_changed(new_version.is_fofa_enabled, old_version.is_fofa_enabled) = 1 OR
             nrg_common.has_value_changed(new_version.additional_extract_time_format, old_version.additional_extract_time_format) = 1 OR
             nrg_common.has_value_changed(new_version.is_internal, old_version.is_internal) = 1 OR
             nrg_common.has_value_changed(new_version.cmc_sales_introducer_reference, old_version.cmc_sales_introducer_reference) = 1
            )
      WHEN NOT MATCHED THEN
       INSERT   (partner_id
                ,logical_load_timestamp
                ,created_by
                ,create_timestamp
                ,updated_by
                ,update_timestamp
                ,effective_start_timestamp
                ,version
                ,partner_name
                ,source_platform
                ,source_partner_id
                ,agreement_type
                ,agreement_conditions
                ,is_white_labelled
                ,profit_centre_name
                ,company_id
                ,email_type
                ,external_reference
                ,service_offering
                ,live_help_enabled
                ,active
                ,is_deleted
                ,ca_schm_cd
                ,is_market_data_taxable
                ,cash_account_ccy
                ,allow_credit
                ,allow_acc_open_from_platform
                ,partner_function
                ,Relationship_Country
                ,Mrkt_Cntrpart_Customer_Id
                ,Mrkt_Cntrpart_Short_Code
                ,Mrkt_Cntrpart_Live_Tenant_Tmpl
                ,Mrkt_Cntrpart_Demo_Tenant_Tmpl
                ,Mrkt_Cntrpart_Cash_Account_Seg
                ,statement_format
                ,is_email_requested
                ,creation_date
                ,website
                ,termination_notice_period
                ,cmc_relationship_manager
                ,terminated
                ,termination_date
                ,mrkt_cntrpart_plat_url_sub_dom
                ,is_allowed_funding
                ,last_remediation_date
                ,rest_of_world_onboarding_url
                ,is_sftp_requested
                ,is_pay_spread_to_partner
                ,is_fofa_enabled
                ,additional_extract_time_format
                ,is_internal
                ,cmc_sales_introducer_reference
                )
      VALUES (new_version.partner_id
             ,lv_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.version
             ,new_version.partner_name
             ,new_version.source_platform
             ,new_version.source_partner_id
             ,new_version.agreement_type
             ,new_version.agreement_conditions
             ,new_version.is_white_labelled
             ,new_version.profit_centre_name
             ,new_version.company_id
             ,new_version.email_type
             ,new_version.external_reference
             ,new_version.service_offering
             ,new_version.Live_Help_Enabled
             ,new_version.Active
             ,new_version.Is_Deleted
             ,new_version.Ca_Schm_Cd
             ,new_version.Is_Market_Data_Taxable
             ,new_version.Cash_Account_Ccy
             ,new_version.Allow_Credit
             ,new_version.Allow_Acc_Open_From_Platform
             ,UPPER(new_version.Partner_Function)
             ,new_version.Relationship_Country
             ,new_version.Mrkt_Cntrpart_Customer_Id
             ,new_version.Mrkt_Cntrpart_Short_Code
             ,new_version.Mrkt_Cntrpart_Live_Tenant_Tmpl
             ,new_version.Mrkt_Cntrpart_Demo_Tenant_Tmpl
             ,new_version.Mrkt_Cntrpart_Cash_Account_Seg
             ,new_version.statement_format
             ,new_version.is_email_requested
             ,new_version.creation_date
             ,new_version.website
             ,new_version.termination_notice_period
             ,new_version.cmc_relationship_manager
             ,new_version.terminated
             ,new_version.termination_date
             ,new_version.mrkt_cntrpart_plat_url_sub_dom
             ,new_version.is_allowed_funding
             ,new_version.last_remediation_date
             ,new_version.rest_of_world_onboarding_url
             ,new_version.is_sftp_requested
             ,new_version.is_pay_spread_to_partner
             ,new_version.is_fofa_enabled
             ,new_version.additional_extract_time_format
             ,new_version.is_internal
             ,new_version.cmc_sales_introducer_reference
         );
     --------------------
     --- put_onboarding_countries
     --------------------

          put_onboarding_countries(p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_partner_id                 => p_partner_id,
                                   p_onboarding_countries_tab   => p_onboarding_countries_tab);

     --------------------
     --- put_revenue_stream_config
     --------------------
      IF p_revenue_stream_confg_tab IS NOT NULL THEN
          put_revenue_stream_config(p_user                       => p_user,
                                    p_logical_load_timestamp     => lv_logical_load_timestamp,
                                    p_effective_start_timestamp  => p_effective_start_timestamp,
                                    p_partner_id                 => p_partner_id,
                                    p_revenue_stream_confg_tab   => p_revenue_stream_confg_tab);
      END IF;

     --------------------
     --- put_onboarding_currencies
     --------------------

         put_onboarding_currencies(p_user                          => p_user,
                                   p_logical_load_timestamp        => lv_logical_load_timestamp,
                                   p_effective_start_timestamp     => p_effective_start_timestamp,
                                   p_partner_id                    => p_partner_id,
                                   p_partner_onboarding_crrn_tab   => p_onboarding_currencies_tab);
     --------------------
     --- put_onboarding_languages
     --------------------

          put_onboarding_languages(p_user                          => p_user,
                                   p_logical_load_timestamp        => lv_logical_load_timestamp,
                                   p_effective_start_timestamp     => p_effective_start_timestamp,
                                   p_partner_id                    => p_partner_id,
                                   p_onboarding_languages_tab      => p_onboarding_languages_tab);

     --------------------
     --- put_restricted_plat_feat
     --------------------

          put_restricted_plat_feat(p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_partner_id                 => p_partner_id,
                                   p_restricted_plat_feat_tab   => p_restricted_plat_feat_tab);



        --------------------
     --- put_cmc_sales_persons
     --------------------

          put_cmc_sales_persons     (p_user                       => p_user,
                                   p_logical_load_timestamp     => lv_logical_load_timestamp,
                                   p_effective_start_timestamp  => p_effective_start_timestamp,
                                   p_partner_id                 => p_partner_id,
                                   p_cmc_sales_persons_tab   => p_cmc_sales_persons_tab);


    --
    --Tax Declarations
    --

    put_tax_declaration(p_user                        => p_user,
                        p_logical_load_timestamp      => lv_logical_load_timestamp,
                        p_effective_start_timestamp   => p_effective_start_timestamp,
                        p_partner_id                  => p_partner_id,
                        p_old_partner_version         => ltab_old_partners.version,
                        p_new_partner_version         => p_version,
                        p_tax_declarations            => p_tax_declarations);

    -- Put Key Inf Document --DR
    put_key_inf_documents(p_user                        => p_user,
                          p_logical_load_timestamp      => lv_logical_load_timestamp,
                          p_effective_start_timestamp   => p_effective_start_timestamp,
                          p_partner_id                  => p_partner_id,
                          p_key_inf_documents_tab       => p_key_inf_documents_tab);


    put_partner_relation (p_user                        => p_user,
                          p_logical_load_timestamp      => lv_logical_load_timestamp,
                          p_effective_start_timestamp   => p_effective_start_timestamp,
                          p_partner_id                  => p_partner_id,
                          p_partner_relation_tab        => p_partner_relations);

    put_partner_onboarding_pfm (p_user                        => p_user,
                                p_logical_load_timestamp      => lv_logical_load_timestamp,
                                p_effective_start_timestamp   => p_effective_start_timestamp,
                                p_partner_id                  => p_partner_id,
                                p_partner_onboarding_pfm_tab  => p_partner_onboarding_pltfrms);

    put_partner_mc_segregations (p_user                        => p_user,
                                 p_logical_load_timestamp      => lv_logical_load_timestamp,
                                 p_effective_start_timestamp   => p_effective_start_timestamp,
                                 p_partner_id                  => p_partner_id,
                                 p_customer_id                 => p_mrkt_cntrpart_customer_id,
                                 p_partner_mc_segregations     => p_partner_mc_segregations);

    logger.logger.set_module(NULL);


  EXCEPTION
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);

  END put_partner;
  -- ===================================================================================
  -- get_partners_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of partner_ids
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_source_platform                platform - Trading Platform MMCFD/MMSB or NG
  -- -----------------------------------------------------------------------------------
  FUNCTION get_partners_ids (p_source_platform IN partners.source_platform%TYPE) RETURN SYS_REFCURSOR

  IS
      lcuv_result SYS_REFCURSOR;
  BEGIN
      logger.logger.set_module('partners_ids');

    OPEN lcuv_result
      FOR
          SELECT partner_id
          FROM   partners
          WHERE  source_platform = NVL(p_source_platform,source_platform);

      logger.logger.set_module(NULL);

      RETURN lcuv_result;

  EXCEPTION
      WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
  END get_partners_ids;

  -- ===================================================================================
  -- get_stubbed_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed ids to return (Optional - not set returns all)
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_ids(p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR

    IS
        lcuv_result           SYS_REFCURSOR;

        lex_unknown_platform  EXCEPTION;
    BEGIN
        logger.logger.set_module('get_stubbed_ids');

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT partner_id
               FROM   partners
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT partner_id
               FROM   partners
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;

        logger.logger.set_module(NULL);

        RETURN lcuv_result;

    EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_stubbed_ids;

  -- ===================================================================================
  -- get_stubbed_partner_desk_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed ids to return (Optional - not set returns all)
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_partner_desk_ids(p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR

    IS
        lcuv_result           SYS_REFCURSOR;

        lex_unknown_platform  EXCEPTION;
    BEGIN
        logger.logger.set_module('get_stubbed_partner_desk_ids');

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT partner_desk_id
               FROM   partner_desks
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT partner_desk_id
               FROM   partner_desks
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;

        logger.logger.set_module(NULL);

        RETURN lcuv_result;

    EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_stubbed_partner_desk_ids;

  -- ===================================================================================
  -- get_stubbed_partner_agent_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to return a set of stubbed id attributes
  --
  -- Notes:
  -- ------
  --
  --     None
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_batch_limit                    Maximum number of stubbed ids to return (Optional - not set returns all)
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20004    Default Exception
  -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_partner_agent_ids(p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR

    IS
        lcuv_result           SYS_REFCURSOR;

        lex_unknown_platform  EXCEPTION;
    BEGIN
        logger.logger.set_module('get_stubbed_partner_agent_ids');

    CASE
      WHEN p_batch_limit IS NOT NULL THEN
           OPEN lcuv_result
               FOR
               SELECT pa.Partner_Agent_Id
               FROM   partner_agents pa
               WHERE effective_start_timestamp = gc_default_timestamp
           AND rownum <= p_batch_limit;
      ELSE
           OPEN lcuv_result
               FOR
               SELECT pa.Partner_Agent_Id
               FROM   partner_agents pa
               WHERE effective_start_timestamp = gc_default_timestamp;
    END CASE;

        logger.logger.set_module(NULL);

        RETURN lcuv_result;

    EXCEPTION
        WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END get_stubbed_partner_agent_ids;

END nrg_partner;

//