
  CREATE OR REPLACE EDITIONABLE PACKAGE "BI_ODS"."NRG_HISTORY_TABLE_RESYNC" AS
    -- ===================================================================================
    -- NRG_HISTORY_TABLE_RESYNC
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --       This is used to resynchronize the effective_start_timestamp and effective_end_timestamp for
    --       all the history tables. The scheduler job will use this package to set the correct dates.
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     Manoj Kumar  16-Dec-2011        1.0     Initial Version
    --
    -- ===================================================================================

    -- ===================================================================================
    -- resync
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This procedure performs the resynchronization of the effective_start_timestamp,
    --     effective_end_timestamp using the merge statement for a particular history table.
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_table_name                     History table name
    --
    -- Return:
    -- -------
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

  PROCEDURE resync(p_table_name VARCHAR2);

    -- ===================================================================================
    -- start_all_history_resync
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     It will start the resync operation for all the history tables configured
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_run_now                        Specifies whether to run serially "in-session" defaulted to N
    --
    --
    -- Return:
    -- -------
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

  PROCEDURE start_all_history_resync(p_run_now IN VARCHAR2  DEFAULT 'N');

    -- ===================================================================================
    -- stop_all_history_resync
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     It will stop the resync operation for all the scheduled history tables.
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

  PROCEDURE stop_all_history_resync;

    -- ===================================================================================
    -- start_history_resync
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     It will start the resync operation for the table passed as the parameter.
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_table_name                     History table name
    --      p_run_now                        Flag for running through the scheduler job or directly
    --
    -- Return:
    -- -------
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

  PROCEDURE start_history_resync (p_table_name VARCHAR2,p_run_now IN VARCHAR2  DEFAULT 'N');

    -- ===================================================================================
    -- stop_history_resync
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     It will stop the resync operation for the table passed as the parameter.
    --
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_table_name                     History table name
    --
    --  Return:
    -- -------
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE stop_history_resync (p_table_name VARCHAR2);

END;

//