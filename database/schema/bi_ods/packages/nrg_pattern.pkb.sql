
  CREATE OR REPLACE EDITIONABLE PACKAGE BODY "BI_ODS"."NRG_PATTERN" 
AS
-- ===================================================================================
-- NRG_PATTERN
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     This package encapsulates NRG (Near Realtime Gatherer)
--
--     Management of the pattern model
--
-- -----------------------------------------------------------------------------------
--
-- Notes:
-- ------
--
--     Run as BI_ODS
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--     -20001    Entity already exists
--
-- Modifications:
-- --------------
--
--   Date         Modified By         Vers    Action
--   ----------   ---------------     -----   ----------------------------------------
--   11/03/2016   Sakina Kinkhabwala  1.0     Creation
--	 21/04/2016   Sakina Kinkhabwala  2.0	  Patch ODS 60.3 - Updated to handle unique key error
--	 18/06/2019   Patrick Dinwiddy    2.1	  JCS-10803
--	 24/04/2020   Patrick Dinwiddy    2.2	  JCS-12719
--	 16/06/2020   Patrick Dinwiddy    2.3	  JCS-13230
-- ===================================================================================
--
--
-- ===================================================================================
-- PACKAGE CONSTANTS
-- ===================================================================================

gc_version            CONSTANT VARCHAR2(3) := '2.3';
gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
gc_true               CONSTANT PLS_INTEGER := 1;
gc_false              CONSTANT PLS_INTEGER := 0;
gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

--
--
-- ===================================================================================
-- PRIVATE MODULES
-- ===================================================================================

-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for PATTERN_ALERT_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_pattern_alert          This is the old version of the pattern_alert data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_history (p_tab_old_alert_preferences    IN alert_preferences%ROWTYPE,
                       p_effective_end_timestamp      IN alert_preferences.effective_start_timestamp%TYPE,
                       p_action                       IN alert_preferences_h.action%TYPE) IS

BEGIN

  INSERT INTO alert_preferences_h
             (person_id,
              trading_account_id,
              alert_type,
              alert_notification_type,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              effective_end_timestamp,
              action,
              action_timestamp)
      VALUES (p_tab_old_alert_preferences.person_id,
              p_tab_old_alert_preferences.trading_account_id,
              p_tab_old_alert_preferences.alert_type,
              p_tab_old_alert_preferences.alert_notification_type,
              p_tab_old_alert_preferences.logical_load_timestamp,
              p_tab_old_alert_preferences.created_by,
              p_tab_old_alert_preferences.create_timestamp,
              p_tab_old_alert_preferences.updated_by,
              p_tab_old_alert_preferences.update_timestamp,
              p_tab_old_alert_preferences.effective_start_timestamp,
              p_effective_end_timestamp,
              p_action,
              SYSTIMESTAMP);
    EXCEPTION WHEN DUP_VAL_ON_INDEX THEN
              NULL;

END put_history;

-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for PATTERN_ALERT_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_pattern_alert          This is the old version of the pattern_alert data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_history (p_tab_old_alert_suspensions    IN alert_suspensions%ROWTYPE,
                       p_effective_end_timestamp      IN alert_suspensions.effective_start_timestamp%TYPE,
                       p_action                       IN alert_suspensions_h.action%TYPE) IS

BEGIN

  INSERT INTO alert_suspensions_h
             (person_id,
              trading_account_id,
              logical_load_timestamp,
              created_by,
              create_timestamp,
              updated_by,
              update_timestamp,
              effective_start_timestamp,
              effective_end_timestamp,
              action,
              action_timestamp,
              is_alerts_suspended,
              suspend_alerts_from,
              suspend_alerts_to)
      VALUES (p_tab_old_alert_suspensions.person_id,
              p_tab_old_alert_suspensions.trading_account_id,
              p_tab_old_alert_suspensions.logical_load_timestamp,
              p_tab_old_alert_suspensions.created_by,
              p_tab_old_alert_suspensions.create_timestamp,
              p_tab_old_alert_suspensions.updated_by,
              p_tab_old_alert_suspensions.update_timestamp,
              p_tab_old_alert_suspensions.effective_start_timestamp,
              p_effective_end_timestamp,
              p_action,
              SYSTIMESTAMP,
              p_tab_old_alert_suspensions.is_alerts_suspended,
              p_tab_old_alert_suspensions.suspend_alerts_from,
              p_tab_old_alert_suspensions.suspend_alerts_to);
    EXCEPTION WHEN DUP_VAL_ON_INDEX THEN
              NULL;

END put_history;

-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for PATTERN_ALERT_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_pattern_alert          This is the old version of the pattern_alert data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_history  (p_tab_old_pattern_alert       IN pattern_alerts%ROWTYPE,
                       p_effective_end_timestamp      IN pattern_alerts.effective_start_timestamp%TYPE,
                       p_action                       IN pattern_alerts_h.action%TYPE)
IS
BEGIN
  INSERT INTO pattern_alerts_h (
               pattern_alert_id
              ,logical_load_timestamp
              ,created_by
              ,create_timestamp
              ,updated_by
              ,update_timestamp
              ,effective_start_timestamp
              ,effective_end_timestamp
              ,business_date
              ,reporting_date
              ,action
              ,action_timestamp
              ,type
              ,creation_time
              ,last_modified_time
              ,creator
              ,last_modifier
              ,status
              ,product_instrument_code
              ,product_asset_class
              ,pattern_type
              ,interval
              ,pattern_status
              ,strength_rating
              ,start_time
              ,length
              ,trend_direction
              ,breakout_time
              ,price_at_breakout
              ,price_projection_low
              ,price_projection_high
              ,pattern_success
              ,price_projection_hit_time
              ,pattern_data_xml)
        VALUES(p_tab_old_pattern_alert.pattern_alert_id,
               p_tab_old_pattern_alert.logical_load_timestamp,
               p_tab_old_pattern_alert.created_by,
               p_tab_old_pattern_alert.create_timestamp,
               p_tab_old_pattern_alert.updated_by,
               p_tab_old_pattern_alert.update_timestamp,
               p_tab_old_pattern_alert.effective_start_timestamp,
               p_effective_end_timestamp,
               p_tab_old_pattern_alert.business_date,
               p_tab_old_pattern_alert.reporting_date,
               p_action,
               SYSTIMESTAMP,
               p_tab_old_pattern_alert.type,
               p_tab_old_pattern_alert.creation_time,
               p_tab_old_pattern_alert.last_modified_time,
               p_tab_old_pattern_alert.creator,
               p_tab_old_pattern_alert.last_modifier,
               p_tab_old_pattern_alert.status,
               p_tab_old_pattern_alert.product_instrument_code,
               p_tab_old_pattern_alert.product_asset_class,
               p_tab_old_pattern_alert.pattern_type,
               p_tab_old_pattern_alert.interval,
               p_tab_old_pattern_alert.pattern_status,
               p_tab_old_pattern_alert.strength_rating,
               p_tab_old_pattern_alert.start_time,
               p_tab_old_pattern_alert.length,
               p_tab_old_pattern_alert.trend_direction,
               p_tab_old_pattern_alert.breakout_time,
               p_tab_old_pattern_alert.price_at_breakout,
               p_tab_old_pattern_alert.price_projection_low,
               p_tab_old_pattern_alert.price_projection_high,
               p_tab_old_pattern_alert.pattern_success,
               p_tab_old_pattern_alert.price_projection_hit_time,
               p_tab_old_pattern_alert.pattern_data_xml
              );
  EXCEPTION
  WHEN DUP_VAL_ON_INDEX THEN
    UPDATE pattern_alerts_h
       SET updated_by                    	= p_tab_old_pattern_alert.updated_by,
           update_timestamp              	= p_tab_old_pattern_alert.update_timestamp,
           logical_load_timestamp        	= p_tab_old_pattern_alert.logical_load_timestamp,
           effective_end_timestamp       	= p_effective_end_timestamp,
           business_date                  = p_tab_old_pattern_alert.business_date,
           reporting_date                 = p_tab_old_pattern_alert.reporting_date,
           action                        	= p_action,
           action_timestamp              	= SYSTIMESTAMP,
           type                       	  = p_tab_old_pattern_alert.type,
           creation_time                  = p_tab_old_pattern_alert.creation_time,
           last_modified_time             = p_tab_old_pattern_alert.last_modified_time,
           creator            		 	      = p_tab_old_pattern_alert.creator,
           last_modifier                	= p_tab_old_pattern_alert.last_modifier,
           status         			 	        = p_tab_old_pattern_alert.status,
           product_instrument_code        = p_tab_old_pattern_alert.product_instrument_code,
           product_asset_class            = p_tab_old_pattern_alert.product_asset_class,
           pattern_type                   = p_tab_old_pattern_alert.pattern_type,
           interval                    	  = p_tab_old_pattern_alert.interval,
           pattern_status            		  = p_tab_old_pattern_alert.pattern_status,
            strength_rating            		= p_tab_old_pattern_alert.strength_rating,
           start_time             	 	    = p_tab_old_pattern_alert.start_time,
           length             			      = p_tab_old_pattern_alert.length,
           trend_direction                = p_tab_old_pattern_alert.trend_direction,
           breakout_time                  = p_tab_old_pattern_alert.breakout_time,
           price_at_breakout        		  = p_tab_old_pattern_alert.price_at_breakout,
           price_projection_low           = p_tab_old_pattern_alert.price_projection_low,
           price_projection_high          = p_tab_old_pattern_alert.price_projection_high,
           pattern_success  				      = p_tab_old_pattern_alert.pattern_success,
           price_projection_hit_time      = p_tab_old_pattern_alert.price_projection_hit_time,
           pattern_data_xml          		  = p_tab_old_pattern_alert.pattern_data_xml
     WHERE pattern_alert_id               = p_tab_old_pattern_alert.pattern_alert_id
       AND effective_start_timestamp      = p_tab_old_pattern_alert.effective_start_timestamp
       AND (nrg_common.has_value_changed(type, p_tab_old_pattern_alert.type) 											   	                 = 1 OR
            nrg_common.has_value_changed(creation_time, p_tab_old_pattern_alert.creation_time) 			   					       = 1 OR
            nrg_common.has_value_changed(last_modified_time, p_tab_old_pattern_alert.last_modified_time)    					 = 1 OR
            nrg_common.has_value_changed(creator, p_tab_old_pattern_alert.creator) 											               = 1 OR
            nrg_common.has_value_changed(last_modifier, p_tab_old_pattern_alert.last_modifier) 								         = 1 OR
            nrg_common.has_value_changed(status, p_tab_old_pattern_alert.status) 											                 = 1 OR
            nrg_common.has_value_changed(product_instrument_code, p_tab_old_pattern_alert.product_instrument_code)	   = 1 OR
            nrg_common.has_value_changed(product_asset_class, p_tab_old_pattern_alert.product_asset_class)    				 = 1 OR
            nrg_common.has_value_changed(pattern_type, p_tab_old_pattern_alert.pattern_type) 								           = 1 OR
            nrg_common.has_value_changed(interval, p_tab_old_pattern_alert.interval) 										               = 1 OR
            nrg_common.has_value_changed(pattern_status, p_tab_old_pattern_alert.pattern_status) 							         = 1 OR
            nrg_common.has_value_changed(strength_rating, p_tab_old_pattern_alert.strength_rating)    							   = 1 OR
            nrg_common.has_value_changed(start_time, p_tab_old_pattern_alert.start_time) 						   	               = 1 OR
            nrg_common.has_value_changed(length, p_tab_old_pattern_alert.length) 											                 = 1 OR
            nrg_common.has_value_changed(trend_direction, p_tab_old_pattern_alert.trend_direction) 				   			     = 1 OR
            nrg_common.has_value_changed(breakout_time, p_tab_old_pattern_alert.breakout_time) 							   	       = 1 OR
            nrg_common.has_value_changed(price_at_breakout, p_tab_old_pattern_alert.price_at_breakout) 				    		 = 1 OR
            nrg_common.has_value_changed(price_projection_low, p_tab_old_pattern_alert.price_projection_low)	 	   		 = 1 OR
            nrg_common.has_value_changed(price_projection_high, p_tab_old_pattern_alert.price_projection_high)         = 1 OR
            nrg_common.has_value_changed(pattern_success, p_tab_old_pattern_alert.pattern_success) 							       = 1 OR
            nrg_common.has_value_changed(price_projection_hit_time, p_tab_old_pattern_alert.price_projection_hit_time) = 1 OR
            nrg_common.has_value_changed(pattern_data_xml, p_tab_old_pattern_alert.pattern_data_xml) 						       = 1 );

END put_history;

-- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PATTERN_REC_COUNTRIES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_pattern_rec_contry
  --     p_effective_end_timestamp
  --     p_product_instrument_code
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_pattern_rec_contry   IN pattern_rec_countries%ROWTYPE,
                        p_effective_end_timestamp      IN pattern_alerts.effective_start_timestamp%TYPE,
                        p_product_instrument_code      IN pattern_alerts.product_instrument_code%TYPE,
                        p_action                       IN pattern_rec_countries_h.action%TYPE)

  IS
  BEGIN
		INSERT INTO pattern_rec_countries_h (
                instrument_id
               ,country
               ,logical_load_timestamp
               ,created_by
               ,create_timestamp
               ,updated_by
               ,update_timestamp
               ,effective_start_timestamp
               ,effective_end_timestamp
               ,action
               ,action_timestamp
               )
        VALUES (p_tab_old_pattern_rec_contry.instrument_id,
                p_tab_old_pattern_rec_contry.country,
                p_tab_old_pattern_rec_contry.logical_load_timestamp,
                p_tab_old_pattern_rec_contry.created_by,
                p_tab_old_pattern_rec_contry.create_timestamp,
                p_tab_old_pattern_rec_contry.updated_by,
                p_tab_old_pattern_rec_contry.update_timestamp,
                p_tab_old_pattern_rec_contry.effective_start_timestamp,
                p_effective_end_timestamp,
                p_action,
                SYSTIMESTAMP
                );
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE pattern_rec_countries_h
         SET logical_load_timestamp    = p_tab_old_pattern_rec_contry.logical_load_timestamp,
             updated_by                = p_tab_old_pattern_rec_contry.updated_by,
             update_timestamp          = p_tab_old_pattern_rec_contry.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP
       WHERE instrument_id             = p_tab_old_pattern_rec_contry.instrument_id
	       AND country                   = p_tab_old_pattern_rec_contry.country
	       AND effective_start_timestamp = p_tab_old_pattern_rec_contry.effective_start_timestamp;

  END put_history;

-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for PATTERN_SUBSCRIPTIONS_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_pattern_sub  This is the old version of the pattern_subscriptions data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_history(p_tab_old_pattern_sub     IN pattern_subscriptions%ROWTYPE,
                      p_effective_end_timestamp IN pattern_subscriptions.effective_start_timestamp%TYPE,
                      p_action                  IN pattern_subscriptions_h.action%TYPE)
IS
BEGIN

    INSERT INTO pattern_subscriptions_h (pattern_subscription_id
                                        ,logical_load_timestamp
                                        ,created_by
                                        ,create_timestamp
                                        ,updated_by
                                        ,update_timestamp
                                        ,effective_start_timestamp
                                        ,effective_end_timestamp
                                        ,action
                                        ,action_timestamp
                                        ,type
                                        ,creation_time
                                        ,last_modified_time
                                        ,creator
                                        ,last_modifier
                                        ,trading_account_id
                                        ,status
                                        ,expiry_time
                                        ,person_id
                                        ,name
                                        ,country_code
                                        )
                                  VALUES(p_tab_old_pattern_sub.pattern_subscription_id,
                                         p_tab_old_pattern_sub.logical_load_timestamp,
                                         p_tab_old_pattern_sub.created_by,
                                         p_tab_old_pattern_sub.create_timestamp,
                                         p_tab_old_pattern_sub.updated_by,
                                         p_tab_old_pattern_sub.update_timestamp,
                                         p_tab_old_pattern_sub.effective_start_timestamp,
                                         p_effective_end_timestamp,
                                         p_action,
                                         SYSTIMESTAMP,
                                         p_tab_old_pattern_sub.type,
                                         p_tab_old_pattern_sub.creation_time,
                                         p_tab_old_pattern_sub.last_modified_time,
                                         p_tab_old_pattern_sub.creator,
                                         p_tab_old_pattern_sub.last_modifier,
                                         p_tab_old_pattern_sub.trading_account_id,
                                         p_tab_old_pattern_sub.status,
                                         p_tab_old_pattern_sub.expiry_time,
                                         p_tab_old_pattern_sub.person_id,
                                         p_tab_old_pattern_sub.name,
                                         p_tab_old_pattern_sub.country_code
                                        );
EXCEPTION
WHEN DUP_VAL_ON_INDEX THEN
  UPDATE pattern_subscriptions_h
     SET updated_by                    	= p_tab_old_pattern_sub.updated_by,
         update_timestamp              	= p_tab_old_pattern_sub.update_timestamp,
         logical_load_timestamp        	= p_tab_old_pattern_sub.logical_load_timestamp,
         effective_end_timestamp       	= p_effective_end_timestamp,
         action                        	= p_action,
         action_timestamp              	= SYSTIMESTAMP,
         type                       	  = p_tab_old_pattern_sub.type,
         creation_time                  = p_tab_old_pattern_sub.creation_time,
         last_modified_time             = p_tab_old_pattern_sub.last_modified_time,
         creator            		 	      = p_tab_old_pattern_sub.creator,
         last_modifier                	= p_tab_old_pattern_sub.last_modifier,
         trading_account_id         	  = p_tab_old_pattern_sub.trading_account_id,
         status                         = p_tab_old_pattern_sub.status,
         expiry_time                    = p_tab_old_pattern_sub.expiry_time,
         person_id                      = p_tab_old_pattern_sub.person_id,
         name                    	      = p_tab_old_pattern_sub.name,
         country_code            		    = p_tab_old_pattern_sub.country_code
   WHERE pattern_subscription_id        = p_tab_old_pattern_sub.pattern_subscription_id
     AND effective_start_timestamp      = p_tab_old_pattern_sub.effective_start_timestamp
     AND (nrg_common.has_value_changed(type, p_tab_old_pattern_sub.type) 												      = 1 OR
          nrg_common.has_value_changed(creation_time, p_tab_old_pattern_sub.creation_time) 						= 1 OR
          nrg_common.has_value_changed(last_modified_time, p_tab_old_pattern_sub.last_modified_time) 	= 1 OR
          nrg_common.has_value_changed(creator, p_tab_old_pattern_sub.creator) 											  = 1 OR
          nrg_common.has_value_changed(last_modifier, p_tab_old_pattern_sub.last_modifier) 						= 1 OR
          nrg_common.has_value_changed(trading_account_id, p_tab_old_pattern_sub.trading_account_id)  = 1 OR
          nrg_common.has_value_changed(status, p_tab_old_pattern_sub.status) 			                    = 1 OR
          nrg_common.has_value_changed(expiry_time, p_tab_old_pattern_sub.expiry_time) 					      = 1 OR
          nrg_common.has_value_changed(person_id, p_tab_old_pattern_sub.person_id) 								    = 1 OR
          nrg_common.has_value_changed(name, p_tab_old_pattern_sub.name) 										          = 1 OR
          nrg_common.has_value_changed(country_code, p_tab_old_pattern_sub.country_code) 							= 1
          );

END put_history;

-- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PATTERN_S_NOTIFICATIONS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_alert_notifi_typ
  --     p_effective_end_timestamp
  --     p_pattern_alert_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_alert_notifi_typ IN pattern_s_notifications%ROWTYPE,
                        p_effective_end_timestamp  IN pattern_subscriptions.effective_start_timestamp%TYPE,
                        p_pattern_subscription_id  IN pattern_subscriptions.pattern_subscription_id%TYPE,
                        p_action                   IN pattern_s_notifications_h.action%TYPE)

  IS
  BEGIN
		INSERT INTO pattern_s_notifications_h(pattern_subscription_id
                                         ,logical_load_timestamp
                                         ,created_by
                                         ,create_timestamp
                                         ,updated_by
                                         ,update_timestamp
                                         ,effective_start_timestamp
                                         ,effective_end_timestamp
                                         ,action
                                         ,action_timestamp
                                         ,alert_notification_type)
                                  VALUES (p_tab_old_alert_notifi_typ.pattern_subscription_id,
                                          p_tab_old_alert_notifi_typ.logical_load_timestamp,
                                          p_tab_old_alert_notifi_typ.created_by,
                                          p_tab_old_alert_notifi_typ.create_timestamp,
                                          p_tab_old_alert_notifi_typ.updated_by,
                                          p_tab_old_alert_notifi_typ.update_timestamp,
                                          p_tab_old_alert_notifi_typ.effective_start_timestamp,
                                          p_effective_end_timestamp,
                                          p_action,
                                          SYSTIMESTAMP,
                                          p_tab_old_alert_notifi_typ.alert_notification_type);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE pattern_s_notifications_h
      SET    logical_load_timestamp    = p_tab_old_alert_notifi_typ.logical_load_timestamp,
             updated_by                = p_tab_old_alert_notifi_typ.updated_by,
             update_timestamp          = p_tab_old_alert_notifi_typ.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             alert_notification_type   = p_tab_old_alert_notifi_typ.alert_notification_type
       WHERE pattern_subscription_id   = p_tab_old_alert_notifi_typ.pattern_subscription_id
         AND alert_notification_type   = p_tab_old_alert_notifi_typ.alert_notification_type
	       AND effective_start_timestamp = p_tab_old_alert_notifi_typ.effective_start_timestamp;

  END put_history;

 -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PATTERN_S_ASSET_CLASSES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_asset_classes
  --     p_effective_end_timestamp
  --     p_pattern_alert_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_asset_classes    IN pattern_s_asset_classes%ROWTYPE,
                        p_effective_end_timestamp  IN pattern_subscriptions.effective_start_timestamp%TYPE,
                        p_pattern_subscription_id  IN pattern_subscriptions.pattern_subscription_id%TYPE,
                        p_action                   IN pattern_s_asset_classes_h.action%TYPE)

  IS
  BEGIN
		INSERT INTO pattern_s_asset_classes_h(pattern_subscription_id
                                         ,logical_load_timestamp
                                         ,created_by
                                         ,create_timestamp
                                         ,updated_by
                                         ,update_timestamp
                                         ,effective_start_timestamp
                                         ,effective_end_timestamp
                                         ,action
                                         ,action_timestamp
                                         ,asset_class)
                                  VALUES (p_tab_old_asset_classes.pattern_subscription_id,
                                          p_tab_old_asset_classes.logical_load_timestamp,
                                          p_tab_old_asset_classes.created_by,
                                          p_tab_old_asset_classes.create_timestamp,
                                          p_tab_old_asset_classes.updated_by,
                                          p_tab_old_asset_classes.update_timestamp,
                                          p_tab_old_asset_classes.effective_start_timestamp,
                                          p_effective_end_timestamp,
                                          p_action,
                                          SYSTIMESTAMP,
                                          p_tab_old_asset_classes.asset_class);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE pattern_s_asset_classes_h
      SET    logical_load_timestamp    = p_tab_old_asset_classes.logical_load_timestamp,
             updated_by                = p_tab_old_asset_classes.updated_by,
             update_timestamp          = p_tab_old_asset_classes.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             asset_class               = p_tab_old_asset_classes.asset_class
       WHERE pattern_subscription_id   = p_tab_old_asset_classes.pattern_subscription_id
         AND asset_class               = p_tab_old_asset_classes.asset_class
	       AND effective_start_timestamp = p_tab_old_asset_classes.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PATTERN_S_INTERVALS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_intervals
  --     p_effective_end_timestamp
  --     p_pattern_alert_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_intervals         IN pattern_s_intervals%ROWTYPE,
                        p_effective_end_timestamp  IN pattern_subscriptions.effective_start_timestamp%TYPE,
                        p_pattern_subscription_id  IN pattern_subscriptions.pattern_subscription_id%TYPE,
                        p_action                   IN pattern_s_intervals_h.action%TYPE)

  IS
  BEGIN
		INSERT INTO pattern_s_intervals_h(pattern_subscription_id
                                     ,logical_load_timestamp
                                     ,created_by
                                     ,create_timestamp
                                     ,updated_by
                                     ,update_timestamp
                                     ,effective_start_timestamp
                                     ,effective_end_timestamp
                                     ,action
                                     ,action_timestamp
                                     ,interval)
                              VALUES (p_tab_old_intervals.pattern_subscription_id,
                                      p_tab_old_intervals.logical_load_timestamp,
                                      p_tab_old_intervals.created_by,
                                      p_tab_old_intervals.create_timestamp,
                                      p_tab_old_intervals.updated_by,
                                      p_tab_old_intervals.update_timestamp,
                                      p_tab_old_intervals.effective_start_timestamp,
                                      p_effective_end_timestamp,
                                      p_action,
                                      SYSTIMESTAMP,
                                      p_tab_old_intervals.interval);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE pattern_s_intervals_h
      SET    logical_load_timestamp    = p_tab_old_intervals.logical_load_timestamp,
             updated_by                = p_tab_old_intervals.updated_by,
             update_timestamp          = p_tab_old_intervals.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             interval                	 = p_tab_old_intervals.interval
      WHERE  pattern_subscription_id   = p_tab_old_intervals.pattern_subscription_id
        AND  interval    				       = p_tab_old_intervals.interval
	    AND  effective_start_timestamp 	 = p_tab_old_intervals.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PATTERN_S_PATTERN_TYPES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_pattern_types
  --     p_effective_end_timestamp
  --     p_pattern_subscription_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_pattern_types    IN pattern_s_pattern_types%ROWTYPE,
                        p_effective_end_timestamp  IN pattern_subscriptions.effective_start_timestamp%TYPE,
                        p_pattern_subscription_id  IN pattern_subscriptions.pattern_subscription_id%TYPE,
                        p_action                   IN pattern_s_pattern_types_h.action%TYPE)

  IS
  BEGIN
		INSERT INTO pattern_s_pattern_types_h(pattern_subscription_id
                                         ,logical_load_timestamp
                                         ,created_by
                                         ,create_timestamp
                                         ,updated_by
                                         ,update_timestamp
                                         ,effective_start_timestamp
                                         ,effective_end_timestamp
                                         ,action
                                         ,action_timestamp
                                         ,pattern_type)
                                  VALUES (p_tab_old_pattern_types.pattern_subscription_id,
                                          p_tab_old_pattern_types.logical_load_timestamp,
                                          p_tab_old_pattern_types.created_by,
                                          p_tab_old_pattern_types.create_timestamp,
                                          p_tab_old_pattern_types.updated_by,
                                          p_tab_old_pattern_types.update_timestamp,
                                          p_tab_old_pattern_types.effective_start_timestamp,
                                          p_effective_end_timestamp,
                                          p_action,
                                          SYSTIMESTAMP,
                                          p_tab_old_pattern_types.pattern_type);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE pattern_s_pattern_types_h
      SET    logical_load_timestamp    = p_tab_old_pattern_types.logical_load_timestamp,
             updated_by                = p_tab_old_pattern_types.updated_by,
             update_timestamp          = p_tab_old_pattern_types.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             pattern_type              = p_tab_old_pattern_types.pattern_type
      WHERE pattern_subscription_id    = p_tab_old_pattern_types.pattern_subscription_id
        AND pattern_type    			     = p_tab_old_pattern_types.pattern_type
	      AND effective_start_timestamp  = p_tab_old_pattern_types.effective_start_timestamp;

  END put_history;

  -- ===================================================================================
  -- put_history
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to write the history table for PATTERN_S_PATTERN_STATUSES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_tab_old_pattern_statuses
  --     p_effective_end_timestamp
  --     p_pattern_subscription_id
  --     p_action
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history(p_tab_old_pattern_statuses  IN pattern_s_pattern_statuses%ROWTYPE,
                        p_effective_end_timestamp  IN pattern_subscriptions.effective_start_timestamp%TYPE,
                        p_pattern_subscription_id  IN pattern_subscriptions.pattern_subscription_id%TYPE,
                        p_action                   IN pattern_s_pattern_statuses_h.action%TYPE)

  IS
  BEGIN
		INSERT INTO pattern_s_pattern_statuses_h (pattern_subscription_id
                                             ,logical_load_timestamp
                                             ,created_by
                                             ,create_timestamp
                                             ,updated_by
                                             ,update_timestamp
                                             ,effective_start_timestamp
                                             ,effective_end_timestamp
                                             ,action
                                             ,action_timestamp
                                             ,pattern_status)
                                      VALUES (p_tab_old_pattern_statuses.pattern_subscription_id,
                                              p_tab_old_pattern_statuses.logical_load_timestamp,
                                              p_tab_old_pattern_statuses.created_by,
                                              p_tab_old_pattern_statuses.create_timestamp,
                                              p_tab_old_pattern_statuses.updated_by,
                                              p_tab_old_pattern_statuses.update_timestamp,
                                              p_tab_old_pattern_statuses.effective_start_timestamp,
                                              p_effective_end_timestamp,
                                              p_action,
                                              SYSTIMESTAMP,
                                              p_tab_old_pattern_statuses.pattern_status);
  EXCEPTION
    WHEN DUP_VAL_ON_INDEX THEN
      UPDATE pattern_s_pattern_statuses_h
         SET logical_load_timestamp    = p_tab_old_pattern_statuses.logical_load_timestamp,
             updated_by                = p_tab_old_pattern_statuses.updated_by,
             update_timestamp          = p_tab_old_pattern_statuses.update_timestamp,
             effective_end_timestamp   = p_effective_end_timestamp,
             action                    = p_action,
             action_timestamp          = SYSTIMESTAMP,
             pattern_status            = p_tab_old_pattern_statuses.pattern_status
       WHERE pattern_subscription_id   = p_tab_old_pattern_statuses.pattern_subscription_id
         AND pattern_status    	       = p_tab_old_pattern_statuses.pattern_status
  	     AND effective_start_timestamp = p_tab_old_pattern_statuses.effective_start_timestamp;

  END put_history;

 -- ===================================================================================
  -- put_pattern_rec_countries
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put pattern_rec_countries
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PATTERN_REC_COUNTRIES
  --     PATTERN_REC_COUNTRIES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_product_instrument_code        product_instrument_code
  --     p_pattern_rec_countries_tab      pattern_rec_countries table
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_pattern_rec_countries(p_user                       IN pattern_alerts.created_by%TYPE,
                                      p_logical_load_timestamp     IN pattern_alerts.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp  IN pattern_alerts.effective_start_timestamp%TYPE,
                                      p_product_instrument_code    IN pattern_alerts.product_instrument_code%TYPE,
                                      p_pattern_rec_countries_tab  IN pattern_rec_countries_tab)
  IS
  TYPE ltype_pattern_rec_countries IS TABLE OF pattern_rec_countries%ROWTYPE;
  ltab_pattern_rec_countries ltype_pattern_rec_countries;

  BEGIN
     logger.logger.set_module('put_pattern_rec_countries');

    ------------------------
    --- Select for deleting a row for given pattern_alert_id for if it does not exist in a new msg
    -------------------------
    BEGIN

      SELECT old_version.instrument_id
            ,old_version.country
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
        BULK COLLECT INTO ltab_pattern_rec_countries
        FROM pattern_rec_countries old_version
       WHERE old_version.instrument_id = p_product_instrument_code
         AND NOT EXISTS (SELECT 1
                           FROM TABLE(CAST(p_pattern_rec_countries_tab AS pattern_rec_countries_tab)) new_version
                          WHERE new_version.country = old_version.country  )
         FOR UPDATE OF old_version.instrument_id;

      FOR l_vcount IN 1..ltab_pattern_rec_countries.COUNT
        LOOP
          put_history(p_tab_old_pattern_rec_contry   => ltab_pattern_rec_countries(l_vcount),
                      p_effective_end_timestamp      => p_effective_start_timestamp,
                      p_product_instrument_code      => p_product_instrument_code,
                      p_action                       => 'D');
      END LOOP;

      FORALL l_vcount IN 1..ltab_pattern_rec_countries.COUNT
        DELETE FROM pattern_rec_countries
         WHERE instrument_id = p_product_instrument_code
           AND country = ltab_pattern_rec_countries(l_vcount).country;

      ltab_pattern_rec_countries.delete();

    EXCEPTION WHEN NO_DATA_FOUND THEN
      NULL;
    END;

     MERGE INTO pattern_rec_countries old_version USING
        (SELECT DISTINCT country
           FROM TABLE(CAST(p_pattern_rec_countries_tab AS pattern_rec_countries_tab))) new_version
      ON (old_version.instrument_id = p_product_instrument_code
      AND old_version.country = new_version.country )
     WHEN NOT MATCHED THEN
      INSERT (instrument_id
             ,country
             ,logical_load_timestamp
             ,created_by
             ,create_timestamp
             ,updated_by
             ,update_timestamp
             ,effective_start_timestamp
             )
      VALUES (p_product_instrument_code
             ,new_version.country
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             );

   logger.logger.set_module(NULL);

  EXCEPTION WHEN OTHERS THEN
        logger.logger.severe(logger.logger.error_backtrace);
        logger.logger.set_module(NULL);
        raise_application_error(-20004, logger.logger.error_backtrace);
  END put_pattern_rec_countries;

-- ===================================================================================
  -- put_alert_notification_types
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put put_alert_notification_types
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PATTERN_S_NOTIFICATIONS
  --     PATTERN_S_NOTIFICATIONS_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_pattern_subscription_id        Pattern_subscription_id
  --     p_pattern_rec_countries_tab      pattern_rec_countries table
  --
  --------------------------------------------------------------------------------------

  PROCEDURE put_alert_notification_types(p_user                             IN pattern_subscriptions.created_by%TYPE,
                                         p_logical_load_timestamp           IN pattern_subscriptions.logical_load_timestamp%TYPE,
                                         p_effective_start_timestamp        IN pattern_subscriptions.effective_start_timestamp%TYPE,
                                         p_pattern_subscription_id          IN pattern_subscriptions.pattern_subscription_id%TYPE,
                                         p_alert_notification_types_tab     IN alert_notification_types_tab)
  IS
  TYPE ltype_alert_notification_types IS TABLE OF PATTERN_S_NOTIFICATIONS%ROWTYPE;
  ltab_alert_notification_types ltype_alert_notification_types;

 BEGIN

     logger.logger.set_module('put_alert_notification_types');


    -------------------------
    --- Select for deleting a row for given pattern_alert_id for if it does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.pattern_subscription_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.alert_notification_type
            BULK COLLECT INTO ltab_alert_notification_types
      FROM pattern_s_notifications old_version
        WHERE old_version.pattern_subscription_id=p_pattern_subscription_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_alert_notification_types_tab AS alert_notification_types_tab)) new_version
                         WHERE new_version.alert_notification_type=old_version.alert_notification_type)
        FOR UPDATE OF old_version.pattern_subscription_id;


     FOR l_vcount IN 1..ltab_alert_notification_types.COUNT
       LOOP
          put_history(p_tab_old_alert_notifi_typ => ltab_alert_notification_types(l_vcount),
                      p_effective_end_timestamp         => p_effective_start_timestamp,
                      p_pattern_subscription_id         => p_pattern_subscription_id,
                      p_action                          => 'D');
      END LOOP;


     FORALL l_vcount IN 1..ltab_alert_notification_types.COUNT
      DELETE FROM pattern_s_notifications
       WHERE pattern_subscription_id = p_pattern_subscription_id
       AND alert_notification_type = ltab_alert_notification_types(l_vcount).alert_notification_type;

      ltab_alert_notification_types.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;

   --
    -- As both the columns coming in tab are primary key,there is no need to check if the data has changed from old to new,it can be inserted
    --



     MERGE  INTO  pattern_s_notifications old_version
      USING (SELECT alert_notification_type
              FROM TABLE(CAST(p_alert_notification_types_tab AS alert_notification_types_tab))) new_version
      ON (old_version.pattern_subscription_id = p_pattern_subscription_id
      AND old_version.alert_notification_type = new_version.alert_notification_type)
      WHEN NOT MATCHED THEN
       INSERT(pattern_subscription_id
            ,logical_load_timestamp
            ,created_by
            ,create_timestamp
            ,updated_by
            ,update_timestamp
            ,effective_start_timestamp
            ,alert_notification_type)
      VALUES (p_pattern_subscription_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.alert_notification_type
            );



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_alert_notification_types;





-- ===================================================================================
  -- put_asset_classes
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put asset classes
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PATTERN_S_ASSET_CLASSES
  --     PATTERN_S_ASSET_CLASSES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_pattern_subscription_id        Pattern_subscription_id
  --     p_asset_classes_tab              pattern_s_asset_classes table
  --
  --------------------------------------------------------------------------------------

PROCEDURE put_asset_classes (p_user                             IN pattern_subscriptions.created_by%TYPE,
                             p_logical_load_timestamp           IN pattern_subscriptions.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp        IN pattern_subscriptions.effective_start_timestamp%TYPE,
                             p_pattern_subscription_id          IN pattern_subscriptions.pattern_subscription_id%TYPE,
                             p_asset_classes_tab                IN asset_classes_tab)
  IS
  TYPE ltype_asset_classes IS TABLE OF pattern_s_asset_classes%ROWTYPE;
        ltab_asset_classes ltype_asset_classes;

 BEGIN

     logger.logger.set_module('put_asset_classes');


    -------------------------
    --- Select for deleting a row for given pattern_subscription_id for if it does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.pattern_subscription_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.asset_class
            BULK COLLECT INTO ltab_asset_classes
      FROM pattern_s_asset_classes old_version
	WHERE old_version.pattern_subscription_id=p_pattern_subscription_id
	AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_asset_classes_tab AS asset_classes_tab)) new_version
					WHERE new_version.asset_class=old_version.asset_class)
	FOR UPDATE OF old_version.pattern_subscription_id;


     FOR l_vcount IN 1..ltab_asset_classes.COUNT
       LOOP
          put_history(p_tab_old_asset_classes           => ltab_asset_classes(l_vcount),
                      p_effective_end_timestamp         => p_effective_start_timestamp,
                      p_pattern_subscription_id         => p_pattern_subscription_id,
                      p_action                          => 'D');
      END LOOP;


     FORALL l_vcount IN 1..ltab_asset_classes.COUNT
      DELETE FROM pattern_s_asset_classes
       WHERE pattern_subscription_id = p_pattern_subscription_id
       AND asset_class = ltab_asset_classes(l_vcount).asset_class;

      ltab_asset_classes.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;



     MERGE  INTO  pattern_s_asset_classes old_version
      USING (SELECT asset_class
              FROM TABLE(CAST(p_asset_classes_tab AS asset_classes_tab))) new_version
      ON (old_version.pattern_subscription_id = p_pattern_subscription_id
      AND old_version.asset_class = new_version.asset_class)
     WHEN NOT MATCHED THEN
       INSERT(pattern_subscription_id
            ,logical_load_timestamp
            ,created_by
            ,create_timestamp
            ,updated_by
            ,update_timestamp
            ,effective_start_timestamp
            ,asset_class)
      VALUES (p_pattern_subscription_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.asset_class
            );



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_asset_classes;





-- ===================================================================================
  -- put_intervals
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put intervals
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PATTERN_S_INTERVALS
  --     PATTERN_S_INTERVALS_H

  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_pattern_subscription_id        Pattern_subscription_id
  --     p_intervals_tab                  pattern_s_intervals table
  --
  --------------------------------------------------------------------------------------

PROCEDURE put_intervals (p_user                                 IN pattern_subscriptions.created_by%TYPE,
                             p_logical_load_timestamp           IN pattern_subscriptions.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp        IN pattern_subscriptions.effective_start_timestamp%TYPE,
                             p_pattern_subscription_id          IN pattern_subscriptions.pattern_subscription_id%TYPE,
                             p_intervals_tab                    IN intervals_tab)
  IS
  TYPE ltype_intervals IS TABLE OF pattern_s_intervals%ROWTYPE;
        ltab_intervals ltype_intervals;

 BEGIN

     logger.logger.set_module('put_intervals');


    -------------------------
    --- Select for deleting a row for given pattern_subscription_id for if it does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.pattern_subscription_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.interval
            BULK COLLECT INTO ltab_intervals
      FROM pattern_s_intervals old_version
        WHERE old_version.pattern_subscription_id=p_pattern_subscription_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_intervals_tab AS intervals_tab)) new_version
                        WHERE new_version.interval=old_version.interval )
        FOR UPDATE OF old_version.pattern_subscription_id;


     FOR l_vcount IN 1..ltab_intervals.COUNT
       LOOP
          put_history(p_tab_old_intervals               => ltab_intervals(l_vcount),
                      p_effective_end_timestamp         => p_effective_start_timestamp,
                      p_pattern_subscription_id         => p_pattern_subscription_id,
                      p_action                          => 'D');
      END LOOP;


     FORALL l_vcount IN 1..ltab_intervals.COUNT
      DELETE FROM pattern_s_intervals
       WHERE pattern_subscription_id = p_pattern_subscription_id
       AND interval = ltab_intervals(l_vcount).interval;

      ltab_intervals.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- As both the columns coming in tab are primary key,there is no need to check if the data has changed from old to new,it can be inserted
    --


     MERGE  INTO  pattern_s_intervals old_version
      USING (SELECT interval
              FROM TABLE(CAST(p_intervals_tab AS intervals_tab))) new_version
      ON (old_version.pattern_subscription_id = p_pattern_subscription_id
      and old_version.interval = new_version.interval)
      WHEN NOT MATCHED THEN
       INSERT(pattern_subscription_id
            ,logical_load_timestamp
            ,created_by
            ,create_timestamp
            ,updated_by
            ,update_timestamp
            ,effective_start_timestamp
            ,interval)
      VALUES (p_pattern_subscription_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.interval
            )
    ;



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_intervals;



-- ===================================================================================
  -- put_pattern_types
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_pattern_types
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     pattern_s_pattern_types
  --     pattern_s_pattern_types_h
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_pattern_subscription_id        Pattern_subscription_id
  --     p_pattern_types_tab              pattern_s_pattern_types table
  --
  --------------------------------------------------------------------------------------

PROCEDURE put_pattern_types (p_user                             IN pattern_subscriptions.created_by%TYPE,
                             p_logical_load_timestamp           IN pattern_subscriptions.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp        IN pattern_subscriptions.effective_start_timestamp%TYPE,
                             p_pattern_subscription_id          IN pattern_subscriptions.pattern_subscription_id%TYPE,
                             p_pattern_types_tab                IN pattern_types_tab)
  IS
  TYPE ltype_pattern_types IS TABLE OF pattern_s_pattern_types%ROWTYPE;
        ltab_pattern_types ltype_pattern_types;

 BEGIN

     logger.logger.set_module('put_pattern_types');


    -------------------------
    --- Select for deleting a row for given pattern_subscription_id for if it does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.pattern_subscription_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.pattern_type
            BULK COLLECT INTO ltab_pattern_types
      FROM pattern_s_pattern_types old_version
        WHERE old_version.pattern_subscription_id=p_pattern_subscription_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_pattern_types_tab AS pattern_types_tab)) new_version
                         WHERE new_version.pattern_type=old_version.pattern_type)
        FOR UPDATE OF old_version.pattern_subscription_id;


     FOR l_vcount IN 1..ltab_pattern_types.COUNT
       LOOP
          put_history(p_tab_old_pattern_types           => ltab_pattern_types(l_vcount),
                      p_effective_end_timestamp         => p_effective_start_timestamp,
                      p_pattern_subscription_id         => p_pattern_subscription_id,
                      p_action                          => 'D');
      END LOOP;


     FORALL l_vcount IN 1..ltab_pattern_types.COUNT
      DELETE FROM pattern_s_pattern_types
       WHERE pattern_subscription_id = p_pattern_subscription_id
       AND pattern_type = ltab_pattern_types(l_vcount).pattern_type;

      ltab_pattern_types.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;
    --
    -- As both the columns coming in tab are primary key,there is no need to check if the data has changed from old to new,it can be inserted
    --

     MERGE  INTO  pattern_s_pattern_types old_version
      USING (SELECT pattern_type
              FROM TABLE(CAST(p_pattern_types_tab AS pattern_types_tab))) new_version
      ON (old_version.pattern_subscription_id = p_pattern_subscription_id
      AND old_version.pattern_type = new_version.pattern_type)
      WHEN NOT MATCHED THEN
       INSERT(pattern_subscription_id
            ,logical_load_timestamp
            ,created_by
            ,create_timestamp
            ,updated_by
            ,update_timestamp
            ,effective_start_timestamp
            ,pattern_type)
      VALUES (p_pattern_subscription_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.pattern_type
            );



   logger.logger.set_module(NULL);


 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_pattern_types;




-- ===================================================================================
  -- put_pattern_statuses
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put_pattern_statuses
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     PATTERN_S_PATTERN_STATUSES
  --     PATTERN_S_PATTERN_STATUSES_H

  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical load timestamp
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_pattern_subscription_id        Pattern_subscription_id
  --     p_pattern_statuses_tab              pattern_s_pattern_statuses table
  --
  --------------------------------------------------------------------------------------

PROCEDURE put_pattern_statuses (p_user                          IN pattern_subscriptions.created_by%TYPE,
                             p_logical_load_timestamp           IN pattern_subscriptions.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp        IN pattern_subscriptions.effective_start_timestamp%TYPE,
                             p_pattern_subscription_id          IN pattern_subscriptions.pattern_subscription_id%TYPE,
                             p_pattern_statuses_tab             IN pattern_statuses_tab)
  IS
  TYPE ltype_pattern_statuses IS TABLE OF pattern_s_pattern_statuses%ROWTYPE;
        ltab_pattern_statuses ltype_pattern_statuses;

 BEGIN


          logger.logger.set_module('put_pattern_statuses');


    -------------------------
    --- Select for deleting a row for given pattern_subscription_id for if it does not exist in a new msg
    -------------------------
     BEGIN
     SELECT old_version.pattern_subscription_id
            ,old_version.logical_load_timestamp
            ,old_version.created_by
            ,old_version.create_timestamp
            ,old_version.updated_by
            ,old_version.update_timestamp
            ,old_version.effective_start_timestamp
            ,old_version.pattern_status
            BULK COLLECT INTO ltab_pattern_statuses
      FROM pattern_s_pattern_statuses old_version
        WHERE old_version.pattern_subscription_id=p_pattern_subscription_id
        AND NOT EXISTS (SELECT 1 FROM TABLE(CAST(p_pattern_statuses_tab AS pattern_statuses_tab)) new_version
                        WHERE new_version.pattern_status=old_version.pattern_status )
        FOR UPDATE OF old_version.pattern_subscription_id;


     FOR l_vcount IN 1..ltab_pattern_statuses.COUNT
       LOOP
          put_history(p_tab_old_pattern_statuses        => ltab_pattern_statuses(l_vcount),
                      p_effective_end_timestamp         => p_effective_start_timestamp,
                      p_pattern_subscription_id         => p_pattern_subscription_id,
                      p_action                          => 'D');
      END LOOP;


     FORALL l_vcount IN 1..ltab_pattern_statuses.COUNT
      DELETE FROM pattern_s_pattern_statuses
       WHERE pattern_subscription_id = p_pattern_subscription_id
       AND pattern_status = ltab_pattern_statuses(l_vcount).pattern_status;

      ltab_pattern_statuses.delete();

      EXCEPTION WHEN NO_DATA_FOUND THEN
        NULL;
      END;

    --
    -- As both the columns coming in tab are primary key,there is no need to check if the data has changed from old to new,it can be inserted
    --

     MERGE  INTO  pattern_s_pattern_statuses old_version
      USING (SELECT pattern_status
              FROM TABLE(CAST(p_pattern_statuses_tab AS pattern_statuses_tab))) new_version
      ON (old_version.pattern_subscription_id = p_pattern_subscription_id
			AND old_version.pattern_status = new_version.pattern_status)
      WHEN NOT MATCHED THEN
       INSERT(pattern_subscription_id
            ,logical_load_timestamp
            ,created_by
            ,create_timestamp
            ,updated_by
            ,update_timestamp
            ,effective_start_timestamp
            ,pattern_status)
      VALUES (p_pattern_subscription_id
             ,p_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.pattern_status
            );



   logger.logger.set_module(NULL);

 EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);
  END put_pattern_statuses;



-- ===================================================================================
--  PUBLIC MODULES
-- ===================================================================================
--
--
--
-- ===================================================================================
-- version
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Function to retrieve the version of the package
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--
-- Return:
-- -------
--
--     Returns a VARCHAR2 representing the version of the package
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--
-- -----------------------------------------------------------------------------------

FUNCTION version
  RETURN VARCHAR2 DETERMINISTIC
IS

BEGIN
logger.logger.set_module('version');
RETURN gc_version;
EXCEPTION
WHEN OTHERS THEN
  logger.logger.SEVERE(logger.logger.error_backtrace);
  logger.logger.set_module(NULL);
  RAISE;
END version;



-- ===================================================================================
-- put_pattern_alerts
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put Pattern Alerts
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--     PARTNER_ALERTS
--		PATTERN_ALERTS_H

--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--		p_user
--   	p_effective_start_timestamp
--   	p_pattern_alert_id
--   	p_type
--   	p_creation_time
--   	p_last_modified_time
--   	p_creator
--   	p_last_modifier
--   	p_status
--   	p_product_instrument_code
--   	p_product_asset_class
--   	p_pattern_type
--   	p_interval
--   	p_pattern_status
--   	p_strength_rating
--   	p_start_time
--   	p_length
--   	p_trend_direction
--   	p_breakout_time
--   	p_price_at_breakout
-- 		p_price_projection_low
--		p_price_projection_high
--		p_pattern_success
--		p_price_projection_hit_time
--		p_pattern_data_xml
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------
PROCEDURE put_pattern_alerts ( p_user				IN pattern_alerts.created_by%TYPE,
					p_effective_start_timestamp 	IN pattern_alerts.effective_start_timestamp%TYPE,
					p_pattern_alert_id				IN pattern_alerts.pattern_alert_id%TYPE,
					p_type							IN pattern_alerts.type%TYPE,
					p_creation_time					IN pattern_alerts.creation_time%TYPE,
					p_last_modified_time			IN pattern_alerts.last_modified_time%TYPE,
					p_creator						IN pattern_alerts.creator%TYPE,
					p_last_modifier					IN pattern_alerts.last_modifier%TYPE,
					p_status						IN pattern_alerts.status%TYPE,
					p_product_instrument_code		IN pattern_alerts.product_instrument_code%TYPE,
					p_product_asset_class			IN pattern_alerts.product_asset_class%TYPE,
					p_pattern_rec_countries_tab     IN pattern_rec_countries_tab,
					p_pattern_type					IN pattern_alerts.pattern_type%TYPE,
					p_interval						IN pattern_alerts.interval%TYPE,
					p_pattern_status				IN pattern_alerts.pattern_status%TYPE,
					p_strength_rating				IN pattern_alerts.strength_rating%TYPE,
					p_start_time					IN pattern_alerts.start_time%TYPE,
					p_length						IN pattern_alerts.length%TYPE,
					p_trend_direction				IN pattern_alerts.trend_direction%TYPE,
					p_breakout_time					IN pattern_alerts.breakout_time%TYPE,
					p_price_at_breakout				IN pattern_alerts.price_at_breakout%TYPE,
					p_price_projection_low			IN pattern_alerts.price_projection_low%TYPE,
					p_price_projection_high			IN pattern_alerts.price_projection_high%TYPE,
					p_pattern_success				IN pattern_alerts.pattern_success%TYPE,
					p_price_projection_hit_time		IN pattern_alerts.price_projection_hit_time%TYPE,
					p_pattern_data_xml				IN pattern_alerts.pattern_data_xml%TYPE
         )
IS

ltab_old_pattern_alerts      pattern_alerts%ROWTYPE;

lv_logical_load_timestamp    pattern_alerts.logical_load_timestamp%TYPE;
l_instrument_code_exist NUMBER;

BEGIN

   logger.logger.set_module('put_pattern_alerts');
   lv_logical_load_timestamp := SYSTIMESTAMP;



	--------------------
	---- Determine if any new instrument_code is coming
	--------------------

	IF (p_product_instrument_code IS NOT NULL) THEN
	SELECT COUNT(1)
	INTO l_instrument_code_exist
	FROM instruments
	WHERE instrument_code = p_product_instrument_code;

	  --
	  -- This is to ensure that the instrument_code record exists for the referential integrity
	  --
	IF (l_instrument_code_exist = 0) THEN
	  bi_ods.nrg_products.create_instrument_stub(p_user => p_user
								  ,p_logical_load_timestamp => lv_logical_load_timestamp
								  ,p_effective_start_timestamp => p_effective_start_timestamp
								  ,p_instrument_code => p_product_instrument_code
                 , p_product_platform   => NULl
                 , p_product_wrapper   =>NULL
                  ,p_mm_instrument_id  => NULL);
	END IF;
	END IF;

--
-- Find partners if exist in the partners table, prepare table for update and then put the old recored to history
--

	BEGIN
		SELECT old_version.pattern_alert_id
			,old_version.logical_load_timestamp
			,old_version.created_by
			,old_version.create_timestamp
			,old_version.updated_by
			,old_version.update_timestamp
			,old_version.effective_start_timestamp
			,old_version.business_date
			,old_version.reporting_date
			,old_version.type
			,old_version.creation_time
			,old_version.last_modified_time
			,old_version.creator
			,old_version.last_modifier
			,old_version.status
			,old_version.product_instrument_code
			,old_version.product_asset_class
			,old_version.pattern_type
			,old_version.interval
			,old_version.pattern_status
			,old_version.strength_rating
			,old_version.start_time
			,old_version.length
			,old_version.trend_direction
			,old_version.breakout_time
			,old_version.price_at_breakout
			,old_version.price_projection_low
			,old_version.price_projection_high
			,old_version.pattern_success
			,old_version.price_projection_hit_time
			,old_version.pattern_data_xml
	   INTO ltab_old_pattern_alerts
	  FROM ( SELECT 	p_pattern_alert_id			pattern_alert_id,
						p_type						type,
						p_creation_time			    creation_time,
						p_last_modified_time		last_modified_time,
						p_creator					creator,
						p_last_modifier				last_modifier,
						p_status					status,
						p_product_instrument_code	product_instrument_code	,
						p_product_asset_class		product_asset_class,
						p_pattern_type				pattern_type,
						p_interval					interval,
						p_pattern_status			pattern_status,
						p_strength_rating			strength_rating	,
						p_start_time				start_time,
						p_length					length,
						p_trend_direction			trend_direction	,
						p_breakout_time				breakout_time,
						p_price_at_breakout			price_at_breakout,
						p_price_projection_low		price_projection_low,
						p_price_projection_high		price_projection_high,
						p_pattern_success			pattern_success,
						p_price_projection_hit_time	price_projection_hit_time,
						p_pattern_data_xml			pattern_data_xml
			 FROM dual  ) new_version,
		   pattern_alerts old_version
	  WHERE new_version.pattern_alert_id=old_version.pattern_alert_id
		AND (nrg_common.has_value_changed(new_version.type, old_version.type) 												= 1 OR
			nrg_common.has_value_changed(new_version.creation_time, old_version.creation_time) 								= 1 OR
			nrg_common.has_value_changed(new_version.last_modified_time, old_version.last_modified_time) 					= 1 OR
			nrg_common.has_value_changed(new_version.creator, old_version.creator) 											= 1 OR
			nrg_common.has_value_changed(new_version.last_modifier, old_version.last_modifier) 								= 1 OR
			nrg_common.has_value_changed(new_version.status, old_version.status) 											= 1 OR
			nrg_common.has_value_changed(new_version.product_instrument_code, old_version.product_instrument_code) 			= 1 OR
			nrg_common.has_value_changed(new_version.product_asset_class, old_version.product_asset_class) 					= 1 OR
			nrg_common.has_value_changed(new_version.pattern_type, old_version.pattern_type) 								= 1 OR
			nrg_common.has_value_changed(new_version.interval, old_version.interval) 										= 1 OR
			nrg_common.has_value_changed(new_version.pattern_status, old_version.pattern_status) 							= 1 OR
			nrg_common.has_value_changed(new_version.strength_rating, old_version.strength_rating) 							= 1 OR
			 nrg_common.has_value_changed(new_version.start_time, old_version.start_time) 							        = 1 OR
			nrg_common.has_value_changed(new_version.length, old_version.length) 											= 1 OR
			nrg_common.has_value_changed(new_version.trend_direction, old_version.trend_direction) 							= 1 OR
			nrg_common.has_value_changed(new_version.breakout_time, old_version.breakout_time) 								= 1 OR
			nrg_common.has_value_changed(new_version.price_at_breakout, old_version.price_at_breakout) 						= 1 OR
			nrg_common.has_value_changed(new_version.price_projection_low, old_version.price_projection_low)				= 1 OR
			nrg_common.has_value_changed(new_version.price_projection_high, old_version.price_projection_high) 				= 1 OR
			nrg_common.has_value_changed(new_version.pattern_success, old_version.pattern_success) 							= 1 OR
		nrg_common.has_value_changed(new_version.price_projection_hit_time, old_version.price_projection_hit_time) 	        = 1 OR
			nrg_common.has_value_changed(new_version.pattern_data_xml, old_version.pattern_data_xml) 						= 1)
		FOR UPDATE OF old_version.pattern_alert_id;


		put_history(p_tab_old_pattern_alert        => ltab_old_pattern_alerts,
					p_effective_end_timestamp      => p_effective_start_timestamp,
					p_action                       => 'U');

	   EXCEPTION
		 WHEN NO_DATA_FOUND  THEN
			NULL;
  END;

--
-- Insert/Update the new data
--
 MERGE  INTO  pattern_alerts old_version
  USING ( SELECT 	p_pattern_alert_id			pattern_alert_id	,
					p_type						type	,
					p_creation_time			    creation_time		,
					p_last_modified_time		last_modified_time	,
					p_creator					creator	,
					p_last_modifier				last_modifier	,
					p_status					status,
					p_product_instrument_code	product_instrument_code	,
					p_product_asset_class		product_asset_class,
					p_pattern_type				pattern_type	,
					p_interval					interval,
					p_pattern_status			pattern_status,
					p_strength_rating			strength_rating	,
					p_start_time				start_time,
					p_length					length,
					p_trend_direction			trend_direction	,
					p_breakout_time				breakout_time	,
					p_price_at_breakout			price_at_breakout,
					p_price_projection_low		price_projection_low	,
					p_price_projection_high		price_projection_high,
					p_pattern_success			pattern_success,
					p_price_projection_hit_time	price_projection_hit_time,
					p_pattern_data_xml			pattern_data_xml
		 FROM dual ) new_version
  ON (old_version.pattern_alert_id = new_version.pattern_alert_id)
  WHEN MATCHED THEN
  UPDATE
  SET   logical_load_timestamp = lv_logical_load_timestamp
	   ,updated_by = p_user
	   ,update_timestamp = SYSTIMESTAMP
	   ,effective_start_timestamp 	= p_effective_start_timestamp
	   ,business_date               = nrg_common.get_business_date(p_start_time)
       ,reporting_date              = nrg_common.get_reporting_date(p_start_time)
	   ,type                       	= new_version.type
	   ,creation_time               = new_version.creation_time
	   ,last_modified_time          = new_version.last_modified_time
	   ,creator            		 	= new_version.creator
	   ,last_modifier               = new_version.last_modifier
	   ,status         			 	= new_version.status
	   ,product_instrument_code     = new_version.product_instrument_code
	   ,product_asset_class         = new_version.product_asset_class
	   ,pattern_type                = new_version.pattern_type
	   ,interval                   	= new_version.interval
	   ,pattern_status            	= new_version.pattern_status
	   ,strength_rating            	= new_version.strength_rating
	   ,start_time             	 	= new_version.start_time
	   ,length             			= new_version.length
	   ,trend_direction             = new_version.trend_direction
	   ,breakout_time               = new_version.breakout_time
	   ,price_at_breakout        	= new_version.price_at_breakout
	   ,price_projection_low        = new_version.price_projection_low
	   ,price_projection_high       = new_version.price_projection_high
	   ,pattern_success  			= new_version.pattern_success
	   ,price_projection_hit_time   = new_version.price_projection_hit_time
	   ,pattern_data_xml            = new_version.pattern_data_xml
   WHERE new_version.pattern_alert_id=old_version.pattern_alert_id
   AND (nrg_common.has_value_changed(new_version.type, old_version.type) 												= 1 OR
		nrg_common.has_value_changed(new_version.creation_time, old_version.creation_time) 								= 1 OR
		nrg_common.has_value_changed(new_version.last_modified_time, old_version.last_modified_time) 					= 1 OR
		nrg_common.has_value_changed(new_version.creator, old_version.creator) 											= 1 OR
		nrg_common.has_value_changed(new_version.last_modifier, old_version.last_modifier) 								= 1 OR
		nrg_common.has_value_changed(new_version.status, old_version.status) 											= 1 OR
		nrg_common.has_value_changed(new_version.product_instrument_code, old_version.product_instrument_code) 			= 1 OR
		nrg_common.has_value_changed(new_version.product_asset_class, old_version.product_asset_class) 					= 1 OR
		nrg_common.has_value_changed(new_version.pattern_type, old_version.pattern_type) 								= 1 OR
		nrg_common.has_value_changed(new_version.interval, old_version.interval) 										= 1 OR
		nrg_common.has_value_changed(new_version.pattern_status, old_version.pattern_status) 							= 1 OR
		nrg_common.has_value_changed(new_version.strength_rating, old_version.strength_rating) 							= 1 OR
		 nrg_common.has_value_changed(new_version.start_time, old_version.start_time) 									= 1 OR
		nrg_common.has_value_changed(new_version.length, old_version.length) 											= 1 OR
		nrg_common.has_value_changed(new_version.trend_direction, old_version.trend_direction) 							= 1 OR
		nrg_common.has_value_changed(new_version.breakout_time, old_version.breakout_time) 								= 1 OR
		nrg_common.has_value_changed(new_version.price_at_breakout, old_version.price_at_breakout) 						= 1 OR
		nrg_common.has_value_changed(new_version.price_projection_low, old_version.price_projection_low)				= 1 OR
		nrg_common.has_value_changed(new_version.price_projection_high, old_version.price_projection_high) 				= 1 OR
		nrg_common.has_value_changed(new_version.pattern_success, old_version.pattern_success) 							= 1 OR
		nrg_common.has_value_changed(new_version.price_projection_hit_time, old_version.price_projection_hit_time)  	= 1 OR
		nrg_common.has_value_changed(new_version.pattern_data_xml, old_version.pattern_data_xml) 						= 1)
  WHEN NOT MATCHED THEN
   INSERT   (pattern_alert_id,
			logical_load_timestamp,
			created_by,
			create_timestamp,
			updated_by,
			update_timestamp,
			effective_start_timestamp,
			business_date,
            reporting_date,
			type,
			creation_time,
			last_modified_time,
			creator,
			last_modifier,
			status,
			product_instrument_code,
			product_asset_class,
			pattern_type,
			interval,
			pattern_status,
			strength_rating,
			start_time,
			length,
			trend_direction,
			breakout_time,
			price_at_breakout,
			price_projection_low,
			price_projection_high,
			pattern_success,
			price_projection_hit_time,
			pattern_data_xml
			)
  VALUES (new_version.pattern_alert_id
		 ,lv_logical_load_timestamp
		 ,p_user
		 ,systimestamp
		 ,p_user
		 ,systimestamp
		 ,p_effective_start_timestamp
		 ,nrg_common.get_business_date(new_version.start_time)
         ,nrg_common.get_reporting_date(new_version.start_time)
		 ,new_version.type
		,new_version.creation_time
		,new_version.last_modified_time
		,new_version.creator
		,new_version.last_modifier
		,new_version.status
		,new_version.product_instrument_code
		,new_version.product_asset_class
		,new_version.pattern_type
		,new_version.interval
		,new_version.pattern_status
		,new_version.strength_rating
		,new_version.start_time
		,new_version.length
		,new_version.trend_direction
		,new_version.breakout_time
		,new_version.price_at_breakout
		,new_version.price_projection_low
		,new_version.price_projection_high
		,new_version.pattern_success
		,new_version.price_projection_hit_time
		,new_version.pattern_data_xml);



--------------------
--- put_pattern_rec_countries
--------------------

          put_pattern_rec_countries(p_user                       	=> p_user,
                                   p_logical_load_timestamp     	=> lv_logical_load_timestamp,
                                   p_effective_start_timestamp  	=> p_effective_start_timestamp,
                                   p_product_instrument_code      	=> p_product_instrument_code,
                                   p_pattern_rec_countries_tab   	=> p_pattern_rec_countries_tab);


logger.logger.set_module(NULL);


EXCEPTION
WHEN DUP_VAL_ON_INDEX THEN
    RETURN;
WHEN OTHERS THEN
logger.logger.severe(logger.logger.error_backtrace);
logger.logger.set_module(NULL);
raise_application_error(-20004, logger.logger.error_backtrace);

END put_pattern_alerts;





-- ===================================================================================
-- put_pattern_subscriptions
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put pattern subscriptions
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--      PATTERN_SUBSCRIPTIONS
--	    PATTERN_SUBSCRIPTIONS_H

--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--		p_user
--   	p_effective_start_timestamp
--   	p_pattern_subscription_id
--   	p_created_by
--   	p_create_timestamp
--   	p_updated_by
--   	p_update_timestamp
--   	p_effective_start_timestamp
--      p_type
--   	p_creation_Time 
--   	p_last_modified_time 
--   	p_creator
--   	p_last_modifier
--   	p_trading_account_dd
--   	p_status
--   	p_expiry_time
--   	p_alert_notification_types_tab
--   	p_person_id
--   	p_name
--      p_country_code
--   	p_asset_classes_tab
--   	p_intervals_tab
-- 	    p_pattern_types_tab
--	    p_pattern_statuses_tab

--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------



PROCEDURE put_pattern_subscriptions (p_user                            IN pattern_subscriptions.created_by%TYPE,
                                    p_effective_start_timestamp        IN pattern_subscriptions.effective_start_timestamp%TYPE,
                                    p_pattern_subscription_id          IN pattern_subscriptions.pattern_subscription_id%TYPE,
                                    p_type                             IN pattern_subscriptions.type%TYPE,
                                    p_creation_Time                    IN pattern_subscriptions.creation_time%TYPE,
                                    p_last_modified_time               IN pattern_subscriptions.last_modified_time%TYPE,
                                    p_creator                          IN pattern_subscriptions.creator%TYPE,
                                    p_last_modifier                    IN pattern_subscriptions.last_modifier%TYPE,
                                    p_trading_account_id               IN pattern_subscriptions.trading_account_id%TYPE,
                                    p_status                           IN pattern_subscriptions.status%TYPE,
                                    p_expiry_time                      IN pattern_subscriptions.expiry_time%TYPE,
                                    p_alert_notification_types_tab     IN alert_notification_types_tab DEFAULT NULL,
                                    p_person_id                        IN pattern_subscriptions.person_id%TYPE,
                                    p_name                             IN pattern_subscriptions.name%TYPE,
                                    p_country_code                     IN pattern_subscriptions.country_code%TYPE,
                                    p_asset_classes_tab                IN asset_classes_tab ,
                                    p_intervals_tab                    IN intervals_tab ,
                                    p_pattern_types_tab                IN pattern_types_tab ,
                                    p_pattern_statuses_tab             IN pattern_statuses_tab
                                    )
IS

        ltab_old_pattern_subscriptions      pattern_subscriptions%ROWTYPE;

        lv_logical_load_timestamp    pattern_subscriptions.logical_load_timestamp%TYPE;


BEGIN

   logger.logger.set_module('put_pattern_subscriptions');
   lv_logical_load_timestamp := SYSTIMESTAMP;

    --
    -- Find partners if exist in the partners table, prepare table for update and then put the old record to history
    --

    BEGIN
		SELECT old_version.pattern_subscription_id
			,old_version.logical_load_timestamp
			,old_version.created_by
			,old_version.create_timestamp
			,old_version.updated_by
			,old_version.update_timestamp
			,old_version.effective_start_timestamp
			,old_version.type
			,old_version.creation_time
			,old_version.last_modified_time
			,old_version.creator
			,old_version.last_modifier
			,old_version.trading_account_id
			,old_version.status
			,old_version.expiry_time
			,old_version.person_id
			,old_version.name
			,old_version.country_code
	   INTO ltab_old_pattern_subscriptions
        FROM ( SELECT 	 p_pattern_subscription_id pattern_subscription_id
                        ,p_type type
                        ,p_creation_time  creation_time
                        ,p_last_modified_time  last_modified_time
                        ,p_creator  creator
                        ,p_last_modifier  last_modifier
                        ,p_trading_account_id trading_account_id
                        ,p_status status
                        ,p_expiry_time expiry_time
                        ,p_person_id person_id
                        ,p_name name
                        ,p_country_code country_code
              FROM dual  ) new_version,
		   pattern_subscriptions old_version
	  WHERE new_version.pattern_subscription_id=old_version.pattern_subscription_id
		AND (nrg_common.has_value_changed(new_version.type, old_version.type) 												= 1 OR
			nrg_common.has_value_changed(new_version.creation_time, old_version.creation_time) 								= 1 OR
			nrg_common.has_value_changed(new_version.last_modified_time, old_version.last_modified_time) 					= 1 OR
			nrg_common.has_value_changed(new_version.creator, old_version.creator) 											= 1 OR
			nrg_common.has_value_changed(new_version.last_modifier, old_version.last_modifier) 								= 1 OR
			nrg_common.has_value_changed(new_version.trading_account_id, old_version.trading_account_id) 				    = 1 OR
			nrg_common.has_value_changed(new_version.status, old_version.status) 			                                = 1 OR
			nrg_common.has_value_changed(new_version.expiry_time, old_version.expiry_time) 					                = 1 OR
			nrg_common.has_value_changed(new_version.person_id, old_version.person_id) 								        = 1 OR
			nrg_common.has_value_changed(new_version.name, old_version.name) 										        = 1 OR
			nrg_common.has_value_changed(new_version.country_code, old_version.country_code) 							    = 1
			)
		FOR UPDATE OF old_version.pattern_subscription_id;


		put_history(p_tab_old_pattern_sub         => ltab_old_pattern_subscriptions,
					p_effective_end_timestamp               => p_effective_start_timestamp,
					p_action                                => 'U');

	   EXCEPTION
		 WHEN NO_DATA_FOUND  THEN
			NULL;
  END;

    --
    -- Insert/Update the new data
    --
     MERGE  INTO  pattern_subscriptions old_version
      USING ( SELECT 	 p_pattern_subscription_id pattern_subscription_id
                        ,p_type type
                        ,p_creation_time  creation_time
                        ,p_last_modified_time  last_modified_time
                        ,p_creator  creator
                        ,p_last_modifier  last_modifier
                        ,p_trading_account_id trading_account_id
                        ,p_status status
                        ,p_expiry_time expiry_time
                        ,p_person_id person_id
                        ,p_name name
                        ,p_country_code country_code
              FROM dual ) new_version
      ON (old_version.pattern_subscription_id = new_version.pattern_subscription_id)
      WHEN MATCHED THEN
      UPDATE
      SET   logical_load_timestamp = lv_logical_load_timestamp
           ,updated_by = p_user
           ,update_timestamp = SYSTIMESTAMP
           ,effective_start_timestamp 	= p_effective_start_timestamp
           ,type                       	= new_version.type
           ,creation_time               = new_version.creation_time
           ,last_modified_time          = new_version.last_modified_time
           ,creator            		 	= new_version.creator
           ,last_modifier               = new_version.last_modifier
           ,trading_account_id          = new_version.trading_account_id
           ,status                      = new_version.status
           ,expiry_time                 = new_version.expiry_time
           ,person_id                   = new_version.person_id
           ,name                    	= new_version.name
           ,country_code            	= new_version.country_code
       WHERE new_version.pattern_subscription_id=old_version.pattern_subscription_id
       AND (nrg_common.has_value_changed(new_version.type, old_version.type) 									= 1 OR
            nrg_common.has_value_changed(new_version.creation_time, old_version.creation_time) 				    = 1 OR
            nrg_common.has_value_changed(new_version.last_modified_time, old_version.last_modified_time) 		= 1 OR
            nrg_common.has_value_changed(new_version.creator, old_version.creator) 								= 1 OR
            nrg_common.has_value_changed(new_version.last_modifier, old_version.last_modifier) 					= 1 OR
            nrg_common.has_value_changed(new_version.trading_account_id, old_version.trading_account_id) 		= 1 OR
            nrg_common.has_value_changed(new_version.status, old_version.status) 			                    = 1 OR
            nrg_common.has_value_changed(new_version.expiry_time, old_version.expiry_time) 					    = 1 OR
            nrg_common.has_value_changed(new_version.person_id, old_version.person_id) 							= 1 OR
            nrg_common.has_value_changed(new_version.name, old_version.name) 									= 1 OR
            nrg_common.has_value_changed(new_version.country_code, old_version.country_code) 					= 1
            )
      WHEN NOT MATCHED THEN
       INSERT   (pattern_subscription_id,
                logical_load_timestamp,
                created_by,
                create_timestamp,
                updated_by,
                update_timestamp,
                effective_start_timestamp,
                type,
                creation_time,
                last_modified_time,
                creator,
                last_modifier,
                trading_account_id,
                status,
                expiry_time,
                person_id,
                name,
                country_code
                )
      VALUES (new_version.pattern_subscription_id
             ,lv_logical_load_timestamp
             ,p_user
             ,systimestamp
             ,p_user
             ,systimestamp
             ,p_effective_start_timestamp
             ,new_version.type
            ,new_version.creation_time
            ,new_version.last_modified_time
            ,new_version.creator
            ,new_version.last_modifier
            ,new_version.trading_account_id
            ,new_version.status
            ,new_version.expiry_time
            ,new_version.person_id
            ,new_version.name
            ,new_version.country_code
            );

    --------------------
    --- put_alert_notification_types
    --------------------

              put_alert_notification_types(p_user                   => p_user,
                                       p_logical_load_timestamp     => lv_logical_load_timestamp,
                                       p_effective_start_timestamp  => p_effective_start_timestamp,
                                       p_pattern_subscription_id    => p_pattern_subscription_id,
                                       p_alert_notification_types_tab   => p_alert_notification_types_tab);



    --------------------
    --- put_asset_classes
    --------------------

              put_asset_classes(p_user                      => p_user,
                               p_logical_load_timestamp     => lv_logical_load_timestamp,
                               p_effective_start_timestamp  => p_effective_start_timestamp,
                               p_pattern_subscription_id    => p_pattern_subscription_id,
                               p_asset_classes_tab          => p_asset_classes_tab);

    --------------------
    --- put_intervals
    --------------------

              put_intervals(p_user                      => p_user,
                           p_logical_load_timestamp     => lv_logical_load_timestamp,
                           p_effective_start_timestamp  => p_effective_start_timestamp,
                           p_pattern_subscription_id    => p_pattern_subscription_id,
                           p_intervals_tab              => p_intervals_tab);


	 --------------------
    --- put_pattern_types
    --------------------
			put_pattern_types 		(p_user                             => p_user,
									 p_logical_load_timestamp           => lv_logical_load_timestamp,
									 p_effective_start_timestamp        => p_effective_start_timestamp,
									 p_pattern_subscription_id          => p_pattern_subscription_id,
									 p_pattern_types_tab                => p_pattern_types_tab)		;







    --------------------
    --- put_pattern_statuses
    --------------------

              put_pattern_statuses(p_user                           => p_user,
                                   p_logical_load_timestamp         => lv_logical_load_timestamp,
                                   p_effective_start_timestamp      => p_effective_start_timestamp,
                                   p_pattern_subscription_id        => p_pattern_subscription_id,
                                   p_pattern_statuses_tab           => p_pattern_statuses_tab);




logger.logger.set_module(NULL);


EXCEPTION
WHEN OTHERS THEN
logger.logger.severe(logger.logger.error_backtrace);
logger.logger.set_module(NULL);
raise_application_error(-20004, logger.logger.error_backtrace);

END put_pattern_subscriptions;

-- ===================================================================================
-- put_alert_preferences
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put alert preferences
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--      ALERT_PRFERENCES
--	    ALERT_PRFERENCES_H

--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------


--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_alert_preferences (p_user                            IN alert_preferences.created_by%TYPE,
                                   p_effective_start_timestamp       IN alert_preferences.effective_start_timestamp%TYPE,
                                   p_trading_account_id              IN alert_preferences.trading_account_id%TYPE,
                                   p_person_id                       IN alert_preferences.person_id%TYPE,
                                   p_alert_preferences               IN alert_preferences_tab) IS

    TYPE lv_alert_preferences IS TABLE OF alert_preferences%ROWTYPE;
    ltab_alert_preferences    lv_alert_preferences;
    lv_dml_success            VARCHAR2(10) := 'FALSE';

  BEGIN

    -- Find obsolete preferences
    SELECT *
      BULK COLLECT INTO ltab_alert_preferences
      FROM alert_preferences ap
     WHERE ap.person_id = p_person_id
       AND ap.trading_account_id = p_trading_account_id
       AND NOT EXISTS (SELECT 1
                         FROM TABLE(CAST(p_alert_preferences AS alert_preferences_tab)) new_version
                        WHERE new_version.alert_type              = ap.alert_type
                          AND new_version.alert_notification_type = ap.alert_notification_type);

    -- Put history for obsolete preferences
       FOR lv_count IN 1..ltab_alert_preferences.COUNT LOOP
           put_history(p_tab_old_alert_preferences => ltab_alert_preferences(lv_count),
                       p_effective_end_timestamp   => p_effective_start_timestamp,
                       p_action                    => 'D');
       END LOOP;

    -- Delete obsolete preferences from main table
    FORALL lv_count IN 1..ltab_alert_preferences.COUNT
    DELETE alert_preferences
     WHERE person_id               = ltab_alert_preferences(lv_count).person_id
       AND trading_account_id      = ltab_alert_preferences(lv_count).trading_account_id
       AND alert_type              = ltab_alert_preferences(lv_count).alert_type
       AND alert_notification_type = ltab_alert_preferences(lv_count).alert_notification_type;

    -- Insert new preferences to main table
    
    LOOP
      BEGIN
           MERGE INTO alert_preferences ap USING
         (SELECT p_trading_account_id AS trading_account_id,
                 p_person_id AS person_id,
                 alert_type,
                 alert_notification_type
            FROM TABLE(CAST(p_alert_preferences AS alert_preferences_tab))) new_version
             ON (new_version.person_id               = ap.person_id
             AND new_version.trading_account_id      = ap.trading_account_id
             AND new_version.alert_type              = ap.alert_type
             AND new_version.alert_notification_type = ap.alert_notification_type)
            WHEN NOT MATCHED THEN INSERT
                (person_id,
                 trading_account_id,
                 alert_type,
                 alert_notification_type,
                 logical_load_timestamp,
                 created_by,
                 create_timestamp,
                 updated_by,
                 update_timestamp,
                 effective_start_timestamp)
         VALUES (p_person_id,
                 p_trading_account_id,
                 new_version.alert_type,
                 new_version.alert_notification_type,
                 systimestamp,
                 p_user,
                 systimestamp,
                 p_user,
                 systimestamp,
                 p_effective_start_timestamp);
                 
                 lv_dml_success := 'TRUE';
                                  
      EXCEPTION
        WHEN dup_val_on_index THEN
          lv_dml_success := 'FALSE';
      END;
      
    EXIT WHEN lv_dml_success = 'TRUE';
    END LOOP;

  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END put_alert_preferences;

-- ===================================================================================
-- put_alert_suspensions
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put alert suspensions
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--      ALERT_SUSPENSIONS
--	    ALERT_SUSPENSIONS_H

--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------


--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_alert_suspensions (p_user                            IN alert_suspensions.created_by%TYPE,
                                   p_effective_start_timestamp       IN alert_suspensions.effective_start_timestamp%TYPE,
                                   p_trading_account_id              IN alert_suspensions.trading_account_id%TYPE,
                                   p_person_id                       IN alert_suspensions.person_id%TYPE,
                                   p_alert_suspensions               IN alert_suspensions_obj) IS

    TYPE lv_alert_suspensions IS TABLE OF alert_suspensions%ROWTYPE;
    ltab_alert_suspensions    lv_alert_suspensions;

  BEGIN

    -- Find obsolete suspensions
    SELECT *
      BULK COLLECT INTO ltab_alert_suspensions
      FROM alert_suspensions ap
     WHERE ap.person_id = p_person_id
       AND ap.trading_account_id = p_trading_account_id
       AND (nrg_common.has_value_changed(ap.is_alerts_suspended, p_alert_suspensions.is_alerts_suspended)	= 1 OR
            nrg_common.has_value_changed(ap.suspend_alerts_from, p_alert_suspensions.suspend_alerts_from)	= 1 OR
            nrg_common.has_value_changed(ap.suspend_alerts_to, p_alert_suspensions.suspend_alerts_to)	= 1);

       IF ltab_alert_suspensions IS NOT NULL THEN

         -- Put history for obsolete suspensions
         FOR lv_count IN 1..ltab_alert_suspensions.COUNT LOOP
             put_history(p_tab_old_alert_suspensions => ltab_alert_suspensions(lv_count),
                         p_effective_end_timestamp   => p_effective_start_timestamp,
                         p_action                    => 'U');
         END LOOP;

       END IF;

    -- Insert new suspensions to main table
     MERGE INTO alert_suspensions ap USING
   (SELECT p_trading_account_id                      AS trading_account_id,
           p_person_id                               AS person_id,
           p_alert_suspensions.is_alerts_suspended   AS is_alerts_suspended,
           p_alert_suspensions.suspend_alerts_from   AS suspend_alerts_from,
           p_alert_suspensions.suspend_alerts_to     AS suspend_alerts_to
      FROM dual) new_version
       ON (new_version.person_id               = ap.person_id
       AND new_version.trading_account_id      = ap.trading_account_id)
      WHEN MATCHED THEN UPDATE
       SET logical_load_timestamp    = SYSTIMESTAMP,
           updated_by                = p_user,
           update_timestamp          = SYSTIMESTAMP,
           effective_start_timestamp = p_effective_start_timestamp,
           ap.is_alerts_suspended    = new_version.is_alerts_suspended,
           ap.suspend_alerts_from    = new_version.suspend_alerts_from,
           ap.suspend_alerts_to      = new_version.suspend_alerts_to
      WHEN NOT MATCHED THEN INSERT
          (person_id,
           trading_account_id,
           logical_load_timestamp,
           created_by,
           create_timestamp,
           updated_by,
           update_timestamp,
           effective_start_timestamp,
           is_alerts_suspended,
           suspend_alerts_from,
           suspend_alerts_to)
   VALUES (p_person_id,
           p_trading_account_id,
           systimestamp,
           p_user,
           systimestamp,
           p_user,
           systimestamp,
           p_effective_start_timestamp,
           new_version.is_alerts_suspended,
           new_version.suspend_alerts_from,
           new_version.suspend_alerts_to);

  EXCEPTION
  WHEN OTHERS THEN
    logger.logger.severe(logger.logger.error_backtrace);
    logger.logger.set_module(NULL);
    raise_application_error(-20004, logger.logger.error_backtrace);

  END put_alert_suspensions;

END nrg_pattern;

//