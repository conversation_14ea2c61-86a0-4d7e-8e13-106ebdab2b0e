
  CREATE OR REPLACE EDITIONABLE PACKAGE BODY "BI_ODS"."NRG_POA" 
AS
    -- ===================================================================================
    -- NRG_POA
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    --  Since the power of attorney can exist for a customer account, the trading account
    --  type is always set to CUSTOMER
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Power Of Attorney Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     14/09/2011   Manoj Kumar        1.0    Creation
    --     08/11/2011   Mark Gornicki      1.1    Fixing issues identified in QA review.
    --                                            Amending package to integrate better with
    --                                            NRG_TRADING_ACCOUNT and NRG_PERSON packages
    --                                            such that all actual DML is performed in this package.
    --                                            To achieve this put_poa is overloaded
    --     16/11/2011   Mark Gornicki      1.2    Added the parameter "p_called_by" to the main put_poa procedure.
    --                                            The reason for adding this was that deadlocks were occurring
    --                                            during testing when called by NRG_TRADING_ACCOUNT and NRG_PERSON.
    --                                            For Example, NRG_TRADING_ACCOUNT creates but does not commit a new account,
    --                                            NRG_POA.PUT_POA is called to populate child power of attorney records
    --                                            but by default this attempts to create a trading account stub for that same account,
    --                                            thus creating the deadlock.
    --	   17/11/2011   Mark Gornicki      1.3    Added logical to set logical_load_timestamp if not set by a calling package
    --	   22/11/2011   Sanket Mittal      1.4    Updated for the new field trading_account_type to be populated as CUSTOMER
    -- ===================================================================================

    -- ===================================================================================
    -- PACKAGE CONSTANTS
    -- ===================================================================================

    gc_version CONSTANT VARCHAR2(3) := '1.4';
    gc_cr      CONSTANT VARCHAR2(1) := CHR(10);
    gc_true    CONSTANT PLS_INTEGER := 1;
    gc_false   CONSTANT PLS_INTEGER := 0;
    gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');

    -- ===================================================================================
    -- PRIVATE MODULES
    -- ===================================================================================

    -- ===================================================================================
    -- put_history
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     procedure to write the history table for all the old versions of the power of attorney records
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_power_of_attorney_rec          This is the old version of the trading account product wrapper settings
    --     p_effective_end_timestamp        This is the end time for the record
    --     p_action                         Type of operation performed
    --
    -- Return:
    -- -------
    --
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_history(p_power_of_attorney_rec            power_of_attorneys%ROWTYPE,
                          p_effective_end_timestamp          power_of_attorneys_h.effective_end_timestamp%TYPE,
                          p_action                           power_of_attorneys_h.action%TYPE)
    IS
    BEGIN
      INSERT INTO power_of_attorneys_h(
                                       created_by,
                                       create_timestamp,
                                       updated_by,
                                       update_timestamp,
                                       logical_load_timestamp,
                                       effective_start_timestamp,
                                       effective_end_timestamp,
                                       action,
                                       action_timestamp,
                                       poa_id,
                                       poa_version,
                                       person_id,
                                       trading_account_id,
                                       trading_account_type,
                                       poa_type,
                                       is_authorised
                                      )
                                    VALUES(p_power_of_attorney_rec.created_by,
                                           p_power_of_attorney_rec.create_timestamp,
                                           p_power_of_attorney_rec.updated_by,
                                           p_power_of_attorney_rec.update_timestamp,
                                           p_power_of_attorney_rec.logical_load_timestamp,
                                           p_power_of_attorney_rec.effective_start_timestamp,
                                           p_effective_end_timestamp,
                                           p_action,
                                           SYSTIMESTAMP,
                                           p_power_of_attorney_rec.poa_id,
                                           p_power_of_attorney_rec.poa_version,
                                           p_power_of_attorney_rec.person_id,
                                           p_power_of_attorney_rec.trading_account_id,
                                           p_power_of_attorney_rec.trading_account_type,
                                           p_power_of_attorney_rec.poa_type,
                                           p_power_of_attorney_rec.is_authorised
                                          );
    EXCEPTION
      WHEN DUP_VAL_ON_INDEX THEN
        UPDATE power_of_attorneys_h
           SET updated_by = p_power_of_attorney_rec.updated_by,
               update_timestamp = p_power_of_attorney_rec.update_timestamp,
               logical_load_timestamp = p_power_of_attorney_rec.logical_load_timestamp,
               effective_end_timestamp = p_effective_end_timestamp,
               action = p_action,
               action_timestamp = SYSTIMESTAMP,
               poa_version = p_power_of_attorney_rec.poa_version,
               person_id = p_power_of_attorney_rec.person_id,
               trading_account_id = p_power_of_attorney_rec.trading_account_id,
               trading_account_type = p_power_of_attorney_rec.trading_account_type,
               poa_type = p_power_of_attorney_rec.poa_type,
               is_authorised = p_power_of_attorney_rec.is_authorised
        WHERE poa_id = p_power_of_attorney_rec.poa_id
		  AND effective_start_timestamp = p_power_of_attorney_rec.effective_start_timestamp
		  AND (/* only where there have been for data changes */
                  nrg_common.has_value_changed(poa_version, p_power_of_attorney_rec.poa_version) = 1
			   OR nrg_common.has_value_changed(person_id,p_power_of_attorney_rec.person_id) = 1
			   OR nrg_common.has_value_changed(trading_account_id,p_power_of_attorney_rec.trading_account_id) = 1
			   OR nrg_common.has_value_changed(poa_type,p_power_of_attorney_rec.poa_type) = 1
			   OR nrg_common.has_value_changed(is_authorised,p_power_of_attorney_rec.is_authorised) = 1
               OR nrg_common.has_value_changed(trading_account_type,p_power_of_attorney_rec.trading_account_type) = 1
			  );
    END put_history;

    -- ===================================================================================
    -- PUBLIC MODULES
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION version
      RETURN VARCHAR2 DETERMINISTIC
    IS
    BEGIN
      logger.logger.set_module('version');
      RETURN gc_version;
    EXCEPTION
      WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          RAISE;
    END version;

    -- ===================================================================================
    -- put_poa
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put  power of attorneys
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     POWER_OF_ATTORNEYS

    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_logical_load_timestamp         Logical Load Timestamp.
	--                                      Note this should ONLY be set/passed in when called from the table processing procedure.
	--                                      It should be NULL when called from NRG JAva
    --     p_poa_id                         Power of attorney id
    --     p_poa_version                    Version of power of attorney record
    --     p_person_id                      The person id to which the power of attorney table relates
    --     p_trading_account_id             The trading account id to which the power of attorney table relates
    --     p_poa_type                       The type of power of attorney
    --     p_is_authorised                  Indicates if the relationship is authorised
    --     p_called_by                      The name of the package that called NRG_POA.PUT_POA e.g. NRG_TRADING_ACCOUNT.
    --                                      This will default to NRG_POA to cope with the onPOA data contract where both
    --                                      trading account and person stub creation would be required
    --


    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Power Of Attorney Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- -----------------------------------------------------------------------------------
  PROCEDURE put_poa(p_user                         IN power_of_attorneys.created_by%TYPE,
                    p_effective_start_timestamp    IN power_of_attorneys.effective_start_timestamp%TYPE,
                    p_logical_load_timestamp       IN power_of_attorneys.logical_load_timestamp%TYPE DEFAULT SYSTIMESTAMP,
                    p_poa_id                       IN power_of_attorneys.poa_id%TYPE,
                    p_poa_version                  IN power_of_attorneys.poa_version%TYPE,
                    p_person_id                    IN power_of_attorneys.person_id%TYPE,
                    p_trading_account_id           IN power_of_attorneys.trading_account_id%TYPE,
                    p_poa_type                     IN power_of_attorneys.poa_type%TYPE,
                    p_is_authorised                IN power_of_attorneys.is_authorised%TYPE,
                    p_called_by                    IN user_objects.object_name%TYPE DEFAULT 'NRG_POA')
	IS

	  lv_logical_load_timestamp    power_of_attorneys.logical_load_timestamp%TYPE;
      lv_effective_start_time      power_of_attorneys.effective_start_timestamp%TYPE := NULL;

      lv_old_poa_rec               power_of_attorneys%ROWTYPE;

	  lex_unknown_operation_type   EXCEPTION;
      lex_poa_not_found            EXCEPTION;

	BEGIN

    logger.logger.set_module('put_poa');

	  --
	  -- Set the Logical Load Timestamp to Now if not set by a calling procedure or array of poas
	  --
	  lv_logical_load_timestamp := NVL(p_logical_load_timestamp,SYSTIMESTAMP);

    --
    -- Create Person Stub for referential integrity
    --
	  IF p_person_id IS NOT NULL AND UPPER(p_called_by) <> 'NRG_PERSON' THEN
         nrg_person.create_person_stub (p_user                        => p_user,
                                        p_logical_load_timestamp      => lv_logical_load_timestamp,
                                        p_effective_start_timestamp   => p_effective_start_timestamp,
                                        p_person_id                   => p_person_id);
    END IF;

    --
    -- Create Trading Account Stub for referential integrity
    --
    IF p_trading_account_id IS NOT NULL AND UPPER(p_called_by) <> 'NRG_TRADING_ACCOUNT' THEN
      nrg_trading_account.create_trading_account_stub (p_user                        => p_user,
                                                       p_logical_load_timestamp      => lv_logical_load_timestamp,
                                                       p_effective_start_timestamp   => p_effective_start_timestamp,
                                                       p_trading_account_id          => p_trading_account_id,
                                                       p_trading_account_type        => 'CUSTOMER');
    END IF;

    --
    --Insert new poa in the power of attorneys table
    --
    BEGIN
      INSERT INTO power_of_attorneys (
                                        poa_id,
                                        logical_load_timestamp,
	                                    created_by,
	                                    create_timestamp,
	                                    updated_by,
	                                    update_timestamp,
	                                    effective_start_timestamp,
	                                    poa_version,
	                                    person_id,
	                                    trading_account_id,
                                        trading_account_type,
	                                    poa_type,
	                                    is_authorised
	                                  )
                              VALUES (
                                      p_poa_id,
                                      lv_logical_load_timestamp,
                                      p_user,
                                      SYSTIMESTAMP,
                                      p_user,
                                      SYSTIMESTAMP,
                                      p_effective_start_timestamp,
                                      p_poa_version,
                                      p_person_id,
                                      p_trading_account_id,
                                      'CUSTOMER',
                                      p_poa_type,
                                      p_is_authorised
                                     );

      EXCEPTION
        WHEN DUP_VAL_ON_INDEX THEN
		     -- Get the effective start time of the previous version of the poa
			 -- and load the records for update
             BEGIN
               SELECT poa.*
               INTO lv_old_poa_rec
               FROM power_of_attorneys poa
               WHERE poa_id = p_poa_id
               FOR UPDATE;

               lv_effective_start_time := lv_old_poa_rec.effective_start_timestamp;
             EXCEPTION
               WHEN no_data_found THEN
                    RAISE lex_poa_not_found;
             END;
	  END;

    --
    -- Update processing
	  --

  	CASE
      WHEN lv_effective_start_time IS NULL THEN
        --
        -- Since the poa is already inserted no operation will be performed
        --
        NULL;
      WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time <= p_effective_start_timestamp THEN
        --
        -- In case when the effective start time of the incoming record is greater than the effective start time
        -- of the poa on the power of attorneys table then this specifies the update of the trade
        -- Update power of attorney with the latest version
        --
        UPDATE power_of_attorneys
           SET logical_load_timestamp = lv_logical_load_timestamp,
               updated_by  = p_user,
               update_timestamp = SYSTIMESTAMP,
               effective_start_timestamp = p_effective_start_timestamp,
               poa_version = p_poa_version,
               person_id = p_person_id,
               trading_account_id = p_trading_account_id,
               trading_account_type = 'CUSTOMER',
               poa_type = p_poa_type,
               is_authorised = p_is_authorised
        WHERE poa_id = p_poa_id AND
              (/* only where there have been for data changes */
                 nrg_common.has_value_changed(poa_version,p_poa_version) = 1 OR
                 nrg_common.has_value_changed(person_id,p_person_id) = 1 OR
                 nrg_common.has_value_changed(trading_account_id,p_trading_account_id) = 1 OR
                 nrg_common.has_value_changed(poa_type,p_poa_type) = 1 OR
                 nrg_common.has_value_changed(is_authorised,p_is_authorised) = 1
              );

        --
        -- Write To History if update occurred
        --
        IF lv_old_poa_rec.effective_start_timestamp <> gc_default_timestamp AND SQL%ROWCOUNT > 0 THEN
          --
          -- If the existing poa is not a stub record and the same record has not been replayed
          -- then put the old record in the history
          --
          put_history (p_power_of_attorney_rec => lv_old_poa_rec,
                       p_effective_end_timestamp => p_effective_start_timestamp,
                       p_action => 'U');
        END IF;

        lv_old_poa_rec := NULL;

      WHEN lv_effective_start_time IS NOT NULL AND lv_effective_start_time > p_effective_start_timestamp THEN
        --
        -- When the effective start time of the incoming record is smaller than the effective start time of the
        -- poa on the power of attorneys table then it signifies old message is being replayed.
        -- Write to history table directly
        --
        SELECT
               p_poa_id,
               lv_logical_load_timestamp,
               p_user,
               SYSTIMESTAMP,
               p_user,
               SYSTIMESTAMP,
               p_effective_start_timestamp,
               p_poa_version,
	           p_person_id,
	           p_trading_account_id,
               'CUSTOMER',
	           p_poa_type,
	           p_is_authorised
        INTO
               lv_old_poa_rec.poa_id,
               lv_old_poa_rec.logical_load_timestamp,
               lv_old_poa_rec.created_by,
               lv_old_poa_rec.create_timestamp,
               lv_old_poa_rec.updated_by,
               lv_old_poa_rec.update_timestamp,
               lv_old_poa_rec.effective_start_timestamp,
               lv_old_poa_rec.poa_version,
               lv_old_poa_rec.person_id,
               lv_old_poa_rec.trading_account_id,
               lv_old_poa_rec.trading_account_type,
               lv_old_poa_rec.poa_type,
               lv_old_poa_rec.is_authorised
        FROM DUAL;

        put_history (p_power_of_attorney_rec => lv_old_poa_rec,
                     p_effective_end_timestamp => p_effective_start_timestamp,
                     p_action => 'I');

        lv_old_poa_rec := NULL;

      ELSE
        RAISE lex_unknown_operation_type;
    END CASE;

  EXCEPTION
    WHEN lex_poa_not_found THEN
      logger.logger.severe('Power of Attorney Deleted Before Update and After Insert');
      logger.logger.set_module(NULL);
      raise_application_error(-20003, 'Power of Attorney Deleted Before Update and After Insert');
    WHEN lex_unknown_operation_type THEN
      logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
      logger.logger.set_module(NULL);
      raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
    WHEN OTHERS THEN
      logger.logger.severe(logger.logger.error_backtrace);
      logger.logger.set_module(NULL);
      raise_application_error(-20004, logger.logger.error_backtrace);
  END put_poa;

    -- ===================================================================================
    -- put_poa
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put  poa
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     POWER_OF_ATTORNEYS

    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_logical_load_timestamp         Logical Load Timestamp
    --                                      Note this should be set to the logical load timestamp as set in the calling package
	--                                      i.e. NRG_PERSON or NRG_TRADING_ACCOUNT
    --     p_entity_name                    The name of the entity to which the power of attorney table relates
    --                                      i.e. PERSON or TRADING_ACCOUNT
    --     p_entity_id                      The id of the entity to which the power of attorney table relates
    --                                      i.e. Person Id or Trading Account Id
    --     p_power_of_attorney              Power of attorney tab
    --     p_called_by                      The name of the package that called NRG_POA.PUT_POA e.g. NRG_TRADING_ACCOUNT.
    --                                      This will default to NRG_POA to cope with the onPOA data contract where both
    --                                      trading account and person stub creation would be required
    --


    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Power Of Attorney Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_poa(p_user                         IN power_of_attorneys.created_by%TYPE,
                      p_effective_start_timestamp    IN power_of_attorneys.effective_start_timestamp%TYPE,
                      p_logical_load_timestamp       IN power_of_attorneys.logical_load_timestamp%TYPE,
                      p_entity_name                  IN user_tables.table_name%TYPE,
                      p_entity_id                    IN persons.person_id%TYPE,
                      p_power_of_attorneys           IN power_of_attorney_tab,
					  p_called_by                    IN user_objects.object_name%TYPE DEFAULT 'NRG_POA')
    IS

      lv_logical_load_timestamp    power_of_attorneys.logical_load_timestamp%TYPE;
   	  lv_effective_start_timestamp power_of_attorneys.effective_start_timestamp%TYPE := NULL;

      TYPE ltyp_poa IS TABLE OF power_of_attorneys%ROWTYPE;
      ltab_poa_old   ltyp_poa;

	  lex_unknown_operation_type   EXCEPTION;
      lex_poa_not_found            EXCEPTION;

    BEGIN

      logger.logger.set_module('put_poa');

	  --
	  -- Set the Logical Load Timestamp to Now if not set by a calling procedure
	  --
	  lv_logical_load_timestamp := NVL(p_logical_load_timestamp,SYSTIMESTAMP);

      --
      -- Bulk Delete the data that has got deleted from the source
      -- including the scenario where all values have been removed i.e. empty
      --
      -- The incoming entity name determines which select is used to populate the pl/sql table
      --
      CASE
        WHEN p_entity_name = 'TRADING_ACCOUNT' THEN
             SELECT *
             BULK COLLECT INTO ltab_poa_old
             FROM power_of_attorneys t
             WHERE t.trading_account_id = p_entity_id
		     AND t.poa_id NOT IN (SELECT cpoa.poa_id
		                                   FROM TABLE(CAST(p_power_of_attorneys AS power_of_attorney_tab)) cpoa
								  )
		     FOR UPDATE;
        WHEN p_entity_name = 'PERSON' THEN
             SELECT *
             BULK COLLECT INTO ltab_poa_old
             FROM power_of_attorneys t
             WHERE t.person_id = p_entity_id
		     AND t.poa_id NOT IN (SELECT cpoa.poa_id
		                                   FROM TABLE(CAST(p_power_of_attorneys AS power_of_attorney_tab)) cpoa
								  )
		     FOR UPDATE;
        ELSE
          RAISE lex_unknown_operation_type;
	  END CASE;

	  --
      -- Copy the original records to the history table and delete from the main table
      --
      FOR lv_count IN 1..ltab_poa_old.COUNT LOOP
          put_history(p_power_of_attorney_rec => ltab_poa_old(lv_count),
                      p_effective_end_timestamp => p_effective_start_timestamp,
                      p_action => 'D');
      END LOOP;

	  FORALL lv_count IN 1 .. ltab_poa_old.COUNT
		  DELETE power_of_attorneys
           WHERE poa_id = ltab_poa_old(lv_count).poa_id;

	  --
	  -- Process the passed in Power of Attorney details for inserts and updates
	  --

    IF p_power_of_attorneys IS NOT NULL THEN
      FOR lv_count IN 1..p_power_of_attorneys.COUNT
      LOOP
	      --
	      -- Call the single record processing procedure
		  --
          nrg_poa.put_poa(
		                  p_user                       => p_user,
                          p_effective_start_timestamp  => p_effective_start_timestamp,
                          p_logical_load_timestamp     => lv_logical_load_timestamp,
                          p_poa_id                     => p_power_of_attorneys(lv_count).poa_id,
                          p_poa_version                => p_power_of_attorneys(lv_count).poa_version,
                          p_person_id                  => p_power_of_attorneys(lv_count).person_id,
                          p_trading_account_id         => p_power_of_attorneys(lv_count).trading_account_id,
                          p_poa_type                   => p_power_of_attorneys(lv_count).poa_type,
                          p_is_authorised              => p_power_of_attorneys(lv_count).is_authorised,
                          p_called_by                  => p_called_by
                         );
      END LOOP;
    END IF;

    EXCEPTION
        WHEN lex_poa_not_found THEN
          logger.logger.severe('Power of Attorney Deleted Before Update and After Insert');
          logger.logger.set_module(NULL);
          raise_application_error(-20003, 'Power of Attorney Deleted Before Update and After Insert');
        WHEN lex_unknown_operation_type THEN
          logger.logger.warning('Operation Not Handled. Please Check the Case Condition');
          logger.logger.set_module(NULL);
          raise_application_error(-20002, 'Operation Not Handled. Please Check the Case Condition');
     WHEN OTHERS THEN
          logger.logger.severe(logger.logger.error_backtrace);
          logger.logger.set_module(NULL);
          raise_application_error(-20004, logger.logger.error_backtrace);
    END put_poa;

END nrg_poa;

//