
  CREATE OR REPLACE EDITIONABLE PACKAGE "BI_ODS"."NRG_POSITION" AS
  -- ===================================================================================
  -- NRG_POSITION
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     This package encapsulates NRG (Near Realtime Gatherer)
  --
  --     Management of Positions
  --
  -- -----------------------------------------------------------------------------------
  --
  -- Notes:
  -- ------
  --
  --     Run as BI_ODS
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --     -20001    Entity already exists
  --
  -- Modifications:
  -- --------------
  --
  --     Date         Modified By       Vers    Action
  --     ----------   ---------------   -----   ----------------------------------------
  --     25/06/2020   Patrick Dinwiddy  1.0    Creation
  -- ===================================================================================
  --

  -- ===================================================================================
  -- version
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Function to retrieve the version of the package
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --     Returns a VARCHAR2 representing the version of the package
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

  FUNCTION version RETURN VARCHAR2 DETERMINISTIC;

  -- ===================================================================================
  -- put_anytime_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     ANYTIME_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_anytime_position                    Anytime Position Details
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_anytime_position_set(p_user                      IN VARCHAR2,
                                     p_effective_start_timestamp IN TIMESTAMP,
                                     p_platform                  IN VARCHAR2,
                                     p_anytime_positions         IN anytime_positions_tab);

  -- ===================================================================================
  -- put_eod_position_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put EOD position
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     LATEST_POSITIONS
  --     EOD_POSITIONS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                            Description
  --     ---------------------------------   ----------------------------------------------
  --      p_user                                User
  --      p_effective_start_timestamp           Effective Start Timestamp
  --      p_platform                            Platform
  --      p_business_date                       Business Date
  --      p_reporting_date                      Reporting Date
  --      p_position                            Position Details
  --      p_record_source                       Record Source
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --      -20004    Default Exception
  -----------------------------------------------------------------------------------------

  PROCEDURE put_eod_position_set (p_user                      IN VARCHAR2,
                                  p_effective_start_timestamp IN TIMESTAMP,
                                  p_platform                  IN VARCHAR2,
                                  p_position                  IN position_tab,
                                  p_record_source             IN eod_open_trades.record_source%TYPE,
                                  p_batch_detail              IN VARCHAR2);

END nrg_position;


//