
  CREATE OR REPLACE EDITIONABLE PACKAGE BODY "BI_ODS"."NRG_MESSAGE_HUB" AS
-- ===================================================================================
-- NRG_MESSAGE_HUB
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     This package encapsulates NRG (Near Realtime Gatherer)
--
--     Management of the nrg_message_hub model
--
-- -----------------------------------------------------------------------------------
--
-- Notes:
-- ------
--
--     Run as BI_ODS
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--     -20001    Entity already exists
--
-- Modifications:
-- --------------
--     Date         Modified By       Vers    Action
--   ----------   ---------------   -----   ----------------------------------------
--  10/10/2017   Sakina Kinkhabwala     1.0    Creation
--  18/04/2018   Deepak Rajurkar        1.1    CHanges for BER-4426


--
-- ===================================================================================
--
--
-- ===================================================================================
-- PACKAGE CONSTANTS
-- ===================================================================================

gc_version            CONSTANT VARCHAR2(3) := '1.1';
gc_cr                 CONSTANT VARCHAR2(1) := chr(10);
gc_true               CONSTANT PLS_INTEGER := 1;
gc_false              CONSTANT PLS_INTEGER := 0;
gc_default_timestamp  TIMESTAMP := to_timestamp('01-Jan-1970', 'DD-Mon-YYYY');


--
--
-- ===================================================================================
-- PRIVATE MODULES
-- ===================================================================================
-- ===================================================================================
-- put_history
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to write the history table for MSG_HUB_MESSAGE_STATUS_H
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--     ------------------------------   ----------------------------------------------
--     p_tab_old_message_status          This is the old version of the price_schemas data
--     p_effective_end_timestamp        This is the end time for the record
--     p_action
--
-- Return:
-- -------
--
--
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------

PROCEDURE put_history  (p_tab_old_message_status      IN msg_hub_message_status%ROWTYPE,
                       p_effective_end_timestamp      IN msg_hub_message_status.effective_start_timestamp%TYPE,
                       p_action                       IN msg_hub_message_status_h.action%TYPE)
IS
BEGIN






INSERT INTO msg_hub_message_status_h (
						message_id
						,logical_load_timestamp
						,created_by
						,create_timestamp
						,updated_by
						,update_timestamp
						,effective_start_timestamp
						,effective_end_timestamp
						,message_source
						,message_source_id
						,status
						,action
						,action_timestamp
						)
				  VALUES(
						p_tab_old_message_status.message_id,
						p_tab_old_message_status.logical_load_timestamp,
						p_tab_old_message_status.created_by,
						p_tab_old_message_status.create_timestamp,
						p_tab_old_message_status.updated_by,
						p_tab_old_message_status.update_timestamp,
						p_tab_old_message_status.effective_start_timestamp,
						p_effective_end_timestamp,
						p_tab_old_message_status.message_source,
						p_tab_old_message_status.message_source_id,
						p_tab_old_message_status.status,
						p_action,
						 SYSTIMESTAMP
						);
EXCEPTION
WHEN DUP_VAL_ON_INDEX THEN
  UPDATE msg_hub_message_status_h
  SET    updated_by                    	= p_tab_old_message_status.updated_by,
		 update_timestamp              	= p_tab_old_message_status.update_timestamp,
		 logical_load_timestamp        	= p_tab_old_message_status.logical_load_timestamp,
		 effective_end_timestamp       	= p_effective_end_timestamp,
		 message_source                 = p_tab_old_message_status.message_source,
		 message_source_id              = p_tab_old_message_status.message_source_id,
		 status							= p_tab_old_message_status.status,
		 action                        	= p_action,
		 action_timestamp              	= SYSTIMESTAMP
WHERE    message_id = p_tab_old_message_status.message_id
AND  	effective_start_timestamp =p_tab_old_message_status.effective_start_timestamp
AND 	(nrg_common.has_value_changed(message_source, p_tab_old_message_status.message_source) 			= 1 OR
		nrg_common.has_value_changed(message_source_id, p_tab_old_message_status.message_source_id) 	= 1 OR
		nrg_common.has_value_changed(status, p_tab_old_message_status.status) 	= 1	);

END put_history;






-- ===================================================================================
--  PUBLIC MODULES
-- ===================================================================================
--
--
--
-- ===================================================================================
-- version
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Function to retrieve the version of the package
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--
-- Return:
-- -------
--
--     Returns a VARCHAR2 representing the version of the package
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--     -20000    Error retrieving version : %error_backtrace
--
-- -----------------------------------------------------------------------------------

FUNCTION version
  RETURN VARCHAR2 DETERMINISTIC
IS

BEGIN
logger.logger.set_module('version');
RETURN gc_version;
EXCEPTION
WHEN OTHERS THEN
  logger.logger.SEVERE(logger.logger.error_backtrace);
  logger.logger.set_module(NULL);
  RAISE;
END version;



-- ===================================================================================
-- put_on_message_status
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put put_on_message_status
--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--     MSG_HUB_MESSAGE_STATUS
--		MSG_HUB_MESSAGE_STATUS_H


--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--		p_user
--   	p_effective_start_timestamp
--   	p_message_id
--   	p_creation_time
--   	p_last_modified_time
--   	p_creator
--   	p_last_modifier
--   	p_message_source
--		p_message_source_id
--		p_status


--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------
PROCEDURE put_on_message_status ( 	p_message_id					IN msg_hub_message_status.message_id%TYPE,
									p_user							IN msg_hub_message_status.created_by%TYPE,
									p_effective_start_timestamp 	IN msg_hub_message_status.effective_start_timestamp%TYPE,
									p_message_source				IN msg_hub_message_status.message_source%TYPE,
									p_message_source_id 			IN msg_hub_message_status.message_source_id%TYPE,
									p_status  						IN msg_hub_message_status.status%TYPE
									)
IS





lv_logical_load_timestamp    msg_hub_message_status.logical_load_timestamp%TYPE;

ltab_old_message_status     msg_hub_message_status%ROWTYPE;
ltab_new_message_status     msg_hub_message_status%ROWTYPE;


BEGIN
  logger.logger.set_module('put_on_message_status');
   lv_logical_load_timestamp := SYSTIMESTAMP;



--
-- Get the old record and check with the new record for any changes
--

	BEGIN
		SELECT old_version.message_id
			,old_version.logical_load_timestamp
			,old_version.created_by
			,old_version.create_timestamp
			,old_version.updated_by
			,old_version.update_timestamp
			,old_version.effective_start_timestamp
			,old_version.message_source
			,old_version.message_source_id
			,old_version.status
	   INTO ltab_old_message_status
	  FROM ( SELECT 	p_message_id			message_id,
						p_message_source		message_source,
						p_message_source_id		message_source_id,
						p_status				status
			 FROM dual  ) new_version,
		   msg_hub_message_status old_version
	  WHERE new_version.message_id=old_version.message_id
		AND (nrg_common.has_value_changed(new_version.message_source, old_version.message_source) 		= 1 OR
			nrg_common.has_value_changed(new_version.message_source_id, old_version.message_source_id) 	= 1 OR
			nrg_common.has_value_changed(new_version.status, old_version.status) 						= 1 )
		FOR UPDATE OF old_version.message_id;

--DR Get new record and compare with old record
       		SELECT new_version.message_id
			,new_version.logical_load_timestamp
			,new_version.created_by
			,new_version.create_timestamp
			,new_version.updated_by
			,new_version.update_timestamp
			,new_version.effective_start_timestamp
			,new_version.message_source
			,new_version.message_source_id
			,new_version.status
	   INTO ltab_new_message_status
	  FROM ( SELECT 	p_message_id			message_id,
                        lv_logical_load_timestamp logical_load_timestamp,
                        p_user                  created_by,
                        lv_logical_load_timestamp create_timestamp,
                        p_user                  updated_by,
						lv_logical_load_timestamp update_timestamp,
                        p_effective_start_timestamp effective_start_timestamp,
                        p_message_source		message_source,
						p_message_source_id		message_source_id,
						p_status				status
			 FROM dual  ) new_version ,
             msg_hub_message_status old_version
	        WHERE new_version.message_id=old_version.message_id;

  --
  -- IF new record effective start timestamp is greater then the excisting record then put old record in history table
  --
             if ltab_new_message_status.effective_start_timestamp > ltab_old_message_status.effective_start_timestamp then
        		put_history(p_tab_old_message_status        => ltab_old_message_status,
					p_effective_end_timestamp      => p_effective_start_timestamp,
					p_action                       => 'U');
  --
  -- IF new record effective start timestamp is less then the excisting record then put old record in history table
  --
             elsif ltab_new_message_status.effective_start_timestamp < ltab_old_message_status.effective_start_timestamp then
             	put_history(p_tab_old_message_status        => ltab_new_message_status,
					p_effective_end_timestamp      => p_effective_start_timestamp,
					p_action                       => 'U');
             end if;

 --DR

	   EXCEPTION
		 WHEN NO_DATA_FOUND  THEN
			NULL;
  END;

--
-- Insert/Update the new data
-- Only update the old record if new records effectice timestamp is greater then old record.
--
 MERGE  INTO  msg_hub_message_status old_version
  USING ( SELECT 	 	p_message_id			message_id,
						p_message_source		message_source,
						p_message_source_id		message_source_id,
                        p_effective_start_timestamp effective_start_timestamp,
						p_status				status
		 FROM dual ) new_version
  ON (old_version.message_id=new_version.message_id)
  WHEN MATCHED THEN
  UPDATE
  SET   logical_load_timestamp 			= lv_logical_load_timestamp
	   ,updated_by 						= p_user
	   ,update_timestamp 				= SYSTIMESTAMP
	   ,effective_start_timestamp 		= p_effective_start_timestamp
	   ,message_source               	= new_version.message_source
	   ,message_source_id               = new_version.message_source_id
	   ,status          				= new_version.status
   WHERE new_version.message_id=old_version.message_id
   AND (nrg_common.has_value_changed(new_version.message_source, old_version.message_source) 			= 1 OR
		nrg_common.has_value_changed(new_version.message_source_id, old_version.message_source_id) 		= 1 OR
		nrg_common.has_value_changed(new_version.status, old_version.status) 							= 1 )
    AND new_version.effective_start_timestamp > old_version.effective_start_timestamp --DR
  WHEN NOT MATCHED THEN
   INSERT   (message_id,
			logical_load_timestamp,
			created_by,
			create_timestamp,
			updated_by,
			update_timestamp,
			effective_start_timestamp,
			message_source,
            message_source_id,
			status
			)
	VALUES (new_version.message_id
			,lv_logical_load_timestamp
			,p_user
			,systimestamp
			,p_user
			,systimestamp
			,p_effective_start_timestamp
			,new_version.message_source
			,new_version.message_source_id
			,new_version.status
		);


logger.logger.set_module(NULL);


EXCEPTION
WHEN DUP_VAL_ON_INDEX THEN
    RETURN;
WHEN OTHERS THEN
logger.logger.severe(logger.logger.error_backtrace);
logger.logger.set_module(NULL);
raise_application_error(-20004, logger.logger.error_backtrace);

END put_on_message_status;





-- ===================================================================================
-- put_message_display_event


-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--     Procedure to put put_message_display_event

--
-- Notes:
-- ------
--
--     Tables potentially populated:
--
--     MSG_HUB_MESSAGE_DISPLAY_EVENT
--
--
-- Parameters:
-- -----------
--
--     Parameter                        Description
--   ------------------------------   ----------------------------------------------
--		p_user
--   	p_effective_start_timestamp
--   	p_message_id
--   	p_creation_time
--   	p_last_modified_time
--   	p_creator
--   	p_last_modifier
--   	p_message_source
--		p_message_source_id
--		p_status

--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--     -------   ---------------------------------------------------------------------
--
--
-- -----------------------------------------------------------------------------------
PROCEDURE put_message_display_event ( 	p_message_id					IN msg_hub_message_display_event.message_id%TYPE,
										p_user							IN msg_hub_message_display_event.created_by%TYPE,
										p_effective_start_timestamp 	IN msg_hub_message_display_event.effective_start_timestamp%TYPE,
										p_message_source				IN msg_hub_message_display_event.message_source%TYPE,
										p_message_source_id 			IN msg_hub_message_display_event.message_source_id%TYPE,
										p_event_time					IN msg_hub_message_display_event.event_time%TYPE,
										p_display_event					IN msg_hub_message_display_event.display_event%TYPE,
										p_display_event_detail			IN msg_hub_message_display_event.display_event_detail%TYPE,
										p_device_fingerprint			IN msg_hub_message_display_event.device_fingerprint%TYPE,
										p_app_version					IN msg_hub_message_display_event.app_version%TYPE,
										p_person_id						IN msg_hub_message_display_event.person_id%TYPE,
										p_trading_account_id			IN msg_hub_message_display_event.trading_account_id%TYPE
										)
IS


lv_logical_load_timestamp    msg_hub_message_display_event.logical_load_timestamp%TYPE;

ltab_old_message_display_event     msg_hub_message_display_event%ROWTYPE;


BEGIN
  logger.logger.set_module('put_message_display_event');
   lv_logical_load_timestamp := SYSTIMESTAMP;



   INSERT  INTO msg_hub_message_display_event (message_id,
			logical_load_timestamp,
			created_by,
			create_timestamp,
			updated_by,
			update_timestamp,
			effective_start_timestamp,
			message_source,
            message_source_id,
			event_time,
			display_event,
			display_event_detail,
			device_fingerprint,
			app_version,
			person_id,
			trading_account_id,
			business_date,
			reporting_date
			)
	VALUES (p_message_id
			,lv_logical_load_timestamp
			,p_user
			,systimestamp
			,p_user
			,systimestamp
			,p_effective_start_timestamp
			,p_message_source
			,p_message_source_id
			,p_event_time
			,p_display_event
			,p_display_event_detail
			,p_device_fingerprint
			,p_app_version
			,p_person_id
			,p_trading_account_id
			,nrg_common.get_business_date(p_event_time)
			,nrg_common.get_reporting_date(p_event_time)
		);


logger.logger.set_module(NULL);


EXCEPTION
WHEN DUP_VAL_ON_INDEX THEN
    RETURN;
WHEN OTHERS THEN
logger.logger.severe(logger.logger.error_backtrace);
logger.logger.set_module(NULL);
raise_application_error(-20004, logger.logger.error_backtrace);

END put_message_display_event;



END ;
//