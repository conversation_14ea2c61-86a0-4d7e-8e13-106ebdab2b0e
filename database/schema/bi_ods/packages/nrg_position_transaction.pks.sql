
  CREATE OR REPLACE EDITIONABLE PACKAGE "BI_ODS"."NRG_POSITION_TRANSACTION" 
AS
    -- ===================================================================================
    -- NRG_POSITION_TRANSACTION
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the Position Transactions model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Position Transaction Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     23/05/2012   Mark Gornicki      1.0    Creation
    --     03/07/2012   Sanket Mittal      1.1    Added the parameter p_record_source
    --     18/03/2013   Sanket Mittal      1.4    Updated for P2 Changes
    --     18/07/2014   Adam Krasnicki     1.5    p_load_latest_positions parameter added to put_position_transaction
    --     08/05/2015   Adam Krasnicki     1.6    put_position_transaction: 3 new params: p_execution_type, p_opening_trade_instrmnt_price, p_opening_trade_strike_price
    --     01/10/2015   Sanket Mittal      2.2    added new parameters p_trading_scope and p_alloc_trading_risk_schema on put_position_transaction
    --     20/01/2016   Sanket Mittal      2.3    Added new parameters for binaries:bo_type, bo_settle_time, bo_tenor, bo_strike_price, bo_strike_price_additional, bo_tenor_start_time
    --     09/05/2016   Adam Krasnicki     2.4    put_position_transactions procedure added (it takes an array of the position transactions)
    --     08/06/2016   Sanket Mittal      2.5    put_position_transactions added parameters BER-2585 SWS 34 - Data Contract Changes - PositionTransaction
    --     01/09/2016   Sanket Mittal      2.7    BER-2863 KNOCKOUT Normalisation Changes
    --     05/04/2018   Deepak Rajurkar    2.9    BER-4441 Add Column FORCED_MARGIN_FX_RATE to table BI_ODS.POSITION_TRANSACTIONS
    --     04/09/2020   Patrick Dinwiddy   3.2    JCS-13251
    --     08/03/2021   Patrick Dinwiddy   3.4    JCS-14418
    --     15/03/2023   Adam Davies        3.6    JCS-16644
    --     22/01/2024   Jie Zou            3.7    CORES-741
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

  -- ===================================================================================
  -- put_position_transactions
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a position transactions
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --   ------------------------------   ----------------------------------------------
  --
  -- Return:
  -- -------
  --
  --
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --   -------   ---------------------------------------------------------------------
  --     -20000    Error retrieving version : %error_backtrace
  --
  -- -----------------------------------------------------------------------------------

 PROCEDURE put_position_transactions(p_position_transactions IN Position_Transactions_tab);

    -- ===================================================================================
    -- put_position_transaction
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a position transaction
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     POSITION_TRANSACTIONS
  --     POSITION_TRANSACTIONS_H
  --     PSTN_TRANSACTION_TYPES
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                          Description
    --     ------------------------------     ----------------------------------------------
    --     p_user                             User
    --     p_effective_start_timestamp        Effective start timestamp
    --     p_transaction_id                   Transaction Id
    --     p_open_trade_id                    Open Trade Id
    --     p_trade_id                         Trade Id
    --     p_transaction_booking_number       Transaction Booking Number
    --     p_platform                         Platform
    --     p_publish_time                     Publish Time
    --     p_event_time                       Event Time
    --     p_version_number                   Version Number
    --     p_is_deleted                       Is Deleted
    --     p_creation_time                    Creation Time
    --     p_creation_identity_token          Creation Identity Token
    --     p_crtn_on_bhlf_of_idntty_tkn       Crtn On Bhlf Of Idntty Tkn
    --     p_update_time                      Update Time
    --     p_update_identity_token            Update Identity Token
    --     p_updt_on_bhlf_of_idntty_tkn       Updt On Bhlf Of Idntty Tkn
    --     p_session_key                      Session Key
    --     p_trading_account_id               Trading Account Id
    --     p_trading_account_type             Trading Account Type
    --     p_trading_account_function         Trading Account Function
    --     p_trading_account_codifier         Trading Account Codifier
    --     p_trdng_accnt_primary_currency     Trading Accnt Primary Currency
    --     p_order_id                         Order Id
    --     p_product_instrument_code          Product Instrument Code
    --     p_product_wrapper_code             Product Wrapper Code
    --     p_mm_instrument_id                 Mm Instrument Id
    --     p_product_schema_code              Product Schema Code
    --     p_product_generation               Product Generation
    --     p_product_point_multiplier         Product Point Multiplier
    --     p_product_currency                 Product Currency
    --     p_product_financing_ratio_max      Product Financing Ratio Max
    --     p_product_frctnl_part_ratio        Product Fractional Part Ratio
    --     p_iscurrencyinfractionalparts      Iscurrencyinfractionalparts
    --     p_channel_id                       Channel Id
    --     p_request_id                       Request Id
    --     p_business_date                    Business Date
    --     p_reporting_date                   Reporting Date
    --     p_cstm_info_vrtl_prtfl_cd          Cstm Info Vrtl Prtfl Cd
    --     p_transaction_time                 Transaction Time
    --     p_transaction_type                 Transaction Type
    --     p_transaction_refcodifier          Transaction Refcodifier
    --     p_transaction_refcode              Transaction Refcode
    --     p_is_automatically_rolled          Is Automatically Rolled
    --     p_position_direction               Position Direction
    --     p_position_financing_ratio         Position Financing Ratio
    --     p_position_quantity                Position Quantity
    --     p_position_quantity_currency       Position Quantity Currency
    --     p_position_quantity_designator     Position Quantity Designator
    --     p_position_margin                  Position Margin
    --     p_position_margin_currency         Position Margin Currency
    --     p_position_amount                  Position Amount
    --     p_position_amount_currency         Position Amount Currency
    --     p_position_margin_in_tapc          Position Margin In Tapc
    --     p_position_amount_in_tapc          Position Amount In Tapc
    --     p_open_trade_price                 Open Trade Price
    --     p_open_trade_margin_fx_rate        Open Trade Margin Fx Rate
    --     p_open_trade_quantity_fx_rate      Open Trade Quantity Fx Rate
    --     p_open_trade_time                  Open Trade Time
    --     p_prev_position_quantity           Prev Position Quantity
    --     p_prev_position_margin             Prev Position Margin
    --     p_prev_position_amount             Prev Position Amount
    --     p_prev_position_margin_in_tapc     Prev Position Margin In Tapc
    --     p_prev_position_amount_in_tapc     Prev Position Amount In Tapc
    --     p_prev_pstn_financing_ratio        Prev Position Financing Ratio
    --     p_prev_prdct_point_multiplier      Prev Product Point Multiplier
    --     p_record_source                    Added the parameter p_record_source
    --     p_trading_scope                    Trading Scope
    --     p_alloc_trading_risk_schema        Allocation Trading Risk Schema
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Position Transaction Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_position_transaction (p_user                             IN position_transactions.created_by%TYPE,
                                        p_effective_start_timestamp        IN position_transactions.effective_start_timestamp%TYPE,
                                        p_transaction_id                   IN position_transactions.transaction_id%TYPE,
                                        p_open_trade_id                    IN position_transactions.open_trade_id%TYPE,
                                        p_trade_id                         IN position_transactions.trade_id%TYPE,
                                        p_transaction_booking_number       IN position_transactions.booking_number%TYPE,
                                        p_platform                         IN position_transactions.platform%TYPE,
                                        p_publish_time                     IN position_transactions.publish_time%TYPE,
                                        p_event_time                       IN position_transactions.event_time%TYPE,
                                        p_version_number                   IN position_transactions.version_number%TYPE,
                                        p_is_deleted                       IN position_transactions.is_deleted%TYPE,
                                        p_creation_time                    IN position_transactions.creation_time%TYPE,
                                        p_creation_identity_token          IN position_transactions.creation_identity_token%TYPE,
                                        p_crtn_on_bhlf_of_idntty_tkn       IN position_transactions.crtn_on_bhlf_of_idntty_tkn%TYPE,
                                        p_update_time                      IN position_transactions.update_time%TYPE,
                                        p_update_identity_token            IN position_transactions.update_identity_token%TYPE,
                                        p_updt_on_bhlf_of_idntty_tkn       IN position_transactions.updt_on_bhlf_of_idntty_tkn%TYPE,
                                        p_session_key                      IN position_transactions.session_key%TYPE,
                                        p_trading_account_id               IN position_transactions.trading_account_id%TYPE,
                                        p_trading_account_type             IN position_transactions.trading_account_type%TYPE,
                                        p_trading_account_function         IN position_transactions.trading_account_function%TYPE,
                                        p_trading_account_codifier         IN position_transactions.trading_account_codifier%TYPE,
                                        p_trdng_accnt_primary_currency     IN position_transactions.trdng_accnt_primary_currency%TYPE,
                                        p_order_id                         IN position_transactions.order_id%TYPE,
                                        p_product_instrument_code          IN position_transactions.product_instrument_code%TYPE,
                                        p_product_wrapper_code             IN position_transactions.product_wrapper_code%TYPE,
                                        p_product_generation               IN position_transactions.product_generation%TYPE,
                                        p_product_point_multiplier         IN position_transactions.product_point_multiplier%TYPE,
                                        p_product_currency                 IN position_transactions.product_currency%TYPE,
                                        p_product_frctnl_part_ratio        IN position_transactions.product_frctnl_part_ratio%TYPE,
                                        p_iscurrencyinfractionalparts      IN position_transactions.is_ccy_in_fractional_parts%TYPE,
                                        p_channel_id                       IN position_transactions.channel_id%TYPE,
                                        p_request_id                       IN position_transactions.request_id%TYPE,
                                        p_transaction_time                 IN position_transactions.transaction_time%TYPE,
                                        p_transaction_type                 IN position_transactions.transaction_type%TYPE,
                                        p_transaction_refcodifier          IN position_transactions.transaction_refcodifier%TYPE,
                                        p_transaction_refcode              IN position_transactions.transaction_refcode%TYPE,
                                        p_is_automatically_rolled          IN position_transactions.is_automatically_rolled%TYPE,
                                        p_position_direction               IN position_transactions.position_direction%TYPE,
                                        p_open_trade_quantity              IN position_transactions.open_trade_quantity%TYPE,
                                        p_quantity_designator              IN position_transactions.quantity_designator%TYPE,
                                        p_open_trade_amount                IN position_transactions.open_trade_amount%TYPE,
                                        p_open_trade_amount_currency       IN position_transactions.open_trade_amount_currency%TYPE,
                                        p_open_trade_amount_in_tapc          IN position_transactions.open_trade_amount_in_tapc%TYPE,
                                        p_open_trade_price                 IN position_transactions.open_trade_price%TYPE,
                                        p_opening_trade_amount_fx_rate      IN position_transactions.opening_trade_amount_fx_rate%TYPE,
                                        p_open_trade_time                  IN position_transactions.open_trade_time%TYPE,
                                        p_record_source                    IN position_transactions.record_source%TYPE,
                                        p_rolled_open_trade_id             IN position_transactions.rolled_open_trade_id%TYPE,
                                        p_opening_trade_app_to_units       IN position_transactions.opening_trade_app_to_units%TYPE,
                                        p_load_latest_positions            IN VARCHAR2 DEFAULT 'NO',
                                        p_execution_type                   IN position_transactions.execution_type%TYPE ,
                                        p_opening_trade_instrmnt_price     IN position_transactions.opening_trade_instrument_price%TYPE,
                                        p_trading_scope                    IN position_transactions.trading_scope%TYPE,
                                        p_binary_type                      IN position_transactions.binary_type%TYPE,
                                        p_settle_time                      IN position_transactions.settle_time%TYPE,
                                        p_tenor                            IN position_transactions.tenor%TYPE,
                                        p_strike_price                     IN position_transactions.strike_price%TYPE,
                                        p_strike_price_additional          IN position_transactions.strike_price_additional%TYPE,
                                        p_tenor_start_time                 IN position_transactions.tenor_start_time%TYPE,
                                        p_binary_result                    IN position_transactions.binary_result%TYPE,
                                        p_alloc_instrument_schema          IN position_transactions.alloc_instrument_schema%TYPE,
                                        p_open_trade_instrmnt_amount       IN position_transactions.open_trade_instrmnt_amount%TYPE,
                                        p_forced_margin_fx_rate            IN position_transactions.forced_margin_fx_rate%TYPE,
                                        p_opn_accrd_trnvr_in_accnt_ccy     IN position_transactions.opn_accrd_trnvr_in_accnt_ccy%TYPE,
                                        p_is_pair_ccy_in_frctnl_prts       IN position_transactions.is_pair_ccy_in_frctnl_prts%TYPE,
                                        p_value_date                       IN position_transactions.value_date%TYPE,
                                        p_pair_currency                    IN position_transactions.pair_currency%TYPE,
                                        p_primary_currency                 IN position_transactions.primary_currency%TYPE,
                                        p_secondary_currency               IN position_transactions.secondary_currency%TYPE,
                                        p_primary_amount                   IN position_transactions.primary_amount%TYPE,
                                        p_secondary_amount                 IN position_transactions.secondary_amount%TYPE,
                                        p_open_trade_margin_percentage     IN position_transactions.open_trade_margin_percentage%TYPE,
                                        p_accrued_fees_in_instrument_currency IN position_transactions.accrued_fees_in_instrument_currency%TYPE,
                                        p_accrued_fees_in_primary_ccy      IN position_transactions.accrued_fees_in_primary_ccy%TYPE,
                                        p_opn_trd_instr_prc_in_prmry_ccy   IN position_transactions.opn_trd_instr_prc_in_prmry_ccy%TYPE,
                                        p_prtfl_swap_period_seq_number     IN position_transactions.prtfl_swap_period_seq_number%TYPE,
                                        p_option_code                      IN position_transactions.option_code%TYPE,
                                        p_transaction_client_costs         IN position_transactions.transaction_client_costs%TYPE,
                                        p_open_accrued_holding_costs       IN position_transactions.open_accrued_holding_costs%TYPE,
                                        p_original_order_id                IN position_transactions.original_order_id%TYPE,
                                        p_strategy_id                      IN position_transactions.strategy_id%TYPE,
                                        p_strategy_type                    IN position_transactions.strategy_type%TYPE
                                      );

    -- ===================================================================================
    -- get_position_transaction_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the position transaction ids
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --      p_creation_time_from            Creation Time From
    --      p_creation_time_to              Creation Time To
    --
    -- Return:
    -- -------
    --
    --     Returns a list of position transaction ids
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000   Default Exception
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION get_position_transaction_ids (p_creation_time_from IN position_transactions.creation_time%TYPE,
                                           p_creation_time_to   IN position_transactions.creation_time%TYPE) RETURN SYS_REFCURSOR;

END nrg_position_transaction;

//