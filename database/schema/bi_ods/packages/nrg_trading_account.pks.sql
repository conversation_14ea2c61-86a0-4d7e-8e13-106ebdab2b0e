CREATE OR REPLACE EDITIONABLE PACKAGE "BI_ODS"."NRG_TRADING_ACCOUNT" 
AS
    -- ===================================================================================
    -- NRG_TRADING_ACCOUNT
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the trading account model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --     -20002    Operation Not Handled. Please Check the Case Condition
    --     -20003    Trading Account Deleted Before Update and After Insert
    --     -20004    Default Exception
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     25/08/2011   Richard Sadd        1.0   Creation
    --     31/08/2011   Sanket Mittal       1.1   Included the POA to trading account
    --     05/09/2011   Sanket Mittal       1.2   Created new procedure create_trading_account_stub
    --                                            Changed the put_trading Account method to include
    --                                            update for history table in case of message replays
    --     10/10/2011   Sanket Mittal       1.4   Updated to enable concurrent transactions
    --     04/11/2011   Mark Gornicki       1.5   Implemented changes identified in QA review done on
    --                                            the 02/11/11 and 03/11/11 by Mark Gornicki
    --                                            These encompass :
    --                                            - Overloading the create_trading_account_stub to cope with a table of account ids
    --                                            - Change power_of_attorney to send a table of person_ids when creating stub records
    --                                            - Consistency within and across packages
    --                                            - Fixing bugs such as history tables being inserted with
    --                                              the incoming data rather than the original record
    --                                            - Fix history bug where the update is being applied to all history
    --                                              rows for a given id instead of a specified effective_start_timestamp
    --                                            - Removal of triggers from the database and ensuring that
    --                                              the package does handle the corresponding functionality
    --                                            - Added the function trading_account_ids for the Java team reconciliation process
    --     08/11/2011   Mark Gornicki       1.6   Added standardised exception handlers
    --     09/11/2011   Sanket Mittal       1.7   Renamed the function trading_account_ids to get_trading_account_ids
    --     25/11/2011   Sanket Mittal       1.8   Added new filed to put_trading_account - trading_account_type
    --     28/11/2011   Mark Gornicki       2.0   Amended the create stub process to set account_function to unknown
    --                                            for the partitioning. Row movement is enabled for when it gets updated.
    --                                            Also changed the inserts/updates to NVL account function to UNKNOWN.
    --     31/01/2012   Mark Gornicki       2.3   Added the function get_stubbed_ids
    --     20/03/2013   Sanket Mittal       2.7   Updated the trading account spec for P2 changes
    --     05/08/2013   Prachi Shah         2.9   Included account customer change reasons for trading account
    --     25/09/2013   Ravi Shankar Gopal  3.0   Included logic to include newly added columns in the Trading_accounts table
    --     11/10/2013   Ravi Shankar Gopal  3.1   Removed Reference to BI_ODS.ACCNT_RISK_CLASSIFICATIONS and BI_ODS.ACCNT_RISK_CLASSIFICATIONS_H Tables
    --     16/10/2013   Ravi Shankar Gopal  3.1   Removed Reference to Absolute liquidation levels and added logic to consume newly added column Liquidation_type
    --     29/11/2013   Adam Krasnicki      3.3   liquidation_reset_level column added to trading_accounts(_h) table
    --     16/12/2013   Adam Krasnicki      3.4   Hedge trading account added
    --     18/03/2014   Adam Krasnicki      3.5   Put_trading_account accepts p_partner_trading_account_obj instead of partner_id variable
    --     11/09/2014   Adam Krasnicki      4.1   New parameters to put_trading_account
    --     06/01/2014   Adam Krasnicki      4.2   Blocked_cash column added and new paraemeter to put_trading_account procedure
    --     28/01/2014   Adam Krasnicki      4.3   New parameter to put_trading_account (p_guaran_stop_loss_or_style)
    --     27/07/2015   Adam Krasnicki      4.3   put_trading_accounts: new tab attribute added: hedge_account_asset_class_tab
    --     25/08/2015   Adam Krasnicki      4.4   New parameters in put procedure
    --     22/09/2015   Sanket Mittal       4.5   Renamed the column COUNTRY_DODE to COUNTRY_CODE for HEDGE_ACCOUNT_ADDRESS and HEDGE_ACCOUNT_ADDRESS_H
    --     22/09/2015   Sanket Mittal       4.6   Updated put_trading_account for BER-2028 new Allocation Account (Profit Centre)
    --     26/10/2015   Sanket Mittal       4.7   Updated for BER-2122 set is_internal_account  = NO for account type 'Allocation'
    --     23/12/2015   Sanket Mittal       4.8   Updated for BER-2207 binary changes
    --     03/03/2016   Sanket Mittal       4.9   Updated for BER-2388 Adding new column Record Source
    --     17/06/2016   Sanket Mittal       5.0   Updated for BER-2644-Integrate updated TradingAccount data contract Knockouts Added additional columns
    --     22/08/2016   Sanket Mittal       5.1   BER-2836 Integrate updated TradingAccount data contract
    --     29/11/2016   Sanket Mittal       5.2   BER-3067 ODS - Integrate updated Trading account data contract
    --     23/01/2017   Sanket Mittal       5.3   BER-3306 ODS - TRADING ACCOUNTS - Integrate updated trading account data contract
    --     18/04/2017   Sanket Mittal       5.6   BER-3531 NRG_TRADING_ACCOUNT - Data contract changes
    --     18/04/2017   Sanket Mittal       5.7   BER-3549 NRG_TRADING_ACCOUNT.get_trading_account_ids - include only 'CM'
    --     01/06/2017   Sanket Mittal       5.8   BER-3671 NRG_TRADING_ACCOUNT changes - new attributes
	--     22/05/2018   S Kinkhabwala       5.9   BER-4443 NRG_TRADING_ACCOUNT changes - new attributes
    --     19/03/2019   Patrick Dinwiddy    6.6   BER-5010 make put_customer_ids public
	--     24/06/2019   Patrick Dinwiddy    6.9   JCS-10915 add attribute is_consider_for_german_cgt
    --     22/04/2020   Patrick Dinwiddy    7.1   JCS-12706 and JCS-12642
    --     22/09/2020   Patrick Dinwiddy    7.2   JCS-13787
    --     04/02/2020   Patrick Dinwiddy    7.3   JCS-14346
    --     21/02/2022   Patrick Dinwiddy    7.5   JCS-15108,14834
    --     05/07/2023   Simon Cheung        7.6   JCS-16944
    --     19/07/2023   Zakaria Alam        7.7   JCS-17349
    --     08/01/2024   Jie Zou             7.8   CORES-734

    -- ===================================================================================
    --    TYPE DECLARATION
    -- ===================================================================================
--        TYPE hedge_account_security_class_tab IS TABLE OF hedge_account_security_class%ROWTYPE;
--        TYPE hedge_account_security_prdct_tab IS TABLE OF hedge_account_security_prdct%ROWTYPE;

    -- ===================================================================================

    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

    -- ===================================================================================
    -- create_trading_account_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a trading account stub in case the trading_account is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     TRADING_ACCOUNTS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical load timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_trading_account_id             Trading Account ID
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_trading_account_stub (p_user                       IN trading_accounts.created_by%TYPE,
                                           p_logical_load_timestamp     IN trading_accounts.logical_load_timestamp%TYPE,
                                           p_effective_start_timestamp  IN trading_accounts.effective_start_timestamp%TYPE,
                                           p_trading_account_id         IN trading_accounts.trading_account_id%TYPE,
                                           p_trading_account_type       IN trading_accounts.trading_account_type%TYPE);


    -- ===================================================================================
    -- create_trading_account_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a trading account stub in case the trading_account is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     TRADING_ACCOUNTS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical load timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_trading_account_id             Trading Account ID table
    --     p_trading_account_type           Trading Account Type CUSTOMER/INTERNAL
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_trading_account_stub (p_user                       IN trading_accounts.created_by%TYPE,
                                           p_logical_load_timestamp     IN trading_accounts.logical_load_timestamp%TYPE,
                                           p_effective_start_timestamp  IN trading_accounts.effective_start_timestamp%TYPE,
                                           p_trading_account_id         IN trading_account_id_tab);

  -- ===================================================================================
  -- put_trading_account
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a trading account
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TRADING_ACCOUNTS
  --     ACCNT_PRDCT_WRPPR_STTNGS
  --     ACCNT_RISK_CLASSIFICATIONS
  --     TRDNG_ACCNTS_CUSTOMERS_LINK
  --     PARTNERS
  --     CUSTOMERS
  --     POWER_OF_ATTORNEYS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trading_account_id             Trading account id
  --     p_partner_trading_account_obj    This record contains Partner id only this field should be consumed (change in sprint 37b)
  --     p_account_version                Account version
  --     p_source_platform                Source platform
  --     p_source_account_id              Source account id
  --     p_account_name                   Account name
  --     p_account_type                   Account type
  --     p_account_function               Account function
  --     p_open_date                      Open date
  --     p_closure_date                   Closure date
  --     p_unauthorised_to_trade_date     Unauthorised to trade date
  --     p_authorised_to_trade            Authorised to trade
  --     p_is_managed                     Is managed
  --     p_is_locked                      Is locked
  --     p_is_closed                      Is closed
  --     p_allow_shortening               Allow shortening
  --     p_allow_financing                Allow financing
  --     p_allow_position_increase        Allow position increase
  --     p_allow_trans_margin_model       Allow transaction based margin model
  --     p_is_internal_test_account       Is internal test account
  --     p_currency                       Currency
  --     p_profit_centre                  Profit centre
  --     p_profile_description            Profile description
  --     p_power_of_attorneys             Table of Power of attorney
  --     p_terms_and_conditions           Terms and conditions
  --     p_tenant_template_code           Tenant template code
  --     p_is_dormant                     Is dormant
  --     p_dormant_date                   Dormancy date
  --     p_last_marked_non_dormant_date   Last marked non dormant date
  --     p_unauthorised_to_trade_reason   Unauthorised to trade reason
  --     p_unauth_to_trade_other_detail   Unauthorised to trade other details
  --     p_closure_reason                 Closure reason
  --     p_closure_other_details          Closure other details
  --     p_cash_accounting_schema_code    Cash accounting schema code
  --     p_product_schema_code            Product schema code
  --     p_trading_risk_schema_code       Trading risk schema code
  --     p_payment_schema_code            Payment schema code
  --     p_crm_schema_code                Crm schema code
  --     p_locale_id                      Locale id
  --     p_locale_version                 Locale version
  --     p_locale_language                Locale language
  --     p_locale_territory               Locale territory
  --     p_locale_timezone                Locale timezone
  --     p_payment_profile_id             Payment profile id
  --     p_pay_prof_block_all_payments    Payment profile block all payments
  --     p_pay_prof_block_withdrawals     Payment profile block withdrawals
  --     p_payment_profile_warn_fct       Payment profile warn fct
  --     p_pay_prf_blck_withd_prcng_spk   Payment profile block withdrawals pricing spike
  --     p_pay_prf_blck_withd_reason      Payment profile block withdrawals reason
  --     p_pay_prf_blck_all_pay_reason    Payment profile block all payment reason
  --     p_payment_prof_warn_fct_reason   Payment profile warn fct reason
  --     p_product_wrapper_settings       Product wrapper settings
  --     p_trad_risk_classifications      Trading risk classifications
  --     p_customer_ids                   Linked Customer ids
  --     p_is_deleted                     If the account has been deleted from source this flag will be set
  --     p_trading_account_type           Type of trading account
  /***** Begin Modification for V3.0 - BER633 *****/
  --     p_tax_schema_code                Tax Schema Code
  --     p_commission_schema_code         Commission Schema Code
  --     p_carrying_costs_schema_code     Carrying Costs Schema Code
  --     p_crryng_csts_offst_schm_cd      Carrying Costs Offset Schema Code
  --     p_price_schema_code              Price Schema Code
  /***** End Modification for V3.0 *****/
  /***** Begin Modification for V3.1 - BER646 *****/
  --     p_liquidation_reset_level        Liquidation reset level
  /***** End Modification for V3.1 - BER646 *****/
  --     p_hedge_closeout_schema_code
  --     p_hedge_broker_code
  --     p_hedge_broker_account_number
  --     p_hedge_execution_broker
  --     p_hedge_clearing_broker
  --     p_is_hedge_tradable
  --     p_hedge_calculate_commission
  --     p_hedge_firm_reference_number
  --     p_hedge_legal_entity_identifie
  --     p_hedge_bank_identifier_code
  --     p_hedge_corporate_sector
  --     p_hedge_financial_counterparty
  --     p_hedge_account_address_tab
  --     p_hedge_account_contact_tab
  --     p_utt_reason_tab
  --     p_order_verification_time
  --     p_introducer_reference
  --     p_record_source
  --     p_is_mt4_vps
  --     p_corporate_sector_type
  --     p_related_hedge_account_id
  --     p_relational_role
  --     p_trading_guard_amount
  --     p_interest_schema_code
  --     p_first_trade_date
  --     p_is_spreadbet
  --     p_hip_classification_risk_bucket
  --     p_is_corporate_brokerage

  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --     -20002    Operation Not Handled. Please Check the Case Condition
  --     -20003    Trading Account Deleted Before Update and After Insert
  --     -20004    Default Exception
  --
  -- -----------------------------------------------------------------------------------

PROCEDURE put_trading_account   (p_user                           IN trading_accounts.created_by%TYPE,
                                   p_effective_start_timestamp       IN trading_accounts.effective_start_timestamp%TYPE,
                                   p_trading_account_id              IN trading_accounts.trading_account_id%TYPE,
                                   p_partner_trading_account_obj     IN partner_trading_account_obj,
                                   p_account_version                 IN trading_accounts.account_version%TYPE,
                                   p_source_platform                 IN trading_accounts.source_platform%TYPE,
                                   p_source_account_id               IN trading_accounts.source_account_id%TYPE,
                                   p_account_name                    IN trading_accounts.account_name%TYPE,
                                   p_account_type                    IN trading_accounts.account_type%TYPE,
                                   p_account_function                IN trading_accounts.account_function%TYPE,
                                   p_open_date                       IN trading_accounts.open_date%TYPE,
                                   p_closure_date                    IN trading_accounts.closure_date%TYPE,
                                   p_unauthorised_to_trade_date      IN trading_accounts.unathrsd_to_trade_date%TYPE,
                                   p_authorised_to_trade             IN trading_accounts.authorised_to_trade%TYPE,
                                   p_is_managed                      IN trading_accounts.is_managed%TYPE,
                                   p_is_locked                       IN trading_accounts.is_locked%TYPE,
                                   p_is_closed                       IN trading_accounts.is_closed%TYPE,
                                   p_allow_shortening                IN trading_accounts.allow_shortening%TYPE,
                                   p_allow_financing                 IN trading_accounts.allow_financing%TYPE,
                                   p_allow_position_increase         IN trading_accounts.allow_position_increase%TYPE,
                                   p_allow_trans_margin_model        IN trading_accounts.allow_trnsctn_bsd_mrgn_mdl%TYPE,
                                   p_is_internal                     IN trading_accounts.is_internal%TYPE,
                                   p_currency                        IN trading_accounts.currency%TYPE,
                                   p_profit_centre                   IN trading_accounts.profit_centre%TYPE,
                                   p_profile_description             IN trading_accounts.profile_description%TYPE,
                                   p_power_of_attorneys              IN power_of_attorney_tab,
                                   p_terms_and_conditions            IN trading_accounts.terms_and_conditions%TYPE,
                                   p_tenant_template_code            IN trading_accounts.tenant_template_code%TYPE,
                                   p_is_dormant                      IN trading_accounts.is_dormant%TYPE,
                                   p_dormant_date                    IN trading_accounts.dormant_date%TYPE,
                                   p_last_marked_non_dormant_date    IN trading_accounts.last_marked_non_dormant_date%TYPE,
                                   p_unauthorised_to_trade_reason    IN trading_accounts.unathrsd_to_trade_reason%TYPE,
                                   p_unauth_to_trade_other_detail    IN trading_accounts.unathrsd_to_trade_othr_dtls%TYPE,
                                   p_closure_reason                  IN trading_accounts.closure_reason%TYPE,
                                   p_closure_other_details           IN trading_accounts.closure_other_details%TYPE,
                                   p_cash_accounting_schema_code     IN trading_accounts.ca_schm_cd%TYPE,
                                   p_product_schema_code             IN trading_accounts.product_schm_cd%TYPE,
                                   p_trading_risk_schema_code        IN trading_accounts.trading_risk_schm_cd%TYPE,
                                   p_payment_schema_code             IN trading_accounts.payment_schm_cd%TYPE,
                                   p_crm_schema_code                 IN trading_accounts.crm_schm_cd%TYPE,
                                   p_locale_id                       IN trading_accounts.locale_id%TYPE,
                                   p_locale_version                  IN trading_accounts.locale_version%TYPE,
                                   p_locale_language                 IN trading_accounts.locale_language%TYPE,
                                   p_locale_territory                IN trading_accounts.locale_territory%TYPE,
                                   p_locale_timezone                 IN trading_accounts.locale_timezone%TYPE,
                                   p_payment_profile_id              IN trading_accounts.payment_profile_id%TYPE,
                                   p_pay_prof_block_all_payments     IN trading_accounts.blockallpayments%TYPE,
                                   p_pay_prof_block_withdrawals      IN trading_accounts.blockwithdrawals%TYPE,
                                   p_payment_profile_warn_fct        IN trading_accounts.warnfct%TYPE,
                                   p_pay_prf_blck_withd_prcng_spk    IN trading_accounts.blockwithdrawalspricingspike%TYPE,
                                   p_pay_prf_blck_withd_reason       IN trading_accounts.blockwithdrawalsreason%TYPE,
                                   p_pay_prf_blck_all_pay_reason     IN trading_accounts.blockallpaymentreason%TYPE,
                                   p_payment_prof_warn_fct_reason    IN trading_accounts.warnfctreason%TYPE,
                                   p_product_wrapper_settings        IN product_wrapper_setting_tab,
                                   p_customer_ids                    IN customer_id_tab,
                                   p_is_deleted                      IN trading_accounts.is_deleted%TYPE,
                                   p_trading_account_type            IN trading_accounts.trading_account_type%TYPE,
                                   p_primary_account_holder_id       IN trading_accounts.primary_account_holder_id%TYPE,
                                   p_is_joint_account                IN trading_accounts.is_joint_account%TYPE,
                                   p_independent_margin_amount       IN trading_accounts.independent_margin_amount%TYPE,
                                   p_liquidation_level               IN trading_accounts.liquidation_level%TYPE,
                                   p_liquidation_warning_level       IN trading_accounts.liquidation_warning_level%TYPE,
                                   p_bi_trading_status               IN trading_accounts.bi_trading_status%TYPE,
                                   p_bi_trading_status_date          IN trading_accounts.bi_trading_status_date%TYPE,
                                   p_sales_tax_country               IN trading_accounts.sales_tax_country%TYPE,
                                   p_commission_schema_code          IN trading_accounts.commission_schema_code%TYPE,
                                   p_carrying_costs_schema_code      IN trading_accounts.carrying_costs_schema_code%TYPE,
                                   p_crryng_csts_offst_schm_cd       IN trading_accounts.crryng_csts_offst_schm_cd%TYPE,
                                   p_price_schema_code               IN trading_accounts.price_schema_code%TYPE,
                                   p_accnt_cust_chng_rsns_tab        IN accnt_cust_chng_rsns_tab
                                   ,p_liquidation_type               IN trading_accounts.liquidation_type%TYPE
                                   ,p_liquidation_reset_level        IN trading_accounts.liquidation_reset_level%TYPE
                                   ,p_hedge_closeout_schema_code     IN trading_accounts.hedge_closeout_schema_code%TYPE
                                   ,p_hedge_broker_code              IN trading_accounts.hedge_broker_code%TYPE
                                   ,p_hedge_broker_account_number    IN trading_accounts.hedge_broker_account_number%TYPE
                                   ,p_hedge_execution_broker         IN trading_accounts.hedge_execution_broker%TYPE
                                   ,p_hedge_clearing_broker          IN trading_accounts.hedge_clearing_broker%TYPE
                                   ,p_is_hedge_tradable              IN trading_accounts.is_hedge_tradable%TYPE
                                   ,p_hedge_calculate_commission     IN trading_accounts.hedge_calculate_commission%TYPE
                                   ,p_hedge_firm_reference_number    IN trading_accounts.hedge_firm_reference_number%TYPE
                                   ,p_hedge_legal_entity_identifie   IN trading_accounts.hedge_legal_entity_identifier%TYPE
                                   ,p_hedge_bank_identifier_code     IN trading_accounts.hedge_bank_identifier_code%TYPE
                                   ,p_hedge_corporate_sector         IN trading_accounts.hedge_corporate_sector%TYPE
                                   ,p_hedge_financial_counterparty   IN trading_accounts.hedge_financial_counterparty%TYPE
                                   ,p_hedge_account_address_tab      IN hedge_account_address_tab
                                   ,p_hedge_account_contact_tab      IN hedge_account_contact_tab
                                   ,p_utt_reason_tab                 IN utt_reason_tab
                                   ,p_order_verification_time        IN trading_accounts.order_verification_time%TYPE
                                   ,p_introducer_reference           IN trading_accounts.Introducer_Reference%TYPE
                                   ,p_trading_desk_id                IN trading_accounts.Trading_Desk_Id%TYPE
                                   ,p_crng_costs_offset_threshold    IN trading_accounts.Crng_Costs_Offset_Threshold%TYPE
                                   ,p_apply_crng_costs_offset        IN trading_accounts.Apply_Crng_Costs_Offset%TYPE
                                   ,p_liquidation_method             IN trading_accounts.Liquidation_Method%TYPE
                                   ,p_ovrrde_ccy_cc_offset           IN trading_accounts.Ovrrde_Ccy_Cc_Offset%TYPE
                                   ,p_ovrrde_ccy_cc_offset_thrshld   IN trading_accounts.Ovrrde_Ccy_Cc_Offset_Threshold%TYPE
                                   ,p_match_open_trades              IN trading_accounts.Match_Open_Trades%TYPE
                                   ,p_apply_carrying_costs           IN trading_accounts.Apply_Carrying_Costs%TYPE
                                   ,p_block_all_payments_ref         IN trading_accounts.block_all_payments_ref%TYPE
                                   ,p_block_withdrawals_ref          IN trading_accounts.block_withdrawals_ref%TYPE
                                   ,p_has_too_many_cards             IN trading_accounts.has_too_many_cards%TYPE
                                   ,p_funded_by_bank_account         IN trading_accounts.funded_by_bank_account%TYPE
                                   ,p_closeout_schema_code           IN trading_accounts.closeout_schema_code%TYPE
                                   ,p_ref_commission_schema_code     IN trading_accounts.ref_commission_schema_code%TYPE
                                   ,p_ref_price_schema_code          IN trading_accounts.ref_price_schema_code%TYPE
                                   ,p_manual_liquidation             IN trading_accounts.manual_liquidation%TYPE
                                   ,p_all_products_are_manual_trd    IN trading_accounts.all_products_are_manual_trdg%TYPE
                                   ,p_accnt_manual_products_tab      IN accnt_manual_products_tab
                                   ,p_ref_price_schm_cds_asst_tab    IN ref_price_schm_cds_asst_tab
                                   ,p_blocked_cash                   IN Trading_Accounts.blocked_cash%TYPE
                                   ,p_guaran_stop_loss_or_style      IN trading_accounts.guaran_stop_loss_or_style%TYPE
                                   ,p_hedge_account_asset_class      IN hedge_account_asset_class_tab
                                   ,p_time_dependent_liquidation     IN trading_accounts.time_dependent_liquidation%TYPE
                                   ,p_time_dep_liquidation_period    IN trading_accounts.time_dep_liquidation_period%TYPE
                                   ,p_charge_commission              IN trading_accounts.Charge_Commission%TYPE
                                   ,p_quote_proposed_timeout         IN trading_accounts.Quote_Proposed_Timeout%TYPE
                                   ,p_accnt_liquidation_levels_tab   IN accnt_liquidation_levels_tab
                                   ,p_accnt_order_exec_type_tab      IN accnt_order_execution_type_tab
                                   ,p_binary_buffer                  IN trading_accounts.binary_buffer%TYPE
                                   ,p_order_time_delay               IN ta_order_time_delay_tab
                                   ,p_loss_limit                     IN ta_limit_loss_tab
                                   ,p_trading_account_multiplier     IN trading_account_multiplier_tab
                                   ,p_record_source                  IN trading_accounts.record_source%TYPE DEFAULT 'CM'
                                   ,p_is_phone_trading_only          IN trading_accounts.is_phone_trading_only%TYPE
                                   ,p_is_manual_execution_only       IN trading_accounts.is_manual_execution_only %TYPE
                                   ,p_knockout_account_min_balance   IN trading_accounts.knockout_account_min_balance%TYPE
                                   ,p_is_holding_costs_modify_gslo   IN trading_accounts.is_holding_costs_modify_gslo%TYPE
                                   ,p_previous_partner_id            IN trading_accounts.previous_partner_id%TYPE
                                   ,p_instrument_schema_code         IN trading_accounts.instrument_schema_code%TYPE
                                   ,p_apply_ko_frwrd_rllvr_csts      IN trading_accounts.apply_ko_frwrd_rllvr_csts%TYPE
                                   ,p_locale                         IN trading_accounts.locale%TYPE
                                   ,p_is_segregated                  IN trading_accounts.is_segregated%TYPE
                                   ,p_tax_declarations               IN tax_declaration_tab
                                   ,p_fxr_schema_code                IN trading_accounts.fxr_schema_code%TYPE
                                   ,p_exec_commission_schema_code    IN trading_accounts.exec_commission_schema_code%TYPE
                                   ,p_is_gslo_widening_allowed       IN trading_accounts.is_gslo_widening_allowed%TYPE
                                   ,p_is_write_off_deficit           IN trading_accounts.is_write_off_deficit%TYPE
                                   ,p_is_multicurrency_enabled       IN trading_accounts.is_multicurrency_enabled%TYPE
                                   ,p_multicurrency_cnvrsn_period    IN trading_accounts.multicurrency_cnvrsn_period%TYPE
                                   ,p_order_execution_mode           IN trading_accounts.order_execution_mode%TYPE
                                   ,p_hedge_usage_type               IN trading_accounts.hedge_usage_type%TYPE
                                   ,p_ta_pll_exec_trad_checks        IN ta_pll_exec_trad_checks_tab
                                   ,p_stockbroking_account_id        IN trading_accounts.stockbroking_account_id%TYPE
                                   ,p_is_institutional               IN trading_accounts.is_institutional%TYPE
                                   ,p_is_mifid_reduce_only           IN trading_accounts.is_mifid_reduce_only%TYPE
                                   ,p_use_tradable_rights			 IN trading_accounts.use_tradable_rights%TYPE
                                   ,p_hedge_crypto_broker_type		 IN trading_accounts.hedge_crypto_broker_type%TYPE
                                   ,p_evolutions                     IN ta_evolution_tab
                                   ,p_open_order_trade_limit		 IN trading_accounts.open_order_trade_limit%TYPE
                                   ,p_is_consider_for_german_cgt     IN trading_accounts.is_consider_for_german_cgt%TYPE
                                   ,p_is_waive_card_deposit_fee      IN trading_accounts.is_waive_card_deposit_fee%TYPE
                                   ,p_is_waive_bank_withdrawal_fee   IN trading_accounts.is_waive_bank_withdrawal_fee%TYPE
                                   ,p_is_funded_by_card              IN trading_accounts.is_funded_by_card%TYPE
                                   ,p_is_block_for_fit               IN trading_accounts.is_block_for_fit%TYPE
                                   ,p_is_money_managed_account       IN trading_accounts.is_money_managed_account%TYPE
                                   ,p_price_feed_schema_code         IN trading_accounts.price_feed_schema_code%TYPE
                                   ,p_is_create_residual_fx_trades   IN trading_accounts.is_create_residual_fx_trades%TYPE
                                   ,p_addtnl_allwbl_fx_bal_ccys      IN addtnl_allwbl_fx_bal_ccys_tab
                                   ,p_fx_rollover_behaviour          IN trading_accounts.fx_rollover_behaviour%TYPE
                                   ,p_designated_node                IN trading_accounts.designated_node%TYPE
                                   ,p_is_fix_tradeable               IN trading_accounts.is_fix_tradeable%TYPE
                                   ,p_fx_schema_code                 IN trading_accounts.fx_schema_code%TYPE
                                   ,p_hedge_calc_exec_commisson      IN trading_accounts.hedge_calc_exec_commisson%TYPE
                                   ,p_account_purpose                IN trading_accounts.account_purpose%TYPE
                                   ,p_parent_trading_account_id      IN trading_accounts.parent_trading_account_id%TYPE
                                   ,p_is_mt4_vps                     IN trading_accounts.is_mt4_vps%TYPE
                                   ,p_give_up_profile                IN ta_give_up_profiles_tab
                                   ,p_corporate_sector_type          IN trading_accounts.corporate_sector_type%TYPE
                                   ,p_related_hedge_account_id       IN trading_accounts.related_hedge_account_id%TYPE
                                   ,p_relational_role                IN trading_accounts.relational_role%TYPE
                                   ,p_trading_guard_amount           IN trading_accounts.trading_guard_amount%TYPE
                                   ,p_secondary_currencies           IN secondary_currencies_tab
                                   ,p_is_inactive_funds              IN trading_accounts.is_inactive_funds%TYPE
                                   ,p_is_fxat                        IN trading_accounts.is_fxat%TYPE
                                   ,p_is_block_deposits              IN trading_accounts.is_block_deposits%TYPE
                                   ,p_block_deposits_reason          IN trading_accounts.block_deposits_reason%TYPE
                                   ,p_block_deposits_reference       IN trading_accounts.block_deposits_reference%TYPE
                                   ,p_rebate_terms_schema_code       IN trading_accounts.rebate_terms_schema_code%TYPE
                                   ,p_is_subject_to_position_closure IN trading_accounts.is_subject_to_position_closure%TYPE
                                   ,p_is_rebate_taxable              IN trading_accounts.is_rebate_taxable%TYPE
                                   ,p_interest_schema_code           IN trading_accounts.interest_schema_code%TYPE
                                   ,p_first_trade_date               IN trading_accounts.first_trade_date%TYPE
                                   ,p_is_spreadbet                   IN trading_accounts.is_spreadbet%TYPE
                                   ,p_is_test_account                IN trading_accounts.is_test_account%TYPE
                                   ,p_hip_classification_risk_bucket IN trading_accounts.hip_classification_risk_bucket%TYPE
                                   ,p_is_corporate_brokerage         IN trading_accounts.is_corporate_brokerage%TYPE
                                   ,p_metatrader_introducer_id       IN trading_accounts.metatrader_introducer_id%TYPE
                                   ,p_conv_csh_bookings_to_prim_ccy  IN trading_accounts.conv_csh_bookings_to_prim_ccy%TYPE
                                   ,p_hedge_is_sernova_account       IN trading_accounts.hedge_is_sernova_account%TYPE
                                   ,p_hedge_comments                 IN trading_accounts.hedge_comments%TYPE
                                   ,p_is_stop_loss_mrgn_rebte_enbld  IN trading_accounts.is_stop_loss_mrgn_rebte_enbld%TYPE
                                   ,p_is_trading_view_enabled        IN trading_accounts.is_trading_view_enabled%TYPE
                                   ,p_relationship_country_override  IN trading_accounts.relationship_country_override%TYPE
                                   ,p_ta_commission_settings         IN ta_commission_settings_tab
                                   ,p_hedge_counterparty_legal_name  IN trading_accounts.hedge_counterparty_legal_name%TYPE
                                   ,p_hedge_account_security_class   IN hedge_account_security_class_tab
                                   ,p_hedge_account_security_prdct   IN hedge_account_security_prdct_tab
                                   ,p_is_eligible_for_multiasset     IN trading_accounts.is_eligible_for_multiasset%TYPE
                                   ,p_hedge_is_firm_holding          IN trading_accounts.hedge_is_firm_holding%TYPE
                                   ,p_custody_fee_schema_code        IN trading_accounts.custody_fee_schema_code%TYPE
                                   ,p_is_opted_in_to_prime           IN trading_accounts.is_opted_in_to_prime%TYPE
                                   ,p_is_reverse_enquiry             IN trading_accounts.is_reverse_enquiry%TYPE
                                   ,p_is_junior_isa                  IN trading_accounts.is_junior_isa%TYPE
                                   ,p_is_dma_cfd_permitted           IN trading_accounts.is_dma_cfd_permitted%TYPE
                                   ,p_hedge_account_dlgtd_rprtng     IN hedge_account_dlgtd_rprtng_tab
                                   );


    -- ===================================================================================
    -- get_trading_account_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of trading_account_id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_source_platform                platform - Trading Platform MMCFD/MMSB or NG
    --     p_open_date_from                 open date - Time trading account was created at source
    --     p_open_date_to                   open date - Time trading account was created at source
    -- -----------------------------------------------------------------------------------
    FUNCTION get_trading_account_ids (p_source_platform IN trading_accounts.source_platform%TYPE,
                                      p_open_date_from  IN trading_accounts.open_date%TYPE,
                                      p_open_date_to    IN trading_accounts.open_date%TYPE) RETURN SYS_REFCURSOR;

    -- ===================================================================================
    -- get_hedge_trading_account_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of hedge trading_account_id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- -----------------------------------------------------------------------------------
    FUNCTION get_hedge_trading_account_ids RETURN SYS_REFCURSOR;

    -- ===================================================================================
    -- get_stubbed_ids
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to return a set of stubbed id attributes
    --
    -- Notes:
    -- ------
    --
    --     None
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_batch_limit                    Maximum number of stubbed id's to return (Optional - not set returns all)
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20004    Default Exception
    -- -----------------------------------------------------------------------------------
    FUNCTION get_stubbed_ids (p_batch_limit  IN NUMBER) RETURN SYS_REFCURSOR;

  -- ===================================================================================
  -- put_customer_ids
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put Customer ID's
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TA_CUST_LINK_TA_FEED
  --     TA_CUST_LINK_TA_FEED_H
  --     CUSTOMERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_trading_account_id             Trading account id
  --     p_customer_ids                   Customer ids
  --     p_old_account_version		  	  Account Version of the existing record on ODS
  --	 p_new_account_version			  Account Version of the new record that is received
  --     p_trading_account_type           Trading account type e.g. CUSTOMER|INTERNAL
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_customer_ids(p_user                        trading_accounts.created_by%TYPE,
                             p_logical_load_timestamp      trading_accounts.logical_load_timestamp%TYPE,
                             p_effective_start_timestamp   trading_accounts.effective_start_timestamp%TYPE,
                             p_trading_account_id          trading_accounts.trading_account_id%TYPE,
                             p_customer_ids                customer_id_tab,
                             p_old_account_version         trading_accounts.account_version%TYPE,
                             p_new_account_version         trading_accounts.account_version%TYPE,
                             p_trading_account_type        trading_accounts.trading_account_type%TYPE);



  -- ===================================================================================
  -- put_give_up_profile
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to retain GiveUpProfile entity in DB
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     GIVE_UP_PROFILES
  --     GIVE_UP_PROFILES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_logical_load_timestamp         Logical Load Timestamp For Transaction
  --     p_effective_start_timestamp      Effective start timestamp
  --     p_give_up_profile                Give up profile to be added/edited
  --     p_prime_broker                   Prime broker to be added/updated
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_give_up_profile (p_user                             ta_give_up_profiles.created_by%type,
                                 p_logical_load_timestamp           ta_give_up_profiles.logical_load_timestamp%type,
                                 p_effective_start_timestamp        ta_give_up_profiles.effective_start_timestamp%type,
                                 p_give_up_profile 				    ta_give_up_profiles_tab);

  -- ===================================================================================
  -- put_history_gup
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to retain Old GiveUpProfile entity in DB
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     TA_GIVE_UP_PROFILES_H
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_old_give_up_profile            Old give_up_profile record to retain in history table
  --     p_effective_end_timestamp        End of life moment for this record
  --     p_action                         End of life cause/event
  --
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --
  --
  -- -----------------------------------------------------------------------------------

  PROCEDURE put_history_gup ( p_old_give_up_profile      IN ta_give_up_profiles%ROWTYPE,
                              p_effective_end_timestamp  IN ta_give_up_profiles_h.effective_end_timestamp%TYPE,
                              p_action                   IN ta_give_up_profiles_h.action%TYPE);

-- ===================================================================================
-- put_regulatory_acceptances
-- ===================================================================================
--
-- Synopsis:
-- ---------
--
--   Procedure to populate regulatory_acceptances and call put procedures for nested objects
--
-- Parameters:
-- -----------
--
--   Parameter                        Description
--   ------------------------------   ----------------------------------------------
--   p_user                           oracle app user
--   p_effective_start_timestamp      timestamp of upstream change
--   p_regulatory_acceptances              table of results
--
-- Exceptions:
-- -----------
--
--     SQLCODE   SQLERRM
--   -------   ---------------------------------------------------------------------
--
-- -----------------------------------------------------------------------------------

  PROCEDURE put_regulatory_acceptances  (p_user                           IN regulatory_acceptances.created_by%TYPE,
                                    p_regulatory_acceptances              IN regulatory_acceptances_tab);

END nrg_trading_account;
//