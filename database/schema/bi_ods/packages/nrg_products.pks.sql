
  CREATE OR REPLACE EDITIONABLE PACKAGE "BI_ODS"."NRG_PRODUCTS" 
AS
    -- ===================================================================================
    -- NRG_PRODUCTS
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     This package encapsulates NRG (Near Realtime Gatherer)
    --
    --     Management of the products model
    --
    -- -----------------------------------------------------------------------------------
    --
    -- Notes:
    -- ------
    --
    --     Run as BI_ODS
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --     -20001    Entity already exists
    --
    -- Modifications:
    -- --------------
    --
    --     Date         Modified By       Vers    Action
    --     ----------   ---------------   -----   ----------------------------------------
    --     27/09/2011   Sanket Mittal      1.0     Creation
    --     12/10/2011   Manoj Kumar        1.1     Added the put_product,remove_product method
    --     27/10/2011   Sanket Mittal      1.2     Updated the package for changed structures
    --     08/11/2011   Manoj Kumar        1.3     Modifed to implement review comments
    --     15/11/2011   Sanket Mittal      1.4     Modified the create stub procedure to handle
    --                                             stub creation for only the instruments
    --     29/11/2011   Mark Gornicki      1.5     Incorporate the CFD Multiplier logic for MM Products
    --     09/10/2012   Sanket Mittal      1.7     Added new attributes to instrument entity
    --     18/04/2013   Sanket Mittal      2.0     P2 Changes. Added entities margin tier and instrument feed settings
    --     20/11/2013   Adam Krasnicki     2.1     put_mrgn_trs_summry is added as a public procedure
    --     27/11/2013   Adam Krasnicki     2.2     Procedure put_margin_tier_set is added as a public procedure
    --     06/12/2013   Adam Krasnicki     2.3     p_reg_instr_identification parameter in put_product_set procedure
    --     23/02/2014   Adam Krasnicki     2.4     p_products_gsko as a new parameter
    --     27/07/2015   Adam Krasnicki     3.6     PMS- StopBuffer change
    --     30/01/2016   Sanket Mittal      3.8     Added changes for Margin Tiers on product_settings
    --     03/03/2016   Sanket Mittal      4.1     Added new attribut primary_inst_code to instruments and changed logic for derived cash instrument
    --     24/06/2016   Sanket Mittal      4.3     BER-2703-SWS 34 - PMS Changes - Instruments
    --     13/07/2016   Sanket Mittal      4.7     BER-2729 Retire: Margin Tiers Summary
    --     24/10/2016   Sanket Mittal      4.9     Changed to add new attributes for countries
    --     29/11/2016   Sanket Mittal      5.0     BER-3024 PMS Snapshot - Economic Calendar Events and Instrument References
    --     19/11/2016   Sanket Mittal      5.1     BER-3218 NRG_PRODUCTS - new Intruments attribue: MarketDataMappingCode
    --     19/01/2017   Sanket Mittal      5.2     BER-3266 NRG_PRODUCTS - BI_ODS.INSTRUMENTS - New attributes
    --     07/02/2017   S Kinkhabwala      5.3     BER - 3312 - Add a new proc put_snapshot_log
    --     07/06/2017   Sanket Mittal      5.6     BER-3171 NRG_PRODUCTS - INSTRUMENT_TYPE_LANGUAGES_H - PK conflict
  	--     25/01/2018	Sakina Kinkhabwala 5.9	   BER-4271/BER-4296 - Add new proc put_inst_type_broker_margins
    --     24/07/2018   Patrick Dinwiddy   6.1     BER-4740/BER-4742 - 2 new procs/new instrument attributes
    --     03/05/2019   Patrick Dinwiddy   6.3     JCS-10574 new PriceStreams and PriceBands objects
    --     22/07/2019   Patrick Dinwiddy   6.4     JCS-11219 and JCS-11143
	--     30/01/2020   Patrick Dinwiddy   6.7     JCS-11638 inst schema metadata entity
	--     05/11/2020   Patrick Dinwiddy   7.0     JCS-13745 new markets entities
    -- ===================================================================================

    -- ===================================================================================
    -- version
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Function to retrieve the version of the package
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --
    -- Return:
    -- -------
    --
    --     Returns a VARCHAR2 representing the version of the package
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20000    Error retrieving version : %error_backtrace
    --
    -- -----------------------------------------------------------------------------------

    FUNCTION version
        RETURN VARCHAR2 DETERMINISTIC;

    -- ===================================================================================
    -- create_instrument_stub
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product stub in case the instrument is missing
    --     This is done to ensure the transactional integrity
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_logical_load_timestamp         Logical Load Timestamp
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_instrument_code                Instrument Code
    --     p_product_platform               Product Platform
    --     p_product_wrapper                Product Wrapper
    --     p_mm_instrument_id               MM Instrument Id
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE create_instrument_stub (p_user                       IN products.created_by%TYPE,
                                      p_logical_load_timestamp     IN products.logical_load_timestamp%TYPE,
                                      p_effective_start_timestamp  IN products.effective_start_timestamp%TYPE,
                                      p_instrument_code            IN products.instrument_code%TYPE,
                                      p_product_platform           IN products.platform%TYPE,
                                      p_product_wrapper            IN products.wrapper_code%TYPE,
                                      p_mm_instrument_id           IN mm_products.mm_instrument_id%TYPE);

    -- ===================================================================================
    -- put_product_set
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCTS
    --     MM_PRODUCTS
    --     INSTRUMENTS
    --     PRODUCT_WRAPPERS
    --     COUNTRIES
    --     REGIONS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_effective_start_timestamp      Effective start timestamp
    --     p_products                       Product Details for MM and NG products
    --     p_product_wrappers               Product Wrapper Details
    --     p_instruments                    Instument details
    --     p_regions                        Regions Details
    --     p_countries                      Countries Details
    --     p_inst_feed_setting
    --     p_reg_instr_identification
    --     p_products_gsko
    --
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------
    PROCEDURE put_product_set    ( p_user                           IN products.created_by%TYPE,
                                  p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                  p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
                                  p_products                        IN product_tab,
                                  p_product_wrappers                IN product_wrapper_tab,
                                  p_instruments                     IN instrument_tab,
                                  p_regions                         IN region_tab,
                                  p_countries                       IN country_tab,
                                  p_inst_feed_setting               IN instrument_feed_setting_tab,
                                  p_reg_instr_identification        IN reg_instr_identification_tab,
                                  p_products_gsko                   IN products_gsko_tab,
                                  p_product_settings                IN product_settings_tab,
                                  p_economic_calendar_events        IN economic_calendar_events_tab,
                                  p_inst_to_economic_events         IN economic_cal_events_inst_tab,
                                  p_tax_treaties                    IN tax_treaty_relations_tab
                                  );
  -- ===================================================================================
  -- put_margin_tier_set
  -- ===================================================================================
  --
  -- Synopsis:
  -- ---------
  --
  --     Procedure to put a product margin tier data set
  --
  -- Notes:
  -- ------
  --
  --     Tables potentially populated:
  --
  --     MARGIN_TIERS
  --
  -- Parameters:
  -- -----------
  --
  --     Parameter                        Description
  --     ------------------------------   ----------------------------------------------
  --     p_user                           User
  --     p_effective_start_timestamp      Product Snapshot Start timestamp
  --     p_margin_tier                    Margin tiers table
  -- Exceptions:
  -- -----------
  --
  --     SQLCODE   SQLERRM
  --     -------   ---------------------------------------------------------------------
  --     -20001    Entity already exists
  --
  -- -----------------------------------------------------------------------------------

  --PROCEDURE put_margin_tier_set ( p_user                            IN products.created_by%TYPE,
   --                               p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
   --                               p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
   --                               p_margin_tier                     IN margin_tier_tab);

    -- ===================================================================================
    -- put_snapshot_identifiers
    -- ===================================================================================
    --
    -- Synopsis:
    -- ---------
    --
    --     Procedure to put a product identifier list
    --
    -- Notes:
    -- ------
    --
    --     Tables potentially populated:
    --
    --     PRODUCT_SNAPSHOT_IDNTFRS
    --
    -- Parameters:
    -- -----------
    --
    --     Parameter                        Description
    --     ------------------------------   ----------------------------------------------
    --     p_user                           User
    --     p_snapshot_identifiers           Product Snapshot Identifiers
    --
    -- Exceptions:
    -- -----------
    --
    --     SQLCODE   SQLERRM
    --     -------   ---------------------------------------------------------------------
    --     -20001    Entity already exists
    --
    -- -----------------------------------------------------------------------------------

    PROCEDURE put_snapshot_identifiers(p_user                       IN product_snapshot_idntfrs.created_by%TYPE,
                                       p_snapshot_identifiers       IN product_snapshot_idntfr_tab);

    PROCEDURE put_snapshot_log         (p_user                            IN products.created_by%TYPE,
                                       p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                       p_snapshot_identifier             IN product_snapshot_idntfrs.snapshot_identifier%TYPE,
                                       p_is_processed                    IN product_snapshot_idntfrs.is_processed%TYPE);

    PROCEDURE put_products(p_user                           IN products.created_by%TYPE,
                           --p_platform                       IN products.Platform%TYPE,
                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                           p_products                       IN product_tab);

    PROCEDURE put_instruments(p_user                            IN products.created_by%TYPE,
                              p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                              p_instruments                     IN instrument_tab);

    PROCEDURE put_products_gsko  (p_user                            IN products.created_by%TYPE,
                                  p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                  p_products_gsko                   IN products_gsko_tab);

    PROCEDURE put_product_wrapper(p_user                           IN products.created_by%TYPE,
                                p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                p_product_wrappers                IN product_wrapper_tab);

    PROCEDURE put_regions(p_user                           IN products.created_by%TYPE,
                          p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                          p_regions                        IN region_tab);

    PROCEDURE put_countries(p_user                           IN products.created_by%TYPE,
                            p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                            p_countries                      IN country_tab);

    PROCEDURE put_reg_inst_identification(p_user                           IN products.created_by%TYPE,
                                          p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                          p_reg_instr_identification       IN reg_instr_identification_tab);

    PROCEDURE put_inst_feed_setting (p_user                               IN products.created_by%TYPE,
                                     p_effective_start_timestamp          IN products.effective_start_timestamp%TYPE,
                                     p_inst_feed_setting                  IN instrument_feed_setting_tab);

    PROCEDURE put_product_settings(p_user                       IN products.created_by%TYPE,
                                   p_effective_start_timestamp  IN products.effective_start_timestamp%TYPE,
                                   p_product_settings           IN product_settings_tab);

    PROCEDURE put_economic_calendar_events(p_user                            IN products.created_by%TYPE,
                                           p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                           p_economic_calendar_events        IN economic_calendar_events_tab);

    PROCEDURE put_inst_to_economic_events(p_user                            IN products.created_by%TYPE,
                                          p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                                          p_inst_to_economic_events         IN economic_cal_events_inst_tab);

    PROCEDURE put_tax_treaties(p_user                            IN products.created_by%TYPE,
                               p_effective_start_timestamp       IN products.effective_start_timestamp%TYPE,
                               p_tax_treaties                    IN tax_treaty_relations_tab);

    PROCEDURE put_instrument_type_language(p_user                           IN products.created_by%TYPE,
                                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                           p_instrument_type_language       IN instrument_type_languages_tab);

    PROCEDURE put_holding_costs (p_user                           IN products.created_by%TYPE,
                                 p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                 p_holding_costs                  IN holding_costs_tab);

    PROCEDURE put_reference_holding_costs (p_user                           IN products.created_by%TYPE,
                                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                           p_reference_holding_costs        IN reference_holding_costs_tab);

	  PROCEDURE put_inst_type_broker_margins(p_user                           IN products.created_by%TYPE,
                                           p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                           p_inst_type_broker_margins       IN inst_type_broker_margins_tab);

    PROCEDURE put_prdct_sttngs_inst_schm (p_user                           IN prdct_sttngs_inst_schema.created_by%TYPE,
                                          p_effective_start_timestamp      IN prdct_sttngs_inst_schema.effective_start_timestamp%TYPE,
                                          p_prdct_sttngs_inst_schm         IN prdct_sttngs_key_tab);

  PROCEDURE put_prdct_sttngs_inst_schm_ccy (p_user                           IN prdct_sttngs_inst_schema_ccy.created_by%TYPE,
                                            p_effective_start_timestamp      IN prdct_sttngs_inst_schema_ccy.effective_start_timestamp%TYPE,
                                            p_prdct_sttngs_inst_schm_ccy         IN prdct_sttngs_key_tab);

  PROCEDURE put_price_streams (p_user                           IN products.created_by%TYPE,
                               p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                               p_price_streams                  IN price_streams_tab);

  PROCEDURE put_instrument_price_bands (p_user                           IN products.created_by%TYPE,
                                        p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                        p_instrument_price_bands         IN instrument_prc_bnds_key_tab);


  PROCEDURE put_instrument_basket_relation(p_user                           IN instrument_basket_relations.created_by%TYPE,
                                           p_effective_start_timestamp      IN instrument_basket_relations.effective_start_timestamp%TYPE,
                                           p_instrument_basket_rel          IN instrument_basket_rel_tab);

  PROCEDURE put_instrument_schema_metadata(p_user                           IN instrument_schema_metadata.created_by%TYPE,
                                           p_effective_start_timestamp      IN instrument_schema_metadata.effective_start_timestamp%TYPE,
                                           p_inst_schema_metadata           IN inst_schema_metadata_tab);

  PROCEDURE put_instrument_swap_point_bands (p_user                           IN products.created_by%TYPE,
                                             p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                             p_instrument_swap_point_bands    IN instrument_swp_bnds_key_tab);

  PROCEDURE put_currencies(p_user                           IN currencies.created_by%TYPE,
                           p_effective_start_timestamp      IN currencies.effective_start_timestamp%TYPE,
                           p_currencies                     IN currencies_tab);

  PROCEDURE put_currency_names(p_user                           IN currency_names.created_by%TYPE,
                               p_effective_start_timestamp      IN currency_names.effective_start_timestamp%TYPE,
                               p_currency_names                 IN currency_names_tab);

  PROCEDURE put_currency_margin_defns(p_user                           IN currency_margin_defns.created_by%TYPE,
                                      p_effective_start_timestamp      IN currency_margin_defns.effective_start_timestamp%TYPE,
                                      p_currency_margin_defns          IN currency_margin_def_key_tab);

  PROCEDURE put_markets(p_user                           IN markets.created_by%TYPE,
                        p_effective_start_timestamp      IN markets.effective_start_timestamp%TYPE,
                        p_markets                        IN market_tab);

  PROCEDURE put_market_hour_types(p_user                           IN market_hour_types.created_by%TYPE,
                                  p_effective_start_timestamp      IN market_hour_types.effective_start_timestamp%TYPE,
                                  p_market_hour_types              IN market_hour_type_tab);

  PROCEDURE put_market_suspensions(p_user                         IN market_suspensions.created_by%TYPE,
                                   p_effective_start_timestamp    IN market_suspensions.effective_start_timestamp%TYPE,
                                   p_market_suspensions           IN market_suspension_tab);

  PROCEDURE put_pms_time_zones(p_user                           IN pms_time_zones.created_by%TYPE,
                               p_effective_start_timestamp      IN pms_time_zones.effective_start_timestamp%TYPE,
                               p_time_zones                     IN pms_time_zone_tab);

  PROCEDURE put_typical_market_hours(p_user                           IN typical_market_hours.created_by%TYPE,
                                     p_effective_start_timestamp      IN typical_market_hours.effective_start_timestamp%TYPE,
                                     p_typical_market_hours           IN typical_market_hour_tab);

  PROCEDURE put_options_trading_class(p_user                           IN options_trading_class.created_by%TYPE,
                                        p_effective_start_timestamp      IN options_trading_class.effective_start_timestamp%TYPE,
                                        p_options_trading_class          IN options_trading_class_tab);

  PROCEDURE put_fx_conversion_bands (p_user                           IN products.created_by%TYPE,
                                                  p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                                  p_instrument_fx_conversion_bands IN instrument_fx_con_bands_key_tab);

   PROCEDURE put_broker_swap_rates (p_user                           IN products.created_by%TYPE,
                                   p_effective_start_timestamp      IN products.effective_start_timestamp%TYPE,
                                   p_broker_swap_rates              IN broker_swap_rates_tab);
  END nrg_products;

//