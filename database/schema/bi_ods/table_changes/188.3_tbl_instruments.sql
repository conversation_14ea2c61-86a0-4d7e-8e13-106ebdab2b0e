ALTER TABLE instruments ADD bb_country_incorporation varchar2(50)
//
ALTER TABLE instruments_h ADD bb_country_incorporation varchar2(50)
//
COMMENT ON COLUMN bi_ods.instruments.bb_country_incorporation IS 'SWS.productMasterPublishing.Snapshot2.Instrument.BB_countryofIncorporation
Column used on US WHT report'
//

ALTER TABLE instruments ADD reference_tax_country varchar2(100)
//
ALTER TABLE instruments_h ADD reference_tax_country varchar2(100)
//
COMMENT ON COLUMN bi_ods.instruments.reference_tax_country  IS 'SWS.productMasterPublishing.Snapshot2.Instrument.ReferenceTaxCountry
Column used on US WHT report'
//

ALTER TABLE instruments ADD ref_country_incorporation_dsc  varchar2(50)
//
ALTER TABLE instruments_h ADD ref_country_incorporation_dsc  varchar2(50)
//
COMMENT ON COLUMN bi_ods.instruments.ref_country_incorporation_dsc  IS 'SWS.productMasterPublishing.Snapshot2.Instrument.ReferenceCountryOfIncorporationDesc
Column used on US WHT report'
//

--//@UNDO
ALTER TABLE instruments SET UNUSED (bb_country_incorporation, reference_tax_country, ref_country_incorporation_dsc)
//
ALTER TABLE instruments_h SET UNUSED (bb_country_incorporation, reference_tax_country, ref_country_incorporation_dsc)
//
