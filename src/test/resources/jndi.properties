#
# This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
#
# The copying and reproduction of this document and/or its content (whether wholly or partly)
# or any incorporation of the same into any other material in any media or format of any kind
# is strictly prohibited. All rights are reserved.
#
# (c) CMC Markets Plc 2011
#
# register some queues in JNDI using the form
# queue.[jndiName] = [physicalName]
queue.NRG.Response.1=DYN.NRG.Response.1?consumer.prefetchSize=1
queue.NRG.Response.2=DYN.NRG.Response.2?consumer.prefetchSize=1
queue.NRG.Response.NonServiceFramework.1=DYN.NRG.Response.NonServiceFramework.1
queue.NRG.Response.NonServiceFramework.2=DYN.NRG.Response.NonServiceFramework.2

#Override the scheduler queue
queue.Scheduler.Request=DYN.Scheduler.Request

#Durable queue subscriptions for NRG.CustomerMaster.1
queue.CustomerMaster.Publish.Accounts=DYN.NRG.Subscribe.CustomerMaster.1
queue.CustomerMaster.Publish.Companies=DYN.NRG.Subscribe.CustomerMaster.1
queue.CustomerMaster.Publish.CorporateStructure=DYN.NRG.Subscribe.CustomerMaster.1
topic.CustomerMaster.Publish.Customers=DYN.NRG.Subscribe.CustomerMaster.1
queue.CustomerMaster.Publish.Partners=DYN.NRG.Subscribe.CustomerMaster.1
queue.CustomerMaster.Publish.Persons=DYN.NRG.Subscribe.CustomerMaster.1
queue.CustomerMaster.Publish.MetaTraderIntroducer=DYN.NRG.Subscribe.CustomerMaster.1

#Durable queue subscriptions for NRG.Subscribe.1
queue.NRG.Subscribe.1=DYN.NRG.Subscribe.1
queue.com.cmcmarkets.IdentityManagement.IdentityService.Publish.Instance1=DYN.NRG.Subscribe.1
queue.CashAccount.Publish.Account=DYN.NRG.Subscribe.1
queue.CashAccount.Publish.Orchestration=DYN.NRG.Subscribe.1
queue.CashAccount.Publish.BookingEntry=DYN.NRG.Subscribe.BookingEntry
queue.CashAccount.Publish.Hedging=DYN.NRG.Subscribe.1
queue.ReportingService.Publish.AbnormalUIEvent=DYN.NRG.Subscribe.1
queue.ReportingService.Publish.PriceSubscription=DYN.NRG.Subscribe.PriceSubscription
queue.ReportingService.Publish.NewsRequestedEvent=DYN.NRG.Subscribe.1
queue.ReportingService.Publish.MorningstarRequestedEvent=DYN.NRG.Subscribe.1
queue.ReportingService.Publish.MessageDisplayEvent=DYN.NRG.Subscribe.1
queue.com.cmcmarkets.IdentityManagement.SessionService.Publish.Instance1=DYN.NRG.Subscribe.1
queue.Onboarding.Publish.1=DYN.NRG.Subscribe.1
queue.Security.Publish=DYN.NRG.Subscribe.1
queue.Alerts.Publish=DYN.NRG.Subscribe.Alerts
queue.Permission.Publish=DYN.NRG.Subscribe.1
queue.ProductMaster.Publish.PriceSchemaUpdated=DYN.NRG.Subscribe.1
queue.SecurityTokenService.Publish=DYN.NRG.Subscribe.SecurityTokenService
queue.MarketCalendar.Publish=DYN.NRG.Subscribe.1
queue.MessageHub.Publish=DYN.NRG.Subscribe.1
queue.EventStore.Publish=DYN.NRG.Subscribe.1
queue.TransfersAdmin.Publish.TransferRequests=DYN.NRG.Subscribe.1
queue.com.cmcmarkets.FMS.Publish.Instance1=DYN.NRG.Subscribe.1

queue.ReportingService.Publish.SinatraSessionEvent=DYN.NRG.Subscribe.Sinatra.Sessions
queue.ReportingService.Publish.SinatraPriceSubscriptionEvent=DYN.NRG.Subscribe.Sinatra.PriceSubscription
queue.ReportingService.Publish.FundCostAndChargesUpdated=DYN.NRG.Subscribe.FundCostAndChargesUpdated

#Durable queue subscriptions for NRG.Subscribe.Trading.1
queue.Trading.Publish.Account=DYN.NRG.Subscribe.Trading.1
queue.Trading.Publish.Trade=DYN.NRG.Subscribe.Trading.1
queue.Trading.Publish.Order=DYN.NRG.Subscribe.Trading.1
queue.Trading.Publish.Configuration=DYN.NRG.Subscribe.Trading.1
queue.Trading.Publish.SpeedBet=DYN.NRG.Subscribe.Trading.1
queue.Trading.Publish.SettlementData=DYN.NRG.Subscribe.Trading.1
queue.Trading.Publish.AggregatedFundOrder=DYN.NRG.Subscribe.Trading.1

#Durable queue subscriptions for NRG.Subscribe.Trading.PositionTransaction.1
queue.Trading.Publish.Position=DYN.NRG.Subscribe.Trading.PositionTransaction.1

#Durable queue subscriptions for NRG.Subscribe.HedgeTrading.1
queue.HedgeTrading.Publish.HedgeProcess=DYN.NRG.Subscribe.HedgeTrading.1
queue.HedgeTrading.Publish.Transaction=DYN.NRG.Subscribe.HedgeTrading.1

#Durable queue subscriptions for NRG.Subscribe.Statements.1
queue.Statements.Publish.1=DYN.NRG.Subscribe.Statements.1

#Durable queue subscriptions for NRG.Subscribe.Scheduler
queue.Scheduler.Publish=DYN.NRG.Subscribe.Scheduler

#Durable queue for priceplan
queue.PricePlan.Publish.CustomerPricePlans=DYN.NRG.Subscribe.CustomerPricePlans
queue.NRG.Subscribe.CustomerPricePlans=DYN.NRG.Subscribe.CustomerPricePlans
queue.PricePlan.Publish.RevenueDetails=NRG.Subscribe.PricePlan.RevenueDetails

#Durable queue for SIPP
queue.SIPPProviderBridge.Request=SIPPProviderBridge.Request
queue.SIPPProviderBridge.Publish.Members=DYN.NRG.Subscribe.SIPP.Members
queue.SIPPProviderBridge.Publish.CashContributions=DYN.NRG.Subscribe.SIPP.CashContributions


queue.NRG.Subscribe.Trading.1=DYN.NRG.Subscribe.Trading.1?consumer.prefetchSize=100
queue.NRG.Subscribe.Trading.PositionTransaction.1=DYN.NRG.Subscribe.Trading.PositionTransaction.1?consumer.prefetchSize=100
queue.NRG.Subscribe.HedgeTrading.1=DYN.NRG.Subscribe.HedgeTrading.1?consumer.prefetchSize=100

queue.CorporateCalendar.Request=CorporateCalendar.Request
queue.CorporateCalendar.Publish=DYN.NRG.Subscribe.CorporateCalendar

# register some topics in JNDI using the form
# topic.[jndiName] = [physicalName]
