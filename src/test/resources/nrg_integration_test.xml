<?xml version="1.0" encoding="UTF-8"?>
<!-- This document and its contents are protected by copyright 2011 and owned 
	by CMC Markets UK Plc. The copying and reproduction of this document and/or 
	its content (whether wholly or partly) or any incorporation of the same into 
	any other material in any media or format of any kind is strictly prohibited. 
	All rights are reserved. (c) CMC Markets Plc 2011 -->

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	                    http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	                    http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<!-- Configuration for encryptor, based on environment variables. In this 
		example, the encryption password will be read from an environment variable 
		called "NRG_ENCRYPTED_KEY" which, once the application has been started, 
		could be safely unset. -->

	<bean id="environmentVariablesConfiguration"
		class="org.jasypt.encryption.pbe.config.EnvironmentStringPBEConfig">
		<property name="algorithm" value="PBEWithMD5AndDES" />
		<property name="passwordEnvName" value="NRG_ENCRYPTED_KEY" />
	</bean>

	<bean id="properties"
		class="com.cmcmarkets.framework.service.config.spring.QueryableEncryptablePropertyPlaceholderConfigurer">
		<constructor-arg ref="configurationEncryptor" />
		<property name="ignoreUnresolvablePlaceholders" value="true" />
		<property name="locations">
			<list>
				<!-- <value>classpath:nrg_app.properties</value> -->
				<!--<value>classpath:nrg_server.properties</value>-->
				<value>classpath:application.properties</value>
				<value>classpath:nrgmessagestorereplayer_server.properties</value>
				<value>classpath:coherence.properties</value>
				<value>classpath:instance.properties</value>
				<value>classpath:jndi.properties</value>
				<value>classpath:nozomi.properties</value>
				<value>classpath:log4j2.component.properties</value>
			</list>
		</property>
	</bean> 

	<!-- The will be the encryptor used for decrypting configuration values. -->

	<bean id="configurationEncryptor" class="org.jasypt.encryption.pbe.StandardPBEStringEncryptor">
		<property name="config" ref="environmentVariablesConfiguration" />
	</bean>

	<bean class="com.cmcmarkets.framework.service.registry.api.model.impl.RequesterInfoFactory"
		factory-method="getInstance">
		<property name="properties" ref="properties" />
	</bean>
	
	<bean class="com.cmcmarkets.framework.service.runtime.ServiceFactory"
		factory-method="getInstance">
		<property name="configuration" ref="properties" />
	</bean>

	<!-- JNDI Template used for accessing the JMS ConnectionFactory -->

	<bean id="jndiTemplate" class="org.springframework.jndi.JndiTemplate">
		<property name="environment">
			<props>
				<prop key="java.naming.factory.initial">${java.naming.factory.initial}</prop>
				<prop key="java.naming.provider.url">${java.naming.provider.url}</prop>
			</props>
		</property>
	</bean>

	<!-- Define the connection factory -->

	<bean id="jmsConnectionFactory" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName" value="${ConnectionFactory}" />
		<property name="jndiTemplate" ref="jndiTemplate" />
	</bean>

	<!-- MESSAGE ROUTING details -->
	<!-- ReplyTo -->
	<bean id="replyTo" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName" value="${replyTo.legacy.destinationName}"/>
		<property name="jndiTemplate" ref="jndiTemplate"/>
	</bean>

	<bean id="serviceFrameworkServiceFactory" class="com.cmcmarkets.framework.service.runtime.ServiceFactory" factory-method="getInstance">
		<property name="properties" ref="properties" />
	</bean>

	<bean id="productCache" class="com.cmcmarkets.nrg.pms.DbrProductCache" lazy-init="true">
		<constructor-arg ref="lockService"/>
		<constructor-arg ref="productMasterService"/>
	</bean>

	<bean id="lockService" class="com.cmcmarkets.coherence.utils.LockService">
		<constructor-arg index="0" type="java.lang.String" value="${replyTo.destinationName}"/>
		<constructor-arg index="1" type="java.lang.String" value="NRG.ProcessControlCache"/>
	</bean>

	<bean id="productMasterService" factory-bean="serviceFactory" factory-method="getService">
		<constructor-arg value="com.cmcmarkets.productmaster.api.service.v4.ProductMasterService" />
	</bean>
	<!-- Partner DAO -->
	<bean id="partnerDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabasePartnerDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>
	
	<!-- Company DAO -->
	<bean id="companyDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseCompanyDAO">
	    <property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Customer DAO -->
	<bean id="customerDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseCustomerDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Pricing DAO -->
	<bean id ="pricingDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabasePricingDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- CorpActionGolden -->
	<bean id= "CorporateActionGoldenDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseCorporateActionGoldenDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Trading Account DAO -->
	<bean id="tradingAccountDAO"
		class="com.cmcmarkets.nrg.server.dao.impl.DatabaseTradingAccountDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Price Plan DAO -->
	<bean id="pricePlanDAO"
		  class="com.cmcmarkets.nrg.server.dao.impl.DatabasePricePlanDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Sipp Member DAO -->
	<bean id="sippMemberDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseSippMemberDao">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Sipp Cash Contribution DAO-->
	<bean id="sippCashContributionDao" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseSippCashContributionDao">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Trading Account DAO -->
	<bean id="orderDAO"
		  class="com.cmcmarkets.nrg.server.dao.impl.DatabaseOrderDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Cash Transaction DAO -->
	<bean id="cashTransactionDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseCashTransactionDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

<!--	<bean id="correlationDao" class="com.cmcmarkets.nrg.server.dao.impl.PrimaryCorrelationDAO">-->
<!--		<property name="delegateCorrelationDAO" ref="secondaryCorrelationDAO" />-->
<!--		<property name="cacheGetSpinIntervalMillis" value="${messageProcessor.cacheGetSpinIntervalMillis}" />-->
<!--		<property name="maxCacheGetSpinWaitMillis" value="${messageProcessor.maxCacheGetSpinWaitMillis}" />-->
<!--	</bean>-->

<!--	<bean id="secondaryCorrelationDAO"-->
<!--		  class="com.cmcmarkets.nrg.server.dao.impl.SecondaryCorrelationDAO"-->
<!--		  factory-method="getInstance">-->
<!--		<property name="correlationQueryDAO" ref="correlationQueryDAO" />-->
<!--		<property name="cashAccountService" ref="cashAccountService" />-->
<!--	</bean>-->


	<!-- Accrued Revenue DAO -->
	<bean id="accruedRevenueDAO"
		  class="com.cmcmarkets.nrg.server.dao.impl.DatabaseAccruedRevenueDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Cash Accounts DAO -->
	<bean id = "cashAccountDAO"
		class = "com.cmcmarkets.nrg.server.dao.impl.DatabaseCashAccountDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>
	<bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<constructor-arg ref="odsDataSource" />
	</bean>

	<!--	Person DAO -->
	<bean id="databasePersonDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabasePersonDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- PMS DAO -->
	<bean id="pmsDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabasePmsDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Trade DAO -->
	<bean id="tradeDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseTradeDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Positions DAO -->
    <bean id="openTradesDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseOpenTradesDAO">
        <property name="dataSource" ref="nrgDataSource" />
    </bean>

	<!-- PaymentData DAO -->
	<bean id="paymentDataDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabasePaymentDataDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>
    
    <!-- PositionTransaction DAO -->
    <bean id="positionTransactionDAO"
        class="com.cmcmarkets.nrg.server.dao.impl.DatabasePositionTransactionDAO">
        <property name="dataSource" ref="nrgDataSource" />
    </bean>
	
	<!-- Database Account Valu8e DAO -->
	<bean id="databaseAccountValueDAO"
		class="com.cmcmarkets.nrg.server.dao.impl.DatabaseAccountValueDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Disable JMS on the UCP connection manager, so that multiple instances 
		of the UCP can exist in the same container. This is a workaround to a bug 
		in the connection pool manager -->

	<bean id="connectionPoolManager" class="oracle.ucp.admin.UniversalConnectionPoolManagerImpl"
		factory-method="getUniversalConnectionPoolManager">
		<property name="jmxEnabled" value="false" />
	</bean>

	<bean id="priceSubscriptionDAO"
		  class="com.cmcmarkets.nrg.server.dao.impl.DatabasePriceSubscriptionDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<bean id="fundTradingDAO"
		  class="com.cmcmarkets.nrg.server.dao.impl.DatabaseFundTradingDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- MetaTraderIntroducer DAO -->
	<bean id="metaTraderIntroducerDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseMetaTraderIntroducerDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<bean id="corporateCalendarEventsDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseCorporateCalendarEventsDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Apollo DAO -->
	<bean id="apolloDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseApolloDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Meta Trader Rebates DAO -->
	<bean id="metaTraderRebateDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseMetaTraderRebateDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Trading Suspensions DAO -->
	<bean id="tradingSuspensionsDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseTradingSuspensionsDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Near Realtime Gatherer data source -->
<!--	<bean depends-on="connectionPoolManager" id="nrgDataSource"-->
<!--		class="oracle.ucp.jdbc.PoolDataSourceFactory" factory-method="getPoolDataSource">-->
<!--		<property name="connectionPoolName"-->
<!--			value="${nearRealtimeGatherer.datasource.connectionPoolName}" />-->
<!--		<property name="URL" value="${nearRealtimeGatherer.datasource.url}" />-->
<!--		<property name="user" value="${nearRealtimeGatherer.datasource.user}" />-->
<!--		<property name="password" value="${nearRealtimeGatherer.datasource.password}" />-->
<!--		<property name="connectionFactoryClassName"-->
<!--			value="${nearRealtimeGatherer.datasource.connectionFactoryClassName}" />-->
<!--		<property name="minPoolSize"-->
<!--			value="${nearRealtimeGatherer.datasource.minPoolSize}" />-->
<!--		<property name="maxPoolSize"-->
<!--			value="${nearRealtimeGatherer.datasource.maxPoolSize}" />-->
<!--		<property name="initialPoolSize"-->
<!--			value="${nearRealtimeGatherer.datasource.initialPoolSize}" />-->
<!--		<property name="validateConnectionOnBorrow"-->
<!--			value="${nearRealtimeGatherer.datasource.validateConnectionOnBorrow}" />-->
<!--		<property name="maxStatements"-->
<!--			value="${nearRealtimeGatherer.datasource.maxStatements}" />-->
<!--		<property name="connectionWaitTimeout"-->
<!--			value="${nearRealtimeGatherer.datasource.connectionWaitTimeout}" />-->
<!--		<property name="maxConnectionReuseTime"-->
<!--			value="${nearRealtimeGatherer.datasource.maxConnectionReuseTime}" />-->
<!--		<property name="connectionProperties"-->
<!--		    value="v$session.program:${catalina.instance}" />-->
<!--	</bean>-->

<!--	<bean id="odsDataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">-->
<!--		<property name="driverClassName" value="oracle.jdbc.driver.OracleDriver"/>-->
<!--		<property name="url" value="${nearRealtimeGatherer.datasource.url}" />-->
<!--		<property name="username" value="bi_ods"/>-->
<!--		<property name="password" value="bi_ods"/>-->
<!--	</bean>-->


	<!-- Near Realtime Gatherer data source -->
	<bean id="nrgDataSource"
		  class="oracle.jdbc.pool.OracleDataSource" >
		<property name="URL" value="${nearRealtimeGatherer.datasource.url}" />
		<property name="user" value="${nearRealtimeGatherer.datasource.user}" />
		<property name="password" value="${nearRealtimeGatherer.datasource.password}" />
		<property name="connectionCachingEnabled" value="true" />
		<property name="connectionProperties" ref="nrgProp"/>
		<property name="connectionCacheName" value="dsCache"/>
	</bean>

	<bean id="nrgProp" class="org.springframework.beans.factory.config.PropertiesFactoryBean">

		<property name="properties">
			<props>
				<prop key="MinLimit">10</prop>
				<prop key="MaxLimit">100</prop>
				<prop key="InactivityTimeout">2</prop>
				<prop key="AbandonedConnectionTimeout">2</prop>
				<prop key="PropertyCheckInterval">1</prop>
			</props>
		</property>
	</bean>

	<bean id="odsDataSource" class="oracle.jdbc.pool.OracleDataSource">
		<property name="URL" value="${nearRealtimeGatherer.datasource.url}" />
		<property name="user" value="bi_ods" />
		<property name="password" value="bi_ods" />

		<property name="connectionCachingEnabled" value="true" />
		<property name="connectionProperties" ref="nrgProp"/>
		<property name="connectionCacheName" value="odsCache"/>
	</bean>

	<bean id="identityDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseIdentityDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Transfers DAO -->
	<bean id="transfersDAO" class="com.cmcmarkets.nrg.server.dao.impl.DatabaseTransfersDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Option X Code DAO -->
	<bean id="optionXCodeDAO"
		  class="com.cmcmarkets.nrg.server.dao.impl.DatabaseOptionsDAO">
		<property name="dataSource" ref="nrgDataSource" />
	</bean>

	<!-- Allow for mock injection in tests -->
	<bean class="org.springframework.beans.factory.config.CustomScopeConfigurer">
		<property name="scopes">
			<map>
				<entry key="thread">
					<bean class="org.springframework.context.support.SimpleThreadScope"/>
				</entry>
			</map>
		</property>
	</bean>

</beans>
