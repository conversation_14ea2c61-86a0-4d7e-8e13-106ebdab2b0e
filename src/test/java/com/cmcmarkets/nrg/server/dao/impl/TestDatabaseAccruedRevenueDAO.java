/**
 * This document and its contents are protected by copyright 2021 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * <p>
 * (c) CMC Markets Plc 2021
 */

package com.cmcmarkets.nrg.server.dao.impl;

import com.cmcmarkets.nrg.api.model.impl.NRGAccruedRevenue;
import com.cmcmarkets.priceplan.api.fluent.model.AccountCharge;
import com.cmcmarkets.priceplan.api.fluent.model.Promotion;
import com.cmcmarkets.priceplanmanagement.api.fluent.model.PromotionStats;
import com.cmcmarkets.priceplanmanagement.api.fluent.model.RevenueDetails;
import org.junit.*;
import org.junit.runners.MethodSorters;
import org.springframework.beans.factory.access.BeanFactoryLocator;
import org.springframework.beans.factory.access.BeanFactoryReference;
import org.springframework.context.access.ContextSingletonBeanFactoryLocator;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@FixMethodOrder(MethodSorters.JVM)
public class TestDatabaseAccruedRevenueDAO {
    private static DatabaseAccruedRevenueDAO m_dao;

    Date fixedDate = new Date(1637248144000L);

    private static JdbcTemplate jdbcTemplate;

    @BeforeClass
    public static void initContext() {
        BeanFactoryLocator beanFactoryLocator = ContextSingletonBeanFactoryLocator.getInstance();
        BeanFactoryReference beanFactoryReference = beanFactoryLocator.useBeanFactory("nrgServiceContext");
        m_dao = (DatabaseAccruedRevenueDAO) beanFactoryReference.getFactory().getBean("accruedRevenueDAO");
        jdbcTemplate = (JdbcTemplate) beanFactoryReference.getFactory().getBean("jdbcTemplate");
    }

    @After
    public void tearDown() {
        // remove all the test data
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE where revenue_month_year = 200012");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE where revenue_month_year = 200011");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE where revenue_month_year = 202111");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_H where revenue_month_year = 200012");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_H where revenue_month_year = 200011");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_H where revenue_month_year = 202111");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE where revenue_month_year = 200012");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE where revenue_month_year = 200011");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE where revenue_month_year = 202111");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE_H where revenue_month_year = 200012");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE_H where revenue_month_year = 200011");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE_H where revenue_month_year = 202111");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_PROMOTIONS_APPLIED where PROMOTION_NAME like 'test%'");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_PROMOTIONS_APPLIED_H where PROMOTION_NAME like 'test%'");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS where PROMOTION_DEBIT_ACCOUNT like 'test%'");
        jdbcTemplate.update("delete from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS_H where PROMOTION_DEBIT_ACCOUNT like 'test%'");

    }
    @Test
    public void test_revenue_details_with_account_charges_and_promotions() {
        // can be gathered to test because the intermediate values will be recorded
        // the data are about 200011 and 200012; and account charge 12345
        test_accrued_revenue_create_with_account_charge12_promotion_12();
        test_accrued_revenue_create_with_account_charge12_promotion_12();
        test_accrued_revenue_create_another_with_charge34_promotion34();
        test_accrued_revenue_create_another_with_charge34_promotion34();
        test_accrued_revenue_create_with_account_charge12_promotion_12_changes();
        test_accrued_revenue_create_with_account_charge12_promotion_12_changes();
        test_accrued_revenue_create_with_account_charge35_promotion_35();
        test_accrued_revenue_create_with_account_charge35_promotion_35();

        jdbcTemplate.update("update BI_ODS.ACCRUED_REVENUE_PROMOTIONS_APPLIED set ACCOUNT_CHARGE_ID = 3 where PROMOTIONS_APPLIED_ID = 4");

        // promotion 3 changed, promotion 4 deleted, promotion 6 added,
        // it should be one 'D' in h table for promotion 4, one 'U' for h table for promotion 3,
        // promotion 4 in main table will be deleted, promotion 6 will be inserted
        test_accrued_revenue_create_with_account_charge3_promotion_36();

        Assert.assertEquals("did not insert properly in table accrued_revenue",
                2, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE where REVENUE_MONTH_YEAR IN (200011, 200012)", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_h",
                1, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_H where REVENUE_MONTH_YEAR IN (200011, 200012)", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_account_charge",
                3, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE where REVENUE_MONTH_YEAR IN (200011, 200012)", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_account_charge_h",
                4, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_ACCOUNT_CHARGE_H where REVENUE_MONTH_YEAR IN (200011, 200012)", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotions_applied",
                5, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTIONS_APPLIED WHERE ACCOUNT_CHARGE_ID IN (1,2,3,4,5)", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotions_applied_h",
                4, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTIONS_APPLIED_H WHERE ACCOUNT_CHARGE_ID IN (1,2,3,4,5)", Integer.class));


    }

    @Test
    public void test_revenue_details_with_only_promotion_stats() throws IOException {
        // the changes include
        // 1. same code different amount
        // 2. new code (considered as added)
        // 2. different size between incoming records and existed records (considered as deleted)

        // put same data multiple times, should be no changes and only 2 records
        test_accrued_revenue_create_with_promotion_stats();
        test_accrued_revenue_create_with_promotion_stats();
        test_accrued_revenue_create_with_promotion_stats();
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                2, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS", Integer.class));

        // put same promotion code data but with different amount
        // should be some changes on amount and timestamp and only 2 records
        test_accrued_revenue_create_with_promotion_stats_changed();
        test_accrued_revenue_create_with_promotion_stats_changed();
        test_accrued_revenue_create_with_promotion_stats_changed();
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                2, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                2, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS_H", Integer.class));

        // put two records, one of them is the same promotion code - "test1", another is a new promotion code - "test3"
        test_accrued_revenue_create_with_promotion_stats_different();
        test_accrued_revenue_create_with_promotion_stats_different();
        test_accrued_revenue_create_with_promotion_stats_different();
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                2, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                4, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS_H", Integer.class));

        //
        test_accrued_revenue_create_with_promotion_stats_changed();
        test_accrued_revenue_create_with_promotion_stats_changed();
        test_accrued_revenue_create_with_promotion_stats_changed();
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                2, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS", Integer.class));
        Assert.assertEquals("did not insert properly in table accrued_revenue_promotion_stats",
                6, (int) jdbcTemplate.queryForObject("select count(*) from BI_ODS.ACCRUED_REVENUE_PROMOTION_STATS_H", Integer.class));
    }

    private void test_accrued_revenue_create_with_promotion_stats() {
        RevenueDetails revenueDetails = constructRevenueDetailsWithPromotionStats();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,11,2021)));
    }

    private void test_accrued_revenue_create_with_promotion_stats_changed() {
        RevenueDetails revenueDetails = constructRevenueDetailsWithPromotionStatsAmountChanged();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,11,2021)));
    }

    private void test_accrued_revenue_create_with_promotion_stats_different() {
        RevenueDetails revenueDetails = constructRevenueDetailsWithPromotionStatsDifferent();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,11,2021)));
    }

    private void test_accrued_revenue_create_with_account_charge12_promotion_12() {
        RevenueDetails revenueDetails = constructRevenueDetailsAccountCharges12Promotion12();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,11,2000)));
    }

    private void test_accrued_revenue_create_another_with_charge34_promotion34() {
        RevenueDetails revenueDetails = constructRevenueDetailsAccountCharges34Promotion34();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,12,2000)));
    }

    private void test_accrued_revenue_create_with_account_charge12_promotion_12_changes() {
        RevenueDetails revenueDetails = constructRevenueDetailsAccountCharges12Promotion12Changes();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,11,2000)));
    }

    private void test_accrued_revenue_create_with_account_charge35_promotion_35() {
        // it becomes useless for the promotions
        RevenueDetails revenueDetails = constructRevenueDetailsAccountCharges35Promotion35();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,12,2000)));
    }

    private void test_accrued_revenue_create_with_account_charge3_promotion_36() {
        RevenueDetails revenueDetails = constructRevenueDetailsAccountCharges3Promotion36();
        Date effectiveStartTime = new Date();
        m_dao.putAccruedRevenuesSeparately(Collections.singletonList(new NRGAccruedRevenue(effectiveStartTime, revenueDetails,12,2000)));
    }

    private RevenueDetails constructRevenueDetailsWithPromotionStats() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withPromotionStats(constructPromotionStats1())
                .build();
    }

    private RevenueDetails constructRevenueDetailsWithPromotionStatsAmountChanged() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withPromotionStats(constructPromotionStats2())
                .build();
    }

    private RevenueDetails constructRevenueDetailsWithPromotionStatsDifferent() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withPromotionStats(constructPromotionStats3())
                .build();
    }

    private List<PromotionStats> constructPromotionStats1() {
        List<PromotionStats> promotionStatsList = new ArrayList<>();
        promotionStatsList.add(PromotionStats.builder().withPromotionDebitAccount("test1").withPromotionSum(BigDecimal.valueOf(11)).build());
        promotionStatsList.add(PromotionStats.builder().withPromotionDebitAccount("test2").withPromotionSum(BigDecimal.valueOf(13.666)).build());
        return promotionStatsList;
    }

    private List<PromotionStats> constructPromotionStats2() {
        List<PromotionStats> promotionStatsList = new ArrayList<>();
        promotionStatsList.add(PromotionStats.builder().withPromotionDebitAccount("test1").withPromotionSum(BigDecimal.valueOf(1)).build());
        promotionStatsList.add(PromotionStats.builder().withPromotionDebitAccount("test2").withPromotionSum(BigDecimal.valueOf(1)).build());
        return promotionStatsList;
    }

    private List<PromotionStats> constructPromotionStats3() {
        List<PromotionStats> promotionStatsList = new ArrayList<>();
        promotionStatsList.add(PromotionStats.builder().withPromotionDebitAccount("test1").withPromotionSum(BigDecimal.valueOf(31)).build());
        promotionStatsList.add(PromotionStats.builder().withPromotionDebitAccount("test3").withPromotionSum(BigDecimal.valueOf(33)).build());
        return promotionStatsList;
    }


    private RevenueDetails constructRevenueDetailsAccountCharges12Promotion12() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withAccountCharges(constructAccountChargeList12Promotion12())
                .build();
    }

    private RevenueDetails constructRevenueDetailsAccountCharges34Promotion34() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withAccountCharges(constructAccountChargeList34Promotion34())
                .build();
    }

    private RevenueDetails constructRevenueDetailsAccountCharges12Promotion12Changes() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(2))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(2))
                .withTotalPromotionCharge(BigDecimal.valueOf(2))
                .withAccountCharges(constructAccountCharges12Promotion12Changed())
                .build();
    }

    private RevenueDetails constructRevenueDetailsAccountCharges35Promotion35() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withAccountCharges(constructAccountChargeList35Promotion35())
                .build();
    }

    private RevenueDetails constructRevenueDetailsAccountCharges3Promotion36() {
        return RevenueDetails.builder()
                .withTotalRevenue(BigDecimal.valueOf(1))
                .withStartDateTime(fixedDate)
                .withEndDateTime(fixedDate)
                .withTotalCustomerCharge(BigDecimal.valueOf(1))
                .withTotalPromotionCharge(BigDecimal.valueOf(1))
                .withAccountCharges(constructAccountChargeList3Promotion36())
                .build();
    }

    private List<AccountCharge> constructAccountChargeList12Promotion12(){
        List<AccountCharge> accountCharges = new ArrayList<>();
        accountCharges.add(constructAccountCharge(1L,null,false));
        accountCharges.add(constructAccountCharge(2L,null,false));
        return accountCharges;
    }

    List<AccountCharge> constructAccountChargeList34Promotion34() {
        List<AccountCharge> accountCharges = new ArrayList<>();
        accountCharges.add(constructAccountCharge(3L,null,false));
        accountCharges.add(constructAccountCharge(4L,null,false));
        return accountCharges;
    }

    List<AccountCharge> constructAccountChargeList35Promotion35() {
        List<AccountCharge> accountCharges = new ArrayList<>();
        accountCharges.add(constructAccountCharge(3L,null,false));
        accountCharges.add(constructAccountCharge(5L,5L,false));
        return accountCharges;
    }

    List<AccountCharge> constructAccountChargeList3Promotion36() {
        List<AccountCharge> accountCharges = new ArrayList<>();
        accountCharges.add(constructAccountCharge3WithPromotion36(3L));
        return accountCharges;
    }

    List<AccountCharge> constructAccountCharges12Promotion12Changed() {
        List<AccountCharge> accountCharges = new ArrayList<>();
        accountCharges.add(constructAccountCharge(1L,null,true));
        accountCharges.add(constructAccountCharge(2L,null,true));
        return accountCharges;
    }

    private AccountCharge constructAccountCharge(Long accountChargeId,Long promotionId, boolean changed){
        return AccountCharge.builder()
                .withId(accountChargeId)
                .withTradingAccountId(accountChargeId)
                .withCalculatedCharge(BigDecimal.valueOf(accountChargeId))
                .withPromotionCharge(BigDecimal.valueOf(accountChargeId))
                .withDaysInMonth(Math.toIntExact(accountChargeId))
                .withConversionRate(BigDecimal.valueOf(accountChargeId))
                .withConvertedFromCurrency("CNY")
                .withCurrencySpecificAmount(BigDecimal.valueOf(accountChargeId))
                .withDateCalculated(fixedDate)
                .withEvaluationStartDate(fixedDate)
                .withEvaluationEndDate(fixedDate)
                .withBookingId(accountChargeId)
                .withCustomerEntityId(accountChargeId)
                .withCustomerId(accountChargeId)
                .withLegalEntity(changed ? "LegalEntity_changed" : "LegalEntity")
                .withPromotionsApplied(
                        promotionId == null ?
                                (changed ? constructPromotionsChanged(accountChargeId) : constructPromotions(accountChargeId))
                                : (changed ? constructPromotionsChanged(promotionId) : constructPromotions(promotionId)))
                .build();
    }

    private AccountCharge constructAccountCharge3WithPromotion36(Long accountChargeId){
        return AccountCharge.builder()
                .withId(accountChargeId)
                .withTradingAccountId(accountChargeId)
                .withCalculatedCharge(BigDecimal.valueOf(accountChargeId))
                .withPromotionCharge(BigDecimal.valueOf(accountChargeId))
                .withDaysInMonth(Math.toIntExact(accountChargeId))
                .withConversionRate(BigDecimal.valueOf(accountChargeId))
                .withConvertedFromCurrency("CNY")
                .withCurrencySpecificAmount(BigDecimal.valueOf(accountChargeId))
                .withDateCalculated(fixedDate)
                .withEvaluationStartDate(fixedDate)
                .withEvaluationEndDate(fixedDate)
                .withBookingId(accountChargeId)
                .withCustomerEntityId(accountChargeId)
                .withCustomerId(accountChargeId)
                .withLegalEntity("LegalEntity")
                .withPromotionsApplied(promotion36())
                .build();
    }

    private List<Promotion> promotion36() {
        List<Promotion> promotions = new ArrayList<>();
        promotions.add(
                Promotion.builder()
                        .withId(6L)
                        .withName("test promotion" + 6)
                        .withType("type" + 6)
                        .withStartDate(fixedDate)
                        .withEndDate(fixedDate)
                        .withPromoCode("promotion code" + 6)
                        .withPercentageOff(BigDecimal.valueOf(6))
                        .build()
        );
        promotions.add(
                Promotion.builder()
                        .withId(3L)
                        .withName("test promotion" + 3 +"_changed")
                        .withType("type"+3+"_changed")
                        .withStartDate(fixedDate)
                        .withEndDate(fixedDate)
                        .withPromoCode("promotion code"+3+"_changed")
                        .withPercentageOff(BigDecimal.valueOf(3))
                        .build()
        );
        return promotions;
    }

    private List<Promotion> constructPromotionsChanged(Long id) {
        List<Promotion> promotions = new ArrayList<>();
        promotions.add(
                Promotion.builder()
                        .withId(id)
                        .withName("test promotion" + id +"_changed")
                        .withType("type"+id+"_changed")
                        .withStartDate(fixedDate)
                        .withEndDate(fixedDate)
                        .withPromoCode("promotion code"+id+"_changed")
                        .withPercentageOff(BigDecimal.valueOf(id))
                        .build()
        );
        return promotions;
    }

    private List<Promotion> constructPromotions(Long id) {
        List<Promotion> promotions = new ArrayList<>();
        promotions.add(
                Promotion.builder()
                        .withId(id)
                        .withName("test promotion"+id)
                        .withType("type"+id)
                        .withStartDate(fixedDate)
                        .withEndDate(fixedDate)
                        .withPromoCode("promotion code"+id)
                        .withPercentageOff(BigDecimal.valueOf(id))
                        .build()
        );
        return promotions;
    }

}
