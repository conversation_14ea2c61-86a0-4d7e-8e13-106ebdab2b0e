package com.cmcmarkets.nrg.server.dao.impl;

import com.cmcmarkets.apollo.api.fluent.model.Configuration;
import com.cmcmarkets.nrg.pms.DbrProductCache;
import org.junit.After;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.beans.factory.access.BeanFactoryLocator;
import org.springframework.beans.factory.access.BeanFactoryReference;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.access.ContextSingletonBeanFactoryLocator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;

public class TestDatabaseApolloDAO {

    private static DatabaseApolloDAO dao;
    private static JdbcTemplate jdbcTemplate;
    private static DbrProductCache productCache;

    @BeforeClass
    public static void initContext() {
        BeanFactoryLocator beanFactoryLocator = ContextSingletonBeanFactoryLocator.getInstance();
        BeanFactoryReference beanFactoryReference = beanFactoryLocator.useBeanFactory("nrgServiceContext");
        ClassPathXmlApplicationContext applicationContext = (ClassPathXmlApplicationContext) beanFactoryReference.getFactory();
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory) applicationContext.getBeanFactory();

        // Mock ProductCache
        productCache = mock(DbrProductCache.class);
        
        // Remove existing singleton if it exists
        if (beanFactory.containsSingleton("productCache")) {
            beanFactory.destroySingleton("productCache");
        }
        
        // Register our mock
        beanFactory.registerSingleton("productCache", productCache);
        
        // Get the DAO and jdbcTemplate after registering mock
        dao = (DatabaseApolloDAO) beanFactory.getBean("apolloDAO");
        jdbcTemplate = (JdbcTemplate) beanFactory.getBean("jdbcTemplate");
    }

    @Before
    public void setup() {
        // Any setup code needed before each test
    }

    @Test
    public void shouldPutConfiguration() {
        Configuration config1 = Configuration.builder()
                .withConfigurationId("1")
                .withConfigurationCode("TEST_TENANT_TEMPLATE_CODE")
                .withConfigurationCategory("APPROPRIATENESS")
                .withConfigurationName("Test tenant")
                .build();

        Configuration config2 = Configuration.builder()
                .withConfigurationId("2")
                .withConfigurationCode("TEST_TENANT_TEMPLATE_CODE")
                .withConfigurationCategory("SOPHISTICATED")
                .withConfigurationName("Test tenant")
                .build();

        List<Configuration> configurations = Stream.of(config1, config2).collect(Collectors.toList());

        dao.putConfigurations(configurations);

        assertEquals("Incorrect number of configurations stored", 2,
                (int) jdbcTemplate.queryForObject(
                        "select count(*) from as_configurations where configuration_code = 'TEST_TENANT_TEMPLATE_CODE'",
                        Integer.class));
    }

    @After
    public void tearDown() {
        jdbcTemplate.update("DELETE FROM as_configurations WHERE configuration_code = 'TEST_TENANT_TEMPLATE_CODE'");
    }
}
