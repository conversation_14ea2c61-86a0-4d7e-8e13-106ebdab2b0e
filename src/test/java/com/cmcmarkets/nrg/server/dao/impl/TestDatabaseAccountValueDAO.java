package com.cmcmarkets.nrg.server.dao.impl;

import static org.junit.Assert.assertEquals;

import com.cmcmarkets.customermaster.api.fluent.model.Company;
import com.cmcmarkets.nrg.api.model.AccountValueEntity;
import com.cmcmarkets.nrg.api.model.HistoricAccountValueEntity;
import com.cmcmarkets.nrg.api.model.Platform;
import com.cmcmarkets.nrg.api.model.impl.NRGAccountValue;
import com.cmcmarkets.nrg.api.model.impl.NRGHistoricAccountValue;
import com.cmcmarkets.nrg.api.model.impl.NRGValuedPosition;
import com.cmcmarkets.scheduler.api.model.TaskType;
import com.cmcmarkets.trading.api3.model.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.stream.IntStream;
import org.junit.After;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.beans.factory.access.BeanFactoryLocator;
import org.springframework.beans.factory.access.BeanFactoryReference;
import org.springframework.context.access.ContextSingletonBeanFactoryLocator;
import org.springframework.jdbc.core.JdbcTemplate;

@SuppressWarnings("unused")
public class TestDatabaseAccountValueDAO {

	static DatabaseAccountValueDAO m_dao;
    private static JdbcTemplate jdbcTemplate;

    @BeforeClass
    public static void initContext()
    {
        BeanFactoryLocator beanFactoryLocator = ContextSingletonBeanFactoryLocator.getInstance();
        BeanFactoryReference beanFactoryReference = beanFactoryLocator.useBeanFactory("nrgServiceContext");
        m_dao = (DatabaseAccountValueDAO) beanFactoryReference.getFactory().getBean("databaseAccountValueDAO");
        jdbcTemplate = (JdbcTemplate) beanFactoryReference.getFactory().getBean("jdbcTemplate");
    }

    @After
    public void tearDown() {
        jdbcTemplate.execute("DELETE FROM bi_ods.hstrc_acnt_vl_daily_fees where created_by = 'test'");
        jdbcTemplate.execute("DELETE FROM bi_ods.hstrc_acnt_vl_daily_fees_h where created_by = 'test'");
        jdbcTemplate.execute("DELETE FROM bi_ods.account_value_snapshots where created_by = 'test'");
    }
    
	@Test
    public void testPutValuedPosition() {
        
        GregorianCalendar calendar = new GregorianCalendar(2021,4,30);

        GregorianCalendar calendarForDateOfIncorporation = new GregorianCalendar(2021,4,30);
        
        GregorianCalendar calendarForLegalEntityidentifierExpiry = new GregorianCalendar(2021,5,2);

        Company company = Company.builder()
                .withLegalEntityIdentifierExpiry(calendarForLegalEntityidentifierExpiry.getTime()).build();
        
        boolean isDeleted = false;
        
        List eodValuedPositions = new ArrayList();
        List eodValuedPositions2 = new ArrayList();

        DefaultValuedPosition position = new DefaultValuedPosition();
        position.setPmsStandardMargin(new BigDecimal(123));
        position.setCustomerLevel1AskPrice(new BigDecimal(2));
        position.setTradingAccountCode(189916l);
        position.setProductInstrumentCode("A-AUMK");
        position.setProductWrapperCode("A-EOVH");

        List<ValuedOpenTrade> VOT = new ArrayList<ValuedOpenTrade>();
        DefaultValuedOpenTrade openTrade = new DefaultValuedOpenTrade();
        openTrade.setOpeningTradeId("99990");
        openTrade.setOrderId("O01003A000000000A7R9");
        openTrade.setAccruedFeesInInstrumentCurrency(new BigDecimal(2));
        openTrade.setCustomerId(99990L);
        openTrade.setOpeningTradeId("A-AUMK");
        openTrade.setOptionMarginEvaluationFxRevalRate(BigDecimal.valueOf(78391));
        openTrade.setOptionEvaluationUnderlyingPrice(BigDecimal.valueOf(54321));
        BigDecimal openTradeMarginRebate = generateRandomBigDecimal();
        openTrade.setMarginRebate(openTradeMarginRebate);
        VOT.add(openTrade);

        position.getValuedOpenTrades().add(openTrade);
        position.setPrimeMarginEvaluationPrice(BigDecimal.valueOf(********));
        position.setPrimeMarginEvaluationFxRevalRate(BigDecimal.valueOf(21));
        position.setExecutedOptionMargin(BigDecimal.valueOf(********));
        BigDecimal positionMarginRebate = generateRandomBigDecimal();
        position.setMarginRebate(positionMarginRebate);
        position.setNetDirection(Direction.BUY);
        BigDecimal standardNetQuantity = generateRandomBigDecimal();
        position.setStandardNetQuantity(standardNetQuantity);
        BigDecimal primeGrossQuantity = generateRandomBigDecimal();
        position.setPrimeGrossQuantity(primeGrossQuantity);
        position.setMarginDirection(Direction.BUY);
        BigDecimal marginQuantity = generateRandomBigDecimal();
        position.setMarginQuantity(marginQuantity);
        BigDecimal marginRebateQuantity = generateRandomBigDecimal();
        position.setMarginRebateQuantity(marginRebateQuantity);
        String strategyId = "TEST-P-98705";
        position.setStrategyId(strategyId);
        StrategyType strategyType = StrategyType.VERTICALSPREAD;
        position.setStrategyType(strategyType);

        NRGValuedPosition valuedPosition = new NRGValuedPosition(calendar.getTime(), position, true);

        GregorianCalendar calendar2 = new GregorianCalendar(2021,5,10);

        NRGValuedPosition valuedPosition2 = new NRGValuedPosition(calendar2.getTime(), position, true);

        eodValuedPositions.add(valuedPosition);
        eodValuedPositions2.add(valuedPosition2);

        m_dao.putValuedPositions("ociobanu", calendar.getTime(), Platform.NG, eodValuedPositions, TaskType.LOAD_NEXT_GEN_VALUED_POSITIONS.name());
        m_dao.putValuedPositions("ociobanu", calendar.getTime(), Platform.NG, eodValuedPositions2, TaskType.LOAD_NEXT_GEN_VALUED_POSITIONS.name());
        m_dao.putValuedPositions("ociobanu", calendar.getTime(), Platform.NG, eodValuedPositions, TaskType.LOAD_NEXT_GEN_LATEST_VALUED_POSITIONS.name());


        Integer checkPositionRecord = jdbcTemplate.queryForObject("SELECT p.prime_margin_evaluation_price FROM position_value_snapshots p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(********,(int)checkPositionRecord);

        Integer checkPositionRecord2 = jdbcTemplate.queryForObject("SELECT p.prime_margin_evaluation_reval_rate FROM position_value_snapshots p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(21,(int)checkPositionRecord2);

        Integer checkPositionRecord_h = jdbcTemplate.queryForObject("SELECT p.prime_margin_evaluation_price FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(********,(int)checkPositionRecord_h);

        Integer checkOpenTradeRecord = jdbcTemplate.queryForObject("SELECT p.option_evaluation_underlying_price FROM position_valued_open_trades p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(54321,(int)checkOpenTradeRecord);

        Integer checkOpenTradeRecord2 = jdbcTemplate.queryForObject("SELECT p.option_margin_evaluation_reval_rate FROM position_valued_open_trades p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(78391,(int)checkOpenTradeRecord2);

        Integer checkOpenTradeRecord_h = jdbcTemplate.queryForObject("SELECT p.option_evaluation_underlying_price FROM position_valued_open_trades_h p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(54321,(int)checkOpenTradeRecord);

        Integer checkLatestOpenTradeRecord2 = jdbcTemplate.queryForObject("SELECT p.option_margin_evaluation_reval_rate FROM latest_valued_open_trades p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(78391,(int)checkLatestOpenTradeRecord2);

        Integer checkLatestPositionRecord2 = jdbcTemplate.queryForObject("SELECT p.prime_margin_evaluation_reval_rate FROM latest_valued_positions p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(21,(int)checkLatestPositionRecord2);

        Integer checkExecutedOptionMargin_PVS = jdbcTemplate.queryForObject("SELECT p.executed_option_margin FROM position_value_snapshots p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(********,(int)checkExecutedOptionMargin_PVS);

        Integer checkExecutedOptionMargin_PVS_H = jdbcTemplate.queryForObject("SELECT p.executed_option_margin FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(********,(int)checkExecutedOptionMargin_PVS_H);

        Integer checkExecutedOptionMargin_LVP = jdbcTemplate.queryForObject("SELECT p.executed_option_margin FROM latest_valued_positions p WHERE p.trading_account_id = 189916",Integer.class);
        assertEquals(********,(int)checkExecutedOptionMargin_LVP);

        BigDecimal marginRebatePVOT = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM position_valued_open_trades p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(openTradeMarginRebate, marginRebatePVOT);

        BigDecimal marginRebatePVOT_H = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM position_valued_open_trades_h p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(openTradeMarginRebate, marginRebatePVOT_H);

        BigDecimal marginRebateLVOT = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM latest_valued_open_trades p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(openTradeMarginRebate, marginRebateLVOT);

        BigDecimal marginRebatePVS = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM position_value_snapshots p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(positionMarginRebate, marginRebatePVS);

        BigDecimal marginRebatePVS_H = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(positionMarginRebate, marginRebatePVS_H);

        BigDecimal marginRebateLVP = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM latest_valued_positions p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(positionMarginRebate, marginRebateLVP);

        String netDirectionPVS = jdbcTemplate.queryForObject("SELECT p.net_direction FROM position_value_snapshots p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("BUY", netDirectionPVS);

        String netDirectionPVS_H = jdbcTemplate.queryForObject("SELECT p.net_direction FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("BUY", netDirectionPVS_H);

        String netDirectionLVP = jdbcTemplate.queryForObject("SELECT p.net_direction FROM latest_valued_positions p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("BUY", netDirectionLVP);

        BigDecimal standardNetQuantityPVS = jdbcTemplate.queryForObject("SELECT p.standard_net_quantity FROM position_value_snapshots p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(standardNetQuantity, standardNetQuantityPVS);

        BigDecimal standardNetQuantityPVS_H = jdbcTemplate.queryForObject("SELECT p.standard_net_quantity FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(standardNetQuantity, standardNetQuantityPVS_H);

        BigDecimal standardNetQuantityLVP = jdbcTemplate.queryForObject("SELECT p.standard_net_quantity FROM latest_valued_positions p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(standardNetQuantity, standardNetQuantityLVP);

        BigDecimal primeGrossQuantityPVS = jdbcTemplate.queryForObject("SELECT p.prime_gross_quantity FROM position_value_snapshots p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(primeGrossQuantity, primeGrossQuantityPVS);

        BigDecimal primeGrossQuantityPVS_H = jdbcTemplate.queryForObject("SELECT p.prime_gross_quantity FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(primeGrossQuantity, primeGrossQuantityPVS_H);

        BigDecimal primeGrossQuantityLVP = jdbcTemplate.queryForObject("SELECT p.prime_gross_quantity FROM latest_valued_positions p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(primeGrossQuantity, primeGrossQuantityLVP);

        String marginDirectionPVS = jdbcTemplate.queryForObject("SELECT p.margin_direction FROM position_value_snapshots p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("BUY", marginDirectionPVS);

        String marginDirectionPVS_H = jdbcTemplate.queryForObject("SELECT p.margin_direction FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("BUY", marginDirectionPVS_H);

        String marginDirectionLVP = jdbcTemplate.queryForObject("SELECT p.margin_direction FROM latest_valued_positions p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("BUY", marginDirectionLVP);

        BigDecimal marginQuantityPVS = jdbcTemplate.queryForObject("SELECT p.margin_quantity FROM position_value_snapshots p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(marginQuantity, marginQuantityPVS);

        BigDecimal marginQuantityPVS_H = jdbcTemplate.queryForObject("SELECT p.margin_quantity FROM position_value_snapshots_h p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(marginQuantity, marginQuantityPVS_H);

        BigDecimal marginQuantityLVP = jdbcTemplate.queryForObject("SELECT p.margin_quantity FROM latest_valued_positions p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(marginQuantity, marginQuantityLVP);

        BigDecimal marginRebateQuantityPVS = jdbcTemplate.queryForObject("SELECT p.margin_rebate_quantity FROM position_value_snapshots p WHERE p.trading_account_id = 189916",BigDecimal.class);
        assertEquals(marginRebateQuantity, marginRebateQuantityPVS);

        String strategyIdExpected = jdbcTemplate.queryForObject("SELECT p.strategy_id FROM position_value_snapshots p WHERE p.trading_account_id = 189916",String.class);
        assertEquals("TEST-P-98705", strategyId);

        String strategyTypeExpected = jdbcTemplate.queryForObject("SELECT p.strategy_type FROM position_value_snapshots p WHERE p.trading_account_id = 189916",String.class);
        assertEquals(StrategyType.VERTICALSPREAD.name(), strategyTypeExpected);
    }
	
	@Test
	public void testPutHistoricAccountValue() {
		
		GregorianCalendar calendar = new GregorianCalendar(2021,4,22);
		
		DefaultHistoricAccountValue entity = new DefaultHistoricAccountValue();
		
		entity.setTradingAccountCode(123l);
		
		List<InvestmentFreeEquity> freeEquities = new ArrayList<InvestmentFreeEquity>();
		
		DefaultInvestmentFreeEquity fe = new DefaultInvestmentFreeEquity();
		fe.setCurrency("USD");
		fe.setFreeEquity(new BigDecimal(123));
		freeEquities.add(fe);
		entity.setInvestmentFreeEquities(freeEquities);
		
		List<HistoricSecondaryCashBalance> secondaryCashBalances = new ArrayList<HistoricSecondaryCashBalance>();
		
		DefaultHistoricSecondaryCashBalance secondaryCashBalance = new DefaultHistoricSecondaryCashBalance();
		secondaryCashBalance.setBalance(new BigDecimal(423l));
		secondaryCashBalance.setCurrency("USD");
		secondaryCashBalance.setUnsettledBalance(new BigDecimal(426l));
		
		secondaryCashBalances.add(secondaryCashBalance);
		
		entity.setSecondaryCashBalances(secondaryCashBalances);
		entity.setUnsettledCashInPrimaryCurrency(new BigDecimal(568l));
		
		NRGHistoricAccountValue historicAccountValue = new NRGHistoricAccountValue(new Date(), entity, true);
		
		List<HistoricAccountValueEntity> eodHistoricAccountValues = new ArrayList<HistoricAccountValueEntity>();
		eodHistoricAccountValues.add(historicAccountValue);
		
		m_dao.putHistoricAccountValues("ociobanu", calendar.getTime(), Platform.NG, eodHistoricAccountValues);
	}

    @Test
    public void testPutHistoricAccountValue_ExecutedOptionMargin() throws InterruptedException {

        GregorianCalendar calendar = new GregorianCalendar(2021,4,22);

        DefaultHistoricAccountValue entity = new DefaultHistoricAccountValue();
        entity.setTradingAccountCode(********L);
        entity.setExecutedOptionMargin(new BigDecimal(568l));

        NRGHistoricAccountValue historicAccountValue = new NRGHistoricAccountValue(new Date(), entity, true);

        m_dao.putHistoricAccountValues("ociobanu", calendar.getTime(), Platform.NG, Collections.singletonList(historicAccountValue));

        Integer historicAccountValueRecord = jdbcTemplate.queryForObject("SELECT p.executed_option_margin FROM historic_account_value p WHERE p.trading_account_id = ********",Integer.class);
        assertEquals(568,(int)historicAccountValueRecord);

    }

    @Test
    public void testPutHistoricAccountValue_MarginRebate() throws InterruptedException {

        GregorianCalendar calendar = new GregorianCalendar(2024, Calendar.SEPTEMBER,9);

        DefaultHistoricAccountValue entity = new DefaultHistoricAccountValue();
        entity.setTradingAccountCode(7654321L);
        entity.setMarginRebate(new BigDecimal(568l));

        NRGHistoricAccountValue historicAccountValue = new NRGHistoricAccountValue(new Date(), entity, true);

        m_dao.putHistoricAccountValues("scheung", calendar.getTime(), Platform.NG, Collections.singletonList(historicAccountValue));

        Integer historicAccountValueRecord = jdbcTemplate.queryForObject("SELECT p.margin_rebate FROM historic_account_value p WHERE p.trading_account_id = 7654321",Integer.class);
        assertEquals(568,(int)historicAccountValueRecord);

    }



    @Test
    public void testPutAccountValueWithDailyFees() {

        /*
        * The duplication is judged by the snapshot time field in db
        * This snapshot time filed is from the parameter
        *
        * */

        // Given a new list of account values with daily fees

        // When
        Date effectiveStartTime = new Date(2024, Calendar.MAY,22,0,0,0);
        m_dao.putAccountValues("test", effectiveStartTime, Platform.NG, constructAccountValuesWithDailyFees(3, false));
        // Then
        assertEquals(3,
                (int) jdbcTemplate.queryForObject("SELECT count(*) FROM bi_ods.hstrc_acnt_vl_daily_fees WHERE CREATED_BY  = 'test'",
                        Integer.class));

        // Given a same list of account values with changed daily fees

        // When
        m_dao.putAccountValues("test", effectiveStartTime, Platform.NG, constructAccountValuesWithDailyFees(3, true));
        // Then
        assertEquals(3,
                (int) jdbcTemplate.queryForObject("SELECT count(*) FROM bi_ods.hstrc_acnt_vl_daily_fees WHERE CREATED_BY  = 'test'",
                        Integer.class));
        assertEquals(3,
                (int) jdbcTemplate.queryForObject("SELECT count(*) FROM bi_ods.hstrc_acnt_vl_daily_fees_h WHERE CREATED_BY  = 'test'",
                        Integer.class));

    }

    private List<AccountValueEntity> constructAccountValuesWithDailyFees(int number, boolean modified) {
        Date effective_start_time = new Date();
        List<AccountValueEntity> accountValues = new ArrayList<>();
        IntStream.range(0, number).forEach(i -> {
            DefaultAccountValue accountValue = new DefaultAccountValue();
            accountValue.setTradingAccountCode(12345L + i);
            accountValue.setDailyFees(constructDailyFees(1, modified));
            accountValue.setTime(1716367940350L);
            accountValues.add(new NRGAccountValue(effective_start_time, accountValue, true));
        });
        return accountValues;
    }

    private List<DailyFee> constructDailyFees(int number, boolean modified) {
        List<DailyFee> dailyFees = new ArrayList<>();
        IntStream.range(0, number).forEach(i -> {
            DefaultDailyFee dailyFee = new DefaultDailyFee();
            dailyFee.setType(DailyFeeType.valueOf(1));
            dailyFee.setCurrency(modified ? "USD_" + i : "M_USD_" + i);
            dailyFee.setAmount(modified ? BigDecimal.TEN : BigDecimal.ZERO);
            dailyFees.add(dailyFee);
        });
        return dailyFees;
    }

    /*
    * This method is used to generate a random BigDecimal value
    * between 1000 and 1000000
     */
    private BigDecimal generateRandomBigDecimal() {
        double randomDouble = Math.random() * 999000 + 1000;
        return BigDecimal.valueOf(randomDouble);
    }
}
