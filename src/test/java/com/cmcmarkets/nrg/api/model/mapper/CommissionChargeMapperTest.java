package com.cmcmarkets.nrg.api.model.mapper;

import com.cmcmarkets.cashaccount.api.model.CommissionType;
import com.cmcmarkets.cashaccount.api.model.DefaultCommissionChargeCustomDataDto;
import com.cmcmarkets.cashaccount.api.model.DefaultFxRevalRateInfoDto;
import com.cmcmarkets.cashaccount.api.model.DefaultSecondaryCurrencyConversionRevalRateInfoDto;
import com.cmcmarkets.cashaccount.api.model.ExecutionType;
import com.cmcmarkets.nrg.api.model.CommissionChargeData;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

public class CommissionChargeMapperTest {

    private final CommissionChargeMapper converter = CommissionChargeMapper.getInstance();
    private final Date date = new Date();
    private final BigDecimal decimal = new BigDecimal(1234);

    @Test
    public void testNullRequestObject() {
        CommissionChargeData customData = converter.map(null);
        assertNull(customData);
    }

    @Test
    public void testCommissionChargeDto() {
        CommissionChargeData customData = converter.map(createCommissionChargeDto(true));
        assertNotNull(customData);
        assertEquals("InstCode", customData.getInstrumentCode());
        assertEquals("Wrapper Code", customData.getProductWrapperCode());
        assertEquals(com.cmcmarkets.nrg.api.model.ExecutionType.DIRECT_MARKET_ACCESS, customData.getExecutionType());
        assertEquals("123456", customData.getOrderId());
        assertEquals("TAC", customData.getTradingAccountCode());
        assertEquals("TACD", customData.getTradingAccountCodifier());
        assertEquals("GBP", customData.getTradingAccountCurrency());
        assertEquals("GBP", customData.getCommissionCurrency());
        assertEquals(decimal, customData.getAmountToChargeInCommissionCurrency());
        assertEquals(decimal, customData.getFxRate());
        assertEquals(decimal, customData.getMatchedQuantity());
        assertEquals(decimal, customData.getTradePrice());
        assertEquals("SrcId", customData.getSourceOrderId());
        assertEquals(decimal, customData.getTotalAmountToChargeInCommissionCurrency());

    }

    @Test
    public void testCommissionChargeDtoNoQuantityDesignatorNoExecutionType() {
        CommissionChargeData customData = converter.map(createCommissionChargeDto(false));
        assertNotNull(customData);
        assertEquals("InstCode", customData.getInstrumentCode());
        assertEquals("Wrapper Code", customData.getProductWrapperCode());
        assertNull(customData.getExecutionType());
        assertEquals("123456", customData.getOrderId());
        assertEquals("TAC", customData.getTradingAccountCode());
        assertEquals("TACD", customData.getTradingAccountCodifier());
        assertEquals("GBP", customData.getTradingAccountCurrency());
        assertEquals("GBP", customData.getCommissionCurrency());
        assertEquals(decimal, customData.getAmountToChargeInCommissionCurrency());
        assertEquals(decimal, customData.getFxRate());
        assertEquals(decimal, customData.getMatchedQuantity());
        assertEquals(decimal, customData.getTradePrice());
        assertNull(customData.getQuantityDesignator());
        assertEquals("SrcId", customData.getSourceOrderId());
        assertEquals(decimal, customData.getTotalAmountToChargeInCommissionCurrency());

    }

    private DefaultCommissionChargeCustomDataDto createCommissionChargeDto(boolean addEnums) {
        DefaultCommissionChargeCustomDataDto customDataDto = new DefaultCommissionChargeCustomDataDto();
        customDataDto.setInstrumentCode("InstCode");
        customDataDto.setProductWrapperCode("Wrapper Code");
        customDataDto.setOrderId("123456");
        customDataDto.setTradingAccountCode("TAC");
        customDataDto.setTradingAccountCodifier("TACD");
        customDataDto.setTradingAccountCurrency("GBP");
        customDataDto.setCommissionCurrency("GBP");
        customDataDto.setAmountToChargeInCommissionCurrency(decimal);
        customDataDto.setFxRate(decimal);
        customDataDto.setMatchedQuantity(decimal);
        if (addEnums) {
            customDataDto.setExecutionType(ExecutionType.DIRECTMARKETACCESS);
        }
        customDataDto.setTradePrice(decimal);
        customDataDto.setTradeTime(date);
        customDataDto.setSourceOrderId("SrcId");
        customDataDto.setTotalAmountToChargeInCommissionCurrency(decimal);

        return customDataDto;
    }

    @Test
    public void testCommissionChargeDtoWithAllFields() {
        DefaultCommissionChargeCustomDataDto customDataDto = new DefaultCommissionChargeCustomDataDto();
        // Set basic fields
        customDataDto.setInstrumentCode("InstCode");
        customDataDto.setProductWrapperCode("Wrapper Code");
        customDataDto.setOrderId("123456");
        customDataDto.setTradingAccountCode("TAC");
        customDataDto.setTradingAccountCodifier("TACD");
        customDataDto.setTradingAccountCurrency("GBP");
        customDataDto.setCommissionCurrency("GBP");
        customDataDto.setExecutionType(ExecutionType.DIRECTMARKETACCESS);
        
        // Set numeric values
        customDataDto.setAmountToChargeInCommissionCurrency(decimal);
        customDataDto.setFxRate(decimal);
        customDataDto.setMatchedQuantity(decimal);
        customDataDto.setTradePrice(decimal);
        
        customDataDto.setTotalAmountToChargeInCommissionCurrency(decimal);
        
        // Set additional fields
        customDataDto.setTradeTime(date);
        customDataDto.setSourceOrderId("SrcId");
        customDataDto.setCommissionSchemaCode("SchemaCode");
        
        CommissionType inputCommissionType = CommissionType.BASISPOINTS;
        customDataDto.setCommissionType(inputCommissionType);
        
        customDataDto.setCommission(decimal);
        customDataDto.setMinimumCommission(decimal);
        customDataDto.setTotalMatchedQuantity(decimal);
        customDataDto.setCommissionLevel("Level1");
        
        // Set tax-related fields
        customDataDto.setTaxCountry("UK");
        customDataDto.setTaxName("VAT");
        customDataDto.setTaxRate(new BigDecimal("0.20"));
        customDataDto.setTaxLiabilityCurrency("GBP");
        customDataDto.setTaxAmountInAccountCurrency(new BigDecimal("10.00"));
        customDataDto.setTaxAmountInTaxLiabilityCurrency(new BigDecimal("10.00"));
        
        // Set amount in account currency
        customDataDto.setAmountToChargeInAccountCurrency(new BigDecimal("50.00"));
        
        // Create FX revaluation rate info for tax
        DefaultFxRevalRateInfoDto taxFxRevalRate = new DefaultFxRevalRateInfoDto();
        taxFxRevalRate.setRate(new BigDecimal("1.25"));
        taxFxRevalRate.setFxPair("GBP/USD");
        customDataDto.setTaxAmountFxRevalRate(taxFxRevalRate);
        
        // Map and verify
        CommissionChargeData result = converter.map(customDataDto);
        
        // Basic assertions
        assertNotNull(result);
        assertEquals("InstCode", result.getInstrumentCode());
        assertEquals("Wrapper Code", result.getProductWrapperCode());
        assertEquals(com.cmcmarkets.nrg.api.model.ExecutionType.DIRECT_MARKET_ACCESS, result.getExecutionType());
        
        // Verify additional fields
        assertEquals(date, result.getTradeTime());
        assertEquals("SchemaCode", result.getCommissionSchemaCode());

        assertNotNull("CommissionType should not be null", result.getCommissionType());
        assertEquals("CommissionType should be correctly mapped", 
                     inputCommissionType, 
                     result.getCommissionType());
        
        assertEquals("CommissionType value should be " + CommissionType.BASISPOINTS,
                     CommissionType.BASISPOINTS.name(),
                     result.getCommissionType().toString());
        
        assertEquals(decimal, result.getCommission());
        assertEquals(decimal, result.getMinimumCommission());
        assertEquals(decimal, result.getTotalMatchedQuantity());
        assertEquals("Level1", result.getCommissionLevel());
        
        // Verify tax-related fields
        assertEquals("UK", result.getTaxCountry());
        assertEquals("VAT", result.getTaxName());
        assertEquals(new BigDecimal("0.20"), result.getTaxRate());
        assertEquals("GBP", result.getTaxLiabilityCurrency());
        assertEquals(new BigDecimal("10.00"), result.getTaxAmountInAccountCurrency());
        assertEquals(new BigDecimal("10.00"), result.getTaxAmountInTaxLiabilityCurrency());
        
        // Verify FX revaluation rate for tax
        assertNotNull(result.getTaxAmountFxRevalRate());
        assertEquals(new BigDecimal("1.25"), result.getTaxAmountFxRevalRate().getRate());
        assertEquals("GBP/USD", result.getTaxAmountFxRevalRate().getFxPair());
        
        // Verify amount in account currency
        assertEquals(new BigDecimal("50.00"), result.getAmountToChargeInAccountCurrency());
    }

    @Test
    public void testCommissionChargeDtoWithSecondaryCurrencyConversion() {
        DefaultCommissionChargeCustomDataDto customDataDto = createCommissionChargeDto(true);
        
        // Create secondary currency conversion info
        DefaultSecondaryCurrencyConversionRevalRateInfoDto secondaryInfo = new DefaultSecondaryCurrencyConversionRevalRateInfoDto();
        secondaryInfo.setRate(new BigDecimal("1.5"));
        secondaryInfo.setSourceCurrency("EUR");
        secondaryInfo.setTargetCurrency("USD");
        
        // Set secondary to primary FX rate directly on the DTO
        customDataDto.setSecondaryToPrimaryFxRate(new BigDecimal("1.2"));
    }
}
