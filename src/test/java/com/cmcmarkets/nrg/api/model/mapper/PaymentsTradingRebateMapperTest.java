package com.cmcmarkets.nrg.api.model.mapper;

import com.cmcmarkets.cashaccount.api.model.PaymentsTradingRebateCustomDataDto;
import com.cmcmarkets.cashaccount.api.model.PaymentsTradingRebateCustomDataInstrumentDto;
import com.cmcmarkets.nrg.api.model.PaymentsTradingRebate;
import org.junit.Test;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

public class PaymentsTradingRebateMapperTest {

    private final PaymentsTradingRebateCustomDataMapper converter = PaymentsTradingRebateCustomDataMapper.getInstance();

    @Test
    public void testNullRequestObject() {
        PaymentsTradingRebate customData = converter.map(null);
        assertNull(customData);
    }

    @Test
    public void testPaymentsTradingRebateDto() {
        PaymentsTradingRebate customData = converter.map(createPaymentsTradingRebateDto());
        assertNotNull(customData);
        assertEquals("TACD", customData.getTradingAccountCodifier());
        assertEquals(new Long(12345), customData.getTradingAccountCode());
        assertEquals("PCD", customData.getPaymentCodifier());
        assertEquals("PC", customData.getPaymentCode());
        assertEquals(3, customData.getInstruments().getInstrument().size());
        BigDecimal decimal = new BigDecimal(1234);
        for(int i = 0; i < 3; i++) {
            assertEquals("InstCode", customData.getInstruments().getInstrument().get(i).getCode());
            assertEquals("GBP", customData.getInstruments().getInstrument().get(i).getProductCurrency());
            assertEquals(decimal, customData.getInstruments().getInstrument().get(i).getPaymentAmountFxRevalRate());
            assertEquals(decimal, customData.getInstruments().getInstrument().get(i).getPaymentAmountInProductCurrency());
            assertEquals(decimal, customData.getInstruments().getInstrument().get(i).getPaymentAmountInTradingAccountPrimaryCurrency());
            assertEquals(decimal, customData.getInstruments().getInstrument().get(i).getTotalTradeAmountInProductCurrency());
            decimal.add(new BigDecimal(1));
        }

    }

    private PaymentsTradingRebateCustomDataDto createPaymentsTradingRebateDto() {
        PaymentsTradingRebateCustomDataDto dataDto = Mockito.mock(PaymentsTradingRebateCustomDataDto.class);
        List<PaymentsTradingRebateCustomDataInstrumentDto> list = createPaymentsTradingRebateInstrumentList();
        Mockito.when(dataDto.getOBSOLETEInstruments()).thenReturn(list);
        Mockito.when(dataDto.getTradingAccountCodifier()).thenReturn("TACD");
        Mockito.when(dataDto.getTradingAccountCode()).thenReturn("12345");
        Mockito.when(dataDto.getPaymentCodifier()).thenReturn("PCD");
        Mockito.when(dataDto.getPaymentCode()).thenReturn("PC");
        return dataDto;
    }

    private List<PaymentsTradingRebateCustomDataInstrumentDto> createPaymentsTradingRebateInstrumentList() {
        List<PaymentsTradingRebateCustomDataInstrumentDto> list = new ArrayList<>();
        BigDecimal decimal = new BigDecimal(1234);
        for(int i = 0; i < 3; i++) {
            PaymentsTradingRebateCustomDataInstrumentDto instrumentDto = Mockito.mock(PaymentsTradingRebateCustomDataInstrumentDto.class);
            Mockito.when(instrumentDto.getCode()).thenReturn("InstCode");
            Mockito.when(instrumentDto.getTotalTradeAmountInProductCurrency()).thenReturn(decimal);
            Mockito.when(instrumentDto.getPaymentAmountInProductCurrency()).thenReturn(decimal);
            Mockito.when(instrumentDto.getPaymentAmountInTradingAccountPrimaryCurrency()).thenReturn(decimal);
            Mockito.when(instrumentDto.getProductCurrency()).thenReturn("GBP");
            Mockito.when(instrumentDto.getPaymentAmountFxRevalRate()).thenReturn(decimal);
            list.add(instrumentDto);
            decimal.add(new BigDecimal(1));
        }
        return list;
    }
}