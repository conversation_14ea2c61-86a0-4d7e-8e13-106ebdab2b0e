<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <xs:element name="TradeCustomData">
    <xs:complexType>
      <xs:all>
        <xs:element minOccurs="0" ref="InstrumentCode"/>
        <xs:element minOccurs="0" ref="ProductWapperCode"/>
        <xs:element minOccurs="0" ref="ClientOrderId"/>
        <xs:element minOccurs="0" ref="PassiveOrderId"/>
        <xs:element minOccurs="0" ref="TradeId"/>
        <xs:element minOccurs="0" ref="CP1TradingAccountCode"/>
        <xs:element minOccurs="0" ref="CP1TradingAccountCodifier"/>
        <xs:element minOccurs="0" ref="CP2TradingAccountCode"/>
        <xs:element minOccurs="0" ref="CP2TradingAccountCodifier"/>
        <xs:element minOccurs="0" ref="CP1TradingAccountPrimaryCurrency"/>
        <xs:element minOccurs="0" ref="MarginAmount"/>
        <xs:element minOccurs="0" ref="MarginAmountCurrency"/>
        <xs:element minOccurs="0" ref="MarginAmountFxRevalRate"/>
        <xs:element minOccurs="0" ref="ProfitLossAmount"/>
        <xs:element minOccurs="0" ref="ProfitLossAmountCurrency"/>
        <xs:element minOccurs="0" ref="ProfitLossAmountFxRevalRate"/>
        <xs:element minOccurs="0" ref="MarginAmountInCP1TradingAccountPrimaryCurrency"/>
        <xs:element minOccurs="0" ref="ProfitLossAmountInCP1TradingAccountPrimaryCurrency"/>
      </xs:all>
    </xs:complexType>
  </xs:element>
  <xs:element name="InstrumentCode" type="xs:string"/>
  <xs:element name="ProductWapperCode" type="xs:string"/>
  <xs:element name="ClientOrderId" type="xs:string"/>
  <xs:element name="PassiveOrderId" type="xs:string"/>
  <xs:element name="TradeId" type="xs:string"/>
  <xs:element name="CP1TradingAccountCode" type="xs:long"/>
  <xs:element name="CP1TradingAccountCodifier" type="xs:string"/>
  <xs:element name="CP2TradingAccountCode" type="xs:long"/>
  <xs:element name="CP2TradingAccountCodifier" type="xs:string"/>
  <xs:element name="CP1TradingAccountPrimaryCurrency" type="xs:string"/>
  <xs:element name="MarginAmount" type="xs:decimal"/>
  <xs:element name="MarginAmountCurrency" type="xs:string"/>
  <xs:element name="MarginAmountFxRevalRate" type="xs:decimal"/>
  <xs:element name="ProfitLossAmount" type="xs:decimal"/>
  <xs:element name="ProfitLossAmountCurrency" type="xs:string"/>
  <xs:element name="ProfitLossAmountFxRevalRate" type="xs:decimal"/>
  <xs:element name="MarginAmountInCP1TradingAccountPrimaryCurrency" type="xs:decimal"/>
  <xs:element name="ProfitLossAmountInCP1TradingAccountPrimaryCurrency" type="xs:decimal"/>
</xs:schema>
