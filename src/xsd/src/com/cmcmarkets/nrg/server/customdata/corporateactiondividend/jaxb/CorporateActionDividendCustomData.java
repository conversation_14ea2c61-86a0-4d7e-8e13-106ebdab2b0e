//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vJAXB 2.1.10 in JDK 6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2012.03.01 at 03:49:02 PM GMT 
//


package com.cmcmarkets.nrg.server.customdata.corporateactiondividend.jaxb;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;all>
 *         &lt;element ref="{}Id" minOccurs="0"/>
 *         &lt;element ref="{}InstrumentCode" minOccurs="0"/>
 *         &lt;element ref="{}InstrumentCurrency" minOccurs="0"/>
 *         &lt;element ref="{}InstrumentCurrencyFractionalPartRatio" minOccurs="0"/>
 *         &lt;element ref="{}ProductWrapperCode" minOccurs="0"/>
 *         &lt;element ref="{}CorporateActionCode" minOccurs="0"/>
 *         &lt;element ref="{}CorporateActionExecutionType" minOccurs="0"/>
 *         &lt;element ref="{}CorporateActionExecutionDate" minOccurs="0"/>
 *         &lt;element ref="{}TotalEffectiveQuantityUnits" minOccurs="0"/>
 *         &lt;element ref="{}FxRevalRate" minOccurs="0"/>
 *         &lt;element ref="{}DividendAmount" minOccurs="0"/>
 *         &lt;element ref="{}DividendAmountCurrency" minOccurs="0"/>
 *         &lt;element ref="{}DividendAmountFractionalPartRatio" minOccurs="0"/>
 *         &lt;element ref="{}DividendAmountIsNet" minOccurs="0"/>
 *         &lt;element ref="{}DividendTaxName" minOccurs="0"/>
 *         &lt;element ref="{}DividendTaxRate" minOccurs="0"/>
 *         &lt;element ref="{}TotalGrossAmountInDividendAmountCurrency" minOccurs="0"/>
 *         &lt;element ref="{}TotalAmountInDividendAmountCurrency" minOccurs="0"/>
 *         &lt;element ref="{}TotalGrossAmountInTradingAccountPrimaryCurrency" minOccurs="0"/>
 *       &lt;/all>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
@XmlRootElement(name = "CorporateActionDividend")
public class CorporateActionDividendCustomData {

    @XmlElement(name = "Id")
    protected Long id;
    @XmlElement(name = "InstrumentCode")
    protected String instrumentCode;
    @XmlElement(name = "InstrumentCurrency")
    protected String instrumentCurrency;
    @XmlElement(name = "InstrumentCurrencyFractionalPartRatio")
    protected BigDecimal instrumentCurrencyFractionalPartRatio;
    @XmlElement(name = "ProductWrapperCode")
    protected String productWrapperCode;
    @XmlElement(name = "CorporateActionCode")
    protected String corporateActionCode;
    @XmlElement(name = "CorporateActionExecutionType")
    protected String corporateActionExecutionType;
    @XmlElement(name = "CorporateActionExecutionDate")
    protected String corporateActionExecutionDate;
    @XmlElement(name = "TotalEffectiveQuantityUnits")
    protected BigDecimal totalEffectiveQuantityUnits;
    @XmlElement(name = "FxRevalRate")
    protected BigDecimal fxRevalRate;
    @XmlElement(name = "DividendAmount")
    protected BigDecimal dividendAmount;
    @XmlElement(name = "DividendAmountCurrency")
    protected String dividendAmountCurrency;
    @XmlElement(name = "DividendAmountFractionalPartRatio")
    protected BigDecimal dividendAmountFractionalPartRatio;
    @XmlElement(name = "DividendAmountIsNet")
    protected Boolean dividendAmountIsNet;
    @XmlElement(name = "DividendTaxName")
    protected String dividendTaxName;
    @XmlElement(name = "DividendTaxRate")
    protected BigDecimal dividendTaxRate;
    @XmlElement(name = "TotalGrossAmountInDividendAmountCurrency")
    protected BigDecimal totalGrossAmountInDividendAmountCurrency;
    @XmlElement(name = "TotalAmountInDividendAmountCurrency")
    protected BigDecimal totalAmountInDividendAmountCurrency;
    @XmlElement(name = "TotalGrossAmountInTradingAccountPrimaryCurrency")
    protected BigDecimal totalGrossAmountInTradingAccountPrimaryCurrency;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setId(Long value) {
        this.id = value;
    }

    /**
     * Gets the value of the instrumentCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInstrumentCode() {
        return instrumentCode;
    }

    /**
     * Sets the value of the instrumentCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInstrumentCode(String value) {
        this.instrumentCode = value;
    }

    /**
     * Gets the value of the instrumentCurrency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInstrumentCurrency() {
        return instrumentCurrency;
    }

    /**
     * Sets the value of the instrumentCurrency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInstrumentCurrency(String value) {
        this.instrumentCurrency = value;
    }

    /**
     * Gets the value of the instrumentCurrencyFractionalPartRatio property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getInstrumentCurrencyFractionalPartRatio() {
        return instrumentCurrencyFractionalPartRatio;
    }

    /**
     * Sets the value of the instrumentCurrencyFractionalPartRatio property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setInstrumentCurrencyFractionalPartRatio(BigDecimal value) {
        this.instrumentCurrencyFractionalPartRatio = value;
    }

    /**
     * Gets the value of the productWrapperCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProductWrapperCode() {
        return productWrapperCode;
    }

    /**
     * Sets the value of the productWrapperCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProductWrapperCode(String value) {
        this.productWrapperCode = value;
    }

    /**
     * Gets the value of the corporateActionCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorporateActionCode() {
        return corporateActionCode;
    }

    /**
     * Sets the value of the corporateActionCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorporateActionCode(String value) {
        this.corporateActionCode = value;
    }

    /**
     * Gets the value of the corporateActionExecutionType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorporateActionExecutionType() {
        return corporateActionExecutionType;
    }

    /**
     * Sets the value of the corporateActionExecutionType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorporateActionExecutionType(String value) {
        this.corporateActionExecutionType = value;
    }

    /**
     * Gets the value of the corporateActionExecutionDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCorporateActionExecutionDate() {
        return corporateActionExecutionDate;
    }

    /**
     * Sets the value of the corporateActionExecutionDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCorporateActionExecutionDate(String value) {
        this.corporateActionExecutionDate = value;
    }

    /**
     * Gets the value of the totalEffectiveQuantityUnits property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalEffectiveQuantityUnits() {
        return totalEffectiveQuantityUnits;
    }

    /**
     * Sets the value of the totalEffectiveQuantityUnits property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalEffectiveQuantityUnits(BigDecimal value) {
        this.totalEffectiveQuantityUnits = value;
    }

    /**
     * Gets the value of the fxRevalRate property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getFxRevalRate() {
        return fxRevalRate;
    }

    /**
     * Sets the value of the fxRevalRate property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setFxRevalRate(BigDecimal value) {
        this.fxRevalRate = value;
    }

    /**
     * Gets the value of the dividendAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDividendAmount() {
        return dividendAmount;
    }

    /**
     * Sets the value of the dividendAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDividendAmount(BigDecimal value) {
        this.dividendAmount = value;
    }

    /**
     * Gets the value of the dividendAmountCurrency property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDividendAmountCurrency() {
        return dividendAmountCurrency;
    }

    /**
     * Sets the value of the dividendAmountCurrency property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDividendAmountCurrency(String value) {
        this.dividendAmountCurrency = value;
    }

    /**
     * Gets the value of the dividendAmountFractionalPartRatio property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDividendAmountFractionalPartRatio() {
        return dividendAmountFractionalPartRatio;
    }

    /**
     * Sets the value of the dividendAmountFractionalPartRatio property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDividendAmountFractionalPartRatio(BigDecimal value) {
        this.dividendAmountFractionalPartRatio = value;
    }

    /**
     * Gets the value of the dividendAmountIsNet property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isDividendAmountIsNet() {
        return dividendAmountIsNet;
    }

    /**
     * Sets the value of the dividendAmountIsNet property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setDividendAmountIsNet(Boolean value) {
        this.dividendAmountIsNet = value;
    }

    /**
     * Gets the value of the dividendTaxName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDividendTaxName() {
        return dividendTaxName;
    }

    /**
     * Sets the value of the dividendTaxName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDividendTaxName(String value) {
        this.dividendTaxName = value;
    }

    /**
     * Gets the value of the dividendTaxRate property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDividendTaxRate() {
        return dividendTaxRate;
    }

    /**
     * Sets the value of the dividendTaxRate property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDividendTaxRate(BigDecimal value) {
        this.dividendTaxRate = value;
    }

    /**
     * Gets the value of the totalGrossAmountInDividendAmountCurrency property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalGrossAmountInDividendAmountCurrency() {
        return totalGrossAmountInDividendAmountCurrency;
    }

    /**
     * Sets the value of the totalGrossAmountInDividendAmountCurrency property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalGrossAmountInDividendAmountCurrency(BigDecimal value) {
        this.totalGrossAmountInDividendAmountCurrency = value;
    }

    /**
     * Gets the value of the totalAmountInDividendAmountCurrency property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalAmountInDividendAmountCurrency() {
        return totalAmountInDividendAmountCurrency;
    }

    /**
     * Sets the value of the totalAmountInDividendAmountCurrency property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalAmountInDividendAmountCurrency(BigDecimal value) {
        this.totalAmountInDividendAmountCurrency = value;
    }

    /**
     * Gets the value of the totalGrossAmountInTradingAccountPrimaryCurrency property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalGrossAmountInTradingAccountPrimaryCurrency() {
        return totalGrossAmountInTradingAccountPrimaryCurrency;
    }

    /**
     * Sets the value of the totalGrossAmountInTradingAccountPrimaryCurrency property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalGrossAmountInTradingAccountPrimaryCurrency(BigDecimal value) {
        this.totalGrossAmountInTradingAccountPrimaryCurrency = value;
    }

}
