package com.cmcmarkets.nrg.pms;
import com.cmcmarkets.framework.service.util.Stopwatch;
import com.cmcmarkets.productmaster.api.SnapshotDetails;
import com.cmcmarkets.productmaster.api.model.Snapshot3;
import com.cmcmarkets.productmaster.api.service.v4.ProductMasterServiceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
public class DbrProductMasterSnapshot implements SnapshotDetails {
    private final Logger log = LoggerFactory.getLogger(getClass());
    private final String snapshotId;
    private final Long activationTime;
    private final Map<String, DbrCurrency> currencies;
    private final Map<String, DbrInstrument> instruments;
    private final Map<String, DbrInstrumentFeedSetting> instrumentFeedSettings;
    private final Map<String, DbrBrokerSwapRate> brokerSwapRates;

    public DbrProductMasterSnapshot(Snapshot3 snapshot3) {
        Stopwatch monitor = Stopwatch.createStarted();
        snapshotId = snapshot3.getIdentifier();
        activationTime = snapshot3.getActivationTime();
        instruments = ProductMasterServiceUtil.getEntries(snapshot3, DbrInstrument.class)
                .collect(Collectors.toMap(DbrInstrument::getCode, Function.identity()));
        currencies = ProductMasterServiceUtil.getEntries(snapshot3, DbrCurrency.class)
                .collect(Collectors.toMap(DbrCurrency::getIso3, Function.identity()));
        instrumentFeedSettings = ProductMasterServiceUtil.getEntries(snapshot3, DbrInstrumentFeedSetting.class)
                .collect(Collectors.toMap(DbrInstrumentFeedSetting::getCode, Function.identity()));
        brokerSwapRates = ProductMasterServiceUtil.getEntries(snapshot3, DbrBrokerSwapRate.class)
                .collect(Collectors.toMap(DbrBrokerSwapRate::getCode, Function.identity()));
        monitor.stop();
        log.info(
                "Finished parsing snapshot {}. Took {} ms. Snapshot contains {} instruments, {} currencies," +
                        " {} instrument feed settings, and {} broker swap rates.",
                snapshotId,
                monitor.elapsedMillis(),
                instruments.size(),
                currencies.size(),
                instrumentFeedSettings.size(),
                brokerSwapRates.size());
    }
    @Override
    public String getSnapshotId() {
        return snapshotId;
    }
    @Override
    public Long getActivationTime() {
        return activationTime;
    }
    public DbrInstrument getInstrument(final String instrumentCode) {
        return instruments.get(instrumentCode);
    }
    public Boolean isProductCodeFoundInPMS(String productCode) {
        return instruments.containsKey(productCode);
    }
    public DbrCurrency getCurrency(final String iso3Code) {
        return currencies.get(iso3Code);
    }
    public Map<String, DbrCurrency> getCurrencies() {
        return currencies;
    }
    public Map<String, DbrInstrument> getInstruments() {
        return instruments;
    }
    public Map<String, DbrInstrumentFeedSetting> getInstrumentFeedSettings() {
        return instrumentFeedSettings;
    }
    public Map<String, DbrBrokerSwapRate> getBrokerSwapRates() {
        return brokerSwapRates;
    }
}