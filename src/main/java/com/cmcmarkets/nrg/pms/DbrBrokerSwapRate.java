package com.cmcmarkets.nrg.pms;

import com.cmcmarkets.productmaster.api.mappers.PmsEntity;
import com.cmcmarkets.productmaster.api.mappers.PmsProperty;
import com.cmcmarkets.productmaster.api.service.v4.PmsEntry;

import java.math.BigDecimal;

import static com.cmcmarkets.productmaster.api.mappers.PmsPropertyType.*;

/**
 * This document and its contents are protected by copyright 2025 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * <p>
 * (c) CMC Markets Plc 2025
 */

@PmsEntity("BrokerSwapRate")
public interface DbrBrokerSwapRate extends PmsEntry {

    @PmsProperty(name = "Code", type = STRING)
    String getCode();

    @PmsProperty(name = "BrokerAccountNumber", type = LONG)
    Long getBrokerAccountNumber();

    @PmsProperty(name = "Currency", type = STRING)
    String getCurrency();

    @PmsProperty(name = "CountryCode", type = STRING)
    String getCountryCode();

    @PmsProperty(name = "InstrumentCode", type = STRING)
    String getInstrumentCode();

    @PmsProperty(name = "Direction", type = STRING)
    String getDirection();

    @PmsProperty(name = "ReferenceRate", type = STRING)
    String getReferenceRate();

    @PmsProperty(name = "Term", type = STRING)
    String getTerm();

    @PmsProperty(name = "LongHaircut", type = DECIMAL)
    BigDecimal getLongHaircut();

    @PmsProperty(name = "ShortHaircut", type = DECIMAL)
    BigDecimal getShortHaircut();
}