package com.cmcmarkets.nrg.api.model;

import java.util.ArrayList;
import java.util.List;

public class ArrayOfPaymentsTradingAdjustmentInstrument
{

    protected List<PaymentsTradingAdjustmentInstrument> paymentsTradingAdjustmentInstrument;

    /**
     * Gets the value of the paymentsTradingAdjustmentInstrument property.
     * 
     * <p>
     * This accessor method returns a reference to the live list, not a snapshot. Therefore any
     * modification you make to the returned list will be present inside the JAXB object. This is
     * why there is not a <CODE>set</CODE> method for the
     * paymentsTradingAdjustmentInstrument property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * 
     * <pre>
     * getPaymentsTradingAdjustmentInstrument().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentsTradingAdjustmentInstrument }
     * 
     * 
     */
    public List<PaymentsTradingAdjustmentInstrument> getPaymentsTradingAdjustmentInstrument()
    {
        if(paymentsTradingAdjustmentInstrument == null)
        {
            paymentsTradingAdjustmentInstrument =
                new ArrayList<PaymentsTradingAdjustmentInstrument>();
        }
        return this.paymentsTradingAdjustmentInstrument;
    }

}
