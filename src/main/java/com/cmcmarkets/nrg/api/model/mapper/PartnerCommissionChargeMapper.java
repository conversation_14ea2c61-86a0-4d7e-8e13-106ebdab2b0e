package com.cmcmarkets.nrg.api.model.mapper;

import org.modelmapper.PropertyMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.cashaccount.api.model.DefaultPartnerCommissionChargeCustomDataDto;
import com.cmcmarkets.cashaccount.api.model.PartnerCommissionChargeCustomDataDto;
import com.cmcmarkets.nrg.api.model.PartnerCommissionCharge;

public class PartnerCommissionChargeMapper implements Mapper<PartnerCommissionChargeCustomDataDto, PartnerCommissionCharge> {

    private static final Logger m_logger = LoggerFactory.getLogger(PartnerCommissionChargeMapper.class);

    private ObjectMapper mapper = new ObjectMapper();

    private PartnerCommissionChargeMapper() {
        PropertyMap<PartnerCommissionChargeCustomDataDto, PartnerCommissionCharge> fxRevalRateMapping = new PropertyMap <PartnerCommissionChargeCustomDataDto, PartnerCommissionCharge>() {
            protected void configure() {
                map().getFxRevalRateTax().setValue(source.getFxRevalRateTax().getRate());
                map().getFxRevalRate().setValue(source.getFxRevalRate().getRate());
                map().getTax().setValue(source.getTaxInfo().getTaxRate());
                map().getTax().setIsTaxReclaimable(source.getTaxInfo().isTaxReclaimable());
                map().getTax().setIsRebateTaxable(source.getTaxInfo().isRebateTaxable());
                map().getTax().setIsWideningTaxable(source.getTaxInfo().isWideningTaxable());
                map().getTradingAccountFxRevalRate().setValue(source.getTradingAccountFxRevalRate().getRate());
            }
        };
        mapper.addMappings(fxRevalRateMapping).include(DefaultPartnerCommissionChargeCustomDataDto.class, PartnerCommissionCharge.class);
    }

    public static PartnerCommissionChargeMapper getInstance() {
        return SingletonHolder.INSTANCE;
    }

    @Override
    public PartnerCommissionCharge map(PartnerCommissionChargeCustomDataDto t) {
        PartnerCommissionCharge customData = null;
        if (t != null) {
            m_logger.debug("Process Partner Commission Charge Custom Data");
            customData = mapper.map(t, PartnerCommissionCharge.class);
        }
        return customData;
    }


    private static class SingletonHolder {
        private static final PartnerCommissionChargeMapper INSTANCE = new PartnerCommissionChargeMapper();
    }
}
