/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.util.Date;

public interface MessageDisplayEventEntity extends NRGEntity
{
    /**
     * @return the messageId
     */
    Long getMessageId();
    
    /**
     * @return the messageSource
     */
    String getMessageSource();
    
    /**
     * @return the messageSourceId
     */
    String getMessageSourceId();
    
    /**
     * @return the eventTime
     */
    Date getEventTime();
    
    /**
     * @return the displayEvent
     */
    String getDisplayEvent();
    
    /**
     * @return the displayEventDetail
     */
    String getDisplayEventDetail();
    
    /**
     * @return the deviceFingerprint
     */
    String getDeviceFingerprint();
    
    /**
     * @return the appVersion
     */
    String getAppVersion();
    
    /**
     * @return the personId
     */
    Long getPersonId();
    
    /**
     * @return the tradingAccountId
     */
    Long getTradingAccountId();
}
