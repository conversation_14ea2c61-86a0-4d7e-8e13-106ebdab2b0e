/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.cmcmarkets.cashaccount.api.model.SecondaryCurrencyConversionRevalRateInfoDto;
import com.google.common.base.Objects;

public class CashTransactionCorporateActionDividend
{
    private Long m_dividendId;
    private String m_productInstrumentCode;
    private String m_productWrapperCode;
    private String m_corporateActionCode;
    private String m_corporateActionExecutionType;
    private Date m_corporateActionExecutionDate;
    private BigDecimal m_dividendAmount;
    private String m_instrumentCurrency;
    private BigDecimal m_instrumentCurrencyFractionalPartRatio;
    private BigDecimal m_totalEffectiveQuantityUnits;
    private BigDecimal m_fxRevalRate;
    private String m_dividendAmountCurrency;
    private BigDecimal m_dividendAmountFractionalPartRatio;
    private BigDecimal m_totalAmountInDividendAmountCurrency;
    private Boolean m_dividendAmountIsNet;
    private String m_dividendTaxName;
    private BigDecimal m_dividendTaxRate;
    private BigDecimal m_totalGrossAmountInDividendAmountCurrency;
    private BigDecimal m_totalGrossAmountInTradingAccountPrimaryCurrency;
    private BigDecimal m_totalAmountInTradingAccountPrimaryCurrency;
    private List<CorporateActionOpenTrade> m_openTrades;
    private BigDecimal fxRevalRateTax;
    private BigDecimal totalEffectiveQuantityCFDUnits;
    private String taxCountry;
    private String usWithholdingTaxReduction;
    private BigDecimal totalTaxAmountInDividendAmountCurrency;
    private BigDecimal totalTaxAmountInTaxCurrency;
    private Boolean isTaxRateResidencyDependent;
    private String period;
    private SecondaryCurrencyConversionRevalRateInfoDto secondaryCurrencyConversionRevalRateInfo;
    private Long m_tradingAccountCode;
    private String m_corporateActionOfficialType; 
    
    public CashTransactionCorporateActionDividend()
    {
    }

    public Long getDividendId()
    {
        return m_dividendId;
    }

    public void setDividendId(Long dividendId)
    {
        m_dividendId = dividendId;
    }

    public String getProductInstrumentCode()
    {
        return m_productInstrumentCode;
    }

    public void setProductInstrumentCode(String productInstrumentCode)
    {
        m_productInstrumentCode = productInstrumentCode;
    }

    public String getProductWrapperCode()
    {
        return m_productWrapperCode;
    }

    public void setProductWrapperCode(String productWrapperCode)
    {
        m_productWrapperCode = productWrapperCode;
    }

    public String getCorporateActionCode()
    {
        return m_corporateActionCode;
    }

    public void setCorporateActionCode(String corporateActionCode)
    {
        m_corporateActionCode = corporateActionCode;
    }

    public String getCorporateActionExecutionType()
    {
        return m_corporateActionExecutionType;
    }

    public void setCorporateActionExecutionType(String corporateActionExecutionType)
    {
        m_corporateActionExecutionType = corporateActionExecutionType;
    }

    public Date getCorporateActionExecutionDate()
    {
        return m_corporateActionExecutionDate;
    }

    public void setCorporateActionExecutionDate(Date corporateActionExecutionDate)
    {
        m_corporateActionExecutionDate = corporateActionExecutionDate;
    }

    public BigDecimal getDividendAmount()
    {
        return m_dividendAmount;
    }

    public void setDividendAmount(BigDecimal dividendAmount)
    {
        m_dividendAmount = dividendAmount;
    }

    public String getInstrumentCurrency()
    {
        return m_instrumentCurrency;
    }

    public void setInstrumentCurrency(String instrumentCurrency)
    {
        m_instrumentCurrency = instrumentCurrency;
    }

    public BigDecimal getInstrumentCurrencyFractionalPartRatio()
    {
        return m_instrumentCurrencyFractionalPartRatio;
    }

    public void setInstrumentCurrencyFractionalPartRatio(BigDecimal instrumentCurrencyFractionalPartRatio)
    {
        m_instrumentCurrencyFractionalPartRatio = instrumentCurrencyFractionalPartRatio;
    }

    public BigDecimal getTotalEffectiveQuantityUnits()
    {
        return m_totalEffectiveQuantityUnits;
    }

    public void setTotalEffectiveQuantityUnits(BigDecimal totalEffectiveQuantityUnits)
    {
        m_totalEffectiveQuantityUnits = totalEffectiveQuantityUnits;
    }

    public BigDecimal getFxRevalRate()
    {
        return m_fxRevalRate;
    }

    public void setFxRevalRate(BigDecimal fxRevalRate)
    {
        m_fxRevalRate = fxRevalRate;
    }

    public String getDividendAmountCurrency()
    {
        return m_dividendAmountCurrency;
    }

    public void setDividendAmountCurrency(String dividendAmountCurrency)
    {
        m_dividendAmountCurrency = dividendAmountCurrency;
    }

    public BigDecimal getDividendAmountFractionalPartRatio()
    {
        return m_dividendAmountFractionalPartRatio;
    }

    public void setDividendAmountFractionalPartRatio(BigDecimal dividendAmountFractionalPartRatio)
    {
        m_dividendAmountFractionalPartRatio = dividendAmountFractionalPartRatio;
    }

    public BigDecimal getTotalAmountInDividendAmountCurrency()
    {
        return m_totalAmountInDividendAmountCurrency;
    }

    public void setTotalAmountInDividendAmountCurrency(BigDecimal totalAmountInDividendAmountCurrency)
    {
        m_totalAmountInDividendAmountCurrency = totalAmountInDividendAmountCurrency;
    }

    public Boolean getDividendAmountIsNet()
    {
        return m_dividendAmountIsNet;
    }

    public void setDividendAmountIsNet(Boolean dividendAmountIsNet)
    {
        m_dividendAmountIsNet = dividendAmountIsNet;
    }

    public String getDividendTaxName()
    {
        return m_dividendTaxName;
    }

    public void setDividendTaxName(String dividendTaxName)
    {
        m_dividendTaxName = dividendTaxName;
    }

    public BigDecimal getDividendTaxRate()
    {
        return m_dividendTaxRate;
    }

    public void setDividendTaxRate(BigDecimal dividendTaxRate)
    {
        m_dividendTaxRate = dividendTaxRate;
    }

    public BigDecimal getTotalGrossAmountInDividendAmountCurrency()
    {
        return m_totalGrossAmountInDividendAmountCurrency;
    }

    public void setTotalGrossAmountInDividendAmountCurrency(BigDecimal totalGrossAmountInDividendAmountCurrency)
    {
        m_totalGrossAmountInDividendAmountCurrency = totalGrossAmountInDividendAmountCurrency;
    }

    public BigDecimal getTotalGrossAmountInTradingAccountPrimaryCurrency()
    {
        return m_totalGrossAmountInTradingAccountPrimaryCurrency;
    }

    public void setTotalGrossAmountInTradingAccountPrimaryCurrency(BigDecimal totalGrossAmountInTradingAccountPrimaryCurrency)
    {
        m_totalGrossAmountInTradingAccountPrimaryCurrency = totalGrossAmountInTradingAccountPrimaryCurrency;
    }

    public BigDecimal getTotalAmountInTradingAccountPrimaryCurrency()
    {
        return m_totalAmountInTradingAccountPrimaryCurrency;
    }

    public void setTotalAmountInTradingAccountPrimaryCurrency(BigDecimal totalAmountInTradingAccountPrimaryCurrency)
    {
    	m_totalAmountInTradingAccountPrimaryCurrency = totalAmountInTradingAccountPrimaryCurrency;
    }
    
    public List<CorporateActionOpenTrade> getOpenTrades()
    {
        return m_openTrades;
    }

    public void setOpenTrades(List<CorporateActionOpenTrade> m_openTrades)
    {
        this.m_openTrades = m_openTrades;
    }

    public BigDecimal getFxRevalRateTax()
    {
        return fxRevalRateTax;
    }

    public void setFxRevalRateTax(BigDecimal fxRevalRateTax)
    {
        this.fxRevalRateTax = fxRevalRateTax;
    }

    public BigDecimal getTotalEffectiveQuantityCFDUnits()
    {
        return totalEffectiveQuantityCFDUnits;
    }

    public void setTotalEffectiveQuantityCFDUnits(BigDecimal totalEffectiveQuantityCFDUnits)
    {
        this.totalEffectiveQuantityCFDUnits = totalEffectiveQuantityCFDUnits;
    }

    public String getTaxCountry()
    {
        return taxCountry;
    }

    public void setTaxCountry(String taxCountry)
    {
        this.taxCountry = taxCountry;
    }

    public String getUsWithholdingTaxReduction()
    {
        return usWithholdingTaxReduction;
    }

    public void setUsWithholdingTaxReduction(String usWithholdingTaxReduction)
    {
        this.usWithholdingTaxReduction = usWithholdingTaxReduction;
    }

    public BigDecimal getTotalTaxAmountInDividendAmountCurrency()
    {
        return totalTaxAmountInDividendAmountCurrency;
    }

    public void setTotalTaxAmountInDividendAmountCurrency(BigDecimal totalTaxAmountInDividendAmountCurrency)
    {
        this.totalTaxAmountInDividendAmountCurrency = totalTaxAmountInDividendAmountCurrency;
    }

    public BigDecimal getTotalTaxAmountInTaxCurrency()
    {
        return totalTaxAmountInTaxCurrency;
    }

    public void setTotalTaxAmountInTaxCurrency(BigDecimal totalTaxAmountInTaxCurrency)
    {
        this.totalTaxAmountInTaxCurrency = totalTaxAmountInTaxCurrency;
    }

    public Boolean isTaxRateResidencyDependent()
    {
        return isTaxRateResidencyDependent;
    }

    public void setTaxRateResidencyDependent(Boolean isTaxRateResidencyDependent)
    {
        this.isTaxRateResidencyDependent = isTaxRateResidencyDependent;
    }

    public String getPeriod()
    {
        return period;
    }

    public void setPeriod(String period)
    {
        this.period = period;
    }
    
    public SecondaryCurrencyConversionRevalRateInfoDto getSecondaryCurrencyConversionRevalRateInfo  () {
        return secondaryCurrencyConversionRevalRateInfo  ;
    }

    public void setSecondaryCurrencyConversionRevalRateInfo(SecondaryCurrencyConversionRevalRateInfoDto value) {
        this.secondaryCurrencyConversionRevalRateInfo = value;
    }
    
    public Long getTradingAccountCode()
    {
        return m_tradingAccountCode;
    }

    public void setTradingAccountCode(Long tradingAccountCode)
    {
        this.m_tradingAccountCode = tradingAccountCode;
    }
    
    public String getCorporateActionOfficialType()
    {
        return m_corporateActionOfficialType;
    }

    public void setCorporateActionOfficialType(String corporateActionOfficialType)
    {
        this.m_corporateActionOfficialType = corporateActionOfficialType;
    }
    
    
    @Override
    public int hashCode()
    {
        return Objects.hashCode(
            m_dividendId,
            m_productInstrumentCode,
            m_productWrapperCode,
            m_corporateActionCode,
            m_corporateActionExecutionType,
            m_corporateActionExecutionDate,
            m_dividendAmount,
            m_instrumentCurrency,
            m_instrumentCurrencyFractionalPartRatio,
            m_totalEffectiveQuantityUnits,
            m_fxRevalRate,
            m_dividendAmountCurrency,
            m_dividendAmountFractionalPartRatio,
            m_totalAmountInDividendAmountCurrency,
            m_dividendAmountIsNet,
            m_dividendTaxName,
            m_dividendTaxRate,
            m_totalGrossAmountInDividendAmountCurrency,
            m_totalGrossAmountInTradingAccountPrimaryCurrency,
            m_totalAmountInTradingAccountPrimaryCurrency,
            m_openTrades,
            fxRevalRateTax,
            totalEffectiveQuantityCFDUnits,
            taxCountry,
            usWithholdingTaxReduction,
            totalTaxAmountInDividendAmountCurrency,
            totalTaxAmountInTaxCurrency,
            isTaxRateResidencyDependent,
            period,
            m_tradingAccountCode,
            m_corporateActionOfficialType);
    }

    @Override
    public boolean equals(Object object)
    {
        if(object instanceof CashTransactionCorporateActionDividend)
        {
            CashTransactionCorporateActionDividend that = (CashTransactionCorporateActionDividend) object;
            return Objects.equal(this.m_dividendId, that.m_dividendId) &&
                Objects.equal(this.m_productInstrumentCode, that.m_productInstrumentCode) &&
                Objects.equal(this.m_productWrapperCode, that.m_productWrapperCode) &&
                Objects.equal(this.m_corporateActionCode, that.m_corporateActionCode) &&
                Objects.equal(this.m_corporateActionExecutionType, that.m_corporateActionExecutionType) &&
                Objects.equal(this.m_corporateActionExecutionDate, that.m_corporateActionExecutionDate) &&
                Objects.equal(this.m_dividendAmount, that.m_dividendAmount) &&
                Objects.equal(this.m_instrumentCurrency, that.m_instrumentCurrency) &&
                Objects.equal(
                    this.m_instrumentCurrencyFractionalPartRatio,
                    that.m_instrumentCurrencyFractionalPartRatio) &&
                Objects.equal(this.m_totalEffectiveQuantityUnits, that.m_totalEffectiveQuantityUnits) &&
                Objects.equal(this.m_fxRevalRate, that.m_fxRevalRate) &&
                Objects.equal(this.m_dividendAmountCurrency, that.m_dividendAmountCurrency) &&
                Objects.equal(this.m_dividendAmountFractionalPartRatio, that.m_dividendAmountFractionalPartRatio) &&
                Objects.equal(this.m_totalAmountInDividendAmountCurrency, that.m_totalAmountInDividendAmountCurrency) &&
                Objects.equal(this.m_dividendAmountIsNet, that.m_dividendAmountIsNet) &&
                Objects.equal(this.m_dividendTaxName, that.m_dividendTaxName) &&
                Objects.equal(this.m_dividendTaxRate, that.m_dividendTaxRate) &&
                Objects.equal(
                    this.m_totalGrossAmountInDividendAmountCurrency,
                    that.m_totalGrossAmountInDividendAmountCurrency) &&
                Objects.equal(
                    this.m_totalGrossAmountInTradingAccountPrimaryCurrency,
                    that.m_totalGrossAmountInTradingAccountPrimaryCurrency) &&
                Objects.equal(
                        this.m_totalAmountInTradingAccountPrimaryCurrency,
                        that.m_totalAmountInTradingAccountPrimaryCurrency) &&
                Objects.equal(this.m_openTrades, that.m_openTrades) &&
                Objects.equal(this.fxRevalRateTax, that.fxRevalRateTax) &&
                Objects.equal(this.totalEffectiveQuantityCFDUnits, that.totalEffectiveQuantityCFDUnits) &&
                Objects.equal(this.taxCountry, that.taxCountry) &&
                Objects.equal(this.usWithholdingTaxReduction, that.usWithholdingTaxReduction) &&
                Objects
                    .equal(this.totalTaxAmountInDividendAmountCurrency, that.totalTaxAmountInDividendAmountCurrency) &&
                Objects.equal(this.totalTaxAmountInTaxCurrency, that.totalTaxAmountInTaxCurrency) &&
                Objects.equal(this.isTaxRateResidencyDependent, that.isTaxRateResidencyDependent) &&
                Objects.equal(this.period, that.period) &&
                Objects.equal(this.m_tradingAccountCode, that.m_tradingAccountCode) &&
                Objects.equal(this.m_corporateActionOfficialType, that.m_corporateActionOfficialType);
        }
        return false;
    }

    @Override
    public String toString()
    {
        return Objects.toStringHelper(this)
            .add("m_dividendId", m_dividendId)
            .add("m_productInstrumentCode", m_productInstrumentCode)
            .add("m_productWrapperCode", m_productWrapperCode)
            .add("m_corporateActionCode", m_corporateActionCode)
            .add("m_corporateActionExecutionType", m_corporateActionExecutionType)
            .add("m_corporateActionExecutionDate", m_corporateActionExecutionDate)
            .add("m_dividendAmount", m_dividendAmount)
            .add("m_instrumentCurrency", m_instrumentCurrency)
            .add("m_instrumentCurrencyFractionalPartRatio", m_instrumentCurrencyFractionalPartRatio)
            .add("m_totalEffectiveQuantityUnits", m_totalEffectiveQuantityUnits)
            .add("m_fxRevalRate", m_fxRevalRate)
            .add("m_dividendAmountCurrency", m_dividendAmountCurrency)
            .add("m_dividendAmountFractionalPartRatio", m_dividendAmountFractionalPartRatio)
            .add("m_totalAmountInDividendAmountCurrency", m_totalAmountInDividendAmountCurrency)
            .add("m_dividendAmountIsNet", m_dividendAmountIsNet)
            .add("m_dividendTaxName", m_dividendTaxName)
            .add("m_dividendTaxRate", m_dividendTaxRate)
            .add("m_totalGrossAmountInDividendAmountCurrency", m_totalGrossAmountInDividendAmountCurrency)
            .add("m_totalGrossAmountInTradingAccountPrimaryCurrency", m_totalGrossAmountInTradingAccountPrimaryCurrency)
            .add("m_totalAmountInTradingAccountPrimaryCurrency", m_totalAmountInTradingAccountPrimaryCurrency)
            .add("m_openTrades", m_openTrades)
            .add("fxRevalRateTax", fxRevalRateTax)
            .add("totalEffectiveQuantityCFDUnits", totalEffectiveQuantityCFDUnits)
            .add("taxCountry", taxCountry)
            .add("usWithholdingTaxReduction", usWithholdingTaxReduction)
            .add("totalTaxAmountInDividendAmountCurrency", totalTaxAmountInDividendAmountCurrency)
            .add("totalTaxAmountInTaxCurrency", totalTaxAmountInTaxCurrency)
            .add("isTaxRateResidencyDependent", isTaxRateResidencyDependent)
            .add("period", period)
            .add("m_tradingAccountCode", m_tradingAccountCode)
            .add("m_corporateActionOfficialType", m_corporateActionOfficialType)
            .toString();
    }

}
