//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference
// Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.08.05 at 01:54:35 PM GMT
//

package com.cmcmarkets.nrg.api.model;

import javax.xml.datatype.XMLGregorianCalendar;

public class BankPayments
{

    protected String paymentCodifier;
    protected String paymentCode;
    protected XMLGregorianCalendar valueDate;

    /**
     * Gets the value of the paymentCodifier property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getPaymentCodifier()
    {
        return paymentCodifier;
    }

    /**
     * Sets the value of the paymentCodifier property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setPaymentCodifier(String value)
    {
        this.paymentCodifier = value;
    }

    /**
     * Gets the value of the paymentCode property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getPaymentCode()
    {
        return paymentCode;
    }

    /**
     * Sets the value of the paymentCode property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setPaymentCode(String value)
    {
        this.paymentCode = value;
    }

    /**
     * Gets the value of the valueDate property.
     * 
     * @return possible object is {@link XMLGregorianCalendar }
     * 
     */
    public XMLGregorianCalendar getValueDate()
    {
        return valueDate;
    }

    /**
     * Sets the value of the valueDate property.
     * 
     * @param value allowed object is {@link XMLGregorianCalendar }
     * 
     */
    public void setValueDate(XMLGregorianCalendar value)
    {
        this.valueDate = value;
    }

}
