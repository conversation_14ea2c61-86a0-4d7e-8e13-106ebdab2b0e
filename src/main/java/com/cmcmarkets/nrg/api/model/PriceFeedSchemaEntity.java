/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.util.List;

import com.cmcmarkets.productmaster.api.model.PriceBandFxr;
import com.cmcmarkets.productmaster.api.model.PriceBandOutOfHours;
import com.cmcmarkets.productmaster.api.model.PriceFeedSchemaItem;;

public interface PriceFeedSchemaEntity extends NRGEntity
{
    /**
     * @return the name
     */
    String getName();

    /**
     * @return the defaultBand
     */
    Integer getDefaultBand();

    /**
     * @return the defaultPriceStreamType
     */    
    Integer getDefaultPriceStreamType();
    
    /**
     * @return the partnerIds
     */
    List<String> getPartnerIds();

    /**
     * @return the priceSchemaItems
     */
    List<PriceFeedSchemaItem> getPriceFeedSchemaItems();
    
    /**
     * @return the defaultAdjustId
     */
    Integer getDefaultAdjustId();
    
    /**
     * @return the defaultAdjustAlias
     */
    String getDefaultAdjustAlias();
    
    /**
     * @return the defaultBandAlias
     */
    String getDefaultBandAlias();
    
    /**
     * @return the defaultPriceStreamTypeAlias
     */
    String getDefaultPriceStreamTypeAlias();

    /**
     * @return the executeAtOrigin
     */
    Boolean isExecuteAtOrigin();

    /**
     * @return defaultSwapPointBand
     */
    Integer getDefaultSwapPointBand();

    /**
     * @return defaultSwapPointBandAlias
     */
    String getDefaultSwapPointBandAlias();

	Double getDefaultSpreadProportion();

    PriceBandOutOfHours getDefaultBandOutOfHours();

    PriceBandFxr getDefaultFxrBand();

}
