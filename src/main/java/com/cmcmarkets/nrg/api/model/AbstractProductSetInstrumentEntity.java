/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.google.common.base.Objects;

public class AbstractProductSetInstrumentEntity extends AbstractDeletableEntity implements ProductSetInstrumentEntity
{
    protected String m_platform;
    protected String m_commodityTypeName;
    protected String m_companySectorName;
    protected String m_countryClassificationName;
    protected String m_currency;
    protected String m_countryCode;
    protected String m_feedSymbol;
    protected String m_ISIN;
    protected String m_instrumentCode;
    protected String m_instrumentType;
    protected String m_MIC;
    protected String m_pairCurrency;
    protected String m_RIC;
    protected String m_shortName;
    protected Long m_mmuid;
    protected Long m_mmuid2;
    protected Long m_mmvalueid;
    protected String m_prophetBandingAlgorithm;
    protected BigDecimal m_prophetBandingFixedSpread;
    protected BigDecimal m_prophetBandingSpreadFactor;
    protected Long m_mmInstIdCfd;
    protected Long m_mmInstIdSbIr;
    protected Long m_mmInstIdSbUk;
    protected Date m_pricingStartDate;
    protected Date m_firstTradingDate;
    protected Date m_lastRolloverDate;
    protected Date m_automaticRolloverDate;
    protected Date m_lastTradingDate;
    protected Date m_cashSettlementDate;
    protected Date m_expiryDate;
    protected Boolean m_isSkipRollover;
    protected String m_lastTradingDateDesc;
    protected String m_lastSettlementDateDesc;
    protected String m_lastRolloverDateDesc;
    protected String m_contractCode;
    protected String m_rolloverTargetCode;
    protected String m_cmcCashInstrumentCode;
    protected String m_cmcFinancialInstrumentType;
    protected String m_exchangeProductCode;
    protected Boolean m_isPRRQualifyingIndex;
    protected BigDecimal m_positionRiskRequirementPercent;
    protected Map<String, InstrumentNames> m_instrumentNames;
    protected String m_securityType;
    protected BigDecimal m_contractSizeOverride;
    protected String m_commodityBase;
    protected String m_commodityDetail;
    protected String m_instrumentTypeCode;
    protected List<PMSEntryLanguage> m_instrumentTypeLanguages;
    protected String m_riskCountryCode;
    protected String m_taxCountryCode;
    protected String m_bloombergCode;
    protected String m_sedol;
    protected String m_reutersMIC;
    protected String m_operatingMIC;
    protected String m_mainExchange;
    protected String m_marketStatus;
    protected Long m_curMktCapUsd;
    protected Long m_avgDailyValueTraded30dUsd;
    protected Long m_avgDailyTraded3mUsd;
    protected BigDecimal m_eqyFreeFloatPct;
    protected String m_securityTyp;
    protected Boolean m_isPairCurrencyInFractionalParts;
    protected BigDecimal m_pointMultiplier;
    protected String m_prophetPrimaryInstrumentCode;
    protected BigDecimal m_couponRate;
    protected Date m_cheapestToDeliverDate;
    protected String m_marketCode;
    protected String m_marketAlias;
    protected String m_marketDataMappingCode;
    protected String m_cfdCfi;
    protected String m_sbCfi;
    protected Boolean m_isMifidiiReportable;
    protected String m_securityDescription;    
    protected BigDecimal m_prophetBrokerMarginAmount;
    protected BigDecimal m_prophetBrokerMarginPercent;
    protected String m_prophetBrokerMarginTier;
    protected String m_prophetBrokerMarginTierFxDB;
    protected String m_prophetBrokerMarginTierFxUBS;
    protected String m_underlyingInstrumentCode;
    protected String m_underlyingInstrumentAlias;    
    protected Boolean m_isCcyInFrctnlPrts;
    protected String m_financingCurrency;
    protected Boolean m_imIsTestInstrument;
    protected Boolean m_imIsDemoOnly;
    protected Boolean m_isFixedSpreadInstrument;
    protected Boolean m_isGuaranteedStopInstrmnt;
    protected Boolean m_isMetatraderInstrument;
    protected Boolean m_isEsmaDuplicate;
    protected Boolean m_isFindableInstrument;
    protected Boolean m_isTradableInstrument;
    protected String m_apiCode;
    protected String m_workflowStatus;
    protected Boolean m_isAsicDuplicate;
    protected Date m_firstNoticeDate;
    protected Boolean m_prophetBcEnabled;
    protected Boolean m_isValueDateRolloverInstrument;
    protected Boolean m_isPreventCfdTradingOnFxAccounts;
    protected String m_taxSecurityType;
    protected String m_productIdValue;
    protected String m_productSelector;
    protected Boolean m_isInstrumentSI;
    protected String m_CUSIP;
    protected String m_fundType;
    protected BigDecimal m_prophetCarryAskOverride;
    protected BigDecimal m_prophetCarryBidOverride;
    protected Boolean m_prophetCarryRateOverridden;
    protected Boolean m_instrumentDelistedInMarket;
    protected String m_BBCountryFullName;
    protected String m_BBReferenceTaxSecurityType;
    protected String m_SAXOInstrumentCode;
    protected String m_assetTypeDescription;
    protected String m_assetSubTypeDescription;
    protected String m_optionRoot;
    protected Boolean m_isNationalDeclarationProduct;
    protected Boolean m_isCAInstrument;
    protected String m_fundAssetClassFocus;

    protected String m_annadsbCommBaseProduct;
    protected String m_annadsbCommSubProduct;
    protected String m_annadsbCommAddSubProduct;
    protected String m_annadsbCommHeaderProduct;
    protected String m_annadsbCommUnderlierId;
    protected String m_annadsbCommUnderlierIdSrc;
    protected String m_upiCfd;
    protected String m_upiSb;
    protected String m_upiOptionsEurCashPut;
    protected String m_upiOptionsEurCashCall;
    protected String m_upiOptionsEurPhysPut;
    protected String m_upiOptionsEurPhysCall;
    protected String m_upiOptionsAmerCashPut;
    protected String m_upiOptionsAmerCashCall;
    protected String m_upiOptionsAmerPhysPut;
    protected String m_upiOptionsAmerPhysCall;
    protected String m_upiOptionsBermCashPut;
    protected String m_upiOptionsBermCashCall;
    protected String m_upiOptionsBermPhysPut;
    protected String m_upiOptionsBermPhysCall;
    protected String m_upiCfdCountdown;
    protected String m_upiSbCountdown;

    protected String m_countdownCfi;

    protected String m_optionsAmerCashCallCFI;
    protected String m_optionsAmerCashPutCFI;
    protected String m_optionsAmerPhysCallCFI;
    protected String m_optionsAmerPhysPutCFI;
    protected String m_optionsBermCashCallCFI;
    protected String m_optionsBermCashPutCFI;
    protected String m_optionsBermPhysPutCFI;
    protected String m_optionsBermPhysCallCFI;
    protected String m_optionsEurCashCallCFI;
    protected String m_optionsEurCashPutCFI;
    protected String m_optionsEurPhysCallCFI;
    protected String m_optionsEurPhysPutCFI;
    protected Long m_currentSharesOutstanding;
    protected String m_upiSbOptions;
    protected String m_sbOptionCFI;
    protected Boolean m_isUseInstForFxConversion;
    protected Boolean m_isOTC;
    protected String m_bbCountryIncorporation;
    protected String m_refTaxCountry;
    protected String m_refCountryIncorporationDsc;
    protected String m_upiCfdOptions;
    protected String m_cfdOptionCFI;
    protected Boolean m_isIsaEligible;

    public AbstractProductSetInstrumentEntity()
    {
        super(null); // TODO effectiveStartDate
    }

    @Override
    public String getCommodityTypeName()
    {
        return m_commodityTypeName;
    }

    @Override
    public String getCompanySectorName()
    {
        return m_companySectorName;
    }

    @Override
    public String getCountryClassificationName()
    {
        return m_countryClassificationName;
    }

    @Override
    public String getCurrency()
    {
        return m_currency;
    }

    @Override
    public String getCountryCode()
    {
        return m_countryCode;
    }

    @Override
    public String getFeedSymbol()
    {
        return m_feedSymbol;
    }

    @Override
    public String getISIN()
    {
        return m_ISIN;
    }

    @Override
    public String getInstrumentCode()
    {
        return m_instrumentCode;
    }

    @Override
    public String getInstrumentType()
    {
        return m_instrumentType;
    }

    @Override
    public String getMIC()
    {
        return m_MIC;
    }

    @Override
    public String getPairCurrency()
    {
        return m_pairCurrency;
    }

    @Override
    public String getRIC()
    {
        return m_RIC;
    }

    @Override
    public String getShortName()
    {
        return m_shortName;
    }

    @Override
    public Long getMMUID()
    {
        return m_mmuid;
    }

    @Override
    public Long getMMUID2()
    {
        return m_mmuid2;
    }

    @Override
    public Long getMMVALUEID()
    {
        return m_mmvalueid;
    }

    @Override
    public String getProphetBandingAlgorithm()
    {
        return m_prophetBandingAlgorithm;
    }

    @Override
    public BigDecimal getProphetBandingFixedSpread()
    {
        return m_prophetBandingFixedSpread;
    }

    @Override
    public BigDecimal getProphetBandingSpreadFactor()
    {
        return m_prophetBandingSpreadFactor;
    }

    @Override
    public Long getMmInstIdCfd()
    {
        return m_mmInstIdCfd;
    }

    @Override
    public Long getMmInstIdSbIr()
    {
        return m_mmInstIdSbIr;
    }

    @Override
    public Long getMmInstIdSbUk()
    {
        return m_mmInstIdSbUk;
    }

    @Override
    public Date getPricingStartDate()
    {
        return m_pricingStartDate;
    }

    @Override
    public Date getFirstTradingDate()
    {
        return m_firstTradingDate;
    }

    @Override
    public Date getLastRolloverDate()
    {
        return m_lastRolloverDate;
    }

    @Override
    public Date getAutomaticRolloverDate()
    {
        return m_automaticRolloverDate;
    }

    @Override
    public Date getLastTradingDate()
    {
        return m_lastTradingDate;
    }

    @Override
    public Date getCashSettlementDate()
    {
        return m_cashSettlementDate;
    }

    @Override
    public Date getExpiryDate()
    {
        return m_expiryDate;
    }

    @Override
    public Boolean isSkipRollover()
    {
        return m_isSkipRollover;
    }

    @Override
    public String getLastTradingDateDesc()
    {
        return m_lastTradingDateDesc;
    }

    @Override
    public String getLastSettlementDateDesc()
    {
        return m_lastSettlementDateDesc;
    }

    @Override
    public String getLastRolloverDateDesc()
    {
        return m_lastRolloverDateDesc;
    }

    @Override
    public String getContractCode()
    {
        return m_contractCode;
    }

    @Override
    public String getRolloverTargetCode()
    {
        return m_rolloverTargetCode;
    }

    @Override
    public String getCmcCashInstrumentCode()
    {
        return m_cmcCashInstrumentCode;
    }

    @Override
    public String getCmcFinancialInstrumentType()
    {
        return m_cmcFinancialInstrumentType;
    }

    @Override
    public String getExchangeProductCode()
    {
        return m_exchangeProductCode;
    }

    @Override
    public Boolean getIsPRRQualifyingIndex()
    {
        return m_isPRRQualifyingIndex;
    }

    @Override
    public BigDecimal getPositionRiskRequirementPercent()
    {
        return m_positionRiskRequirementPercent;
    }

    @Override
    public Map<String, InstrumentNames> getInstrumentNames()
    {
        return m_instrumentNames;
    }

    @Override
    public String getSecurityType()
    {
        return m_securityType;
    }

    @Override
    public BigDecimal getContractSizeOverride()
    {
        return m_contractSizeOverride;
    }

    @Override
    public String getCommodityBase()
    {
        return m_commodityBase;
    }

    @Override
    public String getCommodityDetail()
    {
        return m_commodityDetail;
    }

    @Override
    public String getInstrumentTypeCode()
    {
        return m_instrumentTypeCode;
    }

    @Override
    public List<PMSEntryLanguage> getInstrumentTypeLanguages()
    {
        return m_instrumentTypeLanguages;
    }

    @Override
    public String getRiskCountryCode()
    {
        return m_riskCountryCode;
    }

    @Override
    public String getTaxCountryCode()
    {
        return m_taxCountryCode;
    }

    @Override
    public String getBloombergCode()
    {
        return m_bloombergCode;
    }

    @Override
    public String getSedol()
    {
        return m_sedol;
    }

    @Override
    public String getReutersMIC()
    {
        return m_reutersMIC;
    }

    @Override
    public String getOperatingMIC()
    {
        return m_operatingMIC;
    }

    @Override
    public String getMainExchange()
    {
        return m_mainExchange;
    }

    @Override
    public String getMarketStatus()
    {
        return m_marketStatus;
    }

    @Override
    public Long getCurMktCapUsd()
    {
        return m_curMktCapUsd;
    }

    @Override
    public Long getAvgDailyValueTraded30dUsd()
    {
        return m_avgDailyValueTraded30dUsd;
    }

    @Override
    public Long getAvgDailyTraded3mUsd()
    {
        return m_avgDailyTraded3mUsd;
    }

    @Override
    public BigDecimal getEqyFreeFloatPct()
    {
        return m_eqyFreeFloatPct;
    }

    @Override
    public String getSecurityTyp()
    {
        return m_securityTyp;
    }

    @Override
    public Boolean getIsPairCurrencyInFractionalParts()
    {
        return m_isPairCurrencyInFractionalParts;
    }

    @Override
    public BigDecimal getPointMultiplier()
    {
        return m_pointMultiplier;
    }

    @Override
    public String getProphetPrimaryInstrumentCode()
    {
        return m_prophetPrimaryInstrumentCode;
    }

    @Override
    public BigDecimal getCouponRate()
    {
        return m_couponRate;
    }

    @Override
    public Date getCheapestToDeliverDate()
    {
        return m_cheapestToDeliverDate;
    }

    @Override
    public String getMarketCode()
    {
        return m_marketCode;
    }

    @Override
    public String getMarketAlias()
    {
        return m_marketAlias;
    }

    @Override
    public String getMarketDataMappingCode()
    {
        return m_marketDataMappingCode;
    }

    @Override
    public String getCfdCfi()
    {
        return m_cfdCfi;
    }

    @Override
    public String getSbCfi()
    {
        return m_sbCfi;
    }
    
    @Override
    public Boolean getIsMifidiiReportable()
    {
        return m_isMifidiiReportable;
    }
    
    @Override
    public String getSecurityDescription()
    {
        return m_securityDescription;
    }

    @Override
    public BigDecimal getProphetBrokerMarginAmount()
    {
    	return m_prophetBrokerMarginAmount;
    }

    @Override
    public BigDecimal getProphetBrokerMarginPercent()
    {
    	return m_prophetBrokerMarginPercent;
    }

    @Override
    public String getProphetBrokerMarginTier()
    {
    	return m_prophetBrokerMarginTier;
    }

    @Override
    public String getProphetBrokerMarginTierFxDB()
    {
    	return m_prophetBrokerMarginTierFxDB;
    }

    @Override
    public String getProphetBrokerMarginTierFxUBS()
    {
    	return m_prophetBrokerMarginTierFxUBS;
    }

    @Override
    public String getUnderlyingInstrumentCode()
    {
        return m_underlyingInstrumentCode;
    }

    @Override
    public String getUnderlyingInstrumentAlias()
    {
        return m_underlyingInstrumentAlias;
    }    
        
    @Override
    public Boolean getIsCcyInFrctnlPrts()
    {
        return m_isCcyInFrctnlPrts;
    }
    
    @Override
    public String getFinancingCurrency()
    {
        return m_financingCurrency;
    }
    
    @Override
    public Boolean getImIsTestInstrument()
    {
        return m_imIsTestInstrument;
    }
    
    @Override
    public Boolean getImIsDemoOnly()
    {
        return m_imIsDemoOnly;
    }
    
    @Override
    public Boolean getIsFixedSpreadInstrument()
    {
        return m_isFixedSpreadInstrument;
    }
    
    @Override
    public Boolean getIsGuaranteedStopInstrmnt()
    {
        return m_isGuaranteedStopInstrmnt;
    }
    
    @Override
    public Boolean getIsMetatraderInstrument()
    {
        return m_isMetatraderInstrument;
    }
    
    @Override
    public Boolean getIsEsmaDuplicate()
    {
        return m_isEsmaDuplicate;
    }
    
    @Override
    public Boolean getIsFindableInstrument()
    {
        return m_isFindableInstrument;
    }
    
    @Override
    public Boolean getIsTradableInstrument()
    {
        return m_isTradableInstrument;
    }
    
    @Override
    public String getAPICode()
    {
        return m_apiCode;
    }
    
    @Override
    public String getWorkflowStatus()
    {
        return m_workflowStatus;
    }
    
    @Override
    public Boolean getIsAsicDuplicate()
    {
        return m_isAsicDuplicate;
    }
    
    @Override
    public Date getFirstNoticeDate()
    {
        return m_firstNoticeDate;
    }
    
    @Override
    public Boolean getProphetBcEnabled()
    {
        return m_prophetBcEnabled;
    }
    
    @Override
    public Boolean getIsValueDateRolloverInstrument()
    {
        return m_isValueDateRolloverInstrument;
    }
    
    @Override
    public Boolean getIsPreventCfdTradingOnFxAccounts()
    {
        return m_isPreventCfdTradingOnFxAccounts;
    }
    
    @Override
    public String getTaxSecurityType()
    {
        return m_taxSecurityType;
    }
    
    @Override
    public String getProductIdValue()
    {
        return m_productIdValue;
    }
    
    @Override
    public String getProductSelector()
    {
        return m_productSelector;
    }
        
    @Override
    public Boolean getIsInstrumentSI()
    {
        return m_isInstrumentSI;
    }
    
    @Override
    public String getCUSIP()
    {
        return m_CUSIP;
    }
    
    @Override
    public String getFundType()
    {
        return m_fundType;
    }
     
    @Override
    public BigDecimal getProphetCarryAskOverride()
    {
        return m_prophetCarryAskOverride;
    }
    
    @Override
    public BigDecimal getProphetCarryBidOverride()
    {
        return m_prophetCarryBidOverride;
    }
    
    @Override
    public Boolean getProphetCarryRateOverridden()
    {
        return m_prophetCarryRateOverridden;
    }
    
    @Override
    public Boolean getInstrumentDelistedInMarket()
    {
        return m_instrumentDelistedInMarket;
    }

    @Override
    public String getBBReferenceTaxSecurityType() { return m_BBReferenceTaxSecurityType; }

    @Override
    public String getBBCountryFullName() { return m_BBCountryFullName; }

    @Override
    public String getSAXOInstrumentCode() {
        return m_SAXOInstrumentCode;
    }

    @Override
    public String getAssetTypeDescription() {
        return m_assetTypeDescription;
    }

    @Override
    public String getAssetSubTypeDescription() {
        return m_assetSubTypeDescription;
    }

    @Override
    public String getOptionRoot() {
        return m_optionRoot;
    }

    public Boolean getIsNationalDeclarationProduct() {
        return m_isNationalDeclarationProduct;
    }

    public Boolean getIsCAInstrument() {
        return m_isCAInstrument;
    }

    public String getFundAssetClassFocus(){
        return m_fundAssetClassFocus;
    }

    public String getAnnadsbCommBaseProduct() {
        return m_annadsbCommBaseProduct;
    }

    public String getAnnadsbCommSubProduct() {
        return m_annadsbCommSubProduct;
    }

    public String getAnnadsbCommAddSubProduct() {
        return m_annadsbCommAddSubProduct;
    }

    public String getAnnadsbCommHeaderProduct() {
        return m_annadsbCommHeaderProduct;
    }

    public String getAnnadsbCommUnderlierId() {
        return m_annadsbCommUnderlierId;
    }

    public String getAnnadsbCommUnderlierIdSrc() {
        return m_annadsbCommUnderlierIdSrc;
    }

    public String getUpiCfd() {
        return m_upiCfd;
    }

    public String getUpiSb() {
        return m_upiSb;
    }

    public String getUpiOptionsEurCashPut() {
        return m_upiOptionsEurCashPut;
    }

    public String getUpiOptionsEurCashCall() {
        return m_upiOptionsEurCashCall;
    }

    public String getUpiOptionsEurPhysPut() {
        return m_upiOptionsEurPhysPut;
    }

    public String getUpiOptionsEurPhysCall() {
        return m_upiOptionsEurPhysCall;
    }

    public String getUpiOptionsAmerCashPut() {
        return m_upiOptionsAmerCashPut;
    }

    public String getUpiOptionsAmerCashCall() {
        return m_upiOptionsAmerCashCall;
    }

    public String getUpiOptionsAmerPhysPut() {
        return m_upiOptionsAmerPhysPut;
    }

    public String getUpiOptionsAmerPhysCall() {
        return m_upiOptionsAmerPhysCall;
    }

    public String getUpiOptionsBermCashPut() {
        return m_upiOptionsBermCashPut;
    }

    public String getUpiOptionsBermCashCall() {
        return m_upiOptionsBermCashCall;
    }

    public String getUpiOptionsBermPhysPut() {
        return m_upiOptionsBermPhysPut;
    }

    public String getUpiOptionsBermPhysCall() {
        return m_upiOptionsBermPhysCall;
    }

    public String getUpiCfdCountdown(){
        return m_upiCfdCountdown;
    }
    public String getUpiSbCountdown(){
        return m_upiSbCountdown;
    }

    public String getCountdownCfi(){
        return m_countdownCfi;
    }

    @Override
    public String getOptionsAmerCashCallCFI() {
        return m_optionsAmerCashCallCFI;
    }

    @Override
    public String getOptionsAmerCashPutCFI() {
        return m_optionsAmerCashPutCFI;
    }

    @Override
    public String getOptionsAmerPhysCallCFI() {
        return m_optionsAmerPhysCallCFI;
    }

    @Override
    public String getOptionsAmerPhysPutCFI() {
        return m_optionsAmerPhysPutCFI;
    }

    @Override
    public String getOptionsBermCashCallCFI() {
        return m_optionsBermCashCallCFI;
    }

    @Override
    public String getOptionsBermCashPutCFI() {
        return m_optionsBermCashPutCFI;
    }

    @Override
    public String getOptionsBermPhysPutCFI() {
        return m_optionsBermPhysPutCFI;
    }

    @Override
    public String getOptionsBermPhysCallCFI() {
        return m_optionsBermPhysCallCFI;
    }

    @Override
    public String getOptionsEurCashCallCFI() {
        return m_optionsEurCashCallCFI;
    }

    @Override
    public String getOptionsEurCashPutCFI() {
        return m_optionsEurCashPutCFI;
    }

    @Override
    public String getOptionsEurPhysCallCFI() {
        return m_optionsEurPhysCallCFI;
    }

    @Override
    public String getOptionsEurPhysPutCFI() {
        return m_optionsEurPhysPutCFI;
    }

    @Override
    public Long getCurrentSharesOutstanding() {
        return m_currentSharesOutstanding;
    }

    @Override
    public String getUpiSbOptions() {
        return m_upiSbOptions;
    }

    @Override
    public String getSbOptionCfi() {
        return m_sbOptionCFI;
    }

    @Override
    public Boolean getIsUseInstForFxConversion() {
        return m_isUseInstForFxConversion;
    }

    @Override
    public Boolean getIsOTC() {
        return m_isOTC;
    }

    @Override
    public String getBBCountryIncorporation() {
        return m_bbCountryIncorporation;
    }

    @Override
    public String getRefTaxCountry() {
        return m_refTaxCountry;
    }

    @Override
    public String getRefCountryIncorporationDsc() {
        return m_refCountryIncorporationDsc;
    }

    @Override
    public String getUpiCfdOptions() {
        return m_upiCfdOptions;
    }

    @Override
    public String getCfdOptionCFI() {
        return m_cfdOptionCFI;
    }

    @Override
    public Boolean getIsIsaEligible() {
        return m_isIsaEligible;
    }

    @Override
    public int hashCode()
    {
        return Objects.hashCode(
            super.hashCode(),
            m_platform,
            m_commodityTypeName,
            m_companySectorName,
            m_countryClassificationName,
            m_currency,
            m_countryCode,
            m_feedSymbol,
            m_ISIN,
            m_instrumentCode,
            m_instrumentType,
            m_MIC,
            m_pairCurrency,
            m_RIC,
            m_shortName,
            m_mmuid,
            m_mmuid2,
            m_mmvalueid,
            m_prophetBandingAlgorithm,
            m_prophetBandingFixedSpread,
            m_prophetBandingSpreadFactor,
            m_mmInstIdCfd,
            m_mmInstIdSbIr,
            m_mmInstIdSbUk,
            m_pricingStartDate,
            m_firstTradingDate,
            m_lastRolloverDate,
            m_automaticRolloverDate,
            m_lastTradingDate,
            m_cashSettlementDate,
            m_expiryDate,
            m_isSkipRollover,
            m_lastTradingDateDesc,
            m_lastSettlementDateDesc,
            m_lastRolloverDateDesc,
            m_contractCode,
            m_rolloverTargetCode,
            m_cmcCashInstrumentCode,
            m_cmcFinancialInstrumentType,
            m_exchangeProductCode,
            m_isPRRQualifyingIndex,
            m_positionRiskRequirementPercent,
            m_instrumentNames,
            m_securityType,
            m_contractSizeOverride,
            m_commodityBase,
            m_commodityDetail,
            m_instrumentTypeCode,
            m_instrumentTypeLanguages,
            m_riskCountryCode,
            m_taxCountryCode,
            m_bloombergCode,
            m_sedol,
            m_reutersMIC,
            m_operatingMIC,
            m_mainExchange,
            m_marketStatus,
            m_curMktCapUsd,
            m_avgDailyValueTraded30dUsd,
            m_avgDailyTraded3mUsd,
            m_eqyFreeFloatPct,
            m_securityTyp,
            m_isPairCurrencyInFractionalParts,
            m_pointMultiplier,
            m_prophetPrimaryInstrumentCode,
            m_couponRate,
            m_cheapestToDeliverDate,
            m_marketCode,
            m_marketAlias,
            m_marketDataMappingCode,
            m_cfdCfi,
            m_sbCfi,
            m_isMifidiiReportable,
            m_securityDescription,
            m_underlyingInstrumentCode,
            m_underlyingInstrumentAlias,
            m_isCcyInFrctnlPrts,
            m_financingCurrency,
            m_imIsTestInstrument,
            m_imIsDemoOnly,
            m_isFixedSpreadInstrument,
            m_isGuaranteedStopInstrmnt,
            m_isMetatraderInstrument,
            m_isEsmaDuplicate,
            m_isFindableInstrument,
            m_isTradableInstrument,
            m_apiCode,
            m_workflowStatus,
            m_isAsicDuplicate,
            m_firstNoticeDate,
            m_prophetBcEnabled,
            m_isValueDateRolloverInstrument,
            m_isPreventCfdTradingOnFxAccounts,
            m_taxSecurityType,
            m_productIdValue,
            m_productSelector,
            m_isInstrumentSI,
            m_CUSIP,
            m_fundType,
            m_prophetCarryAskOverride,
            m_prophetCarryBidOverride,
            m_prophetCarryRateOverridden,
            m_instrumentDelistedInMarket,
            m_BBCountryFullName,
            m_BBReferenceTaxSecurityType,
            m_SAXOInstrumentCode,
            m_assetTypeDescription,
            m_assetSubTypeDescription,
            m_optionRoot,
            m_isNationalDeclarationProduct,
            m_isCAInstrument,
            m_fundAssetClassFocus,
            m_annadsbCommBaseProduct,
            m_annadsbCommSubProduct,
            m_annadsbCommAddSubProduct,
            m_annadsbCommHeaderProduct,
            m_annadsbCommUnderlierId,
            m_annadsbCommUnderlierIdSrc,
            m_upiCfd,
            m_upiSb,
            m_upiOptionsEurCashPut,
            m_upiOptionsEurCashCall,
            m_upiOptionsEurPhysPut,
            m_upiOptionsEurPhysCall,
            m_upiOptionsAmerCashPut,
            m_upiOptionsAmerCashCall,
            m_upiOptionsAmerPhysPut,
            m_upiOptionsAmerPhysCall,
            m_upiOptionsBermCashPut,
            m_upiOptionsBermCashCall,
            m_upiOptionsBermPhysPut,
            m_upiOptionsBermPhysCall,
            m_upiCfdCountdown,
            m_upiSbCountdown,
            m_countdownCfi,
            m_optionsAmerCashCallCFI,
            m_optionsAmerCashPutCFI,
            m_optionsAmerPhysCallCFI,
            m_optionsAmerPhysPutCFI,
            m_optionsBermCashCallCFI,
            m_optionsBermCashPutCFI,
            m_optionsBermPhysPutCFI,
            m_optionsBermPhysCallCFI,
            m_optionsEurCashCallCFI,
            m_optionsEurCashPutCFI,
            m_optionsEurPhysCallCFI,
            m_optionsEurPhysPutCFI,
            m_currentSharesOutstanding,
            m_upiSbOptions,
            m_sbOptionCFI,
            m_isUseInstForFxConversion,
            m_isOTC,
            m_bbCountryIncorporation,
            m_refTaxCountry,
            m_refCountryIncorporationDsc,
            m_upiCfdOptions,
            m_cfdOptionCFI,
            m_isIsaEligible
        );
    }

    @Override
    public boolean equals(Object object)
    {
        if(object instanceof AbstractProductSetInstrumentEntity)
        {
            if(!super.equals(object))
                return false;
            AbstractProductSetInstrumentEntity that = (AbstractProductSetInstrumentEntity) object;
            return Objects.equal(this.m_platform, that.m_platform) &&
                Objects.equal(this.m_commodityTypeName, that.m_commodityTypeName) &&
                Objects.equal(this.m_companySectorName, that.m_companySectorName) &&
                Objects.equal(this.m_countryClassificationName, that.m_countryClassificationName) &&
                Objects.equal(this.m_currency, that.m_currency) &&
                Objects.equal(this.m_countryCode, that.m_countryCode) &&
                Objects.equal(this.m_feedSymbol, that.m_feedSymbol) && Objects.equal(this.m_ISIN, that.m_ISIN) &&
                Objects.equal(this.m_instrumentCode, that.m_instrumentCode) &&
                Objects.equal(this.m_instrumentType, that.m_instrumentType) && Objects.equal(this.m_MIC, that.m_MIC) &&
                Objects.equal(this.m_pairCurrency, that.m_pairCurrency) && Objects.equal(this.m_RIC, that.m_RIC) &&
                Objects.equal(this.m_shortName, that.m_shortName) && Objects.equal(this.m_mmuid, that.m_mmuid) &&
                Objects.equal(this.m_mmuid2, that.m_mmuid2) && Objects.equal(this.m_mmvalueid, that.m_mmvalueid) &&
                Objects.equal(this.m_prophetBandingAlgorithm, that.m_prophetBandingAlgorithm) &&
                Objects.equal(this.m_prophetBandingFixedSpread, that.m_prophetBandingFixedSpread) &&
                Objects.equal(this.m_prophetBandingSpreadFactor, that.m_prophetBandingSpreadFactor) &&
                Objects.equal(this.m_mmInstIdCfd, that.m_mmInstIdCfd) &&
                Objects.equal(this.m_mmInstIdSbIr, that.m_mmInstIdSbIr) &&
                Objects.equal(this.m_mmInstIdSbUk, that.m_mmInstIdSbUk) &&
                Objects.equal(this.m_pricingStartDate, that.m_pricingStartDate) &&
                Objects.equal(this.m_firstTradingDate, that.m_firstTradingDate) &&
                Objects.equal(this.m_lastRolloverDate, that.m_lastRolloverDate) &&
                Objects.equal(this.m_automaticRolloverDate, that.m_automaticRolloverDate) &&
                Objects.equal(this.m_lastTradingDate, that.m_lastTradingDate) &&
                Objects.equal(this.m_cashSettlementDate, that.m_cashSettlementDate) &&
                Objects.equal(this.m_expiryDate, that.m_expiryDate) &&
                Objects.equal(this.m_isSkipRollover, that.m_isSkipRollover) &&
                Objects.equal(this.m_lastTradingDateDesc, that.m_lastTradingDateDesc) &&
                Objects.equal(this.m_lastSettlementDateDesc, that.m_lastSettlementDateDesc) &&
                Objects.equal(this.m_lastRolloverDateDesc, that.m_lastRolloverDateDesc) &&
                Objects.equal(this.m_contractCode, that.m_contractCode) &&
                Objects.equal(this.m_rolloverTargetCode, that.m_rolloverTargetCode) &&
                Objects.equal(this.m_cmcCashInstrumentCode, that.m_cmcCashInstrumentCode) &&
                Objects.equal(this.m_cmcFinancialInstrumentType, that.m_cmcFinancialInstrumentType) &&
                Objects.equal(this.m_exchangeProductCode, that.m_exchangeProductCode) &&
                Objects.equal(this.m_isPRRQualifyingIndex, that.m_isPRRQualifyingIndex) &&
                Objects.equal(this.m_positionRiskRequirementPercent, that.m_positionRiskRequirementPercent) &&
                Objects.equal(this.m_instrumentNames, that.m_instrumentNames) &&
                Objects.equal(this.m_securityType, that.m_securityType) &&
                Objects.equal(this.m_contractSizeOverride, that.m_contractSizeOverride) &&
                Objects.equal(this.m_commodityBase, that.m_commodityBase) &&
                Objects.equal(this.m_commodityDetail, that.m_commodityDetail) &&
                Objects.equal(this.m_instrumentTypeCode, that.m_instrumentTypeCode) &&
                Objects.equal(this.m_instrumentTypeLanguages, that.m_instrumentTypeLanguages) &&
                Objects.equal(this.m_riskCountryCode, that.m_riskCountryCode) &&
                Objects.equal(this.m_taxCountryCode, that.m_taxCountryCode) &&
                Objects.equal(this.m_bloombergCode, that.m_bloombergCode) &&
                Objects.equal(this.m_sedol, that.m_sedol) && Objects.equal(this.m_reutersMIC, that.m_reutersMIC) &&
                Objects.equal(this.m_operatingMIC, that.m_operatingMIC) &&
                Objects.equal(this.m_mainExchange, that.m_mainExchange) &&
                Objects.equal(this.m_marketStatus, that.m_marketStatus) &&
                Objects.equal(this.m_curMktCapUsd, that.m_curMktCapUsd) &&
                Objects.equal(this.m_avgDailyValueTraded30dUsd, that.m_avgDailyValueTraded30dUsd) &&
                Objects.equal(this.m_avgDailyTraded3mUsd, that.m_avgDailyTraded3mUsd) &&
                Objects.equal(this.m_eqyFreeFloatPct, that.m_eqyFreeFloatPct) &&
                Objects.equal(this.m_securityTyp, that.m_securityTyp) &&
                Objects.equal(this.m_isPairCurrencyInFractionalParts, that.m_isPairCurrencyInFractionalParts) &&
                Objects.equal(this.m_pointMultiplier, that.m_pointMultiplier) &&
                Objects.equal(this.m_prophetPrimaryInstrumentCode, that.m_prophetPrimaryInstrumentCode) &&
                Objects.equal(this.m_couponRate, that.m_couponRate) &&
                Objects.equal(this.m_cheapestToDeliverDate, that.m_cheapestToDeliverDate) &&
                Objects.equal(this.m_marketCode, that.m_marketCode) &&
                Objects.equal(this.m_marketAlias, that.m_marketAlias) &&
                Objects.equal(this.m_marketDataMappingCode, that.m_marketDataMappingCode) &&
                Objects.equal(this.m_cfdCfi, that.m_cfdCfi) &&
                Objects.equal(this.m_sbCfi, that.m_sbCfi) &&
                Objects.equal(this.m_isMifidiiReportable, that.m_isMifidiiReportable) &&
                Objects.equal(this.m_securityDescription, that.m_securityDescription) &&
                Objects.equal(this.m_underlyingInstrumentCode, that.m_underlyingInstrumentCode) &&
                Objects.equal(this.m_underlyingInstrumentAlias, that.m_underlyingInstrumentAlias) &&                
                Objects.equal(this.m_isCcyInFrctnlPrts, that.m_isCcyInFrctnlPrts) &&
                Objects.equal(this.m_financingCurrency, that.m_financingCurrency) &&
                Objects.equal(this.m_imIsTestInstrument, that.m_imIsTestInstrument) &&
                Objects.equal(this.m_imIsDemoOnly, that.m_imIsDemoOnly) &&
                Objects.equal(this.m_isFixedSpreadInstrument, that.m_isFixedSpreadInstrument) &&
                Objects.equal(this.m_isGuaranteedStopInstrmnt, that.m_isGuaranteedStopInstrmnt) &&
                Objects.equal(this.m_isMetatraderInstrument, that.m_isMetatraderInstrument) &&
                Objects.equal(this.m_isEsmaDuplicate, that.m_isEsmaDuplicate) &&
                Objects.equal(this.m_isFindableInstrument, that.m_isFindableInstrument) &&
                Objects.equal(this.m_isTradableInstrument, that.m_isTradableInstrument) &&
                Objects.equal(this.m_apiCode, that.m_apiCode) &&
                Objects.equal(this.m_workflowStatus, that.m_workflowStatus) &&
                Objects.equal(this.m_isAsicDuplicate, that.m_isAsicDuplicate) &&
                Objects.equal(this.m_firstNoticeDate, that.m_firstNoticeDate) &&
                Objects.equal(this.m_prophetBcEnabled, that.m_prophetBcEnabled) &&
                Objects.equal(this.m_isValueDateRolloverInstrument, that.m_isValueDateRolloverInstrument) &&
                Objects.equal(this.m_isPreventCfdTradingOnFxAccounts, that.m_isPreventCfdTradingOnFxAccounts) &&
                Objects.equal(this.m_taxSecurityType, that.m_taxSecurityType) &&
                Objects.equal(this.m_productIdValue, that.m_productIdValue) &&
                Objects.equal(this.m_productSelector, that.m_productSelector) &&
                Objects.equal(this.m_isInstrumentSI, that.m_isInstrumentSI) &&
                Objects.equal(this.m_CUSIP, that.m_CUSIP) &&
                Objects.equal(this.m_fundType, that.m_fundType) &&
                Objects.equal(this.m_prophetCarryAskOverride, that.m_prophetCarryAskOverride) &&
                Objects.equal(this.m_prophetCarryBidOverride, that.m_prophetCarryBidOverride) &&
                Objects.equal(this.m_prophetCarryRateOverridden, that.m_prophetCarryRateOverridden) &&
                Objects.equal(this.m_instrumentDelistedInMarket, that.m_instrumentDelistedInMarket) &&
                Objects.equal(this.m_BBCountryFullName, that.m_BBCountryFullName) &&
                Objects.equal(this.m_BBReferenceTaxSecurityType, that.m_BBReferenceTaxSecurityType) &&
                Objects.equal(this.m_SAXOInstrumentCode, that.m_SAXOInstrumentCode) &&
                Objects.equal(this.m_assetTypeDescription, that.m_assetTypeDescription) &&
                Objects.equal(this.m_assetSubTypeDescription, that.m_assetSubTypeDescription) &&
                Objects.equal(this.m_optionRoot, that.m_optionRoot) &&
                Objects.equal(this.m_isNationalDeclarationProduct, that.m_isNationalDeclarationProduct) &&
                Objects.equal(this.m_isCAInstrument, that.m_isCAInstrument) &&
                Objects.equal(this.m_fundAssetClassFocus, that.m_fundAssetClassFocus) &&
                Objects.equal(this.m_annadsbCommBaseProduct, that.m_annadsbCommBaseProduct) &&
                Objects.equal(this.m_annadsbCommSubProduct, that.m_annadsbCommSubProduct) &&
                Objects.equal(this.m_annadsbCommAddSubProduct, that.m_annadsbCommAddSubProduct) &&
                Objects.equal(this.m_annadsbCommHeaderProduct, that.m_annadsbCommHeaderProduct) &&
                Objects.equal(this.m_annadsbCommUnderlierId, that.m_annadsbCommUnderlierId) &&
                Objects.equal(this.m_annadsbCommUnderlierIdSrc, that.m_annadsbCommUnderlierIdSrc) &&
                Objects.equal(this.m_upiCfd, that.m_upiCfd) &&
                Objects.equal(this.m_upiSb, that.m_upiSb) &&
                Objects.equal(this.m_upiOptionsEurCashPut, that.m_upiOptionsEurCashPut) &&
                Objects.equal(this.m_upiOptionsEurCashCall, that.m_upiOptionsEurCashCall) &&
                Objects.equal(this.m_upiOptionsEurPhysPut, that.m_upiOptionsEurPhysPut) &&
                Objects.equal(this.m_upiOptionsEurPhysCall, that.m_upiOptionsEurPhysCall) &&
                Objects.equal(this.m_upiOptionsAmerCashPut, that.m_upiOptionsAmerCashPut) &&
                Objects.equal(this.m_upiOptionsAmerCashCall, that.m_upiOptionsAmerCashCall) &&
                Objects.equal(this.m_upiOptionsAmerPhysPut, that.m_upiOptionsAmerPhysPut) &&
                Objects.equal(this.m_upiOptionsAmerPhysCall, that.m_upiOptionsAmerPhysCall) &&
                Objects.equal(this.m_upiOptionsBermCashPut, that.m_upiOptionsBermCashPut) &&
                Objects.equal(this.m_upiOptionsBermCashCall, that.m_upiOptionsBermCashCall) &&
                Objects.equal(this.m_upiOptionsBermPhysPut, that.m_upiOptionsBermPhysPut) &&
                Objects.equal(this.m_upiOptionsBermPhysCall, that.m_upiOptionsBermPhysCall) &&
                Objects.equal(this.m_upiCfdCountdown, that.m_upiCfdCountdown) &&
                Objects.equal(this.m_upiSbCountdown,that.m_upiSbCountdown) &&
                Objects.equal(this.m_countdownCfi, that.m_countdownCfi) &&
                Objects.equal(this.m_optionsAmerCashCallCFI, that.m_optionsAmerCashCallCFI) &&
                Objects.equal(this.m_optionsAmerCashPutCFI, that.m_optionsAmerCashPutCFI) &&
                Objects.equal(this.m_optionsAmerPhysCallCFI, that.m_optionsAmerPhysCallCFI) &&
                Objects.equal(this.m_optionsAmerPhysPutCFI, that.m_optionsAmerPhysPutCFI) &&
                Objects.equal(this.m_optionsBermCashCallCFI, that.m_optionsBermCashCallCFI) &&
                Objects.equal(this.m_optionsBermCashPutCFI, that.m_optionsBermCashPutCFI) &&
                Objects.equal(this.m_optionsBermPhysPutCFI, that.m_optionsBermPhysPutCFI) &&
                Objects.equal(this.m_optionsBermPhysCallCFI, that.m_optionsBermPhysCallCFI) &&
                Objects.equal(this.m_optionsEurCashCallCFI, that.m_optionsEurCashCallCFI) &&
                Objects.equal(this.m_optionsEurCashPutCFI, that.m_optionsEurCashPutCFI) &&
                Objects.equal(this.m_optionsEurPhysCallCFI, that.m_optionsEurPhysCallCFI) &&
                Objects.equal(this.m_optionsEurPhysPutCFI, that.m_optionsEurPhysPutCFI) &&
                Objects.equal(this.m_currentSharesOutstanding, that.m_currentSharesOutstanding) &&
                Objects.equal(this.m_upiSbOptions, that.m_upiSbOptions) &&
                Objects.equal(this.m_sbOptionCFI, that.m_sbOptionCFI) &&
                Objects.equal(this.m_isUseInstForFxConversion,that.m_isUseInstForFxConversion) &&
                Objects.equal(this.m_isOTC,that.m_isOTC) &&
                Objects.equal(this.m_bbCountryIncorporation,that.m_bbCountryIncorporation) &&
                Objects.equal(this.m_refTaxCountry,that.m_refTaxCountry) &&
                Objects.equal(this.m_refCountryIncorporationDsc,that.m_refCountryIncorporationDsc) &&
                Objects.equal(this.m_upiCfdOptions, that.m_upiCfdOptions) &&
                Objects.equal(this.m_cfdOptionCFI, that.m_cfdOptionCFI) &&
                Objects.equal(this.m_isIsaEligible, that.m_isIsaEligible);
        }
        return false;
    }

    @Override
    public String toString()
    {
        return Objects.toStringHelper(this)
            .add("super", super.toString())
            .add("m_platform", m_platform)
            .add("m_commodityTypeName", m_commodityTypeName)
            .add("m_companySectorName", m_companySectorName)
            .add("m_countryClassificationName", m_countryClassificationName)
            .add("m_currency", m_currency)
            .add("m_countryCode", m_countryCode)
            .add("m_feedSymbol", m_feedSymbol)
            .add("m_ISIN", m_ISIN)
            .add("m_instrumentCode", m_instrumentCode)
            .add("m_instrumentType", m_instrumentType)
            .add("m_MIC", m_MIC)
            .add("m_pairCurrency", m_pairCurrency)
            .add("m_RIC", m_RIC)
            .add("m_shortName", m_shortName)
            .add("m_mmuid", m_mmuid)
            .add("m_mmuid2", m_mmuid2)
            .add("m_mmvalueid", m_mmvalueid)
            .add("m_prophetBandingAlgorithm", m_prophetBandingAlgorithm)
            .add("m_prophetBandingFixedSpread", m_prophetBandingFixedSpread)
            .add("m_prophetBandingSpreadFactor", m_prophetBandingSpreadFactor)
            .add("m_mmInstIdCfd", m_mmInstIdCfd)
            .add("m_mmInstIdSbIr", m_mmInstIdSbIr)
            .add("m_mmInstIdSbUk", m_mmInstIdSbUk)
            .add("m_pricingStartDate", m_pricingStartDate)
            .add("m_firstTradingDate", m_firstTradingDate)
            .add("m_lastRolloverDate", m_lastRolloverDate)
            .add("m_automaticRolloverDate", m_automaticRolloverDate)
            .add("m_lastTradingDate", m_lastTradingDate)
            .add("m_cashSettlementDate", m_cashSettlementDate)
            .add("m_expiryDate", m_expiryDate)
            .add("m_isSkipRollover", m_isSkipRollover)
            .add("m_lastTradingDateDesc", m_lastTradingDateDesc)
            .add("m_lastSettlementDateDesc", m_lastSettlementDateDesc)
            .add("m_lastRolloverDateDesc", m_lastRolloverDateDesc)
            .add("m_contractCode", m_contractCode)
            .add("m_rolloverTargetCode", m_rolloverTargetCode)
            .add("m_cmcCashInstrumentCode", m_cmcCashInstrumentCode)
            .add("m_cmcFinancialInstrumentType", m_cmcFinancialInstrumentType)
            .add("m_exchangeProductCode", m_exchangeProductCode)
            .add("m_isPRRQualifyingIndex", m_isPRRQualifyingIndex)
            .add("m_positionRiskRequirementPercent", m_positionRiskRequirementPercent)
            .add("m_instrumentNames", m_instrumentNames)
            .add("m_securityType", m_securityType)
            .add("m_contractSizeOverride", m_contractSizeOverride)
            .add("m_commodityBase", m_commodityBase)
            .add("m_commodityDetail", m_commodityDetail)
            .add("m_instrumentTypeCode", m_instrumentTypeCode)
            .add("m_instrumentTypeLanguages", m_instrumentTypeLanguages)
            .add("m_riskCountryCode", m_riskCountryCode)
            .add("m_taxCountryCode", m_taxCountryCode)
            .add("m_bloombergCode", m_bloombergCode)
            .add("m_sedol", m_sedol)
            .add("m_reutersMIC", m_reutersMIC)
            .add("m_operatingMIC", m_operatingMIC)
            .add("m_mainExchange", m_mainExchange)
            .add("m_marketStatus", m_marketStatus)
            .add("m_curMktCapUsd", m_curMktCapUsd)
            .add("m_avgDailyValueTraded30dUsd", m_avgDailyValueTraded30dUsd)
            .add("m_avgDailyTraded3mUsd", m_avgDailyTraded3mUsd)
            .add("m_eqyFreeFloatPct", m_eqyFreeFloatPct)
            .add("m_securityTyp", m_securityTyp)
            .add("m_isPairCurrencyInFractionalParts", m_isPairCurrencyInFractionalParts)
            .add("m_pointMultiplier", m_pointMultiplier)
            .add("m_prophetPrimaryInstrumentCode", m_prophetPrimaryInstrumentCode)
            .add("m_couponRate", m_couponRate)
            .add("m_cheapestToDeliverDate", m_cheapestToDeliverDate)
            .add("m_marketCode", m_marketCode)
            .add("m_marketAlias", m_marketAlias)
            .add("m_marketDataMappingCode", m_marketDataMappingCode)
            .add("m_cfdCfi", m_cfdCfi)
            .add("m_sbCfi", m_sbCfi)
            .add("m_isMifidiiReportable", m_isMifidiiReportable)
            .add("m_securityDescription", m_securityDescription)
            .add("m_underlyingInstrumentCode", m_underlyingInstrumentCode)
            .add("m_underlyingInstrumentAlias", m_underlyingInstrumentAlias)            
            .add("m_isCcyInFrctnlPrts", m_isCcyInFrctnlPrts)
            .add("m_financingCurrency", m_financingCurrency)
            .add("m_imIsTestInstrument", m_imIsTestInstrument)
            .add("m_imIsDemoOnly", m_imIsDemoOnly)
            .add("m_isFixedSpreadInstrument", m_isFixedSpreadInstrument)
            .add("m_isGuaranteedStopInstrmnt", m_isGuaranteedStopInstrmnt)
            .add("m_isMetatraderInstrument", m_isMetatraderInstrument)
            .add("m_isEsmaDuplicate", m_isEsmaDuplicate)
            .add("m_isFindableInstrument", m_isFindableInstrument)
            .add("m_isTradableInstrument", m_isTradableInstrument)
            .add("m_apiCode", m_apiCode)
            .add("m_workflowStatus", m_workflowStatus)
            .add("m_isAsicDuplicate", m_isAsicDuplicate)
            .add("m_firstNoticeDate", m_firstNoticeDate)
            .add("m_prophetBcEnabled", m_prophetBcEnabled)
            .add("m_isValueDateRolloverInstrument", m_isValueDateRolloverInstrument)
            .add("m_isPreventCfdTradingOnFxAccounts", m_isPreventCfdTradingOnFxAccounts)
            .add("m_taxSecurityType", m_taxSecurityType)
            .add("m_productIdValue", m_productIdValue)
            .add("m_productSelector", m_productSelector)
            .add("m_isInstrumentSI", m_isInstrumentSI)
            .add("m_CUSIP", m_CUSIP)
            .add("m_fundType", m_fundType)
            .add("m_prophetCarryAskOverride", m_prophetCarryAskOverride)
            .add("m_prophetCarryBidOverride", m_prophetCarryBidOverride)
            .add("m_prophetCarryRateOverridden", m_prophetCarryRateOverridden)
            .add("m_instrumentDelistedInMarket", m_instrumentDelistedInMarket)
            .add("m_BBCountryFullName", m_BBCountryFullName)
            .add("m_primaryMarket", m_BBReferenceTaxSecurityType)
            .add("m_SAXOInstrumentCode", m_SAXOInstrumentCode)
            .add("m_assetTypeDescription", m_assetTypeDescription)
            .add("m_assetSubTypeDescription", m_assetSubTypeDescription)
            .add("m_optionRoot", m_optionRoot)
            .add("m_isNationalDeclarationProduct", m_isNationalDeclarationProduct)
            .add("m_isCAInstrument", m_isCAInstrument)
            .add("m_fundAssetClassFocus", m_fundAssetClassFocus)
            .add("m_annadsbCommBaseProduct", m_annadsbCommBaseProduct)
            .add("m_annadsbCommSubProduct", m_annadsbCommSubProduct)
            .add("m_annadsbCommAddSubProduct", m_annadsbCommAddSubProduct)
            .add("m_annadsbCommHeaderProduct", m_annadsbCommHeaderProduct)
            .add("m_annadsbCommUnderlierId", m_annadsbCommUnderlierId)
            .add("m_annadsbCommUnderlierIdSrc", m_annadsbCommUnderlierIdSrc)
            .add("m_upiCfd", m_upiCfd)
            .add("m_upiSb", m_upiSb)
            .add("m_upiOptionsEurCashPut", m_upiOptionsEurCashPut)
            .add("m_upiOptionsEurCashCall", m_upiOptionsEurCashCall)
            .add("m_upiOptionsEurPhysPut", m_upiOptionsEurPhysPut)
            .add("m_upiOptionsEurPhysCall", m_upiOptionsEurPhysCall)
            .add("m_upiOptionsAmerCashPut", m_upiOptionsAmerCashPut)
            .add("m_upiOptionsAmerCashCall", m_upiOptionsAmerCashCall)
            .add("m_upiOptionsAmerPhysPut", m_upiOptionsAmerPhysPut)
            .add("m_upiOptionsAmerPhysCall", m_upiOptionsAmerPhysCall)
            .add("m_upiOptionsBermCashPut", m_upiOptionsBermCashPut)
            .add("m_upiOptionsBermCashCall", m_upiOptionsBermCashCall)
            .add("m_upiOptionsBermPhysPut", m_upiOptionsBermPhysPut)
            .add("m_upiOptionsBermPhysCall", m_upiOptionsBermPhysCall)
            .add("m_upiCfdCountdown ", m_upiCfdCountdown)
            .add("m_upiSbCountdown", m_upiSbCountdown)
            .add("m_countdownCfi", m_countdownCfi)
            .add("m_optionsAmerCashCallCFI", m_optionsAmerCashCallCFI)
            .add("m_optionsAmerCashPutCFI", m_optionsAmerCashPutCFI)
            .add("m_optionsAmerPhysCallCFI", m_optionsAmerPhysCallCFI)
            .add("m_optionsAmerPhysPutCFI", m_optionsAmerPhysPutCFI)
            .add("m_optionsBermCashCallCFI", m_optionsBermCashCallCFI)
            .add("m_optionsBermCashPutCFI", m_optionsBermCashPutCFI)
            .add("m_optionsBermPhysPutCFI", m_optionsBermPhysPutCFI)
            .add("m_optionsBermPhysCallCFI", m_optionsBermPhysCallCFI)
            .add("m_optionsEurCashCallCFI", m_optionsEurCashCallCFI)
            .add("m_optionsEurCashPutCFI", m_optionsEurCashPutCFI)
            .add("m_optionsEurPhysCallCFI", m_optionsEurPhysCallCFI)
            .add("m_optionsEurPhysPutCFI", m_optionsEurPhysPutCFI)
            .add("m_currentSharesOutstanding", m_currentSharesOutstanding)
            .add("m_upiSbOptions", m_upiSbOptions)
            .add("m_sbOptionCFI", m_sbOptionCFI)
            .add("m_isUseInstForFxConversion", m_isUseInstForFxConversion)
            .add("m_isOTC", m_isOTC)
            .add("m_bbCountryIncorporation", m_bbCountryIncorporation)
            .add("m_refTaxCountry", m_refTaxCountry)
            .add("m_refCountryIncorporationDsc", m_refCountryIncorporationDsc)
            .add("m_upiCfdOptions", m_upiCfdOptions)
            .add("m_cfdOptionCFI", m_cfdOptionCFI)
            .add("m_isIsaEligible", m_isIsaEligible)
            .toString();
    }
}
