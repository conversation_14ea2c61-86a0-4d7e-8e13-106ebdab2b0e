/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 *
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.text.SimpleDateFormat;
import java.util.Date;

public class MessageReplay
{
    private String m_messageKey;
    private Date m_messageDate;
    private Date m_replayDate;
    private int m_version;
    private String m_serviceOperationContract;

    public MessageReplay()
    {
    }

    public MessageReplay(String messageKey,
                         Date messageDate,
                         Date replayDate,
                         int version,
                         String serviceOperationContract)
    {
        m_messageKey = messageKey;
        m_messageDate = messageDate;
        m_replayDate = replayDate;
        m_version = version;
        m_serviceOperationContract = serviceOperationContract;
    }

    public String getMessageKey()
    {
        return m_messageKey;
    }

    public void setMessageKey(String messageKey)
    {
        m_messageKey = messageKey;
    }

    public Date getMessageDate()
    {
        return m_messageDate;
    }

    public void setMessageDate(Date messageDate)
    {
        m_messageDate = messageDate;
    }

    public Date getReplayDate()
    {
        return m_replayDate;
    }

    public void setReplayDate(Date replayDate)
    {
        m_replayDate = replayDate;
    }

    public int getVersion()
    {
        return m_version;
    }

    public void setVersion(int version)
    {
        m_version = version;
    }

    public String getServiceOperationContract()
    {
        return m_serviceOperationContract;
    }

    public void setServiceOperationContract(String serviceOperationContract)
    {
        m_serviceOperationContract = serviceOperationContract;
    }

    public void incrementVersion()
    {
        m_version++;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((m_messageDate == null) ? 0 : m_messageDate.hashCode());
        result = prime * result + ((m_messageKey == null) ? 0 : m_messageKey.hashCode());
        result = prime * result + ((m_replayDate == null) ? 0 : m_replayDate.hashCode());
        result = prime * result + ((m_serviceOperationContract == null) ? 0 : m_serviceOperationContract.hashCode());
        result = prime * result + m_version;
        return result;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(this == obj)
        {
            return true;
        }
        if(obj == null)
        {
            return false;
        }
        if(!(obj instanceof MessageReplay))
        {
            return false;
        }
        MessageReplay other = (MessageReplay) obj;
        if(m_messageDate == null)
        {
            if(other.m_messageDate != null)
            {
                return false;
            }
        }
        else if(!m_messageDate.equals(other.m_messageDate))
        {
            return false;
        }
        if(m_messageKey == null)
        {
            if(other.m_messageKey != null)
            {
                return false;
            }
        }
        else if(!m_messageKey.equals(other.m_messageKey))
        {
            return false;
        }
        if(m_replayDate == null)
        {
            if(other.m_replayDate != null)
            {
                return false;
            }
        }
        else if(!m_replayDate.equals(other.m_replayDate))
        {
            return false;
        }
        if(m_serviceOperationContract == null)
        {
            if(other.m_serviceOperationContract != null)
            {
                return false;
            }
        }
        else if(!m_serviceOperationContract.equals(other.m_serviceOperationContract))
        {
            return false;
        }
        if(m_version != other.m_version)
        {
            return false;
        }
        return true;
    }

    @Override
    public String toString()
    {
        StringBuilder builder = new StringBuilder();
        builder.append("MessageReplay [m_messageKey=");
        builder.append(m_messageKey);
        builder.append(", m_messageDate=");
        builder.append(
            m_messageDate == null ? null : new SimpleDateFormat("dd/MM/yyyy HH:mm:ss.SSS z").format(m_messageDate));
        builder.append(", m_replayDate=");
        builder.append(
            m_replayDate == null ? null : new SimpleDateFormat("dd/MM/yyyy HH:mm:ss.SSS z").format(m_replayDate));
        builder.append(", m_version=");
        builder.append(m_version);
        builder.append(", m_serviceOperationContract=");
        builder.append(m_serviceOperationContract);
        builder.append("]");
        return builder.toString();
    }
}
