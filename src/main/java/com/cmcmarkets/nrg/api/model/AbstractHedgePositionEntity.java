/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import com.cmcmarkets.framework.messaging.common.model.Version;
import com.cmcmarkets.hedging.api.model.AssetClass;
import com.cmcmarkets.hedging.api.model.PositionDirection;
import com.cmcmarkets.hedging.api.model.PositionType;
import com.cmcmarkets.hedging.api.model.RiskBucket;
import com.cmcmarkets.hedging.api.model.SettlementStatus;
import com.cmcmarkets.nrg.pms.DbrProductCache;

public abstract class AbstractHedgePositionEntity extends AbstractEntity implements HedgePositionEntity
{
    protected Integer m_contractVersion;
    protected Long m_id;
    protected Long m_brokerAccountNumber;
    protected String m_brokerCode;
    protected String m_externalBrokerAccountNumber;
    protected String m_instrumentCode;
    protected String m_instrumentCodeExternal;
    protected AssetClass m_assetClass;
    protected RiskBucket m_riskBucket;
    protected String m_underlyingInstrumentCode;
    protected Date m_expiryDate;
    protected String m_expiryMonthCode;
    protected Date m_valueDate;
    protected PositionType m_positionType;
    protected Date m_positionDate;
    protected PositionDirection m_direction;
    protected BigDecimal m_quantity;
    protected BigDecimal m_price;
    protected BigDecimal m_amount;
    protected String m_currency;
    protected BigDecimal m_evaluationPrice;
    protected BigDecimal m_evaluationAmount;
    protected BigDecimal m_profitLoss;
    protected BigDecimal m_commission;
    protected Version m_version;
    protected Boolean m_isProductCurrencyInFractionalParts;
    protected BigDecimal m_productFractionalPartRatio;
    protected String m_productCurrency;
    protected Long m_openingReference;
    private Date m_redemptionDate;
	private Date m_currentCouponDate;
	private Integer m_accruedInterestDays;
	private BigDecimal m_accruedInterestAmount;
	private Integer m_evaluationAccruedInterestDays;
	private BigDecimal m_evaluationAccruedInterestAmount;
	private BigDecimal m_effectiveInterestRate;
	private BigDecimal m_effectiveInterestBaseAmount;
	private BigDecimal m_afsReserveAmount;

	private Date m_redemptionPaymentDate;
	private Date m_currentCouponPaymentDate;
	private Boolean m_wasManuallyCorrected;
	private BigDecimal m_newAfsReserveAmount;
	private BigDecimal m_newEffectiveInterestBaseAmount;
	private BigDecimal m_quantitySettlementDate;
	private BigDecimal m_evaluationSpotPrice;
	private Date m_settlementDate;
	private Date m_settlementTime;
	private SettlementStatus m_settlementStatus;
	private BigDecimal m_carryingCosts;
    private DbrProductCache m_productCache;

    protected AbstractHedgePositionEntity(Date effectiveStartTimestamp)
    {
        super(effectiveStartTimestamp);
    }

    @Override
    public Integer getContractVersion()
    {
        return m_contractVersion;
    }

    public void setContractVersion(Integer contractVersion)
    {
        m_contractVersion = contractVersion;
    }

    @Override
    public Long getId()
    {
        return m_id;
    }

    public void setId(Long id)
    {
        m_id = id;
    }

    @Override
    public Long getBrokerAccountNumber()
    {
        return m_brokerAccountNumber;
    }

    public void setBrokerAccountNumber(Long brokerAccountNumber)
    {
        m_brokerAccountNumber = brokerAccountNumber;
    }

    @Override
    public String getBrokerCode()
    {
        return m_brokerCode;
    }

    public void setBrokerCode(String brokerCode)
    {
        m_brokerCode = brokerCode;
    }

    @Override
    public String getExternalBrokerAccountNumber()
    {
        return m_externalBrokerAccountNumber;
    }

    public void setExternalBrokerAccountNumber(String externalBrokerAccountNumber)
    {
        m_externalBrokerAccountNumber = externalBrokerAccountNumber;
    }

    @Override
    public String getInstrumentCode()
    {
        return m_instrumentCode;
    }

    public void setInstrumentCode(String instrumentCode)
    {
        m_instrumentCode = instrumentCode;
    }

    @Override
    public String getInstrumentCodeExternal()
    {
        return m_instrumentCodeExternal;
    }

    public void setInstrumentCodeExternal(String instrumentCodeExternal)
    {
        m_instrumentCodeExternal = instrumentCodeExternal;
    }

    @Override
    public AssetClass getAssetClass()
    {
        return m_assetClass;
    }

    public void setAssetClass(AssetClass assetClass)
    {
        m_assetClass = assetClass;
    }

    @Override
    public RiskBucket getRiskBucket()
    {
        return m_riskBucket;
    }

    public void setRiskBucket(RiskBucket riskBucket)
    {
        m_riskBucket = riskBucket;
    }

    @Override
    public String getUnderlyingInstrumentCode()
    {
        return m_underlyingInstrumentCode;
    }

    public void setUnderlyingInstrumentCode(String underlyingInstrumentCode)
    {
        m_underlyingInstrumentCode = underlyingInstrumentCode;
    }

    @Override
    public Date getExpiryDate()
    {
        return m_expiryDate;
    }

    public void setExpiryDate(Date expiryDate)
    {
        m_expiryDate = expiryDate;
    }

    @Override
    public String getExpiryMonthCode()
    {
        return m_expiryMonthCode;
    }

    public void setExpiryMonthCode(String expiryMonthCode)
    {
        m_expiryMonthCode = expiryMonthCode;
    }

    @Override
    public Date getValueDate()
    {
        return m_valueDate;
    }

    public void setValueDate(Date valueDate)
    {
        m_valueDate = valueDate;
    }

    @Override
    public PositionType getPositionType()
    {
        return m_positionType;
    }

    public void setPositionType(PositionType positionType)
    {
        m_positionType = positionType;
    }

    @Override
    public Date getPositionDate()
    {
        return m_positionDate;
    }

    public void setPositionDate(Date positionDate)
    {
        m_positionDate = positionDate;
    }

    @Override
    public PositionDirection getDirection()
    {
        return m_direction;
    }

    public void setDirection(PositionDirection direction)
    {
        m_direction = direction;
    }

    @Override
    public BigDecimal getQuantity()
    {
        return m_quantity;
    }

    public void setQuantity(BigDecimal quantity)
    {
        m_quantity = quantity;
    }

    @Override
    public BigDecimal getPrice()
    {
        return m_price;
    }

    public void setPrice(BigDecimal price)
    {
        m_price = price;
    }

    @Override
    public BigDecimal getAmount()
    {
        return m_amount;
    }

    public void setAmount(BigDecimal amount)
    {
        m_amount = amount;
    }

    @Override
    public String getCurrency()
    {
        return m_currency;
    }

    public void setCurrency(String currency)
    {
        m_currency = currency;
    }

    @Override
    public BigDecimal getEvaluationPrice()
    {
        return m_evaluationPrice;
    }

    public void setEvaluationPrice(BigDecimal evaluationPrice)
    {
        m_evaluationPrice = evaluationPrice;
    }

    @Override
    public BigDecimal getEvaluationAmount()
    {
        return m_evaluationAmount;
    }

    public void setEvaluationAmount(BigDecimal evaluationAmount)
    {
        m_evaluationAmount = evaluationAmount;
    }

    @Override
    public BigDecimal getProfitLoss()
    {
        return m_profitLoss;
    }

    public void setProfitLoss(BigDecimal profitLoss)
    {
        m_profitLoss = profitLoss;
    }

    @Override
    public BigDecimal getCommission()
    {
        return m_commission;
    }

    public void setCommission(BigDecimal commission)
    {
        m_commission = commission;
    }

    @Override
    public Version getVersion()
    {
        return m_version;
    }

    public void setVersion(Version version)
    {
        m_version = version;
    }

    public void setIsProductCurrencyInFractionalParts(Boolean m_isProductCurrencyInFractionalParts)
    {
        this.m_isProductCurrencyInFractionalParts = m_isProductCurrencyInFractionalParts;
    }

    @Override
    public Boolean isProductCurrencyInFractionalParts()
    {
        return m_isProductCurrencyInFractionalParts;
    }

    @Override
    public BigDecimal getProductFractionalPartRatio()
    {
        return m_productFractionalPartRatio;
    }

    public void setProductFractionalPartRatio(BigDecimal m_productFractionalPartRatio)
    {
        this.m_productFractionalPartRatio = m_productFractionalPartRatio;
    }

    @Override
    public String getProductCurrency()
    {
        return m_productCurrency;
    }

    public void setProductCurrency(String m_productCurrency)
    {
        this.m_productCurrency = m_productCurrency;
    }
    
    @Override
	public Long getOpeningReference()
	{
		return m_openingReference;
	}

	public void setOpeningReference(Long openingReference)
	{
		m_openingReference = openingReference;		
	}
	
	@Override
	public Date getRedemptionDate()
	{
		return m_redemptionDate;
	}
	
	public void setRedemptionDate(Date redemptionDate)
	{
		m_redemptionDate = redemptionDate;		
	}
	
	@Override
	public Date getCurrentCouponDate()
	{
		return m_currentCouponDate;
	}
	
	public void setCurrentCouponDate(Date currentCouponDate)
	{
		m_currentCouponDate = currentCouponDate;		
	}
	
	@Override
	public Integer getAccruedInterestDays()
	{
		return m_accruedInterestDays;
	}
	
	public void setAccruedInterestDays(Integer accruedInterestDays)
	{
		m_accruedInterestDays = accruedInterestDays;		
	}
	
	@Override
	public BigDecimal getAccruedInterestAmount()
	{
		return m_accruedInterestAmount;
	}
	
	public void setAccruedInterestAmount(BigDecimal accruedInterestAmount)
	{
		m_accruedInterestAmount = accruedInterestAmount;		
	}
	
	@Override
	public Integer getEvaluationAccruedInterestDays()
	{
		return m_evaluationAccruedInterestDays;
	}
	
	public void setEvaluationAccruedInterestDays(Integer evaluationAccruedInterestDays)
	{
		m_evaluationAccruedInterestDays = evaluationAccruedInterestDays;		
	}
	
	@Override
	public BigDecimal getEvaluationAccruedInterestAmount()
	{
		return m_evaluationAccruedInterestAmount;
	}
	
	public void setEvaluationAccruedInterestAmount(BigDecimal evaluationAccruedInterestAmount)
	{
		m_evaluationAccruedInterestAmount = evaluationAccruedInterestAmount;		
	}
	
	@Override
	public BigDecimal getEffectiveInterestRate()
	{
		return m_effectiveInterestRate;
	}
	
	public void setEffectiveInterestRate(BigDecimal effectiveInterestRate)
	{
		m_effectiveInterestRate = effectiveInterestRate;		
	}
	
	@Override
	public BigDecimal getEffectiveInterestBaseAmount()
	{
		return m_effectiveInterestBaseAmount;
	}
	
	public void setEffectiveInterestBaseAmount(BigDecimal effectiveInterestBaseAmount)
	{
		m_effectiveInterestBaseAmount = effectiveInterestBaseAmount;		
	}
	
	@Override
	public BigDecimal getAfsReserveAmount()
	{
		return m_afsReserveAmount;
	}
	
	public void setAfsReserveAmount(BigDecimal afsReserveAmount)
	{
		m_afsReserveAmount = afsReserveAmount;		
	}
	
	@Override
	public Date getRedemptionPaymentDate()
	{
		return m_redemptionPaymentDate;
	}
	
	public void setRedemptionPaymentDate(Date redemptionPaymentDate) 
	{
		m_redemptionPaymentDate = redemptionPaymentDate;
	}

	@Override
	public Date getCurrentCouponPaymentDate()
	{
		return m_currentCouponPaymentDate;
	}
	
	public void setCurrentCouponPaymentDate(Date currentCouponPaymentDate) 
	{
		m_currentCouponPaymentDate = currentCouponPaymentDate;
	}

	@Override
	public Boolean getWasManuallyCorrected()
	{
		return m_wasManuallyCorrected;
	}
	
	public void setWasManuallyCorrected(boolean wasManuallyCorrected) 
	{
		m_wasManuallyCorrected = wasManuallyCorrected;
	}
	
	@Override
	public BigDecimal getNewAfsReserveAmount()
	{
		return m_newAfsReserveAmount;
	}

	public void setNewAfsReserveAmount(BigDecimal newAfsReserveAmount) 
	{
		m_newAfsReserveAmount = newAfsReserveAmount;
	}

	@Override
	public BigDecimal getNewEffectiveInterestBaseAmount()
	{
		return m_newEffectiveInterestBaseAmount;
	}
	
	public void setNewEffectiveInterestBaseAmount(BigDecimal newEffectiveInterestBaseAmount) 
	{
		m_newEffectiveInterestBaseAmount = newEffectiveInterestBaseAmount;
	}
	
	@Override
	public BigDecimal getQuantitySettlementDate()
	{
		return m_quantitySettlementDate;
	}

	public void setQuantitySettlementDate(BigDecimal quantitySettlementDate) 
	{
		m_quantitySettlementDate = quantitySettlementDate;
	}
    
    @Override
    public BigDecimal getEvaluationSpotPrice()
    {
        return m_evaluationSpotPrice;
    }

    public void setEvaluationSpotPrice(BigDecimal evaluationSpotPrice) 
    {
        m_evaluationSpotPrice = evaluationSpotPrice;
    }
    
    @Override
    public Date getSettlementDate()
    {
        return m_settlementDate;
    }

    public void setSettlementDate(Date settlementDate) 
    {
        m_settlementDate = settlementDate;
    }
    
    @Override
    public Date getSettlementTime()
    {
        return m_settlementTime;
    }

    public void setSettlementTime(Date settlementTime) 
    {
        m_settlementTime = settlementTime;
    }
    
    @Override
    public SettlementStatus getSettlementStatus()
    {
        return m_settlementStatus;
    }

    public void setSettlementStatus(SettlementStatus settlementStatus) 
    {
        m_settlementStatus = settlementStatus;
    }
    
    @Override
    public BigDecimal getCarryingCosts()
    {
        return m_carryingCosts;
    }

    public void setCarryingCosts(BigDecimal carryingCosts) 
    {
        m_carryingCosts = carryingCosts;
    }

    public DbrProductCache getProductCache() {
        return m_productCache;
    }

    public void setProductCache(DbrProductCache productCache) {
        this.m_productCache = productCache;
    }
    
	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), m_contractVersion, m_id, m_brokerAccountNumber, m_brokerCode,
				m_externalBrokerAccountNumber, m_instrumentCode, m_instrumentCodeExternal, m_assetClass, m_riskBucket,
				m_underlyingInstrumentCode, m_expiryDate, m_expiryMonthCode, m_valueDate, m_positionType,
				m_positionDate, m_direction, m_quantity, m_price, m_amount, m_currency, m_evaluationPrice,
				m_evaluationAmount, m_profitLoss, m_commission, m_version, m_isProductCurrencyInFractionalParts,
				m_productFractionalPartRatio, m_productCurrency, m_openingReference, m_redemptionDate,
				m_currentCouponDate, m_accruedInterestDays, m_accruedInterestAmount, m_evaluationAccruedInterestDays,
				m_evaluationAccruedInterestAmount, m_effectiveInterestRate, m_effectiveInterestBaseAmount,
				m_afsReserveAmount, m_redemptionPaymentDate, m_currentCouponPaymentDate, m_wasManuallyCorrected,
				m_newAfsReserveAmount, m_newEffectiveInterestBaseAmount, m_quantitySettlementDate, m_evaluationSpotPrice,
				m_settlementDate, m_settlementTime, m_settlementStatus, m_carryingCosts);
	}

	@Override
	public boolean equals(Object object) {
		if (object instanceof AbstractHedgePositionEntity) {
			if (!super.equals(object)) {
				return false;
			}
			AbstractHedgePositionEntity that = (AbstractHedgePositionEntity) object;
			return Objects.equals(this.m_contractVersion, that.m_contractVersion) && //
					Objects.equals(this.m_id, that.m_id) && //
					Objects.equals(this.m_brokerAccountNumber, that.m_brokerAccountNumber) && //
					Objects.equals(this.m_brokerCode, that.m_brokerCode) && //
					Objects.equals(this.m_externalBrokerAccountNumber, that.m_externalBrokerAccountNumber) && //
					Objects.equals(this.m_instrumentCode, that.m_instrumentCode) && //
					Objects.equals(this.m_instrumentCodeExternal, that.m_instrumentCodeExternal) && //
					Objects.equals(this.m_assetClass, that.m_assetClass) && //
					Objects.equals(this.m_riskBucket, that.m_riskBucket) && //
					Objects.equals(this.m_underlyingInstrumentCode, that.m_underlyingInstrumentCode) && //
					Objects.equals(this.m_expiryDate, that.m_expiryDate) && //
					Objects.equals(this.m_expiryMonthCode, that.m_expiryMonthCode) && //
					Objects.equals(this.m_valueDate, that.m_valueDate) && //
					Objects.equals(this.m_positionType, that.m_positionType) && //
					Objects.equals(this.m_positionDate, that.m_positionDate) && //
					Objects.equals(this.m_direction, that.m_direction) && //
					Objects.equals(this.m_quantity, that.m_quantity) && //
					Objects.equals(this.m_price, that.m_price) && //
					Objects.equals(this.m_amount, that.m_amount) && //
					Objects.equals(this.m_currency, that.m_currency) && //
					Objects.equals(this.m_evaluationPrice, that.m_evaluationPrice) && //
					Objects.equals(this.m_evaluationAmount, that.m_evaluationAmount) && //
					Objects.equals(this.m_profitLoss, that.m_profitLoss) && //
					Objects.equals(this.m_commission, that.m_commission) && //
					Objects.equals(this.m_version, that.m_version) && //
					Objects.equals(this.m_isProductCurrencyInFractionalParts, that.m_isProductCurrencyInFractionalParts)
					&& //
					Objects.equals(this.m_productFractionalPartRatio, that.m_productFractionalPartRatio) && //
					Objects.equals(this.m_productCurrency, that.m_productCurrency) && //
					Objects.equals(this.m_openingReference, that.m_openingReference) && //
					Objects.equals(this.m_redemptionDate, that.m_redemptionDate) && //
					Objects.equals(this.m_currentCouponDate, that.m_currentCouponDate) && //
					Objects.equals(this.m_accruedInterestDays, that.m_accruedInterestDays) && //
					Objects.equals(this.m_accruedInterestAmount, that.m_accruedInterestAmount) && //
					Objects.equals(this.m_evaluationAccruedInterestDays, that.m_evaluationAccruedInterestDays) && //
					Objects.equals(this.m_evaluationAccruedInterestAmount, that.m_evaluationAccruedInterestAmount) && //
					Objects.equals(this.m_effectiveInterestRate, that.m_effectiveInterestRate) && //
					Objects.equals(this.m_effectiveInterestBaseAmount, that.m_effectiveInterestBaseAmount) && //
					Objects.equals(this.m_afsReserveAmount, that.m_afsReserveAmount) && //
					Objects.equals(this.m_redemptionPaymentDate, that.m_redemptionPaymentDate) && //
					Objects.equals(this.m_currentCouponPaymentDate, that.m_currentCouponPaymentDate) && //
					Objects.equals(this.m_wasManuallyCorrected, that.m_wasManuallyCorrected) && //
					Objects.equals(this.m_newAfsReserveAmount, that.m_newAfsReserveAmount) && //
					Objects.equals(this.m_newEffectiveInterestBaseAmount, that.m_newEffectiveInterestBaseAmount) && //
					Objects.equals(this.m_quantitySettlementDate, that.m_quantitySettlementDate) && //
                    Objects.equals(this.m_evaluationSpotPrice, that.m_evaluationSpotPrice) && //
                    Objects.equals(this.m_settlementDate, that.m_settlementDate) && //
                    Objects.equals(this.m_settlementTime, that.m_settlementTime) && //
                    Objects.equals(this.m_settlementStatus, that.m_settlementStatus) && //
                    Objects.equals(this.m_carryingCosts, that.m_carryingCosts);
		}
		return false;
	}

	@Override
	public String toString() {
		return "AbstractHedgePositionEntity [m_contractVersion=" + m_contractVersion + ", m_id=" + m_id
				+ ", m_brokerAccountNumber=" + m_brokerAccountNumber + ", m_brokerCode=" + m_brokerCode
				+ ", m_externalBrokerAccountNumber=" + m_externalBrokerAccountNumber + ", m_instrumentCode="
				+ m_instrumentCode + ", m_instrumentCodeExternal=" + m_instrumentCodeExternal + ", m_assetClass="
				+ m_assetClass + ", m_riskBucket=" + m_riskBucket + ", m_underlyingInstrumentCode="
				+ m_underlyingInstrumentCode + ", m_expiryDate=" + m_expiryDate + ", m_expiryMonthCode="
				+ m_expiryMonthCode + ", m_valueDate=" + m_valueDate + ", m_positionType=" + m_positionType
				+ ", m_positionDate=" + m_positionDate + ", m_direction=" + m_direction + ", m_quantity=" + m_quantity
				+ ", m_price=" + m_price + ", m_amount=" + m_amount + ", m_currency=" + m_currency
				+ ", m_evaluationPrice=" + m_evaluationPrice + ", m_evaluationAmount=" + m_evaluationAmount
				+ ", m_profitLoss=" + m_profitLoss + ", m_commission=" + m_commission + ", m_version=" + m_version
				+ ", m_isProductCurrencyInFractionalParts=" + m_isProductCurrencyInFractionalParts
				+ ", m_productFractionalPartRatio=" + m_productFractionalPartRatio + ", m_productCurrency="
				+ m_productCurrency + ", m_openingReference=" + m_openingReference + ", m_redemptionDate="
				+ m_redemptionDate + ", m_currentCouponDate=" + m_currentCouponDate + ", m_accruedInterestDays="
				+ m_accruedInterestDays + ", m_accruedInterestAmount=" + m_accruedInterestAmount
				+ ", m_evaluationAccruedInterestDays=" + m_evaluationAccruedInterestDays
				+ ", m_evaluationAccruedInterestAmount=" + m_evaluationAccruedInterestAmount
				+ ", m_effectiveInterestRate=" + m_effectiveInterestRate + ", m_effectiveInterestBaseAmount="
				+ m_effectiveInterestBaseAmount + ", m_afsReserveAmount=" + m_afsReserveAmount
				+ ", m_redemptionPaymentDate=" + m_redemptionPaymentDate + ", m_currentCouponPaymentDate="
				+ m_currentCouponPaymentDate + ", m_wasManuallyCorrected=" + m_wasManuallyCorrected
				+ ", m_newAfsReserveAmount=" + m_newAfsReserveAmount + ", m_newEffectiveInterestBaseAmount="
				+ m_newEffectiveInterestBaseAmount 
				+ ", m_quantitySettlementDate=" + m_quantitySettlementDate
                + ", m_evaluationSpotPrice=" + m_evaluationSpotPrice
                + ", m_settlementDate=" + m_settlementDate
                + ", m_settlementTime=" + m_settlementTime
                + ", m_settlementStatus=" + m_settlementStatus
                + ", m_carryingCosts=" + m_carryingCosts + "]";
	}
}
