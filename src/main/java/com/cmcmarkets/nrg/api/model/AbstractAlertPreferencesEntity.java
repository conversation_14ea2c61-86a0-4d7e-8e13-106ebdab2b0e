package com.cmcmarkets.nrg.api.model;

import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.cmcmarkets.alerts.api.model.AlertNotificationType;
import com.cmcmarkets.alerts.api.model.AlertType;
import com.cmcmarkets.content.api.model.ContentType;
import com.google.common.base.Objects;

public abstract class AbstractAlertPreferencesEntity extends AbstractEntity implements AlertPreferencesEntity {

	protected Integer m_version;    
	protected Long m_personId;   
	protected Long m_tradingAccountId;    
	protected String m_locale;    
	protected String m_timeZone;    
	protected Map<AlertType, Set<AlertNotificationType>> m_alertNotificationSettings;    
	protected Map<ContentType, Set<AlertNotificationType>> m_contentAlertNotificationSettings;    
	protected Long m_calendarAlertTime;   
	protected TimeUnit m_calendarAlertTimeUnit;
	protected Map<String, Set<AlertNotificationType>> m_consolidatedPreferences;
	
    public AbstractAlertPreferencesEntity(Date effectiveStartTimestamp)
    {
        super(effectiveStartTimestamp);
    }
	
    public Integer getVersion()
	{
	    return m_version;
	}
    
    public Long getPersonId()
	{
	    return m_personId;
	}
    
    public Long getTradingAccountId()
    {
	    return m_tradingAccountId;
	}
    
    public String getLocale()
	{
	    return m_locale;
	}
    
    public String getTimeZone()
	{
	    return m_timeZone;
	}
    
    public Map<AlertType, Set<AlertNotificationType>> getAlertNotificationSettings()
	{
	    return m_alertNotificationSettings;
	}
    
    public Map<ContentType, Set<AlertNotificationType>> getContentAlertNotificationSettings()
	{
	    return m_contentAlertNotificationSettings;
	}
    
    public Long getCalendarAlertTime()
	{
	    return m_calendarAlertTime;
	}
    
    public TimeUnit getCalendarAlertTimeUnit()
	{
	    return m_calendarAlertTimeUnit;
	}
    
    public Map<String, Set<AlertNotificationType>> getConsolidatedPreferences()
	{
	    return m_consolidatedPreferences;
	}
    
    @Override
    public int hashCode()
    {
        return Objects
            .hashCode(super.hashCode(),m_version,m_personId,m_tradingAccountId,m_locale,m_timeZone,m_alertNotificationSettings,
            		m_contentAlertNotificationSettings,m_calendarAlertTime,m_calendarAlertTimeUnit,m_consolidatedPreferences);
    }
    
    @Override
    public boolean equals(Object object)
    {
        if(object instanceof AbstractAlertPreferencesEntity)
        {
            if(!super.equals(object))
                return false;
            AbstractAlertPreferencesEntity that = (AbstractAlertPreferencesEntity) object;
            return Objects.equal(this.m_version, that.m_version) &&
            Objects.equal(this.m_personId, that.m_personId) &&
            Objects.equal(this.m_tradingAccountId, that.m_tradingAccountId) &&
            Objects.equal(this.m_locale, that.m_locale) &&
            Objects.equal(this.m_timeZone, that.m_timeZone) &&
            Objects.equal(this.m_alertNotificationSettings, that.    m_alertNotificationSettings) &&
            Objects.equal(this.m_contentAlertNotificationSettings, that.m_contentAlertNotificationSettings) &&
            Objects.equal(this.m_calendarAlertTime, that.m_calendarAlertTime) &&
            Objects.equal(this.m_calendarAlertTimeUnit, that.m_calendarAlertTimeUnit) &&
            Objects.equal(this.m_consolidatedPreferences, that.m_consolidatedPreferences);
        }
        return false;
    }

    @Override
    public String toString()
    {
        return Objects.toStringHelper(this)
            .add("super", super.toString())         
            .add("m_version", m_version)
            .add("m_personId", m_personId)
            .add("m_tradingAccountId", m_tradingAccountId)
            .add("m_locale", m_locale)
            .add("m_timeZone", m_timeZone)
            .add("m_alertNotificationSettings", m_alertNotificationSettings)
            .add("m_contentAlertNotificationSettings", m_contentAlertNotificationSettings)
            .add("m_calendarAlertTime", m_calendarAlertTime)
            .add("m_calendarAlertTimeUnit", m_calendarAlertTimeUnit)
            .add("m_consolidatedPreferences", m_consolidatedPreferences)
            .toString();
    }
}