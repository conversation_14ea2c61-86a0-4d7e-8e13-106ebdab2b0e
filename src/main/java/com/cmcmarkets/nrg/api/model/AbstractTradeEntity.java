/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import com.cmcmarkets.framework.messaging.common.model.DateTime2;
import com.cmcmarkets.trading.api3.model.BinaryType;
import com.cmcmarkets.trading.api3.model.ExecutionType;
import com.cmcmarkets.trading.api3.model.PriceBandOffsetType;
import com.cmcmarkets.trading.api3.model.TradeTax;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public abstract class AbstractTradeEntity extends AbstractEntity implements TradeEntity
{
    protected Long m_bookingNumber;
    protected String m_channelId;
    protected String m_controlledOrderType;
    protected Long m_cp1CashAccountNumber;
    protected Long m_cp2CashAccountNumber;
    protected String m_cp2CustomInfo;
    protected String m_cp2TradingAccountCodifier;
    protected Long m_cp2TradingAccountId;
    protected Long m_creationIdentityToken;
    protected Date m_creationTime;
    protected Long m_creationOnBehalfOfIdentityToken;
    protected String m_direction;
    protected Integer m_directionMultiplier;
    protected Date m_eventTime;
    protected String m_feedSymbol;
    protected BigDecimal m_financingRatio;
    protected Boolean m_isDeleted;
    protected Boolean m_isLateDeal;
    protected Boolean m_isPrimary;
    protected Boolean m_isTradeOfControlledOrder;
    protected BigDecimal m_marginFxRateAsk;
    protected BigDecimal m_marginFxRateBid;
    protected String m_marginFxRateId;
    protected BigDecimal m_marginRequirement;
    protected String m_marginType;
    protected Long m_mmAccountId;
    protected Date m_mmValueDate;
    protected String m_tradeQuantityCurrency;
    protected BigDecimal m_tradeQuantity;
    protected String m_orderId;
    protected String m_platform;
    protected BigDecimal m_productFinancingRatioMaximum;
    protected String m_priceDesignator;
    protected String m_productCurrency;
    protected BigDecimal m_productFractionalPartRatio;
    protected Integer m_productGeneration;
    protected String m_productInstrumentCode;
    protected Long m_mmInstrumentId;
    protected BigDecimal m_productPointMultiplier;
    protected String m_productSchemaCode;
    protected String m_productWrapperCode;
    protected BigDecimal m_profitLossFxRateAsk;
    protected BigDecimal m_profitLossFxRateBid;
    protected Long m_profitLossFxRateId;
    protected String m_prophetSymbol;
    protected Date m_publishTime;
    protected String m_quantityDesignator;
    protected BigDecimal m_quantityFxRateAsk;
    protected BigDecimal m_quantityFxRateBid;
    protected String m_quantityFxRateId;
    protected Long m_quoteId;
    protected BigDecimal m_quoteL1Ask;
    protected BigDecimal m_quoteL1Bid;
    protected BigDecimal m_quotedL1AskPrice;
    protected BigDecimal m_quotedL1BidPrice;
    protected String m_reducedTradeId;
    protected String m_requestId;
    protected String m_reversedOrderId;
    protected String m_reversedTradeId;
    protected String m_sessionKey;
    protected String m_tradingAccountCustomInfoVirtualPortfolioCodifier;
    protected BigDecimal m_tradeAmount;
    protected String m_tradeAmountCurrency;
    protected String m_tradeId;
    protected BigDecimal m_tradeMarginAmount;
    protected String m_tradeMarginCurrency;
    protected BigDecimal m_tradePrice;
    protected BigDecimal m_tradeProfitLoss;
    protected String m_tradeProfitLossCurrency;
    protected Date m_tradeTime;
    protected String m_tradingAccountCodifier;
    protected String m_tradingAccountCurrency;
    protected String m_tradingAccountFunction;
    protected Long m_tradingAccountId;
    protected BigDecimal m_tradeAmountInTradingAccountCurrency;
    protected BigDecimal m_tradeMarginInTradingAccountCurrency;
    protected BigDecimal m_tradeProfitAndLossInTradingAccountCurrency;
    protected String m_tradingAccountCustomInfo;
    protected Long m_updateIdentityToken;
    protected Date m_updateTime;
    protected Long m_updateOnBehalfOfIdentityToken;
    protected Integer m_versionNumber;
    protected String m_visitId;
    protected String m_relatedChildOrderType;
    protected Boolean m_isMandatory;
    protected String m_requestedTradeCloseOrderId;
    protected String m_limitPriceCondition;
    protected String m_relatedParentOrderId;
    protected BigDecimal m_limitPrice;
    protected BigDecimal m_limitTrailingDistance;
    protected BigDecimal m_limitTrailingBestPrice;
    protected String m_orderType;
    protected Boolean m_isProductCurrencyInFractionalParts;
    protected BigDecimal m_quoteAskPrice;
    protected BigDecimal m_quoteBidPrice;
    protected Boolean m_isLimitTrailing;
    protected String m_tradingAccountType;
    protected String m_cp2TradingAccountType;
    protected String m_recordSource;
    protected Date m_quoteReceivedTime;
    protected String m_priceSource;
    protected BigDecimal m_appToUnits;
    protected String m_rolloverClosingTradeId;
    protected Integer m_priceLevel;
    protected Long m_amountFxRateId;
    protected BigDecimal m_amountFxRate;
    protected BigDecimal m_aggregatedPriceQuantity;
    protected List<String> m_requestedOpenTradesToClose;
    protected Boolean m_isHedged;
    protected Date m_orderTime;
    protected Boolean m_IsRolloverClosing;
    protected Integer m_priceOffsetIndex;
    protected Long m_executionStartedTime;
    protected BigDecimal m_tradeInstrumentPrice;
    protected BigDecimal m_strikePrice;
    protected ExecutionType m_executionType;
    protected BigDecimal m_quoteDepthPrice;
    protected BinaryType m_binaryType;
    protected Long m_settleTime;
    protected String m_tenor;
    protected BigDecimal m_strikePriceAdditional;
    protected String m_comment;
    protected BigDecimal m_tradeInstrumentAmount;
    protected BigDecimal m_referenceTradePrice;
    protected BigDecimal m_ladderQuantitiesOverride;
    protected BigDecimal m_cmcTradeInstrumentPrice;
    protected DateTime2 m_tradeTime2;
    protected DateTime2 m_tradeTimeConfirmation;    
    //protected BigDecimal m_tradePriceOffset;
    protected Boolean m_quoteIsFirstGoodInContinuousTrading;
    protected String m_priceStreamCode;
    protected BigDecimal m_capitalGainsInTaxCurrency;
    protected Boolean m_isPairCurrencyInFractionalParts;
    protected BigDecimal m_bidPriceOffset;
    protected BigDecimal m_askPriceOffset;
    protected PriceBandOffsetType m_priceOffsetType;
    protected Date m_valueDate;
    protected BigDecimal m_spotPrice;
    protected String m_pairCurrency;
    protected String m_primaryCurrency;
    protected String m_secondaryCurrency;
    protected BigDecimal m_primaryAmount;
    protected BigDecimal m_secondaryAmount;
    protected String m_fxTenor;
    protected Date m_fxTradeDate;
    protected Long m_swapQuoteId;
    protected BigDecimal m_fullSpreadTradePrice;
    protected BigDecimal m_spreadProportion;
    protected String m_modifiedOpenTradeId;
    protected List<TradeTax> m_tradeTax;
    protected BigDecimal m_capitalGainsInAccountCurrency;
    protected BigDecimal m_amountFxRateBanding;
    protected Boolean m_skipMifidReporting;
    protected Boolean m_skipContractNotes;
    protected Long m_portfolioSwapPeriodSequenceNumber;
    protected String m_venue;
    protected String m_executionBroker;
    protected String m_tradeDirection;
    protected Date m_accountingTradeTime;
    protected BigDecimal m_tradeProfitLossInPrimaryCurrency;
    protected String m_optionCode;
    protected String m_execId;
    protected Boolean m_isClosesLastPosition;
    protected BigDecimal m_accruedHoldingCosts;
    protected String m_metatraderTradeId;

    public AbstractTradeEntity(Date effectiveStartTimestamp)
    {
        super(effectiveStartTimestamp);
    }

    @Override
    public Long getBookingNumber()
    {
        return m_bookingNumber;
    }

    @Override
    public String getChannelId()
    {
        return m_channelId;
    }

    @Override
    public String getControlledOrderType()
    {
        return m_controlledOrderType;
    }

    @Override
    public Long getCp1CashAccountNumber()
    {
        return m_cp1CashAccountNumber;
    }

    @Override
    public Long getCp2CashAccountNumber()
    {
        return m_cp2CashAccountNumber;
    }

    @Override
    public String getCp2CustomInfo()
    {
        return m_cp2CustomInfo;
    }

    @Override
    public String getCp2TradingAccountCodifier()
    {
        return m_cp2TradingAccountCodifier;
    }

    @Override
    public Long getCp2TradingAccountId()
    {
        return m_cp2TradingAccountId;
    }

    @Override
    public Long getCreationIdentityToken()
    {
        return m_creationIdentityToken;
    }

    @Override
    public Date getCreationTime()
    {
        return m_creationTime;
    }

    @Override
    public Long getCreationOnBehalfOfIdentityToken()
    {
        return m_creationOnBehalfOfIdentityToken;
    }

    @Override
    public String getDirection()
    {
        return m_direction;
    }

    @Override
    public Integer getDirectionMultiplier()
    {
        return m_directionMultiplier;
    }

    @Override
    public Date getEventTime()
    {
        return m_eventTime;
    }

    @Override
    public String getFeedSymbol()
    {
        return m_feedSymbol;
    }

    @Override
    public BigDecimal getFinancingRatio()
    {
        return m_financingRatio;
    }

    @Override
    public Boolean isDeleted()
    {
        return m_isDeleted;
    }

    @Override
    public Boolean isLateDeal()
    {
        return m_isLateDeal;
    }

    @Override
    public Boolean isPrimary()
    {
        return m_isPrimary;
    }

    @Override
    public Boolean isTradeOfControlledOrder()
    {
        return m_isTradeOfControlledOrder;
    }

    @Override
    public BigDecimal getMarginFxRateAsk()
    {
        return m_marginFxRateAsk;
    }

    @Override
    public BigDecimal getMarginFxRateBid()
    {
        return m_marginFxRateBid;
    }

    @Override
    public String getMarginFxRateId()
    {
        return m_marginFxRateId;
    }

    @Override
    public BigDecimal getMarginRequirement()
    {
        return m_marginRequirement;
    }

    @Override
    public String getMarginType()
    {
        return m_marginType;
    }

    @Override
    public Long getMmAccountId()
    {
        return m_mmAccountId;
    }

    @Override
    public Date getMmValueDate()
    {
        return m_mmValueDate;
    }

    @Override
    public String getTradeQuantityCurrency()
    {
        return m_tradeQuantityCurrency;
    }

    @Override
    public BigDecimal getTradeQuantity()
    {
        return m_tradeQuantity;
    }

    @Override
    public String getOrderId()
    {
        return m_orderId;
    }

    @Override
    public String getPlatform()
    {
        return m_platform;
    }

    @Override
    public BigDecimal getProductFinancingRatioMaximum()
    {
        return m_productFinancingRatioMaximum;
    }

    @Override
    public String getPriceDesignator()
    {
        return m_priceDesignator;
    }

    @Override
    public String getProductCurrency()
    {
        return m_productCurrency;
    }

    @Override
    public BigDecimal getProductFractionalPartRatio()
    {
        return m_productFractionalPartRatio;
    }

    @Override
    public Integer getProductGeneration()
    {
        return m_productGeneration;
    }

    @Override
    public String getProductInstrumentCode()
    {
        return m_productInstrumentCode;
    }

    @Override
    public Long getMmInstrumentId()
    {
        return m_mmInstrumentId;
    }

    @Override
    public BigDecimal getProductPointMultiplier()
    {
        return m_productPointMultiplier;
    }

    @Override
    public String getProductSchemaCode()
    {
        return m_productSchemaCode;
    }

    @Override
    public String getProductWrapperCode()
    {
        return m_productWrapperCode;
    }

    @Override
    public BigDecimal getProfitLossFxRateAsk()
    {
        return m_profitLossFxRateAsk;
    }

    @Override
    public BigDecimal getProfitLossFxRateBid()
    {
        return m_profitLossFxRateBid;
    }

    @Override
    public Long getProfitLossFxRateId()
    {
        return m_profitLossFxRateId;
    }

    @Override
    public String getProphetSymbol()
    {
        return m_prophetSymbol;
    }

    @Override
    public Date getPublishTime()
    {
        return m_publishTime;
    }

    @Override
    public String getQuantityDesignator()
    {
        return m_quantityDesignator;
    }

    @Override
    public BigDecimal getQuantityFxRateAsk()
    {
        return m_quantityFxRateAsk;
    }

    @Override
    public BigDecimal getQuantityFxRateBid()
    {
        return m_quantityFxRateBid;
    }

    @Override
    public String getQuantityFxRateId()
    {
        return m_quantityFxRateId;
    }

    @Override
    public Long getQuoteId()
    {
        return m_quoteId;
    }

    @Override
    public BigDecimal getQuoteL1Ask()
    {
        return m_quoteL1Ask;
    }

    @Override
    public BigDecimal getQuoteL1Bid()
    {
        return m_quoteL1Bid;
    }

    @Override
    public BigDecimal getQuotedL1AskPrice()
    {
        return m_quotedL1AskPrice;
    }

    @Override
    public BigDecimal getQuotedL1BidPrice()
    {
        return m_quotedL1BidPrice;
    }

    @Override
    public String getReducedTradeId()
    {
        return m_reducedTradeId;
    }

    @Override
    public String getRequestId()
    {
        return m_requestId;
    }

    @Override
    public String getReversedOrderId()
    {
        return m_reversedOrderId;
    }

    @Override
    public String getReversedTradeId()
    {
        return m_reversedTradeId;
    }

    @Override
    public String getSessionKey()
    {
        return m_sessionKey;
    }

    @Override
    public String getTradingAccountCustomInfoVirtualPortfolioCodifier()
    {
        return m_tradingAccountCustomInfoVirtualPortfolioCodifier;
    }

    @Override
    public BigDecimal getTradeAmount()
    {
        return m_tradeAmount;
    }

    @Override
    public String getTradeAmountCurrency()
    {
        return m_tradeAmountCurrency;
    }

    @Override
    public String getTradeId()
    {
        return m_tradeId;
    }

    @Override
    public BigDecimal getTradeMarginAmount()
    {
        return m_tradeMarginAmount;
    }

    @Override
    public String getTradeMarginCurrency()
    {
        return m_tradeMarginCurrency;
    }

    @Override
    public BigDecimal getTradePrice()
    {
        return m_tradePrice;
    }

    @Override
    public BigDecimal getTradeProfitLoss()
    {
        return m_tradeProfitLoss;
    }

    @Override
    public String getTradeProfitLossCurrency()
    {
        return m_tradeProfitLossCurrency;
    }

    @Override
    public Date getTradeTime()
    {
        return m_tradeTime;
    }

    @Override
    public String getTradingAccountCodifier()
    {
        return m_tradingAccountCodifier;
    }

    @Override
    public String getTradingAccountCurrency()
    {
        return m_tradingAccountCurrency;
    }

    @Override
    public String getTradingAccountFunction()
    {
        return m_tradingAccountFunction;
    }

    @Override
    public Long getTradingAccountId()
    {
        return m_tradingAccountId;
    }

    @Override
    public BigDecimal getTradeAmountInTradingAccountCurrency()
    {
        return m_tradeAmountInTradingAccountCurrency;
    }

    @Override
    public BigDecimal getTradeMarginInTradingAccountCurrency()
    {
        return m_tradeMarginInTradingAccountCurrency;
    }

    @Override
    public BigDecimal getTradeProfitAndLossInTradingAccountCurrency()
    {
        return m_tradeProfitAndLossInTradingAccountCurrency;
    }

    @Override
    public String getTradingAccountCustomInfo()
    {
        return m_tradingAccountCustomInfo;
    }

    @Override
    public Long getUpdateIdentityToken()
    {
        return m_updateIdentityToken;
    }

    @Override
    public Date getUpdateTime()
    {
        return m_updateTime;
    }

    @Override
    public Long getUpdateOnBehalfOfIdentityToken()
    {
        return m_updateOnBehalfOfIdentityToken;
    }

    @Override
    public Integer getVersionNumber()
    {
        return m_versionNumber;
    }

    @Override
    public String getVisitId()
    {
        return m_visitId;
    }

    @Override
    public String getRelatedChildOrderType()
    {
        return m_relatedChildOrderType;
    }

    @Override
    public Boolean isMandatory()
    {
        return m_isMandatory;
    }

    @Override
    public String getRequestedTradeCloseOrderId()
    {
        return m_requestedTradeCloseOrderId;
    }

    @Override
    public String getLimitPriceCondition()
    {
        return m_limitPriceCondition;
    }

    @Override
    public String getRelatedParentOrderId()
    {
        return m_relatedParentOrderId;
    }

    @Override
    public BigDecimal getLimitPrice()
    {
        return m_limitPrice;
    }

    @Override
    public BigDecimal getLimitTrailingDistance()
    {
        return m_limitTrailingDistance;
    }

    @Override
    public BigDecimal getLimitTrailingBestPrice()
    {
        return m_limitTrailingBestPrice;
    }

    @Override
    public String getOrderType()
    {
        return m_orderType;
    }

    @Override
    public Boolean isProductCurrencyInFractionalParts()
    {
        return m_isProductCurrencyInFractionalParts;
    }

    @Override
    public BigDecimal getQuoteAskPrice()
    {
        return m_quoteAskPrice;
    }

    @Override
    public BigDecimal getQuoteBidPrice()
    {
        return m_quoteBidPrice;
    }

    @Override
    public Boolean isLimitTrailing()
    {
        return m_isLimitTrailing;
    }

    @Override
    public String getTradingAccountType()
    {
        return m_tradingAccountType;
    }

    @Override
    public String getCp2TradingAccountType()
    {
        return m_cp2TradingAccountType;
    }

    @Override
    public String getRecordSource()
    {
        return m_recordSource;
    }

    @Override
    public String getPriceSource()
    {
        return m_priceSource;
    }

    @Override
    public Date getQuoteReceivedTime()
    {
        return m_quoteReceivedTime;
    }

    @Override
    public BigDecimal getAppToUnits()
    {
        return m_appToUnits;
    }

    @Override
    public String getRolloverClosingTradeId()
    {
        return m_rolloverClosingTradeId;
    }

    @Override
    public Integer getPriceLevel()
    {
        return m_priceLevel;
    }

    @Override
    public Long getAmountFxRateId()
    {
        return m_amountFxRateId;
    }

    @Override
    public BigDecimal getAmountFxRate()
    {
        return m_amountFxRate;
    }

    @Override
    public BigDecimal getAggregatedPriceQuantity()
    {
        return m_aggregatedPriceQuantity;
    }

    @Override
    public List<String> getRequestedOpenTradesToClose()
    {
        return m_requestedOpenTradesToClose;
    }

    @Override
    public Boolean getIsHedged()
    {
        return m_isHedged;
    }

    @Override
    public Date getOrderTime()
    {
        return m_orderTime;
    }

    @Override
    public Boolean isRolloverClosing()
    {
        return m_IsRolloverClosing;
    }

    @Override
    public Integer getPriceOffsetIndex()
    {
        return m_priceOffsetIndex;
    }

    public void setControlledOrderType(String controlledOrderType)
    {
        m_controlledOrderType = controlledOrderType;
    }

    @Override
    public Long getExecutionStartedTime()
    {
        return m_executionStartedTime;
    }

    @Override
    public BigDecimal getTradeInstrumentPrice()
    {
        return m_tradeInstrumentPrice;
    }

    @Override
    public BigDecimal getStrikePrice()
    {
        return m_strikePrice;
    }

    @Override
    public ExecutionType getExecutionType()
    {
        return m_executionType;
    }

    @Override
    public BigDecimal getQuoteDepthPrice()
    {
        return m_quoteDepthPrice;
    }

    @Override
    public BinaryType getBinaryType()
    {
        return m_binaryType;
    }

    @Override
    public Long getSettleTime()
    {
        return m_settleTime;
    }

    @Override
    public String getTenor()
    {
        return m_tenor;
    }

    @Override
    public BigDecimal getStrikePriceAdditional()
    {
        return m_strikePriceAdditional;
    }

    @Override
    public String getComment()
    {
        return m_comment;
    }

    @Override
    public BigDecimal getTradeInstrumentAmount()
    {
        return m_tradeInstrumentAmount;
    }

    @Override
    public BigDecimal getReferenceTradePrice()
    {
        return m_referenceTradePrice;
    }

    @Override
    public BigDecimal getLadderQuantitiesOverride()
    {
        return m_ladderQuantitiesOverride;
    }

    @Override
    public BigDecimal getCmcTradeInstrumentPrice()
    {
        return m_cmcTradeInstrumentPrice;
    }

    @Override
    public DateTime2 getTradeTime2()
    {
        return m_tradeTime2;
    }
    
    @Override
    public DateTime2 getTradeTimeConfirmation()
    {
        return m_tradeTimeConfirmation;
    }

 /*   @Override
    public BigDecimal getTradePriceOffset()
    {
        return m_tradePriceOffset;
    }  */
    
    @Override
    public Boolean isQuoteIsFirstGoodInContinuousTrading()
    {
        return m_quoteIsFirstGoodInContinuousTrading;
    }      
    
    @Override
    public String getPriceStreamCode()
    {
        return m_priceStreamCode;
    }      
    
    @Override
    public BigDecimal getCapitalGainsInTaxCurrency()
    {
        return m_capitalGainsInTaxCurrency;
    }      
    
    @Override
    public Boolean isCurrencyInFractionalParts()
    {
        return m_isProductCurrencyInFractionalParts;
    }
    
    @Override
    public Boolean isPairCurrencyInFractionalParts()
    {
        return m_isPairCurrencyInFractionalParts;
    }   
    
    @Override
    public BigDecimal getBidPriceOffset()
    {
        return m_bidPriceOffset;
    }    
    
    @Override
    public BigDecimal getAskPriceOffset()
    {
        return m_askPriceOffset;
    }      
    
    @Override
    public PriceBandOffsetType getPriceOffsetType()
    {
        return m_priceOffsetType;
    }    
    
    @Override
    public Date getValueDate()
    {
        return m_valueDate;
    }  

    @Override
    public BigDecimal getSpotPrice()
    {
        return m_spotPrice;
    }  

    @Override
    public String getPairCurrency()    
    {
        return m_pairCurrency;
    }  

    @Override
    public String getPrimaryCurrency()
    {
        return m_primaryCurrency;
    }  

    @Override
    public String getSecondaryCurrency()
    {
        return m_secondaryCurrency;
    }  
    
    @Override
    public BigDecimal getPrimaryAmount()
    {
        return m_primaryAmount;
    }  

    @Override
    public BigDecimal getSecondaryAmount()
    {
        return m_secondaryAmount;
    }  

    @Override
    public String getFxTenor()
    {
        return m_fxTenor;
    }  

    @Override
    public Date getFxTradeDate()
    {
        return m_fxTradeDate;
    }  

    @Override
    public Long getSwapQuoteId()
    {
        return m_swapQuoteId;
    }  
    
    @Override
    public BigDecimal getFullSpreadTradePrice()
    {
        return m_fullSpreadTradePrice;
    }  
    
    @Override
    public BigDecimal getSpreadProportion()
    {
        return m_spreadProportion;
    }  
    
    @Override
    public String getModifiedOpenTradeId()
    {
        return m_modifiedOpenTradeId;
    } 
    
    @Override
    public List<TradeTax> getTradeTax()
    {
        return m_tradeTax;
    }
    
    @Override
    public BigDecimal getCapitalGainsInAccountCurrency()
    {
        return m_capitalGainsInAccountCurrency;
    }  
       
    @Override
    public BigDecimal getAmountFxRateBanding()
    {
        return m_amountFxRateBanding;
    }  
       
    @Override
    public Boolean isSkipMifidReporting()
    {
        return m_skipMifidReporting;
    }
        
    @Override
    public Boolean isSkipContractNotes()
    {
        return m_skipContractNotes;
    }
    
    @Override
    public Long getPortfolioSwapPeriodSequenceNumber() {
        return m_portfolioSwapPeriodSequenceNumber;
    }
    
    @Override
    public String getVenue()
    {
        return m_venue;
    } 
    
    @Override
    public String getExecutionBroker()
    {
        return m_executionBroker;
    } 
    
    @Override
    public String getTradeDirection()
    {
        return m_tradeDirection;
    }

    @Override
    public Date getAccountingTradeTime() { return m_accountingTradeTime; }

    @Override
    public BigDecimal getTradeProfitLossInPrimaryCurrency()
    {
        return m_tradeProfitLossInPrimaryCurrency;
    }

    @Override
    public String getOptionCode()
    {
        return m_optionCode;
    }

    @Override
    public String getExecId()
    {
        return m_execId;
    }

    @Override
    public Boolean isClosesLastPosition()
    {
        return m_isClosesLastPosition;
    }

    @Override
    public BigDecimal getAccruedHoldingCosts()
    {
        return m_accruedHoldingCosts;
    }

    @Override
    public String getMetatraderTradeId()
    {
        return m_metatraderTradeId;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((m_IsRolloverClosing == null) ? 0 : m_IsRolloverClosing.hashCode());
        result = prime * result + ((m_aggregatedPriceQuantity == null) ? 0 : m_aggregatedPriceQuantity.hashCode());
        result = prime * result + ((m_amountFxRate == null) ? 0 : m_amountFxRate.hashCode());
        result = prime * result + ((m_amountFxRateId == null) ? 0 : m_amountFxRateId.hashCode());
        result = prime * result + ((m_appToUnits == null) ? 0 : m_appToUnits.hashCode());
        result = prime * result + ((m_binaryType == null) ? 0 : m_binaryType.hashCode());
        result = prime * result + ((m_bookingNumber == null) ? 0 : m_bookingNumber.hashCode());
        result = prime * result + ((m_channelId == null) ? 0 : m_channelId.hashCode());
        result = prime * result + ((m_controlledOrderType == null) ? 0 : m_controlledOrderType.hashCode());
        result = prime * result + ((m_cp1CashAccountNumber == null) ? 0 : m_cp1CashAccountNumber.hashCode());
        result = prime * result + ((m_cp2CashAccountNumber == null) ? 0 : m_cp2CashAccountNumber.hashCode());
        result = prime * result + ((m_cp2CustomInfo == null) ? 0 : m_cp2CustomInfo.hashCode());
        result = prime * result + ((m_cp2TradingAccountCodifier == null) ? 0 : m_cp2TradingAccountCodifier.hashCode());
        result = prime * result + ((m_cp2TradingAccountId == null) ? 0 : m_cp2TradingAccountId.hashCode());
        result = prime * result + ((m_cp2TradingAccountType == null) ? 0 : m_cp2TradingAccountType.hashCode());
        result = prime * result + ((m_creationIdentityToken == null) ? 0 : m_creationIdentityToken.hashCode());
        result = prime * result + ((m_creationOnBehalfOfIdentityToken == null) ? 0 : m_creationOnBehalfOfIdentityToken.hashCode());
        result = prime * result + ((m_creationTime == null) ? 0 : m_creationTime.hashCode());
        result = prime * result + ((m_direction == null) ? 0 : m_direction.hashCode());
        result = prime * result + ((m_directionMultiplier == null) ? 0 : m_directionMultiplier.hashCode());
        result = prime * result + ((m_eventTime == null) ? 0 : m_eventTime.hashCode());
        result = prime * result + ((m_executionStartedTime == null) ? 0 : m_executionStartedTime.hashCode());
        result = prime * result + ((m_executionType == null) ? 0 : m_executionType.hashCode());
        result = prime * result + ((m_feedSymbol == null) ? 0 : m_feedSymbol.hashCode());
        result = prime * result + ((m_financingRatio == null) ? 0 : m_financingRatio.hashCode());
        result = prime * result + ((m_isDeleted == null) ? 0 : m_isDeleted.hashCode());
        result = prime * result + ((m_isHedged == null) ? 0 : m_isHedged.hashCode());
        result = prime * result + ((m_isLateDeal == null) ? 0 : m_isLateDeal.hashCode());
        result = prime * result + ((m_isLimitTrailing == null) ? 0 : m_isLimitTrailing.hashCode());
        result = prime * result + ((m_isMandatory == null) ? 0 : m_isMandatory.hashCode());
        result = prime * result + ((m_isPrimary == null) ? 0 : m_isPrimary.hashCode());
        result = prime * result + ((m_isProductCurrencyInFractionalParts == null) ? 0 : m_isProductCurrencyInFractionalParts.hashCode());
        result = prime * result + ((m_isTradeOfControlledOrder == null) ? 0 : m_isTradeOfControlledOrder.hashCode());
        result = prime * result + ((m_limitPrice == null) ? 0 : m_limitPrice.hashCode());
        result = prime * result + ((m_limitPriceCondition == null) ? 0 : m_limitPriceCondition.hashCode());
        result = prime * result + ((m_limitTrailingBestPrice == null) ? 0 : m_limitTrailingBestPrice.hashCode());
        result = prime * result + ((m_limitTrailingDistance == null) ? 0 : m_limitTrailingDistance.hashCode());
        result = prime * result + ((m_marginFxRateAsk == null) ? 0 : m_marginFxRateAsk.hashCode());
        result = prime * result + ((m_marginFxRateBid == null) ? 0 : m_marginFxRateBid.hashCode());
        result = prime * result + ((m_marginFxRateId == null) ? 0 : m_marginFxRateId.hashCode());
        result = prime * result + ((m_marginRequirement == null) ? 0 : m_marginRequirement.hashCode());
        result = prime * result + ((m_marginType == null) ? 0 : m_marginType.hashCode());
        result = prime * result + ((m_mmAccountId == null) ? 0 : m_mmAccountId.hashCode());
        result = prime * result + ((m_mmInstrumentId == null) ? 0 : m_mmInstrumentId.hashCode());
        result = prime * result + ((m_mmValueDate == null) ? 0 : m_mmValueDate.hashCode());
        result = prime * result + ((m_orderId == null) ? 0 : m_orderId.hashCode());
        result = prime * result + ((m_orderTime == null) ? 0 : m_orderTime.hashCode());
        result = prime * result + ((m_orderType == null) ? 0 : m_orderType.hashCode());
        result = prime * result + ((m_platform == null) ? 0 : m_platform.hashCode());
        result = prime * result + ((m_priceDesignator == null) ? 0 : m_priceDesignator.hashCode());
        result = prime * result + ((m_priceLevel == null) ? 0 : m_priceLevel.hashCode());
        result = prime * result + ((m_priceOffsetIndex == null) ? 0 : m_priceOffsetIndex.hashCode());
        result = prime * result + ((m_priceSource == null) ? 0 : m_priceSource.hashCode());
        result = prime * result + ((m_productCurrency == null) ? 0 : m_productCurrency.hashCode());
        result =
            prime * result + ((m_productFinancingRatioMaximum == null) ? 0 : m_productFinancingRatioMaximum.hashCode());
        result =
            prime * result + ((m_productFractionalPartRatio == null) ? 0 : m_productFractionalPartRatio.hashCode());
        result = prime * result + ((m_productGeneration == null) ? 0 : m_productGeneration.hashCode());
        result = prime * result + ((m_productInstrumentCode == null) ? 0 : m_productInstrumentCode.hashCode());
        result = prime * result + ((m_productPointMultiplier == null) ? 0 : m_productPointMultiplier.hashCode());
        result = prime * result + ((m_productSchemaCode == null) ? 0 : m_productSchemaCode.hashCode());
        result = prime * result + ((m_productWrapperCode == null) ? 0 : m_productWrapperCode.hashCode());
        result = prime * result + ((m_profitLossFxRateAsk == null) ? 0 : m_profitLossFxRateAsk.hashCode());
        result = prime * result + ((m_profitLossFxRateBid == null) ? 0 : m_profitLossFxRateBid.hashCode());
        result = prime * result + ((m_profitLossFxRateId == null) ? 0 : m_profitLossFxRateId.hashCode());
        result = prime * result + ((m_prophetSymbol == null) ? 0 : m_prophetSymbol.hashCode());
        result = prime * result + ((m_publishTime == null) ? 0 : m_publishTime.hashCode());
        result = prime * result + ((m_quantityDesignator == null) ? 0 : m_quantityDesignator.hashCode());
        result = prime * result + ((m_quantityFxRateAsk == null) ? 0 : m_quantityFxRateAsk.hashCode());
        result = prime * result + ((m_quantityFxRateBid == null) ? 0 : m_quantityFxRateBid.hashCode());
        result = prime * result + ((m_quantityFxRateId == null) ? 0 : m_quantityFxRateId.hashCode());
        result = prime * result + ((m_quoteAskPrice == null) ? 0 : m_quoteAskPrice.hashCode());
        result = prime * result + ((m_quoteBidPrice == null) ? 0 : m_quoteBidPrice.hashCode());
        result = prime * result + ((m_quoteDepthPrice == null) ? 0 : m_quoteDepthPrice.hashCode());
        result = prime * result + ((m_quoteId == null) ? 0 : m_quoteId.hashCode());
        result = prime * result + ((m_quoteL1Ask == null) ? 0 : m_quoteL1Ask.hashCode());
        result = prime * result + ((m_quoteL1Bid == null) ? 0 : m_quoteL1Bid.hashCode());
        result = prime * result + ((m_quoteReceivedTime == null) ? 0 : m_quoteReceivedTime.hashCode());
        result = prime * result + ((m_quotedL1AskPrice == null) ? 0 : m_quotedL1AskPrice.hashCode());
        result = prime * result + ((m_quotedL1BidPrice == null) ? 0 : m_quotedL1BidPrice.hashCode());
        result = prime * result + ((m_recordSource == null) ? 0 : m_recordSource.hashCode());
        result = prime * result + ((m_reducedTradeId == null) ? 0 : m_reducedTradeId.hashCode());
        result = prime * result + ((m_relatedChildOrderType == null) ? 0 : m_relatedChildOrderType.hashCode());
        result = prime * result + ((m_relatedParentOrderId == null) ? 0 : m_relatedParentOrderId.hashCode());
        result = prime * result + ((m_requestId == null) ? 0 : m_requestId.hashCode());
        result =
            prime * result + ((m_requestedOpenTradesToClose == null) ? 0 : m_requestedOpenTradesToClose.hashCode());
        result =
            prime * result + ((m_requestedTradeCloseOrderId == null) ? 0 : m_requestedTradeCloseOrderId.hashCode());
        result = prime * result + ((m_reversedOrderId == null) ? 0 : m_reversedOrderId.hashCode());
        result = prime * result + ((m_reversedTradeId == null) ? 0 : m_reversedTradeId.hashCode());
        result = prime * result + ((m_rolloverClosingTradeId == null) ? 0 : m_rolloverClosingTradeId.hashCode());
        result = prime * result + ((m_sessionKey == null) ? 0 : m_sessionKey.hashCode());
        result = prime * result + ((m_settleTime == null) ? 0 : m_settleTime.hashCode());
        result = prime * result + ((m_strikePrice == null) ? 0 : m_strikePrice.hashCode());
        result = prime * result + ((m_strikePriceAdditional == null) ? 0 : m_strikePriceAdditional.hashCode());
        result = prime * result + ((m_tenor == null) ? 0 : m_tenor.hashCode());
        result = prime * result + ((m_tradeAmount == null) ? 0 : m_tradeAmount.hashCode());
        result = prime * result + ((m_tradeAmountCurrency == null) ? 0 : m_tradeAmountCurrency.hashCode());
        result = prime * result +
            ((m_tradeAmountInTradingAccountCurrency == null) ? 0 : m_tradeAmountInTradingAccountCurrency.hashCode());
        result = prime * result + ((m_tradeId == null) ? 0 : m_tradeId.hashCode());
        result = prime * result + ((m_tradeInstrumentPrice == null) ? 0 : m_tradeInstrumentPrice.hashCode());
        result = prime * result + ((m_tradeMarginAmount == null) ? 0 : m_tradeMarginAmount.hashCode());
        result = prime * result + ((m_tradeMarginCurrency == null) ? 0 : m_tradeMarginCurrency.hashCode());
        result = prime * result +
            ((m_tradeMarginInTradingAccountCurrency == null) ? 0 : m_tradeMarginInTradingAccountCurrency.hashCode());
        result = prime * result + ((m_tradePrice == null) ? 0 : m_tradePrice.hashCode());
        result = prime * result + ((m_tradeProfitAndLossInTradingAccountCurrency == null) ? 0
            : m_tradeProfitAndLossInTradingAccountCurrency.hashCode());
        result = prime * result + ((m_tradeProfitLoss == null) ? 0 : m_tradeProfitLoss.hashCode());
        result = prime * result + ((m_tradeProfitLossCurrency == null) ? 0 : m_tradeProfitLossCurrency.hashCode());
        result = prime * result + ((m_tradeQuantity == null) ? 0 : m_tradeQuantity.hashCode());
        result = prime * result + ((m_tradeQuantityCurrency == null) ? 0 : m_tradeQuantityCurrency.hashCode());
        result = prime * result + ((m_tradeTime == null) ? 0 : m_tradeTime.hashCode());
        result = prime * result + ((m_tradingAccountCodifier == null) ? 0 : m_tradingAccountCodifier.hashCode());
        result = prime * result + ((m_tradingAccountCurrency == null) ? 0 : m_tradingAccountCurrency.hashCode());
        result = prime * result + ((m_tradingAccountCustomInfo == null) ? 0 : m_tradingAccountCustomInfo.hashCode());
        result = prime * result + ((m_tradingAccountCustomInfoVirtualPortfolioCodifier == null) ? 0
            : m_tradingAccountCustomInfoVirtualPortfolioCodifier.hashCode());
        result = prime * result + ((m_tradingAccountFunction == null) ? 0 : m_tradingAccountFunction.hashCode());
        result = prime * result + ((m_tradingAccountId == null) ? 0 : m_tradingAccountId.hashCode());
        result = prime * result + ((m_tradingAccountType == null) ? 0 : m_tradingAccountType.hashCode());
        result = prime * result + ((m_updateIdentityToken == null) ? 0 : m_updateIdentityToken.hashCode());
        result = prime * result +
            ((m_updateOnBehalfOfIdentityToken == null) ? 0 : m_updateOnBehalfOfIdentityToken.hashCode());
        result = prime * result + ((m_updateTime == null) ? 0 : m_updateTime.hashCode());
        result = prime * result + ((m_versionNumber == null) ? 0 : m_versionNumber.hashCode());
        result = prime * result + ((m_visitId == null) ? 0 : m_visitId.hashCode());
        result = prime * result + ((m_comment == null) ? 0 : m_comment.hashCode());
        result = prime * result + ((m_tradeInstrumentAmount == null) ? 0 : m_tradeInstrumentAmount.hashCode());
        result = prime * result + ((m_referenceTradePrice == null) ? 0 : m_referenceTradePrice.hashCode());
        result = prime * result + ((m_ladderQuantitiesOverride == null) ? 0 : m_ladderQuantitiesOverride.hashCode());
        result = prime * result + ((m_cmcTradeInstrumentPrice == null) ? 0 : m_cmcTradeInstrumentPrice.hashCode());
        result = prime * result + ((m_tradeTime2 == null) ? 0 : m_tradeTime2.hashCode());
        result = prime * result + ((m_tradeTimeConfirmation == null) ? 0 : m_tradeTimeConfirmation.hashCode());
      //  result = prime * result + ((m_tradePriceOffset == null) ? 0 : m_tradePriceOffset.hashCode());
        result = prime * result + ((m_quoteIsFirstGoodInContinuousTrading == null) ? 0 : m_quoteIsFirstGoodInContinuousTrading.hashCode());
        result = prime * result + ((m_priceStreamCode == null) ? 0 : m_priceStreamCode.hashCode());
        result = prime * result + ((m_capitalGainsInTaxCurrency == null) ? 0 : m_capitalGainsInTaxCurrency.hashCode());
        result = prime * result + ((m_isPairCurrencyInFractionalParts == null) ? 0 : m_isPairCurrencyInFractionalParts.hashCode());
        result = prime * result + ((m_bidPriceOffset == null) ? 0 : m_bidPriceOffset.hashCode());
        result = prime * result + ((m_askPriceOffset == null) ? 0 : m_askPriceOffset.hashCode());
        result = prime * result + ((m_priceOffsetType == null) ? 0 : m_priceOffsetType.hashCode());
        result = prime * result + ((m_valueDate == null) ? 0 : m_valueDate.hashCode());
        result = prime * result + ((m_spotPrice == null) ? 0 : m_spotPrice.hashCode());
        result = prime * result + ((m_pairCurrency == null) ? 0 : m_pairCurrency.hashCode());
        result = prime * result + ((m_primaryCurrency == null) ? 0 : m_primaryCurrency.hashCode());
        result = prime * result + ((m_secondaryCurrency == null) ? 0 : m_secondaryCurrency.hashCode());
        result = prime * result + ((m_primaryAmount == null) ? 0 : m_primaryAmount.hashCode());
        result = prime * result + ((m_secondaryAmount == null) ? 0 : m_secondaryAmount.hashCode());
        result = prime * result + ((m_fxTenor == null) ? 0 : m_fxTenor.hashCode());
        result = prime * result + ((m_fxTradeDate == null) ? 0 : m_fxTradeDate.hashCode());
        result = prime * result + ((m_swapQuoteId == null) ? 0 : m_swapQuoteId.hashCode());
        result = prime * result + ((m_fullSpreadTradePrice == null) ? 0 : m_fullSpreadTradePrice.hashCode());
        result = prime * result + ((m_modifiedOpenTradeId == null) ? 0 : m_modifiedOpenTradeId.hashCode());
        result = prime * result + ((m_tradeTax == null) ? 0 : m_tradeTax.hashCode());
        result = prime * result + ((m_capitalGainsInAccountCurrency == null) ? 0 : m_capitalGainsInAccountCurrency.hashCode());
        result = prime * result + ((m_amountFxRateBanding == null) ? 0 : m_amountFxRateBanding.hashCode());
        result = prime * result + ((m_skipMifidReporting == null) ? 0 : m_skipMifidReporting.hashCode());
        result = prime * result + ((m_skipContractNotes == null) ? 0 : m_skipContractNotes.hashCode());
        result = prime * result + ((m_portfolioSwapPeriodSequenceNumber == null) ? 0 : m_portfolioSwapPeriodSequenceNumber.hashCode());
        result = prime * result + ((m_venue == null) ? 0 : m_venue.hashCode());
        result = prime * result + ((m_executionBroker == null) ? 0 : m_executionBroker.hashCode());
        result = prime * result + ((m_tradeDirection == null) ? 0 : m_tradeDirection.hashCode());
        result = prime * result + ((m_accountingTradeTime == null) ? 0 : m_accountingTradeTime.hashCode());
        result = prime * result + ((m_tradeProfitLossInPrimaryCurrency == null) ? 0 : m_tradeProfitLossInPrimaryCurrency.hashCode());
        result = prime * result + ((m_optionCode == null) ? 0 : m_optionCode.hashCode());
        result = prime * result + ((m_execId == null) ? 0 : m_execId.hashCode());
        result = prime * result + ((m_isClosesLastPosition == null) ? 0 : m_isClosesLastPosition.hashCode());
        result = prime * result + ((m_accruedHoldingCosts == null) ? 0 : m_accruedHoldingCosts.hashCode());
        result = prime * result + ((m_metatraderTradeId == null) ? 0 : m_metatraderTradeId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(this == obj)
            return true;
        if(!super.equals(obj))
            return false;
        if(getClass() != obj.getClass())
            return false;
        AbstractTradeEntity other = (AbstractTradeEntity) obj;
        if(m_IsRolloverClosing == null)
        {
            if(other.m_IsRolloverClosing != null)
                return false;
        }
        else if(!m_IsRolloverClosing.equals(other.m_IsRolloverClosing))
            return false;
        if(m_aggregatedPriceQuantity == null)
        {
            if(other.m_aggregatedPriceQuantity != null)
                return false;
        }
        else if(!m_aggregatedPriceQuantity.equals(other.m_aggregatedPriceQuantity))
            return false;
        if(m_amountFxRate == null)
        {
            if(other.m_amountFxRate != null)
                return false;
        }
        else if(!m_amountFxRate.equals(other.m_amountFxRate))
            return false;
        if(m_amountFxRateId == null)
        {
            if(other.m_amountFxRateId != null)
                return false;
        }
        else if(!m_amountFxRateId.equals(other.m_amountFxRateId))
            return false;
        if(m_appToUnits == null)
        {
            if(other.m_appToUnits != null)
                return false;
        }
        else if(!m_appToUnits.equals(other.m_appToUnits))
            return false;
        if(m_binaryType != other.m_binaryType)
            return false;
        if(m_bookingNumber == null)
        {
            if(other.m_bookingNumber != null)
                return false;
        }
        else if(!m_bookingNumber.equals(other.m_bookingNumber))
            return false;
        if(m_channelId == null)
        {
            if(other.m_channelId != null)
                return false;
        }
        else if(!m_channelId.equals(other.m_channelId))
            return false;
        if(m_controlledOrderType == null)
        {
            if(other.m_controlledOrderType != null)
                return false;
        }
        else if(!m_controlledOrderType.equals(other.m_controlledOrderType))
            return false;
        if(m_cp1CashAccountNumber == null)
        {
            if(other.m_cp1CashAccountNumber != null)
                return false;
        }
        else if(!m_cp1CashAccountNumber.equals(other.m_cp1CashAccountNumber))
            return false;
        if(m_cp2CashAccountNumber == null)
        {
            if(other.m_cp2CashAccountNumber != null)
                return false;
        }
        else if(!m_cp2CashAccountNumber.equals(other.m_cp2CashAccountNumber))
            return false;
        if(m_cp2CustomInfo == null)
        {
            if(other.m_cp2CustomInfo != null)
                return false;
        }
        else if(!m_cp2CustomInfo.equals(other.m_cp2CustomInfo))
            return false;
        if(m_cp2TradingAccountCodifier == null)
        {
            if(other.m_cp2TradingAccountCodifier != null)
                return false;
        }
        else if(!m_cp2TradingAccountCodifier.equals(other.m_cp2TradingAccountCodifier))
            return false;
        if(m_cp2TradingAccountId == null)
        {
            if(other.m_cp2TradingAccountId != null)
                return false;
        }
        else if(!m_cp2TradingAccountId.equals(other.m_cp2TradingAccountId))
            return false;
        if(m_cp2TradingAccountType == null)
        {
            if(other.m_cp2TradingAccountType != null)
                return false;
        }
        else if(!m_cp2TradingAccountType.equals(other.m_cp2TradingAccountType))
            return false;
        if(m_creationIdentityToken == null)
        {
            if(other.m_creationIdentityToken != null)
                return false;
        }
        else if(!m_creationIdentityToken.equals(other.m_creationIdentityToken))
            return false;
        if(m_creationOnBehalfOfIdentityToken == null)
        {
            if(other.m_creationOnBehalfOfIdentityToken != null)
                return false;
        }
        else if(!m_creationOnBehalfOfIdentityToken.equals(other.m_creationOnBehalfOfIdentityToken))
            return false;
        if(m_creationTime == null)
        {
            if(other.m_creationTime != null)
                return false;
        }
        else if(!m_creationTime.equals(other.m_creationTime))
            return false;
        if(m_direction == null)
        {
            if(other.m_direction != null)
                return false;
        }
        else if(!m_direction.equals(other.m_direction))
            return false;
        if(m_directionMultiplier == null)
        {
            if(other.m_directionMultiplier != null)
                return false;
        }
        else if(!m_directionMultiplier.equals(other.m_directionMultiplier))
            return false;
        if(m_eventTime == null)
        {
            if(other.m_eventTime != null)
                return false;
        }
        else if(!m_eventTime.equals(other.m_eventTime))
            return false;
        if(m_executionStartedTime == null)
        {
            if(other.m_executionStartedTime != null)
                return false;
        }
        else if(!m_executionStartedTime.equals(other.m_executionStartedTime))
            return false;
        if(m_executionType != other.m_executionType)
            return false;
        if(m_feedSymbol == null)
        {
            if(other.m_feedSymbol != null)
                return false;
        }
        else if(!m_feedSymbol.equals(other.m_feedSymbol))
            return false;
        if(m_financingRatio == null)
        {
            if(other.m_financingRatio != null)
                return false;
        }
        else if(!m_financingRatio.equals(other.m_financingRatio))
            return false;
        if(m_isDeleted == null)
        {
            if(other.m_isDeleted != null)
                return false;
        }
        else if(!m_isDeleted.equals(other.m_isDeleted))
            return false;
        if(m_isHedged == null)
        {
            if(other.m_isHedged != null)
                return false;
        }
        else if(!m_isHedged.equals(other.m_isHedged))
            return false;
        if(m_isLateDeal == null)
        {
            if(other.m_isLateDeal != null)
                return false;
        }
        else if(!m_isLateDeal.equals(other.m_isLateDeal))
            return false;
        if(m_isLimitTrailing == null)
        {
            if(other.m_isLimitTrailing != null)
                return false;
        }
        else if(!m_isLimitTrailing.equals(other.m_isLimitTrailing))
            return false;
        if(m_isMandatory == null)
        {
            if(other.m_isMandatory != null)
                return false;
        }
        else if(!m_isMandatory.equals(other.m_isMandatory))
            return false;
        if(m_isPrimary == null)
        {
            if(other.m_isPrimary != null)
                return false;
        }
        else if(!m_isPrimary.equals(other.m_isPrimary))
            return false;
        if(m_isProductCurrencyInFractionalParts == null)
        {
            if(other.m_isProductCurrencyInFractionalParts != null)
                return false;
        }
        else if(!m_isProductCurrencyInFractionalParts.equals(other.m_isProductCurrencyInFractionalParts))
            return false;
        if(m_isTradeOfControlledOrder == null)
        {
            if(other.m_isTradeOfControlledOrder != null)
                return false;
        }
        else if(!m_isTradeOfControlledOrder.equals(other.m_isTradeOfControlledOrder))
            return false;
        if(m_limitPrice == null)
        {
            if(other.m_limitPrice != null)
                return false;
        }
        else if(!m_limitPrice.equals(other.m_limitPrice))
            return false;
        if(m_limitPriceCondition == null)
        {
            if(other.m_limitPriceCondition != null)
                return false;
        }
        else if(!m_limitPriceCondition.equals(other.m_limitPriceCondition))
            return false;
        if(m_limitTrailingBestPrice == null)
        {
            if(other.m_limitTrailingBestPrice != null)
                return false;
        }
        else if(!m_limitTrailingBestPrice.equals(other.m_limitTrailingBestPrice))
            return false;
        if(m_limitTrailingDistance == null)
        {
            if(other.m_limitTrailingDistance != null)
                return false;
        }
        else if(!m_limitTrailingDistance.equals(other.m_limitTrailingDistance))
            return false;
        if(m_marginFxRateAsk == null)
        {
            if(other.m_marginFxRateAsk != null)
                return false;
        }
        else if(!m_marginFxRateAsk.equals(other.m_marginFxRateAsk))
            return false;
        if(m_marginFxRateBid == null)
        {
            if(other.m_marginFxRateBid != null)
                return false;
        }
        else if(!m_marginFxRateBid.equals(other.m_marginFxRateBid))
            return false;
        if(m_marginFxRateId == null)
        {
            if(other.m_marginFxRateId != null)
                return false;
        }
        else if(!m_marginFxRateId.equals(other.m_marginFxRateId))
            return false;
        if(m_marginRequirement == null)
        {
            if(other.m_marginRequirement != null)
                return false;
        }
        else if(!m_marginRequirement.equals(other.m_marginRequirement))
            return false;
        if(m_marginType == null)
        {
            if(other.m_marginType != null)
                return false;
        }
        else if(!m_marginType.equals(other.m_marginType))
            return false;
        if(m_mmAccountId == null)
        {
            if(other.m_mmAccountId != null)
                return false;
        }
        else if(!m_mmAccountId.equals(other.m_mmAccountId))
            return false;
        if(m_mmInstrumentId == null)
        {
            if(other.m_mmInstrumentId != null)
                return false;
        }
        else if(!m_mmInstrumentId.equals(other.m_mmInstrumentId))
            return false;
        if(m_mmValueDate == null)
        {
            if(other.m_mmValueDate != null)
                return false;
        }
        else if(!m_mmValueDate.equals(other.m_mmValueDate))
            return false;
        if(m_orderId == null)
        {
            if(other.m_orderId != null)
                return false;
        }
        else if(!m_orderId.equals(other.m_orderId))
            return false;
        if(m_orderTime == null)
        {
            if(other.m_orderTime != null)
                return false;
        }
        else if(!m_orderTime.equals(other.m_orderTime))
            return false;
        if(m_orderType == null)
        {
            if(other.m_orderType != null)
                return false;
        }
        else if(!m_orderType.equals(other.m_orderType))
            return false;
        if(m_platform == null)
        {
            if(other.m_platform != null)
                return false;
        }
        else if(!m_platform.equals(other.m_platform))
            return false;
        if(m_priceDesignator == null)
        {
            if(other.m_priceDesignator != null)
                return false;
        }
        else if(!m_priceDesignator.equals(other.m_priceDesignator))
            return false;
        if(m_priceLevel == null)
        {
            if(other.m_priceLevel != null)
                return false;
        }
        else if(!m_priceLevel.equals(other.m_priceLevel))
            return false;
        if(m_priceOffsetIndex == null)
        {
            if(other.m_priceOffsetIndex != null)
                return false;
        }
        else if(!m_priceOffsetIndex.equals(other.m_priceOffsetIndex))
            return false;
        if(m_priceSource == null)
        {
            if(other.m_priceSource != null)
                return false;
        }
        else if(!m_priceSource.equals(other.m_priceSource))
            return false;
        if(m_productCurrency == null)
        {
            if(other.m_productCurrency != null)
                return false;
        }
        else if(!m_productCurrency.equals(other.m_productCurrency))
            return false;
        if(m_productFinancingRatioMaximum == null)
        {
            if(other.m_productFinancingRatioMaximum != null)
                return false;
        }
        else if(!m_productFinancingRatioMaximum.equals(other.m_productFinancingRatioMaximum))
            return false;
        if(m_productFractionalPartRatio == null)
        {
            if(other.m_productFractionalPartRatio != null)
                return false;
        }
        else if(!m_productFractionalPartRatio.equals(other.m_productFractionalPartRatio))
            return false;
        if(m_productGeneration == null)
        {
            if(other.m_productGeneration != null)
                return false;
        }
        else if(!m_productGeneration.equals(other.m_productGeneration))
            return false;
        if(m_productInstrumentCode == null)
        {
            if(other.m_productInstrumentCode != null)
                return false;
        }
        else if(!m_productInstrumentCode.equals(other.m_productInstrumentCode))
            return false;
        if(m_productPointMultiplier == null)
        {
            if(other.m_productPointMultiplier != null)
                return false;
        }
        else if(!m_productPointMultiplier.equals(other.m_productPointMultiplier))
            return false;
        if(m_productSchemaCode == null)
        {
            if(other.m_productSchemaCode != null)
                return false;
        }
        else if(!m_productSchemaCode.equals(other.m_productSchemaCode))
            return false;
        if(m_productWrapperCode == null)
        {
            if(other.m_productWrapperCode != null)
                return false;
        }
        else if(!m_productWrapperCode.equals(other.m_productWrapperCode))
            return false;
        if(m_profitLossFxRateAsk == null)
        {
            if(other.m_profitLossFxRateAsk != null)
                return false;
        }
        else if(!m_profitLossFxRateAsk.equals(other.m_profitLossFxRateAsk))
            return false;
        if(m_profitLossFxRateBid == null)
        {
            if(other.m_profitLossFxRateBid != null)
                return false;
        }
        else if(!m_profitLossFxRateBid.equals(other.m_profitLossFxRateBid))
            return false;
        if(m_profitLossFxRateId == null)
        {
            if(other.m_profitLossFxRateId != null)
                return false;
        }
        else if(!m_profitLossFxRateId.equals(other.m_profitLossFxRateId))
            return false;
        if(m_prophetSymbol == null)
        {
            if(other.m_prophetSymbol != null)
                return false;
        }
        else if(!m_prophetSymbol.equals(other.m_prophetSymbol))
            return false;
        if(m_publishTime == null)
        {
            if(other.m_publishTime != null)
                return false;
        }
        else if(!m_publishTime.equals(other.m_publishTime))
            return false;
        if(m_quantityDesignator == null)
        {
            if(other.m_quantityDesignator != null)
                return false;
        }
        else if(!m_quantityDesignator.equals(other.m_quantityDesignator))
            return false;
        if(m_quantityFxRateAsk == null)
        {
            if(other.m_quantityFxRateAsk != null)
                return false;
        }
        else if(!m_quantityFxRateAsk.equals(other.m_quantityFxRateAsk))
            return false;
        if(m_quantityFxRateBid == null)
        {
            if(other.m_quantityFxRateBid != null)
                return false;
        }
        else if(!m_quantityFxRateBid.equals(other.m_quantityFxRateBid))
            return false;
        if(m_quantityFxRateId == null)
        {
            if(other.m_quantityFxRateId != null)
                return false;
        }
        else if(!m_quantityFxRateId.equals(other.m_quantityFxRateId))
            return false;
        if(m_quoteAskPrice == null)
        {
            if(other.m_quoteAskPrice != null)
                return false;
        }
        else if(!m_quoteAskPrice.equals(other.m_quoteAskPrice))
            return false;
        if(m_quoteBidPrice == null)
        {
            if(other.m_quoteBidPrice != null)
                return false;
        }
        else if(!m_quoteBidPrice.equals(other.m_quoteBidPrice))
            return false;
        if(m_quoteDepthPrice == null)
        {
            if(other.m_quoteDepthPrice != null)
                return false;
        }
        else if(!m_quoteDepthPrice.equals(other.m_quoteDepthPrice))
            return false;
        if(m_quoteId == null)
        {
            if(other.m_quoteId != null)
                return false;
        }
        else if(!m_quoteId.equals(other.m_quoteId))
            return false;
        if(m_quoteL1Ask == null)
        {
            if(other.m_quoteL1Ask != null)
                return false;
        }
        else if(!m_quoteL1Ask.equals(other.m_quoteL1Ask))
            return false;
        if(m_quoteL1Bid == null)
        {
            if(other.m_quoteL1Bid != null)
                return false;
        }
        else if(!m_quoteL1Bid.equals(other.m_quoteL1Bid))
            return false;
        if(m_quoteReceivedTime == null)
        {
            if(other.m_quoteReceivedTime != null)
                return false;
        }
        else if(!m_quoteReceivedTime.equals(other.m_quoteReceivedTime))
            return false;
        if(m_quotedL1AskPrice == null)
        {
            if(other.m_quotedL1AskPrice != null)
                return false;
        }
        else if(!m_quotedL1AskPrice.equals(other.m_quotedL1AskPrice))
            return false;
        if(m_quotedL1BidPrice == null)
        {
            if(other.m_quotedL1BidPrice != null)
                return false;
        }
        else if(!m_quotedL1BidPrice.equals(other.m_quotedL1BidPrice))
            return false;
        if(m_recordSource == null)
        {
            if(other.m_recordSource != null)
                return false;
        }
        else if(!m_recordSource.equals(other.m_recordSource))
            return false;
        if(m_reducedTradeId == null)
        {
            if(other.m_reducedTradeId != null)
                return false;
        }
        else if(!m_reducedTradeId.equals(other.m_reducedTradeId))
            return false;
        if(m_relatedChildOrderType == null)
        {
            if(other.m_relatedChildOrderType != null)
                return false;
        }
        else if(!m_relatedChildOrderType.equals(other.m_relatedChildOrderType))
            return false;
        if(m_relatedParentOrderId == null)
        {
            if(other.m_relatedParentOrderId != null)
                return false;
        }
        else if(!m_relatedParentOrderId.equals(other.m_relatedParentOrderId))
            return false;
        if(m_requestId == null)
        {
            if(other.m_requestId != null)
                return false;
        }
        else if(!m_requestId.equals(other.m_requestId))
            return false;
        if(m_requestedOpenTradesToClose == null)
        {
            if(other.m_requestedOpenTradesToClose != null)
                return false;
        }
        else if(!m_requestedOpenTradesToClose.equals(other.m_requestedOpenTradesToClose))
            return false;
        if(m_requestedTradeCloseOrderId == null)
        {
            if(other.m_requestedTradeCloseOrderId != null)
                return false;
        }
        else if(!m_requestedTradeCloseOrderId.equals(other.m_requestedTradeCloseOrderId))
            return false;
        if(m_reversedOrderId == null)
        {
            if(other.m_reversedOrderId != null)
                return false;
        }
        else if(!m_reversedOrderId.equals(other.m_reversedOrderId))
            return false;
        if(m_reversedTradeId == null)
        {
            if(other.m_reversedTradeId != null)
                return false;
        }
        else if(!m_reversedTradeId.equals(other.m_reversedTradeId))
            return false;
        if(m_rolloverClosingTradeId == null)
        {
            if(other.m_rolloverClosingTradeId != null)
                return false;
        }
        else if(!m_rolloverClosingTradeId.equals(other.m_rolloverClosingTradeId))
            return false;
        if(m_sessionKey == null)
        {
            if(other.m_sessionKey != null)
                return false;
        }
        else if(!m_sessionKey.equals(other.m_sessionKey))
            return false;
        if(m_settleTime == null)
        {
            if(other.m_settleTime != null)
                return false;
        }
        else if(!m_settleTime.equals(other.m_settleTime))
            return false;
        if(m_strikePrice == null)
        {
            if(other.m_strikePrice != null)
                return false;
        }
        else if(!m_strikePrice.equals(other.m_strikePrice))
            return false;
        if(m_strikePriceAdditional == null)
        {
            if(other.m_strikePriceAdditional != null)
                return false;
        }
        else if(!m_strikePriceAdditional.equals(other.m_strikePriceAdditional))
            return false;
        if(m_tenor == null)
        {
            if(other.m_tenor != null)
                return false;
        }
        else if(!m_tenor.equals(other.m_tenor))
            return false;
        if(m_tradeAmount == null)
        {
            if(other.m_tradeAmount != null)
                return false;
        }
        else if(!m_tradeAmount.equals(other.m_tradeAmount))
            return false;
        if(m_tradeAmountCurrency == null)
        {
            if(other.m_tradeAmountCurrency != null)
                return false;
        }
        else if(!m_tradeAmountCurrency.equals(other.m_tradeAmountCurrency))
            return false;
        if(m_tradeAmountInTradingAccountCurrency == null)
        {
            if(other.m_tradeAmountInTradingAccountCurrency != null)
                return false;
        }
        else if(!m_tradeAmountInTradingAccountCurrency.equals(other.m_tradeAmountInTradingAccountCurrency))
            return false;
        if(m_tradeId == null)
        {
            if(other.m_tradeId != null)
                return false;
        }
        else if(!m_tradeId.equals(other.m_tradeId))
            return false;
        if(m_tradeInstrumentPrice == null)
        {
            if(other.m_tradeInstrumentPrice != null)
                return false;
        }
        else if(!m_tradeInstrumentPrice.equals(other.m_tradeInstrumentPrice))
            return false;
        if(m_tradeMarginAmount == null)
        {
            if(other.m_tradeMarginAmount != null)
                return false;
        }
        else if(!m_tradeMarginAmount.equals(other.m_tradeMarginAmount))
            return false;
        if(m_tradeMarginCurrency == null)
        {
            if(other.m_tradeMarginCurrency != null)
                return false;
        }
        else if(!m_tradeMarginCurrency.equals(other.m_tradeMarginCurrency))
            return false;
        if(m_tradeMarginInTradingAccountCurrency == null)
        {
            if(other.m_tradeMarginInTradingAccountCurrency != null)
                return false;
        }
        else if(!m_tradeMarginInTradingAccountCurrency.equals(other.m_tradeMarginInTradingAccountCurrency))
            return false;
        if(m_tradePrice == null)
        {
            if(other.m_tradePrice != null)
                return false;
        }
        else if(!m_tradePrice.equals(other.m_tradePrice))
            return false;
        if(m_tradeProfitAndLossInTradingAccountCurrency == null)
        {
            if(other.m_tradeProfitAndLossInTradingAccountCurrency != null)
                return false;
        }
        else if(!m_tradeProfitAndLossInTradingAccountCurrency
            .equals(other.m_tradeProfitAndLossInTradingAccountCurrency))
            return false;
        if(m_tradeProfitLoss == null)
        {
            if(other.m_tradeProfitLoss != null)
                return false;
        }
        else if(!m_tradeProfitLoss.equals(other.m_tradeProfitLoss))
            return false;
        if(m_tradeProfitLossCurrency == null)
        {
            if(other.m_tradeProfitLossCurrency != null)
                return false;
        }
        else if(!m_tradeProfitLossCurrency.equals(other.m_tradeProfitLossCurrency))
            return false;
        if(m_tradeQuantity == null)
        {
            if(other.m_tradeQuantity != null)
                return false;
        }
        else if(!m_tradeQuantity.equals(other.m_tradeQuantity))
            return false;
        if(m_tradeQuantityCurrency == null)
        {
            if(other.m_tradeQuantityCurrency != null)
                return false;
        }
        else if(!m_tradeQuantityCurrency.equals(other.m_tradeQuantityCurrency))
            return false;
        if(m_tradeTime == null)
        {
            if(other.m_tradeTime != null)
                return false;
        }
        else if(!m_tradeTime.equals(other.m_tradeTime))
            return false;
        if(m_tradingAccountCodifier == null)
        {
            if(other.m_tradingAccountCodifier != null)
                return false;
        }
        else if(!m_tradingAccountCodifier.equals(other.m_tradingAccountCodifier))
            return false;
        if(m_tradingAccountCurrency == null)
        {
            if(other.m_tradingAccountCurrency != null)
                return false;
        }
        else if(!m_tradingAccountCurrency.equals(other.m_tradingAccountCurrency))
            return false;
        if(m_tradingAccountCustomInfo == null)
        {
            if(other.m_tradingAccountCustomInfo != null)
                return false;
        }
        else if(!m_tradingAccountCustomInfo.equals(other.m_tradingAccountCustomInfo))
            return false;
        if(m_tradingAccountCustomInfoVirtualPortfolioCodifier == null)
        {
            if(other.m_tradingAccountCustomInfoVirtualPortfolioCodifier != null)
                return false;
        }
        else if(!m_tradingAccountCustomInfoVirtualPortfolioCodifier
            .equals(other.m_tradingAccountCustomInfoVirtualPortfolioCodifier))
            return false;
        if(m_tradingAccountFunction == null)
        {
            if(other.m_tradingAccountFunction != null)
                return false;
        }
        else if(!m_tradingAccountFunction.equals(other.m_tradingAccountFunction))
            return false;
        if(m_tradingAccountId == null)
        {
            if(other.m_tradingAccountId != null)
                return false;
        }
        else if(!m_tradingAccountId.equals(other.m_tradingAccountId))
            return false;
        if(m_tradingAccountType == null)
        {
            if(other.m_tradingAccountType != null)
                return false;
        }
        else if(!m_tradingAccountType.equals(other.m_tradingAccountType))
            return false;
        if(m_updateIdentityToken == null)
        {
            if(other.m_updateIdentityToken != null)
                return false;
        }
        else if(!m_updateIdentityToken.equals(other.m_updateIdentityToken))
            return false;
        if(m_updateOnBehalfOfIdentityToken == null)
        {
            if(other.m_updateOnBehalfOfIdentityToken != null)
                return false;
        }
        else if(!m_updateOnBehalfOfIdentityToken.equals(other.m_updateOnBehalfOfIdentityToken))
            return false;
        if(m_updateTime == null)
        {
            if(other.m_updateTime != null)
                return false;
        }
        else if(!m_updateTime.equals(other.m_updateTime))
            return false;
        if(m_versionNumber == null)
        {
            if(other.m_versionNumber != null)
                return false;
        }
        else if(!m_versionNumber.equals(other.m_versionNumber))
            return false;
        if(m_visitId == null)
        {
            if(other.m_visitId != null)
                return false;
        }
        else if(!m_visitId.equals(other.m_visitId))
            return false;
        if(m_comment == null)
        {
            if(other.m_comment != null)
                return false;
        }
        else if(!m_comment.equals(other.m_comment))
            return false;
        if(m_tradeInstrumentAmount == null)
        {
            if(other.m_tradeInstrumentAmount != null)
                return false;
        }
        else if(!m_tradeInstrumentAmount.equals(other.m_tradeInstrumentAmount))
            return false;
        if(m_referenceTradePrice == null)
        {
            if(other.m_referenceTradePrice != null)
                return false;
        }
        else if(!m_referenceTradePrice.equals(other.m_referenceTradePrice))
            return false;
        if(m_ladderQuantitiesOverride == null)
        {
            if(other.m_ladderQuantitiesOverride != null)
                return false;
        }
        else if(!m_ladderQuantitiesOverride.equals(other.m_ladderQuantitiesOverride))
            return false;
        if(m_cmcTradeInstrumentPrice == null)
        {
            if(other.m_cmcTradeInstrumentPrice != null)
                return false;
        }
        else if(!m_cmcTradeInstrumentPrice.equals(other.m_cmcTradeInstrumentPrice))
            return false;
        if(m_tradeTime2 == null)
        {
            if(other.m_tradeTime2 != null)
                return false;
        }
        else if(!m_tradeTime2.equals(other.m_tradeTime2))
            return false;
        
        if(m_tradeTimeConfirmation == null)
        {
            if(other.m_tradeTimeConfirmation != null)
                return false;
        }
        else if(!m_tradeTimeConfirmation.equals(other.m_tradeTimeConfirmation))
            return false;
        
   /*     if(m_tradePriceOffset == null)
        {
            if(other.m_tradePriceOffset != null)
                return false;
        }
        else if(!m_tradePriceOffset.equals(other.m_tradePriceOffset))
            return false;
        */
        if(m_quoteIsFirstGoodInContinuousTrading == null)
        {
            if(other.m_quoteIsFirstGoodInContinuousTrading != null)
                return false;
        }
        else if(!m_quoteIsFirstGoodInContinuousTrading.equals(other.m_quoteIsFirstGoodInContinuousTrading))
            return false;        
        
        if(m_priceStreamCode == null)
        {
            if(other.m_priceStreamCode != null)
                return false;
        }
        else if(!m_priceStreamCode.equals(other.m_priceStreamCode))
            return false;
        
        if(m_capitalGainsInTaxCurrency == null)
        {
            if(other.m_capitalGainsInTaxCurrency != null)
                return false;
        }
        else if(!m_capitalGainsInTaxCurrency.equals(other.m_capitalGainsInTaxCurrency))
            return false;      
        
        if(m_isPairCurrencyInFractionalParts == null)
        {
            if(other.m_isPairCurrencyInFractionalParts != null)
                return false;
        }
        else if(!m_isPairCurrencyInFractionalParts.equals(other.m_isPairCurrencyInFractionalParts))
            return false;
        
        if(m_bidPriceOffset == null)
        {
            if(other.m_bidPriceOffset != null)
                return false;
        }
        else if(!m_bidPriceOffset.equals(other.m_bidPriceOffset))
            return false;
        
        if(m_askPriceOffset == null)
        {
            if(other.m_askPriceOffset != null)
                return false;
        }
        else if(!m_askPriceOffset.equals(other.m_askPriceOffset))
            return false;
        
        if(m_priceOffsetType == null)
        {
            if(other.m_priceOffsetType != null)
                return false;
        }
        else if(!m_priceOffsetType.equals(other.m_priceOffsetType))
            return false;
        if(m_valueDate == null)
        {
            if(other.m_valueDate != null)
                return false;
        }
        else if(!m_valueDate.equals(other.m_valueDate))
            return false;
        if(m_spotPrice == null)
        {
            if(other.m_spotPrice != null)
                return false;
        }
        else if(!m_spotPrice.equals(other.m_spotPrice))
            return false;
        if(m_pairCurrency == null)
        {
            if(other.m_pairCurrency != null)
                return false;
        }
        else if(!m_pairCurrency.equals(other.m_pairCurrency))
            return false;
        if(m_primaryCurrency == null)
        {
            if(other.m_primaryCurrency != null)
                return false;
        }
        else if(!m_primaryCurrency.equals(other.m_primaryCurrency))
            return false;
        if(m_secondaryCurrency == null)
        {
            if(other.m_secondaryCurrency != null)
                return false;
        }
        else if(!m_secondaryCurrency.equals(other.m_secondaryCurrency))
            return false;
        if(m_primaryAmount == null)
        {
            if(other.m_primaryAmount != null)
                return false;
        }
        else if(!m_primaryAmount.equals(other.m_primaryAmount))
            return false;
        if(m_secondaryAmount == null)
        {
            if(other.m_secondaryAmount != null)
                return false;
        }
        else if(!m_secondaryAmount.equals(other.m_secondaryAmount))
            return false;
        if(m_fxTenor == null)
        {
            if(other.m_fxTenor != null)
                return false;
        }
        else if(!m_fxTenor.equals(other.m_fxTenor))
            return false;
        if(m_fxTradeDate == null)
        {
            if(other.m_fxTradeDate != null)
                return false;
        }
        else if(!m_fxTradeDate.equals(other.m_fxTradeDate))
            return false;
        if(m_swapQuoteId == null)
        {
            if(other.m_swapQuoteId != null)
                return false;
        }
        else if(!m_swapQuoteId.equals(other.m_swapQuoteId))
            return false;
        if(m_fullSpreadTradePrice == null)
        {
            if(other.m_fullSpreadTradePrice != null)
                return false;
        }
        else if(!m_fullSpreadTradePrice.equals(other.m_fullSpreadTradePrice))
            return false;            
        if(m_modifiedOpenTradeId == null)
        {
            if(other.m_modifiedOpenTradeId != null)
                return false;
        }
        else if(!m_modifiedOpenTradeId.equals(other.m_modifiedOpenTradeId))
            return false;            
        if(m_tradeTax == null)
        {
            if(other.m_tradeTax != null)
                return false;
        }
        else if(!m_tradeTax.equals(other.m_tradeTax))
            return false;
        if(m_capitalGainsInAccountCurrency == null)
        {
            if(other.m_capitalGainsInAccountCurrency != null)
                return false;
        }
        else if(!m_capitalGainsInAccountCurrency.equals(other.m_capitalGainsInAccountCurrency))
            return false;
        if(m_amountFxRateBanding == null)
        {
            if(other.m_amountFxRateBanding != null)
                return false;
        }
        else if(!m_amountFxRateBanding.equals(other.m_amountFxRateBanding))
            return false;
        if(m_skipMifidReporting == null)
        {
            if(other.m_skipMifidReporting != null)
                return false;
        }
        else if(!m_skipMifidReporting.equals(other.m_skipMifidReporting))
            return false;
        if(m_skipContractNotes == null)
        {
            if(other.m_skipContractNotes != null)
                return false;
        }
        else if(!m_skipContractNotes.equals(other.m_skipContractNotes))
            return false;
        if(m_portfolioSwapPeriodSequenceNumber == null)
        {
            if(other.m_portfolioSwapPeriodSequenceNumber != null)
                return false;
        }
        else if(!m_portfolioSwapPeriodSequenceNumber.equals(other.m_portfolioSwapPeriodSequenceNumber))
            return false;
        if(m_venue == null)
        {
            if(other.m_venue != null)
                return false;
        }
        else if(!m_venue.equals(other.m_venue))
            return false;
        if(m_executionBroker == null)
        {
            if(other.m_executionBroker != null)
                return false;
        }
        else if(!m_executionBroker.equals(other.m_executionBroker))
            return false;
        if(m_tradeDirection == null)
        {
            if(other.m_tradeDirection != null)
                return false;
        }
        else if(!m_tradeDirection.equals(other.m_tradeDirection))
            return false;
        if(m_accountingTradeTime == null)
        {
            if(other.m_accountingTradeTime != null)
                return false;
        }
        else if(!m_accountingTradeTime.equals(other.m_accountingTradeTime))
            return false;
        if(m_tradeProfitLossInPrimaryCurrency == null)
        {
            if(other.m_tradeProfitLossInPrimaryCurrency != null)
                return false;
        }
        else if(!m_tradeProfitLossInPrimaryCurrency.equals(other.m_tradeProfitLossInPrimaryCurrency))
            return false;
        if(m_optionCode == null)
        {
            if(other.m_optionCode != null)
                return false;
        }
        else if(!m_optionCode.equals(other.m_optionCode))
            return false;
        if(m_execId == null)
        {
            if(other.m_execId != null)
                return false;
        }
        else if(!m_execId.equals(other.m_execId))
            return false;
        if(m_isClosesLastPosition == null)
        {
            if(other.m_isClosesLastPosition != null)
                return false;
        }
        else if(!m_isClosesLastPosition.equals(other.m_isClosesLastPosition))
            return false;
        if(m_accruedHoldingCosts == null)
        {
            if(other.m_accruedHoldingCosts != null)
                return false;
        }
        else if(!m_accruedHoldingCosts.equals(other.m_accruedHoldingCosts))
            return false;
        if(m_metatraderTradeId == null)
        {
            if(other.m_metatraderTradeId != null)
                return false;
        }
        else if(!m_metatraderTradeId.equals(other.m_metatraderTradeId))
            return false;
        return true;
    }

    @Override
    public String toString()
    {
        return "AbstractTradeEntity [m_bookingNumber=" + m_bookingNumber + ", m_channelId=" + m_channelId +
            ", m_controlledOrderType=" + m_controlledOrderType + ", m_cp1CashAccountNumber=" + m_cp1CashAccountNumber +
            ", m_cp2CashAccountNumber=" + m_cp2CashAccountNumber + ", m_cp2CustomInfo=" + m_cp2CustomInfo +
            ", m_cp2TradingAccountCodifier=" + m_cp2TradingAccountCodifier + ", m_cp2TradingAccountId=" +
            m_cp2TradingAccountId + ", m_creationIdentityToken=" + m_creationIdentityToken + ", m_creationTime=" +
            m_creationTime + ", m_creationOnBehalfOfIdentityToken=" + m_creationOnBehalfOfIdentityToken +
            ", m_direction=" + m_direction + ", m_directionMultiplier=" + m_directionMultiplier + ", m_eventTime=" +
            m_eventTime + ", m_feedSymbol=" + m_feedSymbol + ", m_financingRatio=" + m_financingRatio +
            ", m_isDeleted=" + m_isDeleted + ", m_isLateDeal=" + m_isLateDeal + ", m_isPrimary=" + m_isPrimary +
            ", m_isTradeOfControlledOrder=" + m_isTradeOfControlledOrder + ", m_marginFxRateAsk=" + m_marginFxRateAsk +
            ", m_marginFxRateBid=" + m_marginFxRateBid + ", m_marginFxRateId=" + m_marginFxRateId +
            ", m_marginRequirement=" + m_marginRequirement + ", m_marginType=" + m_marginType + ", m_mmAccountId=" +
            m_mmAccountId + ", m_mmValueDate=" + m_mmValueDate + ", m_tradeQuantityCurrency=" +
            m_tradeQuantityCurrency + ", m_tradeQuantity=" + m_tradeQuantity + ", m_orderId=" + m_orderId +
            ", m_platform=" + m_platform + ", m_productFinancingRatioMaximum=" + m_productFinancingRatioMaximum +
            ", m_priceDesignator=" + m_priceDesignator + ", m_productCurrency=" + m_productCurrency +
            ", m_productFractionalPartRatio=" + m_productFractionalPartRatio + ", m_productGeneration=" +
            m_productGeneration + ", m_productInstrumentCode=" + m_productInstrumentCode + ", m_mmInstrumentId=" +
            m_mmInstrumentId + ", m_productPointMultiplier=" + m_productPointMultiplier + ", m_productSchemaCode=" +
            m_productSchemaCode + ", m_productWrapperCode=" + m_productWrapperCode + ", m_profitLossFxRateAsk=" +
            m_profitLossFxRateAsk + ", m_profitLossFxRateBid=" + m_profitLossFxRateBid + ", m_profitLossFxRateId=" +
            m_profitLossFxRateId + ", m_prophetSymbol=" + m_prophetSymbol + ", m_publishTime=" + m_publishTime +
            ", m_quantityDesignator=" + m_quantityDesignator + ", m_quantityFxRateAsk=" + m_quantityFxRateAsk +
            ", m_quantityFxRateBid=" + m_quantityFxRateBid + ", m_quantityFxRateId=" + m_quantityFxRateId +
            ", m_quoteId=" + m_quoteId + ", m_quoteL1Ask=" + m_quoteL1Ask + ", m_quoteL1Bid=" + m_quoteL1Bid +
            ", m_quotedL1AskPrice=" + m_quotedL1AskPrice + ", m_quotedL1BidPrice=" + m_quotedL1BidPrice +
            ", m_reducedTradeId=" + m_reducedTradeId + ", m_requestId=" + m_requestId + ", m_reversedOrderId=" +
            m_reversedOrderId + ", m_reversedTradeId=" + m_reversedTradeId + ", m_sessionKey=" + m_sessionKey +
            ", m_tradingAccountCustomInfoVirtualPortfolioCodifier=" +
            m_tradingAccountCustomInfoVirtualPortfolioCodifier + ", m_tradeAmount=" + m_tradeAmount +
            ", m_tradeAmountCurrency=" + m_tradeAmountCurrency + ", m_tradeId=" + m_tradeId + ", m_tradeMarginAmount=" +
            m_tradeMarginAmount + ", m_tradeMarginCurrency=" + m_tradeMarginCurrency + ", m_tradePrice=" +
            m_tradePrice + ", m_tradeProfitLoss=" + m_tradeProfitLoss + ", m_tradeProfitLossCurrency=" +
            m_tradeProfitLossCurrency + ", m_tradeTime=" + m_tradeTime + ", m_tradingAccountCodifier=" +
            m_tradingAccountCodifier + ", m_tradingAccountCurrency=" + m_tradingAccountCurrency +
            ", m_tradingAccountFunction=" + m_tradingAccountFunction + ", m_tradingAccountId=" + m_tradingAccountId +
            ", m_tradeAmountInTradingAccountCurrency=" + m_tradeAmountInTradingAccountCurrency +
            ", m_tradeMarginInTradingAccountCurrency=" + m_tradeMarginInTradingAccountCurrency +
            ", m_tradeProfitAndLossInTradingAccountCurrency=" + m_tradeProfitAndLossInTradingAccountCurrency +
            ", m_tradingAccountCustomInfo=" + m_tradingAccountCustomInfo + ", m_updateIdentityToken=" +
            m_updateIdentityToken + ", m_updateTime=" + m_updateTime + ", m_updateOnBehalfOfIdentityToken=" +
            m_updateOnBehalfOfIdentityToken + ", m_versionNumber=" + m_versionNumber + ", m_visitId=" + m_visitId +
            ", m_relatedChildOrderType=" + m_relatedChildOrderType + ", m_isMandatory=" + m_isMandatory +
            ", m_requestedTradeCloseOrderId=" + m_requestedTradeCloseOrderId + ", m_limitPriceCondition=" +
            m_limitPriceCondition + ", m_relatedParentOrderId=" + m_relatedParentOrderId + ", m_limitPrice=" +
            m_limitPrice + ", m_limitTrailingDistance=" + m_limitTrailingDistance + ", m_limitTrailingBestPrice=" +
            m_limitTrailingBestPrice + ", m_orderType=" + m_orderType + ", m_isProductCurrencyInFractionalParts=" +
            m_isProductCurrencyInFractionalParts + ", m_quoteAskPrice=" + m_quoteAskPrice + ", m_quoteBidPrice=" +
            m_quoteBidPrice + ", m_isLimitTrailing=" + m_isLimitTrailing + ", m_tradingAccountType=" +
            m_tradingAccountType + ", m_cp2TradingAccountType=" + m_cp2TradingAccountType + ", m_recordSource=" +
            m_recordSource + ", m_quoteReceivedTime=" + m_quoteReceivedTime + ", m_priceSource=" + m_priceSource +
            ", m_appToUnits=" + m_appToUnits + ", m_rolloverClosingTradeId=" + m_rolloverClosingTradeId +
            ", m_priceLevel=" + m_priceLevel + ", m_amountFxRateId=" + m_amountFxRateId + ", m_amountFxRate=" +
            m_amountFxRate + ", m_aggregatedPriceQuantity=" + m_aggregatedPriceQuantity +
            ", m_requestedOpenTradesToClose=" + m_requestedOpenTradesToClose + ", m_isHedged=" + m_isHedged +
            ", m_orderTime=" + m_orderTime + ", m_IsRolloverClosing=" + m_IsRolloverClosing + ", m_priceOffsetIndex=" +
            m_priceOffsetIndex + ", m_executionStartedTime=" + m_executionStartedTime + ", m_tradeInstrumentPrice=" +
            m_tradeInstrumentPrice + ", m_strikePrice=" + m_strikePrice + ", m_executionType=" + m_executionType +
            ", m_quoteDepthPrice=" + m_quoteDepthPrice + ", m_binaryType=" + m_binaryType + ", m_settleTime=" +
            m_settleTime + ", m_tenor=" + m_tenor + ", m_strikePriceAdditional=" + m_strikePriceAdditional +
            ", m_comment=" + m_comment + ", m_tradeInstrumentAmount=" + m_tradeInstrumentAmount +
            ", m_referenceTradePrice=" + m_referenceTradePrice + ", m_ladderQuantitiesOverride=" +
            m_ladderQuantitiesOverride + ", m_cmcTradeInstrumentPrice=" + m_cmcTradeInstrumentPrice
            + ", m_tradeTime2=" + m_tradeTime2 + ", m_tradeTimeConfirmation=" + m_tradeTimeConfirmation 
           /* + ", m_tradePriceOffset=" + m_tradePriceOffset*/
            + ", m_quoteIsFirstGoodInContinuousTrading=" + m_quoteIsFirstGoodInContinuousTrading
            + ", m_priceStreamCode=" + m_priceStreamCode + ", m_capitalGainsInTaxCurrency=" + m_capitalGainsInTaxCurrency
            + ", m_isPairCurrencyInFractionalParts=" + m_isPairCurrencyInFractionalParts
            + ", m_bidPriceOffset=" + m_bidPriceOffset + ", m_askPriceOffset=" + m_askPriceOffset
            + ", m_priceOffsetType=" + m_priceOffsetType 
            + ", m_valueDate=" + m_valueDate 
            + ", m_spotPrice=" + m_spotPrice 
            + ", m_pairCurrency=" + m_pairCurrency 
            + ", m_primaryCurrency=" + m_primaryCurrency 
            + ", m_secondaryCurrency=" + m_secondaryCurrency 
            + ", m_primaryAmount=" + m_primaryAmount 
            + ", m_secondaryAmount=" + m_secondaryAmount 
            + ", m_fxTenor=" + m_fxTenor 
            + ", m_fxTradeDate=" + m_fxTradeDate 
            + ", m_swapQuoteId=" + m_swapQuoteId 
            + ", m_fullSpreadTradePrice=" + m_fullSpreadTradePrice 
            + ", m_modifiedOpenTradeId=" + m_modifiedOpenTradeId 
            + ", m_tradeTax=" + m_tradeTax
            + ", m_capitalGainsInAccountCurrency=" + m_capitalGainsInAccountCurrency
            + ", m_amountFxRateBanding=" + m_amountFxRateBanding
            + ", m_skipMifidReporting=" + m_skipMifidReporting
            + ", m_skipContractNotes=" + m_skipContractNotes
            + ", m_portfolioSwapPeriodSequenceNumber=" + m_portfolioSwapPeriodSequenceNumber
            + ", m_venue=" + m_venue
            + ", m_executionBroker=" + m_executionBroker
            + ", m_tradeDirection=" + m_tradeDirection
            + ", m_accountingTradeTime=" + m_accountingTradeTime
            + ", m_tradeProfitLossInPrimaryCurrency=" + m_tradeProfitLossInPrimaryCurrency
            + ", m_optionCode=" + m_optionCode
            + ", m_execId=" + m_execId
            + ", m_isClosesLastPosition=" + m_isClosesLastPosition
            + ", m_accruedHoldingCosts=" + m_accruedHoldingCosts
            + ", m_metatraderTradeId=" + m_metatraderTradeId
            + "]";
                
    }

}
