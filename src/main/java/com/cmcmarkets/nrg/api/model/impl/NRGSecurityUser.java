/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model.impl;

import java.util.Date;

import com.cmcmarkets.nrg.api.model.AbstractSecurityUserEntity;
import com.cmcmarkets.nrg.api.model.SecurityUserEntity;
import com.cmcmarkets.security.api.model.SwsUser;
import com.cmcmarkets.security.api.model.SwsUserCreatedEventArgs;

public class NRGSecurityUser extends AbstractSecurityUserEntity implements SecurityUserEntity
{
    public NRGSecurityUser(SwsUserCreatedEventArgs securityUserCreated)
    {
        super(securityUserCreated.getEventTime());

        m_eventTime = securityUserCreated.getEventTime();
        m_userName = securityUserCreated.getUser() == null ? null : securityUserCreated.getUser().getUserName();
        m_userToken = securityUserCreated.getUser() == null ? null : securityUserCreated.getUser().getUserToken();
    }

    public NRGSecurityUser(Date messageTime, SwsUser user)
    {
        super(new Date());

        m_userName = user.getUserName();
        m_userToken = user.getUserToken();
    }
}