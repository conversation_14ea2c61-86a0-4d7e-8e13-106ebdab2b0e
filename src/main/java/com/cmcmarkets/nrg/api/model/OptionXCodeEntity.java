package com.cmcmarkets.nrg.api.model;

import com.cmcmarkets.framework.messaging.common.TimeOnly;

import java.math.BigDecimal;
import java.util.Date;

public interface OptionXCodeEntity {

    String getOptionCode();

    String getNozomiOutputSymbol();

    String getInstrumentCode();

    String getTradingClassCode();

    String getTradingClassName();

    String getTickSizeTableCode();

    Date getExpirationDate();

    String getOptionType();

    BigDecimal getStrike();

    Date getLastTradingDate();

    String getOrderBookId();

    String getTradingCurrencyCode();

    Integer getStrikeExponent();

    String getFISReferenceId();

    String getPricingInstanceName();

    Integer getTradingClassLastTradingDateOffset();

    String getTradingClassLastTradingTime();

    Date getStagingCreationTime();

    BigDecimal getMultiplier();
}
