/*
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model.impl;

import com.cmcmarkets.cashaccount.api.model.BookingEntry;
import com.cmcmarkets.cashaccount.api.model.PaymentsCustomDataDto;
import com.cmcmarkets.cashaccount.api.model.RefCustomDataContainer;
import com.cmcmarkets.framework.messaging.Message;
import com.cmcmarkets.framework.messaging.common.model.CurrencyAmount;
import com.cmcmarkets.framework.messaging.common.model.RequestInfo;
import com.cmcmarkets.framework.messaging.common.model.Version;
import com.cmcmarkets.nrg.api.model.AbstractHedgeBookingEntity;
import com.cmcmarkets.nrg.api.model.CashTransactionCharge;
import com.cmcmarkets.nrg.api.model.CashTransactionPayment;
import com.cmcmarkets.nrg.api.model.CommissionCharge;
import com.cmcmarkets.nrg.api.model.CommissionChargeData;
import com.cmcmarkets.nrg.api.model.HedgeTransaction;
import com.cmcmarkets.nrg.api.model.HedgeTransactionInfo;
import com.cmcmarkets.nrg.api.model.InstrumentRelation;
import com.cmcmarkets.nrg.api.model.mapper.CarryingCostsMapper;
import com.cmcmarkets.nrg.api.model.mapper.CashTransactionChargeMapper;
import com.cmcmarkets.nrg.api.model.mapper.CashTransactionCorporateActionDividendMapper;
import com.cmcmarkets.nrg.api.model.mapper.CashTransactionPaymentMapper;
import com.cmcmarkets.nrg.api.model.mapper.ChargeRebateMapper;
import com.cmcmarkets.nrg.api.model.mapper.CommissionChargeMapper;
import com.cmcmarkets.nrg.api.model.mapper.HedgeCarryingCostsMapper;
import com.cmcmarkets.nrg.api.model.mapper.HedgeTransactionCustomDataMapper;
import com.cmcmarkets.nrg.api.model.mapper.InstrumentRelationCustomDataMapper;
import com.cmcmarkets.nrg.server.dao.CorrelationDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class NRGHedgeBooking extends AbstractHedgeBookingEntity
{
    private static final Logger m_logger = LoggerFactory.getLogger(NRGHedgeBooking.class);
    private static final String PLATFORM = "NG";
    private static final String TRADING_ACCOUNT_TYPE = "CUSTOMER";
    private static final String TRADE_ID = "Trading.TradeId";
    private static final String ORDER_ID = "Trading.OrderId";

    private CashTransactionCorporateActionDividendMapper converter = CashTransactionCorporateActionDividendMapper.getInstance();
    private CarryingCostsMapper carryingCostsConverter = CarryingCostsMapper.getInstance(PLATFORM);
    private CashTransactionPaymentMapper ctpConverter = CashTransactionPaymentMapper.getInstance(TRADING_ACCOUNT_TYPE);
    private CommissionChargeMapper commissionChargeMapper = CommissionChargeMapper.getInstance();
    private InstrumentRelationCustomDataMapper instrumentRelationCustomDataConverter = InstrumentRelationCustomDataMapper.getInstance();
    private HedgeTransactionCustomDataMapper hedgeTransactionCustomDataMapper = HedgeTransactionCustomDataMapper.getInstance();
    private CashTransactionChargeMapper cashTransactionChargeMapper = CashTransactionChargeMapper.getInstance(TRADING_ACCOUNT_TYPE);
    private ChargeRebateMapper chargeRebateConverter = ChargeRebateMapper.getInstance();
    private HedgeCarryingCostsMapper hedgeCarryingCostsMapper = HedgeCarryingCostsMapper.getInstance();

    public NRGHedgeBooking(Date effectiveStartTime, BookingEntry bookingEntry, CorrelationDAO<Message> correlationDAO)
    {
        super(effectiveStartTime);

        m_bookingNumber = bookingEntry.getBookingNumber();
        m_platform = PLATFORM;
        m_transactionTime = bookingEntry.getTransactionTime();
        m_accountNumberCredit = bookingEntry.getCreditAccountNumber();
        m_accountNumberDebit = bookingEntry.getDebitAccountNumber();
        RequestInfo creationInfo = bookingEntry.getCreationInfo();
        if(creationInfo != null)
        {
            m_sessionKey = creationInfo.getSessionId();
            m_channelId = creationInfo.getChannelId();
            m_requestId = creationInfo.getRequestId();
            m_requestFunction = creationInfo.getFunction();
        }
        Version version = bookingEntry.getVersion();
        if(version != null)
        {
            m_versionNumber = version.getVersionNumber();
            m_isDeleted = version.isDeleted();
            m_creationTime = version.getCreationTime();
            m_creationIdentityToken = version.getCreationIdentityToken();
            m_creationOnBehalfOfIdentityToken = version.getCreationOnBehalfOfIdentityToken();
            m_updateTime = version.getUpdateTime();
            m_updateIdentityToken = version.getUpdateIdentityToken();
            m_updateOnBehalfOfIdentityToken = version.getUpdateOnBehalfOfIdentityToken();
        }
        CurrencyAmount amount = bookingEntry.getAmount();
        if(amount != null)
        {
            m_amount = amount.getAmount();
            m_amountCurrency = amount.getCurrency();
        }
        CurrencyAmount initialClearedDebitAccountBalance = bookingEntry.getOBSOLETEInitialClearedDebitAccountBalance();
        if(initialClearedDebitAccountBalance != null)
        {
            m_intitialClearedDebitAccountBalance = initialClearedDebitAccountBalance.getAmount();
            m_intitialClearedDebitAccountBalanceCurrency = initialClearedDebitAccountBalance.getCurrency();
        }
        CurrencyAmount initialUnclearedDebitAccountBalance = bookingEntry.getOBSOLETEInitialUnclearedDebitAccountBalance();
        if(initialUnclearedDebitAccountBalance != null)
        {
            m_intitialUnclearedDebitAccountBalance = initialUnclearedDebitAccountBalance.getAmount();
            m_intitialUnclearedDebitAccountBalanceCurrency = initialUnclearedDebitAccountBalance.getCurrency();
        }
        CurrencyAmount initialClearedCreditAccountBalance = bookingEntry.getOBSOLETEInitialClearedCreditAccountBalance();
        if(initialClearedCreditAccountBalance != null)
        {
            m_intitialClearedCreditAccountBalance = initialClearedCreditAccountBalance.getAmount();
            m_intitialClearedCreditAccountBalanceCurrency = initialClearedCreditAccountBalance.getCurrency();
        }
        CurrencyAmount initialUnclearedCreditAccountBalance = bookingEntry.getOBSOLETEInitialUnclearedCreditAccountBalance();
        if(initialUnclearedCreditAccountBalance != null)
        {
            m_intitialUncleardCreditAccountBalance = initialUnclearedCreditAccountBalance.getAmount();
            m_intitialUncleardCreditAccountBalanceCurrency = initialUnclearedCreditAccountBalance.getCurrency();
        }
        m_isConditional = bookingEntry.isConditional();
        m_isCancellation = bookingEntry.isCancellation();
        m_lateDeal = "NO";
        m_cancelledBookingNumber = bookingEntry.getCancelledBookingNumber();
        m_reservationNumber = bookingEntry.getReservationNumber();
        m_bookingType = bookingEntry.getBookingType();
        m_bookingComment = bookingEntry.getComment();
        m_referenceCodifier = bookingEntry.getRefCodifier();
        m_referenceCode = bookingEntry.getRefCode();

        m_isExecuted = Boolean.TRUE;

        // check trading account number for debit customer cash account number

        m_logger.debug("Checking trading account for DEBIT cash account {}", m_accountNumberDebit);

        Long debitTradingAccountNumber = correlationDAO.getTradingAccountForCustomerCashAccount(m_accountNumberDebit);

        if(debitTradingAccountNumber != null && debitTradingAccountNumber != -1)
        {
            m_tradingAccountCode = debitTradingAccountNumber;
        }
        else
        {
            // check trading account number for credit customer cash account
            // number
            m_logger.debug("Checking trading account for CREDIT cash account {}", m_accountNumberCredit);

            Long creditTradingAccountNumber = correlationDAO.getTradingAccountForCustomerCashAccount(m_accountNumberCredit);

            if(creditTradingAccountNumber != null && creditTradingAccountNumber != -1)
            {
                m_tradingAccountCode = creditTradingAccountNumber;
            }
        }

        if(m_tradingAccountCode != null)
        {
            m_tradingAccountType = TRADING_ACCOUNT_TYPE;
        }

        if(m_referenceCodifier != null)
        {
            if(m_referenceCodifier.equals(TRADE_ID))
            {
                m_tradeId = m_referenceCode;
            }
            else if(m_referenceCodifier.equals(ORDER_ID))
            {
                m_orderId = m_referenceCode;
            }
        }

        m_recordSource = "NG-HEDGE";

        if(bookingEntry.getRefCustomDataContainer() != null)
        {
            RefCustomDataContainer refCustomDataContainer = bookingEntry.getRefCustomDataContainer();

            m_corporateActionDividend = converter.map(refCustomDataContainer.getCorporateActionDividend());

            PeriodicSettlements periodicSettlements = carryingCostsConverter.map(refCustomDataContainer.getCarryingCosts());
            if(periodicSettlements != null)
            {
                m_periodicSettlements = periodicSettlements.getPeriodicSettlements();
            }


            m_payments = ctpConverter.map(refCustomDataContainer.getPayments());
            if(m_tradingAccountCode == null)
            {
                if(m_payments != null)
                {
                    for(CashTransactionPayment cashTransactionPayment : m_payments)
                    {
                        Long tradingAccountCode = cashTransactionPayment.getTradingAccountCode();
                        if(tradingAccountCode != null)
                        {
                            m_tradingAccountCode = tradingAccountCode;
                            break;
                        }
                    }
                }
                if(m_tradingAccountCode != null)
                {
                    m_tradingAccountType = TRADING_ACCOUNT_TYPE;
                }
            }

            PaymentsCustomDataDto paymentsCustomDto = refCustomDataContainer.getPayments();
            if(paymentsCustomDto != null)
            {
                m_paymentCode = paymentsCustomDto.getPaymentCode();
                m_paymentCodifier = paymentsCustomDto.getPaymentCodifier();
            }

            populateChargesCustomData(refCustomDataContainer);

            CommissionChargeData commissionChargeData = commissionChargeMapper.map(refCustomDataContainer.getCommissionCharge());

            populateCommissionCharge(commissionChargeData);

            InstrumentRelation instrumentRelation = instrumentRelationCustomDataConverter.map(refCustomDataContainer.getInstrumentRelation());
            if(instrumentRelation != null)
            {
                m_instrumentRelationInstrumentCode = instrumentRelation.getInstrumentCode();
                m_instrumentRelationAssetClass = instrumentRelation.getAssetClass();
                m_riskBucket = instrumentRelation.getRiskBucket();
            }

            HedgeTransaction hedgeTransaction = hedgeTransactionCustomDataMapper.map(refCustomDataContainer.getHedgeTransaction());
            if(hedgeTransaction != null)
            {
                m_hedgeTransactionInfo = new HedgeTransactionInfo(hedgeTransaction.getTransactionId(),
                                                                  hedgeTransaction.getBrokerCode(),
                                                                  hedgeTransaction.getInstrumentCode(),
                                                                  hedgeTransaction.getFxRevalRate(),
                                                                  hedgeTransaction.getRiskBucket());
            }
            m_hedgeCarryingCosts = hedgeCarryingCostsMapper.map(refCustomDataContainer.getHedgeCarryingCosts());
        }
        m_parentBookingNumber = bookingEntry.getParentBookingNumber();
        m_toBeCleared = bookingEntry.isToBeCleared();
        m_isCancelled = bookingEntry.isCancelled();
        m_cancellingBookingNumber = bookingEntry.getCancelBookingNumber();
    }

    private void populateCommissionCharge(CommissionChargeData commissionChargeData) {
        if(commissionChargeData != null)
        {
            m_commissionCharge = new CommissionCharge();
            m_commissionCharge.setInstrumentCode(commissionChargeData.getInstrumentCode());
            m_commissionCharge.setProductWrapperCode(commissionChargeData.getProductWrapperCode());
            m_commissionCharge.setOrderId(commissionChargeData.getOrderId());
            m_commissionCharge.setAmountToChargeInInstrumentCurrency(commissionChargeData.getAmountToChargeInCommissionCurrency());
            m_commissionCharge.setInstrumentCurrency(commissionChargeData.getCommissionCurrency());
            m_commissionCharge.setFxRate(commissionChargeData.getFxRate());
            m_commissionCharge.setMatchedQuantity(commissionChargeData.getMatchedQuantity());
            m_commissionCharge.setQuantityDesignator(commissionChargeData.getQuantityDesignator().name());
            m_commissionCharge.setTradingAccountCode(commissionChargeData.getTradingAccountCode());
            m_commissionCharge.setTradingAccountCodifier(commissionChargeData.getTradingAccountCodifier());
            m_commissionCharge.setTradingAccountPrimaryCurrency(commissionChargeData.getTradingAccountCurrency());
            m_commissionCharge.setSourceOrderId(commissionChargeData.getSourceOrderId());
            m_commissionCharge.setTotalAmountToChargeInCommissionCurrency(commissionChargeData.getTotalAmountToChargeInCommissionCurrency());
        }
    }

    private void populateChargesCustomData(RefCustomDataContainer refCustomDataContainer) {
        m_charges = cashTransactionChargeMapper.map(refCustomDataContainer.getCharges());
        if (m_tradingAccountCode == null && m_charges != null) {
            for (CashTransactionCharge cashTransactionCharge : m_charges) {
                Long tradingAccountCode = cashTransactionCharge.getTradingAccountId();
                if (tradingAccountCode != null) {
                    m_tradingAccountCode = tradingAccountCode;
                    break;
                }
            }
            if (m_tradingAccountCode != null) {
                m_tradingAccountType = TRADING_ACCOUNT_TYPE;
            }
        }

        if (refCustomDataContainer.getCharges() != null)
        {
            m_chargeRebate = chargeRebateConverter.map(refCustomDataContainer.getCharges(), TRADING_ACCOUNT_TYPE);
        }
    }

}