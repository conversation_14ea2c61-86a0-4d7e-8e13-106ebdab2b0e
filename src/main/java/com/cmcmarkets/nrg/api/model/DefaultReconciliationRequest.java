package com.cmcmarkets.nrg.api.model;

import com.cmcmarkets.framework.messaging.common.model.AbstractRequestContext;

/**
 * Default implementation of the ReconciliationRequest interface.
 * 
 * <AUTHOR>
 */
public class DefaultReconciliationRequest extends AbstractRequestContext implements ReconciliationRequest
{
    private ReconciliationRequest.ReconciliationType m_reconciliationType;
    private Long m_startTime;
    private Long m_endTime;
    private Long m_eventTime;
    private String m_description;

    /**
     * @{inheritDoc
     */
    @Override
    public ReconciliationRequest.ReconciliationType getReconciliationType()
    {
        return m_reconciliationType;
    }

    /**
     * Sets the ReconciliationType of this request.
     * 
     * @param reconciliationType
     */
    public void setReconciliationType(ReconciliationRequest.ReconciliationType reconciliationType)
    {
        m_reconciliationType = reconciliationType;
    }

    /**
     * @{inheritDoc
     */
    @Override
    public Long getStartTime()
    {
        return m_startTime;
    }

    /**
     * Sets the StartTime property to the passed in value
     * 
     * @param Long
     */
    public void setStartTime(final Long startTime)
    {
        m_startTime = startTime;
    }

    /**
     * @{inheritDoc
     */
    @Override
    public Long getEndTime()
    {
        return m_endTime;
    }

    /**
     * Sets the EndTime property to the passed in value
     * 
     * @param Long
     */
    public void setEndTime(final Long endTime)
    {
        m_endTime = endTime;
    }

    /**
     * @{inheritDoc
     */
    @Override
    public Long getEventTime()
    {
        return m_eventTime;
    }

    /**
     * Sets the EventTime property to the passed in value
     * 
     * @param Long
     */
    public void setEventTime(final Long eventTime)
    {
        m_eventTime = eventTime;
    }

    /**
     * @{inheritDoc
     */
    @Override
    public String getDescription()
    {
        return m_description;
    }

    /**
     * Sets the Id property to the passed in value
     * 
     * @param String
     */
    public void setDescription(final String id)
    {
        m_description = id;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((m_endTime == null) ? 0 : m_endTime.hashCode());
        result = prime * result + ((m_eventTime == null) ? 0 : m_eventTime.hashCode());
        result = prime * result + ((m_description == null) ? 0 : m_description.hashCode());
        result = prime * result + ((m_reconciliationType == null) ? 0 : m_reconciliationType.hashCode());
        result = prime * result + ((m_startTime == null) ? 0 : m_startTime.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(this == obj)
        {
            return true;
        }
        if(!super.equals(obj))
        {
            return false;
        }
        if(getClass() != obj.getClass())
        {
            return false;
        }
        DefaultReconciliationRequest other = (DefaultReconciliationRequest) obj;
        if(m_endTime == null)
        {
            if(other.m_endTime != null)
            {
                return false;
            }
        }
        else if(!m_endTime.equals(other.m_endTime))
        {
            return false;
        }
        if(m_eventTime == null)
        {
            if(other.m_eventTime != null)
            {
                return false;
            }
        }
        else if(!m_eventTime.equals(other.m_eventTime))
        {
            return false;
        }
        if(m_description == null)
        {
            if(other.m_description != null)
            {
                return false;
            }
        }
        else if(!m_description.equals(other.m_description))
        {
            return false;
        }
        if(m_reconciliationType == null)
        {
            if(other.m_reconciliationType != null)
            {
                return false;
            }
        }
        else if(!m_reconciliationType.equals(other.m_reconciliationType))
        {
            return false;
        }
        if(m_startTime == null)
        {
            if(other.m_startTime != null)
            {
                return false;
            }
        }
        else if(!m_startTime.equals(other.m_startTime))
        {
            return false;
        }
        return true;
    }

    @Override
    public String toString()
    {
        StringBuilder builder = new StringBuilder();
        builder.append("DefaultReconciliationRequest [m_endTime=");
        builder.append(m_endTime);
        builder.append(", m_eventTime=");
        builder.append(m_eventTime);
        builder.append(", m_id=");
        builder.append(m_description);
        builder.append(", m_reconciliationType=");
        builder.append(m_reconciliationType);
        builder.append(", m_startTime=");
        builder.append(m_startTime);
        builder.append("]");
        return builder.toString();
    }

}
