/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.util.Date;

import com.cmcmarkets.statement.api.model.StatementType;

public interface StatementProcessingCompleteEntity extends NRGEntity
{
    /**
     * @return the statementType
     */
    StatementType getStatementType();

    /**
     * @return the statementDate
     */
    String getStatementDate();

    /**
     * @return the tradingAccountId
     */
    Long getTradingAccountId();

    /**
     * @return the modifiedDate
     */
    Date getModifiedDate();

    /**
     * @return the identityId
     */
    Long getIdentityId();

    /**
     * @return the bookingId
     */
    Long getBookingId();

    /**
     * @return the profitCentre
     */
    String getProfitCentre();

    /**
     * @return the partnerId
     */
    Long getPartnerId();
    
    /**
     * @return the partnerId
     */
    Long getCustomerId();
}
