/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model.impl;

import java.util.Date;

import com.cmcmarkets.nrg.api.model.AbstractTransferRequest;
import com.cmcmarkets.transfersadmin.api.fluent.model.TransferRequest;

public class NRGTransferRequest extends AbstractTransferRequest
{
    public NRGTransferRequest (Date effectiveStartTime, TransferRequest transferRequest)
    {
        super(effectiveStartTime);        
        m_id                        = transferRequest.getId();
        m_pegaCaseId                = transferRequest.getPegaCaseId();
        m_direction                 = transferRequest.getDirection();
        m_cardinality               = transferRequest.getCardinality();
        m_customerId                = transferRequest.getCustomerId();
        m_tradingAccountId          = transferRequest.getTradingAccountId();
        m_externalAccountReference  = transferRequest.getExternalAccountReference();
        m_externalAccountType       = transferRequest.getExternalAccountType();
        m_creationTime              = transferRequest.getCreationTime();
        m_lastModified              = transferRequest.getLastModified();
        m_allAssetsEntered          = transferRequest.isAllAssetsEntered();
        m_targetCompletion          = transferRequest.getTargetCompletion();
        m_positions                 = transferRequest.getPositions();
        m_state                     = transferRequest.getState();
        m_userContactDetails        = transferRequest.getUserContactDetails();
        m_broker                    = transferRequest.getBroker();
        m_answers                   = transferRequest.getAnswers();
        m_partialOptions            = transferRequest.getPartialOptions();     
        m_externalCaseReference     = transferRequest.getExternalCaseReference();
        m_completedDate             = transferRequest.getCompletedDate();
        m_freezeDays                = transferRequest.getFreezeDays();
    }
}