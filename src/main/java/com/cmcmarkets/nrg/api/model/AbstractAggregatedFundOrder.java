/**
 * This document and its contents are protected by copyright 2013 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2013
 */
package com.cmcmarkets.nrg.api.model;

import com.cmcmarkets.framework.messaging.common.model.Version;
import com.cmcmarkets.nrg.api.model.impl.NRGAggregatedFundOrderIds;
import com.cmcmarkets.trading.api3.model.Direction;
import com.cmcmarkets.trading.api3.model.FundOrderState;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class AbstractAggregatedFundOrder extends AbstractEntity
{

    protected Long m_id;
    protected Long m_processId;
    protected Direction m_direction;
    protected BigDecimal m_amount;
    protected String m_instrumentCurrency;
    protected BigDecimal m_quantity;
    protected String m_instrumentCode;
    protected String m_isin;
    protected String m_statusCode;
    protected String m_statusReason;
    protected FundOrderState m_state;
    protected Date m_tradeDate;
    protected Date m_settlementDate;
    protected BigDecimal m_dealPrice;
    protected String m_dealPriceCurrency;
    protected BigDecimal m_confirmedQuantity;
    protected BigDecimal m_dealAmount;
    protected Version m_version;
    protected Integer m_version_number;
    protected Boolean m_version_is_deleted;
    protected Date m_version_creation_time;
    protected Date m_version_update_time;
    protected Long m_version_creation_identity_token;
    protected Long m_version_creation_obo_identity_token;
    protected Long m_version_updated_identity_token;
    protected Long m_version_obo_updated_identity_token;
    protected List<NRGAggregatedFundOrderIds> m_orderIds;

    public AbstractAggregatedFundOrder(Date effectiveStartTimestamp) {
        super(effectiveStartTimestamp);
    }

    public Long getId()
    {
        return m_id;
    }

    public void setId(Long id)
    {
        m_id = id;
    }

    public Long getProcessId()
    {
        return m_processId;
    }

    public void setProcessId(Long processId)
    {
        m_processId = processId;
    }

    public Direction getDirection()
    {
        return m_direction;
    }

    public void setDirection(Direction direction)
    {
        m_direction = direction;
    }

    public BigDecimal getAmount()
    {
        return m_amount;
    }

    public void setAmount(BigDecimal amount)
    {
        m_amount = amount;
    }

    public String getInstrumentCurrency()
    {
        return m_instrumentCurrency;
    }

    public void setInstrumentCurrency(String instrumentCurrency)
    {
        m_instrumentCurrency = instrumentCurrency;
    }

    public BigDecimal getQuantity()
    {
        return m_quantity;
    }

    public void setQuantity(BigDecimal quantity)
    {
        m_quantity = quantity;
    }

    public String getInstrumentCode()
    {
        return m_instrumentCode;
    }

    public void setInstrumentCode(String instrumentCode)
    {
        m_instrumentCode = instrumentCode;
    }

    public String getIsin()
    {
        return m_isin;
    }

    public void setIsin(String isin)
    {
        m_isin = isin;
    }

    public String getStatusCode()
    {
        return m_statusCode;
    }

    public void setStatusCode(String statusCode)
    {
        m_statusCode = statusCode;
    }

    public String getStatusReason()
    {
        return m_statusReason;
    }

    public void setStatusReason(String statusReason)
    {
        m_statusReason = statusReason;
    }

    public FundOrderState getState()
    {
        return m_state;
    }

    public void setState(FundOrderState state)
    {
        m_state = state;
    }

    public Date getTradeDate()
    {
        return m_tradeDate;
    }

    public void setTradeDate(Date tradeDate)
    {
        m_tradeDate = tradeDate;
    }

    public Date getSettlementDate()
    {
        return m_settlementDate;
    }

    public void setSettlementDate(Date settlementDate)
    {
        m_settlementDate = settlementDate;
    }

    public BigDecimal getDealPrice()
    {
        return m_dealPrice;
    }

    public void setDealPrice(BigDecimal dealPrice)
    {
        m_dealPrice = dealPrice;
    }

    public String getDealPriceCurrency()
    {
        return m_dealPriceCurrency;
    }

    public void setDealPriceCurrency(String dealPriceCurrency)
    {
        m_dealPriceCurrency = dealPriceCurrency;
    }

    public BigDecimal getConfirmedQuantity()
    {
        return m_confirmedQuantity;
    }

    public void setConfirmedQuantity(BigDecimal confirmedQuantity)
    {
        m_confirmedQuantity = confirmedQuantity;
    }

    public BigDecimal getDealAmount()
    {
        return m_dealAmount;
    }

    public void setDealAmount(BigDecimal dealAmount)
    {
        m_dealAmount = dealAmount;
    }

    public Version getVersion() {
        return m_version;
    }

    public void setVersion(Version version) {
        this.m_version = version;
        this.m_version_number = version.getVersionNumber();
        this.m_version_is_deleted = version.isDeleted();
        this.m_version_creation_time = version.getCreationTime();
        this.m_version_update_time = version.getUpdateTime();
        this.m_version_creation_identity_token = version.getCreationIdentityToken();
        this.m_version_creation_obo_identity_token = version.getCreationOnBehalfOfIdentityToken();
        this.m_version_updated_identity_token = version.getUpdateIdentityToken();
        this.m_version_obo_updated_identity_token = version.getUpdateOnBehalfOfIdentityToken();
    }

    public Integer getVersionNumber() {
        return m_version_number;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.m_version_number = versionNumber;
    }

    public Boolean getVersionIsDeleted() {
        return m_version_is_deleted;
    }

    public void setVersionIsDeleted(Boolean versionIsDeleted) {
        this.m_version_is_deleted = versionIsDeleted;
    }

    public Date getVersionCreationTime() {
        return m_version_creation_time;
    }

    public void setVersionCreationTime(Date versionCreationTime) {
        this.m_version_creation_time = versionCreationTime;
    }

    public Date getVersionUpdateTime() {
        return m_version_update_time;
    }

    public void setVersionUpdateTime(Date versionUpdateTime) {
        this.m_version_update_time = versionUpdateTime;
    }

    public Long getVersionCreationIdentityToken() {
        return m_version_creation_identity_token;
    }

    public void setVersionCreationIdentityToken(Long versionCreationIdentityToken) {
        this.m_version_creation_identity_token = versionCreationIdentityToken;
    }

    public Long getVersionCreationOboIdentityToken() {
        return m_version_creation_obo_identity_token;
    }

    public void setVersionCreationOboIdentityToken(Long versionCreationOboIdentityToken) {
        this.m_version_creation_obo_identity_token = versionCreationOboIdentityToken;
    }

    public Long getVersionUpdatedIdentityToken() {
        return m_version_updated_identity_token;
    }

    public void setVersionUpdatedIdentityToken(Long versionUpdatedIdentityToken) {
        this.m_version_updated_identity_token = versionUpdatedIdentityToken;
    }

    public Long getVersionOboUpdatedIdentityToken() {
        return m_version_obo_updated_identity_token;
    }

    public void setVersionOboUpdatedIdentityToken(Long versionOboUpdatedIdentityToken) {
        this.m_version_obo_updated_identity_token = versionOboUpdatedIdentityToken;
    }

    public List<NRGAggregatedFundOrderIds> getOrderIds() {
        return m_orderIds;
    }

    public void setOrderIds(List<NRGAggregatedFundOrderIds> m_orderIds) {
        this.m_orderIds = m_orderIds;
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(
                m_id,
                m_processId,
                m_direction,
                m_amount,
                m_instrumentCurrency,
                m_quantity,
                m_instrumentCode,
                m_isin,
                m_statusCode,
                m_statusReason,
                m_state,
                m_tradeDate,
                m_settlementDate,
                m_dealPrice,
                m_dealPriceCurrency,
                m_confirmedQuantity,
                m_dealAmount,
                m_version,
                m_orderIds
        );
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj == null)
        {
            return false;
        }
        if (getClass() != obj.getClass())
        {
            return false;
        }
        final AbstractAggregatedFundOrder other = (AbstractAggregatedFundOrder)obj;
        return (
                java.util.Objects.equals(m_id, other.m_id) &&
                        java.util.Objects.equals(m_processId, other.m_processId) &&
                        java.util.Objects.equals(m_direction, other.m_direction) &&
                        java.util.Objects.equals(m_amount, other.m_amount) &&
                        java.util.Objects.equals(m_instrumentCurrency, other.m_instrumentCurrency) &&
                        java.util.Objects.equals(m_quantity, other.m_quantity) &&
                        java.util.Objects.equals(m_instrumentCode, other.m_instrumentCode) &&
                        java.util.Objects.equals(m_isin, other.m_isin) &&
                        java.util.Objects.equals(m_statusCode, other.m_statusCode) &&
                        java.util.Objects.equals(m_statusReason, other.m_statusReason) &&
                        java.util.Objects.equals(m_state, other.m_state) &&
                        java.util.Objects.equals(m_tradeDate, other.m_tradeDate) &&
                        java.util.Objects.equals(m_settlementDate, other.m_settlementDate) &&
                        java.util.Objects.equals(m_dealPrice, other.m_dealPrice) &&
                        java.util.Objects.equals(m_dealPriceCurrency, other.m_dealPriceCurrency) &&
                        java.util.Objects.equals(m_confirmedQuantity, other.m_confirmedQuantity) &&
                        java.util.Objects.equals(m_dealAmount, other.m_dealAmount) &&
                        java.util.Objects.equals(m_version, other.m_version) &&
                        java.util.Objects.equals(m_orderIds, other.m_orderIds)
        );
    }

    @Override
    public String toString()
    {
        return com.cmcmarkets.framework.logging.util.LoggingUtils.toStringHelper(this)
                .add("m_id", m_id)
                .add("m_processId", m_processId)
                .add("m_direction", m_direction)
                .add("m_amount", m_amount == null ? null : m_amount.toPlainString())
                .add("m_instrumentCurrency", m_instrumentCurrency)
                .add("m_quantity", m_quantity == null ? null : m_quantity.toPlainString())
                .add("m_instrumentCode", m_instrumentCode)
                .add("m_isin", m_isin)
                .add("m_statusCode", m_statusCode)
                .add("m_statusReason", m_statusReason)
                .add("m_state", m_state)
                .add("m_tradeDate", m_tradeDate)
                .add("m_settlementDate", m_settlementDate)
                .add("m_dealPrice", m_dealPrice == null ? null : m_dealPrice.toPlainString())
                .add("m_dealPriceCurrency", m_dealPriceCurrency)
                .add("m_confirmedQuantity", m_confirmedQuantity == null ? null : m_confirmedQuantity.toPlainString())
                .add("m_dealAmount", m_dealAmount == null ? null : m_dealAmount.toPlainString())
                .add("m_version", m_version == null ? null : m_version.toString())
                .add("m_orderIds", m_orderIds == null ? null : m_orderIds.toString())
                .toString();
    }
}
