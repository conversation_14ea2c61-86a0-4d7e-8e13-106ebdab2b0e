//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference
// Implementation, v2.2.4-2
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.05.08 at 01:30:12 PM GMT
//

package com.cmcmarkets.nrg.api.model;

import java.math.BigDecimal;

import javax.xml.datatype.XMLGregorianCalendar;

public class PartnerChargePosition
{

    protected XMLGregorianCalendar chargeTime;

    protected ExchangeDataClassification customerClassification;

    protected BigDecimal chargeAmount;

    protected String chargeAmountCurrency;

    protected FxRevalRateInfo chargeAmountFxRevalRate;

    protected BigDecimal chargeAmountInPartnerCurrency;

    protected String taxCountry;

    protected String taxName;

    protected BigDecimal taxRate;

    protected BigDecimal taxAmountInPartnerCurrency;

    protected long personId;

    protected String taxLiabilityCurrency;

    protected FxRevalRateInfo taxAmountFxRevalRate;

    protected BigDecimal taxAmountInTaxLiabilityCurrency;

    protected SubscriptionInfo subscriptionInfo;

    protected String id;

    /**
     * Gets the value of the chargeTime property.
     * 
     * @return possible object is {@link XMLGregorianCalendar }
     * 
     */
    public XMLGregorianCalendar getChargeTime()
    {
        return chargeTime;
    }

    /**
     * Sets the value of the chargeTime property.
     * 
     * @param value allowed object is {@link XMLGregorianCalendar }
     * 
     */
    public void setChargeTime(XMLGregorianCalendar value)
    {
        this.chargeTime = value;
    }

    /**
     * Gets the value of the customerClassification property.
     * 
     * @return possible object is {@link ExchangeDataClassification }
     * 
     */
    public ExchangeDataClassification getCustomerClassification()
    {
        return customerClassification;
    }

    /**
     * Sets the value of the customerClassification property.
     * 
     * @param value allowed object is {@link ExchangeDataClassification }
     * 
     */
    public void setCustomerClassification(ExchangeDataClassification value)
    {
        this.customerClassification = value;
    }

    /**
     * Gets the value of the chargeAmount property.
     * 
     * @return possible object is {@link BigDecimal }
     * 
     */
    public BigDecimal getChargeAmount()
    {
        return chargeAmount;
    }

    /**
     * Sets the value of the chargeAmount property.
     * 
     * @param value allowed object is {@link BigDecimal }
     * 
     */
    public void setChargeAmount(BigDecimal value)
    {
        this.chargeAmount = value;
    }

    /**
     * Gets the value of the chargeAmountCurrency property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getChargeAmountCurrency()
    {
        return chargeAmountCurrency;
    }

    /**
     * Sets the value of the chargeAmountCurrency property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setChargeAmountCurrency(String value)
    {
        this.chargeAmountCurrency = value;
    }

    /**
     * Gets the value of the chargeAmountFxRevalRate property.
     * 
     * @return possible object is {@link FxRevalRateInfo }
     * 
     */
    public FxRevalRateInfo getChargeAmountFxRevalRate()
    {
        return chargeAmountFxRevalRate;
    }

    /**
     * Sets the value of the chargeAmountFxRevalRate property.
     * 
     * @param value allowed object is {@link FxRevalRateInfo }
     * 
     */
    public void setChargeAmountFxRevalRate(FxRevalRateInfo value)
    {
        this.chargeAmountFxRevalRate = value;
    }

    /**
     * Gets the value of the chargeAmountInPartnerCurrency property.
     * 
     * @return possible object is {@link BigDecimal }
     * 
     */
    public BigDecimal getChargeAmountInPartnerCurrency()
    {
        return chargeAmountInPartnerCurrency;
    }

    /**
     * Sets the value of the chargeAmountInPartnerCurrency property.
     * 
     * @param value allowed object is {@link BigDecimal }
     * 
     */
    public void setChargeAmountInPartnerCurrency(BigDecimal value)
    {
        this.chargeAmountInPartnerCurrency = value;
    }

    /**
     * Gets the value of the taxCountry property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getTaxCountry()
    {
        return taxCountry;
    }

    /**
     * Sets the value of the taxCountry property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setTaxCountry(String value)
    {
        this.taxCountry = value;
    }

    /**
     * Gets the value of the taxName property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getTaxName()
    {
        return taxName;
    }

    /**
     * Sets the value of the taxName property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setTaxName(String value)
    {
        this.taxName = value;
    }

    /**
     * Gets the value of the taxRate property.
     * 
     * @return possible object is {@link BigDecimal }
     * 
     */
    public BigDecimal getTaxRate()
    {
        return taxRate;
    }

    /**
     * Sets the value of the taxRate property.
     * 
     * @param value allowed object is {@link BigDecimal }
     * 
     */
    public void setTaxRate(BigDecimal value)
    {
        this.taxRate = value;
    }

    /**
     * Gets the value of the taxAmountInPartnerCurrency property.
     * 
     * @return possible object is {@link BigDecimal }
     * 
     */
    public BigDecimal getTaxAmountInPartnerCurrency()
    {
        return taxAmountInPartnerCurrency;
    }

    /**
     * Sets the value of the taxAmountInPartnerCurrency property.
     * 
     * @param value allowed object is {@link BigDecimal }
     * 
     */
    public void setTaxAmountInPartnerCurrency(BigDecimal value)
    {
        this.taxAmountInPartnerCurrency = value;
    }

    /**
     * Gets the value of the personId property.
     * 
     */
    public long getPersonId()
    {
        return personId;
    }

    /**
     * Sets the value of the personId property.
     * 
     */
    public void setPersonId(long value)
    {
        this.personId = value;
    }

    /**
     * Gets the value of the taxLiabilityCurrency property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getTaxLiabilityCurrency()
    {
        return taxLiabilityCurrency;
    }

    /**
     * Sets the value of the taxLiabilityCurrency property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setTaxLiabilityCurrency(String value)
    {
        this.taxLiabilityCurrency = value;
    }

    /**
     * Gets the value of the taxAmountFxRevalRate property.
     * 
     * @return possible object is {@link FxRevalRateInfo }
     * 
     */
    public FxRevalRateInfo getTaxAmountFxRevalRate()
    {
        return taxAmountFxRevalRate;
    }

    /**
     * Sets the value of the taxAmountFxRevalRate property.
     * 
     * @param value allowed object is {@link FxRevalRateInfo }
     * 
     */
    public void setTaxAmountFxRevalRate(FxRevalRateInfo value)
    {
        this.taxAmountFxRevalRate = value;
    }

    /**
     * Gets the value of the taxAmountInTaxLiabilityCurrency property.
     * 
     * @return possible object is {@link BigDecimal }
     * 
     */
    public BigDecimal getTaxAmountInTaxLiabilityCurrency()
    {
        return taxAmountInTaxLiabilityCurrency;
    }

    /**
     * Sets the value of the taxAmountInTaxLiabilityCurrency property.
     * 
     * @param value allowed object is {@link BigDecimal }
     * 
     */
    public void setTaxAmountInTaxLiabilityCurrency(BigDecimal value)
    {
        this.taxAmountInTaxLiabilityCurrency = value;
    }

    /**
     * Gets the value of the subscriptionInfo property.
     * 
     * @return possible object is {@link SubscriptionInfo }
     * 
     */
    public SubscriptionInfo getSubscriptionInfo()
    {
        return subscriptionInfo;
    }

    /**
     * Sets the value of the subscriptionInfo property.
     * 
     * @param value allowed object is {@link SubscriptionInfo }
     * 
     */
    public void setSubscriptionInfo(SubscriptionInfo value)
    {
        this.subscriptionInfo = value;
    }

    /**
     * Gets the value of the id property.
     * 
     * @return possible object is {@link String }
     * 
     */
    public String getId()
    {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value allowed object is {@link String }
     * 
     */
    public void setId(String value)
    {
        this.id = value;
    }

}
