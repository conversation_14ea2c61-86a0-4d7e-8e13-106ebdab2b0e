/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class CashTransactionInterestCustomData
{
    private Long m_tradingAccountCode;
    private List<InterestDay> m_interestDays;


    public Long getTradingAccountCode() {
        return m_tradingAccountCode;
    }

    public void setTradingAccountCode(Long tradingAccountCode) {
        m_tradingAccountCode = tradingAccountCode;
    }

    public List<InterestDay> getInterestDays() {
        return m_interestDays;
    }

    public void setInterestDays(List<InterestDay> interestDays) {
        m_interestDays = interestDays;
    }

    public void addInterestDay(InterestDay interestDay) {
        if ( m_interestDays ==null) m_interestDays = new ArrayList<>();
        m_interestDays.add(interestDay);
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CashTransactionInterestCustomData that = (CashTransactionInterestCustomData) o;
        return Objects.equals(m_tradingAccountCode, that.m_tradingAccountCode) && Objects.equals(m_interestDays, that.m_interestDays);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((m_tradingAccountCode == null) ? 0 : m_tradingAccountCode.hashCode());
        result = prime * result + ((m_interestDays == null) ? 0 : m_interestDays.hashCode());
        return result;
    }
}
