package com.cmcmarkets.nrg.api.model.impl;

import com.cmcmarkets.nrg.api.model.AbstractProductSetBrokerSwapRateEntity;
import java.math.BigDecimal;

public class NRGProductSetBrokerSwapRate extends AbstractProductSetBrokerSwapRateEntity {

    public void setBrokerSwapRateCode(String brokerSwapRateCode)
    {
        this.m_brokerSwapRateCode = brokerSwapRateCode;
    }

    public void setBrokerAccountNumber(Long brokerAccountNumber)
    {
        this.m_brokerAccountNumber = brokerAccountNumber;
    }

    public void setCurrency(String currency)
    {
        this.m_currency = currency;
    }

    public void setCountryCode(String countryCode)
    {
        this.m_countryCode = countryCode;
    }

    public void setInstrumentCode(String instrumentCode)
    {
        this.m_instrumentCode = instrumentCode;
    }

    public void setDirection(String direction)
    {
        this.m_direction = direction;
    }

    public void setReferenceRate(String referenceRate)
    {
        this.m_referenceRate = referenceRate;
    }

    public void setTerm(String term)
    {
        this.m_term = term;
    }

    public void setLongHaircut(BigDecimal longHaircut)
    {
        this.m_longHaircut = longHaircut;
    }

    public void setShortHaircut(BigDecimal shortHaircut)
    {
        this.m_shortHaircut = shortHaircut;
    }

}
