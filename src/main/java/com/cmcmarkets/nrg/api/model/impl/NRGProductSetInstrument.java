/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.cmcmarkets.nrg.api.model.AbstractProductSetInstrumentEntity;
import com.cmcmarkets.nrg.api.model.InstrumentNames;
import com.cmcmarkets.nrg.api.model.PMSEntryLanguage;

public class NRGProductSetInstrument extends AbstractProductSetInstrumentEntity
{
    public void setCommodityTypeName(String commodityTypeName)
    {
        m_commodityTypeName = commodityTypeName;
    }

    public void setCompanySectorName(String companySectorName)
    {
        m_companySectorName = companySectorName;
    }

    public void setCountryClassificationName(String countryClassificationName)
    {
        m_countryClassificationName = countryClassificationName;
    }

    public void setCurrency(String currency)
    {
        m_currency = currency;
    }

    public void setCountryCode(String countryCode)
    {
        m_countryCode = countryCode;
    }

    public void setFeedSymbol(String feedSymbol)
    {
        m_feedSymbol = feedSymbol;
    }

    public void setISIN(String isin)
    {
        m_ISIN = isin;
    }

    public void setInstrumentCode(String instrumentCode)
    {
        m_instrumentCode = instrumentCode;
    }

    public void setInstrumentType(String instrumentType)
    {
        m_instrumentType = instrumentType;
    }

    public void setMIC(String mic)
    {
        m_MIC = mic;
    }

    public void setPairCurrency(String pairCurrency)
    {
        m_pairCurrency = pairCurrency;
    }

    public void setRIC(String ric)
    {
        m_RIC = ric;
    }

    public void setShortName(String shortName)
    {
        m_shortName = shortName;
    }

    public void setMMUID(Long mmuid)
    {
        m_mmuid = mmuid;
    }

    public void setMMUID2(Long mmuid2)
    {
        m_mmuid2 = mmuid2;
    }

    public void setMMVALUEID(Long mmvalueid)
    {
        m_mmvalueid = mmvalueid;
    }

    public void setProphetBandingAlgorithm(String prophetBandingAlgorithm)
    {
        m_prophetBandingAlgorithm = prophetBandingAlgorithm;
    }

    public void setProphetBandingFixedSpread(BigDecimal prophetBandingFixedSpread)
    {
        m_prophetBandingFixedSpread = prophetBandingFixedSpread;
    }

    public void setProphetBandingSpreadFactor(BigDecimal prophetBandingSpreadFactor)
    {
        m_prophetBandingSpreadFactor = prophetBandingSpreadFactor;
    }

    public void setMmInstIdCfd(Long mmInstIdCfd)
    {
        m_mmInstIdCfd = mmInstIdCfd;
    }

    public void setMmInstIdSbIr(Long mmInstIdSbIr)
    {
        m_mmInstIdSbIr = mmInstIdSbIr;
    }

    public void setMmInstIdSbUk(Long mmInstIdSbUk)
    {
        m_mmInstIdSbUk = mmInstIdSbUk;
    }

    public void setPricingStartDate(Date pricingStartDate)
    {
        m_pricingStartDate = pricingStartDate;
    }

    public void setFirstTradingDate(Date firstTradingDate)
    {
        m_firstTradingDate = firstTradingDate;
    }

    public void setLastRolloverDate(Date lastRolloverDate)
    {
        m_lastRolloverDate = lastRolloverDate;
    }

    public void setAutomaticRolloverDate(Date automaticRolloverDate)
    {
        m_automaticRolloverDate = automaticRolloverDate;
    }

    public void setLastTradingDate(Date lastTradingDate)
    {
        m_lastTradingDate = lastTradingDate;
    }

    public void setCashSettlementDate(Date cashSettlementDate)
    {
        m_cashSettlementDate = cashSettlementDate;
    }

    public void setExpiryDate(Date expiryDate)
    {
        m_expiryDate = expiryDate;
    }

    public void setIsSkipRollover(Boolean isSkipRollover)
    {
        m_isSkipRollover = isSkipRollover;
    }

    public void setLastTradingDateDesc(String lastTradingDateDesc)
    {
        m_lastTradingDateDesc = lastTradingDateDesc;
    }

    public void setLastSettlementDateDesc(String lastSettlementDateDesc)
    {
        m_lastSettlementDateDesc = lastSettlementDateDesc;
    }

    public void setLastRolloverDateDesc(String lastRolloverDateDesc)
    {
        m_lastRolloverDateDesc = lastRolloverDateDesc;
    }

    public void setContractCode(String contractCode)
    {
        m_contractCode = contractCode;
    }

    public void setRolloverTargetCode(String rolloverTargetCode)
    {
        m_rolloverTargetCode = rolloverTargetCode;
    }

    public void setCmcCashInstrumentCode(String cmcCashInstrumentCode)
    {
        m_cmcCashInstrumentCode = cmcCashInstrumentCode;
    }

    public void setCmcFinancialInstrumentType(String cmcFinancialInstrumentType)
    {
        m_cmcFinancialInstrumentType = cmcFinancialInstrumentType;
    }

    public void setExchangeProductCode(String exchangeProductCode)
    {
        m_exchangeProductCode = exchangeProductCode;
    }

    public void setIsPRRQualifyingIndex(Boolean isPRRQualifyingIndex)
    {
        m_isPRRQualifyingIndex = isPRRQualifyingIndex;
    }

    public void setPositionRiskRequirementPercent(BigDecimal positionRiskRequirementPercent)
    {
        m_positionRiskRequirementPercent = positionRiskRequirementPercent;
    }

    public void setInstrumentNames(final Map<String, InstrumentNames> instrumentNames)
    {
        m_instrumentNames = instrumentNames;
    }

    public void setSecurityType(String securityType)
    {
        m_securityType = securityType;
    }

    public void setContractSizeOverride(BigDecimal contractSizeOverride)
    {
        m_contractSizeOverride = contractSizeOverride;
    }

    public void setCommodityBase(String commodityBase)
    {
        m_commodityBase = commodityBase;
    }

    public void setCommodityDetail(String commodityDetail)
    {
        m_commodityDetail = commodityDetail;
    }

    public void setInstrumentTypeCode(String instrumentTypeCode)
    {
        m_instrumentTypeCode = instrumentTypeCode;
    }

    public void setInstrumentTypeLanguages(List<PMSEntryLanguage> instrumentTypeLanguages)
    {
        m_instrumentTypeLanguages = instrumentTypeLanguages;
    }

    public void setRiskCountryCode(String riskCountryCode)
    {
        m_riskCountryCode = riskCountryCode;
    }

    public void setTaxCountryCode(String taxCountryCode)
    {
        m_taxCountryCode = taxCountryCode;
    }

    public void setBloombergCode(String bloombergCode)
    {
        m_bloombergCode = bloombergCode;
    }

    public void setSedol(String sedol)
    {
        m_sedol = sedol;
    }

    public void setReutersMIC(String reutersMIC)
    {
        m_reutersMIC = reutersMIC;
    }

    public void setOperatingMIC(String operatingMIC)
    {
        m_operatingMIC = operatingMIC;
    }

    public void setMainExchange(String mainExchange)
    {
        m_mainExchange = mainExchange;
    }

    public void setMarketStatus(String marketStatus)
    {
        m_marketStatus = marketStatus;
    }

    public void setCurMktCapUsd(Long curMktCapUsd)
    {
        m_curMktCapUsd = curMktCapUsd;
    }

    public void setAvgDailyValueTraded30dUsd(Long avgDailyValueTraded30dUsd)
    {
        m_avgDailyValueTraded30dUsd = avgDailyValueTraded30dUsd;
    }

    public void setAvgDailyTraded3mUsd(Long avgDailyTraded3mUsd)
    {
        m_avgDailyTraded3mUsd = avgDailyTraded3mUsd;
    }

    public void setEqyFreeFloatPct(BigDecimal eqyFreeFloatPct)
    {
        m_eqyFreeFloatPct = eqyFreeFloatPct;
    }

    public void setSecurityTyp(String securityTyp)
    {
        m_securityTyp = securityTyp;
    }

    public void setIsPairCurrencyInFractionalParts(Boolean isPairCurrencyInFractionalParts)
    {
        m_isPairCurrencyInFractionalParts = isPairCurrencyInFractionalParts;
    }

    public void setPointMultiplier(BigDecimal pointMultiplier)
    {
        m_pointMultiplier = pointMultiplier;
    }

    public void setProphetPrimaryInstrumentCode(String prophetPrimaryInstrumentCode)
    {
        m_prophetPrimaryInstrumentCode = prophetPrimaryInstrumentCode;
    }

    public void setCouponRate(BigDecimal couponRate)
    {
        m_couponRate = couponRate;
    }

    public void setCheapestToDeliverDate(Date cheapestToDeliverDate)
    {
        m_cheapestToDeliverDate = cheapestToDeliverDate;
    }

    public void setMarketCode(String marketCode)
    {
        m_marketCode = marketCode;
    }

    public void setMarketAlias(String marketAlias)
    {
        m_marketAlias = marketAlias;
    }

    public void setMarketDataMappingCode(String marketDataMappingCode)
    {
        m_marketDataMappingCode = marketDataMappingCode;
    }

    public void setCfdCfi(String cfdCfi)
    {
        m_cfdCfi = cfdCfi;
    }

    public void setSbCfi(String sbCfi)
    {
        m_sbCfi = sbCfi;
    }
    
    public void setIsMifidiiReportable(Boolean isMifidiiReportable)
    {
        m_isMifidiiReportable = isMifidiiReportable;
    }
    
    public void setSecurityDescription(String securityDescription)
    {
        m_securityDescription = securityDescription;
    }

    public void setProphetBrokerMarginAmount(BigDecimal prophetBrokerMarginAmount)
    {
        m_prophetBrokerMarginAmount = prophetBrokerMarginAmount;
    }
    
    public void setProphetBrokerMarginPercent(BigDecimal prophetBrokerMarginPercent)
    {
        m_prophetBrokerMarginPercent = prophetBrokerMarginPercent;
    }
    
    public void setProphetBrokerMarginTier(String prophetBrokerMarginTier)
    {
        m_prophetBrokerMarginTier = prophetBrokerMarginTier;
    }
    
    public void setProphetBrokerMarginTierFxDB(String prophetBrokerMarginTierFxDB)
    {
        m_prophetBrokerMarginTierFxDB = prophetBrokerMarginTierFxDB;
    }
    
    public void setProphetBrokerMarginTierFxUBS(String prophetBrokerMarginTierFxUBS)
    {
        m_prophetBrokerMarginTierFxUBS = prophetBrokerMarginTierFxUBS;
    }

    public void setUnderlyingInstrumentCode(String underlyingInstrumentCode)
    {
        m_underlyingInstrumentCode = underlyingInstrumentCode;
    }

    public void setUnderlyingInstrumentAlias(String underlyingInstrumentAlias)
    {
        m_underlyingInstrumentAlias = underlyingInstrumentAlias;
    }   
    
    public void setIsCcyInFrctnlPrts(Boolean isCcyInFrctnlPrts)
    {
    	m_isCcyInFrctnlPrts = isCcyInFrctnlPrts;
    }
    
    public void setFinancingCurrency(String financingCurrency)
    {
    	m_financingCurrency = financingCurrency;
    }
    
    public void setImIsTestInstrument(Boolean imIsTestInstrument)
    {
    	m_imIsTestInstrument = imIsTestInstrument;
    }
    
    public void setImIsDemoOnly(Boolean imIsDemoOnly)
    {
    	m_imIsDemoOnly = imIsDemoOnly;
    }
    
    public void setIsFixedSpreadInstrument(Boolean isFixedSpreadInstrument)
    {
    	m_isFixedSpreadInstrument = isFixedSpreadInstrument;
    }
    
    public void setIsGuaranteedStopInstrmnt(Boolean isGuaranteedStopInstrmnt)
    {
    	m_isGuaranteedStopInstrmnt = isGuaranteedStopInstrmnt;
    }
    
    public void setIsMetatraderInstrument(Boolean isMetatraderInstrument)
    {
    	m_isMetatraderInstrument = isMetatraderInstrument;
    }
    
    public void setIsEsmaDuplicate(Boolean isEsmaDuplicate)
    {
    	m_isEsmaDuplicate = isEsmaDuplicate;
    }
    
    public void setIsFindableInstrument(Boolean isFindableInstrument)
    {
    	m_isFindableInstrument = isFindableInstrument;
    }
    
    public void setIsTradableInstrument(Boolean isTradableInstrument)
    {
    	m_isTradableInstrument = isTradableInstrument;
    }
    
    public void setAPICode(String apiCode)
    {
    	m_apiCode = apiCode;
    }
    
    public void setWorkflowStatus(String workflowStatus)
    {
    	m_workflowStatus = workflowStatus;
    }
    
    public void setIsAsicDuplicate(Boolean isAsicDuplicate)
    {
    	m_isAsicDuplicate = isAsicDuplicate;
    }
    
    public void setFirstNoticeDate(Date firstNoticeDate)
    {
    	m_firstNoticeDate = firstNoticeDate;
    }
    
    public void setProphetBcEnabled(Boolean prophetBcEnabled)
    {
    	m_prophetBcEnabled = prophetBcEnabled;
    }
    
    public void setIsValueDateRolloverInstrument(Boolean isValueDateRolloverInstrument)
    {
    	m_isValueDateRolloverInstrument = isValueDateRolloverInstrument;
    }
    
    public void setIsPreventCfdTradingOnFxAccounts(Boolean isPreventCfdTradingOnFxAccounts)
    {
    	m_isPreventCfdTradingOnFxAccounts = isPreventCfdTradingOnFxAccounts;
    }
    
    public void setTaxSecurityType(String taxSecurityType)
    {
    	m_taxSecurityType = taxSecurityType;
    }
    
    public void setProductIdValue(String productIdValue)
    {
    	m_productIdValue = productIdValue;
    }
    
    public void setProductSelector(String productSelector)
    {
    	m_productSelector = productSelector;
    }
    
    public void setIsInstrumentSI(Boolean isInstrumentSI)
    {
        m_isInstrumentSI = isInstrumentSI;
    }
    
    public void setCUSIP(String CUSIP)
    {
        m_CUSIP = CUSIP;
    }
    
    public void setFundType(String fundType)
    {
        m_fundType = fundType;
    }
    
    public void setProphetCarryAskOverride(BigDecimal prophetCarryAskOverride)
    {
        m_prophetCarryAskOverride = prophetCarryAskOverride;
    }    
    
    public void setProphetCarryBidOverride(BigDecimal prophetCarryBidOverride)
    {
        m_prophetCarryBidOverride = prophetCarryBidOverride;
    }
    
    public void setProphetCarryRateOverridden(Boolean prophetCarryRateOverridden)
    {
        m_prophetCarryRateOverridden = prophetCarryRateOverridden;
    }
    
    public void setInstrumentDelistedInMarket(Boolean instrumentDelistedInMarket)
    {
        m_instrumentDelistedInMarket = instrumentDelistedInMarket;
    }

    public void setBBCountryFullName(String BBCountryFullName)
    {
        m_BBCountryFullName = BBCountryFullName;
    }

    public void setBBReferenceTaxSecurityType(String BBReferenceTaxSecurityType)
    {
        m_BBReferenceTaxSecurityType = BBReferenceTaxSecurityType;
    }

    public void setSAXOInstrumentCode(String SAXOInstrumentCode) {
        m_SAXOInstrumentCode = SAXOInstrumentCode;
    }

    public void setAssetTypeDescription(String assetTypeDescription) {
        this.m_assetTypeDescription = assetTypeDescription;
    }

    public void setAssetSubTypeDescription(String assetSubTypeDescription) {
        this.m_assetSubTypeDescription = assetSubTypeDescription;
    }

    public void setOptionRoot(String optionRoot) {
        this.m_optionRoot = optionRoot;
    }

    public void setIsNationalDeclarationProduct (Boolean isNationalDeclarationProduct) {
        this.m_isNationalDeclarationProduct = isNationalDeclarationProduct;
    }

    public void setIsCAInstrument (Boolean isCAInstrument) {
        this.m_isCAInstrument = isCAInstrument;
    }
    public void setFundAssetClassFocus (String fundAssetClassFocus) {
        this.m_fundAssetClassFocus = fundAssetClassFocus;
    }

    public void setAnnadsbCommBaseProduct(String annadsbCommBaseProduct) {
        this.m_annadsbCommBaseProduct = annadsbCommBaseProduct;
    }

    public void setAnnadsbCommSubProduct(String annadsbCommSubProduct) {
        this.m_annadsbCommSubProduct = annadsbCommSubProduct;
    }

    public void setAnnadsbCommAddSubProduct(String annadsbCommAddSubProduct) {
        this.m_annadsbCommAddSubProduct = annadsbCommAddSubProduct;
    }

    public void setAnnadsbCommHeaderProduct(String annadsbCommHeaderProduct) {
        this.m_annadsbCommHeaderProduct = annadsbCommHeaderProduct;
    }

    public void setAnnadsbCommUnderlierId(String annadsbCommUnderlierId) {
        this.m_annadsbCommUnderlierId = annadsbCommUnderlierId;
    }

    public void setAnnadsbCommUnderlierIdSrc(String annadsbCommUnderlierIdSrc) {
        this.m_annadsbCommUnderlierIdSrc = annadsbCommUnderlierIdSrc;
    }

    public void setUpiCfd(String upiCfd) {
        this.m_upiCfd = upiCfd;
    }

    public void setUpiSb(String upiSb) {
        this.m_upiSb = upiSb;
    }

    public void setUpiOptionsEurCashPut(String upiOptionsEurCashPut) {
        this.m_upiOptionsEurCashPut = upiOptionsEurCashPut;
    }

    public void setUpiOptionsEurCashCall(String upiOptionsEurCashCall) {
        this.m_upiOptionsEurCashCall = upiOptionsEurCashCall;
    }

    public void setUpiOptionsEurPhysPut(String upiOptionsEurPhysPut) {
        this.m_upiOptionsEurPhysPut = upiOptionsEurPhysPut;
    }

    public void setUpiOptionsEurPhysCall(String upiOptionsEurPhysCall) {
        this.m_upiOptionsEurPhysCall = upiOptionsEurPhysCall;
    }

    public void setUpiOptionsAmerCashPut(String upiOptionsAmerCashPut) {
        this.m_upiOptionsAmerCashPut = upiOptionsAmerCashPut;
    }

    public void setUpiOptionsAmerCashCall(String upiOptionsAmerCashCall) {
        this.m_upiOptionsAmerCashCall = upiOptionsAmerCashCall;
    }

    public void setUpiOptionsAmerPhysPut(String upiOptionsAmerPhysPut) {
        this.m_upiOptionsAmerPhysPut = upiOptionsAmerPhysPut;
    }

    public void setUpiOptionsAmerPhysCall(String upiOptionsAmerPhysCall) {
        this.m_upiOptionsAmerPhysCall = upiOptionsAmerPhysCall;
    }

    public void setUpiOptionsBermCashPut(String upiOptionsBermCashPut) {
        this.m_upiOptionsBermCashPut = upiOptionsBermCashPut;
    }

    public void setUpiOptionsBermCashCall(String upiOptionsBermCashCall) {
        this.m_upiOptionsBermCashCall = upiOptionsBermCashCall;
    }

    public void setUpiOptionsBermPhysPut(String upiOptionsBermPhysPut) {
        this.m_upiOptionsBermPhysPut = upiOptionsBermPhysPut;
    }

    public void setUpiOptionsBermPhysCall(String upiOptionsBermPhysCall) {
        this.m_upiOptionsBermPhysCall = upiOptionsBermPhysCall;
    }
    public void setUpiCfdCountdown(String upiCfdCountdown) {
        this.m_upiCfdCountdown = upiCfdCountdown;
    }
    public void setUpiSbCountDown(String upiSbCountdown){
        this.m_upiSbCountdown = upiSbCountdown;
    }
    public void setCountdownCfi(String countdownCfi){
        this.m_countdownCfi = countdownCfi;
    }

    public void setOptionsAmerCashCallCFI(String optionsAmerCashCallCFI) {
        this.m_optionsAmerCashCallCFI = optionsAmerCashCallCFI;
    }

    public void setOptionsAmerCashPutCFI(String optionsAmerCashPutCFI) {
        this.m_optionsAmerCashPutCFI = optionsAmerCashPutCFI;
    }

    public void setOptionsAmerPhysCallCFI(String optionsAmerPhysCallCFI) {
        this.m_optionsAmerPhysCallCFI = optionsAmerPhysCallCFI;
    }

    public void setOptionsAmerPhysPutCFI(String optionsAmerPhysPutCFI) {
        this.m_optionsAmerPhysPutCFI = optionsAmerPhysPutCFI;
    }

    public void setOptionsBermCashCallCFI(String optionsBermCashCallCFI) {
        this.m_optionsBermCashCallCFI = optionsBermCashCallCFI;
    }

    public void setOptionsBermCashPutCFI(String optionsBermCashPutCFI) {
        this.m_optionsBermCashPutCFI = optionsBermCashPutCFI;
    }

    public void setOptionsBermPhysPutCFI(String optionsBermPhysPutCFI) {
        this.m_optionsBermPhysPutCFI = optionsBermPhysPutCFI;
    }

    public void setOptionsBermPhysCallCFI(String optionsBermPhysCallCFI) {
        this.m_optionsBermPhysCallCFI = optionsBermPhysCallCFI;
    }

    public void setOptionsEurCashCallCFI(String optionsEurCashCallCFI) {
        this.m_optionsEurCashCallCFI = optionsEurCashCallCFI;
    }

    public void setOptionsEurCashPutCFI(String optionsEurCashPutCFI) {
        this.m_optionsEurCashPutCFI = optionsEurCashPutCFI;
    }

    public void setOptionsEurPhysCallCFI(String optionsEurPhysCallCFI) {
        this.m_optionsEurPhysCallCFI = optionsEurPhysCallCFI;
    }

    public void setOptionsEurPhysPutCFI(String optionsEurPhysPutCFI) {
        this.m_optionsEurPhysPutCFI = optionsEurPhysPutCFI;
    }


    public void setCurrentSharesOutstanding(Long currentSharesOutstanding) {
        this.m_currentSharesOutstanding = currentSharesOutstanding;
    }

    public void setUpiSbOptions(String upiSbOptions) {
        this.m_upiSbOptions = upiSbOptions;
    }

    public void setSbOptionCfi(String sbOptionCFI) {
        this.m_sbOptionCFI = sbOptionCFI;
    }

    public void setIsUseInstForFxConversion(Boolean isUseInstForFxConversion) {
        this.m_isUseInstForFxConversion= isUseInstForFxConversion;
    }

    public void setIsOTC(Boolean isOTC) {
        this.m_isOTC= isOTC;
    }

    public void setBBCountryIncorporation(String bbCountryIncorporation) {
        this.m_bbCountryIncorporation = bbCountryIncorporation;
    }

    public void setRefTaxCountry(String refTaxCountry) {
        this.m_refTaxCountry = refTaxCountry;
    }

    public void setRefCountryIncorporationDsc(String refCountryIncorporationDsc) {
        this.m_refCountryIncorporationDsc = refCountryIncorporationDsc;
    }

    public void setUpiCfdOptions(String upiCfdOptions) {
        this.m_upiCfdOptions = upiCfdOptions;
    }

    public void setCfdOptionCFI(String cfdOptionCFI) {
        this.m_cfdOptionCFI = cfdOptionCFI;
    }

    public void setIsIsaEligible(Boolean isIsaEligible) {
        this.m_isIsaEligible = isIsaEligible;
    }

}
