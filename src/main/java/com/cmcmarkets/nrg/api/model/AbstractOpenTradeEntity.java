/**
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.cmcmarkets.nrg.pms.DbrProductCache;
import com.cmcmarkets.trading.api3.model.ManagedOpenTradeInformation;
import com.cmcmarkets.trading.api3.model.PositionTransactionType;
import com.cmcmarkets.trading.api3.model.TradingScope;
import com.cmcmarkets.trading.api3.model.StrategyType;
import com.cmcmarkets.trading.api3.model.BinaryType;
import com.cmcmarkets.trading.api3.model.ExecutionType;

public class AbstractOpenTradeEntity extends AbstractEntity implements OpenTradeEntity
{
    protected BigDecimal m_amount;
    protected String m_amountCurrency;
    protected BigDecimal m_amountInAccountCurrency;
    protected BigDecimal m_closePrice;
    protected String m_customInfoVirtualPortfolioCode;
    protected String m_direction;
    protected BigDecimal m_financingRatio;
    protected BigDecimal m_fractionalPartRatio;
    protected String m_isCurrencyInFractionalParts;
    protected Date m_lastModifiedTime;
    protected BigDecimal m_margin;
    protected String m_marginCurrency;
    protected BigDecimal m_marginSecondary;
    protected String m_marginSecondaryCurrency;
    protected BigDecimal m_marginInTradingAccountPrimaryCurrency;
    protected Long m_marketMakerBackOfficeRef;
    protected Long m_marketMakerAccountId;
    protected Long m_marketMakerInstrumentId;
    protected String m_marketMakerIsPrimary;
    protected Date m_marketMakerValueDate;
    protected Integer m_normailisedDirectionMultiplier;
    protected BigDecimal m_normailisedMarginRequirement;
    protected String m_normailisedMarginType;
    protected BigDecimal m_normailisedOpenQuantityInTradingCurrency;
    protected BigDecimal m_normailisedOpenValueInTradingCurrency;
    protected String m_normailisedTradingCurrency;
    protected String m_normailisedOrderType;
    protected BigDecimal m_normailisedOpenPrice;
    protected Date m_openTime;
    protected String m_openingTradeId;
    protected BigDecimal m_openTradeMarginFxRate;
    protected BigDecimal m_openingTradePrice;
    protected BigDecimal m_openTradeQuantityFxRate;
    protected String m_orderId;
    protected Platform m_platform;
    protected String m_priceDesignator;
    protected String m_productCurrency;
    protected BigDecimal m_productFinancingRatioMaximum;
    protected Integer m_productGeneration;
    protected String m_productInstrumentCode;
    protected BigDecimal m_productPointMultiplier;
    protected String m_productSchemaCode;
    protected String m_productWrapperCode;
    protected BigDecimal m_quantity;
    protected String m_quantityCurrency;
    protected String m_quantityDesignator;
    protected Date m_snapshotTime;
    protected Long m_sourcePlatformAccountId;
    protected Long m_sourcePlatformInstrumentId;
    protected Long m_tradingAccountCode;
    protected String m_tradingAccountCodifier;
    protected String m_tradingAccountFunction;
    protected String m_tradingAccountCurrency;
    protected String m_tradingAccountType;
    protected Long m_lastCounterpartyId;
    protected BigDecimal m_openingTradeAmountFxRate;
    protected BigDecimal m_openingTradeAppToUnits;
    protected Boolean m_isAutomaticallyRolled;
    protected String m_rolledOpenTradeId;
    protected String m_oldStyleFx;
    protected ExecutionType m_executionType;
    protected TradingScope m_tradingScope;
    protected List<ManagedOpenTradeInformation> m_managedOpenTradeInformation;
    protected BinaryType m_binaryType;
    protected Date m_settleTime;
    protected BigDecimal m_strikePrice;
    protected BigDecimal m_strikePriceAdditional;
    protected String m_tenor;
    protected Date m_tenorStartTime;
    protected BigDecimal m_openingTradeInstrumentPrice;
    protected BigDecimal m_forcedMarginFxRate;
    protected BigDecimal m_openAccruedTurnoverInAccountCurrency;
    protected Date m_valueDate;
    protected String m_pairCurrency;
    protected String m_primaryCurrency;
    protected String m_secondaryCurrency;
    protected BigDecimal m_primaryAmount;
    protected BigDecimal m_secondaryAmount;
    protected BigDecimal m_marginPercentage;
    protected PositionTransactionType m_transactionType;    
    protected BigDecimal m_accruedFeesInInstrumentCurrency;
    protected BigDecimal m_accruedFeesInPrimaryCurrency;
    protected BigDecimal m_openingTradeInstrumentPriceInPrimaryCurrency;
    protected String m_optionCode;
    protected BigDecimal m_openAccruedHoldingCosts;
    protected String m_strategyId;
    protected StrategyType m_strategyType;
    private final DbrProductCache m_productCache;

    public AbstractOpenTradeEntity(Date effectiveStartTimestamp, DbrProductCache productCache)
    {
        super(effectiveStartTimestamp);
        m_productCache = productCache;
    }

    @Override
    public BigDecimal getAmount()
    {
        return m_amount;
    }

    @Override
    public String getAmountCurrency()
    {
        return m_amountCurrency;
    }

    @Override
    public BigDecimal getAmountInTradingAccountPrimaryCurrency()
    {
        return m_amountInAccountCurrency;
    }

    @Override
    public BigDecimal getClosePrice()
    {
        return m_closePrice;
    }

    @Override
    public String getCustomInfoVirtualPortfolioCode()
    {
        return m_customInfoVirtualPortfolioCode;
    }

    @Override
    public String getDirection()
    {
        return m_direction;
    }

    @Override
    public BigDecimal getFinancingRatio()
    {
        return m_financingRatio;
    }

    @Override
    public BigDecimal getFractionalPartRatio()
    {
        return m_fractionalPartRatio;
    }

    @Override
    public String getIsCurrencyInFractionalParts()
    {
        return m_isCurrencyInFractionalParts;
    }

    @Override
    public Date getLastModifiedTime()
    {
        return m_lastModifiedTime;
    }

    @Override
    public BigDecimal getMargin()
    {
        return m_margin;
    }

    @Override
    public String getMarginCurrency()
    {
        return m_marginCurrency;
    }

    @Override
    public BigDecimal getMarginSecondary()
    {
        return m_marginSecondary;
    }

    @Override
    public String getMarginSecondaryCurrency()
    {
        return m_marginSecondaryCurrency;
    }

    @Override
    public BigDecimal getMarginInTradingAccountPrimaryCurrency()
    {
        return m_marginInTradingAccountPrimaryCurrency;
    }

    @Override
    public Long getMarketMakerBackOfficeRef()
    {
        return m_marketMakerBackOfficeRef;
    }

    @Override
    public Long getMarketMakerAccountId()
    {
        return m_marketMakerAccountId;
    }

    @Override
    public Long getMarketMakerInstrumentId()
    {
        return m_marketMakerInstrumentId;
    }

    @Override
    public String getMarketMakerIsPrimary()
    {
        return m_marketMakerIsPrimary;
    }

    @Override
    public Date getMarketMakerValueDate()
    {
        return m_marketMakerValueDate;
    }

    @Override
    public Integer getNormailisedDirectionMultiplier()
    {
        return m_normailisedDirectionMultiplier;
    }

    @Override
    public BigDecimal getNormailisedMarginRequirement()
    {
        return m_normailisedMarginRequirement;
    }

    @Override
    public String getNormailisedMarginType()
    {
        return m_normailisedMarginType;
    }

    @Override
    public BigDecimal getNormailisedOpenQuantityInTradingCurrency()
    {
        return m_normailisedOpenQuantityInTradingCurrency;
    }

    @Override
    public BigDecimal getNormailisedOpenValueInTradingCurrency()
    {
        return m_normailisedOpenValueInTradingCurrency;
    }

    @Override
    public String getNormailisedTradingCurrency()
    {
        return m_normailisedTradingCurrency;
    }

    @Override
    public String getNormailisedOrderType()
    {
        return m_normailisedOrderType;
    }

    @Override
    public BigDecimal getNormailisedOpenPrice()
    {
        return m_normailisedOpenPrice;
    }

    @Override
    public Date getOpenTime()
    {
        return m_openTime;
    }

    @Override
    public String getOpenTradeId()
    {
        return m_openingTradeId;
    }

    @Override
    public BigDecimal getOpenTradeMarginFxRate()
    {
        return m_openTradeMarginFxRate;
    }

    @Override
    public BigDecimal getOpenTradePrice()
    {
        return m_openingTradePrice;
    }

    @Override
    public BigDecimal getOpenTradeQuantityFxRate()
    {
        return m_openTradeQuantityFxRate;
    }

    @Override
    public String getOrderId()
    {
        return m_orderId;
    }

    @Override
    public Platform getPlatform()
    {
        return m_platform;
    }

    @Override
    public String getPriceDesignator()
    {
        return m_priceDesignator;
    }

    @Override
    public String getProductCurrency()
    {
        return m_productCurrency;
    }

    @Override
    public BigDecimal getProductFinancingRatioMaximum()
    {
        return m_productFinancingRatioMaximum;
    }

    @Override
    public Integer getProductGeneration()
    {
        return m_productGeneration;
    }

    @Override
    public String getProductInstrumentCode()
    {
        return m_productInstrumentCode;
    }

    @Override
    public BigDecimal getProductPointMultiplier()
    {
        return m_productPointMultiplier;
    }

    @Override
    public String getProductSchemaCode()
    {
        return m_productSchemaCode;
    }

    @Override
    public String getProductWrapperCode()
    {
        return m_productWrapperCode;
    }

    @Override
    public BigDecimal getQuantity()
    {
        return m_quantity;
    }

    @Override
    public String getQuantityCurrency()
    {
        return m_quantityCurrency;
    }

    @Override
    public String getQuantityDesignator()
    {
        return m_quantityDesignator;
    }

    @Override
    public Date getSnapshotTime()
    {
        return m_snapshotTime;
    }

    @Override
    public Long getSourcePlatformAccountId()
    {
        return m_sourcePlatformAccountId;
    }

    @Override
    public Long getSourcePlatformInstrumentId()
    {
        return m_sourcePlatformInstrumentId;
    }

    @Override
    public Long getTradingAccountCode()
    {
        return m_tradingAccountCode;
    }

    @Override
    public String getTradingAccountCodifier()
    {
        return m_tradingAccountCodifier;
    }

    @Override
    public String getTradingAccountFunction()
    {
        return m_tradingAccountFunction;
    }

    @Override
    public String getTradingAccountPrimaryCurrency()
    {
        return m_tradingAccountCurrency;
    }

    @Override
    public String getTradingAccountType()
    {
        return m_tradingAccountType;
    }

    @Override
    public Long getLastCounterpartyId()
    {
        return m_lastCounterpartyId;
    }

    public void setLastCounterpartyId(Long counterpartyAccountId)
    {
        m_lastCounterpartyId = counterpartyAccountId;
    }

    @Override
    public BigDecimal getOpeningTradeAmountFxRate()
    {
        return m_openingTradeAmountFxRate;
    }

    public void setOpeningTradeAmountFxRate(BigDecimal openingTradeAmountFxRate)
    {
        m_openingTradeAmountFxRate = openingTradeAmountFxRate;
    }

    @Override
    public BigDecimal getOpeningTradeAppToUnits()
    {
        return m_openingTradeAppToUnits;
    }

    public void setOpeningTradeAppToUnits(BigDecimal openingTradeAppToUnits)
    {
        m_openingTradeAppToUnits = openingTradeAppToUnits;
    }

    @Override
    public Boolean getIsAutomaticallyRolled()
    {
        return m_isAutomaticallyRolled;
    }

    public void setIsAutomaticallyRolled(Boolean isAutomaticallyRolled)
    {
        m_isAutomaticallyRolled = isAutomaticallyRolled;
    }

    @Override
    public String getRolledOpenTradeId()
    {
        return m_rolledOpenTradeId;
    }

    public void setIsAutomaticallyRolled(String rolledOpenTradeId)
    {
        m_rolledOpenTradeId = rolledOpenTradeId;
    }

    @Override
    public String getOldStyleFx()
    {
        return m_oldStyleFx;
    }

    public void setOldStyleFx(String oldStyleFx)
    {
        m_oldStyleFx = oldStyleFx;
    }

    @Override
    public ExecutionType getExecutionType()
    {
        return m_executionType;
    }

    public void setExecutionType(final ExecutionType executionType)
    {
        m_executionType = executionType;
    }

    @Override
    public TradingScope getTradingScope()
    {
        return m_tradingScope;
    }

    public void setTradingScope(final TradingScope tradingScope)
    {
        m_tradingScope = tradingScope;
    }

    @Override
    public List<ManagedOpenTradeInformation> getManagedOpenTradeInformation()
    {
        return m_managedOpenTradeInformation;
    }

    public void setManagedOpenTradeInformation(final List<ManagedOpenTradeInformation> managedOpenTradeInformation)
    {
        m_managedOpenTradeInformation = managedOpenTradeInformation;
    }

    @Override
    public BinaryType getBinaryType()
    {
        return m_binaryType;
    }

    public void setBinaryType(BinaryType binaryType)
    {
        m_binaryType = binaryType;
    }

    @Override
    public Date getSettleTime()
    {
        return m_settleTime;
    }

    public void setSettleTime(Date settleTime)
    {
        m_settleTime = settleTime;
    }

    @Override
    public BigDecimal getStrikePrice()
    {
        return m_strikePrice;
    }

    public void setStrikePrice(BigDecimal strikePrice)
    {
        m_strikePrice = strikePrice;
    }

    @Override
    public BigDecimal getStrikePriceAdditional()
    {
        return m_strikePriceAdditional;
    }

    public void setStrikePriceAdditional(BigDecimal strikePriceAdditional)
    {
        m_strikePriceAdditional = strikePriceAdditional;
    }

    @Override
    public String getTenor()
    {
        return m_tenor;
    }

    public void setTenor(String tenor)
    {
        m_tenor = tenor;
    }

    @Override
    public Date getTenorStartTime()
    {
        return m_tenorStartTime;
    }

    public void setTenorStartTime(Date tenorStartTime)
    {
        m_tenorStartTime = tenorStartTime;
    }

    @Override
    public BigDecimal getOpeningTradeInstrumentPrice()
    {
        return m_openingTradeInstrumentPrice;
    }

    public void setOpeningTradeInstrumentPrice(BigDecimal openingTradeInstrumentPrice)
    {
        m_openingTradeInstrumentPrice = openingTradeInstrumentPrice;
    }

    @Override
    public BigDecimal getForcedMarginFxRate() {
        return this.m_forcedMarginFxRate;
    }

    public void setForcedMarginFxRate(BigDecimal forcedMarginFxRate) {
        this.m_forcedMarginFxRate = forcedMarginFxRate;
    }
    
    @Override
    public BigDecimal getOpenAccruedTurnoverInAccountCurrency() {
        return this.m_openAccruedTurnoverInAccountCurrency;
    }

    public void setOpenAccruedTurnoverInAccountCurrency(BigDecimal openAccruedTurnoverInAccountCurrency) {
        this.m_openAccruedTurnoverInAccountCurrency = openAccruedTurnoverInAccountCurrency;
    }
    
    @Override
    public Date getValueDate()
    {
        return m_valueDate;
    }  

    @Override
    public String getPairCurrency()    
    {
        return m_pairCurrency;
    }  

    @Override
    public String getPrimaryCurrency()
    {
        return m_primaryCurrency;
    }  

    @Override
    public String getSecondaryCurrency()
    {
        return m_secondaryCurrency;
    }  
    
    @Override
    public BigDecimal getPrimaryAmount()
    {
        return m_primaryAmount;
    }  

    @Override
    public BigDecimal getSecondaryAmount()
    {
        return m_secondaryAmount;
    }  

    @Override
    public BigDecimal getMarginPercentage()
    {
        return m_marginPercentage;
    }
    
    @Override
    public BigDecimal getAccruedFeesInInstrumentCurrency() {
        return m_accruedFeesInInstrumentCurrency;
    }

    @Override
    public BigDecimal getAccruedFeesInPrimaryCurrency() {
        return m_accruedFeesInPrimaryCurrency;
    }
    
    @Override
    public BigDecimal getOpeningTradeInstrumentPriceInPrimaryCurrency() {
        return m_openingTradeInstrumentPriceInPrimaryCurrency;
    }

    @Override
    public PositionTransactionType getTransactionType() {
        return m_transactionType;
    }

    @Override
    public String getOptionCode() {
        return m_optionCode;
    }

    @Override
    public BigDecimal getOpenAccruedHoldingCosts() {
        return m_openAccruedHoldingCosts;
    }

    @Override
    public String getStrategyId() {
        return m_strategyId;
    }

    @Override
    public StrategyType getStrategyType() {
        return m_strategyType;
    }


    public DbrProductCache getProductCache() {
        return m_productCache;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((m_amount == null) ? 0 : m_amount.hashCode());
        result = prime * result + ((m_amountCurrency == null) ? 0 : m_amountCurrency.hashCode());
        result = prime * result + ((m_amountInAccountCurrency == null) ? 0
            : m_amountInAccountCurrency.hashCode());
        result = prime * result + ((m_binaryType == null) ? 0 : m_binaryType.hashCode());
        result = prime * result + ((m_closePrice == null) ? 0 : m_closePrice.hashCode());
        result = prime * result +
            ((m_customInfoVirtualPortfolioCode == null) ? 0 : m_customInfoVirtualPortfolioCode.hashCode());
        result = prime * result + ((m_direction == null) ? 0 : m_direction.hashCode());
        result = prime * result + ((m_executionType == null) ? 0 : m_executionType.hashCode());
        result = prime * result + ((m_financingRatio == null) ? 0 : m_financingRatio.hashCode());
        result = prime * result + ((m_fractionalPartRatio == null) ? 0 : m_fractionalPartRatio.hashCode());
        result = prime * result + ((m_isAutomaticallyRolled == null) ? 0 : m_isAutomaticallyRolled.hashCode());
        result =
            prime * result + ((m_isCurrencyInFractionalParts == null) ? 0 : m_isCurrencyInFractionalParts.hashCode());
        result = prime * result + ((m_lastCounterpartyId == null) ? 0 : m_lastCounterpartyId.hashCode());
        result = prime * result + ((m_lastModifiedTime == null) ? 0 : m_lastModifiedTime.hashCode());
        result =
            prime * result + ((m_managedOpenTradeInformation == null) ? 0 : m_managedOpenTradeInformation.hashCode());
        result = prime * result + ((m_margin == null) ? 0 : m_margin.hashCode());
        result = prime * result + ((m_marginCurrency == null) ? 0 : m_marginCurrency.hashCode());
        result = prime * result + ((m_marginInTradingAccountPrimaryCurrency == null) ? 0
            : m_marginInTradingAccountPrimaryCurrency.hashCode());
        result = prime * result + ((m_marginSecondary == null) ? 0 : m_marginSecondary.hashCode());
        result = prime * result + ((m_marginSecondaryCurrency == null) ? 0 : m_marginSecondaryCurrency.hashCode());
        result = prime * result + ((m_marketMakerAccountId == null) ? 0 : m_marketMakerAccountId.hashCode());
        result = prime * result + ((m_marketMakerBackOfficeRef == null) ? 0 : m_marketMakerBackOfficeRef.hashCode());
        result = prime * result + ((m_marketMakerInstrumentId == null) ? 0 : m_marketMakerInstrumentId.hashCode());
        result = prime * result + ((m_marketMakerIsPrimary == null) ? 0 : m_marketMakerIsPrimary.hashCode());
        result = prime * result + ((m_marketMakerValueDate == null) ? 0 : m_marketMakerValueDate.hashCode());
        result = prime * result +
            ((m_normailisedDirectionMultiplier == null) ? 0 : m_normailisedDirectionMultiplier.hashCode());
        result =
            prime * result + ((m_normailisedMarginRequirement == null) ? 0 : m_normailisedMarginRequirement.hashCode());
        result = prime * result + ((m_normailisedMarginType == null) ? 0 : m_normailisedMarginType.hashCode());
        result = prime * result + ((m_normailisedOpenPrice == null) ? 0 : m_normailisedOpenPrice.hashCode());
        result = prime * result + ((m_normailisedOpenQuantityInTradingCurrency == null) ? 0
            : m_normailisedOpenQuantityInTradingCurrency.hashCode());
        result = prime * result + ((m_normailisedOpenValueInTradingCurrency == null) ? 0
            : m_normailisedOpenValueInTradingCurrency.hashCode());
        result = prime * result + ((m_normailisedOrderType == null) ? 0 : m_normailisedOrderType.hashCode());
        result =
            prime * result + ((m_normailisedTradingCurrency == null) ? 0 : m_normailisedTradingCurrency.hashCode());
        result = prime * result + ((m_oldStyleFx == null) ? 0 : m_oldStyleFx.hashCode());
        result = prime * result + ((m_openTime == null) ? 0 : m_openTime.hashCode());
        result = prime * result + ((m_openTradeMarginFxRate == null) ? 0 : m_openTradeMarginFxRate.hashCode());
        result = prime * result + ((m_openTradeQuantityFxRate == null) ? 0 : m_openTradeQuantityFxRate.hashCode());
        result = prime * result + ((m_openingTradeAmountFxRate == null) ? 0 : m_openingTradeAmountFxRate.hashCode());
        result = prime * result + ((m_openingTradeAppToUnits == null) ? 0 : m_openingTradeAppToUnits.hashCode());
        result = prime * result + ((m_openingTradeId == null) ? 0 : m_openingTradeId.hashCode());
        result =
            prime * result + ((m_openingTradeInstrumentPrice == null) ? 0 : m_openingTradeInstrumentPrice.hashCode());
        result = prime * result + ((m_openingTradePrice == null) ? 0 : m_openingTradePrice.hashCode());
        result = prime * result + ((m_orderId == null) ? 0 : m_orderId.hashCode());
        result = prime * result + ((m_platform == null) ? 0 : m_platform.hashCode());
        result = prime * result + ((m_priceDesignator == null) ? 0 : m_priceDesignator.hashCode());
        result = prime * result + ((m_productCurrency == null) ? 0 : m_productCurrency.hashCode());
        result =
            prime * result + ((m_productFinancingRatioMaximum == null) ? 0 : m_productFinancingRatioMaximum.hashCode());
        result = prime * result + ((m_productGeneration == null) ? 0 : m_productGeneration.hashCode());
        result = prime * result + ((m_productInstrumentCode == null) ? 0 : m_productInstrumentCode.hashCode());
        result = prime * result + ((m_productPointMultiplier == null) ? 0 : m_productPointMultiplier.hashCode());
        result = prime * result + ((m_productSchemaCode == null) ? 0 : m_productSchemaCode.hashCode());
        result = prime * result + ((m_productWrapperCode == null) ? 0 : m_productWrapperCode.hashCode());
        result = prime * result + ((m_quantity == null) ? 0 : m_quantity.hashCode());
        result = prime * result + ((m_quantityCurrency == null) ? 0 : m_quantityCurrency.hashCode());
        result = prime * result + ((m_quantityDesignator == null) ? 0 : m_quantityDesignator.hashCode());
        result = prime * result + ((m_rolledOpenTradeId == null) ? 0 : m_rolledOpenTradeId.hashCode());
        result = prime * result + ((m_settleTime == null) ? 0 : m_settleTime.hashCode());
        result = prime * result + ((m_snapshotTime == null) ? 0 : m_snapshotTime.hashCode());
        result = prime * result + ((m_sourcePlatformAccountId == null) ? 0 : m_sourcePlatformAccountId.hashCode());
        result =
            prime * result + ((m_sourcePlatformInstrumentId == null) ? 0 : m_sourcePlatformInstrumentId.hashCode());
        result = prime * result + ((m_strikePrice == null) ? 0 : m_strikePrice.hashCode());
        result = prime * result + ((m_strikePriceAdditional == null) ? 0 : m_strikePriceAdditional.hashCode());
        result = prime * result + ((m_tenor == null) ? 0 : m_tenor.hashCode());
        result = prime * result + ((m_tenorStartTime == null) ? 0 : m_tenorStartTime.hashCode());
        result = prime * result + ((m_tradingAccountCode == null) ? 0 : m_tradingAccountCode.hashCode());
        result = prime * result + ((m_tradingAccountCodifier == null) ? 0 : m_tradingAccountCodifier.hashCode());
        result = prime * result + ((m_tradingAccountFunction == null) ? 0 : m_tradingAccountFunction.hashCode());
        result = prime * result +
            ((m_tradingAccountCurrency == null) ? 0 : m_tradingAccountCurrency.hashCode());
        result = prime * result + ((m_tradingAccountType == null) ? 0 : m_tradingAccountType.hashCode());
        result = prime * result + ((m_tradingScope == null) ? 0 : m_tradingScope.hashCode());
        result = prime * result + ((m_forcedMarginFxRate == null) ? 0 : m_forcedMarginFxRate.hashCode());
        result = prime * result + ((m_openAccruedTurnoverInAccountCurrency == null) ? 0 : m_openAccruedTurnoverInAccountCurrency.hashCode());   
        result = prime * result + ((m_valueDate == null) ? 0 : m_valueDate.hashCode());
        result = prime * result + ((m_pairCurrency == null) ? 0 : m_pairCurrency.hashCode());
        result = prime * result + ((m_primaryCurrency == null) ? 0 : m_primaryCurrency.hashCode());
        result = prime * result + ((m_secondaryCurrency == null) ? 0 : m_secondaryCurrency.hashCode());
        result = prime * result + ((m_primaryAmount == null) ? 0 : m_primaryAmount.hashCode());
        result = prime * result + ((m_secondaryAmount == null) ? 0 : m_secondaryAmount.hashCode());
        result = prime * result + ((m_marginPercentage == null) ? 0 : m_marginPercentage.hashCode());
        result = prime * result + ((m_accruedFeesInInstrumentCurrency == null) ? 0 : m_accruedFeesInInstrumentCurrency.hashCode());
        result = prime * result + ((m_accruedFeesInPrimaryCurrency == null) ? 0 : m_accruedFeesInPrimaryCurrency.hashCode());
        result = prime * result + ((m_openingTradeInstrumentPriceInPrimaryCurrency == null) ? 0 : m_openingTradeInstrumentPriceInPrimaryCurrency.hashCode());
        result = prime * result + ((m_transactionType == null) ? 0 : m_transactionType.hashCode());
        result = prime * result + ((m_optionCode == null) ? 0 : m_optionCode.hashCode());
        result = prime * result + ((m_openAccruedHoldingCosts == null) ? 0 : m_openAccruedHoldingCosts.hashCode());
        result = prime * result + ((m_strategyId == null) ? 0 : m_strategyId.hashCode());
        result = prime * result + ((m_strategyType == null) ? 0 : m_strategyType.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj)
    {
        if(this == obj)
            return true;
        if(!super.equals(obj))
            return false;
        if(getClass() != obj.getClass())
            return false;
        AbstractOpenTradeEntity other = (AbstractOpenTradeEntity) obj;
        if(m_amount == null)
        {
            if(other.m_amount != null)
                return false;
        }
        else if(!m_amount.equals(other.m_amount))
            return false;
        if(m_amountCurrency == null)
        {
            if(other.m_amountCurrency != null)
                return false;
        }
        else if(!m_amountCurrency.equals(other.m_amountCurrency))
            return false;
        if(m_amountInAccountCurrency == null)
        {
            if(other.m_amountInAccountCurrency != null)
                return false;
        }
        else if(!m_amountInAccountCurrency.equals(other.m_amountInAccountCurrency))
            return false;
        if(m_binaryType != other.m_binaryType)
            return false;
        if(m_closePrice == null)
        {
            if(other.m_closePrice != null)
                return false;
        }
        else if(!m_closePrice.equals(other.m_closePrice))
            return false;
        if(m_customInfoVirtualPortfolioCode == null)
        {
            if(other.m_customInfoVirtualPortfolioCode != null)
                return false;
        }
        else if(!m_customInfoVirtualPortfolioCode.equals(other.m_customInfoVirtualPortfolioCode))
            return false;
        if(m_direction == null)
        {
            if(other.m_direction != null)
                return false;
        }
        else if(!m_direction.equals(other.m_direction))
            return false;
        if(m_executionType != other.m_executionType)
            return false;
        if(m_financingRatio == null)
        {
            if(other.m_financingRatio != null)
                return false;
        }
        else if(!m_financingRatio.equals(other.m_financingRatio))
            return false;
        if(m_fractionalPartRatio == null)
        {
            if(other.m_fractionalPartRatio != null)
                return false;
        }
        else if(!m_fractionalPartRatio.equals(other.m_fractionalPartRatio))
            return false;
        if(m_isAutomaticallyRolled == null)
        {
            if(other.m_isAutomaticallyRolled != null)
                return false;
        }
        else if(!m_isAutomaticallyRolled.equals(other.m_isAutomaticallyRolled))
            return false;
        if(m_isCurrencyInFractionalParts == null)
        {
            if(other.m_isCurrencyInFractionalParts != null)
                return false;
        }
        else if(!m_isCurrencyInFractionalParts.equals(other.m_isCurrencyInFractionalParts))
            return false;
        if(m_lastCounterpartyId == null)
        {
            if(other.m_lastCounterpartyId != null)
                return false;
        }
        else if(!m_lastCounterpartyId.equals(other.m_lastCounterpartyId))
            return false;
        if(m_lastModifiedTime == null)
        {
            if(other.m_lastModifiedTime != null)
                return false;
        }
        else if(!m_lastModifiedTime.equals(other.m_lastModifiedTime))
            return false;
        if(m_managedOpenTradeInformation == null)
        {
            if(other.m_managedOpenTradeInformation != null)
                return false;
        }
        else if(!m_managedOpenTradeInformation.equals(other.m_managedOpenTradeInformation))
            return false;
        if(m_margin == null)
        {
            if(other.m_margin != null)
                return false;
        }
        else if(!m_margin.equals(other.m_margin))
            return false;
        if(m_marginCurrency == null)
        {
            if(other.m_marginCurrency != null)
                return false;
        }
        else if(!m_marginCurrency.equals(other.m_marginCurrency))
            return false;
        if(m_marginInTradingAccountPrimaryCurrency == null)
        {
            if(other.m_marginInTradingAccountPrimaryCurrency != null)
                return false;
        }
        else if(!m_marginInTradingAccountPrimaryCurrency.equals(other.m_marginInTradingAccountPrimaryCurrency))
            return false;
        if(m_marginSecondary == null)
        {
            if(other.m_marginSecondary != null)
                return false;
        }
        else if(!m_marginSecondary.equals(other.m_marginSecondary))
            return false;
        if(m_marginSecondaryCurrency == null)
        {
            if(other.m_marginSecondaryCurrency != null)
                return false;
        }
        else if(!m_marginSecondaryCurrency.equals(other.m_marginSecondaryCurrency))
            return false;
        if(m_marketMakerAccountId == null)
        {
            if(other.m_marketMakerAccountId != null)
                return false;
        }
        else if(!m_marketMakerAccountId.equals(other.m_marketMakerAccountId))
            return false;
        if(m_marketMakerBackOfficeRef == null)
        {
            if(other.m_marketMakerBackOfficeRef != null)
                return false;
        }
        else if(!m_marketMakerBackOfficeRef.equals(other.m_marketMakerBackOfficeRef))
            return false;
        if(m_marketMakerInstrumentId == null)
        {
            if(other.m_marketMakerInstrumentId != null)
                return false;
        }
        else if(!m_marketMakerInstrumentId.equals(other.m_marketMakerInstrumentId))
            return false;
        if(m_marketMakerIsPrimary == null)
        {
            if(other.m_marketMakerIsPrimary != null)
                return false;
        }
        else if(!m_marketMakerIsPrimary.equals(other.m_marketMakerIsPrimary))
            return false;
        if(m_marketMakerValueDate == null)
        {
            if(other.m_marketMakerValueDate != null)
                return false;
        }
        else if(!m_marketMakerValueDate.equals(other.m_marketMakerValueDate))
            return false;
        if(m_normailisedDirectionMultiplier == null)
        {
            if(other.m_normailisedDirectionMultiplier != null)
                return false;
        }
        else if(!m_normailisedDirectionMultiplier.equals(other.m_normailisedDirectionMultiplier))
            return false;
        if(m_normailisedMarginRequirement == null)
        {
            if(other.m_normailisedMarginRequirement != null)
                return false;
        }
        else if(!m_normailisedMarginRequirement.equals(other.m_normailisedMarginRequirement))
            return false;
        if(m_normailisedMarginType == null)
        {
            if(other.m_normailisedMarginType != null)
                return false;
        }
        else if(!m_normailisedMarginType.equals(other.m_normailisedMarginType))
            return false;
        if(m_normailisedOpenPrice == null)
        {
            if(other.m_normailisedOpenPrice != null)
                return false;
        }
        else if(!m_normailisedOpenPrice.equals(other.m_normailisedOpenPrice))
            return false;
        if(m_normailisedOpenQuantityInTradingCurrency == null)
        {
            if(other.m_normailisedOpenQuantityInTradingCurrency != null)
                return false;
        }
        else if(!m_normailisedOpenQuantityInTradingCurrency.equals(other.m_normailisedOpenQuantityInTradingCurrency))
            return false;
        if(m_normailisedOpenValueInTradingCurrency == null)
        {
            if(other.m_normailisedOpenValueInTradingCurrency != null)
                return false;
        }
        else if(!m_normailisedOpenValueInTradingCurrency.equals(other.m_normailisedOpenValueInTradingCurrency))
            return false;
        if(m_normailisedOrderType == null)
        {
            if(other.m_normailisedOrderType != null)
                return false;
        }
        else if(!m_normailisedOrderType.equals(other.m_normailisedOrderType))
            return false;
        if(m_normailisedTradingCurrency == null)
        {
            if(other.m_normailisedTradingCurrency != null)
                return false;
        }
        else if(!m_normailisedTradingCurrency.equals(other.m_normailisedTradingCurrency))
            return false;
        if(m_oldStyleFx == null)
        {
            if(other.m_oldStyleFx != null)
                return false;
        }
        else if(!m_oldStyleFx.equals(other.m_oldStyleFx))
            return false;
        if(m_openTime == null)
        {
            if(other.m_openTime != null)
                return false;
        }
        else if(!m_openTime.equals(other.m_openTime))
            return false;
        if(m_openTradeMarginFxRate == null)
        {
            if(other.m_openTradeMarginFxRate != null)
                return false;
        }
        else if(!m_openTradeMarginFxRate.equals(other.m_openTradeMarginFxRate))
            return false;
        if(m_openTradeQuantityFxRate == null)
        {
            if(other.m_openTradeQuantityFxRate != null)
                return false;
        }
        else if(!m_openTradeQuantityFxRate.equals(other.m_openTradeQuantityFxRate))
            return false;
        if(m_openingTradeAmountFxRate == null)
        {
            if(other.m_openingTradeAmountFxRate != null)
                return false;
        }
        else if(!m_openingTradeAmountFxRate.equals(other.m_openingTradeAmountFxRate))
            return false;
        if(m_openingTradeAppToUnits == null)
        {
            if(other.m_openingTradeAppToUnits != null)
                return false;
        }
        else if(!m_openingTradeAppToUnits.equals(other.m_openingTradeAppToUnits))
            return false;
        if(m_openingTradeId == null)
        {
            if(other.m_openingTradeId != null)
                return false;
        }
        else if(!m_openingTradeId.equals(other.m_openingTradeId))
            return false;
        if(m_openingTradeInstrumentPrice == null)
        {
            if(other.m_openingTradeInstrumentPrice != null)
                return false;
        }
        else if(!m_openingTradeInstrumentPrice.equals(other.m_openingTradeInstrumentPrice))
            return false;
        if(m_openingTradePrice == null)
        {
            if(other.m_openingTradePrice != null)
                return false;
        }
        else if(!m_openingTradePrice.equals(other.m_openingTradePrice))
            return false;
        if(m_orderId == null)
        {
            if(other.m_orderId != null)
                return false;
        }
        else if(!m_orderId.equals(other.m_orderId))
            return false;
        if(m_platform != other.m_platform)
            return false;
        if(m_priceDesignator == null)
        {
            if(other.m_priceDesignator != null)
                return false;
        }
        else if(!m_priceDesignator.equals(other.m_priceDesignator))
            return false;
        if(m_productCurrency == null)
        {
            if(other.m_productCurrency != null)
                return false;
        }
        else if(!m_productCurrency.equals(other.m_productCurrency))
            return false;
        if(m_productFinancingRatioMaximum == null)
        {
            if(other.m_productFinancingRatioMaximum != null)
                return false;
        }
        else if(!m_productFinancingRatioMaximum.equals(other.m_productFinancingRatioMaximum))
            return false;
        if(m_productGeneration == null)
        {
            if(other.m_productGeneration != null)
                return false;
        }
        else if(!m_productGeneration.equals(other.m_productGeneration))
            return false;
        if(m_productInstrumentCode == null)
        {
            if(other.m_productInstrumentCode != null)
                return false;
        }
        else if(!m_productInstrumentCode.equals(other.m_productInstrumentCode))
            return false;
        if(m_productPointMultiplier == null)
        {
            if(other.m_productPointMultiplier != null)
                return false;
        }
        else if(!m_productPointMultiplier.equals(other.m_productPointMultiplier))
            return false;
        if(m_productSchemaCode == null)
        {
            if(other.m_productSchemaCode != null)
                return false;
        }
        else if(!m_productSchemaCode.equals(other.m_productSchemaCode))
            return false;
        if(m_productWrapperCode == null)
        {
            if(other.m_productWrapperCode != null)
                return false;
        }
        else if(!m_productWrapperCode.equals(other.m_productWrapperCode))
            return false;
        if(m_quantity == null)
        {
            if(other.m_quantity != null)
                return false;
        }
        else if(!m_quantity.equals(other.m_quantity))
            return false;
        if(m_quantityCurrency == null)
        {
            if(other.m_quantityCurrency != null)
                return false;
        }
        else if(!m_quantityCurrency.equals(other.m_quantityCurrency))
            return false;
        if(m_quantityDesignator == null)
        {
            if(other.m_quantityDesignator != null)
                return false;
        }
        else if(!m_quantityDesignator.equals(other.m_quantityDesignator))
            return false;
        if(m_rolledOpenTradeId == null)
        {
            if(other.m_rolledOpenTradeId != null)
                return false;
        }
        else if(!m_rolledOpenTradeId.equals(other.m_rolledOpenTradeId))
            return false;
        if(m_settleTime == null)
        {
            if(other.m_settleTime != null)
                return false;
        }
        else if(!m_settleTime.equals(other.m_settleTime))
            return false;
        if(m_snapshotTime == null)
        {
            if(other.m_snapshotTime != null)
                return false;
        }
        else if(!m_snapshotTime.equals(other.m_snapshotTime))
            return false;
        if(m_sourcePlatformAccountId == null)
        {
            if(other.m_sourcePlatformAccountId != null)
                return false;
        }
        else if(!m_sourcePlatformAccountId.equals(other.m_sourcePlatformAccountId))
            return false;
        if(m_sourcePlatformInstrumentId == null)
        {
            if(other.m_sourcePlatformInstrumentId != null)
                return false;
        }
        else if(!m_sourcePlatformInstrumentId.equals(other.m_sourcePlatformInstrumentId))
            return false;
        if(m_strikePrice == null)
        {
            if(other.m_strikePrice != null)
                return false;
        }
        else if(!m_strikePrice.equals(other.m_strikePrice))
            return false;
        if(m_strikePriceAdditional == null)
        {
            if(other.m_strikePriceAdditional != null)
                return false;
        }
        else if(!m_strikePriceAdditional.equals(other.m_strikePriceAdditional))
            return false;
        if(m_tenor == null)
        {
            if(other.m_tenor != null)
                return false;
        }
        else if(!m_tenor.equals(other.m_tenor))
            return false;
        if(m_tenorStartTime == null)
        {
            if(other.m_tenorStartTime != null)
                return false;
        }
        else if(!m_tenorStartTime.equals(other.m_tenorStartTime))
            return false;
        if(m_tradingAccountCode == null)
        {
            if(other.m_tradingAccountCode != null)
                return false;
        }
        else if(!m_tradingAccountCode.equals(other.m_tradingAccountCode))
            return false;
        if(m_tradingAccountCodifier == null)
        {
            if(other.m_tradingAccountCodifier != null)
                return false;
        }
        else if(!m_tradingAccountCodifier.equals(other.m_tradingAccountCodifier))
            return false;
        if(m_tradingAccountFunction == null)
        {
            if(other.m_tradingAccountFunction != null)
                return false;
        }
        else if(!m_tradingAccountFunction.equals(other.m_tradingAccountFunction))
            return false;
        if(m_tradingAccountCurrency == null)
        {
            if(other.m_tradingAccountCurrency != null)
                return false;
        }
        else if(!m_tradingAccountCurrency.equals(other.m_tradingAccountCurrency))
            return false;
        if(m_tradingAccountType == null)
        {
            if(other.m_tradingAccountType != null)
                return false;
        }
        else if(!m_tradingAccountType.equals(other.m_tradingAccountType))
            return false;
        if(m_tradingScope != other.m_tradingScope)
            return false;

        if(m_forcedMarginFxRate == null)
        {
            if(other.m_forcedMarginFxRate != null)
                return false;
        }
        else if(!m_forcedMarginFxRate.equals(other.m_forcedMarginFxRate))
            return false;
        
        if(m_openAccruedTurnoverInAccountCurrency == null)
        {
            if(other.m_openAccruedTurnoverInAccountCurrency != null)
                return false;
        }
        else if(!m_openAccruedTurnoverInAccountCurrency.equals(other.m_openAccruedTurnoverInAccountCurrency))
            return false;
        if(m_valueDate == null)
        {
            if(other.m_valueDate != null)
                return false;
        }
        else if(!m_valueDate.equals(other.m_valueDate))
            return false;
        if(m_pairCurrency == null)
        {
            if(other.m_pairCurrency != null)
                return false;
        }
        else if(!m_pairCurrency.equals(other.m_pairCurrency))
            return false;
        if(m_primaryCurrency == null)
        {
            if(other.m_primaryCurrency != null)
                return false;
        }
        else if(!m_primaryCurrency.equals(other.m_primaryCurrency))
            return false;
        if(m_secondaryCurrency == null)
        {
            if(other.m_secondaryCurrency != null)
                return false;
        }
        else if(!m_secondaryCurrency.equals(other.m_secondaryCurrency))
            return false;
        if(m_primaryAmount == null)
        {
            if(other.m_primaryAmount != null)
                return false;
        }
        else if(!m_primaryAmount.equals(other.m_primaryAmount))
            return false;
        if(m_secondaryAmount == null)
        {
            if(other.m_secondaryAmount != null)
                return false;
        }
        else if(!m_secondaryAmount.equals(other.m_secondaryAmount))
            return false;
        
        if(m_marginPercentage == null)
        {
            if(other.m_marginPercentage != null)
                return false;
        }
        else if(!m_marginPercentage.equals(other.m_marginPercentage))
            return false;
        
        if(m_accruedFeesInInstrumentCurrency == null)
        {
            if(other.m_accruedFeesInInstrumentCurrency != null)
                return false;
        }
        else if(!m_accruedFeesInInstrumentCurrency.equals(other.m_accruedFeesInInstrumentCurrency))
            return false;
        
        if(m_accruedFeesInPrimaryCurrency == null)
        {
            if(other.m_accruedFeesInPrimaryCurrency != null)
                return false;
        }
        else if(!m_accruedFeesInPrimaryCurrency.equals(other.m_accruedFeesInPrimaryCurrency))
            return false;
        if(m_openingTradeInstrumentPriceInPrimaryCurrency == null)
        {
            if(other.m_openingTradeInstrumentPriceInPrimaryCurrency != null)
                return false;
        }
        else if(!m_openingTradeInstrumentPriceInPrimaryCurrency.equals(other.m_openingTradeInstrumentPriceInPrimaryCurrency))
            return false;
        if(m_transactionType == null)
        {
            if(other.m_transactionType != null)
                return false;
        }
        else if(!m_transactionType.equals(other.m_transactionType))
            return false;
        if(m_optionCode == null)
        {
            if(other.m_optionCode != null)
                return false;
        }
        else if(!m_optionCode.equals(other.m_optionCode))
            return false;

        if(m_openAccruedHoldingCosts == null)
        {
            if(other.m_openAccruedHoldingCosts != null)
                return false;
        }
        else if(!m_openAccruedHoldingCosts.equals(other.m_openAccruedHoldingCosts))
            return false;

        if(m_strategyId == null)
        {
            if(other.m_strategyId != null)
                return false;
        }
        else if(!m_strategyId.equals(other.m_strategyId))
            return false;
        if(m_strategyType == null)
        {
            if(other.m_strategyType != null)
                return false;
        }
        else if(!m_strategyType.equals(other.m_strategyType))
            return false;

        return true;
    }

    @Override
    public String toString()
    {
        return "AbstractOpenTradeEntity [m_amount=" + m_amount + ", m_amountCurrency=" + m_amountCurrency +
            ", m_amountInTradingAccountPrimaryCurrency=" + m_amountInAccountCurrency + ", m_closePrice=" +
            m_closePrice + ", m_customInfoVirtualPortfolioCode=" + m_customInfoVirtualPortfolioCode + ", m_direction=" +
            m_direction + ", m_financingRatio=" + m_financingRatio + ", m_fractionalPartRatio=" +
            m_fractionalPartRatio + ", m_isCurrencyInFractionalParts=" + m_isCurrencyInFractionalParts +
            ", m_lastModifiedTime=" + m_lastModifiedTime + ", m_margin=" + m_margin + ", m_marginCurrency=" +
            m_marginCurrency + ", m_marginSecondary=" + m_marginSecondary + ", m_marginSecondaryCurrency=" +
            m_marginSecondaryCurrency + ", m_marginInTradingAccountPrimaryCurrency=" +
            m_marginInTradingAccountPrimaryCurrency + ", m_marketMakerBackOfficeRef=" + m_marketMakerBackOfficeRef +
            ", m_marketMakerAccountId=" + m_marketMakerAccountId + ", m_marketMakerInstrumentId=" +
            m_marketMakerInstrumentId + ", m_marketMakerIsPrimary=" + m_marketMakerIsPrimary +
            ", m_marketMakerValueDate=" + m_marketMakerValueDate + ", m_normailisedDirectionMultiplier=" +
            m_normailisedDirectionMultiplier + ", m_normailisedMarginRequirement=" + m_normailisedMarginRequirement +
            ", m_normailisedMarginType=" + m_normailisedMarginType + ", m_normailisedOpenQuantityInTradingCurrency=" +
            m_normailisedOpenQuantityInTradingCurrency + ", m_normailisedOpenValueInTradingCurrency=" +
            m_normailisedOpenValueInTradingCurrency + ", m_normailisedTradingCurrency=" + m_normailisedTradingCurrency +
            ", m_normailisedOrderType=" + m_normailisedOrderType + ", m_normailisedOpenPrice=" +
            m_normailisedOpenPrice + ", m_openTime=" + m_openTime + ", m_openingTradeId=" + m_openingTradeId +
            ", m_openTradeMarginFxRate=" + m_openTradeMarginFxRate + ", m_openingTradePrice=" + m_openingTradePrice +
            ", m_openTradeQuantityFxRate=" + m_openTradeQuantityFxRate + ", m_orderId=" + m_orderId + ", m_platform=" +
            m_platform + ", m_priceDesignator=" + m_priceDesignator + ", m_productCurrency=" + m_productCurrency +
            ", m_productFinancingRatioMaximum=" + m_productFinancingRatioMaximum + ", m_productGeneration=" +
            m_productGeneration + ", m_productInstrumentCode=" + m_productInstrumentCode +
            ", m_productPointMultiplier=" + m_productPointMultiplier + ", m_productSchemaCode=" + m_productSchemaCode +
            ", m_productWrapperCode=" + m_productWrapperCode + ", m_quantity=" + m_quantity + ", m_quantityCurrency=" +
            m_quantityCurrency + ", m_quantityDesignator=" + m_quantityDesignator + ", m_snapshotTime=" +
            m_snapshotTime + ", m_sourcePlatformAccountId=" + m_sourcePlatformAccountId +
            ", m_sourcePlatformInstrumentId=" + m_sourcePlatformInstrumentId + ", m_tradingAccountCode=" +
            m_tradingAccountCode + ", m_tradingAccountCodifier=" + m_tradingAccountCodifier +
            ", m_tradingAccountFunction=" + m_tradingAccountFunction + ", m_tradingAccountPrimaryCurrency=" +
            m_tradingAccountCurrency + ", m_tradingAccountType=" + m_tradingAccountType +
            ", m_lastCounterpartyId=" + m_lastCounterpartyId + ", m_openingTradeAmountFxRate=" +
            m_openingTradeAmountFxRate + ", m_openingTradeAppToUnits=" + m_openingTradeAppToUnits +
            ", m_isAutomaticallyRolled=" + m_isAutomaticallyRolled + ", m_rolledOpenTradeId=" + m_rolledOpenTradeId +
            ", m_oldStyleFx=" + m_oldStyleFx + ", m_executionType=" + m_executionType + ", m_tradingScope=" +
            m_tradingScope + ", m_managedOpenTradeInformation=" + m_managedOpenTradeInformation + ", m_binaryType=" +
            m_binaryType + ", m_settleTime=" + m_settleTime + ", m_strikePrice=" + m_strikePrice +
            ", m_strikePriceAdditional=" + m_strikePriceAdditional + ", m_tenor=" + m_tenor + ", m_tenorStartTime=" +
            m_tenorStartTime + ", m_openingTradeInstrumentPrice=" + m_openingTradeInstrumentPrice +
            ", m_forcedMarginFxRate=" + m_forcedMarginFxRate +
            ", m_openAccruedTurnoverInAccountCurrency=" + m_openAccruedTurnoverInAccountCurrency
            + ", m_valueDate=" + m_valueDate 
            + ", m_pairCurrency=" + m_pairCurrency 
            + ", m_primaryCurrency=" + m_primaryCurrency 
            + ", m_secondaryCurrency=" + m_secondaryCurrency 
            + ", m_primaryAmount=" + m_primaryAmount 
            + ", m_secondaryAmount=" + m_secondaryAmount
            + ", m_marginPercentage=" + m_marginPercentage
            + ", m_accruedFeesInInstrumentCurrency=" + m_accruedFeesInInstrumentCurrency
            + ", m_accruedFeesInPrimaryCurrency=" + m_accruedFeesInPrimaryCurrency
            + ", m_openingTradeInstrumentPriceInPrimaryCurrency=" + m_openingTradeInstrumentPriceInPrimaryCurrency
            + ", m_transactionType=" + m_transactionType
            + ", m_optionCode=" + m_optionCode
            + ", m_openAccruedHoldingCosts=" + m_openAccruedHoldingCosts
            + ", m_strategyId=" + m_strategyId
            + ", m_strategyType=" + m_strategyType
            + "]";
    }

}
