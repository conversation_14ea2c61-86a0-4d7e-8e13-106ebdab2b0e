package com.cmcmarkets.nrg.api.model.mapper;

import java.util.LinkedList;
import java.util.List;

import org.modelmapper.PropertyMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.cashaccount.api.model.AmountCalculationMode;
import com.cmcmarkets.cashaccount.api.model.CarryingCostsCustomDataDto;
import com.cmcmarkets.cashaccount.api.model.DefaultSettlementOpenTradeDto;
import com.cmcmarkets.cashaccount.api.model.Directions;
import com.cmcmarkets.cashaccount.api.model.QuantityDesignator;
import com.cmcmarkets.cashaccount.api.model.SettlementOpenTradeDto;
import com.cmcmarkets.nrg.api.model.CarryingCosts;
import com.cmcmarkets.nrg.api.model.CarryingCostsOffset;
import com.cmcmarkets.nrg.api.model.CashTransactionPeriodicSettlement;
import com.cmcmarkets.nrg.api.model.impl.PeriodicSettlements;

public class CarryingCostsMapper implements Mapper<CarryingCostsCustomDataDto, PeriodicSettlements> {

    private static final Logger m_logger = LoggerFactory.getLogger(CarryingCostsMapper.class);
    private static CarryingCostsMapper mInstance;

    private ObjectMapper modelMapper = new ObjectMapper();
    private ObjectMapper carryingCostMapper = new ObjectMapper();

    private CarryingCostsMapper(String platform) {
        org.modelmapper.Converter<QuantityDesignator, String> quanityDesignatorConvertor = ctx -> ctx.getSource() != null ? ctx.getSource().name() : null;
        org.modelmapper.Converter<Directions, String> directionsConvertor = ctx -> ctx.getSource() != null ? ctx.getSource().name() : null;
        org.modelmapper.Converter<AmountCalculationMode, String> amountCalculationModeConvertor = ctx -> ctx.getSource() != null ? ctx.getSource().name() : null;

        PropertyMap<SettlementOpenTradeDto, CashTransactionPeriodicSettlement> fxRevalRateMapping = new PropertyMap <SettlementOpenTradeDto, CashTransactionPeriodicSettlement>() {
            protected void configure() {
                map().setOrderPlatform(platform);
                map().setOpeningTradePrice(source.getEvaluationPrice());
                map().setOpenTradeAmount(source.getEvaluationOpenTradeAmount());
                map().setOpenTradeAmountInTradingAccountPrimaryCurrency(source.getOBSOLETEOpenTradeAmountInAccountCurrency());
                map().setProductInstrumentCode(source.getInstrumentCode());
                map().setCarryingCostsFxRevalRate(source.getFxRevalRate());
                map().setCarryingCostsInTradingAccountPrimaryCurrency(source.getCarryingCostsInAccountCurrency());
                map().setOrderCarryingCosts(source.getCarryingCosts());
                map().setOrderCarryingCostRate(source.getCarryingCostRate());
                map().setOrderCurrency(source.getOpenTradeAmountCurrency());
            }
        };
        modelMapper.addMappings(fxRevalRateMapping).include(DefaultSettlementOpenTradeDto.class, CashTransactionPeriodicSettlement.class);
        modelMapper.addConverter(quanityDesignatorConvertor);
        modelMapper.addConverter(directionsConvertor);
        carryingCostMapper.addConverter(amountCalculationModeConvertor);
    }

    public static CarryingCostsMapper getInstance(String platform) {
        if (mInstance == null) {
            synchronized (CarryingCostsMapper.class) {
                if (mInstance == null) {
                    mInstance = new CarryingCostsMapper(platform);
                }
            }
        }
        return mInstance;
    }

    @Override
    public PeriodicSettlements map(CarryingCostsCustomDataDto carryingCostCustomDto) {
        List<CashTransactionPeriodicSettlement> cashTransactionPeriodicSettlementList = null;
        if(carryingCostCustomDto != null)
        {
            m_logger.debug("Processing Carrying Costs Custom Data");
            cashTransactionPeriodicSettlementList = new LinkedList<>();
            List<SettlementOpenTradeDto> settlementOpenTrades = carryingCostCustomDto.getSettlementOpenTrades();
            if(settlementOpenTrades != null)
            {
                for(SettlementOpenTradeDto openTrade : settlementOpenTrades) {
                    if(openTrade != null)
                    {
                        CashTransactionPeriodicSettlement periodicSettlement = modelMapper.map(openTrade, CashTransactionPeriodicSettlement.class);
                        periodicSettlement.setTradingAccountPrimaryCurrency(carryingCostCustomDto.getTradingAccountCurrency());
                        periodicSettlement.setProductWrapperCode(carryingCostCustomDto.getProductWrapperCode());
                        periodicSettlement.setSettlementTime(carryingCostCustomDto.getSettlementTime());
                        cashTransactionPeriodicSettlementList.add(periodicSettlement);
                    }
                }
            }        

	        return new PeriodicSettlements(
	                cashTransactionPeriodicSettlementList,
	                null,
	                new CarryingCostsOffset(createCarryingCostsCustomData(carryingCostCustomDto)));
    	}        
        return null;
    }

    private CarryingCosts createCarryingCostsCustomData(CarryingCostsCustomDataDto carryingCostCustomDto) {
        CarryingCosts customData = null;
        if(carryingCostCustomDto != null)
        {
            customData = carryingCostMapper.map(carryingCostCustomDto, CarryingCosts.class);
        }
        return customData;
    }
}
