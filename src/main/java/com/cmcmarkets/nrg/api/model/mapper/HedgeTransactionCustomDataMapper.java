package com.cmcmarkets.nrg.api.model.mapper;

import com.cmcmarkets.cashaccount.api.model.DefaultHedgeTransactionCustomDataDto;
import com.cmcmarkets.cashaccount.api.model.HedgeTransactionCustomDataDto;
import com.cmcmarkets.nrg.api.model.HedgeTransaction;
import org.modelmapper.PropertyMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HedgeTransactionCustomDataMapper implements Mapper<HedgeTransactionCustomDataDto, HedgeTransaction> {

    private static final Logger m_logger = LoggerFactory.getLogger(HedgeTransactionCustomDataMapper.class);

    private ObjectMapper mapper = new ObjectMapper();

    private HedgeTransactionCustomDataMapper() {
        PropertyMap<HedgeTransactionCustomDataDto, HedgeTransaction> fxRevalRateMapping = new PropertyMap <HedgeTransactionCustomDataDto, HedgeTransaction>() {
            protected void configure() {
                map().getFxRevalRate().setValue(source.getOBSOLETEFxRevalRate().getAmount());
            }
        };
        mapper.addMappings(fxRevalRateMapping).include(DefaultHedgeTransactionCustomDataDto.class, HedgeTransaction.class);
    }

    public static HedgeTransactionCustomDataMapper getInstance() {
        return SingletonHolder.INSTANCE;
    }

    @Override
    public HedgeTransaction map(HedgeTransactionCustomDataDto t) {
        HedgeTransaction customData = null;
        if (t != null) {
            m_logger.debug("Process Hedge Transaction Custom Data");
            customData = mapper.map(t, HedgeTransaction.class);
        }
        return customData;
    }

    private static class SingletonHolder {
        private static final HedgeTransactionCustomDataMapper INSTANCE = new HedgeTransactionCustomDataMapper();
    }
}
