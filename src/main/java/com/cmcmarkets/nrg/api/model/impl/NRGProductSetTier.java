/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model.impl;

import java.util.List;

import com.cmcmarkets.nrg.api.model.AbstractProductSetTierEntity;
import com.cmcmarkets.productmaster.api.model.Tier;
import com.cmcmarkets.productmaster.api.model.TierEx;

public class NRGProductSetTier extends AbstractProductSetTierEntity
{
    public void setProductSettingCode(String productSettingCode)
    {
        m_productSettingCode = productSettingCode;
    }

    public void setInstrumentSchemaCode(String instrumentSchemaCode)
    {
        m_instrumentSchemaCode = instrumentSchemaCode;
    }

    public void setCurrency(String currency)
    {
        m_currency = currency;
    }

    public void setTiers(List<Tier> tiers)
    {
        m_tiers = tiers;
    }

    public void setTiersEx(List<TierEx> tiers)
    {
        m_tiersEx = tiers;
    }
}
