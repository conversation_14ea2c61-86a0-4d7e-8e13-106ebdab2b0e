/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model.impl;

import java.util.Date;

import com.cmcmarkets.framework.messaging.common.model.TriState;
import com.cmcmarkets.framework.messaging.common.model.Version;
import com.cmcmarkets.nrg.api.model.AbstractFinanceGeneralLedgerMappingEntity;

public final class NRGFinanceGeneralLedgerMappingEntity extends AbstractFinanceGeneralLedgerMappingEntity
{

    public NRGFinanceGeneralLedgerMappingEntity(Long     id,
											    Version  version,
											    Long     accountNumber,
											    String   accountType,
											    String   accountCode,
											    String   accountCategory,
											    String   cashAccountingSchemaCode,
											    TriState isSegregated,
											    String   primaryLegalEntity,
											    String   primaryAccount,
											    String   primaryDepartment,
											    String   primaryProfitCentre,
											    String   primaryAffiliate,
											    String   b2BBSLegalEntity,
											    String   b2BBSAccount,
											    String   b2BBSDepartment,
											    String   b2BBSProfitCentre,
											    String   b2BBSAffiliate,
											    String   b2BPnLLegalEntity,
											    String   b2BPnLAccount,
											    String   b2BPnLDepartment,
											    String   b2BPnLProfitCentre,
											    String   b2BPnLAffiliate,
											    String   retainedEarningsAccount,
											    TriState trialBalanceProfitCentreSplit,
											    Date     effectiveStartTimestamp)
    { 
        super(effectiveStartTimestamp);

        		m_id = id;
                m_version = version;
                m_accountNumber = accountNumber;
                m_accountType = accountType;
                m_accountCode = accountCode;
                m_accountCategory = accountCategory;
                m_cashAccountingSchemaCode = cashAccountingSchemaCode;
                m_isSegregated = isSegregated;
                m_primaryLegalEntity = primaryLegalEntity;
                m_primaryAccount = primaryAccount;
                m_primaryDepartment = primaryDepartment;
                m_primaryProfitCentre = primaryProfitCentre;
                m_primaryAffiliate = primaryAffiliate;
                m_b2BBSLegalEntity = b2BBSLegalEntity;
                m_b2BBSAccount = b2BBSAccount;
                m_b2BBSDepartment = b2BBSDepartment;
                m_b2BBSProfitCentre = b2BBSProfitCentre;
                m_b2BBSAffiliate = b2BBSAffiliate;
                m_b2BPnLLegalEntity = b2BPnLLegalEntity;
                m_b2BPnLAccount = b2BPnLAccount;
                m_b2BPnLDepartment = b2BPnLDepartment;
                m_b2BPnLProfitCentre = b2BPnLProfitCentre;
                m_b2BPnLAffiliate = b2BPnLAffiliate;
                m_retainedEarningsAccount = retainedEarningsAccount;
                m_trialBalanceProfitCentreSplit = trialBalanceProfitCentreSplit;
    }
}
