/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.api.model.impl;

import com.cmcmarkets.nrg.api.model.AbstractProductSetRegulatoryInstrumentIdentificationEntity;

public class NRGProductSetRegulatoryInstrumentIdentification
    extends AbstractProductSetRegulatoryInstrumentIdentificationEntity
{
    public void setCode(String code)
    {
        this.m_code = code;
    }

    public void setAccountType(String accountType)
    {
        this.m_accountType = accountType;
    }

    public void setComment(String comment)
    {
        this.m_comment = comment;
    }

    public void setIdentifierType(String identifierType)
    {
        this.m_identifierType = identifierType;
    }

    public void setInstrumentCode(String instrumentCode)
    {
        this.m_instrumentCode = instrumentCode;
    }

    public void setInstrumentCountry(String instrumentCountry)
    {
        this.m_instrumentCountry = instrumentCountry;
    }

    public void setInstrumentTypeCode(String instrumentTypeCode)
    {
        this.m_instrumentTypeCode = instrumentTypeCode;
    }

    public void setMic(String mic)
    {
        this.m_mic = mic;
    }

    public void setName(String name)
    {
        this.m_name = name;
    }

    public void setRegulation(String regulation)
    {
        this.m_regulation = regulation;
    }

    public void setReportable(Boolean reportable)
    {
        this.m_reportable = reportable;
    }

    public void setReportableInstrumentType(String reportableInstrumentType)
    {
        this.m_reportableInstrumentType = reportableInstrumentType;
    }

    public void setBrokerCode(String brokerCode)
    {
        this.m_brokerCode = brokerCode;
    }

    public void setDerivativeType(String derivativeType)
    {
        this.m_derivativeType = derivativeType;
    }

    public void setDeleted(Boolean deleted)
    {
        this.m_deleted = deleted;
    }
}
