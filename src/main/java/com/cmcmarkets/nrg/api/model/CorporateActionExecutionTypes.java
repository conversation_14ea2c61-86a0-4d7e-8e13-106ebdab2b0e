//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.8-b130911.1802 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.07.21 at 04:26:08 PM BST 
//


package com.cmcmarkets.nrg.api.model;

public enum CorporateActionExecutionTypes {

    OTHER("Other"),

    CASH_DIVIDEND("CashDividend"),

    STOCK_SPLIT("StockSplit"),

    RIGHTS_ISSUE("RightsIssue"),

    NO_EXECUTION("NoExecution");

    private final String value;

    CorporateActionExecutionTypes(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static CorporateActionExecutionTypes fromValue(String v) {
        for (CorporateActionExecutionTypes c: CorporateActionExecutionTypes.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
