package com.cmcmarkets.nrg.api.model;

import java.util.List;

public class HedgeCarryingCosts {
	
	protected String brokerCode;
	protected String carryingCostsSchemaCode;
	protected List<PositionCarryingCosts> positionCarryingCosts;
	
	public String getBrokerCode() {
        return brokerCode;
    }
	
    public void setBrokerCode(String value) {
        this.brokerCode = value;
    }
    
	public String getCarryingCostsSchemaCode() {
        return carryingCostsSchemaCode;
    }
	
    public void setCarryingCostsSchemaCode(String value) {
        this.carryingCostsSchemaCode = value;
    }
    
	public List<PositionCarryingCosts> getPositionCarryingCosts() {
        return positionCarryingCosts;
    }
	
    public void setPositionCarryingCosts(List<PositionCarryingCosts> value) {
        this.positionCarryingCosts = value;
    }
    
}