/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 *
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.api.exception;

@SuppressWarnings("serial")
public class HeartbeatRuntimeException extends RuntimeException
{

    public HeartbeatRuntimeException()
    {
    }

    public HeartbeatRuntimeException(String message)
    {
        super(message);
    }

    public HeartbeatRuntimeException(Throwable cause)
    {
        super(cause);
    }

    public HeartbeatRuntimeException(String message, Throwable cause)
    {
        super(message, cause);
    }

}
