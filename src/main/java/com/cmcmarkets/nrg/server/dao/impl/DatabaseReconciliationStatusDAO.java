/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Struct;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.AbstractDataSource;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.DataId;
import com.cmcmarkets.nrg.api.model.ReconciliationStatusEntity;
import com.cmcmarkets.nrg.server.dao.ReconciliationStatusDAO;
import com.cmcmarkets.nrg.utils.NRGUtils;

import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

public class DatabaseReconciliationStatusDAO extends AbstractDataSource implements ReconciliationStatusDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabaseReconciliationStatusDAO.class);

    private static final String PUT_RECONCILIATION_STATUS_STATEMENT =
        "{ call bi_ods.nrg_reconciliation_status.put_reconciliation_status(?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";
    private static final int PUT_RECONCILIATION_STATUS_START_TIMESTAMP = 1;
    private static final int PUT_RECONCILIATION_STATUS_END_TIMESTAMP = 2;
    private static final int PUT_RECONCILIATION_STATUS_SERVICE_NAME = 3;
    private static final int PUT_RECONCILIATION_STATUS_FROM_DATE = 4;
    private static final int PUT_RECONCILIATION_STATUS_TO_DATE = 5;
    private static final int PUT_RECONCILIATION_STATUS_NO_OF_RECORDS_AT_SOURCE = 6;
    private static final int PUT_RECONCILIATION_STATUS_NO_OF_RECORDS_AT_ODS = 7;
    private static final int PUT_RECONCILIATION_STATUS_IS_DATA_COMPLETE = 8;
    private static final int PUT_RECONCILIATION_STATUS_MISSING_IDS = 9;
    private static final int PUT_RECONCILIATION_STATUS_ERROR_REASON = 10;

    public DatabaseReconciliationStatusDAO()
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("Initialising " + this.getClass().getSimpleName());
        }
    }

    @Override
    public void putReconciliationStatusEntity(ReconciliationStatusEntity reconciliationStatusEntity)
    {
        if(m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + reconciliationStatusEntity);
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_RECONCILIATION_STATUS_STATEMENT);
            setParameterDefaultsForPutReconciliationStatusStatement(statement);

            if(reconciliationStatusEntity.getStartTime() != null)
            {
                statement.setTimestamp(
                    PUT_RECONCILIATION_STATUS_START_TIMESTAMP,
                    new Timestamp(reconciliationStatusEntity.getStartTime().getTime()));
            }

            if(reconciliationStatusEntity.getEndTime() != null)
            {
                statement.setTimestamp(
                    PUT_RECONCILIATION_STATUS_END_TIMESTAMP,
                    new Timestamp(reconciliationStatusEntity.getEndTime().getTime()));
            }

            if(reconciliationStatusEntity.getServiceName() != null)
            {
                statement
                    .setString(PUT_RECONCILIATION_STATUS_SERVICE_NAME, reconciliationStatusEntity.getServiceName());
            }

            if(reconciliationStatusEntity.getFromDate() != null)
            {
                statement.setTimestamp(
                    PUT_RECONCILIATION_STATUS_FROM_DATE,
                    new Timestamp(reconciliationStatusEntity.getFromDate().getTime()));
            }

            if(reconciliationStatusEntity.getToDate() != null)
            {
                statement.setTimestamp(
                    PUT_RECONCILIATION_STATUS_TO_DATE,
                    new Timestamp(reconciliationStatusEntity.getToDate().getTime()));
            }

            if(reconciliationStatusEntity.getNumberOfRecordsAtSource() != null)
            {
                statement.setLong(
                    PUT_RECONCILIATION_STATUS_NO_OF_RECORDS_AT_SOURCE,
                    reconciliationStatusEntity.getNumberOfRecordsAtSource());
            }

            if(reconciliationStatusEntity.getNumberOfRecordsAtODS() != null)
            {
                statement.setLong(
                    PUT_RECONCILIATION_STATUS_NO_OF_RECORDS_AT_ODS,
                    reconciliationStatusEntity.getNumberOfRecordsAtODS());
            }

            if(reconciliationStatusEntity.isDataComplete() != null)
            {
                statement.setString(
                    PUT_RECONCILIATION_STATUS_IS_DATA_COMPLETE,
                    NRGUtils.getYesOrNo(reconciliationStatusEntity.isDataComplete()));
            }

            Collection<DataId> missingDataIds = reconciliationStatusEntity.getMissingDataIds();
            if(missingDataIds != null && missingDataIds.size() > 0)
            {
                StructDescriptor dataIdObjDescriptor =
                    StructDescriptor.createDescriptor("BI_ODS.DATA_ID_OBJ", connection);
                Struct[] dataIdStruct = new STRUCT[missingDataIds.size()];
                int i = 0;
                for(DataId dataId : missingDataIds)
                {
                    dataIdStruct[i++] = new STRUCT(
                        dataIdObjDescriptor,
                        connection,
                        new Object[]{dataId.getDataId(), dataId.getDataId2(), dataId.getEntityName(), null, dataId.getErrorMessage()});
                }
                ArrayDescriptor dataIdTabDescriptor =
                    ArrayDescriptor.createDescriptor("BI_ODS.DATA_ID_TAB", connection);
                Array dataIdArray = new ARRAY(dataIdTabDescriptor, connection, dataIdStruct);
                statement.setArray(PUT_RECONCILIATION_STATUS_MISSING_IDS, dataIdArray);
            }

            if(reconciliationStatusEntity.getErrorReason() != null)
            {
                statement
                    .setString(PUT_RECONCILIATION_STATUS_ERROR_REASON, reconciliationStatusEntity.getErrorReason());
            }

            OracleDatabaseHelper.execute(statement);

            commit(connection);

            if(m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + reconciliationStatusEntity);
            }
        }
        catch(SQLException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting " + reconciliationStatusEntity, e);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutReconciliationStatusStatement(CallableStatement statement)
        throws SQLException
    {
        statement.setNull(PUT_RECONCILIATION_STATUS_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_RECONCILIATION_STATUS_END_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_RECONCILIATION_STATUS_SERVICE_NAME, Types.VARCHAR);
        statement.setNull(PUT_RECONCILIATION_STATUS_FROM_DATE, Types.TIMESTAMP);
        statement.setNull(PUT_RECONCILIATION_STATUS_TO_DATE, Types.TIMESTAMP);
        statement.setNull(PUT_RECONCILIATION_STATUS_NO_OF_RECORDS_AT_SOURCE, Types.NUMERIC);
        statement.setNull(PUT_RECONCILIATION_STATUS_NO_OF_RECORDS_AT_ODS, Types.NUMERIC);
        statement.setNull(PUT_RECONCILIATION_STATUS_IS_DATA_COMPLETE, Types.VARCHAR);
        statement.setNull(PUT_RECONCILIATION_STATUS_MISSING_IDS, Types.ARRAY, "BI_ODS.DATA_ID_TAB");
        statement.setNull(PUT_RECONCILIATION_STATUS_ERROR_REASON, Types.VARCHAR);
    }

}
