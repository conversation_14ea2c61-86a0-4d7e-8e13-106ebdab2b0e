/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.AbstractDataSource;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.OnMessageStatusEntity;
import com.cmcmarkets.nrg.server.dao.MessageHubDAO;

public class DatabaseMessageHubDAO extends AbstractDataSource implements MessageHubDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabaseMessageHubDAO.class);

    private static final String PUT_ON_MESSAGE_STATUS_STATEMENT =
        "{ call bi_ods.nrg_message_hub.put_on_message_status(?, ?, ?, ?, ?, ?) }";
    private static final int PUT_ON_MESSAGE_STATUS_MESSAGE_ID = 1;
    private static final int PUT_ON_MESSAGE_STATUS_USER = 2;
    private static final int PUT_ON_MESSAGE_STATUS_EFFECTIVE_START_TIMESTAMP = 3;
    private static final int PUT_ON_MESSAGE_STATUS_MESSAGE_SOURCE = 4;
    private static final int PUT_ON_MESSAGE_STATUS_MESSAGE_SOURCE_ID = 5;
    private static final int PUT_ON_MESSAGE_STATUS_STATUS = 6;

    public DatabaseMessageHubDAO()
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("Initialising " + this.getClass().getSimpleName());
        }
    }

    @Override
    public void putOnMessageStatusEntity(OnMessageStatusEntity onMessageStatusEntity)
    {
        if(m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + onMessageStatusEntity);
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_ON_MESSAGE_STATUS_STATEMENT);
            setParameterDefaultsForPutOnMessageStatusStatement(statement);

            statement.setString(PUT_ON_MESSAGE_STATUS_USER, onMessageStatusEntity.getUser());

            if(onMessageStatusEntity.getEffectiveStartTimestamp() != null)
            {
                statement.setTimestamp(
                    PUT_ON_MESSAGE_STATUS_EFFECTIVE_START_TIMESTAMP,
                    new Timestamp(onMessageStatusEntity.getEffectiveStartTimestamp().getTime()));
            }
            if(onMessageStatusEntity.getMessageId() != null)
            {
                statement.setLong(PUT_ON_MESSAGE_STATUS_MESSAGE_ID, onMessageStatusEntity.getMessageId());
            }
            if(onMessageStatusEntity.getMessageSource() != null)
            {
                statement.setString(PUT_ON_MESSAGE_STATUS_MESSAGE_SOURCE, onMessageStatusEntity.getMessageSource());
            }
            if(onMessageStatusEntity.getMessageSourceId() != null)
            {
                statement.setString(PUT_ON_MESSAGE_STATUS_MESSAGE_SOURCE_ID, onMessageStatusEntity.getMessageSourceId());
            }
            if(onMessageStatusEntity.getStatus() != null)
            {
                statement.setString(PUT_ON_MESSAGE_STATUS_STATUS, onMessageStatusEntity.getStatus());
            }

            OracleDatabaseHelper.execute(statement);

            commit(connection);

            if(m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + onMessageStatusEntity);
            }
        }
        catch(SQLException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting " + onMessageStatusEntity);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutOnMessageStatusStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_ON_MESSAGE_STATUS_USER, Types.VARCHAR);
        statement.setNull(PUT_ON_MESSAGE_STATUS_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_ON_MESSAGE_STATUS_MESSAGE_ID, Types.NUMERIC);
        statement.setNull(PUT_ON_MESSAGE_STATUS_MESSAGE_SOURCE, Types.NUMERIC);
        statement.setNull(PUT_ON_MESSAGE_STATUS_MESSAGE_SOURCE_ID, Types.NUMERIC);
        statement.setNull(PUT_ON_MESSAGE_STATUS_STATUS, Types.NUMERIC);
    }
}
