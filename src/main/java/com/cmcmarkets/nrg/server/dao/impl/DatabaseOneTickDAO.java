/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Struct;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.AbstractDataSource;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.OneTickValueOfAccount;
import com.cmcmarkets.nrg.server.dao.OneTickDAO;

import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

public class DatabaseOneTickDAO extends AbstractDataSource implements OneTickDAO {
	private static final Logger m_logger = LoggerFactory.getLogger(DatabaseOneTickDAO.class);

	private static final String PUT_VOA_STATEMENT = "{ call bi_ads_otq.nrg_otq_voa.put_voa(?, ?) }";
	private static final int PUT_USER = 1;
	private static final int PUT_VOA = 2;
	
	private String m_user = "NRG";
	private Connection m_connection = null;	
	
    public DatabaseOneTickDAO()
    {
        m_logger.info("Initialising {}", this.getClass().getSimpleName());
    }
    
    @Override
	public void putVoaRecords(Collection<OneTickValueOfAccount> voaRecords) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putVoaRecords: " + voaRecords.size());
		}
		
		CallableStatement statement  = null;
		try {
			m_connection = getConnection();
			statement = m_connection.prepareCall(PUT_VOA_STATEMENT);
			setParameterDefaultsForPutVoaStatement(statement);
			statement.setString(PUT_USER, m_user);
			
			if (voaRecords != null && !voaRecords.isEmpty()) {
				StructDescriptor voaRecordsObjDescriptor = StructDescriptor.createDescriptor("BI_ADS_OTQ.VALUE_OF_ACCOUNT_OBJ",
						m_connection);
				Struct[] voaRecordsStruct = new STRUCT[voaRecords.size()];
				int i = 0;
				for (OneTickValueOfAccount voaRecord : voaRecords) {
					 voaRecordsStruct[i++] = new STRUCT(voaRecordsObjDescriptor, m_connection,
								new Object[] { voaRecord.getEffectiveStartTimestamp()  == null ? null : new Timestamp(voaRecord.getEffectiveStartTimestamp().getTime()),
											   voaRecord.getTradingAccountId(),
											   voaRecord.getSymbolName(),
											   voaRecord.getAssetPctSprd(),
											   voaRecord.getMarkOutGbp(),
											   voaRecord.getInternalisationComponentGbp()
											   });
					}						
				ArrayDescriptor voaRecordsTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ADS_OTQ.VALUE_OF_ACCOUNT_TAB", m_connection);
				Array voaRecordsArray = new ARRAY(voaRecordsTabDescriptor, m_connection, voaRecordsStruct);
				
				statement.setArray(PUT_VOA, voaRecordsArray);
			}
			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putVoaRecordsEntities");
			}
			commit(m_connection);
		} 
		catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putVoaRecordsEntities", e);
			}
			throw new DAOException(e);
		} 
		finally {
			rollback(m_connection);
            close(null, statement, m_connection);
		}
	}
    
	private void setParameterDefaultsForPutVoaStatement(CallableStatement statement) 
			throws SQLException {
		statement.setNull(PUT_USER, Types.VARCHAR);
		statement.setNull(PUT_VOA, Types.ARRAY, "BI_ADS_OTQ.VALUE_OF_ACCOUNT_TAB");
	}
			
}