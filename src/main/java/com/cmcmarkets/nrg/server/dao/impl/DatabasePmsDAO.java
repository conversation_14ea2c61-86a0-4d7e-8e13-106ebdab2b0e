/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import com.cmcmarkets.database.AbstractDataSource;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.InstrumentNames;
import com.cmcmarkets.nrg.api.model.PMSEntryLanguage;
import com.cmcmarkets.nrg.api.model.PriceFeedSchemaEntity;
import com.cmcmarkets.nrg.api.model.PriceSchemaEntity;
import com.cmcmarkets.nrg.api.model.ProductSetBrokerSwapRateEntity;
import com.cmcmarkets.nrg.api.model.ProductSetCountryEntity;
import com.cmcmarkets.nrg.api.model.ProductSetCurrencyMarginDefinition;
import com.cmcmarkets.nrg.api.model.ProductSetCurrencyName;
import com.cmcmarkets.nrg.api.model.ProductSetInstFeedSettingEntity;
import com.cmcmarkets.nrg.api.model.ProductSetInstSchemaProdSettingEntity;
import com.cmcmarkets.nrg.api.model.ProductSetInstrumentBasketRelationEntity;
import com.cmcmarkets.nrg.api.model.ProductSetInstrumentEntity;
import com.cmcmarkets.nrg.api.model.ProductSetInstrumentSchemaMetadata;
import com.cmcmarkets.nrg.api.model.ProductSetInstrumentTypeBrokerMarginEntity;
import com.cmcmarkets.nrg.api.model.ProductSetMarketEntity;
import com.cmcmarkets.nrg.api.model.ProductSetMarketHourTypeEntity;
import com.cmcmarkets.nrg.api.model.ProductSetMarketSuspensionEntity;
import com.cmcmarkets.nrg.api.model.ProductSetOptionsTradingClassEntity;
import com.cmcmarkets.nrg.api.model.ProductSetPriceStreamEntity;
import com.cmcmarkets.nrg.api.model.ProductSetProductEntity;
import com.cmcmarkets.nrg.api.model.ProductSetProductWrapperEntity;
import com.cmcmarkets.nrg.api.model.ProductSetRegionEntity;
import com.cmcmarkets.nrg.api.model.ProductSetRegulatoryInstrumentIdentificationEntity;
import com.cmcmarkets.nrg.api.model.ProductSetTierEntity;
import com.cmcmarkets.nrg.api.model.ProductSetTimeZoneEntity;
import com.cmcmarkets.nrg.api.model.ProductSetTypicalMarketHoursEntity;
import com.cmcmarkets.nrg.api.model.SnapshotIdEntity;
import com.cmcmarkets.nrg.api.model.impl.NRGPriceFeedSchema2;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetCountry;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetEconomicCalendarEvent;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetHoldingCosts;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetInstFeedSetting;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetInstrument;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetInstrumentSchemaMetadata;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetInstrumentToEconomicCalendarEvent;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetProduct;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetProductWrapper;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetReferenceHoldingCosts;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetRegion;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetRegulatoryInstrumentIdentification;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetTaxTreatyRelation;
import com.cmcmarkets.nrg.api.model.impl.NRGProductSetting;
import com.cmcmarkets.nrg.pms.DbrCurrency;
import com.cmcmarkets.nrg.server.dao.PmsDao;
import com.cmcmarkets.nrg.utils.NRGUtils;
import com.cmcmarkets.productmaster.api.model.PriceFeedSchemaItem;
import com.cmcmarkets.productmaster.api.model.PriceFeedSchemaOverride2;
import com.cmcmarkets.productmaster.api.model.PriceSchemaItem;
import com.cmcmarkets.productmaster.api.model.SnapshotDimensionProperty;
import com.cmcmarkets.productmaster.api.model.SnapshotProperty2;
import oracle.jdbc.OracleConnection;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.Date;
import java.sql.SQLException;
import java.sql.Struct;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import static com.cmcmarkets.nrg.server.dao.NrgDAOUtils.setString;
import static com.cmcmarkets.nrg.server.dao.NrgDAOUtils.setTimestamp;
import static com.cmcmarkets.nrg.server.dao.NrgDAOUtils.toArray;
import static com.cmcmarkets.nrg.server.dao.NrgDAOUtils.toStruct;
import static com.cmcmarkets.nrg.server.dao.NrgDAOUtils.transformThenToStruct;

public class DatabasePmsDAO extends AbstractDataSource implements PmsDao {
	private static final Logger m_logger = LoggerFactory.getLogger(DatabasePmsDAO.class);

	private static final int PUT_USER = 1;
	private static final int PUT_EFFECTIVE_START_TIMESTAMP = 2;

	private static final String PUT_PRODUCTS_STATEMENT = "{ call bi_ods.nrg_products.put_products(?, ?, ?) }";
	private static final int PUT_PRODUCTS = 3;

	private static final String PUT_PRODUCT_WRAPPERS_STATEMENT = "{ call bi_ods.nrg_products.put_product_wrapper(?, ?, ?) }";
	private static final int PUT_PRODUCT_WRAPPERS = 3;

	private static final String PUT_INSTRUMENT_TYPE_LANGUAGES_STATEMENT = "{ call bi_ods.nrg_products.put_instrument_type_language(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_TYPE_LANGUAGES = 3;

	private static final String PUT_INSTRUMENTS_STATEMENT = "{ call bi_ods.nrg_products.put_instruments(?, ?, ?) }";
	private static final int PUT_INSTRUMENTS = 3;

	private static final String PUT_REGIONS_STATEMENT = "{ call bi_ods.nrg_products.put_regions(?, ?, ?) }";
	private static final int PUT_REGIONS = 3;

	private static final String PUT_COUNTRIES_STATEMENT = "{ call bi_ods.nrg_products.put_countries(?, ?, ?) }";
	private static final int PUT_COUNTRIES = 3;

	private static final String PUT_INSTRUMENT_FEED_SETTINGS_STATEMENT = "{ call bi_ods.nrg_products.put_inst_feed_setting(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_FEED_SETTINGS = 3;

	private static final String PUT_REGULATORY_INSTRUMENT_IDENTIFICATIONS_STATEMENT = "{ call bi_ods.nrg_products.put_reg_inst_identification(?, ?, ?) }";
	private static final int PUT_REGULATORY_INSTRUMENT_IDENTIFICATIONS = 3;

	private static final String PUT_GSKO_PRODUCTS_STATEMENT = "{ call bi_ods.nrg_products.put_products_gsko(?, ?, ?) }";
	private static final int PUT_GSKO_PRODUCTS = 3;

	private static final String PUT_PRODUCT_SETTINGS_STATEMENT = "{ call bi_ods.nrg_products.put_product_settings(?, ?, ?) }";
	private static final int PUT_PRODUCT_SETTINGS = 3;

	private static final String PUT_ECONOMIC_CALENDAR_EVENTS_STATEMENT = "{ call bi_ods.nrg_products.put_economic_calendar_events(?, ?, ?) }";
	private static final int PUT_ECONOMIC_CALENDAR_EVENTS = 3;

	private static final String PUT_INSTRUMENT_TO_ECONOMIC_CALENDAR_EVENTS_STATEMENT = "{ call bi_ods.nrg_products.put_inst_to_economic_events(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_TO_ECONOMIC_CALENDAR_EVENTS = 3;

	private static final String PUT_TAX_TREATY_RELATIONS_STATEMENT = "{ call bi_ods.nrg_products.put_tax_treaties(?, ?, ?) }";
	private static final int PUT_TAX_TREATY_RELATIONS = 3;

	private static final String PUT_SNAPSHOT_LOG_STATEMENT = "{ call bi_ods.nrg_products.put_snapshot_log(?, ?, ?, ?) }";
	private static final int PUT_SNAPSHOT_LOG_IDENTIFIER = 3;
	private static final int PUT_SNAPSHOT_LOG_STATUS = 4;

	private static final String PUT_EOD_SNAPSHOT_IDS_STATEMENT = "{ call bi_ods.nrg_products.put_snapshot_identifiers(?, ?) }";
	private static final int PUT_SNAPSHOT_IDS_SET_USER = 1;
	private static final int PUT_SNAPSHOT_IDS_SET_SNAPSHOT_IDS = 2;

	private static final String PUT_PRICE_SCHEMAS_STATEMENT = "{ call bi_ods.nrg_price_schema.put_price_schemas(?) }";
	private static final int PUT_PRICE_SCHEMAS_SCHEMAS = 1;

	private static final String PUT_PRICE_FEED_SCHEMAS_STATEMENT = "{ call bi_ods.nrg_price_schema.put_price_feed_schema_set(?,?,?) }";
	private static final int PUT_PRICE_FEED_SCHEMAS_SCHEMAS = 3;

	private static final String PUT_PRICE_FEED_SCHEMAS_2_STATEMENT = "{ call bi_ods.nrg_price_schema.put_price_feed_schema_2_set(?,?,?) }";
	private static final int PUT_PRICE_FEED_SCHEMAS_SCHEMAS2 = 3;

	private static final String PUT_REFERENCE_HOLDING_COSTS_STATEMENT = "{ call bi_ods.nrg_products.put_reference_holding_costs(?, ?, ?) }";
	private static final int PUT_REFERENCE_HOLDING_COSTS = 3;

	private static final String PUT_HOLDING_COSTS_STATEMENT = "{ call bi_ods.nrg_products.put_holding_costs(?, ?, ?) }";
	private static final int PUT_HOLDING_COSTS = 3;

	private static final String PUT_INSTRUMENT_TYPE_BROKER_MARGINS_STATEMENT = "{ call bi_ods.nrg_products.put_inst_type_broker_margins(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_TYPE_BROKER_MARGINS = 3;

	private static final String PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS_STATEMENT = "{ call bi_ods.nrg_products.put_prdct_sttngs_inst_schm(?, ?, ?) }";
	private static final int PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS = 3;

	private static final String PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS_STATEMENT_CCY = "{ call bi_ods.nrg_products.put_prdct_sttngs_inst_schm_ccy(?, ?, ?) }";
	private static final int PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS_CCY = 3;

	private static final String PUT_PRICE_STREAMS_STATEMENT = "{ call bi_ods.nrg_products.put_price_streams(?, ?, ?) }";
	private static final int PUT_PRICE_STREAMS = 3;
	
	private static final String PUT_INSTRUMENT_PRICE_BAND_OFFSETS_STATEMENT = "{ call bi_ods.nrg_products.put_instrument_price_bands(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_PRICE_BAND_OFFSETS = 3;
	
	private static final String PUT_INSTRUMENT_SWAP_POINT_BAND_OFFSETS_STATEMENT = "{ call bi_ods.nrg_products.put_instrument_swap_point_bands(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_SWAP_POINT_BAND_OFFSETS = 3;
	
	private static final String PUT_INSTRUMENT_BASKET_RELATIONS_STATEMENT = "{ call bi_ods.nrg_products.put_instrument_basket_relation(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_BASKET_RELATIONS = 3;
	
	private static final String PUT_INSTRUMENT_SCHEMA_METADATA_STATEMENT = "{ call bi_ods.nrg_products.put_instrument_schema_metadata(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_SCHEMA_METADATA = 3;
	
	private static final String PUT_CURRENCIES_STATEMENT = "{ call bi_ods.nrg_products.put_currencies(?, ?, ?) }";
	private static final int PUT_CURRENCIES = 3;
	
	private static final String PUT_CURRENCY_NAMES_STATEMENT = "{ call bi_ods.nrg_products.put_currency_names(?, ?, ?) }";
	private static final int PUT_CURRENCY_NAMES = 3;
	
	private static final String PUT_CURRENCY_MARGIN_DEFINITIONS_STATEMENT = "{ call bi_ods.nrg_products.put_currency_margin_defns(?, ?, ?) }";
	private static final int PUT_CURRENCY_MARGIN_DEFINITIONS = 3;	

	private static final String PUT_MARKETS_STATEMENT = "{ call bi_ods.nrg_products.put_markets(?, ?, ?) }";
	private static final int PUT_MARKETS = 3;

	private static final String PUT_MARKET_HOUR_TYPES_STATEMENT = "{ call bi_ods.nrg_products.put_market_hour_types(?, ?, ?) }";
	private static final int PUT_MARKET_HOUR_TYPES = 3;

	private static final String PUT_MARKET_SUSPENSIONS_STATEMENT = "{ call bi_ods.nrg_products.put_market_suspensions(?, ?, ?) }";
	private static final int PUT_MARKET_SUSPENSIONS = 3;

	private static final String PUT_TIMEZONES_STATEMENT = "{ call bi_ods.nrg_products.put_pms_time_zones(?, ?, ?) }";
	private static final int PUT_TIMEZONES = 3;
	
	private static final String PUT_TYPICAL_MARKET_HOURS_STATEMENT = "{ call bi_ods.nrg_products.put_typical_market_hours(?, ?, ?) }";
	private static final int PUT_TYPICAL_MARKET_HOURS = 3;

	private static final String PUT_OPTIONS_TRADING_CLASS_STATEMENT = "{ call bi_ods.nrg_products.put_options_trading_class(?, ?, ?) }";
	private static final int PUT_OPTIONS_TRADING_CLASS = 3;
	private static final String APPLY_PRIME_MARGIN_BUFFER = "ApplyPrimeMarginBuffer";

	private static final String PUT_INSTRUMENT_FX_CONVERSION_BAND_OFFSETS_STATEMENT = "{ call bi_ods.nrg_products.put_fx_conversion_bands(?, ?, ?) }";
	private static final int PUT_INSTRUMENT_FX_CONVERSION_BAND_OFFSETS = 3;

	private static final String PUT_BROKER_SWAP_RATES_STATEMENT = "{ call bi_ods.nrg_products.put_broker_swap_rates(?, ?, ?) }";
	private static final int PUT_BROKER_SWAP_RATES = 3;

	private String m_user = null;
	private java.util.Date m_effectiveStartTimestamp = null;
	private Connection m_connection = null;

	public DatabasePmsDAO() {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("Initialising " + this.getClass().getSimpleName());
		}
	}

	@Override
	public void beginPutProductSet(String user, java.util.Date effectiveStartTimestamp, String snapshotId)
			throws DAOException, SQLException {
		m_logger.info("begin put product set {}", snapshotId);
		m_user = user;
		m_effectiveStartTimestamp = effectiveStartTimestamp;
	}

	@Override
	public void commitPutProductSet() throws DAOException {
		/*m_logger.info("commit put product set");
		commit(m_connection);
		rollback(m_connection);
		close(null, null, m_connection);
		m_connection = null;  */
		m_logger.info("finished put product set");
	}

	@Override
	public void rollbackPutProductSet() throws DAOException {
		m_logger.info("rollback put product set");
	/*	rollback(m_connection);
		close(null, null, m_connection);
		m_connection = null;  */
	}

	@Override
	public void putProducts(String snapshotId, Collection<NRGProductSetProduct> products) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putProducts: " + products.size());
		}
		
		Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_PRODUCTS_STATEMENT);
			setParameterDefaultsForPutProductsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));
			if (products != null && !products.isEmpty()) {
				StructDescriptor productObjDescriptor = StructDescriptor.createDescriptor("BI_ODS.PRODUCT_OBJ",
				    connection);
				Struct[] productStruct = new STRUCT[products.size()];
				int i = 0;
				for (ProductSetProductEntity product : products) {

					productStruct[i++] = new STRUCT(productObjDescriptor, connection, new Object[] {
							product.getPlatform(), product.getCode(), product.getProductWrapper(),
							product.getMarketMakerInstrumentId(), product.getProductSettingCode(), product.getTerm(),
							product.getUnderlyingName(), NRGUtils.getYesOrNo(product.isCurrencyInFractionalParts()),
							product.getContractSize(), product.getMarketMakerCurrencyId(), product.getCurrency(),
							product.getMarketMakerCurrencyDescription(), product.getFractionalPartRatio(),
							product.getInitialPublishDate() == null ? null
									: new Date(product.getInitialPublishDate().getTime()),
							NRGUtils.getYesOrNo(product.isFindable()), product.getShortName(), product.getLongName(),
							product.getExpiry() == null ? null : new Date(product.getExpiry().getTime()),
							product.getCFDMultiplier(), product.getPoint(), product.getMarketMakerTerm(),
							product.getMarketMakerTermDescription(), NRGUtils.getYesOrNo(product.isDeleted()),
							product.getDefaultFinancingRatio(),
							NRGUtils.getYesOrNo(product.getIsPairCurrencyInFractionalParts()),
							product.getPointMultiplier() });
				}
				ArrayDescriptor productTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PRODUCT_TAB",
				    connection);
				Array productArray = new ARRAY(productTabDescriptor, connection, productStruct);
				statement.setArray(PUT_PRODUCTS, productArray);
			}
			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putProducts");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putProducts", e);
			}
			//rollbackPutProductSet();

			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	public void putInstrumentTypeLanguages(String snapshotId, Map<String, Set<PMSEntryLanguage>> intrumentTypeLanguages)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentTypeLanguages: " + intrumentTypeLanguages.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_TYPE_LANGUAGES_STATEMENT);
			setParameterDefaultsForPutInstrumentTypeLanguagesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			Array instrumentTypeLanguagesArray = null;

			if (intrumentTypeLanguages != null && !intrumentTypeLanguages.isEmpty()) {
				StructDescriptor instrumentTypeLanguageObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.INSTRUMENT_TYPE_LANGUAGES_OBJ", connection);
				List<STRUCT> instrumentTypeLanguagesStruct = new ArrayList<STRUCT>();
				for (String instrumentTypeCode : intrumentTypeLanguages.keySet()) {
					Set<PMSEntryLanguage> entries = intrumentTypeLanguages.get(instrumentTypeCode);
					if (entries != null) {
						for (PMSEntryLanguage entry : entries) {
							instrumentTypeLanguagesStruct.add(new STRUCT(instrumentTypeLanguageObjDescriptor,
							    connection, new Object[] { instrumentTypeCode, entry.getLanguageCode(),
											entry.getDefaultName(), entry.getName() }));
						}
					}
				}
				ArrayDescriptor instrumentTypeLanguagesTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.INSTRUMENT_TYPE_LANGUAGES_TAB", connection);
				instrumentTypeLanguagesArray = new ARRAY(instrumentTypeLanguagesTabDescriptor, connection,
						instrumentTypeLanguagesStruct.toArray());

				statement.setArray(PUT_INSTRUMENT_TYPE_LANGUAGES, instrumentTypeLanguagesArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putInstrumentTypeLanguages");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putInstrumentTypeLanguages", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putInstruments(String snapshotId, Collection<NRGProductSetInstrument> instruments) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstruments: " + instruments.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENTS_STATEMENT);
			setParameterDefaultsForPutInstrumentsStatement(statement);
			setString(statement, PUT_USER, m_user);
			setTimestamp(statement, PUT_EFFECTIVE_START_TIMESTAMP, m_effectiveStartTimestamp.getTime());

			if (instruments != null && !instruments.isEmpty()) {
				List<Struct> instrumentsList = new ArrayList<>();
				for (ProductSetInstrumentEntity instrument : instruments) {
					Array instrumentNamesArray = null;
					if (instrument.getInstrumentNames() != null && !instrument.getInstrumentNames().isEmpty()) {

						List<Struct> instrumentNamesList = new ArrayList<>();
						for (String languageCode : instrument.getInstrumentNames().keySet()) {
							InstrumentNames names = instrument.getInstrumentNames().get(languageCode);
							if (names != null) {
								instrumentNamesList.add(
										toStruct(connection,
												"BI_ODS.INSTRUMENT_LANGUAGES_OBJ",
												instrument.getInstrumentCode(),
												languageCode,
												names.getShortName(),
												names.getLongName())
								);

							}
						}
						instrumentNamesArray = toArray(connection,
								"BI_ODS.INSTRUMENT_LANGUAGES_TAB",
								instrumentNamesList.toArray());
					}

					instrumentsList.add(
							transformThenToStruct(
									connection,
									"BI_ODS.INSTRUMENT_OBJ",
									false,
									instrument.getInstrumentCode(), instrument.getCountryCode(), instrument.getFeedSymbol(),
									instrument.getInstrumentType(), instrument.getShortName(), instrument.getISIN(),
									instrument.getMIC(), instrument.getRIC(), instrument.getCurrency(),
									instrument.getPairCurrency(), instrument.getCompanySectorName(),
									instrument.getCommodityTypeName(), instrument.getCountryClassificationName(),
									NRGUtils.getYesOrNo(instrument.isDeleted()), instrument.getMMUID(), instrument.getMMUID2(),
									instrument.getMMVALUEID(), instrument.getProphetBandingAlgorithm(),
									instrument.getProphetBandingFixedSpread(), instrument.getProphetBandingSpreadFactor(),
									instrument.getMmInstIdCfd(), instrument.getMmInstIdSbIr(), instrument.getMmInstIdSbUk(),
									instrument.getPricingStartDate() == null ? null
											: new Timestamp(instrument.getPricingStartDate().getTime()),
									instrument.getFirstTradingDate() == null ? null
											: new Timestamp(instrument.getFirstTradingDate().getTime()),
									instrument.getLastRolloverDate() == null ? null
											: new Timestamp(instrument.getLastRolloverDate().getTime()),
									instrument.getAutomaticRolloverDate() == null ? null
											: new Timestamp(instrument.getAutomaticRolloverDate().getTime()),
									instrument.getLastTradingDate() == null ? null
											: new Timestamp(instrument.getLastTradingDate().getTime()),
									instrument.getCashSettlementDate() == null ? null
											: new Timestamp(instrument.getCashSettlementDate().getTime()),
									instrument.getExpiryDate() == null ? null
											: new Timestamp(instrument.getExpiryDate().getTime()),
									NRGUtils.getYesOrNo(instrument.isSkipRollover()), instrument.getLastTradingDateDesc(),
									instrument.getLastSettlementDateDesc(), instrument.getLastRolloverDateDesc(),
									instrument.getContractCode(), instrument.getRolloverTargetCode(),
									instrument.getCmcCashInstrumentCode(), instrument.getCmcFinancialInstrumentType(),
									instrument.getExchangeProductCode(),
									NRGUtils.getYesOrNo(instrument.getIsPRRQualifyingIndex()),
									instrument.getPositionRiskRequirementPercent(), instrument.getSecurityType(),
									instrumentNamesArray, instrument.getContractSizeOverride(), instrument.getCommodityBase(),
									instrument.getCommodityDetail(), instrument.getInstrumentTypeCode(),
									instrument.getRiskCountryCode(), instrument.getTaxCountryCode(),
									instrument.getBloombergCode(), instrument.getSedol(), instrument.getReutersMIC(),
									instrument.getOperatingMIC(), instrument.getMainExchange(), instrument.getMarketStatus(),
									instrument.getCurMktCapUsd(), instrument.getAvgDailyValueTraded30dUsd(),
									instrument.getAvgDailyTraded3mUsd(), instrument.getEqyFreeFloatPct(),
									instrument.getProphetPrimaryInstrumentCode(), instrument.getCouponRate(),
									instrument.getCheapestToDeliverDate() == null ? null
											: new Timestamp(instrument.getCheapestToDeliverDate().getTime()),
									instrument.getMarketCode(), instrument.getMarketAlias(),
									instrument.getMarketDataMappingCode(), instrument.getCfdCfi(), instrument.getSbCfi(),
									NRGUtils.getYesOrNo(instrument.getIsMifidiiReportable()),
									instrument.getSecurityDescription(), instrument.getProphetBrokerMarginAmount(),
									instrument.getProphetBrokerMarginPercent(), instrument.getProphetBrokerMarginTier(),
									instrument.getProphetBrokerMarginTierFxDB(), instrument.getProphetBrokerMarginTierFxUBS(),
									instrument.getUnderlyingInstrumentCode(), instrument.getUnderlyingInstrumentAlias(),
									NRGUtils.getYesOrNo(instrument.getIsCcyInFrctnlPrts()),
									NRGUtils.getYesOrNo(instrument.getIsPairCurrencyInFractionalParts()),
									instrument.getPointMultiplier(),
									instrument.getFinancingCurrency(),
									NRGUtils.getYesOrNo(instrument.getImIsTestInstrument()),
									NRGUtils.getYesOrNo(instrument.getImIsDemoOnly()),
									NRGUtils.getYesOrNo(instrument.getIsFixedSpreadInstrument()),
									NRGUtils.getYesOrNo(instrument.getIsGuaranteedStopInstrmnt()),
									NRGUtils.getYesOrNo(instrument.getIsMetatraderInstrument()),
									NRGUtils.getYesOrNo(instrument.getIsEsmaDuplicate()),
									NRGUtils.getYesOrNo(instrument.getIsFindableInstrument()),
									NRGUtils.getYesOrNo(instrument.getIsTradableInstrument()),
									instrument.getAPICode(),
									instrument.getWorkflowStatus(),
									NRGUtils.getYesOrNo(instrument.getIsAsicDuplicate()),
									instrument.getFirstNoticeDate() == null ? null
											: new Timestamp(instrument.getFirstNoticeDate().getTime()),
									NRGUtils.getYesOrNo(instrument.getProphetBcEnabled()),
									NRGUtils.getYesOrNo(instrument.getIsValueDateRolloverInstrument()),
									NRGUtils.getYesOrNo(instrument.getIsPreventCfdTradingOnFxAccounts()),
									instrument.getTaxSecurityType(),
									instrument.getProductIdValue(),
									instrument.getProductSelector(),
									NRGUtils.getYesOrNo(instrument.getIsInstrumentSI()),
									instrument.getCUSIP(),
									instrument.getFundType(),
									instrument.getProphetCarryAskOverride(),
									instrument.getProphetCarryBidOverride(),
									NRGUtils.getYesOrNo(instrument.getProphetCarryRateOverridden()),
									NRGUtils.getYesOrNo(instrument.getInstrumentDelistedInMarket()),
									instrument.getSAXOInstrumentCode(),
									instrument.getBBCountryFullName(),
									instrument.getBBReferenceTaxSecurityType(),
									instrument.getAssetTypeDescription(),
									instrument.getAssetSubTypeDescription(),
									instrument.getOptionRoot(),
									NRGUtils.getYesOrNo(instrument.getIsNationalDeclarationProduct()),
									NRGUtils.getYesOrNo(instrument.getIsCAInstrument()),
									instrument.getFundAssetClassFocus(),
									instrument.getAnnadsbCommBaseProduct(),
									instrument.getAnnadsbCommSubProduct(),
									instrument.getAnnadsbCommAddSubProduct(),
									instrument.getAnnadsbCommHeaderProduct(),
									instrument.getAnnadsbCommUnderlierId(),
									instrument.getAnnadsbCommUnderlierIdSrc(),
									instrument.getUpiCfd(),
									instrument.getUpiSb(),
									instrument.getUpiOptionsEurCashPut(),
									instrument.getUpiOptionsEurCashCall(),
									instrument.getUpiOptionsEurPhysPut(),
									instrument.getUpiOptionsEurPhysCall(),
									instrument.getUpiOptionsAmerCashPut(),
									instrument.getUpiOptionsAmerCashCall(),
									instrument.getUpiOptionsAmerPhysPut(),
									instrument.getUpiOptionsAmerPhysCall(),
									instrument.getUpiOptionsBermCashPut(),
									instrument.getUpiOptionsBermCashCall(),
									instrument.getUpiOptionsBermPhysPut(),
									instrument.getUpiOptionsBermPhysCall(),
									instrument.getUpiCfdCountdown(),
									instrument.getUpiSbCountdown(),
									instrument.getCountdownCfi(),
									instrument.getOptionsAmerCashCallCFI(),
									instrument.getOptionsAmerCashPutCFI(),
									instrument.getOptionsAmerPhysCallCFI(),
									instrument.getOptionsAmerPhysPutCFI(),
									instrument.getOptionsBermCashCallCFI(),
									instrument.getOptionsBermCashPutCFI(),
									instrument.getOptionsBermPhysPutCFI(),
									instrument.getOptionsBermPhysCallCFI(),
									instrument.getOptionsEurCashCallCFI(),
									instrument.getOptionsEurCashPutCFI(),
									instrument.getOptionsEurPhysCallCFI(),
									instrument.getOptionsEurPhysPutCFI(),
									instrument.getCurrentSharesOutstanding(),
									instrument.getUpiSbOptions(),
									instrument.getSbOptionCfi(),
									NRGUtils.getYesOrNo(instrument.getIsUseInstForFxConversion()),
									NRGUtils.getYesOrNo(instrument.getIsOTC()),
									instrument.getBBCountryIncorporation(),
									instrument.getRefTaxCountry(),
									instrument.getRefCountryIncorporationDsc(),
									instrument.getUpiCfdOptions(),
									instrument.getCfdOptionCFI(),
									NRGUtils.getYesOrNo(instrument.getIsIsaEligible())
							));


				}

				Array instrumentArray = toArray(connection, "BI_ODS.INSTRUMENT_TAB", instrumentsList.toArray());
				statement.setArray(PUT_INSTRUMENTS, instrumentArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putInstruments");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putInstruments", e);
			}
			//rollbackPutProductSet();

			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	@Override
	public void putProductWrappers(String snapshotId, Collection<NRGProductSetProductWrapper> productWrappers)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putProductWrappers: " + productWrappers.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_PRODUCT_WRAPPERS_STATEMENT);
			setParameterDefaultsForPutProductWrappersStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (productWrappers != null && !productWrappers.isEmpty()) {
				StructDescriptor productWrapperObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRODUCT_WRAPPER_OBJ", connection);
				Struct[] productWrapperStruct = new STRUCT[productWrappers.size()];
				int i = 0;
				for (ProductSetProductWrapperEntity productWrapper : productWrappers) {
					productWrapperStruct[i++] = new STRUCT(productWrapperObjDescriptor, connection,
							new Object[] { productWrapper.getProductWrapperCode(),
									productWrapper.getProductWrapperType(), productWrapper.getShortName(),
									productWrapper.getLongName(),
									NRGUtils.getYesOrNo(productWrapper.isQuantityAmountAvailable()),
									NRGUtils.getYesOrNo(productWrapper.isQuantityUnitsAvailable()),
									NRGUtils.getYesOrNo(productWrapper.isQuantityUnitsAdministrable()),
									NRGUtils.getYesOrNo(productWrapper.isQuantityAmountAdministrable()),
									NRGUtils.getYesOrNo(productWrapper.isQuantityAmountPerPointAvailable()),
									NRGUtils.getYesOrNo(productWrapper.isQuantityAmountPerPointAdministrable()),
									NRGUtils.getYesOrNo(productWrapper.isDeleted()) });
				}
				ArrayDescriptor productWrapperTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.PRODUCT_WRAPPER_TAB", connection);
				Array productWrapperArray = new ARRAY(productWrapperTabDescriptor, connection, productWrapperStruct);
				statement.setArray(PUT_PRODUCT_WRAPPERS, productWrapperArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putProductWrappers");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putProductWrappers", e);
			}
			//rollbackPutProductSet();
			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	@Override
	public void putCountries(String snapshotId, Collection<NRGProductSetCountry> countries) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putCountries: " + countries.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_COUNTRIES_STATEMENT);
			setParameterDefaultsForPutCountriesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (countries != null && !countries.isEmpty()) {
				StructDescriptor countryObjDescriptor = StructDescriptor.createDescriptor("BI_ODS.COUNTRY_OBJ",
						connection);
				Struct[] countriesStruct = new STRUCT[countries.size()];
				int i = 0;
				for (ProductSetCountryEntity country : countries) {
					countriesStruct[i++] = new STRUCT(countryObjDescriptor, connection,
							new Object[] { country.getCountryCode(), country.getCountryName(), country.getISO2(),
									country.getISO3(), country.getISONumeric(), country.getRegionCode(),
									country.getLabourForce(), country.getUnemploymentRate(), country.getExportsInUSD(),
									country.getImportsInUSD(), country.getPopulation(), country.getCurrency(),
									country.getStatus(), NRGUtils.getYesOrNo(country.isDeleted()),
									NRGUtils.getYesOrNo(country.isEeaMember()),
									NRGUtils.getYesOrNo(country.isEuMember()), country.getDividendTaxRate(),
									country.getDeclarationValidityPeriod() });
				}
				ArrayDescriptor countryTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.COUNTRY_TAB",
						connection);
				Array countryArray = new ARRAY(countryTabDescriptor, connection, countriesStruct);
				statement.setArray(PUT_COUNTRIES, countryArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putCountries");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putCountries", e);
			}
			//rollbackPutProductSet();
			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	@Override
	public void putRegions(String snapshotId, Collection<NRGProductSetRegion> regions) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putRegions: " + regions.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_REGIONS_STATEMENT);
			setParameterDefaultsForPutRegionsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (regions != null && !regions.isEmpty()) {
				StructDescriptor regionObjDescriptor = StructDescriptor.createDescriptor("BI_ODS.REGION_OBJ",
						connection);
				Struct[] regionsStruct = new STRUCT[regions.size()];
				int i = 0;
				for (ProductSetRegionEntity region : regions) {
					regionsStruct[i++] = new STRUCT(regionObjDescriptor, connection,
							new Object[] { region.getRegionCode(), region.getRegionName(), region.getISONumeric(),
									region.getLanguage(), NRGUtils.getYesOrNo(region.isDeleted()) });
				}
				ArrayDescriptor regionTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.REGION_TAB",
						connection);
				Array regionArray = new ARRAY(regionTabDescriptor, connection, regionsStruct);
				statement.setArray(PUT_REGIONS, regionArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putRegions");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putRegions", e);
			}
			//rollbackPutProductSet();
			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	@Override
	public void putInstrumentFeedSettings(String snapshotId, Collection<NRGProductSetInstFeedSetting> feedSettings)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentFeedSettings: " + feedSettings.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_FEED_SETTINGS_STATEMENT);
			setParameterDefaultsForPutInstrumentFeedSettingsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (feedSettings != null && !feedSettings.isEmpty()) {
				StructDescriptor feedSettingObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.INSTRUMENT_FEED_SETTING_OBJ", connection);
				Struct[] feedSettingsStruct = new STRUCT[feedSettings.size()];
				int i = 0;
				for (ProductSetInstFeedSettingEntity feedSetting : feedSettings) {
					feedSettingsStruct[i++] = new STRUCT(feedSettingObjDescriptor, connection, new Object[] {
							feedSetting.getCode(), feedSetting.getInstrumentCode(), feedSetting.getFeedType(),
							feedSetting.getNGQuoteSymbol(), feedSetting.getBand1MinimumAbsoluteBidMargin(),
							feedSetting.getBand1MinimumAbsoluteAskMargin(), feedSetting.getBand1ProportionalMargin(),
							feedSetting.getBand2MinimumAbsoluteBidMargin(),
							feedSetting.getBand2MinimumAbsoluteAskMargin(), feedSetting.getBand2ProportionalMargin(),
							feedSetting.getBand3MinimumAbsoluteBidMargin(),
							feedSetting.getBand3MinimumAbsoluteAskMargin(), feedSetting.getBand3ProportionalMargin(),
							feedSetting.getBand4MinimumAbsoluteBidMargin(),
							feedSetting.getBand4MinimumAbsoluteAskMargin(), feedSetting.getBand4ProportionalMargin(),
							feedSetting.getBand5MinimumAbsoluteBidMargin(),
							feedSetting.getBand5MinimumAbsoluteAskMargin(), feedSetting.getBand5ProportionalMargin(),
							feedSetting.getFeedProducer(), feedSetting.getFeedSymbol(), feedSetting.getAccessLevel(),
							feedSetting.getPriceNumberOfDecimals(),
							feedSetting.getInitialPublishedDate() == null ? null
									: new Timestamp(feedSetting.getInitialPublishedDate()),
							feedSetting.getLastPublishedDate() == null ? null
									: new Timestamp(feedSetting.getLastPublishedDate()),
							feedSetting.getBand6MinimumAbsoluteBidMargin(),
							feedSetting.getBand6MinimumAbsoluteAskMargin(), feedSetting.getBand6ProportionalMargin(),
							feedSetting.getBand7MinimumAbsoluteBidMargin(),
							feedSetting.getBand7MinimumAbsoluteAskMargin(), feedSetting.getBand7ProportionalMargin(),
							feedSetting.getBand8MinimumAbsoluteBidMargin(),
							feedSetting.getBand8MinimumAbsoluteAskMargin(), feedSetting.getBand8ProportionalMargin(),
							feedSetting.getBand9MinimumAbsoluteBidMargin(),
							feedSetting.getBand9MinimumAbsoluteAskMargin(), feedSetting.getBand9ProportionalMargin(),
							feedSetting.getBand10MinimumAbsoluteBidMargin(),
							feedSetting.getBand10MinimumAbsoluteAskMargin(),
							feedSetting.getBand10ProportionalMargin() });
				}
				ArrayDescriptor feedSettingTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.INSTRUMENT_FEED_SETTING_TAB", connection);
				Array countryArray = new ARRAY(feedSettingTabDescriptor, connection, feedSettingsStruct);
				statement.setArray(PUT_INSTRUMENT_FEED_SETTINGS, countryArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putInstrumentFeedSettings");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putInstrumentFeedSettings", e);
			}
			//rollbackPutProductSet();
			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	@Override
	public void putRegulatoryInstrumentIdentifications(String snapshotId,
			Collection<NRGProductSetRegulatoryInstrumentIdentification> regulatoryInstrumentIdentifications)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putRegulatoryInstrumentIdentifications: " + regulatoryInstrumentIdentifications.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_REGULATORY_INSTRUMENT_IDENTIFICATIONS_STATEMENT);
			setParameterDefaultsForPutRegulatoryInstrumentIdentificationsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (regulatoryInstrumentIdentifications != null && !regulatoryInstrumentIdentifications.isEmpty()) {
				StructDescriptor regulatoryInstrumentIdentificationObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.REG_INSTR_IDENTIFICATION_OBJ", connection);
				Struct[] regulatoryInstrumentIdentificationsStruct = new STRUCT[regulatoryInstrumentIdentifications
						.size()];
				int i = 0;
				for (ProductSetRegulatoryInstrumentIdentificationEntity entry : regulatoryInstrumentIdentifications) {
					regulatoryInstrumentIdentificationsStruct[i++] = new STRUCT(
							regulatoryInstrumentIdentificationObjDescriptor, connection,
							new Object[] { entry.getCode(), entry.getName(), entry.getRegulation(),
									entry.getAccountType(), entry.getInstrumentCode(), entry.getInstrumentTypeCode(),
									entry.getInstrumentCountry(), entry.getMIC(),
									NRGUtils.getYesOrNo(entry.isReportable()), entry.getIdentifierType(),
									entry.getBrokerCode(), entry.getReportableInstrumentType(),
									entry.getDerivativeType() });
				}
				ArrayDescriptor regulatoryInstrumentIdentificationsTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.REG_INSTR_IDENTIFICATION_TAB", connection);
				Array regulatoryInstrumentIdentificationsArray = new ARRAY(
						regulatoryInstrumentIdentificationsTabDescriptor, connection,
						regulatoryInstrumentIdentificationsStruct);
				statement.setArray(PUT_REGULATORY_INSTRUMENT_IDENTIFICATIONS, regulatoryInstrumentIdentificationsArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putRegulatoryInstrumentIdentifications");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putRegulatoryInstrumentIdentifications", e);
			}
			//rollbackPutProductSet();
			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
		}
	}

	@Override
	public void putGSKOProductSettings(String snapshotId, Map<String, SnapshotDimensionProperty> gskoProductSettings)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putGSKOProductSettings: " + gskoProductSettings.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_GSKO_PRODUCTS_STATEMENT);
			setParameterDefaultsForPutGskoProductsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (gskoProductSettings != null && !gskoProductSettings.isEmpty()) {
				StructDescriptor gskoProductSettingObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRODUCTS_GSKO_OBJ", connection);
				List<STRUCT> gskoProductSettingsStruct = new ArrayList<STRUCT>();
				for (Entry<String, SnapshotDimensionProperty> entry : gskoProductSettings.entrySet()) {
					SnapshotDimensionProperty setting = entry.getValue();

					if (setting != null) {
						if (setting.getValues() != null) {
							for (SnapshotProperty2 property : setting.getValues()) {
								if (property != null) {
									gskoProductSettingsStruct.add(new STRUCT(gskoProductSettingObjDescriptor,
											connection,
											new Object[] { entry.getKey(), APPLY_PRIME_MARGIN_BUFFER, property.getKey(),
													property.getIntegerValue() != null
															? (property.getIntegerValue().equals(1) ? "Yes" : "No")
															: null }));
								}
							}
						}
						if (setting.getDefaultValue() != null) {
							gskoProductSettingsStruct.add(new STRUCT(gskoProductSettingObjDescriptor, connection,
									new Object[] { entry.getKey(), APPLY_PRIME_MARGIN_BUFFER,
											setting.getDefaultValue().getKey(),
											setting.getDefaultValue().getIntegerValue() != null
													? (setting.getDefaultValue().getIntegerValue().equals(1) ? "Yes"
															: "No")
													: null }));
						}
					}
				}
				ArrayDescriptor gskoProductSettingsTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.PRODUCTS_GSKO_TAB", connection);
				Array gskoProductSettingsArray = new ARRAY(gskoProductSettingsTabDescriptor, connection,
						gskoProductSettingsStruct.toArray(new STRUCT[] {}));
				statement.setArray(PUT_GSKO_PRODUCTS, gskoProductSettingsArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putGSKOProductSettings");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putGSKOProductSettings", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putProductSettings(String snapshotId, Collection<NRGProductSetting> productsSettings)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putProductSettings: " + productsSettings.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_PRODUCT_SETTINGS_STATEMENT);
			setParameterDefaultsForPutProductSettingsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (productsSettings != null) {
				StructDescriptor objDescriptor = StructDescriptor.createDescriptor("BI_ODS.PRODUCT_SETTINGS_OBJ",
						connection);
				Struct[] struct = new STRUCT[productsSettings.size()];
				int i = 0;
				for (NRGProductSetting setting : productsSettings) {

					Array marginTiersCfd = getMarginTiersCfdArray(setting, connection);
					Array marginTiersSb = getMarginTiersSbArray(setting, connection);
					Array marginTiersOptions = getMarginTiersOptionsArray(setting, connection);

					struct[i++] = new STRUCT(objDescriptor, connection,
							new Object[] { setting.getProductSettingCode(), null, null, setting.getStopLossBuffer(),
									setting.getStopLossBufferType(), setting.getStopEntryBuffer(),
									setting.getStopEntryBufferType(), setting.getBinaryType(), setting.getBinaryTenor(),
									marginTiersCfd, marginTiersSb, setting.getInstrumentCode(), marginTiersOptions });
				}
				ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PRODUCT_SETTINGS_TAB",
						connection);
				Array array = new ARRAY(tabDescriptor, connection, struct);
				statement.setArray(PUT_PRODUCT_SETTINGS, array);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putProductSettings");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putProductSettings", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putTaxTreatyRelations(String snapshotId, Collection<NRGProductSetTaxTreatyRelation> taxTreatyRelations)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putTaxTreatyRelations: " + taxTreatyRelations.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_TAX_TREATY_RELATIONS_STATEMENT);
			setParameterDefaultsForPutTaxTreatyRelationsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (taxTreatyRelations != null && !taxTreatyRelations.isEmpty()) {
				StructDescriptor objDescriptor = StructDescriptor.createDescriptor("BI_ODS.TAX_TREATY_RELATIONS_OBJ",
						connection);
				Struct[] struct = new STRUCT[taxTreatyRelations.size()];
				int i = 0;
				for (NRGProductSetTaxTreatyRelation event : taxTreatyRelations) {
					struct[i++] = new STRUCT(objDescriptor, connection,
							new Object[] { event.getCode(), event.getTreatyCountryCode(),
									event.getReportingCountryCode(), event.getTreatyWhtTaxRate(),
									new Date(event.getValidFrom()) });
				}
				ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.TAX_TREATY_RELATIONS_TAB",
						connection);
				Array array = new ARRAY(tabDescriptor, connection, struct);
				statement.setArray(PUT_TAX_TREATY_RELATIONS, array);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putTaxTreatyRelations");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putTaxTreatyRelations", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putSnapshotIdentifier(String snapshotId, Long snapshotActivationTime, SNAPSHOT_STATUS status)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putSnapshotLog: {} {}", snapshotId, status);
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
            connection = getConnection();
			statement = connection.prepareCall(PUT_SNAPSHOT_LOG_STATEMENT);
			setParameterDefaultsForPutSnapshotIdentifierStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(snapshotActivationTime));
			statement.setString(PUT_SNAPSHOT_LOG_IDENTIFIER, snapshotId);
			statement.setString(PUT_SNAPSHOT_LOG_STATUS, NRGUtils.getYesOrNo(SNAPSHOT_STATUS.PROCESSED == status));

			OracleDatabaseHelper.execute(statement);

			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putSnapshotLog: {} {}", snapshotId, status);
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putSnapshotLog {} {}", snapshotId, status, e);
			}
			//rollbackPutProductSet();
			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);     
        }
	}

	@Override
	public void putInstrumentToEconomicCalendarEvents(String snapshotId,
			Collection<NRGProductSetInstrumentToEconomicCalendarEvent> instrumentToEconomicCalendarEvents)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentToEconomicCalendarEvents: " + instrumentToEconomicCalendarEvents.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_TO_ECONOMIC_CALENDAR_EVENTS_STATEMENT);
			setParameterDefaultsForPutInstrumentToEconomicCalendarEventsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (instrumentToEconomicCalendarEvents != null && !instrumentToEconomicCalendarEvents.isEmpty()) {
				StructDescriptor objDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.ECONOMIC_CAL_EVENTS_INST_OBJ", connection);
				Struct[] struct = new STRUCT[instrumentToEconomicCalendarEvents.size()];
				int i = 0;
				for (NRGProductSetInstrumentToEconomicCalendarEvent event : instrumentToEconomicCalendarEvents) {
					struct[i++] = new STRUCT(objDescriptor, connection, new Object[] { event.getCode(),
							event.getInstrumentCode(), event.getEconomicCalendarEventCode() });
				}
				ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.ECONOMIC_CAL_EVENTS_INST_TAB",
						connection);
				Array array = new ARRAY(tabDescriptor, connection, struct);
				statement.setArray(PUT_INSTRUMENT_TO_ECONOMIC_CALENDAR_EVENTS, array);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putInstrumentToEconomicCalendarEvents");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putInstrumentToEconomicCalendarEvents", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putEconomicCalendarEvents(String snapshotId,
			Collection<NRGProductSetEconomicCalendarEvent> economicCalendarEvents) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putEconomicCalendarEvents: " + economicCalendarEvents.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_ECONOMIC_CALENDAR_EVENTS_STATEMENT);
			setParameterDefaultsForPutEconomicCalendarEventsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (economicCalendarEvents != null && !economicCalendarEvents.isEmpty()) {
				StructDescriptor objDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.ECONOMIC_CALENDAR_EVENTS_OBJ", connection);
				Struct[] struct = new STRUCT[economicCalendarEvents.size()];
				int i = 0;
				for (NRGProductSetEconomicCalendarEvent event : economicCalendarEvents) {
					Array langArray = null;
					if (event != null) {
						if (event.getNames() != null) {
							// Extract the language array
							StructDescriptor langObjDescriptor = StructDescriptor
									.createDescriptor("BI_ODS.ECONOMIC_CAL_EVENTS_LANG_OBJ", connection);
							Struct[] langStruct = new STRUCT[event.getNames().size()];
							int j = 0;
							for (PMSEntryLanguage entry : event.getNames()) {
								langStruct[j++] = new STRUCT(langObjDescriptor, connection,
										new Object[] { event.getCode(), entry.getLanguageCode(), entry.getDefaultName(),
												entry.getName() });
							}
							ArrayDescriptor langTabDescriptor = ArrayDescriptor
									.createDescriptor("BI_ODS.ECONOMIC_CAL_EVENTS_LANG_TAB", connection);
							langArray = new ARRAY(langTabDescriptor, connection, langStruct);
						}

						struct[i++] = new STRUCT(objDescriptor, connection,
								new Object[] { event.getCode(), 
										event.getName(), 
										event.getDescription(),
										event.getMeasures(), 
										event.getCare(), 
										event.getFrequency(), 
										event.getSource(),
										event.getEffect(), 
										event.getImpact(), 
										NRGUtils.getYesOrNo(event.isVisible()),
										(event.getISO2() == null) ? null : event.getISO2().substring(0,2), 
										NRGUtils.getYesOrNo(event.hasSpecifiedDueTime()),
										event.getEventCodifier(), 
										event.getEventCode(), 
										event.getReutersEventCode(),
										event.getOrganisationCode(), 
										langArray });
					}
				}
				ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.ECONOMIC_CALENDAR_EVENTS_TAB",
						connection);
				Array array = new ARRAY(tabDescriptor, connection, struct);
				statement.setArray(PUT_ECONOMIC_CALENDAR_EVENTS, array);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("putEconomicCalendarEvents");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putEconomicCalendarEvents", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putBrokerSwapRates(String snapshotId, Long activationTime, Collection<ProductSetBrokerSwapRateEntity> brokerSwapRates) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putBrokerSwapRates: " + brokerSwapRates.size());
		}

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_BROKER_SWAP_RATES_STATEMENT);
			setParameterDefaultsForPutBrokerSwapRatesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(activationTime));

			Array brokerSwapRatesArray = null;
			if (brokerSwapRates != null && !brokerSwapRates.isEmpty()) {
				StructDescriptor brokerSwapRatesObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.BROKER_SWAP_RATES_OBJ", connection);
				Struct[] brokerSwapRatesStruct = new STRUCT[brokerSwapRates.size()];
				int i = 0;
				for (ProductSetBrokerSwapRateEntity entry : brokerSwapRates) {
					brokerSwapRatesStruct[i++] = new STRUCT(brokerSwapRatesObjDescriptor, connection,
							new Object[] {
									entry.getBrokerSwapRateCode(),
									entry.getBrokerAccountNumber(),
									entry.getCurrency(),
									entry.getCountryCode(),
									entry.getInstrumentCode(),
									mapDirection(entry.getDirection()),
									entry.getReferenceRate(),
									entry.getTerm(),
									entry.getLongHaircut(),
									entry.getShortHaircut()
							});
				}
				ArrayDescriptor brokerSwapRatesTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.BROKER_SWAP_RATES_TAB", connection);
				brokerSwapRatesArray = new ARRAY(brokerSwapRatesTabDescriptor, connection, brokerSwapRatesStruct);

				statement.setArray(PUT_BROKER_SWAP_RATES, brokerSwapRatesArray);

				OracleDatabaseHelper.execute(statement);
			}

			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putBrokerSwapRates");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putBrokerSwapRates", e);
			}
			rollback(connection);
			throw new DAOException(e);
		} finally {
			close(null, statement, connection);
		}
	}

	private String mapDirection(String direction) {
		if (direction == null) {
			return null;
		}

		switch (direction) {
			case "0":
				return "BOTH";
			case "1":
				return "LONG";
			case "2":
				return "SHORT";
			default:
				return direction.toUpperCase();
		}
	}
	private Array getMarginTiersOptionsArray(NRGProductSetting setting, Connection connection) throws SQLException {
		StructDescriptor objDescriptor = StructDescriptor.createDescriptor("BI_ODS.MARGIN_DEF_NG_OPTIONS_OBJ", connection);
		List<Struct> struct = new ArrayList<>();
		for (ProductSetTierEntity tierSet : setting.getMarginTiersOptions()) {
			if (tierSet != null && tierSet.getTiersEx() != null && !tierSet.getTiersEx().isEmpty()) {
				Object[] data = new Object[] { setting.getProductSettingCode(),
						getSizeFromTiersEx(tierSet, 1), getRateFromTiersEx(tierSet, 1), getFloorFromTiersEx(tierSet, 1),
						getSizeFromTiersEx(tierSet, 2), getRateFromTiersEx(tierSet, 2), getFloorFromTiersEx(tierSet, 2),
						getSizeFromTiersEx(tierSet, 3), getRateFromTiersEx(tierSet, 3), getFloorFromTiersEx(tierSet, 3),
						getSizeFromTiersEx(tierSet, 4), getRateFromTiersEx(tierSet, 4), getFloorFromTiersEx(tierSet, 4),
						getSizeFromTiersEx(tierSet, 5), getRateFromTiersEx(tierSet, 5), getFloorFromTiersEx(tierSet, 5),
						tierSet.getInstrumentSchemaCode() };

				struct.add(new STRUCT(objDescriptor, connection, data));
			}
		}
		ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.MARGIN_DEF_NG_CFD_TAB", connection);
		return new ARRAY(tabDescriptor, connection, struct.toArray(new STRUCT[] {}));
	}

	private Array getMarginTiersCfdArray(NRGProductSetting setting, Connection connection) throws SQLException {
		StructDescriptor objDescriptor = StructDescriptor.createDescriptor("BI_ODS.MARGIN_DEF_NG_CFD_OBJ", connection);
		List<Struct> struct = new ArrayList<>();
		for (ProductSetTierEntity tierSet : setting.getMarginTiersCfd()) {
			if (tierSet != null && tierSet.getTiers() != null && !tierSet.getTiers().isEmpty()) {
				Object[] data = new Object[] { setting.getProductSettingCode(), getSizeLimitFromTiers(tierSet, 1),
						getRateFromTiers(tierSet, 1), getSizeLimitFromTiers(tierSet, 2), getRateFromTiers(tierSet, 2),
						getSizeLimitFromTiers(tierSet, 3), getRateFromTiers(tierSet, 3),
						getSizeLimitFromTiers(tierSet, 4), getRateFromTiers(tierSet, 4),
						getSizeLimitFromTiers(tierSet, 5), getRateFromTiers(tierSet, 5),
						tierSet.getInstrumentSchemaCode() };

				struct.add(new STRUCT(objDescriptor, connection, data));
			}
		}
		ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.MARGIN_DEF_NG_CFD_TAB", connection);
		return new ARRAY(tabDescriptor, connection, struct.toArray(new STRUCT[] {}));
	}

	private Array getMarginTiersSbArray(NRGProductSetting setting, Connection connection) throws SQLException {
		StructDescriptor objDescriptor = StructDescriptor.createDescriptor("BI_ODS.MARGIN_DEF_NG_SB_OBJ", connection);
		List<Struct> struct = new ArrayList<>();
		for (ProductSetTierEntity tierSet : setting.getMarginTiersSb()) {
			if (tierSet != null && tierSet.getTiers() != null && !tierSet.getTiers().isEmpty()) {
				Object[] data = new Object[] { setting.getProductSettingCode(), tierSet.getCurrency(),
						getSizeLimitFromTiers(tierSet, 1), getRateFromTiers(tierSet, 1),
						getSizeLimitFromTiers(tierSet, 2), getRateFromTiers(tierSet, 2),
						getSizeLimitFromTiers(tierSet, 3), getRateFromTiers(tierSet, 3),
						getSizeLimitFromTiers(tierSet, 4), getRateFromTiers(tierSet, 4),
						getSizeLimitFromTiers(tierSet, 5), getRateFromTiers(tierSet, 5),
						tierSet.getInstrumentSchemaCode() };

				struct.add(new STRUCT(objDescriptor, connection, data));
			}
		}
		ArrayDescriptor tabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.MARGIN_DEF_NG_SB_TAB", connection);
		return new ARRAY(tabDescriptor, connection, struct.toArray(new STRUCT[] {}));
	}

	private BigDecimal getRateFromTiers(ProductSetTierEntity tierSet, int tier) {
		BigDecimal rate = null;
		if (tierSet != null && tierSet.getTiers() != null && tierSet.getTiers().size() >= tier) {
			rate = tierSet.getTiers().get(tier - 1).getRate();
		}
		return rate;
	}

	private BigDecimal getRateFromTiersEx(ProductSetTierEntity tierSet, int tier) {
		BigDecimal rate = null;
		if (tierSet != null && tierSet.getTiersEx() != null && tierSet.getTiersEx().size() >= tier) {
			rate = tierSet.getTiersEx().get(tier - 1).getRate();
		}
		return rate;
	}

	private Long getSizeLimitFromTiers(ProductSetTierEntity tierSet, int tier) {
		Long sizeLimit = null;
		if (tierSet != null && tierSet.getTiers() != null && tierSet.getTiers().size() >= tier) {
			sizeLimit = tierSet.getTiers().get(tier - 1).getSizeLimit();
		}
		return sizeLimit;
	}

	private Long getSizeFromTiersEx(ProductSetTierEntity tierSet, int tier) {
		Long size = null;
		if (tierSet != null && tierSet.getTiersEx() != null && tierSet.getTiersEx().size() >= tier) {
			size = tierSet.getTiersEx().get(tier - 1).getSize();
		}
		return size;
	}

	private BigDecimal getFloorFromTiersEx(ProductSetTierEntity tierSet, int tier) {
		BigDecimal floor = null;
		if (tierSet != null && tierSet.getTiersEx() != null && tierSet.getTiersEx().size() >= tier) {
			floor = tierSet.getTiersEx().get(tier - 1).getFloor();
		}
		return floor;
	}

	@Override
	public void putEODSnapshotIds(String user, List<SnapshotIdEntity> snapshotIds) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putEODSnapshotIds: " + snapshotIds);
		}

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_EOD_SNAPSHOT_IDS_STATEMENT);
			setParameterDefaultsForPutEODSnapshotIdsSetStatement(statement);

			statement.setString(PUT_SNAPSHOT_IDS_SET_USER, user);

			if (snapshotIds != null && !snapshotIds.isEmpty()) {
				StructDescriptor snapshotIdObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRODUCT_SNAPSHOT_IDNTFR_OBJ", connection);
				Struct[] snapshotIdStruct = new STRUCT[snapshotIds.size()];
				int i = 0;
				for (SnapshotIdEntity id : snapshotIds) {
					snapshotIdStruct[i++] = new STRUCT(snapshotIdObjDescriptor, connection,
							new Object[] { id.getId() });
				}
				ArrayDescriptor snapshotIdTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.PRODUCT_SNAPSHOT_IDNTFR_TAB", connection);
				Array snapshotIdArray = new ARRAY(snapshotIdTabDescriptor, connection, snapshotIdStruct);
				statement.setArray(PUT_SNAPSHOT_IDS_SET_SNAPSHOT_IDS, snapshotIdArray);
			}

			OracleDatabaseHelper.execute(statement);
			commit(connection);

			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putEODSnapshotIds");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putEODSnapshotIds", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	private void setParameterDefaultsForPutEODSnapshotIdsSetStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_SNAPSHOT_IDS_SET_USER, Types.VARCHAR);
		statement.setNull(PUT_SNAPSHOT_IDS_SET_SNAPSHOT_IDS, Types.ARRAY, "BI_ODS.PRODUCT_SNAPSHOT_IDNTFR_TAB");
	}

	private void setParameterDefaultsForPutPriceSchemasStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_PRICE_SCHEMAS_SCHEMAS, Types.ARRAY, "BI_ODS.PRICE_SCHEMAS_TAB");
	}

	private void setParameterDefaultsForPutPriceFeedSchemasStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_PRICE_FEED_SCHEMAS_SCHEMAS, Types.ARRAY, "BI_ODS.PRICE_FEED_SCHEMAS_TAB");
	}

	private void setParameterDefaultsForPutPriceFeedSchemas2Statement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_PRICE_FEED_SCHEMAS_SCHEMAS2, Types.ARRAY, "BI_ODS.PRICE_FEED_SCHEMAS_2_TAB");
	}
	
	private void setParameterDefaultsForPutProductsStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_PRODUCTS, Types.ARRAY, "BI_ODS.PRODUCT_TAB");
	}

	private void setParameterDefaultsForPutInstrumentTypeLanguagesStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_TYPE_LANGUAGES, Types.ARRAY, "BI_ODS.INSTRUMENT_TYPE_LANGUAGES_TAB");
	}

	private void setParameterDefaultsForPutInstrumentTypeBrokerMarginsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_TYPE_BROKER_MARGINS, Types.ARRAY, "BI_ODS.INST_TYPE_BROKER_MARGINS_TAB");
	}

	private void setParameterDefaultsForPutInstrumentsStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_INSTRUMENTS, Types.ARRAY, "BI_ODS.INSTRUMENT_TAB");
	}

	private void setParameterDefaultsForPutProductWrappersStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_PRODUCTS, Types.ARRAY, "BI_ODS.PRODUCT_WRAPPER_TAB");
	}

	private void setParameterDefaultsForPutRegionsStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_REGIONS, Types.ARRAY, "BI_ODS.REGION_TAB");
	}

	private void setParameterDefaultsForPutCountriesStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_COUNTRIES, Types.ARRAY, "BI_ODS.COUNTRY_TAB");
	}

	private void setParameterDefaultsForPutProductSettingsStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_PRODUCT_SETTINGS, Types.ARRAY, "BI_ODS.PRODUCT_SETTINGS_TAB");
	}

	private void setParameterDefaultsForPutTaxTreatyRelationsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_TAX_TREATY_RELATIONS, Types.ARRAY, "BI_ODS.TAX_TREATY_RELATIONS_TAB");
	}

	private void setParameterDefaultsForPutSnapshotIdentifierStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_SNAPSHOT_LOG_IDENTIFIER, Types.VARCHAR);
		statement.setNull(PUT_SNAPSHOT_LOG_STATUS, Types.VARCHAR);
	}

	private void setParameterDefaultsForPutInstrumentFeedSettingsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_FEED_SETTINGS, Types.ARRAY, "BI_ODS.INSTRUMENT_FEED_SETTING_TAB");
	}

	private void setParameterDefaultsForPutRegulatoryInstrumentIdentificationsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_REGULATORY_INSTRUMENT_IDENTIFICATIONS, Types.ARRAY,
				"BI_ODS.REG_INSTR_IDENTIFICATION_TAB");
	}

	private void setParameterDefaultsForPutGskoProductsStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_GSKO_PRODUCTS, Types.ARRAY, "BI_ODS.PRODUCTS_GSKO_TAB");
	}

	private void setParameterDefaultsForPutEconomicCalendarEventsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_ECONOMIC_CALENDAR_EVENTS, Types.ARRAY, "BI_ODS.ECONOMIC_CALENDAR_EVENTS_TAB");
	}

	private void setParameterDefaultsForPutInstrumentToEconomicCalendarEventsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_TO_ECONOMIC_CALENDAR_EVENTS, Types.ARRAY,
				"BI_ODS.ECONOMIC_CAL_EVENTS_INST_TAB");
	}

	private void setParameterDefaultsForPutReferenceHoldingCostsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_REFERENCE_HOLDING_COSTS, Types.ARRAY, "BI_ODS.REFERENCE_HOLDING_COSTS_TAB");
	}

	private void setParameterDefaultsForPutHoldingCostsStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_HOLDING_COSTS, Types.ARRAY, "BI_ODS.HOLDING_COSTS_TAB");
	}

	private void setParameterDefaultsForPutProductSettingsInstrumentSchemasStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS, Types.ARRAY, "BI_ODS.PRDCT_STTNGS_INST_SCHM_TAB");
	}

	private void setParameterDefaultsForPutProductSettingsInstrumentSchemasCcyStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS_CCY, Types.ARRAY,
				"BI_ODS.PRDCT_STTNGS_INST_SCHM_CCY_TAB");
	}
	
	private void setParameterDefaultsForPutInstrumentPriceBandOffsetsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_PRICE_BAND_OFFSETS, Types.ARRAY,
				"BI_ODS.INSTRUMENT_PRC_BNDS_KEY_TAB");
	}

	private void setParameterDefaultsForPutInstrumentFxConversionBandOffsetsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_FX_CONVERSION_BAND_OFFSETS, Types.ARRAY,
				"BI_ODS.INSTRUMENT_FX_CON_BANDS_KEY_TAB");
	}
	
	private void setParameterDefaultsForPutInstrumentSwapPointBandOffsetsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_SWAP_POINT_BAND_OFFSETS, Types.ARRAY,
				"BI_ODS.INSTRUMENT_SWP_BNDS_KEY_TAB");
	}
	
	private void setParameterDefaultsForPutPriceStreamsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_PRICE_STREAMS, Types.ARRAY,
				"BI_ODS.PRICE_STREAMS_TAB");
	}
	
	private void setParameterDefaultsForPutInstrumentBasketRelationsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_BASKET_RELATIONS, Types.ARRAY,
				"BI_ODS.INSTRUMENT_BASKET_REL_TAB");
	}
	
	private void setParameterDefaultsForPutInstSchemaMetadataStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_INSTRUMENT_SCHEMA_METADATA, Types.ARRAY,
				"BI_ODS.INST_SCHEMA_METADATA_TAB");
	}
	
	private void setParameterDefaultsForPutCurrenciesStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_CURRENCIES, Types.ARRAY,
				"BI_ODS.CURRENCIES_TAB");
	}

    private void setParameterDefaultsForPutCurrencyNamesStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_CURRENCY_NAMES, Types.ARRAY,
				"BI_ODS.CURRENCY_NAMES_TAB");
	}
	
	private void setParameterDefaultsForPutCurrencyMarginDefinitionsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_CURRENCY_MARGIN_DEFINITIONS, Types.ARRAY,
				"BI_ODS.CURRENCY_MARGIN_DEF_KEY_TAB");
	}	

	private void setParameterDefaultsForPutMarketsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_MARKETS, Types.ARRAY,
				"BI_ODS.MARKET_TAB");
	}
	
	private void setParameterDefaultsForPutMarketHourTypesStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_MARKET_HOUR_TYPES, Types.ARRAY,
				"BI_ODS.MARKET_HOUR_TYPE_TAB");
	}
	
	private void setParameterDefaultsForPutMarketSuspensionsStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_MARKET_SUSPENSIONS, Types.ARRAY,
				"BI_ODS.MARKET_SUSPENSION_TAB");
	}
	
	private void setParameterDefaultsForPutTimeZonesStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_TIMEZONES, Types.ARRAY,
				"BI_ODS.PMS_TIME_ZONE_TAB");
	}
	
	private void setParameterDefaultsForPutTypicalMarketHoursStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_TYPICAL_MARKET_HOURS, Types.ARRAY,
				"BI_ODS.TYPICAL_MARKET_HOUR_TAB");
	}

	private void setParameterDefaultsForPutOptionsTradingClassStatement(CallableStatement statement)
			throws SQLException {
		statement.setNull(PUT_OPTIONS_TRADING_CLASS, Types.ARRAY,
				"BI_ODS.OPTIONS_TRADING_CLASS_TAB");
	}

	private void setParameterDefaultsForPutBrokerSwapRatesStatement(CallableStatement statement) throws SQLException {
		statement.setNull(PUT_BROKER_SWAP_RATES, Types.ARRAY, "BI_ODS.BROKER_SWAP_RATES_TAB");
	}

	@Override
	public void putPriceSchemas(String user, List<PriceSchemaEntity> priceSchemas, boolean isDeleted, boolean isReqResp)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("calling putPriceSchemas");

		}

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_PRICE_SCHEMAS_STATEMENT);
			setParameterDefaultsForPutPriceSchemasStatement(statement);

			Array priceSchemaArray = null;
			if (priceSchemas != null && !priceSchemas.isEmpty()) {
				StructDescriptor priceSchemaObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRICE_SCHEMAS_OBJ", connection);
				Struct[] priceSchemaStruct = new STRUCT[priceSchemas.size()];
				int i = 0;
				for (PriceSchemaEntity priceSchema : priceSchemas) {
					Array priceSchemaItemArray = null;
					if (priceSchema.getPriceSchemaItems() != null && !priceSchema.getPriceSchemaItems().isEmpty()) {
						StructDescriptor priceSchemaItemObjDescriptor = StructDescriptor
								.createDescriptor("BI_ODS.PRICE_SCHEMA_ITEM_OBJ", connection);
						Struct[] priceSchemaItemStruct = new STRUCT[priceSchema.getPriceSchemaItems().size()];
						int j = 0;
						for (PriceSchemaItem priceSchemaItem : priceSchema.getPriceSchemaItems()) {
							priceSchemaItemStruct[j++] = new STRUCT(priceSchemaItemObjDescriptor, connection,
									new Object[] { priceSchemaItem.getOverrideType(), priceSchemaItem.getCode(),
											priceSchemaItem.getPriceBand() });
						}
						ArrayDescriptor priceSchemaItemTabDescriptor = ArrayDescriptor
								.createDescriptor("BI_ODS.PRICE_SCHEMA_ITEM_TAB", connection);
						priceSchemaItemArray = new ARRAY(priceSchemaItemTabDescriptor, connection,
								priceSchemaItemStruct);
					}

					priceSchemaStruct[i++] = new STRUCT(priceSchemaObjDescriptor, connection,
							new Object[] { user, priceSchema.getName(),
									new Timestamp(priceSchema.getEffectiveStartTimestamp().getTime()),
									priceSchema.getDefaultBand(), priceSchemaItemArray, NRGUtils.getYesOrNo(isDeleted),
									NRGUtils.getYesOrNo(isReqResp) });
				}
				ArrayDescriptor priceSchemaTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PRICE_SCHEMAS_TAB",
						connection);
				priceSchemaArray = new ARRAY(priceSchemaTabDescriptor, connection, priceSchemaStruct);
			}

			statement.setArray(PUT_PRICE_SCHEMAS_SCHEMAS, priceSchemaArray);

			OracleDatabaseHelper.execute(statement);
			

			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putPriceSchemas");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putPriceSchemas", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putPriceFeedSchemas(String user, List<PriceFeedSchemaEntity> priceFeedSchemas, boolean isDeleted, Long scheduleTime)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("calling putPriceFeedSchemas");
        }

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_PRICE_FEED_SCHEMAS_STATEMENT);
			setParameterDefaultsForPutPriceFeedSchemasStatement(statement);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(scheduleTime));
			statement.setString(PUT_USER, user);
			
			Long scheduleEodDate = NRGUtils.getEOD(scheduleTime).getTime();
			String isEoD = NRGUtils.getYesOrNo((scheduleTime.equals(scheduleEodDate)));
			
			Array priceFeedSchemaArray = null;
			if (priceFeedSchemas != null && !priceFeedSchemas.isEmpty()) {
				StructDescriptor priceFeedSchemaObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRICE_FEED_SCHEMAS_OBJ", connection);
				Struct[] priceFeedSchemaStruct = new STRUCT[priceFeedSchemas.size()];
				int i = 0;
				for (PriceFeedSchemaEntity priceFeedSchema : priceFeedSchemas) {
				    
				   /* if(priceFeedSchema.getName().equals("BI Test PFS 2")) {
				        int v = 0;
				    }*/
				    
					Array priceFeedSchemaItemArray = null;
					if (priceFeedSchema.getPriceFeedSchemaItems() != null && !priceFeedSchema.getPriceFeedSchemaItems().isEmpty()) {
						StructDescriptor priceFeedSchemaItemObjDescriptor = StructDescriptor
								.createDescriptor("BI_ODS.PRICE_FEED_SCHEMA_ITEMS_OBJ", connection);
						Struct[] priceFeedSchemaItemStruct = new STRUCT[priceFeedSchema.getPriceFeedSchemaItems().size()];
						int j = 0;
						for (PriceFeedSchemaItem priceFeedSchemaItem : priceFeedSchema.getPriceFeedSchemaItems()) {
							priceFeedSchemaItemStruct[j++] = new STRUCT(priceFeedSchemaItemObjDescriptor, connection,
									new Object[] { priceFeedSchema.getName(),
												   priceFeedSchemaItem.getOverrideType(), 
											       priceFeedSchemaItem.getCode(),
											       priceFeedSchemaItem.getPriceBand(),
											       priceFeedSchemaItem.getPriceStreamType(),
											       priceFeedSchemaItem.getPriceAdjustId(),
											       priceFeedSchemaItem.getPriceAdjustAlias(),
											       priceFeedSchemaItem.getPriceBandAlias(),
											       priceFeedSchemaItem.getPriceStreamTypeAlias(),
											       isEoD,
											       NRGUtils.getYesOrNo(priceFeedSchemaItem.isExecuteAtOrigin()),
											       priceFeedSchemaItem.getSwapPointBand(),
											       priceFeedSchemaItem.getSwapPointBandAlias(),
											       priceFeedSchemaItem.getSpreadProportion(),
											       priceFeedSchemaItem.getPriceBandOutOfHours() == null ? null :
											           priceFeedSchemaItem.getPriceBandOutOfHours().getBandId(),
                                                   priceFeedSchemaItem.getPriceBandOutOfHours() == null ? null :
                                                       priceFeedSchemaItem.getPriceBandOutOfHours().getBandAlias(),
                                                   priceFeedSchemaItem.getPriceBandFxr() == null ? null :
                                                       priceFeedSchemaItem.getPriceBandFxr().getBandId(),
                                                   priceFeedSchemaItem.getPriceBandFxr() == null ? null :
                                                       priceFeedSchemaItem.getPriceBandFxr().getBandAlias()
												   });
						}
						ArrayDescriptor priceFeedSchemaItemTabDescriptor = ArrayDescriptor
								.createDescriptor("BI_ODS.PRICE_FEED_SCHEMA_ITEMS_TAB", connection);
						priceFeedSchemaItemArray = new ARRAY(priceFeedSchemaItemTabDescriptor, connection,
								priceFeedSchemaItemStruct);
					}

					priceFeedSchemaStruct[i++] = new STRUCT(priceFeedSchemaObjDescriptor, connection,
							new Object[] { priceFeedSchema.getName(),
										   priceFeedSchema.getDefaultBand(), 
										   priceFeedSchema.getDefaultPriceStreamType(),
										   priceFeedSchemaItemArray, 
										   priceFeedSchema.getDefaultAdjustId(),
										   priceFeedSchema.getDefaultAdjustAlias(),
										   priceFeedSchema.getDefaultBandAlias(),
										   priceFeedSchema.getDefaultPriceStreamTypeAlias(),
										   NRGUtils.getYesOrNo(isDeleted),
										   isEoD,
										   NRGUtils.getYesOrNo(priceFeedSchema.isExecuteAtOrigin()),
									       priceFeedSchema.getDefaultSwapPointBand(),
									       priceFeedSchema.getDefaultSwapPointBandAlias(),
									       priceFeedSchema.getDefaultSpreadProportion(),
									       priceFeedSchema.getDefaultBandOutOfHours() == null ? null :
									           priceFeedSchema.getDefaultBandOutOfHours().getBandId(),
								           priceFeedSchema.getDefaultBandOutOfHours() == null ? null :
								               priceFeedSchema.getDefaultBandOutOfHours().getBandAlias(),
                                           priceFeedSchema.getDefaultFxrBand() == null ? null :
                                               priceFeedSchema.getDefaultFxrBand().getBandId(),
                                           priceFeedSchema.getDefaultFxrBand() == null ? null :
                                               priceFeedSchema.getDefaultFxrBand().getBandAlias()
										   });
				}
				ArrayDescriptor priceFeedSchemaTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PRICE_FEED_SCHEMAS_TAB",
						connection);
				priceFeedSchemaArray = new ARRAY(priceFeedSchemaTabDescriptor, connection, priceFeedSchemaStruct);
			}

			statement.setArray(PUT_PRICE_FEED_SCHEMAS_SCHEMAS, priceFeedSchemaArray);

			OracleDatabaseHelper.execute(statement);
			

			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putPriceFeedSchemas");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putPriceFeedSchemas", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}
	
	@Override
	public void putReferenceHoldingCosts(String snapshotId,
			Map<String, NRGProductSetReferenceHoldingCosts> referenceHoldingCosts) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putReferenceHoldingCosts: " + referenceHoldingCosts.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_REFERENCE_HOLDING_COSTS_STATEMENT);
			setParameterDefaultsForPutReferenceHoldingCostsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (referenceHoldingCosts != null && !referenceHoldingCosts.isEmpty()) {
				Struct[] referenceHoldingCostsStruct = new Struct[referenceHoldingCosts.size()];
				int i = 0;
				for (String referenceHoldingCostsCode : referenceHoldingCosts.keySet()) {
					NRGProductSetReferenceHoldingCosts referenceHoldingCostsData = referenceHoldingCosts
							.get(referenceHoldingCostsCode);

					referenceHoldingCostsStruct[i++] = connection.createStruct("BI_ODS.REFERENCE_HOLDING_COSTS_OBJ",
							new Object[] { referenceHoldingCostsCode, referenceHoldingCostsData.getSchemaId(),
									referenceHoldingCostsData.getCountryOffset(),
									referenceHoldingCostsData.getMarketCode(),
									referenceHoldingCostsData.getInstrumentTypeCode(),
									referenceHoldingCostsData.getInstrumentCode() });
				}
				statement.setArray(PUT_REFERENCE_HOLDING_COSTS, ((OracleConnection) connection)
						.createOracleArray("BI_ODS.REFERENCE_HOLDING_COSTS_TAB", referenceHoldingCostsStruct));
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putReferenceHoldingCosts");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putReferenceHoldingCosts", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putHoldingCosts(String snapshotId, Map<String, NRGProductSetHoldingCosts> holdingCosts)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putHoldingCosts: " + holdingCosts.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_HOLDING_COSTS_STATEMENT);
			setParameterDefaultsForPutHoldingCostsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			if (holdingCosts != null && !holdingCosts.isEmpty()) {
				ArrayList holdingCostsStruct = new ArrayList<>();
				int i = 0;
				for (String holdingCostsCode : holdingCosts.keySet()) {
					NRGProductSetHoldingCosts holdingCostsData = holdingCosts.get(holdingCostsCode);

					Set<String> schemaCodes = new HashSet<>();

					if (holdingCostsData.getCustomerOffsets() != null
							&& holdingCostsData.getCustomerOffsets().keySet() != null
							&& !holdingCostsData.getCustomerOffsets().keySet().isEmpty()) {
						schemaCodes.addAll(holdingCostsData.getCustomerOffsets().keySet());
					}

					if (holdingCostsData.getPartnerCharges() != null
							&& holdingCostsData.getPartnerCharges().keySet() != null
							&& !holdingCostsData.getPartnerCharges().keySet().isEmpty()) {
						schemaCodes.addAll(holdingCostsData.getPartnerCharges().keySet());
					}

					for (String schemaCode : schemaCodes) {
						holdingCostsStruct.add(connection.createStruct("BI_ODS.HOLDING_COSTS_OBJ",
								new Object[] { holdingCostsCode, schemaCode,
										holdingCostsData.getPartnerCharges() == null ? null
												: holdingCostsData.getPartnerCharges().get(schemaCode),
										holdingCostsData.getCustomerOffsets() == null ? null
												: holdingCostsData.getCustomerOffsets().get(schemaCode),
										holdingCostsData.getMarketCode(), holdingCostsData.getInstrumentTypeCode(),
										holdingCostsData.getInstrumentCode() }));
					}
				}
				statement.setArray(PUT_HOLDING_COSTS, ((OracleConnection) connection)
						.createOracleArray("BI_ODS.HOLDING_COSTS_TAB", holdingCostsStruct.toArray(new Struct[] {})));
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putHoldingCosts");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putHoldingCosts", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	public void putInstrumentTypeBrokerMargins(String snapshotId,
			Map<String, List<ProductSetInstrumentTypeBrokerMarginEntity>> intrumentTypeBrokerMargins)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentTypeBrokerMargins: " + intrumentTypeBrokerMargins.size());
		}

        Connection connection = null;
		CallableStatement statement = null;
		try {
		    connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_TYPE_BROKER_MARGINS_STATEMENT);
			setParameterDefaultsForPutInstrumentTypeBrokerMarginsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

			Array instrumentTypeBrokerMarginsArray = null;

			if (intrumentTypeBrokerMargins != null && !intrumentTypeBrokerMargins.isEmpty()) {
				StructDescriptor instrumentTypeBrokerMarginObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.INST_TYPE_BROKER_MARGINS_OBJ", connection);
				List<STRUCT> instrumentTypeBrokerMarginsStruct = new ArrayList<>();
				for (String instrumentTypeCode : intrumentTypeBrokerMargins.keySet()) {
					List<ProductSetInstrumentTypeBrokerMarginEntity> entries = intrumentTypeBrokerMargins
							.get(instrumentTypeCode);
					if (entries != null) {
						for (ProductSetInstrumentTypeBrokerMarginEntity entry : entries) {
							instrumentTypeBrokerMarginsStruct
									.add(new STRUCT(instrumentTypeBrokerMarginObjDescriptor, connection,
											new Object[] { instrumentTypeCode, entry.getKey(), entry.getValue() }));
						}
					}
				}
				ArrayDescriptor instrumentTypeBrokerMarginsTabDescriptor = ArrayDescriptor
						.createDescriptor("BI_ODS.INST_TYPE_BROKER_MARGINS_TAB", connection);
				instrumentTypeBrokerMarginsArray = new ARRAY(instrumentTypeBrokerMarginsTabDescriptor, connection,
						instrumentTypeBrokerMarginsStruct.toArray());

				statement.setArray(PUT_INSTRUMENT_TYPE_BROKER_MARGINS, instrumentTypeBrokerMarginsArray);
			}

			OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("finished putInstrumentTypeBrokerMargins");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putInstrumentTypeBrokerMargins", e);
			}
			//rollbackPutProductSet();
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}


	public void putProductSettingsInstrumentSchemas(String snapshotId, String targetSchema, Collection<ProductSetInstSchemaProdSettingEntity> instProperties) throws DAOException
	{
			if (m_logger.isInfoEnabled()) {
				
				if (targetSchema == "Inst") {
				m_logger.info("putProductSettingsInstrumentSchemas: " + instProperties.size());
			    }
				else if (targetSchema == "InstCcy") {
				m_logger.info("putProductSettingsInstrumentCcySchemas: " + instProperties.size());
				}
			}

	        Connection connection = null;
			CallableStatement statement = null;
			try {
			    connection = getConnection();
				if (targetSchema == "Inst") {
				statement = connection.prepareCall(PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS_STATEMENT);
				}
				else if (targetSchema == "InstCcy") {
				statement = connection.prepareCall(PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS_STATEMENT_CCY);
				}
				
				setParameterDefaultsForPutProductSettingsInstrumentSchemasStatement(statement);
				statement.setString(PUT_USER, m_user);
				statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(m_effectiveStartTimestamp.getTime()));

				if (instProperties != null && !instProperties.isEmpty()) {
					StructDescriptor instSchemaPropertiesObjDescriptor = StructDescriptor
							.createDescriptor("BI_ODS.PRDCT_STTNGS_KEY_OBJ", connection);
					List<STRUCT> instSchemaPropertiesStruct = new ArrayList<>();
					for (ProductSetInstSchemaProdSettingEntity entry : instProperties) {
						if (entry != null) {
							if (entry.m_dimensionValue.getValues() != null) {
								for (SnapshotProperty2 property : entry.m_dimensionValue.getValues()) {
									if (property != null && property.getDecimalValue() != null) {
										instSchemaPropertiesStruct.add(new STRUCT(instSchemaPropertiesObjDescriptor,
												connection,
												new Object[] { entry.m_settingCode, 
															   entry.m_propertyKey, 
															   entry.m_instrumentCode,
															   entry.m_wrapperCode, 
														       property.getKey(),
														       property.getDecimalValue()}));
									}
									else if (property != null && property.getIntegerValue() != null) {
										instSchemaPropertiesStruct.add(new STRUCT(instSchemaPropertiesObjDescriptor,
												connection,
												new Object[] { entry.m_settingCode, 
															   entry.m_propertyKey, 
															   entry.m_instrumentCode,
															   entry.m_wrapperCode, 
														       property.getKey(),
														       property.getIntegerValue()}));
									}
								}
							}

							if (entry.m_dimensionValue.getDefaultValue() != null && entry.m_dimensionValue.getDefaultValue().getDecimalValue() != null) {
								        instSchemaPropertiesStruct.add(new STRUCT(instSchemaPropertiesObjDescriptor,
								        		connection,
								        		new Object[] { entry.m_settingCode, 
								        					   entry.m_propertyKey,
								        					   entry.m_instrumentCode,
								        					   entry.m_wrapperCode,
														       entry.m_dimensionValue.getDefaultValue().getKey(),
														       entry.m_dimensionValue.getDefaultValue().getDecimalValue()}));
							        }
							        else if (entry.m_dimensionValue.getDefaultValue() != null && entry.m_dimensionValue.getDefaultValue().getIntegerValue() != null) {
						        		instSchemaPropertiesStruct.add(new STRUCT(instSchemaPropertiesObjDescriptor,
						        				connection,
								        		new Object[] { entry.m_settingCode, 
								        					   entry.m_propertyKey,
								        					   entry.m_instrumentCode,
								        					   entry.m_wrapperCode,
														       entry.m_dimensionValue.getDefaultValue().getKey(),
														       entry.m_dimensionValue.getDefaultValue().getIntegerValue()}));
					                }						        					       
							
							if (entry.m_dimensionValue.getDimensionTypeDefaultValues() != null) {
								for (SnapshotProperty2 property : entry.m_dimensionValue.getDimensionTypeDefaultValues()) {
							        if (property != null && property.getDecimalValue() != null) {
								        instSchemaPropertiesStruct.add(new STRUCT(instSchemaPropertiesObjDescriptor,
								        		connection,
								        		new Object[] { entry.m_settingCode, 
								        					   entry.m_propertyKey,
								        					   entry.m_instrumentCode,
								        					   entry.m_wrapperCode, 
								        					   property.getKey(),
														       property.getDecimalValue()}));
							        }
							        else if (property != null && property.getIntegerValue() != null) {
						        		instSchemaPropertiesStruct.add(new STRUCT(instSchemaPropertiesObjDescriptor,
						        				connection,
								        		new Object[] { entry.m_settingCode, 
								        					   entry.m_propertyKey,
								        					   entry.m_instrumentCode,
								        					   entry.m_wrapperCode, 
														       property.getKey(),
														       property.getIntegerValue()}));														       
					                }
						        }
					        }
						}
					}
					ArrayDescriptor instSchemaPropertiesTabDescriptor = ArrayDescriptor
							.createDescriptor("BI_ODS.PRDCT_STTNGS_KEY_TAB", connection);
					Array instSchemaPropertiesArray = new ARRAY(instSchemaPropertiesTabDescriptor, connection,
							instSchemaPropertiesStruct.toArray(new STRUCT[] {}));
					statement.setArray(PUT_PRODUCT_SETTINGS_INSTRUMENT_SCHEMAS, instSchemaPropertiesArray);
				}

				OracleDatabaseHelper.execute(statement);
				if (m_logger.isInfoEnabled()) {
					
					if (targetSchema == "Inst") {
					m_logger.info("finished putProductSettingsInstrumentSchemas");
				    }
					else if (targetSchema == "InstCcy") {
						m_logger.info("finished putProductSettingsInstrumentCcySchemas");						
					}
				}
			} catch (SQLException e) {
				if (m_logger.isErrorEnabled()) {
					
					if (targetSchema == "Inst") {
					m_logger.error("Error on putProductSettingsInstrumentSchemas", e);
					 }
					else if (targetSchema == "InstCcy") {
				    m_logger.error("Error on putProductSettingsInstrumentCcySchemas", e);
					}
				}
				//rollbackPutProductSet();
				throw new DAOException(e);
			} finally {
	            rollback(connection);
	            close(null, statement, connection);     
			}
		}
        
	public void putPriceStreams(String snapshotId, Long snapshotActivationTime, Collection<ProductSetPriceStreamEntity> priceStreams) throws DAOException {
		m_logger.info("putPriceStreams: {} for snapshot id : {}", priceStreams.size(), snapshotId);

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_PRICE_STREAMS_STATEMENT);
			setParameterDefaultsForPutPriceStreamsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(snapshotActivationTime));

			Array priceStreamArray = null;
			if (priceStreams != null && !priceStreams.isEmpty()) {
				StructDescriptor priceStreamObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRICE_STREAMS_OBJ", connection);
				Struct[] priceStreamStruct = new STRUCT[priceStreams.size()];
				int i = 0;
				for (ProductSetPriceStreamEntity priceStream : priceStreams) {		
					
					Array marginLevelsArray = null;

					if (priceStream.getMarginLevels() != null && !priceStream.getMarginLevels().isEmpty()) {
						
						List<String> levels = priceStream.getMarginLevels();
						
						StructDescriptor marginLevelsObjDescriptor = StructDescriptor
								.createDescriptor("BI_ODS.PRICE_STREAMS_MARGINS_OBJ", connection);
						Struct[] marginLevelsStruct = new STRUCT[priceStream.getMarginLevels().size()];
						int j = 0;
						for (String level : levels) {				
							
							
							String[] marginString = level.split("\\|");
							String band        = marginString[0];
							String marginType  = marginString[1];
							String marginValue = marginString[2];													
							
							if(marginValue.matches("-?\\d+(\\.\\d+)?")) {
								
								marginLevelsStruct[j++] = new STRUCT(marginLevelsObjDescriptor, connection,
											new Object[] {priceStream.getPriceStreamCode(),
													      band,
														  marginType,
														  marginValue});
							}
						}						
						ArrayDescriptor marginLevelsTabDescriptor = ArrayDescriptor
								.createDescriptor("BI_ODS.PRICE_STREAMS_MARGINS_TAB", connection);
						marginLevelsArray = new ARRAY(marginLevelsTabDescriptor, connection, marginLevelsStruct);
						
					}
					priceStreamStruct[i++] = new STRUCT(priceStreamObjDescriptor, connection,
							new Object[] {priceStream.getPriceStreamCode(),
									      priceStream.getInstrumentCode(),
									      priceStream.getPriceStreamTypeId(),
									      priceStream.getLegacyInstrumentCode(),
									      priceStream.getPriceStreamTypeAlias(),
									      priceStream.getSymbolName(),
									      priceStream.getPriceNumberOfDecimals(),
									      priceStream.getCmcCashPriceStreamCode(),
									      priceStream.getMarketHourType(),
									      priceStream.getMarketHourTypeCode(),
									      NRGUtils.getYesOrNo(priceStream.isPrimary()),
									      marginLevelsArray,
									      priceStream.getPriceOrigin(),
									      priceStream.getProphetPrimaryProductCode()});
				}
				ArrayDescriptor priceStreamTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PRICE_STREAMS_TAB",
						connection);
				priceStreamArray = new ARRAY(priceStreamTabDescriptor, connection, priceStreamStruct);
			}

			statement.setArray(PUT_PRICE_STREAMS, priceStreamArray);

			OracleDatabaseHelper.execute(statement);
			commit(connection);
			m_logger.debug("putPriceStreams finished");

		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putPriceStreams", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}
    	
	public void putInstrumentPriceBandOffsets(String snapshotId, 
											  Long snapshotActivationTime, 
											  Map<String, Map<String, BigDecimal>> instrumentPriceBandOffsets, 
											  Map<String, Map<String, String>> instrumentPriceBandOffsetTypes) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentPriceBandOffsets: " + instrumentPriceBandOffsets.size());
		   }

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_PRICE_BAND_OFFSETS_STATEMENT);
			setParameterDefaultsForPutInstrumentPriceBandOffsetsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(snapshotActivationTime));
			Array priceBandArray = null;
			
			if (instrumentPriceBandOffsets != null && !instrumentPriceBandOffsets.isEmpty()) {
				StructDescriptor priceBandObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.INSTRUMENT_PRC_BNDS_KEY_OBJ", connection);
				List<STRUCT> priceBandStruct = new ArrayList<>();
				
				for (Map.Entry<String, Map<String, BigDecimal>> priceBand : instrumentPriceBandOffsets.entrySet()) {		
					
					if (priceBand != null && priceBand.getValue() != null) {		
						String[] keyString = priceBand.getKey().split("\\|");
						String instCode    = keyString[0];
						String side 	   = keyString[1];
						
						for (Map.Entry<String, BigDecimal> priceBandEntry : priceBand.getValue().entrySet()) {
							
							String OffsetType = instrumentPriceBandOffsetTypes.get(instCode).get(priceBandEntry.getKey());
							priceBandStruct.add(new STRUCT(priceBandObjDescriptor, connection,
									new Object[] {instCode,
											      side,
											      priceBandEntry.getKey(),
											      priceBandEntry.getValue(),
											      OffsetType}));
						}
					}								
				}													
					
				ArrayDescriptor priceBandTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.INSTRUMENT_PRC_BNDS_KEY_TAB",
						connection);
				priceBandArray = new ARRAY(priceBandTabDescriptor, connection, priceBandStruct.toArray(new STRUCT[] {}));			

			statement.setArray(PUT_INSTRUMENT_PRICE_BAND_OFFSETS, priceBandArray);

			}
			
			OracleDatabaseHelper.execute(statement);
			

			if (m_logger.isInfoEnabled()) {
				m_logger.info("Finished putPriceBandOffsets");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putPriceBandOffsets", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}
	
	@Override
	public void putInstrumentSwapPointBandOffsets(String snapshotId, 
				Long snapshotActivationTime, 
				Map<String, Map<String, BigDecimal>> instrumentSwapPointBandOffsets, 
				Map<String, Map<String, String>> instrumentSwapPointBandOffsetTypes) throws DAOException {
		
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentSwapPointBandOffsets: " + instrumentSwapPointBandOffsets.size());
		}
		
		Connection connection = null;
		CallableStatement statement = null;
		
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_SWAP_POINT_BAND_OFFSETS_STATEMENT);
			setParameterDefaultsForPutInstrumentSwapPointBandOffsetsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(snapshotActivationTime));
			Array SwapPointBandArray = null;
			
			if (instrumentSwapPointBandOffsets != null && !instrumentSwapPointBandOffsets.isEmpty()) {
				StructDescriptor SwapPointBandObjDescriptor = StructDescriptor
				.createDescriptor("BI_ODS.INSTRUMENT_SWP_BNDS_KEY_OBJ", connection);
				List<STRUCT> SwapPointBandStruct = new ArrayList<>();
				
				for (Map.Entry<String, Map<String, BigDecimal>> SwapPointBand : instrumentSwapPointBandOffsets.entrySet()) {		
				
					if (SwapPointBand != null && SwapPointBand.getValue() != null) {		
						String[] keyString = SwapPointBand.getKey().split("\\|");
						String instCode    = keyString[0];
						String side 	   = keyString[1];
						
						for (Map.Entry<String, BigDecimal> SwapPointBandEntry : SwapPointBand.getValue().entrySet()) {
						
							String OffsetType = instrumentSwapPointBandOffsetTypes.get(instCode).get(SwapPointBandEntry.getKey());
							SwapPointBandStruct.add(new STRUCT(SwapPointBandObjDescriptor, connection,
								new Object[] {instCode,
										      side,
										      SwapPointBandEntry.getKey(),
										      SwapPointBandEntry.getValue(),
										      OffsetType}));
						}
					}								
				}													
				
				ArrayDescriptor SwapPointBandTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.INSTRUMENT_SWP_BNDS_KEY_TAB",
				connection);
				SwapPointBandArray = new ARRAY(SwapPointBandTabDescriptor, connection, SwapPointBandStruct.toArray(new STRUCT[] {}));			
				
				statement.setArray(PUT_INSTRUMENT_SWAP_POINT_BAND_OFFSETS, SwapPointBandArray);
				
			}
			
			OracleDatabaseHelper.execute(statement);
			
			
			if (m_logger.isInfoEnabled()) {
				m_logger.info("Finished putSwapPointBandOffsets");
			}
		} catch (SQLException e) {
		if (m_logger.isErrorEnabled()) {
			m_logger.error("Error on putSwapPointBandOffsets", e);
		}
		rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}
	
    public void putInstrumentBasketRelations(String snapshotId, Long snapshotActivationTime, Collection<ProductSetInstrumentBasketRelationEntity> basketRelations) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstrumentBasketRelations: " + basketRelations.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_BASKET_RELATIONS_STATEMENT);
			setParameterDefaultsForPutInstrumentBasketRelationsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(snapshotActivationTime));
	
			Array basketRelationArray = null;
			if (basketRelations != null && !basketRelations.isEmpty()) {
				StructDescriptor basketRelationObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.INSTRUMENT_BASKET_REL_OBJ", connection);
				Struct[] basketRelationStruct = new STRUCT[basketRelations.size()];
				int i = 0;
				for (ProductSetInstrumentBasketRelationEntity basketRelation : basketRelations) {					
	
					basketRelationStruct[i++] = new STRUCT(basketRelationObjDescriptor, connection,
							new Object[] {basketRelation.getBasketRelationCode(),
									      basketRelation.getBasketInstrumentCode(),
									      basketRelation.getConstituentInstrumentCode(),
									      basketRelation.getNumberOfUnits(),
									      basketRelation.getPriceAdjustmentFactor(),
									      NRGUtils.getYesOrNo(basketRelation.getUseCmcPrice()),
									      basketRelation.getStreamCodeType(),
									      basketRelation.getFreeFloatFactor(),
									      basketRelation.getInclusionFactor(),
									      basketRelation.getPercentageWeight()});
				}
				ArrayDescriptor basketRelationTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.INSTRUMENT_BASKET_REL_TAB",
						connection);
				basketRelationArray = new ARRAY(basketRelationTabDescriptor, connection, basketRelationStruct);
			
	
			statement.setArray(PUT_INSTRUMENT_BASKET_RELATIONS, basketRelationArray);
	
			OracleDatabaseHelper.execute(statement);
			
			}
	
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putbasketRelations");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putbasketRelations", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	    }
    
    public void putInstSchemaMetadata(String snapshotId, Long activationTime, Collection<NRGProductSetInstrumentSchemaMetadata> instSchemaMetadata) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putInstSchemaMetadata: " + instSchemaMetadata.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_INSTRUMENT_SCHEMA_METADATA_STATEMENT);
			setParameterDefaultsForPutInstSchemaMetadataStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array instSchemaMetadataArray = null;
			if (instSchemaMetadata != null && !instSchemaMetadata.isEmpty()) {
				StructDescriptor instSchemaMetadataObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.INST_SCHEMA_METADATA_OBJ", connection);
				Struct[] instSchemaMetadataStruct = new STRUCT[instSchemaMetadata.size()];
				int i = 0;
				for (ProductSetInstrumentSchemaMetadata entry : instSchemaMetadata) {
				    if(entry.getName().contains("Isa")) {
				        int b = 0;
				    }
					instSchemaMetadataStruct[i++] = new STRUCT(instSchemaMetadataObjDescriptor, connection,
							new Object[] {entry.getName(),
										  entry.getNameEx(),
										  entry.getMetadata().getPrefix(),
										  entry.getMetadata().getEntity(),
										  entry.getMetadata().getProfitCentre(),
										  entry.getMetadata().getRegulatoryClassification(),
										  entry.getMetadata().getChannel(),
										  NRGUtils.getYesOrNo(entry.getMetadata().isNoDeficit()),
										  entry.getMetadata().getRegulation(),
										  entry.getMetadata().getSpecial(),
										  entry.getMetadata().getAdditionalInformation(),
										  NRGUtils.getYesOrNo(entry.getMetadata().isIsaSchema()),
									      NRGUtils.getYesOrNo(entry.isNgCfdSupported()),
										  NRGUtils.getYesOrNo(entry.isNgSbSupported()),
										  NRGUtils.getYesOrNo(entry.isNgSpeedBetCfdSupported()),
										  NRGUtils.getYesOrNo(entry.isNgSpeedBetSbSupported()),
										  NRGUtils.getYesOrNo(entry.isNgFxSupported()),
										  NRGUtils.getYesOrNo(entry.isNgInvestmentsSupported()),
										  entry.getFrmGroup(),
									      NRGUtils.getYesOrNo(entry.isNgOptionsOtcSupported()),
										  NRGUtils.getYesOrNo(entry.isNgFuturesSupported())});
				}
				ArrayDescriptor instSchemaMetadataTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.INST_SCHEMA_METADATA_TAB",
						connection);
				instSchemaMetadataArray = new ARRAY(instSchemaMetadataTabDescriptor, connection, instSchemaMetadataStruct);
			}
	
			statement.setArray(PUT_INSTRUMENT_SCHEMA_METADATA, instSchemaMetadataArray);
	
			OracleDatabaseHelper.execute(statement);
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putInstSchemaMetadata");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putInstSchemaMetadata", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	    }
    
    @Override
    public void putCurrencies(String snapshotId, Long activationTime, Collection<DbrCurrency> currencies) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putCurrencies: " + currencies.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_CURRENCIES_STATEMENT);
			setParameterDefaultsForPutCurrenciesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array currenciesArray = null;
			if (currencies != null && !currencies.isEmpty()) {
				StructDescriptor currenciesObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.CURRENCIES_OBJ", connection);
				Struct[] currenciesStruct = new STRUCT[currencies.size()];
				int i = 0;
				for (DbrCurrency entry : currencies) {
	
					currenciesStruct[i++] = new STRUCT(currenciesObjDescriptor, connection,
							new Object[] {entry.getCode(),
										  entry.getIso3(),
										  NRGUtils.getYesOrNo(entry.getIsTradingCurrency()),
										  entry.getFractionalPartRatio(),
										  entry.getFXAmountNumberOfDecimals(),
										  NRGUtils.getYesOrNo(entry.getFXGoodBusinessDaysCalendar()),
										  entry.getFXSettlementPeriod()});
				}
				ArrayDescriptor currenciesTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.CURRENCIES_TAB",
						connection);
				currenciesArray = new ARRAY(currenciesTabDescriptor, connection, currenciesStruct);
			}
	
			statement.setArray(PUT_CURRENCIES, currenciesArray);
	
			OracleDatabaseHelper.execute(statement);
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putCurrencies");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putCurrencies", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	  }
    
    @Override
    public void putCurrencyNames(String snapshotId, Long activationTime, Collection<ProductSetCurrencyName> currencyNames) throws DAOException {
    	if (m_logger.isInfoEnabled()) {
			m_logger.info("putCurrencyNames: " + currencyNames.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_CURRENCY_NAMES_STATEMENT);
			setParameterDefaultsForPutCurrencyNamesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array currencyNamesArray = null;
			if (currencyNames != null && !currencyNames.isEmpty()) {
				StructDescriptor currencyNamesObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.CURRENCY_NAMES_OBJ", connection);
				Struct[] currencyNamesStruct = new STRUCT[currencyNames.size()];
				int i = 0;
				for (ProductSetCurrencyName entry : currencyNames) {	
	
					currencyNamesStruct[i++] = new STRUCT(currencyNamesObjDescriptor, connection,
							new Object[] {entry.m_currencyCode,
										  entry.m_languageCode.replace("Language", "Default"),
										  entry.m_currencyName});
				}
				ArrayDescriptor currencyNamesTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.CURRENCY_NAMES_TAB",
						connection);
				currencyNamesArray = new ARRAY(currencyNamesTabDescriptor, connection, currencyNamesStruct);
			}
	
			statement.setArray(PUT_CURRENCY_NAMES, currencyNamesArray);
	
			OracleDatabaseHelper.execute(statement);
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putCurrencyNames");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putCurrencyNames", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
      }
 
    @Override
    public void putCurrencyMarginDefinitions(String snapshotId, Long activationTime, Collection<ProductSetCurrencyMarginDefinition> currencyMarginDefinitions) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("putCurrencyMarginDefinitions: " + currencyMarginDefinitions.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_CURRENCY_MARGIN_DEFINITIONS_STATEMENT);
			setParameterDefaultsForPutCurrencyMarginDefinitionsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array currencyMarginDefinitionsArray = null;
			if (currencyMarginDefinitions != null && !currencyMarginDefinitions.isEmpty()) {
				StructDescriptor currencyMarginDefinitionsObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.CURRENCY_MARGIN_DEF_KEY_OBJ", connection);
				Struct[] currencyMarginDefinitionsStruct = new STRUCT[currencyMarginDefinitions.size()];
				int i = 0;
				for (ProductSetCurrencyMarginDefinition entry : currencyMarginDefinitions) {	
	
					currencyMarginDefinitionsStruct[i++] = new STRUCT(currencyMarginDefinitionsObjDescriptor, connection,
							new Object[] {entry.m_currencyCode,
										  entry.m_fxSchemaCode.replace("FxSchema", "Default"),
										  entry.m_sizeLimit,	  
										  entry.m_rate});
				}
				ArrayDescriptor currencyMarginDefinitionsTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.CURRENCY_MARGIN_DEF_KEY_TAB",
						connection);
				currencyMarginDefinitionsArray = new ARRAY(currencyMarginDefinitionsTabDescriptor, connection, currencyMarginDefinitionsStruct);			
	
			statement.setArray(PUT_CURRENCY_MARGIN_DEFINITIONS, currencyMarginDefinitionsArray);
	
			OracleDatabaseHelper.execute(statement);
			
			}
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("putCurrencyMarginDefinitions");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putCurrencyMarginDefinitions", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
      }
   
    @Override
    public void putMarkets(String snapshotId, Long activationTime, Collection<ProductSetMarketEntity> markets) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("put Markets: " + markets.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_MARKETS_STATEMENT);
			setParameterDefaultsForPutMarketsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array marketsArray = null;
			if (markets != null && !markets.isEmpty()) {
				StructDescriptor marketsObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.MARKET_OBJ", connection);
				Struct[] marketsStruct = new STRUCT[markets.size()];
				int i = 0;
				for (ProductSetMarketEntity entry : markets) {	
	
					marketsStruct[i++] = new STRUCT(marketsObjDescriptor, connection,
							new Object[] {entry.getEntryCode(),										  
										  entry.getName(),
										  entry.getSymbol(),
										  entry.getCoppClarkOperatingMic(),
										  entry.getTimezoneCode(),
										  entry.getMarketType(),
										  entry.getMic()});
				}
				ArrayDescriptor marketsTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.MARKET_TAB",connection);
				marketsArray = new ARRAY(marketsTabDescriptor, connection, marketsStruct);			
	
			statement.setArray(PUT_MARKETS, marketsArray);
	
			OracleDatabaseHelper.execute(statement);			
			}
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("put Markets");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on put Markets", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
      }

    @Override
    public void putMarketHourTypes(String snapshotId, Long activationTime, Collection<ProductSetMarketHourTypeEntity> markets) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("put Market Hour Types: " + markets.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_MARKET_HOUR_TYPES_STATEMENT);
			setParameterDefaultsForPutMarketHourTypesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array marketsArray = null;
			if (markets != null && !markets.isEmpty()) {
				StructDescriptor marketsObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.MARKET_HOUR_TYPE_OBJ", connection);
				Struct[] marketsStruct = new STRUCT[markets.size()];
				int i = 0;
				for (ProductSetMarketHourTypeEntity entry : markets) {	
	
					marketsStruct[i++] = new STRUCT(marketsObjDescriptor, connection,
							new Object[] {entry.getEntryCode(),										  
										  entry.getName(),
										  entry.getProphetHourType(),
										  entry.getAssignedProductWrappersForCMCMarketHours()});
				}
				
				ArrayDescriptor marketsTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.MARKET_HOUR_TYPE_TAB",connection);
				marketsArray = new ARRAY(marketsTabDescriptor, connection, marketsStruct);			
	
			statement.setArray(PUT_MARKET_HOUR_TYPES, marketsArray);
	
			OracleDatabaseHelper.execute(statement);
			}
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("put Market Hour Types");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on put Market Hour Types", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
      }

    @Override
    public void putMarketSuspensions(String snapshotId, Long activationTime, Collection<ProductSetMarketSuspensionEntity> markets) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("put Market Suspensions: " + markets.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_MARKET_SUSPENSIONS_STATEMENT);
			setParameterDefaultsForPutMarketSuspensionsStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array marketsArray = null;
			if (markets != null && !markets.isEmpty()) {
				StructDescriptor marketsObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.MARKET_SUSPENSION_OBJ", connection);
				Struct[] marketsStruct = new STRUCT[markets.size()];
				int i = 0;
				for (ProductSetMarketSuspensionEntity entry : markets) {	
	
					marketsStruct[i++] = new STRUCT(marketsObjDescriptor, connection,
							new Object[] {entry.getEntryCode(),			
										  entry.getStartDateTime() == null ? null : new Timestamp(entry.getStartDateTime().getDate().getTime()),
										  entry.getStartDateTime() == null ? null : entry.getStartDateTime().getTimezoneCode(),
										  entry.getEndDateTime() == null ? null : new Timestamp(entry.getEndDateTime().getDate().getTime()),
										  entry.getEndDateTime() == null ? null : entry.getEndDateTime().getTimezoneCode(),							  
										  entry.getMarketHourTypeCode(),
										  entry.getMarketCode()});
				}
				ArrayDescriptor marketsTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.MARKET_SUSPENSION_TAB",connection);
				marketsArray = new ARRAY(marketsTabDescriptor, connection, marketsStruct);			
	
			statement.setArray(PUT_MARKET_SUSPENSIONS, marketsArray);
	
			OracleDatabaseHelper.execute(statement);
			}

			if (m_logger.isDebugEnabled()) {
				m_logger.debug("put Market Suspensions");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on put Market Suspensions", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
      }
    
    @Override
    public void putTimeZones(String snapshotId, Long activationTime, Collection<ProductSetTimeZoneEntity> markets) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("put TimeZones: " + markets.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_TIMEZONES_STATEMENT);
			setParameterDefaultsForPutTimeZonesStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array marketsArray = null;
			if (markets != null && !markets.isEmpty()) {
				StructDescriptor marketsObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PMS_TIME_ZONE_OBJ", connection);
				Struct[] marketsStruct = new STRUCT[markets.size()];
				int i = 0;
				for (ProductSetTimeZoneEntity entry : markets) {	
	
					marketsStruct[i++] = new STRUCT(marketsObjDescriptor, connection,
							new Object[] {entry.getEntryCode(),										  
										  entry.getName(),
										  entry.getTimeZoneReference()});
				}
				ArrayDescriptor marketsTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PMS_TIME_ZONE_TAB",connection);
				marketsArray = new ARRAY(marketsTabDescriptor, connection, marketsStruct);		
	
			statement.setArray(PUT_TIMEZONES, marketsArray);
	
			OracleDatabaseHelper.execute(statement);
			
		    }
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("put TimeZones");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on put TimeZones", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
    }

    @Override
    public void putTypicalMarketHours(String snapshotId, Long activationTime, Collection<ProductSetTypicalMarketHoursEntity> markets) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("put TypicalMarketHours: " + markets.size());
		   }
	
		Connection connection = null;
		CallableStatement statement = null;
	
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_TYPICAL_MARKET_HOURS_STATEMENT);
			setParameterDefaultsForPutTypicalMarketHoursStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((activationTime)));
	
			Array marketsArray = null;
			if (markets != null && !markets.isEmpty()) {
				StructDescriptor marketsObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.TYPICAL_MARKET_HOUR_OBJ", connection);
				Struct[] marketsStruct = new STRUCT[markets.size()];
				int i = 0;
				for (ProductSetTypicalMarketHoursEntity entry : markets) {	
	
					marketsStruct[i++] = new STRUCT(marketsObjDescriptor, connection,
							new Object[] {entry.getEntryCode(),										  										  
										  entry.getValidFrom() == null ? null : new Timestamp(entry.getValidFrom()),
										  entry.getValidTill() == null ? null : new Timestamp(entry.getValidTill()),
										  entry.getStartTime() == null ? null : new Timestamp(entry.getStartTime().getDate().getTime()),
										  entry.getStartTime() == null ? null : entry.getStartTime().getTimezoneCode(),
										  entry.getStartWeekDay(),
										  entry.getEndTime() == null ? null : new Timestamp(entry.getEndTime().getDate().getTime()),
									      entry.getEndTime() == null ? null : entry.getEndTime().getTimezoneCode(),
										  entry.getEndWeekDay(),
										  entry.getMarketHourTypeCode(),
										  entry.getMarketCode()});
				}
				ArrayDescriptor marketsTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.TYPICAL_MARKET_HOUR_TAB",connection);
				marketsArray = new ARRAY(marketsTabDescriptor, connection, marketsStruct);			
	
			statement.setArray(PUT_TYPICAL_MARKET_HOURS, marketsArray);
	
			OracleDatabaseHelper.execute(statement);
			
			}
			
			if (m_logger.isDebugEnabled()) {
				m_logger.debug("put TypicalMarketHours");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on put TypicalMarketHours", e);
			}

			rollback(connection);
			throw new DAOException(e);
		} finally {
            close(null, statement, connection);		
		}
    }

	@Override
	public void putPriceFeedSchemas2(String user, boolean isEod, List<NRGPriceFeedSchema2> priceFeedSchemas, Long asOfTime)
			throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("calling putPriceFeedSchemas2");
		}

		Connection connection = null;
		CallableStatement statement = null;

		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_PRICE_FEED_SCHEMAS_2_STATEMENT);
			setParameterDefaultsForPutPriceFeedSchemas2Statement(statement);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(asOfTime));
			statement.setString(PUT_USER, user);

			Long scheduleEodDate = NRGUtils.getEOD(asOfTime).getTime();
			String isEoD = NRGUtils.getYesOrNo((asOfTime.equals(scheduleEodDate)));

			Array priceFeedSchemaArray = null;
			if (priceFeedSchemas != null && !priceFeedSchemas.isEmpty()) {

				StructDescriptor priceFeedSchemaObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.PRICE_FEED_SCHEMAS_2_OBJ", connection);
				Struct[] priceFeedSchemaStruct = new STRUCT[priceFeedSchemas.size()];
				int i = 0;

				for (NRGPriceFeedSchema2 priceFeedSchema : priceFeedSchemas) {
							Array priceFeedSchemaOverridesArray = null;
							if (priceFeedSchema.getOverridesList() != null && !priceFeedSchema.getOverridesList().isEmpty()) {
								StructDescriptor priceFeedSchemaOverrideObjDescriptor = StructDescriptor
										.createDescriptor("BI_ODS.PRICE_FEED_SCHEMA_OVERRIDES_OBJ", connection);
								Struct[] priceFeedSchemaOverrideStruct = new STRUCT[priceFeedSchema.getOverridesList().size()];
								int j = 0;
								for (PriceFeedSchemaOverride2 priceFeedSchemaOverride : priceFeedSchema.getOverridesList()) {
									priceFeedSchemaOverrideStruct[j++] = new STRUCT(priceFeedSchemaOverrideObjDescriptor, connection,
											new Object[]{
													priceFeedSchemaOverride.getOverrideType() == null ? null : priceFeedSchemaOverride.getOverrideType().name(),
													priceFeedSchemaOverride.getCode(),
													priceFeedSchemaOverride.getPriceBand() == null ? null :
															priceFeedSchemaOverride.getPriceBand().getBandId(),
													priceFeedSchemaOverride.getPriceBand() == null ? null :
															priceFeedSchemaOverride.getPriceBand().getBandAlias(),
													priceFeedSchemaOverride.getPriceBandOutOfHours() == null ? null :
															priceFeedSchemaOverride.getPriceBandOutOfHours().getBandId(),
													priceFeedSchemaOverride.getPriceBandOutOfHours() == null ? null :
															priceFeedSchemaOverride.getPriceBandOutOfHours().getBandAlias(),
													priceFeedSchemaOverride.getPriceBandFxr() == null ? null :
															priceFeedSchemaOverride.getPriceBandFxr().getBandId(),
													priceFeedSchemaOverride.getPriceBandFxr() == null ? null :
															priceFeedSchemaOverride.getPriceBandFxr().getBandAlias(),
													priceFeedSchemaOverride.getPriceBandFxt() == null ? null :
															priceFeedSchemaOverride.getPriceBandFxt().getBandId(),
													priceFeedSchemaOverride.getPriceBandFxt() == null ? null :
															priceFeedSchemaOverride.getPriceBandFxt().getBandAlias(),
													priceFeedSchemaOverride.getSwapPointBand() == null ? null :
															priceFeedSchemaOverride.getSwapPointBand().getBandId(),
													priceFeedSchemaOverride.getSwapPointBand() == null ? null :
															priceFeedSchemaOverride.getSwapPointBand().getBandAlias(),
													priceFeedSchemaOverride.getPriceAdjustId(),
													priceFeedSchemaOverride.getPriceAdjustAlias(),
													priceFeedSchemaOverride.getPriceStreamType(),
													priceFeedSchemaOverride.getPriceStreamTypeAlias(),
													NRGUtils.getYesOrNo(priceFeedSchemaOverride.getExecuteAtOrigin()),
													priceFeedSchemaOverride.getSpreadProportion(),
													priceFeedSchemaOverride.getExecutionStyle() == null ? null : priceFeedSchemaOverride.getExecutionStyle().name()
											});
								}
								ArrayDescriptor priceFeedSchemaItemTabDescriptor = ArrayDescriptor
										.createDescriptor("BI_ODS.PRICE_FEED_SCHEMA_OVERRIDES_TAB", connection);
								priceFeedSchemaOverridesArray = new ARRAY(priceFeedSchemaItemTabDescriptor, connection,
										priceFeedSchemaOverrideStruct);
							}

							priceFeedSchemaStruct[i++] = new STRUCT(priceFeedSchemaObjDescriptor, connection,
									new Object[]{
											priceFeedSchema.getCode(),
											priceFeedSchema.getName(),
											priceFeedSchema.getLegacyId(),
											priceFeedSchema.getProductWrapper(),
											NRGUtils.getYesOrNo(isEod),
											priceFeedSchema.getDefaultPriceBand() == null ? null :
													priceFeedSchema.getDefaultPriceBand().getBandId(),
											priceFeedSchema.getDefaultPriceBand() == null ? null :
													priceFeedSchema.getDefaultPriceBand().getBandAlias(),
											priceFeedSchema.getDefaultPriceBandOutOfHours() == null ? null :
													priceFeedSchema.getDefaultPriceBandOutOfHours().getBandId(),
											priceFeedSchema.getDefaultPriceBandOutOfHours() == null ? null :
													priceFeedSchema.getDefaultPriceBandOutOfHours().getBandAlias(),
											priceFeedSchema.getDefaultPriceBandFxr() == null ? null :
													priceFeedSchema.getDefaultPriceBandFxr().getBandId(),
											priceFeedSchema.getDefaultPriceBandFxr() == null ? null :
													priceFeedSchema.getDefaultPriceBandFxr().getBandAlias(),
											priceFeedSchema.getDefaultPriceBandFxt() == null ? null :
													priceFeedSchema.getDefaultPriceBandFxt().getBandId(),
											priceFeedSchema.getDefaultPriceBandFxt() == null ? null :
													priceFeedSchema.getDefaultPriceBandFxt().getBandAlias(),
											priceFeedSchema.getDefaultSwapPointBand() == null ? null :
													priceFeedSchema.getDefaultSwapPointBand().getBandId(),
											priceFeedSchema.getDefaultSwapPointBand() == null ? null :
													priceFeedSchema.getDefaultSwapPointBand().getBandAlias(),
											priceFeedSchema.getDefaultAdjustId(),
											priceFeedSchema.getDefaultAdjustAlias(),
											priceFeedSchema.getDefaultPriceStreamType(),
											priceFeedSchema.getDefaultPriceStreamTypeAlias(),
											NRGUtils.getYesOrNo(priceFeedSchema.getExecuteAtOrigin()),
											priceFeedSchema.getDefaultSpreadProportion(),
											priceFeedSchema.getExecutionStyle() == null ? null : priceFeedSchema.getExecutionStyle().name(),
											priceFeedSchemaOverridesArray
									});
				}
					ArrayDescriptor priceFeedSchemaTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.PRICE_FEED_SCHEMAS_2_TAB",
							connection);
					priceFeedSchemaArray = new ARRAY(priceFeedSchemaTabDescriptor, connection, priceFeedSchemaStruct);

				statement.setArray(PUT_PRICE_FEED_SCHEMAS_SCHEMAS2, priceFeedSchemaArray);

				OracleDatabaseHelper.execute(statement);

			}
				if (m_logger.isDebugEnabled()) {
					m_logger.debug("putPriceFeedSchemas2");
				}

		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on putPriceFeedSchemas2", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putOptionsTradingClass(String snapshotId, Long currentSnapshotActivationTime, Collection<ProductSetOptionsTradingClassEntity> otc) throws DAOException {
		if (m_logger.isInfoEnabled()) {
			m_logger.info("put OptionsTradingClass: " + otc.size());
		}
		Connection connection = null;
		CallableStatement statement = null;
		try {
			connection = getConnection();
			statement = connection.prepareCall(PUT_OPTIONS_TRADING_CLASS_STATEMENT);
			setParameterDefaultsForPutOptionsTradingClassStatement(statement);
			statement.setString(PUT_USER, m_user);
			statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp((currentSnapshotActivationTime)));

			Array otcArray = null;
			if (otc != null && !otc.isEmpty()) {
				StructDescriptor otcObjDescriptor = StructDescriptor
						.createDescriptor("BI_ODS.OPTIONS_TRADING_CLASS_OBJ", connection);
				Struct[] otcStruct = new STRUCT[otc.size()];
				int i = 0;
				for (ProductSetOptionsTradingClassEntity entry : otc) {

					otcStruct[i++] = new STRUCT(otcObjDescriptor, connection,
							  new Object[] {entry.getCode(),
											entry.getExerciseStyle(),
											entry.getExpirationDateInfo(),
											entry.getExpirationTimeInfo(),
											entry.getSettlementDateInfo(),
											entry.getSettlementTimeInfo(),
											entry.getMultiplier(),
											entry.getMinSize(),
											entry.getSizeIncrease(),
											entry.getName(),
											entry.getProductSettingNgOptionsOtcCode(),
											entry.getSettlementType(),
							  				entry.isFindable().getDefaultValue() != null &&
													entry.isFindable().getDefaultValue().getIntegerValue() != null	?
													NRGUtils.getYesOrNoTriState(entry.isFindable().getDefaultValue().getIntegerValue()) : null,
											entry.isTradable().getDefaultValue() != null &&
													entry.isTradable().getDefaultValue().getIntegerValue() != null	?
													NRGUtils.getYesOrNoTriState(entry.isTradable().getDefaultValue().getIntegerValue()) : null,
											entry.getOptionContractMaxPositionSizeLong().getDefaultValue() != null &&
													entry.getOptionContractMaxPositionSizeLong().getDefaultValue().getDecimalValue() != null ?
													entry.getOptionContractMaxPositionSizeLong().getDefaultValue().getDecimalValue() : null,
											entry.getOptionContractMaxPositionSizeShort().getDefaultValue() != null &&
													entry.getOptionContractMaxPositionSizeShort().getDefaultValue().getDecimalValue() != null ?
													entry.getOptionContractMaxPositionSizeShort().getDefaultValue().getDecimalValue() : null,
											entry.getTradeQuantityUnitsMaximum().getDefaultValue() != null &&
													entry.getTradeQuantityUnitsMaximum().getDefaultValue().getDecimalValue() != null ?
													entry.getTradeQuantityUnitsMaximum().getDefaultValue().getDecimalValue() : null,
											entry.getPricingSymbol(),
											entry.getPricingInstanceName()});
				}
				ArrayDescriptor otcTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.OPTIONS_TRADING_CLASS_TAB",connection);
				otcArray = new ARRAY(otcTabDescriptor, connection, otcStruct);

				statement.setArray(PUT_OPTIONS_TRADING_CLASS, otcArray);

				OracleDatabaseHelper.execute(statement);
			}

			if (m_logger.isDebugEnabled()) {
				m_logger.debug("put OptionsTradingClass");
			}
		} catch (SQLException e) {
			if (m_logger.isErrorEnabled()) {
				m_logger.error("Error on put OptionsTradingClass", e);
			}
			rollback(connection); 
            throw new DAOException(e);
        } finally {
            close(null, statement, connection);
        }
	}

	@Override
	public void putInstrumentFxConversionBandOffsets(String snapshotId,
													 Long snapshotActivationTime,
													 Map<String, Map<String, BigDecimal>> instrumentFxConversionBandOffsets,
													 Map<String, Map<String, String>> instrumentFxConversionBandOffsetTypes) throws DAOException {
	if(m_logger.isInfoEnabled()) {
		m_logger.info("putInstrumentFxConversionBandOffsets: " + instrumentFxConversionBandOffsets.size());
	}

	Connection connection = null;
	CallableStatement statement = null;

	try{
		connection = getConnection();
		statement = connection.prepareCall(PUT_INSTRUMENT_FX_CONVERSION_BAND_OFFSETS_STATEMENT);
		setParameterDefaultsForPutInstrumentFxConversionBandOffsetsStatement(statement);
		statement.setString(PUT_USER, m_user);
		statement.setTimestamp(PUT_EFFECTIVE_START_TIMESTAMP, new Timestamp(snapshotActivationTime));
		Array fxConversionBandArray = null;

		if(instrumentFxConversionBandOffsets != null && !instrumentFxConversionBandOffsets.isEmpty()) {
			StructDescriptor fxConversionObjDescriptor = StructDescriptor
					.createDescriptor("BI_ODS.INSTRUMENT_FX_CON_BANDS_KEY_OBJ", connection);
			List<STRUCT> fxConversionStruct = new ArrayList<>();

			for(Map.Entry<String, Map<String, BigDecimal>> fxConversionBand: instrumentFxConversionBandOffsets.entrySet()) {

				if(fxConversionBand != null && fxConversionBand.getValue() != null) {
					String[] keyString = fxConversionBand.getKey().split("\\|");
					String instCode = keyString[0];
					String side = keyString[1];

					for (Map.Entry<String, BigDecimal> fxConversionBandEntry : fxConversionBand.getValue().entrySet()) {
						String offsetType = instrumentFxConversionBandOffsetTypes.get(instCode).get(fxConversionBandEntry.getKey());
						fxConversionStruct.add(new STRUCT(fxConversionObjDescriptor, connection,
								new Object[] {instCode,
								side,
								fxConversionBandEntry.getKey(),
								fxConversionBandEntry.getValue(),
								offsetType}));
					}
				}
			}

			ArrayDescriptor fxConversionBandTabDescriptor = ArrayDescriptor
					.createDescriptor("BI_ODS.INSTRUMENT_FX_CON_BANDS_KEY_TAB", connection);
		fxConversionBandArray = new ARRAY(fxConversionBandTabDescriptor, connection, fxConversionStruct.toArray(new STRUCT[]{}));
		statement.setArray(PUT_INSTRUMENT_FX_CONVERSION_BAND_OFFSETS, fxConversionBandArray);
		OracleDatabaseHelper.execute(statement);
			if (m_logger.isInfoEnabled()) {
				m_logger.info("Finished putFxConversionBandOffsets");
			}
		}
	}catch (SQLException e) {
		if (m_logger.isErrorEnabled()) {
			m_logger.error("Error on putFxConversionBandOffsets", e);
		}
		rollback(connection);
		throw new DAOException(e);
	} finally {
		close(null, statement, connection);
	}
	}
}