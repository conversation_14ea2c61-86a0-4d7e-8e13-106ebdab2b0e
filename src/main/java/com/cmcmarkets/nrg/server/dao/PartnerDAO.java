/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao;

import java.util.Collection;
import java.util.Set;

import com.cmcmarkets.nrg.api.model.DataId;
import com.cmcmarkets.nrg.api.model.PartnerAgentEntity;
import com.cmcmarkets.nrg.api.model.PartnerDeskEntity;
import com.cmcmarkets.nrg.api.model.PartnerEntity;
import com.cmcmarkets.nrg.api.model.Platform;

public interface PartnerDAO extends DataIdDAO
{
    public void putPartnerEntity(PartnerEntity partnerEntity);

    public void putPartnerAgentEntity(PartnerAgentEntity partnerAgentEntity);

    public void putPartnerDeskEntity(PartnerDeskEntity partnerDeskEntity);

    public Set<Long> getStubbedPartners();

    public Collection<DataId> getMissingPartnerIds(Platform platform);

    public Set<Long> getStubbedPartnerAgents();

    public Collection<DataId> getMissingPartnerAgentIds(Platform platform);

    public Set<Long> getStubbedPartnerDesks();

    public Collection<DataId> getMissingPartnerDeskIds(Platform platform);
}
