/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2013
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Struct;
import java.sql.Types;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.AbstractDataSource;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.Platform;
import com.cmcmarkets.nrg.api.model.RegionalNotificationLevelEntity;
import com.cmcmarkets.nrg.server.dao.AccountMonitoringDAO;
import com.cmcmarkets.nrg.utils.NRGUtils;

import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

public class DatabaseAccountMonitoringDAO extends AbstractDataSource implements AccountMonitoringDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabaseAccountMonitoringDAO.class.getName());

    private static final String PUT_ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_STATEMENT =
        "{ call bi_ods.nrg_account_monitoring.put_accnt_mon_rgn_notif_lvls(?, ?, ?) }";

    private static final int ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET_USER = 1;
    private static final int ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET = 2;
    private static final int ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET_PLATFORM = 3;

    public DatabaseAccountMonitoringDAO()
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.debug("Initialising " + this.getClass().getSimpleName());
        }
    }

    @Override
    public void putAccountMonitoringRegionalNotificationLevels(List<? extends RegionalNotificationLevelEntity> accountMonitoringRegionalNotificationLevels)
        throws DAOException
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info(
                "putAccountMonitoringRegionalNotificationLevels: " +
                    accountMonitoringRegionalNotificationLevels.size());
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_STATEMENT);
            setParameterDefaultsForPutAccountMonitoringRegionalNotificationLevelsSetStatement(statement);

            statement.setString(ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET_USER, "NRG");
            statement.setString(
                ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET_PLATFORM,
                Platform.NG.name().toUpperCase());

            if(accountMonitoringRegionalNotificationLevels != null &&
                !accountMonitoringRegionalNotificationLevels.isEmpty())
            {
                StructDescriptor accountValueObjDescriptor =
                    StructDescriptor.createDescriptor("BI_ODS.ACCNT_MON_RGN_NOTIF_LVLS_OBJ", connection);
                Struct[] accountValueStruct = new STRUCT[accountMonitoringRegionalNotificationLevels.size()];
                int i = 0;
                for(RegionalNotificationLevelEntity notificationLevel : accountMonitoringRegionalNotificationLevels)
                {
                    accountValueStruct[i++] = new STRUCT(
                        accountValueObjDescriptor,
                        connection,
                        new Object[]{notificationLevel.getCloseOutSchemaCode(),
                                     notificationLevel.getPrimeMarginBuffer(),
                                     NRGUtils.getYesOrNo(notificationLevel.isSchemaActive()),
                                     notificationLevel.getMinPrimeMarginType(),
                                     notificationLevel.getMinPrimeMarginValue(),
                                     notificationLevel.getStandardMarginCalculationStrategy(),
                                     notificationLevel.getUnrealizedProfitLossCalculationStrategy()});
                }
                ArrayDescriptor positionTabDescriptor =
                    ArrayDescriptor.createDescriptor("BI_ODS.ACCNT_MON_RGN_NOTIF_LVLS_TAB", connection);
                Array positionArray = new ARRAY(positionTabDescriptor, connection, accountValueStruct);
                statement.setArray(ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET, positionArray);
            }

            OracleDatabaseHelper.execute(statement);

            commit(connection);

            if(m_logger.isInfoEnabled())
            {
                m_logger.info("putAccountMonitoringRegionalNotificationLevels successful");
            }
        }
        catch(SQLException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error("Error on putAccountMonitoringRegionalNotificationLevels", e);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutAccountMonitoringRegionalNotificationLevelsSetStatement(CallableStatement statement)
        throws SQLException
    {
        statement.setNull(ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET_USER, Types.VARCHAR);
        statement.setNull(
            ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET,
            Types.ARRAY,
            "BI_ODS.ACCNT_MON_RGN_NOTIF_LVLS_TAB");
        statement.setNull(ACCOUNT_MONITORING_REGIONAL_NOTIFICATION_LEVELS_SET_PLATFORM, Types.VARCHAR);
    }

}
