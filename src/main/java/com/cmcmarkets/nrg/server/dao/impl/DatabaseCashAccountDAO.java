/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 *
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Struct;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.cashaccount.api.model.FundingAmount;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.CashAccountEntity;
import com.cmcmarkets.nrg.api.model.DataId;
import com.cmcmarkets.nrg.api.model.FinanceGeneralLedgerMappingEntity;
import com.cmcmarkets.nrg.api.model.Platform;
import com.cmcmarkets.nrg.server.dao.CashAccountDAO;
import com.cmcmarkets.nrg.utils.NRGUtils;

import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

public class DatabaseCashAccountDAO extends DatabaseDataIdDAO implements CashAccountDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabaseCashAccountDAO.class);

    private static final String PUT_CASH_ACCOUNT_STATEMENT =
        "{ call bi_ods.nrg_cash_account.put_cash_account(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?) }";
    private static final int PUT_CASH_ACCOUNT_USER = 1;
    private static final int PUT_CASH_ACCOUNT_EFFECTIVE_START_TIMESTAMP = 2;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_NUMBER = 3;
    private static final int PUT_CASH_ACCOUNT_TRADING_ACCOUNT_ID = 4;
    private static final int PUT_CASH_ACCOUNT_TRADING_ACCOUNT_TYPE = 5;
    private static final int PUT_CASH_ACCOUNT_SESSION_KEY = 6;
    private static final int PUT_CASH_ACCOUNT_IS_PROVIDER = 7;
    private static final int PUT_CASH_ACCOUNT_CASH_SCHEMA_CODE = 8;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_TYPE = 9;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_CURRENCY = 10;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_COMMENT = 11;
    private static final int PUT_CASH_ACCOUNT_REFERENCE_CODIFIER = 12;
    private static final int PUT_CASH_ACCOUNT_REFERENCE_CODE = 13;
    private static final int PUT_CASH_ACCOUNT_VERSION_NUMBER = 14;
    private static final int PUT_CASH_ACCOUNT_CREATION_TIME = 15;
    private static final int PUT_CASH_ACCOUNT_CREATION_IDENTITY_TOKEN = 16;
    private static final int PUT_CASH_ACCOUNT_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN = 17;
    private static final int PUT_CASH_ACCOUNT_UPDATE_TIME = 18;
    private static final int PUT_CASH_ACCOUNT_UPDATE_IDENTITY_TOKEN = 19;
    private static final int PUT_CASH_ACCOUNT_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN = 20;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_FUNCTION = 21;
    private static final int PUT_CASH_ACCOUNT_CHANNEL_ID = 22;
    private static final int PUT_CASH_ACCOUNT_REQUEST_ID = 23;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_LIMIT = 24;
    private static final int PUT_CASH_ACCOUNT_IS_CONDITIONAL_DEBIT_LOCKED = 25;
    private static final int PUT_CASH_ACCOUNT_IS_DELETED = 26;
    private static final int PUT_CASH_ACCOUNT_PLATFORM = 27;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_CATEGORY = 28;
    private static final int PUT_CASH_ACCOUNT_IS_SEGREGATED = 29;
    private static final int PUT_CASH_ACCOUNT_ACCOUNT_CODE = 30;

    private static final int PUT_CASH_ACCOUNT_ACCOUNT_CLASS = 31;

    private static final int PUT_CASH_ACCOUNT_ACCOUNT_GROUP_CODE = 32;

    private static final int PUT_CASH_ACCOUNT_CATEGORY = 33;

    private static final int PUT_CASH_ACCOUNT_RECONCILIATION_TYPE = 34;
    private static final int PUT_CASH_ACCOUNT_RECONCILIATION_OWNER = 35;
    private static final int PUT_CASH_ACCOUNT_COUNTER_PARTY = 36;
    private static final int PUT_CASH_ACCOUNT_STATEMENTS = 37;
    private static final String GET_STUBBED_CASH_ACCOUNTS_STATEMENT =
        "{? = call bi_ods.nrg_cash_account.get_stubbed_ids(?, ?) }";
    private static final int GET_STUBBED_CASH_ACCOUNTS_RESULT_SET = 1;
    private static final int GET_STUBBED_CASH_ACCOUNTS_BATCH_LIMIT = 2;
    private static final int STUBBED_CASH_ACCOUNTS_PLATFORM = 3;

    private static final String PUT_FINANCIAL_GENERAL_LEDGER_STATEMENT =
        "{ call bi_ods.nrg_cash_account.put_fnnc_gnrl_ldgr_mppngs(?, ?, ?) }";
    private static final int PUT_FINANCIAL_GENERAL_LEDGER_USER = 1;
    private static final int PUT_FINANCIAL_GENERAL_LEDGER_EFFECTIVE_START_TIMESTAMP = 2;
    private static final int PUT_FINANCIAL_GENERAL_LEDGER_VALUES = 3;

    private static final String PUT_FUNDING_AMOUNTS_STATEMENT =
        "{ call bi_ods.nrg_cash_account.put_funding_amounts(?, ?, ?, ?) }";
    private static final int PUT_FUNDING_AMOUNTS_USER = 1;
    private static final int PUT_FUNDING_AMOUNTS_EFFECTIVE_START_TIMESTAMP = 2;
    private static final int PUT_FUNDING_AMOUNTS_TAX_YEAR = 3;
    private static final int PUT_FUNDING_AMOUNTS_VALUES = 4;
    
    public DatabaseCashAccountDAO()
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("Initialising " + this.getClass().getSimpleName());
        }
    }

    @Override
    public Collection<DataId> getMissingCashAccountIds(Platform platform)
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("getMissingOrderIds: {}", platform);
        }

        return super.getMissingDataIds(platform, Entity.CashAccount);
    }

    @Override
    public void putCashAccountEntity(CashAccountEntity cashAccount)
    {
        if(m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + cashAccount);
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_CASH_ACCOUNT_STATEMENT);
            setParameterDefaultsForPutTradingAccountStatement(statement);

            statement.setString(PUT_CASH_ACCOUNT_USER, cashAccount.getUser());

            if(cashAccount.getEffectiveStartTimestamp() != null)
            {
                statement.setTimestamp(
                    PUT_CASH_ACCOUNT_EFFECTIVE_START_TIMESTAMP,
                    new Timestamp(cashAccount.getEffectiveStartTimestamp().getTime()));
            }
            if(cashAccount.getAccountNumber() != null)
            {
                statement.setLong(PUT_CASH_ACCOUNT_ACCOUNT_NUMBER, cashAccount.getAccountNumber());
            }
            if(cashAccount.getTradingAccountId() != null)
            {
                statement.setLong(PUT_CASH_ACCOUNT_TRADING_ACCOUNT_ID, cashAccount.getTradingAccountId());
            }
            if(cashAccount.getTradingAccountType() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_TRADING_ACCOUNT_TYPE, cashAccount.getTradingAccountType());
            }
            if(cashAccount.getSessionKey() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_SESSION_KEY, cashAccount.getSessionKey());
            }
            if(cashAccount.getCashSchemaCode() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_CASH_SCHEMA_CODE, cashAccount.getCashSchemaCode());
            }
            if(cashAccount.getAccountType() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_TYPE, cashAccount.getAccountType());
            }
            if(cashAccount.getAccountCurrency() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_CURRENCY, cashAccount.getAccountCurrency());
            }
            if(cashAccount.getAccountComment() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_COMMENT, cashAccount.getAccountComment());
            }
            if(cashAccount.getReferenceCodifier() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_REFERENCE_CODIFIER, cashAccount.getReferenceCodifier());
            }
            if(cashAccount.getReferenceCode() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_REFERENCE_CODE, cashAccount.getReferenceCode());
            }
            if(cashAccount.getVersionNumber() != null)
            {
                statement.setInt(PUT_CASH_ACCOUNT_VERSION_NUMBER, cashAccount.getVersionNumber());
            }
            if(cashAccount.getCreationTime() != null)
            {
                statement.setTimestamp(
                    PUT_CASH_ACCOUNT_CREATION_TIME,
                    new Timestamp(cashAccount.getCreationTime().getTime()));
            }
            if(cashAccount.getCreationIdentityToken() != null)
            {
                statement.setLong(PUT_CASH_ACCOUNT_CREATION_IDENTITY_TOKEN, cashAccount.getCreationIdentityToken());
            }
            if(cashAccount.getCreationOnBehalfOfIdentityToken() != null)
            {
                statement.setLong(
                    PUT_CASH_ACCOUNT_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN,
                    cashAccount.getCreationOnBehalfOfIdentityToken());
            }
            if(cashAccount.getUpdateTime() != null)
            {
                statement
                    .setTimestamp(PUT_CASH_ACCOUNT_UPDATE_TIME, new Timestamp(cashAccount.getUpdateTime().getTime()));
            }
            if(cashAccount.getUpdateIdentityToken() != null)
            {
                statement.setLong(PUT_CASH_ACCOUNT_UPDATE_IDENTITY_TOKEN, cashAccount.getUpdateIdentityToken());
            }
            if(cashAccount.getUpdateOnBehalfOfIdentityToken() != null)
            {
                statement.setLong(
                    PUT_CASH_ACCOUNT_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN,
                    cashAccount.getUpdateOnBehalfOfIdentityToken());
            }
            if(cashAccount.getAccountFunction() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_FUNCTION, cashAccount.getAccountFunction());
            }
            if(cashAccount.getChannelId() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_CHANNEL_ID, cashAccount.getChannelId());
            }
            if(cashAccount.getRequestId() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_REQUEST_ID, cashAccount.getRequestId());
            }
            if(cashAccount.getAccountLimit() != null)
            {
                statement.setBigDecimal(PUT_CASH_ACCOUNT_ACCOUNT_LIMIT, cashAccount.getAccountLimit());
            }
            if(cashAccount.isConditionalDebitLocked() != null)
            {
                statement.setString(
                    PUT_CASH_ACCOUNT_IS_CONDITIONAL_DEBIT_LOCKED,
                    NRGUtils.getYesOrNo(cashAccount.isConditionalDebitLocked()));
            }
            if(cashAccount.isDeleted() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_IS_DELETED, NRGUtils.getYesOrNo(cashAccount.isDeleted()));
            }
            if(cashAccount.getPlatform() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_PLATFORM, cashAccount.getPlatform());
            }
            if(cashAccount.getAccountCategory() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_CATEGORY, cashAccount.getAccountCategory());
            }
            if(cashAccount.isSegregated() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_IS_SEGREGATED, NRGUtils.getYesOrNo(cashAccount.isSegregated()));
            }
            if(cashAccount.getAccountCode() != null)
            {
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_CODE, cashAccount.getAccountCode());
            }
            if(cashAccount.getAccountClass() != null)
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_CLASS,cashAccount.getAccountClass());

            if(cashAccount.getAccountGroupCode() != null)
                statement.setString(PUT_CASH_ACCOUNT_ACCOUNT_GROUP_CODE,cashAccount.getAccountGroupCode());

            if(cashAccount.getCategory() != null)
                statement.setString(PUT_CASH_ACCOUNT_CATEGORY,cashAccount.getCategory());

            if(cashAccount.getReconciliationType() != null)
                statement.setString(PUT_CASH_ACCOUNT_RECONCILIATION_TYPE,cashAccount.getReconciliationType().name());

            if(cashAccount.getReconciliationOwner() != null)
                statement.setString(PUT_CASH_ACCOUNT_RECONCILIATION_OWNER,cashAccount.getReconciliationOwner());

            if(cashAccount.getCounterParty() != null)
                statement.setString(PUT_CASH_ACCOUNT_COUNTER_PARTY,cashAccount.getCounterParty());

            if(cashAccount.getStatement() != null)
                statement.setString(PUT_CASH_ACCOUNT_STATEMENTS,cashAccount.getStatement());


            OracleDatabaseHelper.execute(statement);

            commit(connection);

            if(m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + cashAccount);
            }
        }
        catch(SQLException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting " + cashAccount);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutTradingAccountStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_CASH_ACCOUNT_USER, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_NUMBER, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_TRADING_ACCOUNT_ID, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_TRADING_ACCOUNT_TYPE, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_SESSION_KEY, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_IS_PROVIDER, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_CASH_SCHEMA_CODE, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_TYPE, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_COMMENT, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_REFERENCE_CODIFIER, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_REFERENCE_CODE, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_VERSION_NUMBER, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_CREATION_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_CASH_ACCOUNT_CREATION_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_UPDATE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_CASH_ACCOUNT_UPDATE_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_FUNCTION, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_CHANNEL_ID, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_REQUEST_ID, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_LIMIT, Types.NUMERIC);
        statement.setNull(PUT_CASH_ACCOUNT_IS_CONDITIONAL_DEBIT_LOCKED, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_IS_DELETED, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_PLATFORM, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_CATEGORY, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_IS_SEGREGATED, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_CODE, Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_CLASS,Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_ACCOUNT_GROUP_CODE,Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_CATEGORY,Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_RECONCILIATION_TYPE,Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_RECONCILIATION_OWNER,Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_COUNTER_PARTY,Types.VARCHAR);
        statement.setNull(PUT_CASH_ACCOUNT_STATEMENTS,Types.VARCHAR);
    }

    @Override
    public Set<Long> getStubbedCashAccounts(Platform platform)
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("getStubbedCashAccounts: {}", platform);
        }

        Connection connection = null;
        CallableStatement statement = null;
        Set<Long> identities = new HashSet<Long>();
        try
        {
            connection = getConnection();
            statement = connection.prepareCall(GET_STUBBED_CASH_ACCOUNTS_STATEMENT);

            statement.registerOutParameter(GET_STUBBED_CASH_ACCOUNTS_RESULT_SET, OracleTypes.CURSOR);
            statement.setString(GET_STUBBED_CASH_ACCOUNTS_BATCH_LIMIT, null);
            statement.setString(STUBBED_CASH_ACCOUNTS_PLATFORM, platform.name());

            OracleDatabaseHelper.execute(statement);

            ResultSet resultSet = (ResultSet) statement.getObject(GET_STUBBED_CASH_ACCOUNTS_RESULT_SET);
            while(resultSet.next())
            {
                identities.add(resultSet.getLong("account_number"));
            }

            m_logger.info("Retrieved {} stubbed Cash Account IDs", identities.size());
        }
        catch(SQLException e)
        {
            m_logger.error("Error getting Stubbed Cash Accounts - {}", e.getMessage());
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
        return identities;
    }

    @Override
    public Collection<DataId> getMissingHedgeCashAccountIds(Platform platform)
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("getMissingHedgeBookingIds: {}", platform);
        }

        return super.getMissingDataIds(platform, Entity.HedgeBooking);
    }
    
    @Override
    public void putFinancialGeneralLedgerEntries(String user,
                                                 Date effectiveStartTimestamp,
                                                 Collection<FinanceGeneralLedgerMappingEntity> entities)
    {

        if(user == null || effectiveStartTimestamp == null || entities == null)
        {
            throw new DAOException(
                "Invalid parameters passed in: User=" + user + ", EffectiveStartTimestamp=" +
                    effectiveStartTimestamp);
        }

        if(m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + entities.size() + " FinanceGeneralLedgerMappingEntities");
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_FINANCIAL_GENERAL_LEDGER_STATEMENT);
            setParameterDefaultsForPutFinancialLedgerStatement(statement);

            statement.setString(PUT_FINANCIAL_GENERAL_LEDGER_USER, user);
            statement.setTimestamp(
            		PUT_FINANCIAL_GENERAL_LEDGER_EFFECTIVE_START_TIMESTAMP,
                new Timestamp(effectiveStartTimestamp.getTime()));

            if(!entities.isEmpty())
            {
                StructDescriptor financialGeneralLedgerEntriesObjDescriptor =
                    StructDescriptor.createDescriptor("BI_ODS.FINNC_GNRL_LDGR_MPPNGS_OBJ", connection);
                Struct[] financialGeneralLedgerEntriesStruct = new STRUCT[entities.size()];
                int i = 0;
                for(FinanceGeneralLedgerMappingEntity entity : entities)
                {
                	financialGeneralLedgerEntriesStruct[i++] = new STRUCT(
                			financialGeneralLedgerEntriesObjDescriptor,
                        connection,
                        new Object[]{entity.getId(),
                        			 entity.getVersion().getVersionNumber(), 
                        			 NRGUtils.getYesOrNo(entity.getVersion().isDeleted()),
                                     entity.getVersion().getCreationTime() == null ? null : new Timestamp(entity.getVersion().getCreationTime().getTime()),
                                     entity.getVersion().getUpdateTime() == null ? null : new Timestamp(entity.getVersion().getUpdateTime().getTime()),
                                     entity.getVersion().getCreationIdentityToken(),
                                     entity.getVersion().getCreationOnBehalfOfIdentityToken(),
                                     entity.getVersion().getUpdateIdentityToken(),
                                     entity.getVersion().getUpdateOnBehalfOfIdentityToken(),
                                     entity.getAccountNumber(),
                                     entity.getAccountType(),
                                     entity.getAccountCode(),
                                     entity.getAccountCategory(),
                                     entity.getCashAccountingSchemaCode(),
                                     entity.getIsSegregated(),
                                     entity.getPrimaryLegalEntity(),
                                     entity.getPrimaryAccount(),
                                     entity.getPrimaryDepartment(),
                                     entity.getPrimaryProfitCentre(),
                                     entity.getPrimaryAffiliate(),
                                     entity.getB2BBSLegalEntity(),
                                     entity.getB2BBSAccount(),
                                     entity.getB2BBSDepartment(),
                                     entity.getB2BBSProfitCentre(),
                                     entity.getB2BBSAffiliate(),
                                     entity.getB2BPnLLegalEntity(),
                                     entity.getB2BPnLAccount(),
                                     entity.getB2BPnLDepartment(),
                                     entity.getB2BPnLProfitCentre(),
                                     entity.getB2BPnLAffiliate(),
                                     entity.getRetainedEarningsAccount(),
                                     entity.getTrialBalanceProfitCentreSplit()
                		  });
                }
                ArrayDescriptor financialGeneralLedgerEntriesTabDescriptor =
                    ArrayDescriptor.createDescriptor("BI_ODS.FINNC_GNRL_LDGR_MPPNGS_TAB", connection);
                Array financialGeneralLedgerEntriesArray = new ARRAY(
                		financialGeneralLedgerEntriesTabDescriptor,
                    connection,
                    financialGeneralLedgerEntriesStruct);
                statement.setArray(PUT_FINANCIAL_GENERAL_LEDGER_VALUES, financialGeneralLedgerEntriesArray);
            }

            OracleDatabaseHelper.execute(statement);

            commit(connection);

            if(m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + entities.size() + " FinancialGeneralLedgerEntries");
            }
        }
        catch(SQLException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting FinancialGeneralLedgerEntries:" + e.getMessage());
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutFinancialLedgerStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_FINANCIAL_GENERAL_LEDGER_USER, Types.VARCHAR);
        statement.setNull(PUT_FINANCIAL_GENERAL_LEDGER_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_FINANCIAL_GENERAL_LEDGER_VALUES, Types.ARRAY, "BI_ODS.FINNC_GNRL_LDGR_MPPNGS_TAB");
    }
    
    private void setParameterDefaultsForPutFundingAmountsStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_FUNDING_AMOUNTS_USER, Types.VARCHAR);
        statement.setNull(PUT_FUNDING_AMOUNTS_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_FUNDING_AMOUNTS_TAX_YEAR, Types.NUMERIC);
        statement.setNull(PUT_FUNDING_AMOUNTS_VALUES, Types.ARRAY, "BI_ODS.FUNDING_AMOUNTS_TAB");
    }
    
    @Override
    public void putFundingAmounts(Collection<FundingAmount> fundingAmounts,
                                  Date effectiveStartTimestamp, 
                                  Integer taxYear) {
        if (m_logger.isInfoEnabled()) {
            m_logger.info("putFundingAmounts: " + fundingAmounts.size());
        }
        
        Connection m_connection = null;
        CallableStatement statement  = null;
        try {
            m_connection = getConnection();
            statement = m_connection.prepareCall(PUT_FUNDING_AMOUNTS_STATEMENT);
            setParameterDefaultsForPutFundingAmountsStatement(statement);
            statement.setString(PUT_FUNDING_AMOUNTS_USER, "NRG");
            statement.setInt(PUT_FUNDING_AMOUNTS_TAX_YEAR, taxYear);
            statement.setTimestamp(PUT_FUNDING_AMOUNTS_EFFECTIVE_START_TIMESTAMP,
            new Timestamp(effectiveStartTimestamp.getTime()));
            
            if (fundingAmounts != null && !fundingAmounts.isEmpty()) {
                StructDescriptor fundingAmountObjDescriptor = StructDescriptor.createDescriptor("BI_ODS.FUNDING_AMOUNTS_OBJ",
                        m_connection);
                Struct[] fundingAmountStruct = new STRUCT[fundingAmounts.size()];
                int i = 0;
                for (FundingAmount fundingAmount : fundingAmounts) {
                    
                    Array fundingAmountAccountsArray          = null;
                    if (fundingAmount.getTradingAccountCodes() != null && !fundingAmount.getTradingAccountCodes().isEmpty()) {
                        
                        List<Long> fundingAmountAccounts = fundingAmount.getTradingAccountCodes();
                        
                        StructDescriptor fundingAmountAccountsObjDescriptor = StructDescriptor
                                .createDescriptor("BI_ODS.FUNDING_AMOUNT_ACCOUNTS_OBJ", m_connection);
                        Struct[] fundingAmountAccountsStruct = new STRUCT[fundingAmount.getTradingAccountCodes().size()];
                        int j = 0;
                        for (Long account : fundingAmountAccounts) {                          
                             fundingAmountAccountsStruct[j++] = new STRUCT(fundingAmountAccountsObjDescriptor, m_connection,
                                        new Object[] {account});
                            }                       
                        ArrayDescriptor fundingAmountAccountsTabDescriptor = ArrayDescriptor
                                .createDescriptor("BI_ODS.FUNDING_AMOUNT_ACCOUNTS_TAB", m_connection);
                        fundingAmountAccountsArray = new ARRAY(fundingAmountAccountsTabDescriptor, m_connection, fundingAmountAccountsStruct);
                    }
                                
                    fundingAmountStruct[i++] = new STRUCT(fundingAmountObjDescriptor, m_connection, new Object[] {
                        fundingAmount.getFundingGroupId(),
                        fundingAmount.getFundingAllowance(),
                        fundingAmount.getUsedFundingAllowance(),
                        fundingAmount.getAvailableFundingAmount(),
                        fundingAmount.getTransactionPaymentId(),
                        fundingAmount.getTransactionBookingNumber(),
                        fundingAmount.getTransactionUsedFundingAllowanceChange(),
                        NRGUtils.getYesOrNo(fundingAmount.isTransactionIsCancellation()),
                        fundingAmount.getTransactionComment(),
                        fundingAmount.getTransactionTime() == null ? null : new Timestamp(fundingAmount.getTransactionTime().getTime()),                            
                        fundingAmount.getSubscriptionTime() == null ? null : new Timestamp(fundingAmount.getSubscriptionTime().getTime()),   
                        fundingAmountAccountsArray,
                        fundingAmount.getAllowanceAdjustmentType() == null ? null : fundingAmount.getAllowanceAdjustmentType().name()
                    });
                }
                
            ArrayDescriptor fundingAmountTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.FUNDING_AMOUNTS_TAB",
                    m_connection);
            Array fundingAmountArray = new ARRAY(fundingAmountTabDescriptor, m_connection, fundingAmountStruct);
            statement.setArray(PUT_FUNDING_AMOUNTS_VALUES, fundingAmountArray);
        }
        OracleDatabaseHelper.execute(statement);
        if (m_logger.isInfoEnabled()) {
            m_logger.info("finished putFundingAmountEntities");
        }
        commit(m_connection);
    } 
    catch (SQLException e) {
        if (m_logger.isErrorEnabled()) {
            m_logger.error("Error on putFundingAmountEntities", e);
        }
        throw new DAOException(e);
    } 
    finally {
        rollback(m_connection);
        close(null, statement, m_connection);
    }
}

    
}
