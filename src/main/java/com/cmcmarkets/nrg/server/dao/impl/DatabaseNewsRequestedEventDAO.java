/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.AbstractDataSource;
import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.NewsRequestedEventEntity;
import com.cmcmarkets.nrg.server.dao.NewsRequestedEventDAO;
import com.cmcmarkets.nrg.utils.SessionHashUtils;
import com.cmcmarkets.nrg.utils.SessionHashUtils.InvalidSessionFormatException;

public class DatabaseNewsRequestedEventDAO extends AbstractDataSource implements NewsRequestedEventDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabaseNewsRequestedEventDAO.class);

    private static final String PUT_NEWS_REQUESTED_EVENT_STATEMENT = "{ call bi_ods.nrg_news_event.put_news_event(?, ?, ?, ?, ?, ?, ?, ?, ?, ?) }";
    private static final int PUT_NEWS_REQUESTED_EVENT_USER = 1;
    private static final int PUT_NEWS_REQUESTED_EVENT_EFFECTIVE_START_TIMESTAMP = 2;
    private static final int PUT_NEWS_REQUESTED_EVENT_TIME = 3;
    private static final int PUT_NEWS_REQUESTED_EVENT_SESSION_KEY = 4;
    private static final int PUT_NEWS_REQUESTED_EVENT_SENDER_IDENTITY_ID = 5;
    private static final int PUT_NEWS_REQUESTED_EVENT_ON_BEHALF_OF_IDENTITY_ID = 6;
    private static final int PUT_NEWS_REQUESTED_EVENT_REQUEST_ID = 7;
    private static final int PUT_NEWS_REQUESTED_EVENT_CHANNEL_ID = 8;
    private static final int PUT_NEWS_REQUESTED_EVENT_ACCOUNT_FUNCTION = 9;
    private static final int PUT_NEWS_REQUESTED_EVENT_PROVIDER = 10;

    private static final String PROVIDER = "REUTERS";

    public DatabaseNewsRequestedEventDAO()
    {
        m_logger.info("Initialising {}", this.getClass().getSimpleName());
    }

    @Override
    public void putNewsRequestedEventEntity(NewsRequestedEventEntity newsRequestedEventEntity)
    {
        m_logger.debug("Putting {}", newsRequestedEventEntity);

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_NEWS_REQUESTED_EVENT_STATEMENT);
            setParameterDefaultsForPutNewsEventStatement(statement);

            if (newsRequestedEventEntity.getUser() != null)
            {
                statement.setString(PUT_NEWS_REQUESTED_EVENT_USER, newsRequestedEventEntity.getUser());
            }
            if (newsRequestedEventEntity.getEffectiveStartTimestamp() != null)
            {
                statement.setTimestamp(PUT_NEWS_REQUESTED_EVENT_EFFECTIVE_START_TIMESTAMP, new Timestamp(newsRequestedEventEntity.getEffectiveStartTimestamp().getTime()));
            }
            if (newsRequestedEventEntity.getSentTime() != null)
            {
                statement.setTimestamp(PUT_NEWS_REQUESTED_EVENT_TIME, new Timestamp(newsRequestedEventEntity.getSentTime()));
            }
            if (newsRequestedEventEntity.getUserSessionKey() != null)
            {
                statement.setString(PUT_NEWS_REQUESTED_EVENT_SESSION_KEY, SessionHashUtils.evaluateHashedSession(newsRequestedEventEntity.getUserSessionKey()));
            }
            if (newsRequestedEventEntity.getIdentity() != null)
            {
                statement.setLong(PUT_NEWS_REQUESTED_EVENT_SENDER_IDENTITY_ID, newsRequestedEventEntity.getIdentity().getId());
            }
            if (newsRequestedEventEntity.getOnBehalfOfIdentity() != null)
            {
                statement.setLong(PUT_NEWS_REQUESTED_EVENT_ON_BEHALF_OF_IDENTITY_ID, newsRequestedEventEntity.getOnBehalfOfIdentity().getId());
            }
            if (newsRequestedEventEntity.getRequestId() != null)
            {
                statement.setLong(PUT_NEWS_REQUESTED_EVENT_REQUEST_ID, newsRequestedEventEntity.getIdentity().getId());
            }
            if (newsRequestedEventEntity.getChannel() != null)
            {
                statement.setInt(PUT_NEWS_REQUESTED_EVENT_CHANNEL_ID, newsRequestedEventEntity.getChannel().getID());
            }
            if (newsRequestedEventEntity.getFunction() != null)
            {
                statement.setString(PUT_NEWS_REQUESTED_EVENT_ACCOUNT_FUNCTION, newsRequestedEventEntity.getFunction());
            }
            statement.setString(PUT_NEWS_REQUESTED_EVENT_PROVIDER, PROVIDER);

            OracleDatabaseHelper.execute(statement);
            commit(connection);

            m_logger.debug("Put {}", newsRequestedEventEntity);
        }
        catch (SQLException | InvalidSessionFormatException e)
        {
            m_logger.error("Error putting {}", newsRequestedEventEntity);
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutNewsEventStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_USER, Types.VARCHAR);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_SESSION_KEY, Types.VARCHAR);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_SENDER_IDENTITY_ID, Types.NUMERIC);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_ON_BEHALF_OF_IDENTITY_ID, Types.NUMERIC);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_REQUEST_ID, Types.VARCHAR);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_CHANNEL_ID, Types.NUMERIC);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_ACCOUNT_FUNCTION, Types.VARCHAR);
        statement.setNull(PUT_NEWS_REQUESTED_EVENT_PROVIDER, Types.VARCHAR);
    }
}
