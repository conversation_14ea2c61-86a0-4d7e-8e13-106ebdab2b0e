/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Struct;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.DataId;
import com.cmcmarkets.nrg.api.model.Platform;
import com.cmcmarkets.nrg.api.model.PositionTransactionEntity;
import com.cmcmarkets.nrg.server.dao.PositionTransactionDAO;
import com.cmcmarkets.nrg.utils.NRGUtils;
import com.cmcmarkets.nrg.utils.SessionHashUtils;
import com.cmcmarkets.nrg.utils.SessionHashUtils.InvalidSessionFormatException;

import oracle.jdbc.OracleTypes;
import oracle.sql.ARRAY;
import oracle.sql.ArrayDescriptor;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

public class DatabasePositionTransactionDAO extends DatabaseDataIdDAO implements PositionTransactionDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabasePositionTransactionDAO.class);

    private static final String POSITION_TRANSACTION_IDS_STATEMENT = "{? = call bi_ods.nrg_position_transaction.get_position_transaction_ids(?,?) }";
    private static final int POSITION_TRANSACTION_IDS_RESULT_SET = 1;
    private static final int POSITION_TRANSACTION_IDS_FROM_TIMESTAMP = 2;
    private static final int POSITION_TRANSACTION_IDS_TO_TIMESTAMP = 3;

    private static final String PUT_POSITION_TRANSACTION_STATEMENT =
        "{ call bi_ods.nrg_position_transaction.put_position_transaction(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";

    private static final int PUT_POSITION_TRANSACTION_USER = 1;
    private static final int PUT_POSITION_TRANSACTION_EFFECTIVE_START_TIMESTAMP = 2;
    private static final int PUT_POSITION_TRANSACTION_TRANSACTION_ID = 3;
    private static final int PUT_POSITION_TRANSACTION_OPEN_TRADE_ID = 4;
    private static final int PUT_POSITION_TRANSACTION_TRADE_ID = 5;
    private static final int PUT_POSITION_TRANSACTION_BOOKING_NUMBER = 6;
    private static final int PUT_POSITION_TRANSACTION_PLATFORM = 7;
    private static final int PUT_POSITION_TRANSACTION_PUBLISH_TIME = 8;
    private static final int PUT_POSITION_TRANSACTION_EVENT_TIME = 9;
    private static final int PUT_POSITION_TRANSACTION_VERSION_NUMBER = 10;
    private static final int PUT_POSITION_TRANSACTION_IS_DELETED = 11;
    private static final int PUT_POSITION_TRANSACTION_CREATION_TIME = 12;
    private static final int PUT_POSITION_TRANSACTION_CREATION_IDENTITY_TOKEN = 13;
    private static final int PUT_POSITION_TRANSACTION_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN = 14;
    private static final int PUT_POSITION_TRANSACTION_UPDATE_TIME = 15;
    private static final int PUT_POSITION_TRANSACTION_UPDATE_IDENTITY_TOKEN = 16;
    private static final int PUT_POSITION_TRANSACTION_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN = 17;
    private static final int PUT_POSITION_TRANSACTION_SESSION_ID = 18;
    private static final int PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_CODE = 19;
    private static final int PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_TYPE = 20;
    private static final int PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_FUNCTION = 21;
    private static final int PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_CODIFIER = 22;
    private static final int PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_PRIMARY_CURRENCY = 23;
    private static final int PUT_POSITION_TRANSACTION_ORDER_ID = 24;
    private static final int PUT_POSITION_TRANSACTION_PRODUCT_INSTRUMENT_CODE = 25;
    private static final int PUT_POSITION_TRANSACTION_PRODUCT_WRAPPER_CODE = 26;
    private static final int PUT_POSITION_TRANSACTION_PRODUCT_GENERATION = 27;
    private static final int PUT_POSITION_TRANSACTION_PRODUCT_POINT_MULTIPLIER = 28;
    private static final int PUT_POSITION_TRANSACTION_PRODUCT_CURRENCY = 29;
    private static final int PUT_POSITION_TRANSACTION_PRODUCT_FRACTIONAL_PART_RATIO = 30;
    private static final int PUT_POSITION_TRANSACTION_IS_PRODUCT_CURRENCY_IN_FRACTIONAL_PARTS = 31;
    private static final int PUT_POSITION_TRANSACTION_CHANNEL_ID = 32;
    private static final int PUT_POSITION_TRANSACTION_REQUEST_ID = 33;
    private static final int PUT_POSITION_TRANSACTION_TRANSACTION_TIME = 34;
    private static final int PUT_POSITION_TRANSACTION_TRANSACTION_TYPE = 35;
    private static final int PUT_POSITION_TRANSACTION_REF_CODIFIER = 36;
    private static final int PUT_POSITION_TRANSACTION_REF_CODE = 37;
    private static final int PUT_POSITION_TRANSACTION_IS_AUTOMATICALLY_ROLLED = 38;
    private static final int PUT_POSITION_TRANSACTION_DIRECTION = 39;
    private static final int PUT_POSITION_TRANSACTION_QUANTITY = 40;
    private static final int PUT_POSITION_TRANSACTION_QUANTITY_DESIGNATOR = 41;
    private static final int PUT_POSITION_TRANSACTION_AMOUNT = 42;
    private static final int PUT_POSITION_TRANSACTION_AMOUNT_CURRENCY = 43;
    private static final int PUT_POSITION_TRANSACTION_AMOUNT_IN_TRADING_ACCOUNT_PRIMARY_CURRENCY = 44;
    private static final int PUT_POSITION_TRANSACTION_OPEN_TRADE_PRICE = 45;
    private static final int PUT_POSITION_TRANSACTION_TRADE_AMT_FX_RATE = 46;
    private static final int PUT_POSITION_TRANSACTION_OPEN_TRADE_TIME = 47;
    private static final int PUT_POSITION_TRANSACTION_RECORD_SOURCE = 48;
    private static final int PUT_POSITION_TRANSACTION_ROLLED_OPEN_TRADE_ID = 49;
    private static final int PUT_POSITION_TRANSACTION_TRADE_APP_TO_UNITS = 50;
    private static final int PUT_POSITION_TRANSACTION_LOAD_LATEST_POSITIONS = 51;
    private static final int PUT_POSITION_TRANSACTION_EXECUTION_TYPE = 52;
    private static final int PUT_POSITION_TRANSACTION_OPENING_TRADE_INSTRUMENT_PRICE = 53;
    private static final int PUT_POSITION_TRANSACTION_TRADING_SCOPE = 54;
    private static final int PUT_POSITION_TRANSACTION_BINARY_TYPE = 55;
    private static final int PUT_POSITION_TRANSACTION_SETTLE_TIME = 56;
    private static final int PUT_POSITION_TRANSACTION_TENOR = 57;
    private static final int PUT_POSITION_TRANSACTION_STRIKE_PRICE = 58;
    private static final int PUT_POSITION_TRANSACTION_STRIKE_PRICE_ADDITIONAL = 59;
    private static final int PUT_POSITION_TRANSACTION_TENOR_START_TIME = 60;
    private static final int PUT_POSITION_TRANSACTION_BINARY_RESULT = 61;
    private static final int PUT_POSITION_TRANSACTION_ALLOCATION_TRADING_INSTRUMENT_SCHEMA = 62;
    private static final int PUT_POSITION_TRANSACTION_OPEN_TRADE_INSTRUMENT_AMOUNT = 63;
    private static final int PUT_POSITION_TRANSACTION_FORCED_MARGIN_FX_RATE = 64;
    private static final int PUT_POSITION_TRANSACTION_OPEN_ACCRUED_TURNOVER_IN_ACCOUNT_CURRENCY = 65;
    private static final int PUT_POSITION_TRANSACTION_IS_PAIR_CURRENCY_IN_FRACTIONAL_PARTS = 66;
    private static final int PUT_POSITION_TRANSACTION_VALUE_DATE = 67;
    private static final int PUT_POSITION_TRANSACTION_PAIR_CURRENCY = 68;
    private static final int PUT_POSITION_TRANSACTION_PRIMARY_CURRENCY = 69;
    private static final int PUT_POSITION_TRANSACTION_SECONDARY_CURRENCY = 70;
    private static final int PUT_POSITION_TRANSACTION_PRIMARY_AMOUNT = 71;
    private static final int PUT_POSITION_TRANSACTION_SECONDARY_AMOUNT = 72;
    private static final int PUT_POSITION_TRANSACTION_OPEN_TRADE_MARGIN_PERCENTAGE = 73;
    private static final int PUT_POSITION_TRANSACTION_ACCRUED_FEES_INSTRUMENT_CCY = 74;
    private static final int PUT_POSITION_TRANSACTION_ACCRUED_FEES_PRIMARY_CCY = 75;
    private static final int PUT_POSITION_TRANSACTION_OPENING_TRADE_INST_PRICE_PRIMARY_CCY = 76;
    private static final int PUT_POSITION_TRANSACTION_PORTFOLIO_SWAP_PERIOD_SEQ_NUMBER = 77;
    private static final int PUT_POSITION_TRANSACTION_OPTION_CODE = 78;
    private static final int PUT_POSITION_TRANSACTION_TRANSACTION_CLIENT_COSTS = 79;
    private static final int PUT_POSITION_TRANSACTION_OPEN_ACCRUED_HOLDING_COSTS = 80;
    private static final int PUT_POSITION_TRANSACTION_ORIGINAL_ORDER_ID = 81;
    private static final int PUT_POSITION_TRANSACTION_STRATEGY_ID = 82;
    private static final int PUT_POSITION_TRANSACTION_STRATEGY_TYPE = 83;

    private static final String PUT_POSITION_TRANSACTIONS_STATEMENT = "{ call bi_ods.nrg_position_transaction.put_position_transactions(?) }";

    private static final int PUT_POSITION_TRANSACTIONS_ENTITIES = 1;

    public DatabasePositionTransactionDAO()
    {
        if (m_logger.isInfoEnabled())
        {
            m_logger.info("Initialising " + this.getClass().getSimpleName());
        }
    }

    @Override
    public Set<String> getPositionTransactionIds(Platform platform, java.util.Date from, java.util.Date to) throws DAOException
    {
        if (m_logger.isDebugEnabled())
        {
            m_logger.debug("getPositionTransactionIds(" + platform + ", " + from + ", " + to + ")");
        }

        Connection connection = null;
        CallableStatement statement = null;
        Set<String> positionTransactionIds = new HashSet<String>();
        try
        {
            connection = getConnection();
            statement = connection.prepareCall(POSITION_TRANSACTION_IDS_STATEMENT);

            if (platform == null || from == null || to == null)
            {
                throw new DAOException("Invalid parameters passed: Platform=" + platform + ", From=" + from + ", To=" + to);
            }

            statement.registerOutParameter(POSITION_TRANSACTION_IDS_RESULT_SET, OracleTypes.CURSOR);
            // statement.setString(POSITION_TRANSACTION_IDS_PLATFORM, platform.name());
            statement.setTimestamp(POSITION_TRANSACTION_IDS_FROM_TIMESTAMP, new Timestamp(from.getTime()));
            statement.setTimestamp(POSITION_TRANSACTION_IDS_TO_TIMESTAMP, new Timestamp(to.getTime()));

            OracleDatabaseHelper.execute(statement);

            ResultSet resultSet = (ResultSet) statement.getObject(POSITION_TRANSACTION_IDS_RESULT_SET);
            while (resultSet.next())
            {
                positionTransactionIds.add(resultSet.getString("transaction_id"));
            }

            if (m_logger.isInfoEnabled())
            {
                m_logger.info("Retrieved " + positionTransactionIds.size() + " position transactions ids");
            }
        }
        catch (SQLException e)
        {
            if (m_logger.isErrorEnabled())
            {
                m_logger.error("Error getting position transactions ids using (" + platform + ", " + from + ", " + to + ")");
            }
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
        return positionTransactionIds;
    }

    @Override
    public void putPositionTransactionEntity(PositionTransactionEntity positionTransactionEntity) throws DAOException
    {
        if (m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + positionTransactionEntity);
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_POSITION_TRANSACTION_STATEMENT);
            setParameterDefaultsForPutPositionTransactionStatement(statement);

            if (positionTransactionEntity.getUser() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_USER, positionTransactionEntity.getUser());
            }
            if (positionTransactionEntity.getEffectiveStartTimestamp() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_EFFECTIVE_START_TIMESTAMP, new Timestamp(positionTransactionEntity.getEffectiveStartTimestamp().getTime()));
            }
            if (positionTransactionEntity.getTransactionId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRANSACTION_ID, positionTransactionEntity.getTransactionId());
            }
            if (positionTransactionEntity.getOpenTradeId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_OPEN_TRADE_ID, positionTransactionEntity.getOpenTradeId());
            }
            if (positionTransactionEntity.getTradeId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRADE_ID, positionTransactionEntity.getTradeId());
            }
            if (positionTransactionEntity.getTransactionBookingId() != null && !positionTransactionEntity.getTransactionBookingId().equals(-1L))
            {
                statement.setLong(PUT_POSITION_TRANSACTION_BOOKING_NUMBER, positionTransactionEntity.getTransactionBookingId());
            }
            if (positionTransactionEntity.getPlatform() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_PLATFORM, positionTransactionEntity.getPlatform());
            }
            if (positionTransactionEntity.getPublishTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_PUBLISH_TIME, new Timestamp(positionTransactionEntity.getPublishTime().getTime()));
            }
            if (positionTransactionEntity.getEventTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_EVENT_TIME, new Timestamp(positionTransactionEntity.getEventTime().getTime()));
            }
            if (positionTransactionEntity.getVersionNumber() != null)
            {
                statement.setInt(PUT_POSITION_TRANSACTION_VERSION_NUMBER, positionTransactionEntity.getVersionNumber());
            }
            if (positionTransactionEntity.isDeleted() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_IS_DELETED, NRGUtils.getYesOrNo(positionTransactionEntity.isDeleted()));
            }
            if (positionTransactionEntity.getCreationTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_CREATION_TIME, new Timestamp(positionTransactionEntity.getCreationTime().getTime()));
            }
            if (positionTransactionEntity.getCreationIdentityToken() != null)
            {
                statement.setLong(PUT_POSITION_TRANSACTION_CREATION_IDENTITY_TOKEN, positionTransactionEntity.getCreationIdentityToken());
            }
            if (positionTransactionEntity.getCreationOnBehalfOfIdentityToken() != null)
            {
                statement.setLong(PUT_POSITION_TRANSACTION_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN, positionTransactionEntity.getCreationOnBehalfOfIdentityToken());
            }
            if (positionTransactionEntity.getUpdateTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_UPDATE_TIME, new Timestamp(positionTransactionEntity.getUpdateTime().getTime()));
            }
            if (positionTransactionEntity.getUpdateIdentityToken() != null)
            {
                statement.setLong(PUT_POSITION_TRANSACTION_UPDATE_IDENTITY_TOKEN, positionTransactionEntity.getUpdateIdentityToken());
            }
            if (positionTransactionEntity.getUpdateOnBehalfOfIdentityToken() != null)
            {
                statement.setLong(PUT_POSITION_TRANSACTION_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN, positionTransactionEntity.getUpdateOnBehalfOfIdentityToken());
            }
            if (positionTransactionEntity.getSessionId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_SESSION_ID, SessionHashUtils.evaluateHashedSession(positionTransactionEntity.getSessionId()));
            }
            if (positionTransactionEntity.getTradingAccountCode() != null)
            {
                statement.setLong(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_CODE, positionTransactionEntity.getTradingAccountCode());
            }
            if (positionTransactionEntity.getTradingAccountType() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_TYPE, positionTransactionEntity.getTradingAccountType());
            }
            if (positionTransactionEntity.getFunction() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_FUNCTION, positionTransactionEntity.getFunction());
            }
            if (positionTransactionEntity.getTradingAccountCodifier() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_CODIFIER, positionTransactionEntity.getTradingAccountCodifier());
            }
            if (positionTransactionEntity.getTradingAccountPrimaryCurrency() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_PRIMARY_CURRENCY, positionTransactionEntity.getTradingAccountPrimaryCurrency());
            }
            if (positionTransactionEntity.getOrderId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_ORDER_ID, positionTransactionEntity.getOrderId());
            }
            if (positionTransactionEntity.getProductInstrumentCode() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_PRODUCT_INSTRUMENT_CODE, positionTransactionEntity.getProductInstrumentCode());
            }
            if (positionTransactionEntity.getProductWrapperCode() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_PRODUCT_WRAPPER_CODE, positionTransactionEntity.getProductWrapperCode());
            }
            if (positionTransactionEntity.getProductGeneration() != null)
            {
                statement.setInt(PUT_POSITION_TRANSACTION_PRODUCT_GENERATION, positionTransactionEntity.getProductGeneration());
            }
            if (positionTransactionEntity.getProductPointMultiplier() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_PRODUCT_POINT_MULTIPLIER, positionTransactionEntity.getProductPointMultiplier());
            }
            if (positionTransactionEntity.getProductCurrency() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_PRODUCT_CURRENCY, positionTransactionEntity.getProductCurrency());
            }
            if (positionTransactionEntity.getProductFractionalPartRatio() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_PRODUCT_FRACTIONAL_PART_RATIO, positionTransactionEntity.getProductFractionalPartRatio());
            }
            if (positionTransactionEntity.isCurrencyInFractionalParts() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_IS_PRODUCT_CURRENCY_IN_FRACTIONAL_PARTS, NRGUtils.getYesOrNo(positionTransactionEntity.isCurrencyInFractionalParts()));
            }
            if (positionTransactionEntity.getChannelId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_CHANNEL_ID, positionTransactionEntity.getChannelId());
            }
            if (positionTransactionEntity.getRequestId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_REQUEST_ID, positionTransactionEntity.getRequestId());
            }
            if (positionTransactionEntity.getTransactionTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_TRANSACTION_TIME, new Timestamp(positionTransactionEntity.getTransactionTime().getTime()));
            }
            if (positionTransactionEntity.getTransactionType() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRANSACTION_TYPE, positionTransactionEntity.getTransactionType());
            }
            if (positionTransactionEntity.getTransactionRefCodifier() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_REF_CODIFIER, positionTransactionEntity.getTransactionRefCodifier());
            }
            if (positionTransactionEntity.getTransactionRefCode() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_REF_CODE, positionTransactionEntity.getTransactionRefCode());
            }
            if (positionTransactionEntity.getPositionDirection() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_DIRECTION, positionTransactionEntity.getPositionDirection());
            }
            if (positionTransactionEntity.getPositionQuantity() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_QUANTITY, positionTransactionEntity.getPositionQuantity());
            }
            if (positionTransactionEntity.getPositionQuantityDesignator() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_QUANTITY_DESIGNATOR, positionTransactionEntity.getPositionQuantityDesignator());
            }
            if (positionTransactionEntity.getPositionAmount() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_AMOUNT, positionTransactionEntity.getPositionAmount());
            }
            if (positionTransactionEntity.getPositionAmountCurrency() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_AMOUNT_CURRENCY, positionTransactionEntity.getPositionAmountCurrency());
            }
            if (positionTransactionEntity.getPositionAmountInTradingAccountPrimaryCurrency() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_AMOUNT_IN_TRADING_ACCOUNT_PRIMARY_CURRENCY, positionTransactionEntity.getPositionAmountInTradingAccountPrimaryCurrency());
            }
            if (positionTransactionEntity.getOpenTradePrice() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPEN_TRADE_PRICE, positionTransactionEntity.getOpenTradePrice());
            }
            if (positionTransactionEntity.getOpenTradePrice() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_OPEN_TRADE_TIME, new Timestamp(positionTransactionEntity.getOpenTradeTime().getTime()));
            }

            if (positionTransactionEntity.getRecordSource() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_RECORD_SOURCE, positionTransactionEntity.getRecordSource());
            }

            if (positionTransactionEntity.getRolledOpenTradeId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_ROLLED_OPEN_TRADE_ID, positionTransactionEntity.getRolledOpenTradeId());
            }

            if (positionTransactionEntity.getTradeAmountFxRate() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_TRADE_AMT_FX_RATE, positionTransactionEntity.getTradeAmountFxRate());
            }

            if (positionTransactionEntity.getOpeningTradeAppToUnits() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_TRADE_APP_TO_UNITS, positionTransactionEntity.getOpeningTradeAppToUnits());
            }

            if (positionTransactionEntity.getLoadLatestPositions() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_LOAD_LATEST_POSITIONS, NRGUtils.getYesOrNo(positionTransactionEntity.getLoadLatestPositions()));
            }
            if (positionTransactionEntity.getExecutionType() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_EXECUTION_TYPE, positionTransactionEntity.getExecutionType().name());
            }
            if (positionTransactionEntity.getOpeningTradeInstrumentPrice() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPENING_TRADE_INSTRUMENT_PRICE, positionTransactionEntity.getOpeningTradeInstrumentPrice());
            }

            if (positionTransactionEntity.getTradingScope() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRADING_SCOPE, positionTransactionEntity.getTradingScope().name());
            }

            if (positionTransactionEntity.getBinaryType() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_BINARY_TYPE, positionTransactionEntity.getBinaryType().name());
            }
            if (positionTransactionEntity.getSettleTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_SETTLE_TIME, new Timestamp(positionTransactionEntity.getSettleTime()));
            }
            if (positionTransactionEntity.getTenor() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TENOR, positionTransactionEntity.getTenor());
            }
            if (positionTransactionEntity.getStrikePrice() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_STRIKE_PRICE, positionTransactionEntity.getStrikePrice());
            }
            if (positionTransactionEntity.getStrikePriceAdditional() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_STRIKE_PRICE_ADDITIONAL, positionTransactionEntity.getStrikePriceAdditional());
            }
            if (positionTransactionEntity.getTenorStartTime() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_TENOR_START_TIME, new Timestamp(positionTransactionEntity.getTenorStartTime()));
            }
            if (positionTransactionEntity.getBinaryResult() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_BINARY_RESULT, positionTransactionEntity.getBinaryResult().name());
            }

            if (positionTransactionEntity.getAllocationInstrumentSchema() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_ALLOCATION_TRADING_INSTRUMENT_SCHEMA, positionTransactionEntity.getAllocationInstrumentSchema());
            }

            if (positionTransactionEntity.getOpenTradeInstrumentAmount() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPEN_TRADE_INSTRUMENT_AMOUNT, positionTransactionEntity.getOpenTradeInstrumentAmount());
            }

            if (positionTransactionEntity.getForcedMarginFxRate() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_FORCED_MARGIN_FX_RATE, positionTransactionEntity.getForcedMarginFxRate());
            }

            if (positionTransactionEntity.getOpenAccruedTurnoverInAccountCurrency() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPEN_ACCRUED_TURNOVER_IN_ACCOUNT_CURRENCY, positionTransactionEntity.getOpenAccruedTurnoverInAccountCurrency());
            }
            
            if (positionTransactionEntity.isPairCurrencyInFractionalParts() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_IS_PAIR_CURRENCY_IN_FRACTIONAL_PARTS, NRGUtils.getYesOrNo(positionTransactionEntity.isPairCurrencyInFractionalParts()));
            }
            if (positionTransactionEntity.getValueDate() != null)
            {
                statement.setTimestamp(PUT_POSITION_TRANSACTION_VALUE_DATE, new Timestamp(positionTransactionEntity.getValueDate().getTime()));
            }
            if (positionTransactionEntity.getPairCurrency() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_PAIR_CURRENCY, positionTransactionEntity.getPairCurrency());
            }
            if (positionTransactionEntity.getPrimaryCurrency() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_PRIMARY_CURRENCY, positionTransactionEntity.getPrimaryCurrency());
            }
            if (positionTransactionEntity.getSecondaryCurrency() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_SECONDARY_CURRENCY, positionTransactionEntity.getSecondaryCurrency());
            }
            if (positionTransactionEntity.getPrimaryAmount() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_PRIMARY_AMOUNT, positionTransactionEntity.getPrimaryAmount());
            }
            if (positionTransactionEntity.getSecondaryAmount() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_SECONDARY_AMOUNT, positionTransactionEntity.getSecondaryAmount());
            }
            if (positionTransactionEntity.getOpenTradeMarginPercentage() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPEN_TRADE_MARGIN_PERCENTAGE, positionTransactionEntity.getOpenTradeMarginPercentage());
            }
            if (positionTransactionEntity.getAccruedFeesInInstrumentCurrency() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_ACCRUED_FEES_INSTRUMENT_CCY, positionTransactionEntity.getAccruedFeesInInstrumentCurrency());
            }
            if (positionTransactionEntity.getAccruedFeesInPrimaryCurrency() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_ACCRUED_FEES_PRIMARY_CCY, positionTransactionEntity.getAccruedFeesInPrimaryCurrency());
            }
            if (positionTransactionEntity.getOpeningTradeInstrumentPriceInPrimaryCurrency() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPENING_TRADE_INST_PRICE_PRIMARY_CCY, positionTransactionEntity.getOpeningTradeInstrumentPriceInPrimaryCurrency());
            }
            if (positionTransactionEntity.getOpeningTradeInstrumentPriceInPrimaryCurrency() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPENING_TRADE_INST_PRICE_PRIMARY_CCY, positionTransactionEntity.getOpeningTradeInstrumentPriceInPrimaryCurrency());
            }
            if (positionTransactionEntity.getPortfolioSwapPeriodSequenceNumber() != null)
            {
                statement.setLong(PUT_POSITION_TRANSACTION_PORTFOLIO_SWAP_PERIOD_SEQ_NUMBER, positionTransactionEntity.getPortfolioSwapPeriodSequenceNumber());
            }
            if (positionTransactionEntity.getOptionCode() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_OPTION_CODE, positionTransactionEntity.getOptionCode());
            }
            if (positionTransactionEntity.getTransactionClientCosts() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_TRANSACTION_CLIENT_COSTS, positionTransactionEntity.getTransactionClientCosts());
            }
            if (positionTransactionEntity.getOpenAccruedHoldingCosts() != null)
            {
                statement.setBigDecimal(PUT_POSITION_TRANSACTION_OPEN_ACCRUED_HOLDING_COSTS, positionTransactionEntity.getOpenAccruedHoldingCosts());
            }
            if (positionTransactionEntity.getOriginalOrderId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_ORIGINAL_ORDER_ID, positionTransactionEntity.getOriginalOrderId());
            }
            if (positionTransactionEntity.getStrategyId() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_STRATEGY_ID, positionTransactionEntity.getStrategyId());
            }
            if (positionTransactionEntity.getStrategyType() != null)
            {
                statement.setString(PUT_POSITION_TRANSACTION_STRATEGY_TYPE, positionTransactionEntity.getStrategyType().name());
            }
            
            OracleDatabaseHelper.execute(statement);
            commit(connection);
            if (m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + positionTransactionEntity);
            }
        }
        catch (SQLException | InvalidSessionFormatException e)
        {
            if (m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting " + positionTransactionEntity);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    @Override
    public Set<DataId> getMissingPositionTransactionIds()
    {
        if (m_logger.isInfoEnabled())
        {
            m_logger.info("getMissingPositionTransactionIds");
        }

        return super.getMissingDataIds(null, Entity.PositionTransaction);
    }

    @Override
    public Collection<DataId> getMissingPositionTransactionDates()
    {
        if (m_logger.isInfoEnabled())
        {
            m_logger.info("getMissingPositionTransactionDates");
        }

        return super.getMissingDataIds(null, Entity.PositionTransactionDate);
    }

    private void setParameterDefaultsForPutPositionTransactionStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_POSITION_TRANSACTION_USER, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_TRANSACTION_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_TRADE_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADE_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_BOOKING_NUMBER, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_PLATFORM, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PUBLISH_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_EVENT_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_VERSION_NUMBER, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_IS_DELETED, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_CREATION_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_CREATION_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_UPDATE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_UPDATE_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_SESSION_ID, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_CODE, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_TYPE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_FUNCTION, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_CODIFIER, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADING_ACCOUNT_PRIMARY_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_ORDER_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PRODUCT_INSTRUMENT_CODE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PRODUCT_WRAPPER_CODE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PRODUCT_GENERATION, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_PRODUCT_POINT_MULTIPLIER, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_PRODUCT_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PRODUCT_FRACTIONAL_PART_RATIO, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_IS_PRODUCT_CURRENCY_IN_FRACTIONAL_PARTS, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_CHANNEL_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_REQUEST_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRANSACTION_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_TRANSACTION_TYPE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_REF_CODIFIER, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_REF_CODE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_DIRECTION, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_IS_AUTOMATICALLY_ROLLED, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_QUANTITY, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_QUANTITY_DESIGNATOR, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_AMOUNT, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_AMOUNT_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_AMOUNT_IN_TRADING_ACCOUNT_PRIMARY_CURRENCY, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_TRADE_PRICE, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_TRADE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_RECORD_SOURCE, Types.VARCHAR);

        statement.setNull(PUT_POSITION_TRANSACTION_ROLLED_OPEN_TRADE_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADE_AMT_FX_RATE, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADE_APP_TO_UNITS, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_LOAD_LATEST_POSITIONS, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_EXECUTION_TYPE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_OPENING_TRADE_INSTRUMENT_PRICE, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_TRADE_INSTRUMENT_AMOUNT, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_TRADING_SCOPE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_ALLOCATION_TRADING_INSTRUMENT_SCHEMA, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_BINARY_TYPE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_SETTLE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_TENOR, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_STRIKE_PRICE, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_STRIKE_PRICE_ADDITIONAL, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_TENOR_START_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_BINARY_RESULT, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_FORCED_MARGIN_FX_RATE, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_ACCRUED_TURNOVER_IN_ACCOUNT_CURRENCY, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_IS_PAIR_CURRENCY_IN_FRACTIONAL_PARTS, Types.DECIMAL);
        statement.setNull(PUT_POSITION_TRANSACTION_VALUE_DATE, Types.TIMESTAMP);
        statement.setNull(PUT_POSITION_TRANSACTION_PAIR_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PRIMARY_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_SECONDARY_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_PRIMARY_AMOUNT, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_SECONDARY_AMOUNT, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_TRADE_MARGIN_PERCENTAGE, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_ACCRUED_FEES_INSTRUMENT_CCY, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_ACCRUED_FEES_PRIMARY_CCY, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_OPENING_TRADE_INST_PRICE_PRIMARY_CCY, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_PORTFOLIO_SWAP_PERIOD_SEQ_NUMBER, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_OPTION_CODE, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_TRANSACTION_CLIENT_COSTS, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_OPEN_ACCRUED_HOLDING_COSTS, Types.NUMERIC);
        statement.setNull(PUT_POSITION_TRANSACTION_ORIGINAL_ORDER_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_STRATEGY_ID, Types.VARCHAR);
        statement.setNull(PUT_POSITION_TRANSACTION_STRATEGY_TYPE, Types.VARCHAR);
    }

    private void setParameterDefaultsForPutPositionTransactionsStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_POSITION_TRANSACTIONS_ENTITIES, Types.ARRAY, "BI_ODS.POSITION_TRANSACTIONS_TAB");
    }

    @Override
    public void putPositionTransactionEntityBatch(List<? extends PositionTransactionEntity> positionTransactionEntities) throws DAOException
    {
        if (m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + positionTransactionEntities);
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_POSITION_TRANSACTIONS_STATEMENT);
            setParameterDefaultsForPutPositionTransactionsStatement(statement);

            if (positionTransactionEntities != null && positionTransactionEntities.size() > 0)
            {
                StructDescriptor objDescriptor = StructDescriptor.createDescriptor("BI_ODS.POSITION_TRANSACTIONS_OBJ", connection);
                Struct[] struct = new STRUCT[positionTransactionEntities.size()];
                int i = 0;
                for (PositionTransactionEntity positionTransactionEntity : positionTransactionEntities)
                {
                    struct[i++] = new STRUCT(
                        objDescriptor,
                        connection,
                        new Object[]{positionTransactionEntity.getUser(), asTimestamp(positionTransactionEntity.getEffectiveStartTimestamp()), positionTransactionEntity.getTransactionId(),
                                     positionTransactionEntity.getOpenTradeId(), positionTransactionEntity.getTradeId(),
                                     (positionTransactionEntity.getTransactionBookingId() != null && !positionTransactionEntity.getTransactionBookingId().equals(-1L))
                                         ? positionTransactionEntity.getTransactionBookingId() : null,
                                     positionTransactionEntity.getPlatform(), asTimestamp(positionTransactionEntity.getPublishTime()), asTimestamp(positionTransactionEntity.getEventTime()),
                                     positionTransactionEntity.getVersionNumber(), NRGUtils.getYesOrNo(positionTransactionEntity.isDeleted()), asTimestamp(positionTransactionEntity.getCreationTime()),
                                     positionTransactionEntity.getCreationIdentityToken(), positionTransactionEntity.getCreationOnBehalfOfIdentityToken(),
                                     asTimestamp(positionTransactionEntity.getUpdateTime()), positionTransactionEntity.getUpdateIdentityToken(),
                                     positionTransactionEntity.getUpdateOnBehalfOfIdentityToken(), SessionHashUtils.evaluateHashedSession(positionTransactionEntity.getSessionId()),
                                     positionTransactionEntity.getTradingAccountCode(), positionTransactionEntity.getTradingAccountType(), positionTransactionEntity.getFunction(),
                                     positionTransactionEntity.getTradingAccountCodifier(), positionTransactionEntity.getTradingAccountPrimaryCurrency(), positionTransactionEntity.getOrderId(),
                                     positionTransactionEntity.getProductInstrumentCode(), positionTransactionEntity.getProductWrapperCode(), positionTransactionEntity.getProductGeneration(),
                                     positionTransactionEntity.getProductPointMultiplier(), positionTransactionEntity.getProductCurrency(), positionTransactionEntity.getProductFractionalPartRatio(),
                                     NRGUtils.getYesOrNo(positionTransactionEntity.isCurrencyInFractionalParts()), positionTransactionEntity.getChannelId(), positionTransactionEntity.getRequestId(),
                                     asTimestamp(positionTransactionEntity.getTransactionTime()), positionTransactionEntity.getTransactionType(), positionTransactionEntity.getTransactionRefCodifier(),
                                     positionTransactionEntity.getTransactionRefCode(), null, positionTransactionEntity.getPositionDirection(), positionTransactionEntity.getPositionQuantity(),
                                     positionTransactionEntity.getPositionQuantityDesignator(), positionTransactionEntity.getPositionAmount(), positionTransactionEntity.getPositionAmountCurrency(),
                                     positionTransactionEntity.getPositionAmountInTradingAccountPrimaryCurrency(), positionTransactionEntity.getOpenTradePrice(),
                                     positionTransactionEntity.getTradeAmountFxRate(), asTimestamp(positionTransactionEntity.getOpenTradeTime()), positionTransactionEntity.getRecordSource(),
                                     positionTransactionEntity.getRolledOpenTradeId(), positionTransactionEntity.getOpeningTradeAppToUnits(),
                                     NRGUtils.getYesOrNo(positionTransactionEntity.getLoadLatestPositions()),
                                     positionTransactionEntity.getExecutionType() == null ? null : positionTransactionEntity.getExecutionType().name(),
                                     positionTransactionEntity.getOpeningTradeInstrumentPrice(),
                                     positionTransactionEntity.getTradingScope() == null ? null : positionTransactionEntity.getTradingScope().name(),
                                     positionTransactionEntity.getBinaryType() == null ? null : positionTransactionEntity.getBinaryType().name(),
                                     asTimestamp(positionTransactionEntity.getSettleTime()), positionTransactionEntity.getTenor(), positionTransactionEntity.getStrikePrice(),
                                     positionTransactionEntity.getStrikePriceAdditional(), asTimestamp(positionTransactionEntity.getTenorStartTime()),
                                     positionTransactionEntity.getBinaryResult() == null ? null : positionTransactionEntity.getBinaryResult().name(),
                                     positionTransactionEntity.getAllocationInstrumentSchema(),
                                     positionTransactionEntity.getOpenTradeInstrumentAmount(),
                                     positionTransactionEntity.getForcedMarginFxRate(),
                                     positionTransactionEntity.getOpenAccruedTurnoverInAccountCurrency(),
                                     NRGUtils.getYesOrNo(positionTransactionEntity.isPairCurrencyInFractionalParts()),
                                     positionTransactionEntity.getValueDate() == null ? null : new Timestamp(positionTransactionEntity.getValueDate().getTime()),
                            		 positionTransactionEntity.getPairCurrency(),
                            		 positionTransactionEntity.getPrimaryCurrency(),
                            		 positionTransactionEntity.getSecondaryCurrency(),
                            		 positionTransactionEntity.getPrimaryAmount(),
                                     positionTransactionEntity.getSecondaryAmount(),
                                     positionTransactionEntity.getOpenTradeMarginPercentage(),
                                     positionTransactionEntity.getAccruedFeesInInstrumentCurrency(),
                                     positionTransactionEntity.getAccruedFeesInPrimaryCurrency(),
                                     positionTransactionEntity.getOpeningTradeInstrumentPriceInPrimaryCurrency(),
                                     positionTransactionEntity.getPortfolioSwapPeriodSequenceNumber(),
                                     positionTransactionEntity.getOptionCode(),
                                     positionTransactionEntity.getTransactionClientCosts(),
                                     positionTransactionEntity.getOpenAccruedHoldingCosts(),
                                     positionTransactionEntity.getOriginalOrderId(),
                                     positionTransactionEntity.getStrategyId(),
                                     positionTransactionEntity.getStrategyType() == null ? null : positionTransactionEntity.getStrategyType().name()});
                }
                ArrayDescriptor tradingAccountIdTabDescriptor = ArrayDescriptor.createDescriptor("BI_ODS.POSITION_TRANSACTIONS_TAB", connection);
                Array array = new ARRAY(tradingAccountIdTabDescriptor, connection, struct);
                statement.setArray(PUT_POSITION_TRANSACTIONS_ENTITIES, array);
            }

            OracleDatabaseHelper.execute(statement);
            commit(connection);
            if (m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + positionTransactionEntities);
            }
        }
        catch (SQLException | InvalidSessionFormatException e)
        {
            if (m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting " + positionTransactionEntities);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }
}
