/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * 
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */

package com.cmcmarkets.nrg.server.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.database.oracle.OracleDatabaseHelper;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.DataId;
import com.cmcmarkets.nrg.api.model.DataIdAndVersion;
import com.cmcmarkets.nrg.api.model.Platform;
import com.cmcmarkets.nrg.api.model.SpeedBetEntity;
import com.cmcmarkets.nrg.server.dao.SpeedBetDAO;
import com.cmcmarkets.nrg.utils.NRGUtils;
import com.cmcmarkets.nrg.utils.SessionHashUtils;
import com.cmcmarkets.nrg.utils.SessionHashUtils.InvalidSessionFormatException;
import com.cmcmarkets.trading.api3.model.BetDirection;
import com.cmcmarkets.trading.api3.model.CloseComment;

import oracle.jdbc.OracleTypes;
import oracle.sql.STRUCT;
import oracle.sql.StructDescriptor;

public class DatabaseSpeedBetDAO extends DatabaseDataIdDAO implements SpeedBetDAO
{
    private static final Logger m_logger = LoggerFactory.getLogger(DatabaseSpeedBetDAO.class);

    private static final String PUT_SPEEDBET_STATEMENT =
        "{ call bi_ods.nrg_speedbet.put_speedbet(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) }";
    private static final int PUT_SPEEDBET_USER = 1;
    private static final int PUT_SPEEDBET_EFFECTIVE_START_TIMESTAMP = 2;
    private static final int PUT_SPEEDBET_PLATFORM = 3;
    private static final int PUT_SPEEDBET_TRADE_ID = 4;
    private static final int PUT_SPEEDBET_TRADING_ACCOUNT_ID = 5;
    private static final int PUT_SPEEDBET_TRADING_ACCOUNT_CODIFIER = 6;
    private static final int PUT_SPEEDBET_TRADING_ACCOUNT_FUNCTION = 7;
    private static final int PUT_SPEEDBET_TRADING_ACCOUNT_CURRENCY = 8;
    private static final int PUT_SPEEDBET_PRODUCT_INSTRUMENT_CODE = 9;
    private static final int PUT_SPEEDBET_PRODUCT_WRAPPER_CODE = 10;
    private static final int PUT_SPEEDBET_DIRECTION = 11;
    private static final int PUT_SPEEDBET_ORDER_TYPE = 12;
    private static final int PUT_SPEEDBET_STRIKE_PRICE = 13;
    private static final int PUT_SPEEDBET_PRICE_ADJUSTMENT = 14;
    private static final int PUT_SPEEDBET_TENOR = 15;
    private static final int PUT_SPEEDBET_STAKE = 16;
    private static final int PUT_SPEEDBET_OPENING_BOOKING_ID = 17;
    private static final int PUT_SPEEDBET_WIN_PAYOUT = 18;
    private static final int PUT_SPEEDBET_DRAW_PAYOUT = 19;
    private static final int PUT_SPEEDBET_OPENING_TIME = 20;
    private static final int PUT_SPEEDBET_SETTLE_TIME = 21;
    private static final int PUT_SPEEDBET_OPENING_QUOTE_ID = 22;
    private static final int PUT_SPEEDBET_OPENING_SPEED_BET_QUOTE_ID = 23;
    private static final int PUT_SPEEDBET_CLIENT_STATE_QUOTE_ID = 24;
    private static final int PUT_SPEEDBET_CLIENT_STATE_SPEED_BET_QUOTE_ID = 25;
    private static final int PUT_SPEEDBET_CLIENT_STATE_REQUEST_TIME = 26;
    private static final int PUT_SPEEDBET_CLIENT_STATE_STRIKE_PRICE = 27;
    private static final int PUT_SPEEDBET_CLIENT_STATE_PRICEADJUSTMENT = 28;
    private static final int PUT_SPEEDBET_STATE = 29;
    private static final int PUT_SPEEDBET_SETTLE_PRICE = 30;
    private static final int PUT_SPEEDBET_SETTLE_QUOTE_RECEIVE_TIME = 31;
    private static final int PUT_SPEEDBET_SETTLE_QUOTE_ID = 32;
    private static final int PUT_SPEEDBET_RESULT = 33;
    private static final int PUT_SPEEDBET_SETTLE_BOOKING_ID = 34;
    private static final int PUT_SPEEDBET_FAILED_SETTLE_QUOTE_ID = 35;
    private static final int PUT_SPEEDBET_FAILED_SETTLE_QUOTE_RECEIVED = 36;
    private static final int PUT_SPEEDBET_CANCELLATION_OPENING_BOOKING = 37;
    private static final int PUT_SPEEDBET_CANCELLATION_SETTLE_BOOKING = 38;
    private static final int PUT_SPEEDBET_VERSION_NUMBER = 39;
    private static final int PUT_SPEEDBET_IS_DELETED = 40;
    private static final int PUT_SPEEDBET_CREATION_TIME = 41;
    private static final int PUT_SPEEDBET_CREATION_IDENTITY_TOKEN = 42;
    private static final int PUT_SPEEDBET_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN = 43;
    private static final int PUT_SPEEDBET_UPDATE_TIME = 44;
    private static final int PUT_SPEEDBET_UPDATE_IDENTITY_TOKEN = 45;
    private static final int PUT_SPEEDBET_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN = 46;
    private static final int PUT_SPEEDBET_SETTLE_AMOUNT = 47;
    private static final int PUT_SPEEDBET_CLIENT_STATE_MID_PRICE = 48;
    private static final int PUT_SPEEDBET_CLOSE_REASON = 49;
    private static final int PUT_SPEEDBET_CONTROLLED_OPEN_SPEED_BET_TYPE = 50;
    private static final int PUT_SPEEDBET_CONTROLLED_SETTLE_SPEED_BET_TYPE = 51;
    private static final int PUT_SPEEDBET_QUANTITY_DESIGNATOR = 52;
    private static final int PUT_SPEEDBET_ORDER_ID = 53;
    private static final int PUT_SPEEDBET_RECORD_SOURCE = 54;
    private static final int PUT_SPEEDBET_SOURCE_CID = 55;
    private static final int PUT_SPEEDBET_SOURCE_RID = 56;
    private static final int PUT_SPEEDBET_SESSION_ID = 57;
    private static final int PUT_SPEEDBET_CLIENT_STATE_PARAMETER_INFO = 58;
    private static final int PUT_SPEEDBET_FAILED_SETTLE_REASON = 59;
    private static final int PUT_SPEEDBET_TRADE_TIME = 60;
    private static final int PUT_SPEEDBET_DIRECTION_MULTIPLIER = 61;
    private static final int PUT_SPEEDBET_TRADE_PRICE = 62;
    private static final int PUT_SPEEDBET_TRADE_QUANTITY = 63;
    private static final int PUT_SPEEDBET_TRADE_QUANTITY_CURRENCY = 64;
    private static final int PUT_SPEEDBET_TRADE_AMOUNT = 65;
    private static final int PUT_SPEEDBET_TRADE_AMOUNT_CURRENCY = 66;
    private static final int PUT_SPEEDBET_TRADE_AMOUNT_IN_TRADING_ACCOUNT_CURRENCY = 67;
    private static final int PUT_SPEEDBET_TRADE_PROFIT_LOSS_CURRENCY = 68;
    private static final int PUT_SPEEDBET_CLOSE_COMMENT = 69;
    private static final int PUT_SPEEDBET_PRICE_STREAM_CODE = 70;
    private static final int PUT_SPEEDBET_CAPITAL_GAINS_IN_TAX_CURRENCY = 71;
    private static final int PUT_SPEEDBET_CAPITAL_GAINS_IN_ACCOUNT_CURRENCY = 72;
    
    private static final String SPEED_BET_IDS_STATEMENT = "{? = call bi_ods.nrg_speedbet.get_speedbet_ids(?,?,?) }";
    private static final int SPEED_BET_IDS_VERSIONS_RESULT_SET = 1;
    private static final int SPEED_BET_IDS_VERSIONS_PLATFORM = 2;
    private static final int SPEED_BET_IDS_VERSIONS_FROM_TIMESTAMP = 3;
    private static final int SPEED_BET_IDS_VERSIONS_TO_TIMESTAMP = 4;

    public DatabaseSpeedBetDAO()
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("Initialising " + this.getClass().getSimpleName());
        }
    }

    @Override
    public DataIdAndVersion<String, Long> getSpeedBetIdVersions(Platform platform,
                                                                java.util.Date from,
                                                                java.util.Date to)
    {
        if(m_logger.isDebugEnabled())
        {
            m_logger.debug("getSpeedBetIdVersions(" + platform + ", " + from + ", " + to + ")");
        }

        Connection connection = null;
        CallableStatement statement = null;
        DataIdAndVersion<String, Long> speedBetIdsVersions = new DataIdAndVersion<String, Long>();
        try
        {
            connection = getConnection();
            statement = connection.prepareCall(SPEED_BET_IDS_STATEMENT);

            if(platform == null || from == null || to == null)
            {
                throw new DAOException(
                    "Invalid parameters passed: Platform=" + platform + ", From=" + from + ", To=" + to);
            }

            statement.registerOutParameter(SPEED_BET_IDS_VERSIONS_RESULT_SET, OracleTypes.CURSOR);
            statement.setString(SPEED_BET_IDS_VERSIONS_PLATFORM, platform.name());
            statement.setTimestamp(SPEED_BET_IDS_VERSIONS_FROM_TIMESTAMP, new Timestamp(from.getTime()));
            statement.setTimestamp(SPEED_BET_IDS_VERSIONS_TO_TIMESTAMP, new Timestamp(to.getTime()));

            OracleDatabaseHelper.execute(statement);

            ResultSet resultSet = (ResultSet) statement.getObject(SPEED_BET_IDS_VERSIONS_RESULT_SET);
            while(resultSet.next())
            {
                speedBetIdsVersions.putEntry(resultSet.getString("order_id"), resultSet.getLong("version_number"));
            }

            if(m_logger.isInfoEnabled())
            {
                m_logger.info("Retrieved " + speedBetIdsVersions.size() + " speedbet IDs and Versions");
            }
        }
        catch(SQLException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error(
                    "Error getting speed bet ids and versions using (" + platform + ", " + from + ", " + to + ")");
            }
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
        return speedBetIdsVersions;
    }

    @Override
    public void putSpeedBetEntity(SpeedBetEntity speedBetEntity)
    {
        if(m_logger.isDebugEnabled())
        {
            m_logger.debug("Putting " + speedBetEntity);
        }

        Connection connection = null;
        CallableStatement statement = null;

        try
        {
            connection = getConnection();
            statement = connection.prepareCall(PUT_SPEEDBET_STATEMENT);
            setParameterDefaultsForPutSpeedBetStatement(statement);

            if(speedBetEntity.getUser() != null)
            {
                statement.setString(PUT_SPEEDBET_USER, speedBetEntity.getUser());
            }
            if(speedBetEntity.getEffectiveStartTimestamp() != null)
            {
                statement.setTimestamp(
                    PUT_SPEEDBET_EFFECTIVE_START_TIMESTAMP,
                    new Timestamp(speedBetEntity.getEffectiveStartTimestamp().getTime()));
            }
            statement.setString(PUT_SPEEDBET_PLATFORM, Platform.NG.name());
            if(speedBetEntity.getId() != null)
            {
                statement.setString(PUT_SPEEDBET_TRADE_ID, speedBetEntity.getId());
                statement.setString(PUT_SPEEDBET_ORDER_ID, speedBetEntity.getId());
            }
            if(speedBetEntity.getTradingAccountCode() != null)
            {
                statement.setLong(PUT_SPEEDBET_TRADING_ACCOUNT_ID, speedBetEntity.getTradingAccountCode());
            }

            statement.setString(PUT_SPEEDBET_TRADING_ACCOUNT_CODIFIER, "CUSTOMER");

            if(speedBetEntity.getTradingAccountFunction() != null)
            {
                statement.setString(PUT_SPEEDBET_TRADING_ACCOUNT_FUNCTION, speedBetEntity.getTradingAccountFunction());
            }
            if(speedBetEntity.getTradingAccountCurrency() != null)
            {
                statement.setString(PUT_SPEEDBET_TRADING_ACCOUNT_CURRENCY, speedBetEntity.getTradingAccountCurrency());
            }
            if(speedBetEntity.getProductInstrumentCode() != null)
            {
                statement.setString(PUT_SPEEDBET_PRODUCT_INSTRUMENT_CODE, speedBetEntity.getProductInstrumentCode());
            }
            if(speedBetEntity.getProductWrapperCode() != null)
            {
                statement.setString(PUT_SPEEDBET_PRODUCT_WRAPPER_CODE, speedBetEntity.getProductWrapperCode());
            }
            if(speedBetEntity.getDirection() != null)
            {
                statement.setString(
                    PUT_SPEEDBET_DIRECTION,
                    BetDirection.UP == speedBetEntity.getDirection() ? "BUY" : "SELL");
            }

            statement.setString(PUT_SPEEDBET_ORDER_TYPE, "COUNTDOWN");

            if(speedBetEntity.getStrikePrice() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_STRIKE_PRICE, speedBetEntity.getStrikePrice());
            }
            if(speedBetEntity.getPriceAdjustment() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_PRICE_ADJUSTMENT, speedBetEntity.getPriceAdjustment());
            }
            if(speedBetEntity.getTenor() != null)
            {
                statement.setInt(PUT_SPEEDBET_TENOR, speedBetEntity.getTenor());
            }
            if(speedBetEntity.getStake() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_STAKE, speedBetEntity.getStake());
            }
            if(speedBetEntity.getOpeningBookingId() != null)
            {
                statement.setLong(PUT_SPEEDBET_OPENING_BOOKING_ID, speedBetEntity.getOpeningBookingId());
            }
            if(speedBetEntity.getWinPayout() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_WIN_PAYOUT, speedBetEntity.getWinPayout());
            }
            if(speedBetEntity.getDrawPayout() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_DRAW_PAYOUT, speedBetEntity.getDrawPayout());
            }
            if(speedBetEntity.getOpeningTime() != null)
            {
                statement
                    .setTimestamp(PUT_SPEEDBET_OPENING_TIME, new Timestamp(speedBetEntity.getOpeningTime().getTime()));
            }
            if(speedBetEntity.getSettleTime() != null)
            {
                statement
                    .setTimestamp(PUT_SPEEDBET_SETTLE_TIME, new Timestamp(speedBetEntity.getSettleTime().getTime()));
            }
            if(speedBetEntity.getOpeningQuoteId() != null)
            {
                statement.setLong(PUT_SPEEDBET_OPENING_QUOTE_ID, speedBetEntity.getOpeningQuoteId());
            }
            if(speedBetEntity.getOpeningSpeedBetQuoteId() != null)
            {
                statement.setLong(PUT_SPEEDBET_OPENING_SPEED_BET_QUOTE_ID, speedBetEntity.getOpeningSpeedBetQuoteId());
            }
            if(speedBetEntity.getClientStateQuoteId() != null)
            {
                statement
                    .setString(PUT_SPEEDBET_CLIENT_STATE_QUOTE_ID, speedBetEntity.getClientStateQuoteId().toString());
            }
            if(speedBetEntity.getClientStateSpeedBetQuoteId() != null)
            {
                statement.setLong(
                    PUT_SPEEDBET_CLIENT_STATE_SPEED_BET_QUOTE_ID,
                    speedBetEntity.getClientStateSpeedBetQuoteId());
            }
            if(speedBetEntity.getClientStateRequestTime() != null)
            {
                statement.setTimestamp(
                    PUT_SPEEDBET_CLIENT_STATE_REQUEST_TIME,
                    new Timestamp(speedBetEntity.getClientStateRequestTime().getTime()));
            }
            if(speedBetEntity.getClientStateStrikePrice() != null)
            {
                statement
                    .setBigDecimal(PUT_SPEEDBET_CLIENT_STATE_STRIKE_PRICE, speedBetEntity.getClientStateStrikePrice());
            }
            if(speedBetEntity.getClientStatePriceAdjustment() != null)
            {
                statement.setBigDecimal(
                    PUT_SPEEDBET_CLIENT_STATE_PRICEADJUSTMENT,
                    speedBetEntity.getClientStatePriceAdjustment());
            }
            if(speedBetEntity.getState() != null)
            {
                statement.setString(PUT_SPEEDBET_STATE, speedBetEntity.getState().name());
            }
            if(speedBetEntity.getSettlePrice() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_SETTLE_PRICE, speedBetEntity.getSettlePrice());
            }
            if(speedBetEntity.getSettleQuoteReceiveTime() != null)
            {
                statement.setTimestamp(
                    PUT_SPEEDBET_SETTLE_QUOTE_RECEIVE_TIME,
                    new Timestamp(speedBetEntity.getSettleQuoteReceiveTime().getTime()));
            }
            if(speedBetEntity.getSettleQuoteId() != null)
            {
                statement.setLong(PUT_SPEEDBET_SETTLE_QUOTE_ID, speedBetEntity.getSettleQuoteId());
            }
            if(speedBetEntity.getResult() != null)
            {
                statement.setString(PUT_SPEEDBET_RESULT, speedBetEntity.getResult().name());
            }
            if(speedBetEntity.getSettleBookingId() != null)
            {
                statement.setLong(PUT_SPEEDBET_SETTLE_BOOKING_ID, speedBetEntity.getSettleBookingId());
            }
            if(speedBetEntity.getFailedSettleQuoteId() != null)
            {
                statement.setLong(PUT_SPEEDBET_FAILED_SETTLE_QUOTE_ID, speedBetEntity.getFailedSettleQuoteId());
            }
            if(speedBetEntity.getFailedSettleQuoteReceiveTime() != null)
            {
                statement.setTimestamp(
                    PUT_SPEEDBET_FAILED_SETTLE_QUOTE_RECEIVED,
                    new Timestamp(speedBetEntity.getFailedSettleQuoteReceiveTime().getTime()));
            }
            if(speedBetEntity.getCancellationOpeningBookingId() != null)
            {
                statement.setLong(
                    PUT_SPEEDBET_CANCELLATION_OPENING_BOOKING,
                    speedBetEntity.getCancellationOpeningBookingId());
            }
            if(speedBetEntity.getCancellationSettleBookingId() != null)
            {
                statement
                    .setLong(PUT_SPEEDBET_CANCELLATION_SETTLE_BOOKING, speedBetEntity.getCancellationSettleBookingId());
            }
            if(speedBetEntity.getVersion() != null)
            {
                if(speedBetEntity.getVersion().getVersionNumber() != null)
                {
                    statement.setLong(PUT_SPEEDBET_VERSION_NUMBER, speedBetEntity.getVersion().getVersionNumber());
                }
                if(speedBetEntity.getVersion().isDeleted() != null)
                {
                    statement.setString(
                        PUT_SPEEDBET_IS_DELETED,
                        NRGUtils.getYesOrNo(speedBetEntity.getVersion().isDeleted()));
                }
                if(speedBetEntity.getVersion().getCreationTime() != null)
                {
                    statement.setTimestamp(
                        PUT_SPEEDBET_CREATION_TIME,
                        new Timestamp(speedBetEntity.getVersion().getCreationTime().getTime()));
                }
                if(speedBetEntity.getVersion().getCreationIdentityToken() != null)
                {
                    statement.setLong(
                        PUT_SPEEDBET_CREATION_IDENTITY_TOKEN,
                        speedBetEntity.getVersion().getCreationIdentityToken());
                }
                if(speedBetEntity.getVersion().getCreationOnBehalfOfIdentityToken() != null)
                {
                    statement.setLong(
                        PUT_SPEEDBET_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN,
                        speedBetEntity.getVersion().getCreationOnBehalfOfIdentityToken());
                }
                if(speedBetEntity.getVersion().getUpdateTime() != null)
                {
                    statement.setTimestamp(
                        PUT_SPEEDBET_UPDATE_TIME,
                        new Timestamp(speedBetEntity.getVersion().getUpdateTime().getTime()));
                }
                if(speedBetEntity.getVersion().getUpdateIdentityToken() != null)
                {
                    statement.setLong(
                        PUT_SPEEDBET_UPDATE_IDENTITY_TOKEN,
                        speedBetEntity.getVersion().getUpdateIdentityToken());
                }
                if(speedBetEntity.getVersion().getUpdateOnBehalfOfIdentityToken() != null)
                {
                    statement.setLong(
                        PUT_SPEEDBET_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN,
                        speedBetEntity.getVersion().getUpdateOnBehalfOfIdentityToken());
                }
            }
            if(speedBetEntity.getSettleAmount() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_SETTLE_AMOUNT, speedBetEntity.getSettleAmount());
            }
            if(speedBetEntity.getClientStateMidPrice() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_CLIENT_STATE_MID_PRICE, speedBetEntity.getClientStateMidPrice());
            }
            if(speedBetEntity.getCloseReason() != null)
            {
                statement.setString(PUT_SPEEDBET_CLOSE_REASON, speedBetEntity.getCloseReason());
            }
            if(speedBetEntity.getControlledOpenSpeedBetType() != null)
            {
                statement.setString(
                    PUT_SPEEDBET_CONTROLLED_OPEN_SPEED_BET_TYPE,
                    speedBetEntity.getControlledOpenSpeedBetType());
            }
            if(speedBetEntity.getControlledSettleSpeedBetType() != null)
            {
                statement.setString(
                    PUT_SPEEDBET_CONTROLLED_SETTLE_SPEED_BET_TYPE,
                    speedBetEntity.getControlledSettleSpeedBetType());
            }
            statement.setString(PUT_SPEEDBET_QUANTITY_DESIGNATOR, "AMOUNTPERBET");
            statement.setString(PUT_SPEEDBET_RECORD_SOURCE, "SPEEDBET");

            if(speedBetEntity.getClientStateParameterInfo() != null)
            {
                statement
                    .setString(PUT_SPEEDBET_CLIENT_STATE_PARAMETER_INFO, speedBetEntity.getClientStateParameterInfo());
            }
            if(speedBetEntity.getFailedSettleReason() != null)
            {
                statement.setString(PUT_SPEEDBET_FAILED_SETTLE_REASON, speedBetEntity.getFailedSettleReason());
            }
            if(speedBetEntity.getOpeningTime() != null)
            {
                statement
                    .setTimestamp(PUT_SPEEDBET_TRADE_TIME, new Timestamp(speedBetEntity.getOpeningTime().getTime()));
            }
            if(speedBetEntity.getDirection() != null)
            {
                statement.setLong(
                    PUT_SPEEDBET_DIRECTION_MULTIPLIER,
                    BetDirection.UP == speedBetEntity.getDirection() ? 1 : -1);
            }
            if(speedBetEntity.getStrikePrice() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_TRADE_PRICE, speedBetEntity.getStrikePrice());
            }
            if(speedBetEntity.getStake() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_TRADE_QUANTITY, speedBetEntity.getStake());
            }
            if(speedBetEntity.getTradingAccountCurrency() != null)
            {
                statement.setString(PUT_SPEEDBET_TRADE_QUANTITY_CURRENCY, speedBetEntity.getTradingAccountCurrency());
            }
            if(speedBetEntity.getStake() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_TRADE_AMOUNT, speedBetEntity.getStake());
            }
            if(speedBetEntity.getTradingAccountCurrency() != null)
            {
                statement.setString(PUT_SPEEDBET_TRADE_AMOUNT_CURRENCY, speedBetEntity.getTradingAccountCurrency());
            }
            if(speedBetEntity.getStake() != null)
            {
                statement
                    .setBigDecimal(PUT_SPEEDBET_TRADE_AMOUNT_IN_TRADING_ACCOUNT_CURRENCY, speedBetEntity.getStake());
            }
            if(speedBetEntity.getTradingAccountCurrency() != null)
            {
                statement
                    .setString(PUT_SPEEDBET_TRADE_PROFIT_LOSS_CURRENCY, speedBetEntity.getTradingAccountCurrency());
            }
            if(speedBetEntity.getRequestInfo() != null)
            {
                if(speedBetEntity.getRequestInfo().getChannelId() != null)
                {
                    statement.setString(PUT_SPEEDBET_SOURCE_CID, speedBetEntity.getRequestInfo().getChannelId());
                }
                if(speedBetEntity.getRequestInfo().getRequestId() != null)
                {
                    statement.setString(PUT_SPEEDBET_SOURCE_RID, speedBetEntity.getRequestInfo().getRequestId());
                }
                if(speedBetEntity.getRequestInfo().getSessionId() != null)
                {
                    statement.setString(PUT_SPEEDBET_SESSION_ID, SessionHashUtils.evaluateHashedSession(speedBetEntity.getRequestInfo().getSessionId()));
                }
            }
            
            if(speedBetEntity.getCapitalGainsInTaxCurrency() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_CAPITAL_GAINS_IN_TAX_CURRENCY, speedBetEntity.getCapitalGainsInTaxCurrency());
            }
            
            if(speedBetEntity.getCapitalGainsInAccountCurrency() != null)
            {
                statement.setBigDecimal(PUT_SPEEDBET_CAPITAL_GAINS_IN_ACCOUNT_CURRENCY, speedBetEntity.getCapitalGainsInAccountCurrency());
            }
            
            if(speedBetEntity.getPriceStreamCode() != null)
            {
                statement.setString(PUT_SPEEDBET_PRICE_STREAM_CODE, speedBetEntity.getPriceStreamCode());
            }
            
            if (speedBetEntity.getCloseComment() != null)
            {
            	CloseComment c = speedBetEntity.getCloseComment();
                STRUCT struct = new STRUCT(
                		StructDescriptor.createDescriptor("BI_ODS.ORDER_CLOSE_COMMENT_OBJ", connection),
                        connection,
                        new Object[]{
                        		c.getCustom(),
                        		c.getRequestedFxRateSymbol(),
                        		NRGUtils.getYesOrNo(c.isQuoteIdSet()),
                        		c.getQuoteId(),
                        		c.getExistingOrderId(),
                        		c.getUnderlyingPositionSize(),
                        		c.getUnderlyingMaxPositionSize(),
                        		c.getProductUnderlyingMaxPositionSize(),
                        		c.getAccountProportion(),
                        		c.getOrderStrikePrice(),
                        		c.getStrikePriceAdditional(),
                        		c.getBinaryTradingStatus(),
                        		NRGUtils.getYesOrNo(c.isTargetAboveSet()),
                				NRGUtils.getYesOrNo(c.isTargetAbove()),
                        		c.getQuoteStrikePrice(),
                        		c.getUnderlyingInstrument(),
                        		c.getEvaluationPrice(),
                        		c.getCurrentOpenTradeAmount()==null ? null : c.getCurrentOpenTradeAmount().getAmount(),
                        		c.getCurrentOpenTradeAmount()==null ? null : c.getCurrentOpenTradeAmount().getCurrency(),
                        		c.getMaxOpenTradeAmount()==null ? null : c.getMaxOpenTradeAmount().getAmount(),
                        		c.getMaxOpenTradeAmount()==null ? null : c.getMaxOpenTradeAmount().getCurrency(),
                        		c.getEvaluationFxRevalRate(),
                        		c.getAmountFxRevalRateBid(),
                        		c.getAmountFxRevalRateAsk(),
                        		c.getProfitLossFxRevalRateBid(),
                        		c.getProfitLossFxRevalRateAsk(),
                        		c.getProfitLossFxRevalRateMid(),
                        		c.getMarketPrice(),
                        		c.getMinDistance(),
                        		c.getParentTargetPrice(),
                        		c.getTakeProfitPrice(),
                        		c.getPositionSize(),
                        		c.getMaxPositionSize(),
                        		c.getQuantity(),
                        		c.getMaxQuantity(),
                        		c.getProductMaxPositionSize(),
                        		c.getProductTradeQuantityMaximum(),
                        		c.getQuoteProportion(),
                        		c.getMargin(),
                        		c.getEquity(),
                        		c.getEquityChange(),
                        		c.getMarginChange(),
                        		c.getSpreadLoss(),
                        		c.getPremium(),
                        		c.getStopLossPrice(),
                        		c.getStopLossTrailingDistance(),
                        		c.getGuaranteedStopLossPrice(),
                        		c.getLevel1Bid(),
                        		c.getLevel1Ask(),
                        		NRGUtils.getYesOrNo(c.isPriceOffsetIndexSet()),
                        		c.getPriceOffsetIndex(),
                        		c.getLossAmount(),
                        		c.getCurrentProfitLossAmount(),
                        		c.getLossLimitAmount(),
                        		c.getPeriodType(),
                        		c.getPeriod() == null ? null : new Timestamp(c.getPeriod().getTime()),
                        		c.getSWSPTimeoutAt() == null ? null : new Timestamp(c.getSWSPTimeoutAt().getTime()),
                        		c.getRiskReason(),
                        		NRGUtils.getYesOrNo(c.isQueueCountSet()),
                        		c.getQueueCount(),
                        		NRGUtils.getYesOrNo(c.isExecutionsPerPeriodSet()),
                        		c.getExecutionsPerPeriod(),
                        		NRGUtils.getYesOrNo(c.isExecutionsPeriodInMillisecondsSet()),
                        		c.getExecutionsPeriodInMilliseconds(),
                        		c.getExecutionPrice(),
                        		c.getAggregatedQuantity(),
                        		c.getOppositePrice(),
                        		c.getExposure(),
                        		c.getMaxExposure(),
                        		NRGUtils.getYesOrNo(c.isProductMaxExposureSet()),
                        		c.getProductMaxExposure(),
                        		c.getTotalExposure(),
                        		c.getMaxTotalExposure(),
                        		NRGUtils.getYesOrNo(c.isProductMaxTotalExposureSet()),
                        		c.getProductMaxTotalExposure(),
                        		c.getExpiryTime() == null ? null : new Timestamp(c.getExpiryTime().getTime()),
                        		c.getGoodForTime() == null ? null : new Timestamp(c.getGoodForTime().getTime()),
                        		c.getExecutionTime() == null ? null : new Timestamp(c.getExecutionTime().getTime()),
                        		c.getUtcNow() == null ? null : new Timestamp(c.getUtcNow().getTime()),
                        		c.getStartTime() == null ? null : new Timestamp(c.getStartTime().getTime()),
                        		NRGUtils.getYesOrNo(c.isTimeoutMsSet()),
                        		c.getTimeoutMs(),
                        		c.getMaxStakeSizeIndividual(),
                        		c.getMinStakeSize(),
                        		c.getConcurrentStake(),
                        		c.getMaxConcurrentStake(),
                        		c.getProductMaxStakeSizeConcurrent(),
                        		c.getStopLossDistance(),
                                c.getGuaranteedStopLossDistance(),
                                c.getCurrency(),
                                c.getCurrencyType(),
                                c.getTakeProfitDistance(),
                                c.getOrderTypeMaximum(),
                                c.getQuoteReceivedTime() == null ? null : new Timestamp(c.getQuoteReceivedTime().getTime()),
                                c.getMaxOpenPositionAmountGross() == null ? null : c.getMaxOpenPositionAmountGross().getAmount(),
                                c.getMaxOpenPositionAmountGross() == null ? null : c.getMaxOpenPositionAmountGross().getCurrency(),
                                c.getCurrentOpenPositionAmountGross() == null ? null : c.getCurrentOpenPositionAmountGross().getAmount(),
                                c.getCurrentOpenPositionAmountGross() == null ? null : c.getCurrentOpenPositionAmountGross().getAmount(),
                                c.getMaxOpenPositionAmountNet() == null ? null : c.getMaxOpenPositionAmountNet().getAmount(),
                                c.getMaxOpenPositionAmountNet() == null ? null : c.getMaxOpenPositionAmountNet().getAmount(),
                                c.getCurrentOpenPositionAmountNet() == null ? null : c.getCurrentOpenPositionAmountNet().getAmount(),
                                c.getCurrentOpenPositionAmountNet() == null ? null : c.getCurrentOpenPositionAmountNet().getAmount(),
                                c.getEvaluationFxRevalRateInstrumentToUsd(),
                                c.getEvaluationFxRevalRatePairToUsd(),
                                c.getPriceOffset(),
                                c.getPriceOffsetType(),
                                c.getPriceAdjustFieldId(),
                                c.getEvaluationFxRevalRatePrimaryToTradingAccountPrimaryCurrency(),
                                c.getEvaluationFxRevalRateSecondaryToTradingAccountPrimaryCurrency(),
                                c.getFxTradeDate() == null ? null : new Timestamp(c.getFxTradeDate().getTime()),
                                c.getValueDate() == null ? null : new Timestamp(c.getValueDate().getTime()),
                                c.getFxTenor(),
                                c.getQuoteKey(),
                                NRGUtils.getYesOrNo(c.isSwapPointsQuoteIdSet()),
                                c.getSwapPointsQuoteId(),
                                c.getUsedFxRateSymbol(),
                                c.getUsedFxRateQuoteKey(),
                                c.getEvaluationFxRevalRatePrimaryCurrencyToUsd(),
                                c.getEvaluationFxRevalRateSecondaryCurrencyToUsd(),
                                c.getCurrentCurrencyGroupOpenPositionAmount() == null ? null :
                                    c.getCurrentCurrencyGroupOpenPositionAmount().getAmount(),
                                c.getCurrentCurrencyGroupOpenPositionAmount() == null ? null :
                                    c.getCurrentCurrencyGroupOpenPositionAmount().getCurrency(),
                                c.getMaxCurrencyGroupOpenPositionAmount() == null ? null :
                                    c.getMaxCurrencyGroupOpenPositionAmount().getAmount(),
                                c.getMaxCurrencyGroupOpenPositionAmount() == null ? null :
                                    c.getMaxCurrencyGroupOpenPositionAmount().getCurrency(),
                                c.getCurrencyGroupCode(),
                                c.getFreeEquity(),
                                c.getFreeEquityChange(),
                                c.getCommissionAmount() == null ? null :
                                    c.getCommissionAmount().getAmount(),
                                c.getCommissionAmount() == null ? null :
                                    c.getCommissionAmount().getCurrency(),
                                c.getInvestmentTaxAmount() == null ? null : c.getInvestmentTaxAmount().getAmount(),
                                c.getInvestmentTaxAmount() == null ? null : c.getInvestmentTaxAmount().getCurrency(),
                                c.getInvestmentFxCommissionAmount() == null ? null : c.getInvestmentFxCommissionAmount().getAmount(),
                                c.getInvestmentFxCommissionAmount() == null ? null : c.getInvestmentFxCommissionAmount().getCurrency(),
                                c.getAmountFxRevalRateId(),
                                NRGUtils.getYesOrNo(c.isOptionQuoteIdSet()),
                                c.getOptionQuoteId(),
                                c.getOptionExposureLong(),
                                c.getOptionExposureShort(),
                                c.getOptionExposureOpenOrders(),
                                c.getInvestmentSettledPositionQuantity(),
                                c.getPendingPositionSellQuantity(),
                                c.getUnderlyingMidPrice(),
                                c.getMarginRebate(),
                                c.getMarginRebateChange()
                                    // If updating ORDER_CLOSE_COMMENT_OBJ update DatabaseOrderDAO as well!!
                                    });
                
                statement.setObject(PUT_SPEEDBET_CLOSE_COMMENT, struct);
                                
            }

            OracleDatabaseHelper.execute(statement);
            commit(connection);
            if(m_logger.isDebugEnabled())
            {
                m_logger.debug("Put " + speedBetEntity);
            }
        }
        catch(SQLException | InvalidSessionFormatException e)
        {
            if(m_logger.isErrorEnabled())
            {
                m_logger.error("Error putting " + speedBetEntity);
            }
            rollback(connection);
            throw new DAOException(e);
        }
        finally
        {
            close(null, statement, connection);
        }
    }

    private void setParameterDefaultsForPutSpeedBetStatement(CallableStatement statement) throws SQLException
    {
        statement.setNull(PUT_SPEEDBET_USER, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_EFFECTIVE_START_TIMESTAMP, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_TRADE_ID, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_PLATFORM, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_TRADING_ACCOUNT_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_TRADING_ACCOUNT_CODIFIER, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_ORDER_ID, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_PRODUCT_INSTRUMENT_CODE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_PRODUCT_WRAPPER_CODE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CREATION_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_UPDATE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_CREATION_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_CREATION_ON_BEHALF_OF_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_UPDATE_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_UPDATE_ON_BEHALF_OF_IDENTITY_TOKEN, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_VERSION_NUMBER, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_IS_DELETED, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_TRADING_ACCOUNT_FUNCTION, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_TRADING_ACCOUNT_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_DIRECTION, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_ORDER_TYPE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_QUANTITY_DESIGNATOR, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_STRIKE_PRICE, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_PRICE_ADJUSTMENT, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_TENOR, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_STAKE, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_OPENING_BOOKING_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_WIN_PAYOUT, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_DRAW_PAYOUT, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_OPENING_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_SETTLE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_OPENING_QUOTE_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_OPENING_SPEED_BET_QUOTE_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_QUOTE_ID, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_SPEED_BET_QUOTE_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_REQUEST_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_STRIKE_PRICE, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_PRICEADJUSTMENT, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_STATE, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_SETTLE_PRICE, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_SETTLE_QUOTE_RECEIVE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_SETTLE_QUOTE_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_RESULT, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_SETTLE_BOOKING_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_FAILED_SETTLE_QUOTE_ID, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_FAILED_SETTLE_QUOTE_RECEIVED, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_CANCELLATION_OPENING_BOOKING, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_CANCELLATION_SETTLE_BOOKING, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_SETTLE_AMOUNT, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_MID_PRICE, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_CLOSE_REASON, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CONTROLLED_OPEN_SPEED_BET_TYPE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CONTROLLED_SETTLE_SPEED_BET_TYPE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_RECORD_SOURCE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_SOURCE_CID, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_SOURCE_RID, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_SESSION_ID, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CLIENT_STATE_PARAMETER_INFO, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_FAILED_SETTLE_REASON, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_TRADE_TIME, Types.TIMESTAMP);
        statement.setNull(PUT_SPEEDBET_DIRECTION_MULTIPLIER, Types.NUMERIC);
        statement.setNull(PUT_SPEEDBET_TRADE_PRICE, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_TRADE_QUANTITY, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_TRADE_QUANTITY_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_TRADE_AMOUNT, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_TRADE_AMOUNT_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_TRADE_AMOUNT_IN_TRADING_ACCOUNT_CURRENCY, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_TRADE_PROFIT_LOSS_CURRENCY, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CLOSE_COMMENT, Types.STRUCT, "BI_ODS.ORDER_CLOSE_COMMENT_OBJ");
        statement.setNull(PUT_SPEEDBET_PRICE_STREAM_CODE, Types.VARCHAR);
        statement.setNull(PUT_SPEEDBET_CAPITAL_GAINS_IN_TAX_CURRENCY, Types.DECIMAL);
        statement.setNull(PUT_SPEEDBET_CAPITAL_GAINS_IN_ACCOUNT_CURRENCY, Types.DECIMAL);
        
    }

    @Override
    public Collection<DataId> getMissingSpeedBetIdVersions(Platform platform)
    {
        if(m_logger.isInfoEnabled())
        {
            m_logger.info("getSpeedBetIds: {}", platform);
        }

        return super.getMissingDataIds(platform, Entity.SpeedBet);
    }
}
