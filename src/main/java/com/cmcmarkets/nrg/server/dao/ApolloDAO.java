/**
 * This document and its contents are protected by copyright 2011 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 * 
 * (c) CMC Markets Plc 2011
 */
package com.cmcmarkets.nrg.server.dao;

import java.util.Collection;
import java.util.List;

import com.cmcmarkets.apollo.api.fluent.model.Configuration;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.ApolloQuestionnaireEntity;

public interface ApolloDAO extends EntityDAO
{    
    public void putQuestionnaireEntities(Collection<ApolloQuestionnaireEntity> questionnaires) throws DAOException;
    public void putConfigurations(List<Configuration> configurations) throws DAOException;
}