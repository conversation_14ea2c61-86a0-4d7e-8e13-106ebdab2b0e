/**
 * This document and its contents are protected by copyright 2013 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2013
 */
package com.cmcmarkets.nrg.server.prices.binaryCache.model.util;

import com.cmcmarkets.framework.messaging.common.model.util.DateTimeUtils;
import com.cmcmarkets.nrg.api.protos.GetBinarySnapshotProtoBuf;
import com.cmcmarkets.nrg.api.protos.GetBinarySnapshotProtoBuf.GetBinarySnapshotProto;
import com.cmcmarkets.nrg.api.protos.GetBinarySnapshotProtoBuf.GetBinarySnapshotProto.Key;
import com.cmcmarkets.nrg.server.prices.binaryCache.model.DefaultGetBinarySnapshot;
import com.cmcmarkets.nrg.server.prices.binaryCache.model.GetBinarySnapshot;

/**
 * Utility to convert between <code>GetBinarySnapshot</code> and <code>GetBinarySnapshotProto</code>
 *
 * <AUTHOR>
 *
 */
public class GetBinarySnapshotUtils
{
    /**
     * Create pojo from protocol buffer.
     */
    public static GetBinarySnapshot decodeGetBinarySnapshot(GetBinarySnapshotProto proto)
    {
        DefaultGetBinarySnapshot pojo = new DefaultGetBinarySnapshot();

        if(proto.hasSnapshotTime())
        {
            pojo.setSnapshotTime(DateTimeUtils.decodeDateTime(proto.getSnapshotTime()));
        }

        if(proto.hasKey())
        {
            pojo.setKey(decodeKey(proto.getKey()));
        }

        return pojo;
    }

    /**
     * Create protocol buffer from pojo.
     */
    public static GetBinarySnapshotProto encodeGetBinarySnapshot(GetBinarySnapshot pojo)
    {
        GetBinarySnapshotProto.Builder builder = GetBinarySnapshotProto.newBuilder();

        if(pojo.getSnapshotTime() != null)
        {
            builder.setSnapshotTime(DateTimeUtils.encodeDateTime(pojo.getSnapshotTime()));
        }

        if(pojo.getKey() != null)
        {

            builder.setKey(encodeKey(pojo.getKey()));
        }

        return builder.build();
    }

    /**
     * Create pojo from protocol buffer.
     */
    public static GetBinarySnapshot.Key decodeKey(Key proto)
    {
        return GetBinarySnapshot.Key.valueOf(proto.getNumber());
    }

    /**
     * Create protocol buffer from pojo.
     */
    public static GetBinarySnapshotProtoBuf.GetBinarySnapshotProto.Key encodeKey(GetBinarySnapshot.Key pojo)
    {
        return GetBinarySnapshotProtoBuf.GetBinarySnapshotProto.Key.valueOf(pojo.getValue());
    }
}
