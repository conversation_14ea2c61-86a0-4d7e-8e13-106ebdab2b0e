package com.cmcmarkets.nrg.server.handlers.routed;

import com.cmcmarkets.framework.messaging.Message;
import com.cmcmarkets.metatradermanagerapigateway.api.fluent.model.OnMetaTraderRebate;
import com.cmcmarkets.metatradermanagerapigateway.api.fluent.service.v1.MetaTraderManagerAPIGatewayService;
import com.cmcmarkets.nrg.server.dao.MetaTraderRebateDAO;
import com.cmcmarkets.nrg.server.handlers.AbstractOnMessageHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public class MetaTraderRebateHandler {

	private static final Logger logger = LoggerFactory.getLogger(MetaTraderRebateHandler.class);

	public MetaTraderRebateHandler(MetaTraderManagerAPIGatewayService service, MetaTraderRebateDAO dao) {

		logger.info("Initialising {}", this.getClass().getSimpleName());

		service.subscribeOnMetaTraderRebate(new AbstractOnMessageHandler<OnMetaTraderRebate>(dao) {
			@Override
			public void processMessage(Date sentTime, OnMetaTraderRebate event, Message message) {
				dao.putMetaTraderRebates(event.getRebates());
			}
		});
	}
}
