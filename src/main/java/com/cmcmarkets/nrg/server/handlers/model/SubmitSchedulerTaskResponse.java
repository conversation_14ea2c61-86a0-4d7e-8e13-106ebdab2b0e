/**
 * This document and its contents are protected by copyright 2013 and owned by CMC Markets UK Plc.
 * The copying and reproduction of this document and/or its content (whether wholly or partly) or
 * any incorporation of the same into any other material in any media or format of any kind is
 * strictly prohibited. All rights are reserved.
 *
 * (c) CMC Markets Plc 2022
 */
package com.cmcmarkets.nrg.server.handlers.model;

import com.cmcmarkets.framework.messaging.common.model.Response;
import com.cmcmarkets.framework.service.annotation.DataContract;

/**
 * <AUTHOR>
 * 
 */
@DataContract
public interface SubmitSchedulerTaskResponse extends Response
{
	/**
	 * @return the id
	 */
	String getId();
	
	/**
	 * @return the result
	 */
	SchedulerTaskResponseCode getResult();
	
	/**
	 * @return the message
	 */
	String getMessage();
}
