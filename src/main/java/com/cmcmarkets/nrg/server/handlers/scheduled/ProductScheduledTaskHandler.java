package com.cmcmarkets.nrg.server.handlers.scheduled;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import com.cmcmarkets.nrg.api.model.impl.*;
import com.cmcmarkets.productmaster.api.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cmcmarkets.framework.service.Listener;
import com.cmcmarkets.nrg.api.exception.DAOException;
import com.cmcmarkets.nrg.api.model.PriceFeedSchemaEntity;
import com.cmcmarkets.nrg.api.model.PriceSchemaEntity;
import com.cmcmarkets.nrg.api.model.ReconciliationStatusEntity;
import com.cmcmarkets.nrg.api.model.SnapshotIdEntity;
import com.cmcmarkets.nrg.server.dao.PmsDao;
import com.cmcmarkets.nrg.server.dao.ReconciliationStatusDAO;
import com.cmcmarkets.nrg.server.handlers.ServiceSubscriber;
import com.cmcmarkets.productmaster.api.service.v4.ProductMasterService;
import com.cmcmarkets.scheduler.api.listeners.AbstractEndOfDayScheduledEventListener;
import com.cmcmarkets.scheduler.api.listeners.AbstractExactTimeScheduledEventListener;
import com.cmcmarkets.scheduler.api.model.ScheduledTaskEvent;
import com.cmcmarkets.scheduler.api.model.TaskParameter;
import com.cmcmarkets.scheduler.api.model.TaskType;

public class ProductScheduledTaskHandler {
    private static final Logger m_logger = LoggerFactory.getLogger(ProductScheduledTaskHandler.class);

    ProductMasterService productMasterService;

    public ProductScheduledTaskHandler(final ProductMasterService productMasterService,
                                       final PmsDao productSetDAO,
                                       final ReconciliationStatusDAO reconciliationStatusDAO,
                                       final ServiceSubscriber serviceSubscriber) {
        this.productMasterService = productMasterService;

        final AbstractEndOfDayScheduledEventListener pmsSnapshotIdsScheduledTaskEventListener =
                new AbstractEndOfDayScheduledEventListener() {
                    // We synchronise processing of ScheduledTaskEvents
                    private Lock m_lock = new ReentrantLock();

                    @Override
                    protected void process(ScheduledTaskEvent event, List<Date> eodDatesToLoad) {
                        NRGReconciliationStatus reconciliationStatus = new NRGReconciliationStatus();
                        reconciliationStatus.setStartTime(System.currentTimeMillis());
                        reconciliationStatus.setServiceName(TaskType.LOAD_PMS_SNAPSHOT_IDS.name());
                        m_lock.lock();
                        try {
                            if (!eodDatesToLoad.isEmpty()) {
                                reconciliationStatus.setFromDate(eodDatesToLoad.get(0).getTime());
                                reconciliationStatus.setToDate(eodDatesToLoad.get(eodDatesToLoad.size() - 1).getTime());
                                try {
                                    SnapshotIdEntity currentSnapshotId = getCurrentSnapshotId();
                                    if (currentSnapshotId == null) {
                                        throw new RuntimeException("Failed to get current PMS snapshot id.");
                                    }

                                    long snapshotIdCount = 0;
                                    for (Date eodDate : eodDatesToLoad) {
                                        m_logger.info("Getting snapshot ids for {}", eodDate);
                                        List<SnapshotIdEntity> snapshotIds = getSnapshotIds(eodDate);
                                        // Add in the current snapshot id
                                        snapshotIds.add(currentSnapshotId);

                                        m_logger.info("{} snapshot ids loaded, about to persist", snapshotIds.size());

                                        productSetDAO.putEODSnapshotIds("NRG", snapshotIds);
                                        snapshotIdCount += snapshotIds.size();
                                    }
                                    reconciliationStatus.setODSCount(0);
                                    reconciliationStatus.setSourceCount(snapshotIdCount);
                                    event.setUserInfo(snapshotIdCount + " snapshot ids loaded");

                                } catch (Exception ex) {
                                    reconciliationStatus.setEndTime(System.currentTimeMillis());
                                    reconciliationStatus.setErrorReason(ex.getMessage());
                                    reconciliationStatus.setIsDataComplete(Boolean.FALSE);
                                    persistReconciliationStatus(reconciliationStatus);
                                    throw new RuntimeException(ex);
                                }
                            }
                        } finally {
                            m_lock.unlock();
                        }

                        reconciliationStatus.setIsDataComplete(Boolean.TRUE);
                        reconciliationStatus.setEndTime(System.currentTimeMillis());
                        persistReconciliationStatus(reconciliationStatus);
                    }

                    /**
                     * Returns the current snapshot id as a SnapshotIdEntity instance
                     *
                     * @return SnapshotIdEntity
                     */
                    private SnapshotIdEntity getCurrentSnapshotId() {
                        NRGSnapshotId snapshotId = null;

                        CurrentSnapshotIdentifierResponse resp =
                                productMasterService.getCurrentSnapshotIdentifier(new DefaultGetCurrentSnapshotIdentifier());

                        if (resp != null && resp.getIdentifier() != null) {
                            snapshotId = new NRGSnapshotId(resp.getIdentifier());
                        }

                        return snapshotId;
                    }

                    /**
                     * Returns a Collection of SnapshotIdEntity instances that relate to the given time
                     *
                     * @param requestTime
                     * @return List<SnapshotIdEntity>
                     */
                    private List<SnapshotIdEntity> getSnapshotIds(Date requestTime) {
                        List<SnapshotIdEntity> snapshotIds = new ArrayList<SnapshotIdEntity>();

                        DefaultSnapshotIdentiferListRequest request = new DefaultSnapshotIdentiferListRequest();
                        request.setBeginDate(new Date(requestTime.getTime() - ONE_DAY));
                        request.setEndDate(requestTime);

                        SnapshotIdentifierListResponse resp = productMasterService.getSnapshotIdentifierList(request);

                        if (resp != null && resp.getIdentifier() != null && !resp.getIdentifier().isEmpty()) {
                            for (String id : resp.getIdentifier()) {
                                snapshotIds.add(new NRGSnapshotId(id));
                            }
                        }

                        return snapshotIds;
                    }

                    private void persistReconciliationStatus(ReconciliationStatusEntity reconciliationStatus) {
                        try {
                            reconciliationStatusDAO.putReconciliationStatusEntity(reconciliationStatus);
                        } catch (DAOException ex) {
                            m_logger.info("Failed to persist reconciliation status: {}", reconciliationStatus, ex);
                        }
                    }
                };

        final AbstractEndOfDayScheduledEventListener priceSchemaScheduledTaskEventListener =
                new AbstractEndOfDayScheduledEventListener() {
                    // We synchronise processing of ScheduledTaskEvents
                    private Lock m_lock = new ReentrantLock();

                    @Override
                    protected void process(ScheduledTaskEvent event, List<Date> eodDatesToLoad) {
                        Date startTime = new Date();
                        NRGReconciliationStatus reconciliationStatus = new NRGReconciliationStatus();
                        reconciliationStatus.setStartTime(startTime.getTime());
                        reconciliationStatus.setFromDate(startTime.getTime());
                        reconciliationStatus.setToDate(startTime.getTime());
                        reconciliationStatus.setServiceName(event.getScheduledTask().getTask().getType().name());
                        m_lock.lock();
                        try {
                            try {
                                long schemaCount = 0;
                                m_logger.info("Requesting price schemas");
                                List<PriceSchema> priceSchemas = getPriceSchemas();
                                m_logger.info("{} price schemas received, about to persist", priceSchemas.size());

                                List<PriceSchemaEntity> priceSchemaEntities = new ArrayList<PriceSchemaEntity>();
                                for (PriceSchema priceSchema : priceSchemas) {
                                    priceSchemaEntities.add(new NRGPriceSchema(startTime, priceSchema));
                                }
                                productSetDAO.putPriceSchemas("NRG", priceSchemaEntities, false, true);
                                schemaCount = priceSchemaEntities.size();

                                reconciliationStatus.setODSCount(0);
                                reconciliationStatus.setSourceCount(schemaCount);
                                reconciliationStatus.setIsDataComplete(priceSchemas.size() == schemaCount);
                                event.setUserInfo(schemaCount + " price schemas loaded");
                            } catch (Exception ex) {
                                reconciliationStatus.setEndTime(System.currentTimeMillis());
                                reconciliationStatus.setErrorReason(ex.getMessage());
                                reconciliationStatus.setIsDataComplete(Boolean.FALSE);
                                persistReconciliationStatus(reconciliationStatus);
                                throw new RuntimeException(ex);
                            }
                        } finally {
                            m_lock.unlock();
                        }
                        reconciliationStatus.setEndTime(System.currentTimeMillis());
                        persistReconciliationStatus(reconciliationStatus);
                    }

                    private List<PriceSchema> getPriceSchemas() {
                        return productMasterService.getPriceSchemas(new DefaultGetPriceSchemasRequest()).getPriceSchemas();
                    }

                    private void persistReconciliationStatus(ReconciliationStatusEntity reconciliationStatus) {
                        try {
                            reconciliationStatusDAO.putReconciliationStatusEntity(reconciliationStatus);
                        } catch (DAOException ex) {
                            m_logger.info("Failed to persist reconciliation status: {}", reconciliationStatus, ex);
                        }
                    }
                };

        productMasterService.subscribePriceSchemaUpdated(event -> {
            try {
                productSetDAO.putPriceSchemas(
                        "NRG",
                        Arrays.asList(new NRGPriceSchema(new Date(), event.getPriceSchema())),
                        event.isDeleted(),
                        false);
            } catch (Exception ex) {
                m_logger.warn("Failed to load/persist published price schema: " + event, ex);
            }
        });

/*        
        final Listener<ScheduledTaskEvent> priceFeedSchemaScheduledTaskEventListener =
                new Listener<ScheduledTaskEvent>() {
                    private Lock m_lock = new ReentrantLock();

                    @Override
                    public void onEvent(ScheduledTaskEvent event) 
                    {
                        synchronized(this)
                        {
                        	m_lock.lock();
                             try   
                             {
                                    long schemaCount = 0;
                                    m_logger.info("Requesting price feed schemas");
                                    
                                    Date taskAsOfTime = new Date();                
                                    if (event.getScheduledTask().getTask().getParametersList() != null) {
                                    	for (TaskParameter pmt : event.getScheduledTask().getTask().getParametersList()) {
                                    		if (pmt.getName().equals("executionDate")) {                                                  			
                                    			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm");
                                    			simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                                    			taskAsOfTime = simpleDateFormat.parse(pmt.getValue());                                			                                		
                                    		}
                                    	}                                	
                                    }
                             
                                    List<PriceFeedSchema> priceFeedSchemas = getPriceFeedSchemas(taskAsOfTime);
                                    m_logger.info("{} price feed schemas received, about to persist", priceFeedSchemas.size());

                                    List<PriceFeedSchemaEntity> priceFeedSchemaEntities = new ArrayList<PriceFeedSchemaEntity>();
                                    for(PriceFeedSchema priceFeedSchema : priceFeedSchemas)
                                    {
                                        priceFeedSchemaEntities.add(new NRGPriceFeedSchema(taskAsOfTime, priceFeedSchema));
                                    }
                                    
                                    productSetDAO.putPriceFeedSchemas("NRG", priceFeedSchemaEntities, false, true, taskAsOfTime.getTime());
                                    schemaCount = priceFeedSchemaEntities.size();
                                    
                                    event.setUserInfo(schemaCount + " price feed schemas loaded");
                                                     
	                        } catch (Exception e) {
	                        	throw new RuntimeException(e);
							}
	                        finally
	                        {
	                            m_lock.unlock();
	                        }
                        }
                    }
                       
                    
                private List<PriceFeedSchema> getPriceFeedSchemas(Date taskAsOfTime)
                {
                 	DefaultGetPriceFeedSchemasRequest request = new DefaultGetPriceFeedSchemasRequest();
                 	request.setAsOf(taskAsOfTime);
                    return productMasterService.getPriceFeedSchemas(request).getPriceFeedSchemas();
                }
        };
        
        */

        final AbstractExactTimeScheduledEventListener priceFeedSchemaScheduledTaskEventListener =
                new AbstractExactTimeScheduledEventListener() {
                    @Override
                    protected void process(ScheduledTaskEvent event, Date executionDate, Integer intervalMinutes) {
                        NRGReconciliationStatus reconciliationStatus = new NRGReconciliationStatus();
                        reconciliationStatus.setStartTime(System.currentTimeMillis());
                        reconciliationStatus.setServiceName(TaskType.LOAD_PRICE_FEED_SCHEMAS_ANYTIME.name());
                        if (executionDate != null) {
                            try {
                                reconciliationStatus.setFromDate(executionDate.getTime());
                                reconciliationStatus.setToDate(executionDate.getTime());
                                long schemaCount = 0;

                                m_logger.info("Requesting price feed schemas at {}", executionDate);
                                List<PriceFeedSchema> priceFeedSchemas = getPriceFeedSchemas(executionDate);
                                if (priceFeedSchemas != null && !priceFeedSchemas.isEmpty()) {

                                    m_logger.info("{} price feed schemas received, about to persist", priceFeedSchemas.size());

                                    List<PriceFeedSchemaEntity> priceFeedSchemaEntities = new ArrayList<PriceFeedSchemaEntity>();
                                    for (PriceFeedSchema priceFeedSchema : priceFeedSchemas) {
                                        priceFeedSchemaEntities.add(new NRGPriceFeedSchema(executionDate, priceFeedSchema));
                                    }

                                    productSetDAO.putPriceFeedSchemas("NRG", priceFeedSchemaEntities, false, executionDate.getTime());
                                    schemaCount = priceFeedSchemaEntities.size();
                                }
                                reconciliationStatus.setODSCount(0);
                                reconciliationStatus.setSourceCount(schemaCount);
                                reconciliationStatus.setIsDataComplete(priceFeedSchemas.size() == schemaCount);
                                event.setUserInfo(schemaCount + " price feed schemas loaded");
                            } catch (Exception ex) {
                                reconciliationStatus.setEndTime(System.currentTimeMillis());
                                reconciliationStatus.setErrorReason(ex.getMessage());
                                reconciliationStatus.setIsDataComplete(Boolean.FALSE);
                                persistReconciliationStatus(reconciliationStatus);
                                throw new RuntimeException(ex);
                            }
                        }
                        reconciliationStatus.setIsDataComplete(Boolean.TRUE);
                        reconciliationStatus.setEndTime(System.currentTimeMillis());
                        persistReconciliationStatus(reconciliationStatus);
                    }

                    private List<PriceFeedSchema> getPriceFeedSchemas(Date taskAsOfTime) {
                        DefaultGetPriceFeedSchemasRequest request = new DefaultGetPriceFeedSchemasRequest();
                        request.setAsOf(taskAsOfTime);
                        return productMasterService.getPriceFeedSchemas(request).getPriceFeedSchemas();
                    }

                    private void persistReconciliationStatus(ReconciliationStatusEntity reconciliationStatus) {
                        try {
                            reconciliationStatusDAO.putReconciliationStatusEntity(reconciliationStatus);
                        } catch (DAOException ex) {
                            m_logger.info("Failed to persist reconciliation status: {}", reconciliationStatus, ex);
                        }
                    }
                };

        final AbstractEndOfDayScheduledEventListener priceFeedSchemaEoDScheduledTaskEventListener =
                new AbstractEndOfDayScheduledEventListener() {
                    // We synchronise processing of ScheduledTaskEvents
                    private Lock m_lock = new ReentrantLock();

                    @Override
                    protected void process(ScheduledTaskEvent event, List<Date> eodDatesToLoad) {
                        Date startTime = new Date();
                        NRGReconciliationStatus reconciliationStatus = new NRGReconciliationStatus();
                        reconciliationStatus.setStartTime(startTime.getTime());
                        reconciliationStatus.setFromDate(startTime.getTime());
                        reconciliationStatus.setToDate(startTime.getTime());
                        reconciliationStatus.setServiceName(event.getScheduledTask().getTask().getType().name());
                        m_lock.lock();
                        try {
                            try {
                                long schemaCount = 0;
                                m_logger.info("Requesting price feed schemas");

                                Date taskAsOfTime = new Date();
                                if (event.getScheduledTask().getTask().getParametersList() != null) {
                                    for (TaskParameter pmt : event.getScheduledTask().getTask().getParametersList()) {
                                        if (pmt.getName().equals("fromDate")) {
                                            String dateFormat = "dd/MM/yyyy HH:mm:ss.SSSZ";
                                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
                                            taskAsOfTime = simpleDateFormat.parse(pmt.getValue());
                                        }
                                    }
                                }

                                List<PriceFeedSchema> priceFeedSchemas = getPriceFeedSchemas(taskAsOfTime);
                                m_logger.info("{} price feed schemas received, about to persist", priceFeedSchemas.size());

                                List<PriceFeedSchemaEntity> priceFeedSchemaEntities = new ArrayList<PriceFeedSchemaEntity>();
                                for (PriceFeedSchema priceFeedSchema : priceFeedSchemas) {
                                    priceFeedSchemaEntities.add(new NRGPriceFeedSchema(startTime, priceFeedSchema));
                                }

                                productSetDAO.putPriceFeedSchemas("NRG", priceFeedSchemaEntities, false, taskAsOfTime.getTime());
                                schemaCount = priceFeedSchemaEntities.size();

                                reconciliationStatus.setODSCount(0);
                                reconciliationStatus.setSourceCount(schemaCount);
                                reconciliationStatus.setIsDataComplete(priceFeedSchemas.size() == schemaCount);
                                event.setUserInfo(schemaCount + " price feed schemas loaded");
                            } catch (Exception ex) {
                                reconciliationStatus.setEndTime(System.currentTimeMillis());
                                reconciliationStatus.setErrorReason(ex.getMessage());
                                reconciliationStatus.setIsDataComplete(Boolean.FALSE);
                                persistReconciliationStatus(reconciliationStatus);
                                throw new RuntimeException(ex);
                            }
                        } finally {
                            m_lock.unlock();
                        }
                        reconciliationStatus.setEndTime(System.currentTimeMillis());
                        persistReconciliationStatus(reconciliationStatus);
                    }

                    private List<PriceFeedSchema> getPriceFeedSchemas(Date taskAsOfTime) {
                        DefaultGetPriceFeedSchemasRequest request = new DefaultGetPriceFeedSchemasRequest();
                        request.setAsOf(taskAsOfTime);
                        return productMasterService.getPriceFeedSchemas(request).getPriceFeedSchemas();
                    }

                    private void persistReconciliationStatus(ReconciliationStatusEntity reconciliationStatus) {
                        try {
                            reconciliationStatusDAO.putReconciliationStatusEntity(reconciliationStatus);
                        } catch (DAOException ex) {
                            m_logger.info("Failed to persist reconciliation status: {}", reconciliationStatus, ex);
                        }
                    }
                };

        final AbstractExactTimeScheduledEventListener priceFeedProductSchemaScheduledTaskEventListener =
                new AbstractExactTimeScheduledEventListener() {
                    @Override
                    protected void process(ScheduledTaskEvent event, Date executionDate, Integer intervalMinutes) {
                        NRGReconciliationStatus reconciliationStatus = new NRGReconciliationStatus();
                        reconciliationStatus.setStartTime(System.currentTimeMillis());
                        reconciliationStatus.setServiceName(TaskType.LOAD_PRICE_FEED_SCHEMAS_ANYTIME.name());
                        if (executionDate != null) {
                            try {
                                reconciliationStatus.setFromDate(executionDate.getTime());
                                reconciliationStatus.setToDate(executionDate.getTime());
                                long schemaCount = 0;

                                m_logger.info("Requesting price feed schemas at {}", executionDate);
                                List<PriceFeedSchema2> priceFeedSchemas2 = getPriceFeedSchemas2WithSettings(executionDate);


                                List<NRGPriceFeedSchema2> priceFeedSchemaEntities2 = getNrgPriceFeedSchema2(executionDate, priceFeedSchemas2);
                                m_logger.info("{} price feed schemas 2 received, after filtering persisting {}", priceFeedSchemas2.size(), priceFeedSchemaEntities2.size());


                                productSetDAO.putPriceFeedSchemas2("NRG", false, priceFeedSchemaEntities2, executionDate.getTime());
                                schemaCount = priceFeedSchemas2.size();

                                reconciliationStatus.setODSCount(0);
                                reconciliationStatus.setSourceCount(schemaCount);
                                reconciliationStatus.setIsDataComplete(priceFeedSchemas2.size() == schemaCount);
                                event.setUserInfo(schemaCount + " price feed schemas 2 loaded");
                            } catch (Exception ex) {
                                reconciliationStatus.setEndTime(System.currentTimeMillis());
                                reconciliationStatus.setErrorReason(ex.getMessage());
                                reconciliationStatus.setIsDataComplete(Boolean.FALSE);
                                persistReconciliationStatus(reconciliationStatus);
                                throw new RuntimeException(ex);
                            }
                        }
                        reconciliationStatus.setIsDataComplete(Boolean.TRUE);
                        reconciliationStatus.setEndTime(System.currentTimeMillis());
                        persistReconciliationStatus(reconciliationStatus);
                    }


                    private void persistReconciliationStatus(ReconciliationStatusEntity reconciliationStatus) {
                        try {
                            reconciliationStatusDAO.putReconciliationStatusEntity(reconciliationStatus);
                        } catch (DAOException ex) {
                            m_logger.info("Failed to persist reconciliation status: {}", reconciliationStatus, ex);
                        }
                    }
                };

        final AbstractEndOfDayScheduledEventListener priceFeedProductSchemaEoDScheduledTaskEventListener =
                new AbstractEndOfDayScheduledEventListener() {
                    // We synchronise processing of ScheduledTaskEvents
                    private Lock m_lock = new ReentrantLock();

                    @Override
                    protected void process(ScheduledTaskEvent event, List<Date> eodDatesToLoad) {
                        Date startTime = new Date();
                        NRGReconciliationStatus reconciliationStatus = new NRGReconciliationStatus();
                        reconciliationStatus.setStartTime(startTime.getTime());
                        reconciliationStatus.setFromDate(startTime.getTime());
                        reconciliationStatus.setToDate(startTime.getTime());
                        reconciliationStatus.setServiceName(event.getScheduledTask().getTask().getType().name());
                        m_lock.lock();
                        try {
                            try {
                                long schemaCount = 0;
                                m_logger.info("Requesting price feed schemas 2");

                                Date taskAsOfTime = new Date();
                                if (event.getScheduledTask().getTask().getParametersList() != null) {
                                    for (TaskParameter pmt : event.getScheduledTask().getTask().getParametersList()) {
                                        if (pmt.getName().equals("fromDate")) {
                                            String dateFormat = "dd/MM/yyyy HH:mm:ss.SSSZ";
                                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
                                            taskAsOfTime = simpleDateFormat.parse(pmt.getValue());
                                        }
                                    }
                                }

                                List<PriceFeedSchema2> priceFeedSchemas2 = getPriceFeedSchemas2WithSettings(taskAsOfTime);

                                List<NRGPriceFeedSchema2> priceFeedSchemaEntities2 = getNrgPriceFeedSchema2(taskAsOfTime, priceFeedSchemas2);
                                m_logger.info("{} price feed schemas 2 received, after filtering persisting {}", priceFeedSchemas2.size(), priceFeedSchemaEntities2.size());

                                productSetDAO.putPriceFeedSchemas2("NRG", true, priceFeedSchemaEntities2, taskAsOfTime.getTime());
                                schemaCount = priceFeedSchemas2.size();

                                reconciliationStatus.setODSCount(0);
                                reconciliationStatus.setSourceCount(schemaCount);
                                reconciliationStatus.setIsDataComplete(priceFeedSchemas2.size() == schemaCount);
                                event.setUserInfo(schemaCount + " price feed schemas 2 loaded");
                            } catch (Exception ex) {
                                reconciliationStatus.setEndTime(System.currentTimeMillis());
                                reconciliationStatus.setErrorReason(ex.getMessage());
                                reconciliationStatus.setIsDataComplete(Boolean.FALSE);
                                persistReconciliationStatus(reconciliationStatus);
                                throw new RuntimeException(ex);
                            }
                        } finally {
                            m_lock.unlock();
                        }
                        reconciliationStatus.setEndTime(System.currentTimeMillis());
                        persistReconciliationStatus(reconciliationStatus);
                    }

                    private void persistReconciliationStatus(ReconciliationStatusEntity reconciliationStatus) {
                        try {
                            reconciliationStatusDAO.putReconciliationStatusEntity(reconciliationStatus);
                        } catch (DAOException ex) {
                            m_logger.info("Failed to persist reconciliation status: {}", reconciliationStatus, ex);
                        }
                    }
                };
     /*       productMasterService.subscribePriceFeedSchemaUpdated(new Listener<PriceFeedSchemaUpdated>() {
                @Override
                public void onEvent(PriceFeedSchemaUpdated event)
                {
                    try
                    {
                        productSetDAO.putPriceFeedSchemas(
                            "NRG",
                            Arrays.asList(new NRGPriceFeedSchema(new Date(), event.getPriceFeedSchema())),
                            event.isDeleted(),
                            false,
                            new Date().getTime());
                    }
                    catch(Exception ex)
                    {
                        m_logger.warn("Failed to load/persist published price schema: " + event, ex);
                    }
                }
            });        */

        serviceSubscriber.subscribeToSchedulerEvent(TaskType.LOAD_PMS_SNAPSHOT_IDS, pmsSnapshotIdsScheduledTaskEventListener);

        serviceSubscriber.subscribeToSchedulerEvent(TaskType.LOAD_PRICE_SCHEMAS, priceSchemaScheduledTaskEventListener);

        serviceSubscriber.subscribeToSchedulerEvent(TaskType.LOAD_PRICE_FEED_SCHEMAS_ANYTIME, priceFeedSchemaScheduledTaskEventListener);

        serviceSubscriber.subscribeToSchedulerEvent(TaskType.LOAD_PRICE_FEED_SCHEMAS, priceFeedSchemaEoDScheduledTaskEventListener);

        serviceSubscriber.subscribeToSchedulerEvent(TaskType.LOAD_PRICE_FEED_SCHEMAS_ANYTIME, priceFeedProductSchemaScheduledTaskEventListener);

        serviceSubscriber.subscribeToSchedulerEvent(TaskType.LOAD_PRICE_FEED_SCHEMAS, priceFeedProductSchemaEoDScheduledTaskEventListener);
    }


    public List<PriceFeedSchema2> getPriceFeedSchemas2WithSettings(Date taskAsOfTime) {
        DefaultGetPriceFeedSchemasRequest2 request = new DefaultGetPriceFeedSchemasRequest2();
        request.setAsOf(taskAsOfTime.getTime());

        return productMasterService.getPriceFeedSchemas2(request)
                .getPriceFeedSchemasList()
                .stream()
                .filter(Objects::nonNull)
                .filter(pfs -> pfs.getCode() != null && !pfs.getCode().isEmpty())
                .filter(pfs -> pfs.getSettingsList() != null && !pfs.getSettingsList().isEmpty())
                .collect(Collectors.toList());
    }

    public List<NRGPriceFeedSchema2> getNrgPriceFeedSchema2(Date asOfTime, List<PriceFeedSchema2> priceFeedSchema2List) {
        return priceFeedSchema2List.stream()
                .filter(Objects::nonNull)
                .map(priceFeedSchema2 -> getNrgPriceFeedSchema2(asOfTime, priceFeedSchema2))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    public List<NRGPriceFeedSchema2> getNrgPriceFeedSchema2(Date asOfTime, PriceFeedSchema2 priceFeedSchema2) {
        return priceFeedSchema2.getSettingsList().stream()
                .filter(Objects::nonNull)
                .map(setting ->
                        new NRGPriceFeedSchema2(asOfTime,
                                priceFeedSchema2.getCode(),
                                priceFeedSchema2.getName(),
                                priceFeedSchema2.getLegacyId(),
                                setting
                        ))
                .collect(Collectors.toList());

    }
}
