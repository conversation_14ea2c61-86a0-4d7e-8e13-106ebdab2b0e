package com.cmcmarkets.nrg.server.handlers.routed;

import com.cmcmarkets.accountmaster.api.fluent.model.*;
import com.cmcmarkets.accountmaster.api.fluent.service.AccountMasterService;
import com.cmcmarkets.framework.messaging.common.model.NotificationType;
import com.cmcmarkets.nrg.server.dao.CashAccountDAO;
import com.cmcmarkets.nrg.server.dao.TradingAccountDAO;
import com.cmcmarkets.nrg.server.handlers.AbstractOnMessageHandler;
import com.cmcmarkets.nrg.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Date;

import static com.cmcmarkets.nrg.utils.Constants.getNRGSourcePlatform;

public final class AccountMasterMessageHandler
{
    private static final Logger m_logger = LoggerFactory.getLogger(AccountMasterMessageHandler.class.getName());
    private static final int NOTIFICATION_CODE_REGULATORY_ACCEPTANCE_ADDED = 134;
    private static final int NOTIFICATION_CODE_REGULATORY_ACCEPTANCE_ACCT_CREATED = 128;
    
    public AccountMasterMessageHandler(final AccountMasterService accountMasterService,
                                       final TradingAccountDAO tradingAccountDAO,
                                       final CashAccountDAO cashAccountDAO)
    {
        m_logger.info("Initialising {}", this.getClass().getSimpleName());

        accountMasterService.subscribeOnTradingAccount(
            new AbstractOnMessageHandler<OnTradingAccount>(tradingAccountDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnTradingAccount event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    tradingAccountDAO.putTradingAccountEntity(
                            event.getTradingAccount(), 
                            sentTime, 
                            NotificationType.Type.REMOVE == event.getType().getType(), 
                            Constants.TradingAccountType.CUSTOMER);
                    
                    if(event.getTradingAccount().getChildAccounts() != null) {
                    	for(ChildAccount childAccount : event.getTradingAccount().getChildAccounts()) {
                            tradingAccountDAO.putChildAccountEntity(childAccount, event.getTradingAccount(), sentTime, NotificationType.Type.REMOVE == event.getType().getType());
                    	}
                    }

                    if(event.getType().getNotificationCodes().contains(NOTIFICATION_CODE_REGULATORY_ACCEPTANCE_ADDED) ||
                       event.getType().getNotificationCodes().contains(NOTIFICATION_CODE_REGULATORY_ACCEPTANCE_ACCT_CREATED)) {

                        RegulatoryAcceptance acceptance = RegulatoryAcceptance
                                .builder()
                                .withTradingAccountId(event.getTradingAccount().getId())
                                .build();
                        SearchRegulatoryAcceptanceRequest request = SearchRegulatoryAcceptanceRequest.builder()
                                .withRegulatoryAcceptances(Collections.singletonList(acceptance)).build();
                       
                        SearchRegulatoryAcceptanceResponse response = accountMasterService.searchRegulatoryAcceptance(request);
                        tradingAccountDAO.putRegulatoryAcceptances(response.getRegulatoryAcceptances());
                    }
                }
            });

        accountMasterService.subscribeOnTradingAccountClosure(
            new AbstractOnMessageHandler<OnTradingAccountClosure>(tradingAccountDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnTradingAccountClosure event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    tradingAccountDAO.putTradingAccountEntity(event.getTradingAccount(), sentTime, NotificationType.Type.REMOVE == event.getType().getType(), Constants.TradingAccountType.CUSTOMER);
                    
                    if(event.getTradingAccount().getChildAccounts() != null) {
                    	for(ChildAccount childAccount : event.getTradingAccount().getChildAccounts()) {
                            tradingAccountDAO.putChildAccountEntity(childAccount, event.getTradingAccount(), sentTime, NotificationType.Type.REMOVE == event.getType().getType());
                    	}
                    }       
                }

            });

        accountMasterService
            .subscribeOnHedgeAccount(new AbstractOnMessageHandler<OnHedgeAccount>(tradingAccountDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnHedgeAccount event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    tradingAccountDAO.putHedgeAccountEntity(event.getHedgeAccount(), sentTime,
                            NotificationType.Type.REMOVE == event.getType().getType());
                }
            });
    }

}
