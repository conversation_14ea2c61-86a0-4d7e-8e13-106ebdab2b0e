package com.cmcmarkets.nrg.server.handlers.routed;

import com.cmcmarkets.customermaster.api.fluent.model.*;
import com.cmcmarkets.customermaster.api.fluent.service.*;
import com.cmcmarkets.framework.messaging.common.model.NotificationType;
import com.cmcmarkets.nrg.api.model.impl.*;
import com.cmcmarkets.nrg.server.dao.*;
import com.cmcmarkets.nrg.server.handlers.AbstractOnMessageHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

public final class CustomerMasterMessageHandler
{
    private static final Logger m_logger = LoggerFactory.getLogger(CustomerMasterMessageHandler.class.getName());

    public CustomerMasterMessageHandler(final PersonService personService,
                                        final PersonDAO personDAO,
                                        final CustomerService customerService,
                                        final CustomerDAO customerDAO,
                                        final CompanyService companyService,
                                        final CompanyDAO companyDAO,
                                        final PartnerService partnerService,
                                        final PartnerDAO partnerDAO,
                                        final CorporateStructureService corporateStructureService,
                                        final CorporateStructureDAO corporateStructureDAO,
                                        final PaymentDataService paymentDataService,
                                        final PaymentDataDAO paymentDataDAO)
    {
        m_logger.info("Initialising {}", this.getClass().getSimpleName());

        personService.subscribeOnPerson(new AbstractOnMessageHandler<PersonEvent>(personDAO) {
            @Override
            public void processMessage(Date sentTime,
                                       PersonEvent event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
                personDAO.putPersonEntity(
                    new NRGPerson(
                        sentTime,
                        event.getPerson(),
                        NotificationType.Type.REMOVE == event.getType().getType()));
            }
        });

        customerService.subscribeOnCustomer(new AbstractOnMessageHandler<CustomerEvent>(customerDAO) {
            @Override
            public void processMessage(Date sentTime,
                                       CustomerEvent event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
                customerDAO.putCustomerEntity(
                    new NRGCustomer(
                        sentTime,
                        event.getCustomer(),
                        NotificationType.Type.REMOVE == event.getType().getType()));
            }
        });

        companyService.subscribeOnCompany(new AbstractOnMessageHandler<CompanyEvent>(companyDAO) {
            @Override
            public void processMessage(Date sentTime,
                                       CompanyEvent event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
                companyDAO.putCompanyEntity(event.getCompany(), sentTime, NotificationType.Type.REMOVE == event.getType().getType());
            }
        });

        corporateStructureService.subscribeOnCorporateStructure(
            new AbstractOnMessageHandler<OnCorporateStructure>(corporateStructureDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnCorporateStructure event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    if(event.getOffice() != null)
                    {
                        corporateStructureDAO.putOfficeEntity(
                            new NRGOffice(
                                sentTime,
                                event.getOffice(),
                                event.getType().getType() == NotificationType.Type.REMOVE),
                            event.getType().getType() == NotificationType.Type.REMOVE);
                    }
                }
            });

        corporateStructureService.subscribeOnCorporateStructure(
            new AbstractOnMessageHandler<OnCorporateStructure>(corporateStructureDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnCorporateStructure event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    if(event.getLegalEntity() != null)
                    {
                        corporateStructureDAO.putLegalEntity(
                            new NRGLegalEntity(
                                sentTime,
                                event.getLegalEntity(),
                                event.getType().getType() == NotificationType.Type.REMOVE),
                            event.getType().getType() == NotificationType.Type.REMOVE);
                    }
                }
            });

        corporateStructureService.subscribeOnCorporateStructure(
            new AbstractOnMessageHandler<OnCorporateStructure>(corporateStructureDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnCorporateStructure event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    if(event.getProfitCentre() != null)
                    {
                        corporateStructureDAO.putProfitCentreEntity(
                            new NRGProfitCentre(
                                sentTime,
                                event.getProfitCentre(),
                                event.getType().getType() == NotificationType.Type.REMOVE),
                            event.getType().getType() == NotificationType.Type.REMOVE);
                    }
                }
            });

        partnerService.subscribeOnPartner(new AbstractOnMessageHandler<OnPartner>(partnerDAO) {
            @Override
            public void processMessage(Date sentTime,
                                       OnPartner event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
                partnerDAO.putPartnerEntity(
                    new NRGPartner(
                        sentTime,
                        event.getPartner(),
                        NotificationType.Type.REMOVE == event.getType().getType()));

                if(event.getPartner() != null && event.getPartner().getPartnerId() != null)
                {
                    handlePartnerDesks(event.getPartner().getPartnerId());
                }
            }

            private void handlePartnerDesks(Long partnerId)
            {
                if (partnerId != -1) {
                    GetPartnerDesksRequest request = GetPartnerDesksRequest.builder()
                            .withPartnerId(partnerId).build();
                    GetPartnerDesksResponse response = partnerService.getPartnerDesks(request);
                    if(response != null && response.getDesks() != null)
                    {
                        for(PartnerDesk desk : response.getDesks())
                        {
                            partnerDAO.putPartnerDeskEntity(new NRGPartnerDesk(new Date(), desk, false));
                        }
                    }
                }
            }
        });

        partnerService
            .subscribeOnPartnerAgent(new AbstractOnMessageHandler<OnPartnerAgent>(partnerDAO) {
                @Override
                public void processMessage(Date sentTime,
                                           OnPartnerAgent event,
                                           com.cmcmarkets.framework.messaging.Message message)
                {
                    partnerDAO.putPartnerAgentEntity(
                        new NRGPartnerAgent(
                            sentTime,
                            event.getPartnerAgent(),
                            NotificationType.Type.REMOVE == event.getType().getType()));
                    
                    if(event.getPartnerAgent() != null && event.getPartnerAgent().getPartnerId() != null)
                    {
                    	requestCompany(event.getPartnerAgent().getPartnerId());
                    }
                }
                
                private void requestCompany(Long partnerId)
                {
                    if (partnerId != -1) {
                        FindPartnerRequest request = FindPartnerRequest.builder()
                        .withPartnerId(partnerId).build();
                        FindPartnerResponse response = partnerService.findPartner(request);
                        if(response != null && response.getPartner() != null && response.getPartner().getCompanyId() != null)
                        {
                        	FindCompanyRequest findCompanyRequest = FindCompanyRequest.builder()
                            .withCompanyId(response.getPartner().getCompanyId()).build();

                            FindCompanyResponse findCompanyResponse = companyService.findCompany(findCompanyRequest);
                            Company company = findCompanyResponse.getCompany();
                            if (company != null) {
                            companyDAO.putCompanyEntity(company, new Date(), false);
                            }
                        }
                    }
                }                
            });

        partnerService.subscribeOnPartnerDesk(new AbstractOnMessageHandler<OnPartnerDesk>(partnerDAO) {
            @Override
            public void processMessage(Date sentTime,
                                       OnPartnerDesk event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
                partnerDAO.putPartnerDeskEntity(
                    new NRGPartnerDesk(
                        sentTime,
                        event.getPartnerDesk(),
                        NotificationType.Type.REMOVE == event.getType().getType()));
            }
        });

        partnerService.subscribeOnIntroducer(new AbstractOnMessageHandler<OnIntroducer>(customerDAO) {
            @Override
            public void processMessage(Date sentTime,
                                       OnIntroducer event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
                customerDAO.putIntroducerEntity(
                    new NRGIntroducer(
                        sentTime,
                        event.getIntroducer(),
                        NotificationType.Type.REMOVE == event.getType().getType()));
            }
        });
        
        paymentDataService.subscribeOnPaymentBankRegistration(
        		new AbstractOnMessageHandler<PaymentBankRegistrationEvent>(paymentDataDAO) {
            @Override
            public void processMessage(Date sentTime,
            						   PaymentBankRegistrationEvent event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
            	paymentDataDAO.putPaymentBankRegistrationEntity(
                    new NRGPaymentBankRegistration(
                        sentTime,
                        event));
            }
        });
        
        paymentDataService.subscribeOnPaymentCardRegistration(
        		new AbstractOnMessageHandler<PaymentCardRegistrationEvent>(paymentDataDAO) {
            @Override
            public void processMessage(Date sentTime,
            						   PaymentCardRegistrationEvent event,
                                       com.cmcmarkets.framework.messaging.Message message)
            {
            	paymentDataDAO.putPaymentCardRegistrationEntity(
                    new NRGPaymentCardRegistration(
                        sentTime,
                        event));
            }
        });
        
        
    }

}
